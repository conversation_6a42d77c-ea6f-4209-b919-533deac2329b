#!/bin/bash

# ========= 配置参数 =========
MODEL_ID="Qwen/Qwen2.5-Coder-3B"          # 魔搭模型名称
TARGET_DIR="./models"   # 模型保存路径，请自行修改
PYTHON_BIN="python3"                      # 或指定虚拟环境内的 python 路径

# ========= 创建保存目录 =========
mkdir -p "$TARGET_DIR"

# ========= 调用 Python 下载模型 =========
$PYTHON_BIN <<EOF
from modelscope.hub.snapshot_download import snapshot_download
import os

model_id = "$MODEL_ID"
target_dir = "$TARGET_DIR"

os.makedirs(target_dir, exist_ok=True)

snapshot_download(
    model_id,
    cache_dir=target_dir,
)
print("✅ 模型已下载至:", target_dir)
EOF
