# Ratings and Reality Shows

**题目ID**: 887/D  
**比赛**: Codeforces Round 444 (Div. 2)  
**年份**: 2017  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

There are two main kinds of events in the life of top-model: fashion shows and photo shoots. Participating in any of these events affects the rating of appropriate top-model. After each photo shoot model's rating increases by a and after each fashion show decreases by b (designers do too many experiments nowadays). Moreover, sometimes top-models participates in talk shows. After participating in talk show model becomes more popular and increasing of her rating after photo shoots become c and decreasing of her rating after fashion show becomes d.

<PERSON><PERSON><PERSON> wants to participate in a talk show, but she wants to do it in such a way that her rating will never become negative. Help her to find a suitable moment for participating in the talk show.

Let's assume that model's career begins in moment 0. At that moment <PERSON><PERSON><PERSON>'s rating was equal to start. If talk show happens in moment t if will affect all events in model's life in interval of time [t..t + len) (including t and not including t + len), where len is duration of influence.

<PERSON><PERSON><PERSON> wants to participate in a talk show, but she wants to do it in such a way that her rating will not become become negative before talk show or during period of influence of talk show. Help her to find a suitable moment for participating in the talk show.

## 输入格式

In first line there are 7 positive integers n, a, b, c, d, start, len (1 ≤ n ≤ 3·105, 0 ≤ start ≤ 109, 1 ≤ a, b, c, d, len ≤ 109), where n is a number of fashion shows and photo shoots, a, b, c and d are rating changes described above, start is an initial rating of model and len is a duration of influence of talk show.

In next n lines descriptions of events are given. Each of those lines contains two integers ti and qi (1 ≤ ti ≤ 109, 0 ≤ q ≤ 1) — moment, in which event happens and type of this event. Type 0 corresponds to the fashion show and type 1 — to photo shoot.

Events are given in order of increasing ti, all ti are different.

## 输出格式

Print one non-negative integer t — the moment of time in which talk show should happen to make Izabella's rating non-negative before talk show and during period of influence of talk show. If there are multiple answers print smallest of them. If there are no such moments, print  - 1.

## 样例

### 样例 1

**输入**:
```
5 1 1 1 4 0 5
1 1
2 1
3 1
4 0
5 0
```

**输出**:
```
6
```

### 样例 2

**输入**:
```
1 1 2 1 2 1 2
1 0
```

**输出**:
```
-1
```

