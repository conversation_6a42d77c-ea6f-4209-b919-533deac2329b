# Kuro and Walking Route

**题目ID**: 979/C  
**比赛**: Codeforces Round 482 (Div. 2)  
**年份**: 2018  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON><PERSON> is living in a country called Uberland, consisting of $$$n$$$ towns, numbered from $$$1$$$ to $$$n$$$, and $$$n - 1$$$ bidirectional roads connecting these towns. It is possible to reach each town from any other. Each road connects two towns $$$a$$$ and $$$b$$$. <PERSON><PERSON> loves walking and he is planning to take a walking marathon, in which he will choose a pair of towns $$$(u, v)$$$ ($$$u \neq v$$$) and walk from $$$u$$$ using the shortest path to $$$v$$$ (note that $$$(u, v)$$$ is considered to be different from $$$(v, u)$$$).

Oddly, there are 2 special towns in Uberland named Flowrisa (denoted with the index $$$x$$$) and Beetopia (denoted with the index $$$y$$$). Flowrisa is a town where there are many strong-scent flowers, and Beetopia is another town where many bees live. In particular, <PERSON><PERSON> will avoid any pair of towns $$$(u, v)$$$ if on the path from $$$u$$$ to $$$v$$$, he reaches Beetopia after he reached Flowrisa, since the bees will be attracted with the flower smell on <PERSON>ro’s body and sting him.

<PERSON><PERSON> wants to know how many pair of city $$$(u, v)$$$ he can take as his route. Since he’s not really bright, he asked you to help him with this problem.

## 输入格式

The first line contains three integers $$$n$$$, $$$x$$$ and $$$y$$$ ($$$1 \leq n \leq 3 \cdot 10^5$$$, $$$1 \leq x, y \leq n$$$, $$$x \ne y$$$) - the number of towns, index of the town Flowrisa and index of the town Beetopia, respectively.

$$$n - 1$$$ lines follow, each line contains two integers $$$a$$$ and $$$b$$$ ($$$1 \leq a, b \leq n$$$, $$$a \ne b$$$), describes a road connecting two towns $$$a$$$ and $$$b$$$.

It is guaranteed that from each town, we can reach every other town in the city using the given roads. That is, the given map of towns and roads is a tree.

## 输出格式

A single integer resembles the number of pair of towns $$$(u, v)$$$ that Kuro can use as his walking route.

## 样例

### 样例 1

**输入**:
```
3 1 3
1 2
2 3
```

**输出**:
```
5
```

### 样例 2

**输入**:
```
3 1 3
1 2
1 3
```

**输出**:
```
4
```

## 备注

On the first example, Kuro can choose these pairs:

- $$$(1, 2)$$$: his route would be $$$1 \rightarrow 2$$$,
- $$$(2, 3)$$$: his route would be $$$2 \rightarrow 3$$$,
- $$$(3, 2)$$$: his route would be $$$3 \rightarrow 2$$$,
- $$$(2, 1)$$$: his route would be $$$2 \rightarrow 1$$$,
- $$$(3, 1)$$$: his route would be $$$3 \rightarrow 2 \rightarrow 1$$$.

Kuro can't choose pair $$$(1, 3)$$$ since his walking route would be $$$1 \rightarrow 2 \rightarrow 3$$$, in which Kuro visits town $$$1$$$ (Flowrisa) and then visits town $$$3$$$ (Beetopia), which is not allowed (note that pair $$$(3, 1)$$$ is still allowed because although Kuro visited Flowrisa and Beetopia, he did not visit them in that order).

On the second example, Kuro can choose the following pairs:

- $$$(1, 2)$$$: his route would be $$$1 \rightarrow 2$$$,
- $$$(2, 1)$$$: his route would be $$$2 \rightarrow 1$$$,
- $$$(3, 2)$$$: his route would be $$$3 \rightarrow 1 \rightarrow 2$$$,
- $$$(3, 1)$$$: his route would be $$$3 \rightarrow 1$$$.

## 题解

We can consider the city as a graph, in which every town is a vertex and every road connecting two towns is an edge. Since $$$m$$$ < $$$n$$$, we can deduce that this graph is a tree. Now, instead of finding the number of pairs that Kuro can choose, we can find the number of pairs that Kuro cannot choose. In other words, we must find the number of pairs of vertices $$$(u, v)$$$, in which the shortest path from $$$u$$$ to $$$v$$$ passes through $$$x$$$ and then through $$$y$$$. But how can we do this?
If we take vertex $$$y$$$ as the root of the tree, we can see that every pair of vertices that Kuro cannot choose begins from any node within the subtree of node $$$x$$$, and finishes at any node but within the subtree of node $$$z$$$, which $$$z$$$ is a direct child of $$$y$$$ lying on the shortest path from $$$x$$$ to $$$y$$$. In total, the number of pairs of vertices that we are looking for is equal of $$$n \cdot (n - 1) - s[x] \cdot (s[y] - s[z])$$$, which $$$s[i]$$$ denotes the size of the subtree of node $$$i$$$. We can implement this using simple DFS.
Time complexity: $$$O(n)$$$.

