# Hashing Trees

**题目ID**: 901/A  
**比赛**: Codeforces Round 453 (Div. 1)  
**年份**: 2017  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON> is taking part in a programming competition. In one of the problems she should check if some rooted trees are isomorphic or not. She has never seen this problem before, but, being an experienced participant, she guessed that she should match trees to some sequences and then compare these sequences instead of trees. <PERSON> wants to match each tree with a sequence a0, a1, ..., ah, where h is the height of the tree, and ai equals to the number of vertices that are at distance of i edges from root.

Unfortunately, this time <PERSON>'s intuition was wrong, and there could be several trees matching the same sequence. To show it, you need to write a program that, given the sequence ai, builds two non-isomorphic rooted trees that match that sequence, or determines that there is only one such tree.

Two rooted trees are isomorphic, if you can reenumerate the vertices of the first one in such a way, that the index of the root becomes equal the index of the root of the second tree, and these two trees become equal.

The height of a rooted tree is the maximum number of edges on a path from the root to any other vertex.

## 输入格式

The first line contains a single integer h (2 ≤ h ≤ 105) — the height of the tree.

The second line contains h + 1 integers — the sequence a0, a1, ..., ah (1 ≤ ai ≤ 2·105). The sum of all ai does not exceed 2·105. It is guaranteed that there is at least one tree matching this sequence.

## 输出格式

If there is only one tree matching this sequence, print "perfect".

Otherwise print "ambiguous" in the first line. In the second and in the third line print descriptions of two trees in the following format: in one line print $$\sum_{i=0}^{h} a_i$$ integers, the k-th of them should be the parent of vertex k or be equal to zero, if the k-th vertex is the root.

These treese should be non-isomorphic and should match the given sequence.

## 样例

### 样例 1

**输入**:
```
2
1 1 1
```

**输出**:
```
perfect
```

### 样例 2

**输入**:
```
2
1 2 2
```

**输出**:
```
ambiguous
0 1 1 3 3
0 1 1 3 2
```

## 备注

The only tree in the first example and the two printed trees from the second example are shown on the picture:

## 题解

There are many ways to solve the problem. First of all you should build any single tree. To do this, you first build the longest path from the root and then attach remained vertices on proper heights. Thus each vertex is either on the longest path or has parent on this path. To build the second tree you should use different vertices from previous levels during construction to make it different from first tree. This is always possible if there are two consecutive ai > 1. Otherwise the tree is determined uniquely by ai sequence.

