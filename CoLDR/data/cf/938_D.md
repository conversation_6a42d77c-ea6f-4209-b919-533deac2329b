# Buy a Ticket

**题目ID**: 938/D  
**比赛**: Educational Codeforces Round 38 (Rated for Div. 2)  
**年份**: 2018  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

Musicians of a popular band "Flayer" have announced that they are going to "make their exit" with a world tour. Of course, they will visit Berland as well.

There are n cities in Berland. People can travel between cities using two-directional train routes; there are exactly m routes, i-th route can be used to go from city vi to city ui (and from ui to vi), and it costs wi coins to use this route.

Each city will be visited by "Flayer", and the cost of the concert ticket in i-th city is ai coins.

You have friends in every city of Berland, and they, knowing about your programming skills, asked you to calculate the minimum possible number of coins they have to pay to visit the concert. For every city i you have to compute the minimum number of coins a person from city i has to spend to travel to some city j (or possibly stay in city i), attend a concert there, and return to city i (if j ≠ i).

Formally, for every $$i \in [1,n]$$ you have to calculate $$\min_{j=1}^{n} 2d(i,j) + a_j$$, where d(i, j) is the minimum number of coins you have to spend to travel from city i to city j. If there is no way to reach city j from city i, then we consider d(i, j) to be infinitely large.

## 输入格式

The first line contains two integers n and m (2 ≤ n ≤ 2·105, 1 ≤ m ≤ 2·105).

Then m lines follow, i-th contains three integers vi, ui and wi (1 ≤ vi, ui ≤ n, vi ≠ ui, 1 ≤ wi ≤ 1012) denoting i-th train route. There are no multiple train routes connecting the same pair of cities, that is, for each (v, u) neither extra (v, u) nor (u, v) present in input.

The next line contains n integers a1, a2, ... ak (1 ≤ ai ≤ 1012) — price to attend the concert in i-th city.

## 输出格式

Print n integers. i-th of them must be equal to the minimum number of coins a person from city i has to spend to travel to some city j (or possibly stay in city i), attend a concert there, and return to city i (if j ≠ i).

## 样例

### 样例 1

**输入**:
```
4 2
1 2 4
2 3 7
6 20 1 25
```

**输出**:
```
6 14 1 25
```

### 样例 2

**输入**:
```
3 3
1 2 1
2 3 1
1 3 1
30 10 20
```

**输出**:
```
12 10 12
```

## 题解

Solution: The function of the path length is not that different from the usual one. You can multiply edge weights by two and run Dijkstra in the following manner. Set disti = ai for all $$i \in [1,n]$$ and push these values to heap. When finished, disti will be equal to the shortest path.

