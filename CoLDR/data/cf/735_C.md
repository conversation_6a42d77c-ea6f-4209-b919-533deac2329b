# Tennis Championship

**题目ID**: 735/C  
**比赛**: Codeforces Round 382 (Div. 2)  
**年份**: 2016  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

Famous Brazil city Rio de Janeiro holds a tennis tournament and <PERSON><PERSON><PERSON> doesn't want to miss this event. There will be n players participating, and the tournament will follow knockout rules from the very first game. That means, that if someone loses a game he leaves the tournament immediately.

Organizers are still arranging tournament grid (i.e. the order games will happen and who is going to play with whom) but they have already fixed one rule: two players can play against each other only if the number of games one of them has already played differs by no more than one from the number of games the other one has already played. Of course, both players had to win all their games in order to continue participating in the tournament.

Tournament hasn't started yet so the audience is a bit bored. <PERSON><PERSON><PERSON> decided to find out what is the maximum number of games the winner of the tournament can take part in (assuming the rule above is used). However, it is unlikely he can deal with this problem without your help.

## 输入格式

The only line of the input contains a single integer n (2 ≤ n ≤ 1018) — the number of players to participate in the tournament.

## 输出格式

Print the maximum number of games in which the winner of the tournament can take part.

## 样例

### 样例 1

**输入**:
```
2
```

**输出**:
```
1
```

### 样例 2

**输入**:
```
3
```

**输出**:
```
2
```

### 样例 3

**输入**:
```
4
```

**输出**:
```
2
```

### 样例 4

**输入**:
```
10
```

**输出**:
```
4
```

## 备注

In all samples we consider that player number 1 is the winner.

In the first sample, there would be only one game so the answer is 1.

In the second sample, player 1 can consequently beat players 2 and 3.

In the third sample, player 1 can't play with each other player as after he plays with players 2 and 3 he can't play against player 4, as he has 0 games played, while player 1 already played 2. Thus, the answer is 2 and to achieve we make pairs (1, 2) and (3, 4) and then clash the winners.

