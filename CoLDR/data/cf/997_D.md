# Cycles in product

**题目ID**: 997/D  
**比赛**: Codeforces Round 493 (Div. 1)  
**年份**: 2018  
**时间限制**: 7.0秒  
**内存限制**: 256MB  

## 题目描述

Consider a tree (that is, an undirected connected graph without loops) $$$T_1$$$ and a tree $$$T_2$$$. Let's define their cartesian product $$$T_1 \times T_2$$$ in a following way.

Let $$$V$$$ be the set of vertices in $$$T_1$$$ and $$$U$$$ be the set of vertices in $$$T_2$$$.

Then the set of vertices of graph $$$T_1 \times T_2$$$ is $$$V \times U$$$, that is, a set of ordered pairs of vertices, where the first vertex in pair is from $$$V$$$ and the second — from $$$U$$$.

Let's draw the following edges:

- Between $$$(v, u_1)$$$ and $$$(v, u_2)$$$ there is an undirected edge, if $$$u_1$$$ and $$$u_2$$$ are adjacent in $$$U$$$.
- Similarly, between $$$(v_1, u)$$$ and $$$(v_2, u)$$$ there is an undirected edge, if $$$v_1$$$ and $$$v_2$$$ are adjacent in $$$V$$$.

Please see the notes section for the pictures of products of trees in the sample tests.

Let's examine the graph $$$T_1 \times T_2$$$. How much cycles (not necessarily simple) of length $$$k$$$ it contains? Since this number can be very large, print it modulo $$$998244353$$$.

The sequence of vertices $$$w_1$$$, $$$w_2$$$, ..., $$$w_k$$$, where $$$w_i \in V \times U$$$ called cycle, if any neighboring vertices are adjacent and $$$w_1$$$ is adjacent to $$$w_k$$$. Cycles that differ only by the cyclic shift or direction of traversal are still considered different.

## 输入格式

First line of input contains three integers — $$$n_1$$$, $$$n_2$$$ and $$$k$$$ ($$$2 \le n_1, n_2 \le 4000$$$, $$$2 \le k \le 75$$$) — number of vertices in the first tree, number of vertices in the second tree and the cycle length respectively.

Then follow $$$n_1 - 1$$$ lines describing the first tree. Each of this lines contains two integers — $$$v_i, u_i$$$ ($$$1 \le v_i, u_i \le n_1$$$), which define edges of the first tree.

Then follow $$$n_2 - 1$$$ lines, which describe the second tree in the same format.

It is guaranteed, that given graphs are trees.

## 输出格式

Print one integer — number of cycles modulo $$$998244353$$$.

## 样例

### 样例 1

**输入**:
```
2 2 2
1 2
1 2
```

**输出**:
```
8
```

### 样例 2

**输入**:
```
2 2 4
1 2
1 2
```

**输出**:
```
32
```

### 样例 3

**输入**:
```
2 3 4
1 2
1 2
1 3
```

**输出**:
```
70
```

### 样例 4

**输入**:
```
4 2 2
1 2
1 3
1 4
1 2
```

**输出**:
```
20
```

## 备注

The following three pictures illustrate graph, which are products of the trees from sample tests.

In the first example, the list of cycles of length $$$2$$$ is as follows:

- «AB», «BA»
- «BC», «CB»
- «AD», «DA»
- «CD», «DC»

## 题解

Consider an arbitrary cycle in a graph product.
Due to the definition of the product, the adjacent vertices in the cycle correspond to the transition by the edge either in the first tree or in the second tree.
This way, if you write the edges corresponding to one tree in a separate list, you will get a cycle in this tree.
Also, if we have a cycle in one tree of length $$$a$$$ and a cycle of length $$$b$$$ in the second tree, we can make $$$C_{a+b}^a$$$ cycles in the product.
Thus, the problem is reduced to calculating for each length up to $$$k$$$ the number of cycles in each tree separately, and then mixing them into cycles in the product.
—–
Let's select the centroid $$$c$$$ of the tree and count all cycles, which go through it, delete centroid and then recursively count in remaining components.
How looks cycle which goes through $$$c$$$? We need to start in some vertex $$$v$$$, then go to $$$c$$$ (not going through $$$c$$$ in between), and then go back to $$$v$$$, possibly going through $$$c$$$.
Let's define two dp's: $$$f[v][k]$$$ — number of ways to go from $$$c$$$ to $$$v$$$ by exactly $$$k$$$ steps not going through $$$c$$$ in between, $$$g[v][k]$$$ — number of ways to go from $$$c$$$ to $$$v$$$, but without previous limitation.
This way the answer for $$$v$$$ through centroid $$$c$$$ in convolution of $$$f[v]$$$ и $$$g[v]$$$: $$$ans[i+j] += f[v][i] * g[v][j]$$$.
Case where $$$v = c$$$ should be processed separately, in this case we can simply $$$ans[i] += g[c][i]$$$.
How much time it takes to compute dp? In fact, it is $$$\mathcal{O}(nk)$$$, $$$g[v][i]$$$ is equal to sum of $$$g[u][i - 1]$$$ where $$$u$$$ is neighbor of $$$v$$$. Since the graph is tree, there are $$$\mathcal{O}(n)$$$ neighbors in total and $$$\mathcal{O}(nk)$$$ transitions.
$$$f[v]$$$ is counted the same way, but with removed transitions through $$$c$$$. ——
The final complextiy is: $$$\mathcal{O}(n k^2 log(n))$$$.
$$$\O(\log(n))$$$ is for centroid decomposition.
On one level we need to compute dp $$$\mathcal{O}(nk)$$$ and then compute convolution $$$\mathcal{O}(nk^2)$$$, so it is $$$\mathcal{O}(n k^2 log(n))$$$.
Solution can be optimized with fast polynomial multiplication, leading to complexity $$$\mathcal{O}(n k \log(k) log(n))$$$, but it wasn't required.

