# Permutation Cycle

**题目ID**: 932/C  
**比赛**: ICM Technex 2018 and Codeforces Round 463 (Div. 1 + Div. 2, combined)  
**年份**: 2018  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

For a permutation P[1... N] of integers from 1 to N, function f is defined as follows:

$$$$

Let g(i) be the minimum positive integer j such that f(i, j) = i. We can show such j always exists.

For given N, A, B, find a permutation P of integers from 1 to N such that for 1 ≤ i ≤ N, g(i) equals either A or B.

## 输入格式

The only line contains three integers N, A, B (1 ≤ N ≤ 106, 1 ≤ A, B ≤ N).

## 输出格式

If no such permutation exists, output -1. Otherwise, output a permutation of integers from 1 to N.

## 样例

### 样例 1

**输入**:
```
9 2 5
```

**输出**:
```
6 5 8 3 4 1 9 2 7
```

### 样例 2

**输入**:
```
3 2 1
```

**输出**:
```
1 2 3
```

## 备注

In the first example, g(1) = g(6) = g(7) = g(9) = 2 and g(2) = g(3) = g(4) = g(5) = g(8) = 5

In the second example, g(1) = g(2) = g(3) = 1

## 题解

For f(i, j) = i and g(i) = k, there must exist a cycle of length k beginning from index i and ending at the same index i of permutation P. While generating a permutation P, we are constrained to generate cycles of length either A or B as g(i) for all 1 ≤ i ≤ N must be equal to either of them.
Let us try to generate a cycle of length k for indices i till i + k - 1 using only the integers i till i + k - 1, each once. If P[i] = i + k - 1 and P[j] = j - 1 for all i < j ≤ i + k - 1, we in turn get a cycle of length k for each of the indices i till i + k - 1, that is f(j, k) = j for all i ≤ j ≤ i + k - 1.
So, if there exists a solution (x, y) where x ≥ 0 and y ≥ 0, for Ax + By = N, we can in turn generate a permutation P satisfying our needs. Otherwise, no such permutation is possible.
So, now for any one of the solution (x, y), generate x cycles of length A, beginning from indices 1, A + 1, A * 2 + 1 ... A * (x - 1) + 1 and then beginning from indices A * x + 1, A * x + 1 + B, ... A * x + 1 + B * (y - 1), generate y cycles of length B.

