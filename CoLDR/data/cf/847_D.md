# Dog Show

**题目ID**: 847/D  
**比赛**: 2017-2018 ACM-ICPC, NEERC, Southern Subregional Contest, qualification stage (Online Mirror, ACM-ICPC Rules, Teams Preferred)  
**年份**: 2017  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

A new dog show on TV is starting next week. On the show dogs are required to demonstrate bottomless stomach, strategic thinking and self-preservation instinct. You and your dog are invited to compete with other participants and naturally you want to win!

On the show a dog needs to eat as many bowls of dog food as possible (bottomless stomach helps here). Dogs compete separately of each other and the rules are as follows:

At the start of the show the dog and the bowls are located on a line. The dog starts at position x = 0 and n bowls are located at positions x = 1, x = 2, ..., x = n. The bowls are numbered from 1 to n from left to right. After the show starts the dog immediately begins to run to the right to the first bowl.

The food inside bowls is not ready for eating at the start because it is too hot (dog's self-preservation instinct prevents eating). More formally, the dog can eat from the i-th bowl after ti seconds from the start of the show or later.

It takes dog 1 second to move from the position x to the position x + 1. The dog is not allowed to move to the left, the dog runs only to the right with the constant speed 1 distance unit per second. When the dog reaches a bowl (say, the bowl i), the following cases are possible:

- the food had cooled down (i.e. it passed at least ti seconds from the show start): the dog immediately eats the food and runs to the right without any stop,
- the food is hot (i.e. it passed less than ti seconds from the show start): the dog has two options: to wait for the i-th bowl, eat the food and continue to run at the moment ti or to skip the i-th bowl and continue to run to the right without any stop.

After T seconds from the start the show ends. If the dog reaches a bowl of food at moment T the dog can not eat it. The show stops before T seconds if the dog had run to the right of the last bowl.

You need to help your dog create a strategy with which the maximum possible number of bowls of food will be eaten in T seconds.

## 输入格式

Two integer numbers are given in the first line - n and T (1 ≤ n ≤ 200 000, 1 ≤ T ≤ 2·109) — the number of bowls of food and the time when the dog is stopped.

On the next line numbers t1, t2, ..., tn (1 ≤ ti ≤ 109) are given, where ti is the moment of time when the i-th bowl of food is ready for eating.

## 输出格式

Output a single integer — the maximum number of bowls of food the dog will be able to eat in T seconds.

## 样例

### 样例 1

**输入**:
```
3 5
1 5 3
```

**输出**:
```
2
```

### 样例 2

**输入**:
```
1 2
1
```

**输出**:
```
1
```

### 样例 3

**输入**:
```
1 1
1
```

**输出**:
```
0
```

## 备注

In the first example the dog should skip the second bowl to eat from the two bowls (the first and the third).

## 题解

Заметим, что вместо того чтобы ждать у каждой миски отдельно - собака может подождать x секунд в начале, и потом бежать вправо, съедая по пути все готовые миски.
Найдем для каждой миски с номером i отрезок которому должен принадлежать x, чтобы согбака эту миску съела. Чтобы успеть съесть миску до истечения времени, x должен строго меньше T - i. Чтобы еда успела отсыть, x должен быть не меньше ti - i + 1. Также x не может быть меньше 0.
Получив отрезки для каждой миски, надо найти точку покрытую наибольшим количеством отрезков. Для этого создадим список событий открытия и закрытия отрезков. Отсортируем этот список по позиции события. Если у двух событий позиция одинаковая, событие закрытия отрезка должно идти раньше. После этого пройдем по получившемуся списку слева направа, поддерживая количество открытых отрезков, прибавляя к нему 1 в случае события открытия, вычитая 1 в случае закрытия. Максимальное значение количества открытых отрезков во время такого прохода и будет ответом на задачу.

