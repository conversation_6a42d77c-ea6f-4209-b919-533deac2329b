# Bulmart

**题目ID**: 730/C  
**比赛**: 2016-2017 ACM-ICPC, NEERC, Southern Subregional Contest (Online Mirror, ACM-ICPC Rules, Teams Preferred)  
**年份**: 2016  
**时间限制**: 1.5秒  
**内存限制**: 512MB  

## 题目描述

A new trade empire is rising in Berland. Bulmart, an emerging trade giant, decided to dominate the market of ... shovels! And now almost every city in Berland has a Bulmart store, and some cities even have several of them! The only problem is, at the moment sales are ... let's say a little below estimates. Some people even say that shovels retail market is too small for such a big company to make a profit. But the company management believes in the future of that market and seeks new ways to increase income.

There are n cities in Berland connected with m bi-directional roads. All roads have equal lengths. It can happen that it is impossible to reach a city from another city using only roads. There is no road which connects a city to itself. Any pair of cities can be connected by at most one road.

There are w Bulmart stores in Berland. Each of them is described by three numbers:

- ci — the number of city where the i-th store is located (a city can have no stores at all or have several of them),
- ki — the number of shovels in the i-th store,
- pi — the price of a single shovel in the i-th store (in burles).

The latest idea of the Bulmart management is to create a program which will help customers get shovels as fast as possible for affordable budget. Formally, the program has to find the minimum amount of time needed to deliver rj shovels to the customer in the city gj for the total cost of no more than aj burles. The delivery time between any two adjacent cities is equal to 1. If shovels are delivered from several cities, the delivery time is equal to the arrival time of the last package. The delivery itself is free of charge.

The program needs to find answers to q such queries. Each query has to be processed independently from others, i.e. a query does not change number of shovels in stores for the next queries.

## 输入格式

The first line contains two integers n, m (1 ≤ n ≤ 5000, 0 ≤ m ≤ min(5000, n·(n - 1) / 2)). Each of the next m lines contains two integers xe and ye, meaning that the e-th road connects cities xe and ye (1 ≤ xe, ye ≤ n).

The next line contains a single integer w (1 ≤ w ≤ 5000) — the total number of Bulmart stores in Berland. Each of the next w lines contains three integers describing the i-th store: ci, ki, pi (1 ≤ ci ≤ n, 1 ≤ ki, pi ≤ 2·105).

The next line contains a single integer q (1 ≤ q ≤ 1000) — the number of queries. Each of the next q lines contains three integers describing the j-th query: gj, rj and aj (1 ≤ gj ≤ n, 1 ≤ rj, aj ≤ 109)

## 输出格式

Output q lines. On the j-th line, print an answer for the j-th query — the minimum amount of time needed to deliver rj shovels to the customer in city gj spending no more than aj burles. Print -1 if there is no solution for the j-th query.

## 样例

### 样例 1

**输入**:
```
6 4
4 2
5 4
1 2
3 2
2
4 1 2
3 2 3
6
1 2 6
2 3 7
3 1 2
4 3 8
5 2 5
6 1 10
```

**输出**:
```
2
-1
2
2
3
-1
```

## 题解

Let's sort all stores in increasing order of cost of one shovel and we need to process queries separately one by one.
Let for the current query the number city to deliver is g, the number of shovels to deliver is r and the cost must be no more than a. At first with help of bfs find the distance from the city g to the other cities. Then we need to make binary search on the maximum distance from the city g to the cities from which the shovels will be deliver for the current query.
In the objective function of the binary search we need to iterate through the stores in the sorted order of their cost and if the distance to the current store is no more than permitted we need to deliver from this city as many shovels as we can and as we need.
If for the current permitted distance we can deliver r shovels and it price no more than a move to the middle the right end of the binary search. In the other case move to the middle the left end of the binary search.
Do not forget clear the array with distances before process each query.

