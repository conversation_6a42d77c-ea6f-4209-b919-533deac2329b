# Divisibility by 25

**题目ID**: 988/E  
**比赛**: Codeforces Round 486 (Div. 3)  
**年份**: 2018  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

You are given an integer $$$n$$$ from $$$1$$$ to $$$10^{18}$$$ without leading zeroes.

In one move you can swap any two adjacent digits in the given number in such a way that the resulting number will not contain leading zeroes. In other words, after each move the number you have cannot contain any leading zeroes.

What is the minimum number of moves you have to make to obtain a number that is divisible by $$$25$$$? Print -1 if it is impossible to obtain a number that is divisible by $$$25$$$.

## 输入格式

The first line contains an integer $$$n$$$ ($$$1 \le n \le 10^{18}$$$). It is guaranteed that the first (left) digit of the number $$$n$$$ is not a zero.

## 输出格式

If it is impossible to obtain a number that is divisible by $$$25$$$, print -1. Otherwise print the minimum number of moves required to obtain such number.

Note that you can swap only adjacent digits in the given number.

## 样例

### 样例 1

**输入**:
```
5071
```

**输出**:
```
4
```

### 样例 2

**输入**:
```
705
```

**输出**:
```
1
```

### 样例 3

**输入**:
```
1241367
```

**输出**:
```
-1
```

## 备注

In the first example one of the possible sequences of moves is 5071 $$$\rightarrow$$$ 5701 $$$\rightarrow$$$ 7501 $$$\rightarrow$$$ 7510 $$$\rightarrow$$$ 7150.

## 题解

Let's iterate over all pairs of digits in the number. Let the first digit in the pair be at position $$$i$$$ and the second at position $$$j$$$. Let's place these digits to the last two positions in the number. The first greedily goes to the last position and then the second goes to the position next to that. Now the number can contain a leading zero. Find the leftmost non-zero digit and move it to the first position. Then if the current number is divisible by $$$25$$$ try to update the answer with the number of swaps. It is easy to show that the number of swaps is minimal in this algorithm. The only difference we can introduce is the number of times digit $$$i$$$, digit $$$j$$$ and the leftmost non-zero digit swap among themselves. And that is minimized. You can also notice that the order of swaps doesn't matter and you can rearrange them in such a way that no leading zero appears on any step.
This solution has time complexity $$$O(\log^3 n)$$$. You can also solve this problem with $$$O(\log n)$$$ complexity because you have to check only four options of the two last digits ($$$25$$$, $$$50$$$, $$$75$$$, $$$00$$$). It is always optimal to choose both rightmost occurrences of the corresponding digits. You can show that even if you are required to swap the chosen ones, there will be no other pair with smaller total amount of moves.

