# Perfect Number

**题目ID**: 919/B  
**比赛**: Codeforces Round 460 (Div. 2)  
**年份**: 2018  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

We consider a positive integer perfect, if and only if the sum of its digits is exactly $$$10$$$. Given a positive integer $$$k$$$, your task is to find the $$$k$$$-th smallest perfect positive integer.

## 输入格式

A single line with a positive integer $$$k$$$ ($$$1 \leq k \leq 10\,000$$$).

## 输出格式

A single number, denoting the $$$k$$$-th smallest perfect integer.

## 样例

### 样例 1

**输入**:
```
1
```

**输出**:
```
19
```

### 样例 2

**输入**:
```
2
```

**输出**:
```
28
```

## 备注

The first perfect integer is $$$19$$$ and the second one is $$$28$$$.

## 题解

Let's use brute force the find the answer.
You may find the answer is not too large (i.e. not bigger than $$$2 \cdot 10^7$$$), then you can find it in the given time limit.
You can check every possible answer from $$$1$$$ (or from $$$19$$$), until we find the $$$k$$$-th perfect integer.
That's all what we need to do. :P
Time complexity: $$$\mathcal{O}(answer)$$$. :P
Bonus1: Could you please come up with a faster brute force algorithm which doesn't need to check too many possible answers?
Bonus2: Can you solve the problem with a bigger $$$k$$$ (i.e. $$$k \leq 10^{18}$$$ or even bigger)?

