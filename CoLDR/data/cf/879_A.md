# <PERSON><PERSON>'s Diagnosis

**题目ID**: 879/A  
**比赛**: Codeforces Round 443 (Div. 2)  
**年份**: 2017  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

It seems that <PERSON><PERSON> is seriously sick. He is going visit n doctors to find out the exact diagnosis. Each of the doctors needs the information about all previous visits, so <PERSON><PERSON> has to visit them in the prescribed order (i.e. <PERSON><PERSON> should first visit doctor 1, then doctor 2, then doctor 3 and so on). <PERSON><PERSON> will get the information about his health from the last doctor.

Doctors have a strange working schedule. The doctor i goes to work on the si-th day and works every di day. So, he works on days si, si + di, si + 2di, ....

The doctor's appointment takes quite a long time, so <PERSON><PERSON> can not see more than one doctor per day. What is the minimum time he needs to visit all doctors?

## 输入格式

First line contains an integer n — number of doctors (1 ≤ n ≤ 1000).

Next n lines contain two numbers si and di (1 ≤ si, di ≤ 1000).

## 输出格式

Output a single integer — the minimum day at which <PERSON><PERSON> can visit the last doctor.

## 样例

### 样例 1

**输入**:
```
3
2 2
1 2
2 2
```

**输出**:
```
4
```

### 样例 2

**输入**:
```
2
10 1
6 5
```

**输出**:
```
11
```

## 备注

In the first sample case, Borya can visit all doctors on days 2, 3 and 4.

In the second sample case, <PERSON>rya can visit all doctors on days 10 and 11.

## 题解

Note that <PERSON>rya can use a greedy algorithm. He will visit each doctor as soon as possible. We only need to find the earliest day when he can do it. Constraints are pretty low, so we can use almost any reasonable way.
For example, we can just go through all the days, starting from the current one, and check if the doctor is working on that day. At the step i we need to go through at most max(si, di) days.
There is a more efficient way. We can find the smallest x that is greater than the current day, such that $$x \equiv s_i \bmod d_i$$, in O(1). If x ≥ si, Borya will visit a doctor on day x, otherwise on day si. This solution is O(n).

