# A Creative Cutout

**题目ID**: 933/D  
**比赛**: Codeforces Round 462 (Div. 1)  
**年份**: 2018  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

Everything red frightens <PERSON><PERSON> the monster. So do red paper and... you, red on Codeforces, potential or real.

Big <PERSON><PERSON> has got a piece of paper with endless lattice points, where lattice points form squares with the same area. His most favorite closed shape is the circle because of its beauty and simplicity. Once he had obtained this piece of paper, he prepares it for paper-cutting.

He drew n concentric circles on it and numbered these circles from 1 to n such that the center of each circle is the same lattice point and the radius of the k-th circle is $$\sqrt{k}$$ times the length of a lattice edge.

Define the degree of beauty of a lattice point as the summation of the indices of circles such that this lattice point is inside them, or on their bounds. <PERSON><PERSON> wanted to ask you the total degree of beauty of all the lattice points, but changed his mind.

Defining the total degree of beauty of all the lattice points on a piece of paper with n circles as f(n), you are asked to figure out $$(\sum_{k=1}^{m} f(k)) \bmod (10^9 + 7)$$.

## 输入格式

The first line contains one integer m (1 ≤ m ≤ 1012).

## 输出格式

In the first line print one integer representing $$(\sum_{k=1}^{m} f(k)) \bmod (10^9 + 7)$$.

## 样例

### 样例 1

**输入**:
```
5
```

**输出**:
```
387
```

### 样例 2

**输入**:
```
233
```

**输出**:
```
788243189
```

## 备注

A piece of paper with 5 circles is shown in the following.

There are 5 types of lattice points where the degree of beauty of each red point is 1 + 2 + 3 + 4 + 5 = 15, the degree of beauty of each orange point is 2 + 3 + 4 + 5 = 14, the degree of beauty of each green point is 4 + 5 = 9, the degree of beauty of each blue point is 5 and the degree of beauty of each gray point is 0. Therefore, f(5) = 5·15 + 4·14 + 4·9 + 8·5 = 207.

Similarly, f(1) = 5, f(2) = 23, f(3) = 50, f(4) = 102 and consequently $$(\sum_{k=1}^{5} f(k)) \bmod (10^9 + 7) = 387$$.

## 题解

For the sake of explanation, let's use $${\binom {n}{k}}$$ to represent the binomial coefficient $$\frac{n!}{k!(n-k)!}$$ and construct a coordinate system such that each coordinate axis parallels to one of the lattice edges, the origin is the center of concentric circles and each unit of length in this system is as long as the length of a lattice edge.
For f(n), we could figure out the contribution of each lattice point (x, y) is
Defining L as x2 + y2, we could conclude for the answer the contribution of each lattice point (x, y) is
By using $$(x^2+y^2)^n=\sum_{k=0}^{n}\binom{n}{k}x^{2k}y^{2(n-k)}$$, we could form the answer as
The remaining part to solve this problem is just to enumerate all the possible integers x, q and then calculate $$\sum_{p=0}^{3-q} \operatorname{coeff}_{p,q} x^{2p}$$, $$\sum_{y \in \mathbb{Z}, |y| \leq \sqrt{m - x^2}} y^{2q}$$ in constant time. The total complexity is $${\mathcal {O}}({\sqrt {m}})$$.
By the way, the standard solution has hardcoded some closed forms to calculate the partial sum of small powers fast, but you can precalculate $$s_{q}(k) = \sum_{y \in \mathbb{Z}, |y| \leq k} y^{2q}$$ and then enumerate x.
Please be careful with 64-bit integer overflow, for example, 1012·109 ≥ 264. Although there is a pretest in case of m = 232 to reject brute force and some solutions with obvious overflow, it is highly probable to fail in the case of large input such as m = 1012. The probability of failure is increasing when the number increases. Take care.
Bonus. Solve the problem with 1 ≤ m ≤ 1018.

