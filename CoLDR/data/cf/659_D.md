# Bicycle Race

**题目ID**: 659/D  
**比赛**: Codeforces Round 346 (Div. 2)  
**年份**: 2016  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON> participates in a bicycle race.

The speedway takes place on the shores of Lake Lucerne, just repeating its contour. As you know, the lake shore consists only of straight sections, directed to the north, south, east or west.

Let's introduce a system of coordinates, directing the Ox axis from west to east, and the Oy axis from south to north. As a starting position of the race the southernmost point of the track is selected (and if there are several such points, the most western among them). The participants start the race, moving to the north. At all straight sections of the track, the participants travel in one of the four directions (north, south, east or west) and change the direction of movement only in bends between the straight sections. The participants, of course, never turn back, that is, they do not change the direction of movement from north to south or from east to west (or vice versa).

Maria is still young, so she does not feel confident at some turns. Namely, <PERSON> feels insecure if at a failed or untimely turn, she gets into the water. In other words, <PERSON> considers the turn dangerous if she immediately gets into the water if it is ignored.

Help <PERSON> get ready for the competition — determine the number of dangerous turns on the track.

## 输入格式

The first line of the input contains an integer n (4 ≤ n ≤ 1000) — the number of straight sections of the track.

The following (n + 1)-th line contains pairs of integers (xi, yi) ( - 10 000 ≤ xi, yi ≤ 10 000). The first of these points is the starting position. The i-th straight section of the track begins at the point (xi, yi) and ends at the point (xi + 1, yi + 1).

It is guaranteed that:

- the first straight section is directed to the north;
- the southernmost (and if there are several, then the most western of among them) point of the track is the first point;
- the last point coincides with the first one (i.e., the start position);
- any pair of straight sections of the track has no shared points (except for the neighboring ones, they share exactly one point);
- no pair of points (except for the first and last one) is the same;
- no two adjacent straight sections are directed in the same direction or in opposite directions.

## 输出格式

Print a single integer — the number of dangerous turns on the track.

## 样例

### 样例 1

**输入**:
```
6
0 0
0 1
1 1
1 2
2 2
2 0
0 0
```

**输出**:
```
1
```

### 样例 2

**输入**:
```
16
1 1
1 5
3 5
3 7
2 7
2 9
6 9
6 7
5 7
5 3
4 3
4 4
3 4
3 2
5 2
5 1
1 1
```

**输出**:
```
6
```

## 备注

The first sample corresponds to the picture:

The picture shows that you can get in the water under unfortunate circumstances only at turn at the point (1, 1). Thus, the answer is 1.

## 题解

From the track description follows that Maria moves the way that the water always located to the right from her, so she could fall into the water only while turning left. To check if the turn is to the left, let's give every Maria's moves directions a number: moving to the north — 0, moving to the west — 1, to the south — 2 and to the east — 3. Then the turn is to the left if and only if the number of direction after performing a turn dir is equal to the number before performing a turn oldDir plus one modulo 4 $$( \mathrm { d i r } \equiv o l d D i r + 1 ( \mod 4 ) )$$.
This solution has complexity O(n).
One can solve this problem in alternative way. Let the answer be equal to x (that means that the number of inner corners of 270 degrees equals x, but the number of inner corners of 90 degrees to n - x). As soon as the sum of the inner corners' values of polygon of n vertices is equal to 180 × (n - 2), then x × 270 + (n - x) × 90 equals to 180 × (n - 2). This leads us to $$x = \frac{n-4}{2}$$, being the answer for the problem calculated in O(1).

