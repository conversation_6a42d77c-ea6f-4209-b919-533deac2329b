# Hexadecimal's Numbers

**题目ID**: 9/C  
**比赛**: Codeforces Beta Round 9 (Div. 2 Only)  
**年份**: 2010  
**时间限制**: 1.0秒  
**内存限制**: 64MB  

## 题目描述

One beautiful July morning a terrible thing happened in Mainframe: a mean virus <PERSON><PERSON><PERSON> somehow got access to the memory of his not less mean sister <PERSON><PERSON><PERSON><PERSON>. He loaded there a huge amount of n different natural numbers from 1 to n to obtain total control over her energy.

But his plan failed. The reason for this was very simple: Hexadecimal didn't perceive any information, apart from numbers written in binary format. This means that if a number in a decimal representation contained characters apart from 0 and 1, it was not stored in the memory. Now Megabyte wants to know, how many numbers were loaded successfully.

## 输入格式

Input data contains the only number n (1 ≤ n ≤ 109).

## 输出格式

Output the only number — answer to the problem.

## 样例

### 样例 1

**输入**:
```
10
```

**输出**:
```
2
```

## 备注

For n = 10 the answer includes numbers 1 and 10.

