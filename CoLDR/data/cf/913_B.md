# Christmas Spruce

**题目ID**: 913/B  
**比赛**: Hello 2018  
**年份**: 2018  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

Consider a rooted tree. A rooted tree has one special vertex called the root. All edges are directed from the root. Vertex u is called a child of vertex v and vertex v is called a parent of vertex u if there exists a directed edge from v to u. A vertex is called a leaf if it doesn't have children and has a parent.

Let's call a rooted tree a spruce if its every non-leaf vertex has at least 3 leaf children. You are given a rooted tree, check whether it's a spruce.

The definition of a rooted tree can be found here.

## 输入格式

The first line contains one integer n — the number of vertices in the tree (3 ≤ n ≤ 1 000). Each of the next n - 1 lines contains one integer pi (1 ≤ i ≤ n - 1) — the index of the parent of the i + 1-th vertex (1 ≤ pi ≤ i).

Vertex 1 is the root. It's guaranteed that the root has at least 2 children.

## 输出格式

Print "Yes" if the tree is a spruce and "No" otherwise.

## 样例

### 样例 1

**输入**:
```
4
1
1
1
```

**输出**:
```
Yes
```

### 样例 2

**输入**:
```
7
1
1
1
2
2
2
```

**输出**:
```
No
```

### 样例 3

**输入**:
```
8
1
1
1
1
3
3
3
```

**输出**:
```
Yes
```

## 备注

The first example:

The second example:

It is not a spruce, because the non-leaf vertex 1 has only 2 leaf children.

The third example:

## 题解

Lets calculate amount of children for each vertex. To do that lets increase by 1 c[pi] for every pi. Then iterate over all vertexes. If i-th vertex has 0 children (i.e. c[i] = 0), skip this vertex. Else again iterate over all vertexes and calculate number of vertexes j such that c[j] = 0 and pj = i. If this number is lower than 3, answer is "No". Else answer is "Yes".

