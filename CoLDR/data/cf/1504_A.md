# Déjà Vu

**题目ID**: 1504/A  
**比赛**: Codeforces Round 712 (Div. 2)  
**年份**: 2021  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

A palindrome is a string that reads the same backward as forward. For example, the strings "z", "aaa", "aba", and "abccba" are palindromes, but "codeforces" and "ab" are not. You hate palindromes because they give you déjà vu.

There is a string $$$s$$$. You must insert exactly one character 'a' somewhere in $$$s$$$. If it is possible to create a string that is not a palindrome, you should find one example. Otherwise, you should report that it is impossible.

For example, suppose $$$s=$$$ "cbabc". By inserting an 'a', you can create "acbabc", "cababc", "cbaabc", "cbabac", or "cbabca". However "cbaabc" is a palindrome, so you must output one of the other options.

## 输入格式

The first line contains a single integer $$$t$$$ ($$$1\le t\le 10^4$$$) — the number of test cases.

The only line of each test case contains a string $$$s$$$ consisting of lowercase English letters.

The total length of all strings does not exceed $$$3\cdot 10^5$$$.

## 输出格式

For each test case, if there is no solution, output "NO".

Otherwise, output "YES" followed by your constructed string of length $$$|s|+1$$$ on the next line. If there are multiple solutions, you may print any.

You can print each letter of "YES" and "NO" in any case (upper or lower).

## 样例

### 样例 1

**输入**:
```
6
cbabc
ab
zza
ba
a
nutforajaroftuna
```

**输出**:
```
YES
cbabac
YES
aab
YES
zaza
YES
baa
NO
YES
nutforajarofatuna
```

## 备注

The first test case is described in the statement.

In the second test case, we can make either "aab" or "aba". But "aba" is a palindrome, so "aab" is the only correct answer.

In the third test case, "zaza" and "zzaa" are correct answers, but not "azza".

In the fourth test case, "baa" is the only correct answer.

In the fifth test case, we can only make "aa", which is a palindrome. So the answer is "NO".

In the sixth test case, "anutforajaroftuna" is a palindrome, but inserting 'a' elsewhere is valid.

## 题解

If $$$s$$$ is the character 'a' repeated some number of times, there is no solution. Otherwise, I claim either 'a' + $$$s$$$ or $$$s$$$ + 'a' is a solution (or both).
Let's prove it. Assume for contradiction that 'a' + $$$s$$$ and $$$s$$$ + 'a' are both palindromes. Then the first and last characters of $$$s$$$ are 'a'. Then the second and second to last characters of $$$s$$$ are 'a'. Repeating this, we see that all characters of $$$s$$$ are 'a', but we assumed we are not in this case. Therefore, the claim is true.
The solution is simply to check if 'a' + $$$s$$$ and $$$s$$$ + 'a' are palindromes and output the correct one. Complexity is $$$O(n)$$$.

