# The Number of Pairs

**题目ID**: 1499/D  
**比赛**: Educational Codeforces Round 106 (Rated for Div. 2)  
**年份**: 2021  
**时间限制**: 2.0秒  
**内存限制**: 512MB  

## 题目描述

You are given three positive (greater than zero) integers $$$c$$$, $$$d$$$ and $$$x$$$.

You have to find the number of pairs of positive integers $$$(a, b)$$$ such that equality $$$c \cdot lcm(a, b) - d \cdot gcd(a, b) = x$$$ holds. Where $$$lcm(a, b)$$$ is the least common multiple of $$$a$$$ and $$$b$$$ and $$$gcd(a, b)$$$ is the greatest common divisor of $$$a$$$ and $$$b$$$.

## 输入格式

The first line contains one integer $$$t$$$ ($$$1 \le t \le 10^4$$$) — the number of test cases.

Each test case consists of one line containing three integer $$$c$$$, $$$d$$$ and $$$x$$$ ($$$1 \le c, d, x \le 10^7$$$).

## 输出格式

For each test case, print one integer — the number of pairs ($$$a, b$$$) such that the above equality holds.

## 样例

### 样例 1

**输入**:
```
4
1 1 3
4 2 6
3 3 7
2 7 25
```

**输出**:
```
4
3
0
8
```

## 备注

In the first example, the correct pairs are: ($$$1, 4$$$), ($$$4,1$$$), ($$$3, 6$$$), ($$$6, 3$$$).

In the second example, the correct pairs are: ($$$1, 2$$$), ($$$2, 1$$$), ($$$3, 3$$$).

## 题解

Let's represent $$$a$$$ as $$$Ag$$$ and $$$b$$$ as $$$Bg$$$, where $$$g=gcd(a,b)$$$ and $$$gcd(A,B)=1$$$. By definition $$$lcm(a,b) = \frac{ab}{g}$$$, so we can represent $$$lcm(a,b)$$$ as $$$\frac{Ag \cdot Bg}{g} = ABg$$$.
Now we can rewrite the equation from the statement as follows: $$$c \cdot ABg - d \cdot g = x \implies g(c \cdot AB - d) = x$$$. Since the left-hand side is divisible by $$$g$$$, the right-hand side should also be divisible. So we can iterate over $$$g$$$ as divisors of $$$x$$$. If the right-hand side of $$$c \cdot AB = \frac{x}{g} + d$$$ is not divisible by $$$c$$$ then we can skip such $$$g$$$. $$$AB = \frac{\frac{x}{g}+d}{c}$$$ (let's denote as $$$k$$$). If $$$k$$$ has some prime divisor $$$p$$$ then exactly one of $$$A$$$ and $$$B$$$ should be divisible by $$$p$$$ because $$$gcd(A,B)=1$$$ ($$$A$$$ and $$$B$$$ have no common divisors). So there are $$$2^{\textit{the number of prime divisors of k}}$$$ pairs of $$$A$$$ and $$$B$$$ for current value of $$$g$$$.
We can precalculate the minimum prime divisor for each number up to $$$2 \cdot 10^7$$$ (the maximum value of $$$k$$$ that you may need) in $$$O(2 \cdot 10^7 \log{\log{(2 \cdot 10^7)}})$$$ using Eratosthenes sieve. Now we can solve the problem in $$$O(\sqrt{x}\log{x})$$$ for each testcase, but that's not fast enough. To speed up this approach, we can precalculate the number of prime divisors for each number up to $$$2 \cdot 10^7$$$. Let's denote $$$mind_i$$$ as the minimum prime divisor of $$$i$$$ and $$$val_i$$$ as the number of prime divisors of $$$i$$$. Then $$$val_i = val_{i / mind_i}$$$ plus $$$1$$$ if $$$mind_i \neq mind_{i / mind_i}$$$. Now, to solve the problem, we only need to iterate over the divisors of $$$x$$$, so the time complexity is $$$O(\sqrt{x})$$$ per testcase.

