# Love Triangle

**题目ID**: 939/A  
**比赛**: Codeforces Round 464 (Div. 2)  
**年份**: 2018  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

As you could know there are no male planes nor female planes. However, each plane on Earth likes some other plane. There are n planes on Earth, numbered from 1 to n, and the plane with number i likes the plane with number fi, where 1 ≤ fi ≤ n and fi ≠ i.

We call a love triangle a situation in which plane A likes plane B, plane B likes plane C and plane C likes plane A. Find out if there is any love triangle on Earth.

## 输入格式

The first line contains a single integer n (2 ≤ n ≤ 5000) — the number of planes.

The second line contains n integers f1, f2, ..., fn (1 ≤ fi ≤ n, fi ≠ i), meaning that the i-th plane likes the fi-th.

## 输出格式

Output «YES» if there is a love triangle consisting of planes on Earth. Otherwise, output «NO».

You can output any letter in lower case or in upper case.

## 样例

### 样例 1

**输入**:
```
5
2 4 5 1 3
```

**输出**:
```
YES
```

### 样例 2

**输入**:
```
5
5 5 5 5 1
```

**输出**:
```
NO
```

## 备注

In first example plane 2 likes plane 4, plane 4 likes plane 1, plane 1 likes plane 2 and that is a love triangle.

In second example there are no love triangles.

## 题解

It is enough to check if there is some i such that fffi = i, i. e. f[f[f[i]]] == i.

