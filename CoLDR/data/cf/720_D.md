# Slalom

**题目ID**: 720/D  
**比赛**: Russian Code Cup 2016 - Finals [Unofficial Mirror, Div. 1 Only Recommended]  
**年份**: 2016  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

Little girl <PERSON><PERSON> likes winter sports, today she's planning to take part in slalom skiing.

The track is represented as a grid composed of n × m squares. There are rectangular obstacles at the track, composed of grid squares. <PERSON><PERSON> must get from the square (1, 1) to the square (n, m). She can move from a square to adjacent square: either to the right, or upwards. If the square is occupied by an obstacle, it is not allowed to move to that square.

One can see that each obstacle can actually be passed in two ways: either it is to the right of <PERSON><PERSON>'s path, or to the left. <PERSON><PERSON> likes to try all ways to do things, so she would like to know how many ways are there to pass the track. Two ways are considered different if there is an obstacle such that it is to the right of the path in one way, and to the left of the path in the other way.

Help <PERSON><PERSON> to find the number of ways to pass the track. The number of ways can be quite big, so <PERSON><PERSON> would like to know it modulo 109 + 7.

The pictures below show different ways to pass the track in sample tests.

## 输入格式

The first line of input data contains three positive integers: n, m and k (3 ≤ n, m ≤ 106, 0 ≤ k ≤ 105) — the size of the track and the number of obstacles.

The following k lines contain four positive integers each: x1, y1, x2, y2 (1 ≤ x1 ≤ x2 ≤ n, 1 ≤ y1 ≤ y2 ≤ m) — coordinates of bottom left, and top right squares of the obstacle.

It is guaranteed that there are no obstacles at squares (1, 1) and (n, m), and no obstacles overlap (but some of them may touch).

## 输出格式

Output one integer — the number of ways to pass the track modulo 109 + 7.

## 样例

### 样例 1

**输入**:
```
3 3 0
```

**输出**:
```
1
```

### 样例 2

**输入**:
```
4 5 1
2 2 3 4
```

**输出**:
```
2
```

### 样例 3

**输入**:
```
5 5 3
2 2 2 3
4 2 5 2
4 4 4 4
```

**输出**:
```
3
```

## 题解

First let us consider all paths from the starting square to the finish one. Let us say that two paths are equivalent, if each obstacle is at the same side for both paths. For each class of equivalence let us choose the representative path — the one that tries to go as low as possible, lexicographically minimum.
Let us use dynamic programming. For each square let us count the number of representative paths that go from the starting square to this one. When the obstacle starts, some paths can now separate. The new representatives will pass this obstacle from above (it will be to the right of them). So we add the sum of values for squares below it, but above any other lower obstacle, to the value for the square right above the obstacle.
To overcome the time and memory limits that the naive solution with O(nm) memory and O(nm2) time complexity, we use segment tree for range sum queries with mass update, running scanline and events "start of an obstacle", "end of an obstacle". This leads to the solution with O(m) memory and O(n log m) time complexity.

