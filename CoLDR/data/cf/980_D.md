# Perfect Groups

**题目ID**: 980/D  
**比赛**: Codeforces Round 480 (Div. 2)  
**年份**: 2018  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON><PERSON><PERSON> has written the greatest test case of all time for one of his problems. For a given array of integers, the problem asks to find the minimum number of groups the array can be divided into, such that the product of any pair of integers in the same group is a perfect square.

Each integer must be in exactly one group. However, integers in a group do not necessarily have to be contiguous in the array.

<PERSON><PERSON><PERSON> wishes to create more cases from the test case he already has. His test case has an array $$$A$$$ of $$$n$$$ integers, and he needs to find the number of contiguous subarrays of $$$A$$$ that have an answer to the problem equal to $$$k$$$ for each integer $$$k$$$ between $$$1$$$ and $$$n$$$ (inclusive).

## 输入格式

The first line of input contains a single integer $$$n$$$ ($$$1 \leq n \leq 5000$$$), the size of the array.

The second line contains $$$n$$$ integers $$$a_1$$$,$$$a_2$$$,$$$\dots$$$,$$$a_n$$$ ($$$-10^8 \leq a_i \leq 10^8$$$), the values of the array.

## 输出格式

Output $$$n$$$ space-separated integers, the $$$k$$$-th integer should be the number of contiguous subarrays of $$$A$$$ that have an answer to the problem equal to $$$k$$$.

## 样例

### 样例 1

**输入**:
```
2
5 5
```

**输出**:
```
3 0
```

### 样例 2

**输入**:
```
5
5 -4 2 1 8
```

**输出**:
```
5 5 3 2 0
```

### 样例 3

**输入**:
```
1
0
```

**输出**:
```
1
```

## 题解

First let us examine perfect squares; a perfect square is a number $$$x$$$ that can be written as the product of an integer and itself $$$y*y$$$. This means that for each prime factor of the number $$$x$$$, the frequency of that factor must be even so it can be distributed evenly between each of the two $$$y$$$'s.
This leads to the idea that for every two numbers $$$a$$$ and $$$b$$$ in a group, for a prime factor $$$p_{i}$$$, either both $$$a$$$ and $$$b$$$ have an even frequency of $$$p_{i}$$$, or they both have an odd frequency of it.
So using that observation, for each number $$$x$$$ in the array we can discard all pairs of equal prime factors (keeping one copy of each factor with an odd frequency).
For example, number $$$40$$$ has factors $$${2, 2, 2, 5}$$$, so we can just ignore the first pair of $$$2's$$$ because they're redundant and transform it into $$${2, 5}$$$, which is the number $$$10$$$.
Now after replacing each number with the product of its odd factors, we can just count the number of distinct elements in each subarray as each element can be only grouped with its copies. This can be done by checking all possible subarrays and keeping a frequency array to count the number of distinct elements in it.
- Elements need to be mapped to non-negative integers between $$$1$$$ and $$$n$$$ so we don't use a set to count them.
- Need to be careful in case there's a zero in the subarray. Zeros can join any group, so unless the subarray contains only zeros, we can ignore them.
Solution Complexity: $$$O(n\times sqrt(max a_i) + n^{2})$$$

