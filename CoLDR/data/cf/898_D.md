# Alarm Clock

**题目ID**: 898/D  
**比赛**: Codeforces Round 451 (Div. 2)  
**年份**: 2017  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

Every evening <PERSON><PERSON> sets n alarm clocks to wake up tomorrow. Every alarm clock rings during exactly one minute and is characterized by one integer ai — number of minute after midnight in which it rings. Every alarm clock begins ringing at the beginning of the minute and rings during whole minute.

<PERSON><PERSON> will definitely wake up if during some m consecutive minutes at least k alarm clocks will begin ringing. Pay attention that <PERSON><PERSON> considers only alarm clocks which begin ringing during given period of time. He doesn't consider alarm clocks which started ringing before given period of time and continues ringing during given period of time.

<PERSON><PERSON> is so tired that he wants to sleep all day long and not to wake up. Find out minimal number of alarm clocks <PERSON><PERSON> should turn off to sleep all next day. Now all alarm clocks are turned on.

## 输入格式

First line contains three integers n, m and k (1 ≤ k ≤ n ≤ 2·105, 1 ≤ m ≤ 106) — number of alarm clocks, and conditions of <PERSON><PERSON>'s waking up.

Second line contains sequence of distinct integers a1, a2, ..., an (1 ≤ ai ≤ 106) in which ai equals minute on which i-th alarm clock will ring. Numbers are given in arbitrary order. <PERSON><PERSON> lives in a Berland in which day lasts for 106 minutes.

## 输出格式

Output minimal number of alarm clocks that <PERSON><PERSON> should turn off to sleep all next day long.

## 样例

### 样例 1

**输入**:
```
3 3 2
3 5 1
```

**输出**:
```
1
```

### 样例 2

**输入**:
```
5 10 3
12 8 18 25 1
```

**输出**:
```
0
```

### 样例 3

**输入**:
```
7 7 2
7 3 4 1 6 5 2
```

**输出**:
```
6
```

### 样例 4

**输入**:
```
2 2 2
1 3
```

**输出**:
```
0
```

## 备注

In first example Vitalya should turn off first alarm clock which rings at minute 3.

In second example Vitalya shouldn't turn off any alarm clock because there are no interval of 10 consequence minutes in which 3 alarm clocks will ring.

In third example Vitalya should turn off any 6 alarm clocks.

## 题解

At first we need to sort all alarms in increasing order of their times. Also we will use set, where we will store alarm times.
We will iterate through the alarms beginning from the first. Let current alarm time equals to x. Until set does not empty and the first set element less than x - m + 1 we should remove the first set element. After that only alarm with times not before m - 1 minutes relatively x will be in set. If after that the set size less than k - 1 we should insert x in the set (we will not turn off this alarm). In the other case, we should turn off this alarm, so we increase the answer on one and do not insert x in the set.

