# <PERSON> and Making Potions

**题目ID**: 734/C  
**比赛**: Codeforces Round 379 (Div. 2)  
**年份**: 2016  
**时间限制**: 4.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON> is playing a very interesting computer game, but now he is stuck at one of the levels. To pass to the next level he has to prepare n potions.

<PERSON> has a special kettle, that can prepare one potions in x seconds. Also, he knows spells of two types that can faster the process of preparing potions.

1. Spells of this type speed up the preparation time of one potion. There are m spells of this type, the i-th of them costs bi manapoints and changes the preparation time of each potion to ai instead of x.
2. Spells of this type immediately prepare some number of potions. There are k such spells, the i-th of them costs di manapoints and instantly create ci potions.

<PERSON> can use no more than one spell of the first type and no more than one spell of the second type, and the total number of manapoints spent should not exceed s. Consider that all spells are used instantly and right before <PERSON> starts to prepare potions.

<PERSON> wants to get to the next level as fast as possible, so he is interested in the minimum number of time he needs to spent in order to prepare at least n potions.

## 输入格式

The first line of the input contains three integers n, m, k (1 ≤ n ≤ 2·109, 1 ≤ m, k ≤ 2·105) — the number of potions, <PERSON> has to make, the number of spells of the first type and the number of spells of the second type.

The second line of the input contains two integers x and s (2 ≤ x ≤ 2·109, 1 ≤ s ≤ 2·109) — the initial number of seconds required to prepare one potion and the number of manapoints Anton can use.

The third line contains m integers ai (1 ≤ ai < x) — the number of seconds it will take to prepare one potion if the i-th spell of the first type is used.

The fourth line contains m integers bi (1 ≤ bi ≤ 2·109) — the number of manapoints to use the i-th spell of the first type.

There are k integers ci (1 ≤ ci ≤ n) in the fifth line — the number of potions that will be immediately created if the i-th spell of the second type is used. It's guaranteed that ci are not decreasing, i.e. ci ≤ cj if i < j.

The sixth line contains k integers di (1 ≤ di ≤ 2·109) — the number of manapoints required to use the i-th spell of the second type. It's guaranteed that di are not decreasing, i.e. di ≤ dj if i < j.

## 输出格式

Print one integer — the minimum time one has to spent in order to prepare n potions.

## 样例

### 样例 1

**输入**:
```
20 3 2
10 99
2 4 3
20 10 40
4 15
10 80
```

**输出**:
```
20
```

### 样例 2

**输入**:
```
20 3 2
10 99
2 4 3
200 100 400
4 15
100 800
```

**输出**:
```
200
```

## 备注

In the first sample, the optimum answer is to use the second spell of the first type that costs 10 manapoints. Thus, the preparation time of each potion changes to 4 seconds. Also, Anton should use the second spell of the second type to instantly prepare 15 potions spending 80 manapoints. The total number of manapoints used is 10 + 80 = 90, and the preparation time is 4·5 = 20 seconds (15 potions were prepared instantly, and the remaining 5 will take 4 seconds each).

In the second sample, Anton can't use any of the spells, so he just prepares 20 potions, spending 10 seconds on each of them and the answer is 20·10 = 200.

## 题解

At first, observe that if we'll take the i-th potion of the first type and the j-th potion of the second type, then we can prepare all the potions in $$a_i \cdot (n - c_j)$$ seconds. So, we have to minimize this number.
Let's iterate over what potion of the first type we'll use. Then we must find such spell of the second type that will prepare instantly as many as possible potions, and we'll have enough manapoints for it. It can be done using binary search, because the characteristics of potions of the second type — ci and di are sorted in non-decreasing order.
Time complexity is $${ \mathcal { O } } ( m \cdot \log k + k )$$.

