# The Artful Expedient

**题目ID**: 869/A  
**比赛**: Codeforces Round 439 (Div. 2)  
**年份**: 2017  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

Rock... Paper!

After <PERSON> have found the deterministic winning (losing?) strategy for rock-paper-scissors, her brother, <PERSON><PERSON><PERSON>, comes up with a new game as a substitute. The game works as follows.

A positive integer n is decided first. Both <PERSON><PERSON><PERSON> and <PERSON> independently choose n distinct positive integers, denoted by x1, x2, ..., xn and y1, y2, ..., yn respectively. They reveal their sequences, and repeat until all of 2n integers become distinct, which is the only final state to be kept and considered.

Then they count the number of ordered pairs (i, j) (1 ≤ i, j ≤ n) such that the value xi xor yj equals to one of the 2n integers. Here xor means the bitwise exclusive or operation on two integers, and is denoted by operators ^ and/or xor in most programming languages.

<PERSON> claims a win if the number of such pairs is even, and <PERSON><PERSON><PERSON> does otherwise. And you're here to help determine the winner of their latest game.

## 输入格式

The first line of input contains a positive integer n (1 ≤ n ≤ 2 000) — the length of both sequences.

The second line contains n space-separated integers x1, x2, ..., xn (1 ≤ xi ≤ 2·106) — the integers finally chosen by <PERSON><PERSON>mi.

The third line contains n space-separated integers y1, y2, ..., yn (1 ≤ yi ≤ 2·106) — the integers finally chosen by Karen.

Input guarantees that the given 2n integers are pairwise distinct, that is, no pair (i, j) (1 ≤ i, j ≤ n) exists such that one of the following holds: xi = yj; i ≠ j and xi = xj; i ≠ j and yi = yj.

## 输出格式

Output one line — the name of the winner, that is, "Koyomi" or "Karen" (without quotes). Please be aware of the capitalization.

## 样例

### 样例 1

**输入**:
```
3
1 2 3
4 5 6
```

**输出**:
```
Karen
```

### 样例 2

**输入**:
```
5
2 4 6 8 10
9 7 5 3 1
```

**输出**:
```
Karen
```

## 备注

In the first example, there are 6 pairs satisfying the constraint: (1, 1), (1, 2), (2, 1), (2, 3), (3, 2) and (3, 3). Thus, Karen wins since 6 is an even number.

In the second example, there are 16 such pairs, and Karen wins again.

## 题解

First approach: Optimize the straightforward solution.
The O(n3) solution is to iterate through (i, j) pairs, then iterate over k and check whether xi xor yj equals either xk or yk. But it doesn't fit into the time limit.
We try to get rid of the k loop and make the check faster. Here's the insight: we create an array a, and let a[i] denote "whether value i appears in the given 2n integers". In this way we can make the check comsume O(1) time (with O(n) preprocessing for a), resulting in an O(n2) overall time complexity. Please see the model solution for an implementation.
A detail worth mentioning is that xi xor yj may exceed 2·106 and become as large as 2097152 = 221. Thus the array should be of size 2097152 instead of 2·106 and if not, invalid memory access may take place.
Second approach: Believe in magic.
Let's forget about all loops and algorithmic stuff and start fresh. What's the parity of the answer?
Looking at the samples again, do note that Karen scores two consecutive wins. The fact is that, Karen always wins.
Proof. For any pair (i, j), if an index k exists such that xi xor yj  = xk, then this k is unique since all 2n integers are distinct. Then, pair (k, j) also fulfills the requirement, since xk xor yj  = xi. The similar goes for cases where xi xor yj  = yk. Therefore, each valid pair satisfying the requirement can be mapped to exactly another valid pair, and the mapping is unique and involutory (that is, f(f(u)) = u). Thus, the number of such pairs is always even.
So, Karen still claims her constant win. Maybe it's Koyomi's obscure reconciliation ;)

