# News About Credit

**题目ID**: 769/B  
**比赛**: VK Cup 2017 - Qualification 1  
**年份**: 2017  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON><PERSON><PERSON><PERSON> studies at the university in the group which consists of n students (including himself). All they are registrated in the social net "TheContacnt!".

Not all students are equally sociable. About each student you know the value ai — the maximum number of messages which the i-th student is agree to send per day. The student can't send messages to himself.

In early morning <PERSON><PERSON><PERSON><PERSON> knew important news that the programming credit will be tomorrow. For this reason it is necessary to urgently inform all groupmates about this news using private messages.

Your task is to make a plan of using private messages, so that:

- the student i sends no more than ai messages (for all i from 1 to n);
- all students knew the news about the credit (initially only <PERSON><PERSON><PERSON><PERSON> knew it);
- the student can inform the other student only if he knows it himself.

Let's consider that all students are numerated by distinct numbers from 1 to n, and <PERSON><PERSON><PERSON><PERSON> always has the number 1.

In that task you shouldn't minimize the number of messages, the moment of time, when all knew about credit or some other parameters. Find any way how to use private messages which satisfies requirements above.

## 输入格式

The first line contains the positive integer n (2 ≤ n ≤ 100) — the number of students.

The second line contains the sequence a1, a2, ..., an (0 ≤ ai ≤ 100), where ai equals to the maximum number of messages which can the i-th student agree to send. Consider that <PERSON>ycarp always has the number 1.

## 输出格式

Print -1 to the first line if it is impossible to inform all students about credit.

Otherwise, in the first line print the integer k — the number of messages which will be sent. In each of the next k lines print two distinct integers f and t, meaning that the student number f sent the message with news to the student number t. All messages should be printed in chronological order. It means that the student, who is sending the message, must already know this news. It is assumed that students can receive repeated messages with news of the credit.

If there are several answers, it is acceptable to print any of them.

## 样例

### 样例 1

**输入**:
```
4
1 2 1 0
```

**输出**:
```
3
1 2
2 4
2 3
```

### 样例 2

**输入**:
```
6
2 0 1 3 2 0
```

**输出**:
```
6
1 3
3 4
1 2
4 5
5 6
4 6
```

### 样例 3

**输入**:
```
3
0 2 2
```

**输出**:
```
-1
```

## 备注

In the first test Polycarp (the student number 1) can send the message to the student number 2, who after that can send the message to students number 3 and 4. Thus, all students knew about the credit.

## 题解

For solving this task you need to consider the following. Let it be that not all students knew news for that moment.
If there are not students which knew this news and still can send messages, the answer for the task is -1.
Otherwise, it is necessary to send the message to the student x, which still doesn't know news, который еще не знает новость, wherein the number of messages which then can be sent by the student x was maximum among students which still don't know the news. It does not matter which one of the students will send the message to the student number x.
It is necessary to repeat the descriped process until not all students knew the news.
For example, for realization it was possible firstly to sort out all students in descending number of messages which each of them can send. Then you can use the queue and put other students who learn the news to the end of it. At the same time, students should learn the news according to the sorted order. It is necessary to send messages starting from Polycarp (student number 1), because initially only he knews the news.

