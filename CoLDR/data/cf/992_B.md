# Nastya Studies Informatics

**题目ID**: 992/B  
**比赛**: Codeforces Round 489 (Div. 2)  
**年份**: 2018  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

Today on Informatics class <PERSON><PERSON><PERSON> learned about GCD and LCM (see links below). <PERSON><PERSON><PERSON> is very intelligent, so she solved all the tasks momentarily and now suggests you to solve one of them as well.

We define a pair of integers (a, b) good, if GCD(a, b) = x and LCM(a, b) = y, where GCD(a, b) denotes the greatest common divisor of a and b, and LCM(a, b) denotes the least common multiple of a and b.

You are given two integers x and y. You are to find the number of good pairs of integers (a, b) such that l ≤ a, b ≤ r. Note that pairs (a, b) and (b, a) are considered different if a ≠ b.

## 输入格式

The only line contains four integers l, r, x, y (1 ≤ l ≤ r ≤ 109, 1 ≤ x ≤ y ≤ 109).

## 输出格式

In the only line print the only integer — the answer for the problem.

## 样例

### 样例 1

**输入**:
```
1 2 1 2
```

**输出**:
```
2
```

### 样例 2

**输入**:
```
1 12 1 12
```

**输出**:
```
4
```

### 样例 3

**输入**:
```
50 100 3 30
```

**输出**:
```
0
```

## 备注

In the first example there are two suitable good pairs of integers (a, b): (1, 2) and (2, 1).

In the second example there are four suitable good pairs of integers (a, b): (1, 12), (12, 1), (3, 4) and (4, 3).

In the third example there are good pairs of integers, for example, (3, 30), but none of them fits the condition l ≤ a, b ≤ r.

## 题解

Let's consider some suitable pair $$(a,b)$$. As $$\operatorname{GCD}(a,b)=x$$, we can present number $$\alpha$$ as $$\mathcal{C} \cdot \tau^*$$, and number $$\beta$$ as $$d \cdot x$$, where we know that $$c,d\geq1$$ and $$GCD(c,d)=1$$.
Let's consider too that from the restriction from the problem $$1 \leq a, b \leq n$$ we surely know the restriction for $$\mathcal{C}$$ and $$d$$, that is $$\frac{l}{x} \leq c, d \leq \frac{r}{x}$$.
Let's remember we know that $$x \cdot y = a \cdot b$$ (because $$-1$$ is $$GCD(a,b)$$, $$y$$ is $${\mathrm {LCM}}(a,b)$$). Then we can get $$x \cdot y = c \cdot x \cdot d \cdot x$$. Dividing by $$-1$$:
$$y = c \cdot d \cdot x$$.
$${ \frac { y } { x } } = c \cdot d$$.
Now if $$y \% x \neq 0$$, answer equals 0.
Else as $$\frac{y}{r}$$ is surely less than $${ 1 0 } ^ { 9 }$$, we can just sort out all possible pairs $$(c,d)$$ of divisors $$\frac{y}{r}$$, such that $$c \cdot d = \frac{y}{x}$$ , and then to check that $$GCD(c,d)=1$$ and $$c,d$$ are in the getting above restrictions. Complexity of this solution is $${\mathcal{O}}({\sqrt {y}})$$.

