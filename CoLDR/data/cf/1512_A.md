# Spy Detected!

**题目ID**: 1512/A  
**比赛**: Codeforces Round 713 (Div. 3)  
**年份**: 2021  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

You are given an array $$$a$$$ consisting of $$$n$$$ ($$$n \ge 3$$$) positive integers. It is known that in this array, all the numbers except one are the same (for example, in the array $$$[4, 11, 4, 4]$$$ all numbers except one are equal to $$$4$$$).

Print the index of the element that does not equal others. The numbers in the array are numbered from one.

## 输入格式

The first line contains a single integer $$$t$$$ ($$$1 \le t \le 100$$$). Then $$$t$$$ test cases follow.

The first line of each test case contains a single integer $$$n$$$ ($$$3 \le n \le 100$$$) — the length of the array $$$a$$$.

The second line of each test case contains $$$n$$$ integers $$$a_1, a_2, \ldots, a_n$$$ ($$$1 \le a_i \le 100$$$).

It is guaranteed that all the numbers except one in the $$$a$$$ array are the same.

## 输出格式

For each test case, output a single integer — the index of the element that is not equal to others.

## 样例

### 样例 1

**输入**:
```
4
4
11 13 11 11
5
1 4 4 4 4
10
3 3 3 3 10 3 3 3 3 3
3
20 20 10
```

**输出**:
```
2
1
5
3
```

## 题解

To find a number that differs from the rest of the numbers in the array, you need to iterate through the array, maintaining two pairs of numbers $$$(x_1, c_1)$$$ and $$$(x_2, c_2)$$$, where $$$x_i$$$ is a number from the array, $$$c_i$$$ is how many times the number $$$x_i$$$ occurs in the array.
Then, to get an answer, you need to find the position of the $$$x_i$$$ that occurs in the array exactly once (i.e. $$$c_i = 1$$$).

