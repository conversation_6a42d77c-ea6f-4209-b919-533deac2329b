# Set Theory

**题目ID**: 856/A  
**比赛**: Russian Code Cup 2017 - Finals [Unofficial Mirror, Div. 1 Only Recommended, Teams Allowed]  
**年份**: 2017  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON><PERSON> and <PERSON><PERSON><PERSON> like studying sets of positive integers.

One day <PERSON><PERSON><PERSON> has written a set A containing n different integers ai on a blackboard. Now he asks <PERSON><PERSON> to create a set B containing n different integers bj such that all n2 integers that can be obtained by summing up ai and bj for all possible pairs of i and j are different.

Both <PERSON><PERSON> and <PERSON><PERSON><PERSON> don't like big numbers, so all numbers in A are from 1 to 106, and all numbers in B must also be in the same range.

Help <PERSON><PERSON> to create the set B that satisfies <PERSON><PERSON><PERSON>'s requirement.

## 输入格式

Input data contains multiple test cases. The first line contains an integer t — the number of test cases (1 ≤ t ≤ 100).

Each test case is described in the following way: the first line of the description contains one integer n — the number of elements in A (1 ≤ n ≤ 100).

The second line contains n integers ai — the elements of A (1 ≤ ai ≤ 106).

## 输出格式

For each test first print the answer:

- NO, if <PERSON><PERSON>'s task is impossible to solve, there is no way to create the required set B.
- YES, if there is the way to create the required set. In this case the second line must contain n different positive integers bj — elements of B (1 ≤ bj ≤ 106). If there are several possible sets, output any of them.

## 样例

### 样例 1

**输入**:
```
3
3
1 10 100
1
1
2
2 4
```

**输出**:
```
YES
1 2 3
YES
1
YES
1 2
```

## 题解

Небольшие спойлеры: ответ всегда YES, почему — будет понятно из последующего решения.
Рассмотрим наивное решение данной задачи. Будем последовательно перебирать bj и проверять, что при суммировании всех ai со всеми выбранными bj, все суммы различны. Несложно заметить, что данное решение будет работать за O(max(B)·n2).
Оценим максимальный элемент в множестве B. Для начала, он не может превышать 106 по условию задачи. Затем, рассмотрим следующее равенство: ai1 + bj1 = ai2 + bj2, которое отражает ситуацию несовместимости двух элементов выбранных для B. Немного перепишем: bj2 = bj1 - (ai2 - ai1). Таким образом, разность любых двух элементов множества B не может быть равна разности каких-то двух элементов из множества A. В множестве A может быть O(n2) различных разностей, из чего следует, что max(B) не меньше, чем O(n2) в худшем случае.
Из выше описанного следует, что наивное решение данной задачи работает за O(n4) на один тест, что не укладывается в ограничение по времени. Как можно было оптимизировать данное решение? Заметим, что мы каждый раз считаем суммы элементов двух множеств, и избавимся от этого. Заведем массив bad, в котором будем отмечать числа, которые в дальнейшем не подойдут для множества B. Теперь при переборе bj, если текущее число не отмечено в bad, то его можно взять в ответ. Какие числа в таком случае нельзя будет в дальнейшем брать в ответ? Те, которые отличаются от bj, на разность каких-то двух чисел из множества A. (Так как bj перебираем по возрастанию, будем рассматривать только положительные разности, которых, очевидно, не более n2 / 2)
Таким образом, при переборе очередного bj мы будем что-то пересчитывать только в том случае, если мы это число берем в ответ. Таким образом, асимптотика данного решения будет O(max(B) + n3) на один тест. Также заметим, что max(B) в таком решении никогда не превзойдет n3 / 2, а значит, общая асимптотика времени работы решения —O(n3) на тест.
Послесловие: К решению за O(n4) существует множество эвристик, не каждую из которых получилось отсечь тестами таким образом, чтобы не заставлять участников писать аккуратный оптимизированный код с правильным решением. Таким образом, задачу можно решить либо придумав небольшую оптимизацию, либо запихиванием наивного решения.

