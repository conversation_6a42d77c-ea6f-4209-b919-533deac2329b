# Stairs and Elevators

**题目ID**: 925/A  
**比赛**: VK Cup 2018 - Round 3  
**年份**: 2018  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

In the year of $$$30XX$$$ participants of some world programming championship live in a single large hotel. The hotel has $$$n$$$ floors. Each floor has $$$m$$$ sections with a single corridor connecting all of them. The sections are enumerated from $$$1$$$ to $$$m$$$ along the corridor, and all sections with equal numbers on different floors are located exactly one above the other. Thus, the hotel can be represented as a rectangle of height $$$n$$$ and width $$$m$$$. We can denote sections with pairs of integers $$$(i, j)$$$, where $$$i$$$ is the floor, and $$$j$$$ is the section number on the floor.

The guests can walk along the corridor on each floor, use stairs and elevators. Each stairs or elevator occupies all sections $$$(1, x)$$$, $$$(2, x)$$$, $$$\ldots$$$, $$$(n, x)$$$ for some $$$x$$$ between $$$1$$$ and $$$m$$$. All sections not occupied with stairs or elevators contain guest rooms. It takes one time unit to move between neighboring sections on the same floor or to move one floor up or down using stairs. It takes one time unit to move up to $$$v$$$ floors in any direction using an elevator. You can assume you don't have to wait for an elevator, and the time needed to enter or exit an elevator is negligible.

You are to process $$$q$$$ queries. Each query is a question "what is the minimum time needed to go from a room in section $$$(x_1, y_1)$$$ to a room in section $$$(x_2, y_2)$$$?"

## 输入格式

The first line contains five integers $$$n, m, c_l, c_e, v$$$ ($$$2 \leq n, m \leq 10^8$$$, $$$0 \leq c_l, c_e \leq 10^5$$$, $$$1 \leq c_l + c_e \leq m - 1$$$, $$$1 \leq v \leq n - 1$$$) — the number of floors and section on each floor, the number of stairs, the number of elevators and the maximum speed of an elevator, respectively.

The second line contains $$$c_l$$$ integers $$$l_1, \ldots, l_{c_l}$$$ in increasing order ($$$1 \leq l_i \leq m$$$), denoting the positions of the stairs. If $$$c_l = 0$$$, the second line is empty.

The third line contains $$$c_e$$$ integers $$$e_1, \ldots, e_{c_e}$$$ in increasing order, denoting the elevators positions in the same format. It is guaranteed that all integers $$$l_i$$$ and $$$e_i$$$ are distinct.

The fourth line contains a single integer $$$q$$$ ($$$1 \leq q \leq 10^5$$$) — the number of queries.

The next $$$q$$$ lines describe queries. Each of these lines contains four integers $$$x_1, y_1, x_2, y_2$$$ ($$$1 \leq x_1, x_2 \leq n$$$, $$$1 \leq y_1, y_2 \leq m$$$) — the coordinates of starting and finishing sections for the query. It is guaranteed that the starting and finishing sections are distinct. It is also guaranteed that these sections contain guest rooms, i. e. $$$y_1$$$ and $$$y_2$$$ are not among $$$l_i$$$ and $$$e_i$$$.

## 输出格式

Print $$$q$$$ integers, one per line — the answers for the queries.

## 样例

### 样例 1

**输入**:
```
5 6 1 1 3
2
5
3
1 1 5 6
1 3 5 4
3 3 5 3
```

**输出**:
```
7
5
4
```

## 备注

In the first query the optimal way is to go to the elevator in the 5-th section in four time units, use it to go to the fifth floor in two time units and go to the destination in one more time unit.

In the second query it is still optimal to use the elevator, but in the third query it is better to use the stairs in the section 2.

## 题解

First thing to mention is that we can use no more than one stairs or elevator per query. Indeed, optimal path is always a few sections horizontally, then a stair of elevator, then a few sections horizontally.
Then, we can note that we can always use one of the nearest stairs/elevators to start/finish. Using this fact, we can binary search in the sequence of stairs/elevators to find the optimal one, and choose the optimum between using a stairs and an elevator. Don't forget about the case where you don't have to reach any stairs/elevators.
The complexity is $$$O(q \log{n})$$$.

