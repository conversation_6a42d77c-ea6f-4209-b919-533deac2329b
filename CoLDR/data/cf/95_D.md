# Horse Races

**题目ID**: 95/D  
**比赛**: Codeforces Beta Round 77 (Div. 1 Only)  
**年份**: 2011  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON><PERSON> likes horse racing very much. Horses numbered from l to r take part in the races. <PERSON><PERSON> wants to evaluate the probability of victory; for some reason, to do that he needs to know the amount of nearly lucky horses' numbers. A nearly lucky number is an integer number that has at least two lucky digits the distance between which does not exceed k. <PERSON><PERSON> learned from some of his mates from Lviv that lucky digits are digits 4 and 7. The distance between the digits is the absolute difference between their positions in the number of a horse. For example, if k = 2, then numbers 412395497, 404, 4070400000070004007 are nearly lucky and numbers 4, 4123954997, 4007000040070004007 are not.

<PERSON><PERSON> prepared t intervals [li, ri] and invented number k, common for all of them. Your task is to find how many nearly happy numbers there are in each of these segments. Since the answers can be quite large, output them modulo 1000000007 (109 + 7).

## 输入格式

The first line contains two integers t and k (1 ≤ t, k ≤ 1000) — the number of segments and the distance between the numbers correspondingly. Next t lines contain pairs of integers li and ri (1 ≤ l ≤ r ≤ 101000). All numbers are given without the leading zeroes. Numbers in each line are separated by exactly one space character.

## 输出格式

Output t lines. In each line print one integer — the answer for the corresponding segment modulo 1000000007 (109 + 7).

## 样例

### 样例 1

**输入**:
```
1 2
1 100
```

**输出**:
```
4
```

### 样例 2

**输入**:
```
1 2
70 77
```

**输出**:
```
2
```

### 样例 3

**输入**:
```
2 1
1 20
80 100
```

**输出**:
```
0
0
```

## 备注

In the first sample, the four nearly lucky numbers are 44, 47, 74, 77.

In the second sample, only 74 and 77 are in the given segment.

