# Sleepy Game

**题目ID**: 937/D  
**比赛**: Codeforces Round 467 (Div. 2)  
**年份**: 2018  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON><PERSON> and <PERSON><PERSON><PERSON> arranged a game. The game runs by the following rules. Players have a directed graph consisting of n vertices and m edges. One of the vertices contains a chip. Initially the chip is located at vertex s. Players take turns moving the chip along some edge of the graph. <PERSON><PERSON> goes first. Player who can't move the chip loses. If the game lasts for 106 turns the draw is announced.

<PERSON><PERSON><PERSON> was performing big laboratory work in "Spelling and parts of speech" at night before the game, so he fell asleep at the very beginning of the game. <PERSON><PERSON> decided to take the advantage of this situation and make both <PERSON><PERSON>'s and <PERSON><PERSON><PERSON>'s moves.

Your task is to help <PERSON><PERSON> find out if he can win the game or at least draw a tie.

## 输入格式

The first line of input contain two integers n and m — the number of vertices and the number of edges in the graph (2 ≤ n ≤ 105, 0 ≤ m ≤ 2·105).

The next n lines contain the information about edges of the graph. i-th line (1 ≤ i ≤ n) contains nonnegative integer ci — number of vertices such that there is an edge from i to these vertices and ci distinct integers ai, j — indices of these vertices (1 ≤ ai, j ≤ n, ai, j ≠ i).

It is guaranteed that the total sum of ci equals to m.

The next line contains index of vertex s — the initial position of the chip (1 ≤ s ≤ n).

## 输出格式

If <PERSON>ya can win print «Win» in the first line. In the next line print numbers v1, v2, ..., vk (1 ≤ k ≤ 106) — the sequence of vertices Petya should visit for the winning. Vertex v1 should coincide with s. For i = 1... k - 1 there should be an edge from vi to vi + 1 in the graph. There must be no possible move from vertex vk. The sequence should be such that Petya wins the game.

If Petya can't win but can draw a tie, print «Draw» in the only line. Otherwise print «Lose».

## 样例

### 样例 1

**输入**:
```
5 6
2 2 3
2 4 5
1 4
1 5
0
1
```

**输出**:
```
Win
1 2 4 5
```

### 样例 2

**输入**:
```
3 2
1 3
1 1
0
2
```

**输出**:
```
Lose
```

### 样例 3

**输入**:
```
2 2
1 2
1 1
1
```

**输出**:
```
Draw
```

## 备注

In the first example the graph is the following:

Initially the chip is located at vertex 1. In the first move Petya moves the chip to vertex 2, after that he moves it to vertex 4 for Vasya. After that he moves to vertex 5. Now it is Vasya's turn and there is no possible move, so Petya wins.

In the second example the graph is the following:

Initially the chip is located at vertex 2. The only possible Petya's move is to go to vertex 1. After that he has to go to 3 for Vasya. Now it's Petya's turn but he has no possible move, so Petya loses.

In the third example the graph is the following:

Petya can't win, but he can move along the cycle, so the players will draw a tie.

## 题解

Note that the answer is sequence of adjacent vertices of even length such that the last vertex of this sequence has no outgoing edges.
Build state graph as follows:
State is pair (v, parity), where v is vertex of initial graph and parity is parity of count of vertices on path from s to v. For every edge uv of initial graph add edges $$( u, 0 ) \rightarrow ( v, 1 )$$ and $$( u, 1 ) \rightarrow ( v, 0 )$$ in state graph. So there exists path from (s, 1) to (v, parity) if and only if there exists path from s to v in initial graph of parity parity.
Lets find all reachable from (s, 1) states using BFS or DFS. If there is state (v, 0) among them such that v has no outgoing edges in initial graph, then Petya can win. He can move along vertices in path from (s, 1) to (v, 0) in state graph.
Otherwise we need to check if Petya can make 106 moves for drawing a tie. If there is a tie then the chip visited some vertex twice, because n < 106. Therefore it is sufficient to check if there is a cycle in initial graph reachable from s. In this case Petya can play as follows: move to any vertex of cycle and then move along the cycle as long as it requires to draw a tie.

