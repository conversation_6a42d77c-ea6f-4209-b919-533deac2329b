# Reachability from the Capital

**题目ID**: 999/E  
**比赛**: Codeforces Round 490 (Div. 3)  
**年份**: 2018  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

There are $$$n$$$ cities and $$$m$$$ roads in Berland. Each road connects a pair of cities. The roads in Berland are one-way.

What is the minimum number of new roads that need to be built to make all the cities reachable from the capital?

New roads will also be one-way.

## 输入格式

The first line of input consists of three integers $$$n$$$, $$$m$$$ and $$$s$$$ ($$$1 \le n \le 5000, 0 \le m \le 5000, 1 \le s \le n$$$) — the number of cities, the number of roads and the index of the capital. Cities are indexed from $$$1$$$ to $$$n$$$.

The following $$$m$$$ lines contain roads: road $$$i$$$ is given as a pair of cities $$$u_i$$$, $$$v_i$$$ ($$$1 \le u_i, v_i \le n$$$, $$$u_i \ne v_i$$$). For each pair of cities $$$(u, v)$$$, there can be at most one road from $$$u$$$ to $$$v$$$. Roads in opposite directions between a pair of cities are allowed (i.e. from $$$u$$$ to $$$v$$$ and from $$$v$$$ to $$$u$$$).

## 输出格式

Print one integer — the minimum number of extra roads needed to make all the cities reachable from city $$$s$$$. If all the cities are already reachable from $$$s$$$, print 0.

## 样例

### 样例 1

**输入**:
```
9 9 1
1 2
1 3
2 3
1 5
5 6
6 1
1 8
9 8
7 1
```

**输出**:
```
3
```

### 样例 2

**输入**:
```
5 4 5
1 2
2 3
3 4
4 1
```

**输出**:
```
1
```

## 备注

The first example is illustrated by the following:

For example, you can add roads ($$$6, 4$$$), ($$$7, 9$$$), ($$$1, 7$$$) to make all the cities reachable from $$$s = 1$$$.

The second example is illustrated by the following:

In this example, you can add any one of the roads ($$$5, 1$$$), ($$$5, 2$$$), ($$$5, 3$$$), ($$$5, 4$$$) to make all the cities reachable from $$$s = 5$$$.

## 题解

This problem is (almost) equivalent to the following: count the number of sources (the vertices with indegree equal to $$$0$$$) in the given graph's condensation. Thus, there exist solutions with complexity $$$O(n + m)$$$. However, the constraints in the problem are small, so solutions with complexity $$$O(n \cdot m)$$$ also pass.
One of these solutions is the following: first, let's mark all the vertices reachable from $$$s$$$ as good, using a simple DFS. Then, for each bad vertex $$$v$$$, count the number of bad vertices reachable from $$$v$$$ (it also can be done by simple DFS). Let this number be $$$\mathit{cnt}_v$$$. Now, iterate over all bad vertices in non-increasing order of $$$\mathit{cnt}_v$$$. For the current bad vertex $$$v$$$, if it is still not marked as good, run a DFS from it, marking all the reachable vertices as good, and increase the answer by $$$1$$$ (in fact, we are implicitly adding the edge $$$(s, v)$$$). It can be proved that this solution gives an optimal answer.

