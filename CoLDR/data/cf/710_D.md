# Two Arithmetic Progressions

**题目ID**: 710/D  
**比赛**: Educational Codeforces Round 16  
**年份**: 2016  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

You are given two arithmetic progressions: a1k + b1 and a2l + b2. Find the number of integers x such that L ≤ x ≤ R and x = a1k' + b1 = a2l' + b2, for some integers k', l' ≥ 0.

## 输入格式

The only line contains six integers a1, b1, a2, b2, L, R (0 < a1, a2 ≤ 2·109,  - 2·109 ≤ b1, b2, L, R ≤ 2·109, L ≤ R).

## 输出格式

Print the desired number of integers x.

## 样例

### 样例 1

**输入**:
```
2 0 3 3 5 21
```

**输出**:
```
3
```

### 样例 2

**输入**:
```
2 4 3 0 6 17
```

**输出**:
```
2
```

