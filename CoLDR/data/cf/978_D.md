# Almost Arithmetic Progression

**题目ID**: 978/D  
**比赛**: Codeforces Round 481 (Div. 3)  
**年份**: 2018  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON><PERSON><PERSON><PERSON> likes arithmetic progressions. A sequence $$$[a_1, a_2, \dots, a_n]$$$ is called an arithmetic progression if for each $$$i$$$ ($$$1 \le i < n$$$) the value $$$a_{i+1} - a_i$$$ is the same. For example, the sequences $$$[42]$$$, $$$[5, 5, 5]$$$, $$$[2, 11, 20, 29]$$$ and $$$[3, 2, 1, 0]$$$ are arithmetic progressions, but $$$[1, 0, 1]$$$, $$$[1, 3, 9]$$$ and $$$[2, 3, 1]$$$ are not.

It follows from the definition that any sequence of length one or two is an arithmetic progression.

<PERSON><PERSON><PERSON><PERSON> found some sequence of positive integers $$$[b_1, b_2, \dots, b_n]$$$. He agrees to change each element by at most one. In the other words, for each element there are exactly three options: an element can be decreased by $$$1$$$, an element can be increased by $$$1$$$, an element can be left unchanged.

Determine a minimum possible number of elements in $$$b$$$ which can be changed (by exactly one), so that the sequence $$$b$$$ becomes an arithmetic progression, or report that it is impossible.

It is possible that the resulting sequence contains element equals $$$0$$$.

## 输入格式

The first line contains a single integer $$$n$$$ $$$(1 \le n \le 100\,000)$$$ — the number of elements in $$$b$$$.

The second line contains a sequence $$$b_1, b_2, \dots, b_n$$$ $$$(1 \le b_i \le 10^{9})$$$.

## 输出格式

If it is impossible to make an arithmetic progression with described operations, print -1. In the other case, print non-negative integer — the minimum number of elements to change to make the given sequence becomes an arithmetic progression. The only allowed operation is to add/to subtract one from an element (can't use operation twice to the same position).

## 样例

### 样例 1

**输入**:
```
4
24 21 14 10
```

**输出**:
```
3
```

### 样例 2

**输入**:
```
2
500 500
```

**输出**:
```
0
```

### 样例 3

**输入**:
```
3
14 5 1
```

**输出**:
```
-1
```

### 样例 4

**输入**:
```
5
1 3 6 9 12
```

**输出**:
```
1
```

## 备注

In the first example Polycarp should increase the first number on $$$1$$$, decrease the second number on $$$1$$$, increase the third number on $$$1$$$, and the fourth number should left unchanged. So, after Polycarp changed three elements by one, his sequence became equals to $$$[25, 20, 15, 10]$$$, which is an arithmetic progression.

In the second example Polycarp should not change anything, because his sequence is an arithmetic progression.

In the third example it is impossible to make an arithmetic progression.

In the fourth example Polycarp should change only the first element, he should decrease it on one. After that his sequence will looks like $$$[0, 3, 6, 9, 12]$$$, which is an arithmetic progression.

## 题解

If $$$n \le 2$$$ we can print $$$0$$$, because each such sequence is an arithmetic progression.
Note, that an arithmetic progression is uniquely determined by the first two terms. So we should brute $$$d_1$$$ from $$$-1$$$ to $$$1$$$ — the change of the first element of the given sequence, and $$$d_2$$$ from $$$-1$$$ to $$$1$$$ — the change of the second element of the given sequence. Then $$$a_1 = b_1 + d_1$$$ and $$$a_2 = b_2 + d_2$$$. Also we will store $$$cnt$$$ — the number of changed elements in the sequence.
Initially $$$cnt = |d_1| + |d_2|$$$. Now we need to iterate through the sequence from the third element to $$$n$$$-th. Let current element in the position $$$i$$$. It should be equals to $$$a_i = a_1 + (i - 1) \cdot (a_2 - a_1)$$$. If $$$|a_i - b_i| > 1$$$, then such arithmetic progression is unreachable. Else, if $$$a_i \ne b_i$$$ we should increase $$$cnt$$$ on one.
After we considered all elements we should update the answer with the value of $$$cnt$$$, if for all $$$i$$$ it was true that $$$|a_i - b_i| \le 1$$$.

