# Shockers

**题目ID**: 904/C  
**比赛**: Технокубок 2018 - Отборочный Раунд 4  
**年份**: 2017  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON><PERSON> participates in a show called "Shockers". The rules are quite easy: jury selects one letter which <PERSON><PERSON> doesn't know. He should make a small speech, but every time he pronounces a word that contains the selected letter, he receives an electric shock. He can make guesses which letter is selected, but for each incorrect guess he receives an electric shock too. The show ends when <PERSON><PERSON> guesses the selected letter correctly.

<PERSON><PERSON> can't keep in mind everything, so he could guess the selected letter much later than it can be uniquely determined and get excessive electric shocks. Excessive electric shocks are those which <PERSON><PERSON> got after the moment the selected letter can be uniquely determined. You should find out the number of excessive electric shocks.

## 输入格式

The first line contains a single integer n (1 ≤ n ≤ 105) — the number of actions <PERSON><PERSON> did.

The next n lines contain descriptions of his actions, each line contains description of one action. Each action can be of one of three types:

1. <PERSON><PERSON> pronounced some word and didn't get an electric shock. This action is described by the string ". w" (without quotes), in which "." is a dot (ASCII-code 46), and w is the word that <PERSON><PERSON> said.
2. <PERSON><PERSON> pronounced some word and got an electric shock. This action is described by the string "! w" (without quotes), in which "!" is an exclamation mark (ASCII-code 33), and w is the word that Valentin said.
3. Valentin made a guess about the selected letter. This action is described by the string "? s" (without quotes), in which "?" is a question mark (ASCII-code 63), and s is the guess — a lowercase English letter.

All words consist only of lowercase English letters. The total length of all words does not exceed 105.

It is guaranteed that last action is a guess about the selected letter. Also, it is guaranteed that Valentin didn't make correct guesses about the selected letter before the last action. Moreover, it's guaranteed that if Valentin got an electric shock after pronouncing some word, then it contains the selected letter; and also if Valentin didn't get an electric shock after pronouncing some word, then it does not contain the selected letter.

## 输出格式

Output a single integer — the number of electric shocks that Valentin could have avoided if he had told the selected letter just after it became uniquely determined.

## 样例

### 样例 1

**输入**:
```
5
! abc
. ad
. b
! cd
? c
```

**输出**:
```
1
```

### 样例 2

**输入**:
```
8
! hello
! codeforces
? c
. o
? d
? h
. l
? e
```

**输出**:
```
2
```

### 样例 3

**输入**:
```
7
! ababahalamaha
? a
? b
? a
? b
? a
? h
```

**输出**:
```
0
```

## 备注

In the first test case after the first action it becomes clear that the selected letter is one of the following: a, b, c. After the second action we can note that the selected letter is not a. Valentin tells word "b" and doesn't get a shock. After that it is clear that the selected letter is c, but Valentin pronounces the word cd and gets an excessive electric shock.

In the second test case after the first two electric shocks we understand that the selected letter is e or o. Valentin tries some words consisting of these letters and after the second word it's clear that the selected letter is e, but Valentin makes 3 more actions before he makes a correct hypothesis.

In the third example the selected letter can be uniquely determined only when Valentin guesses it, so he didn't get excessive electric shocks.

## 题解

From last action, selected letter can be found; let it be c (without loss of generality). For each of other 25 letters, answers on some actions are contradicting with assumption that this letter was selected; moreover, for each letter d not equal to c, we can find the earlest such action with number Ad (for each action, we can easily check if assumption "d is selected" is contradicting with the action or not on linear time). Then, the answer is a number of electric shocks after action with number which is maximal among all such Ad-s.

