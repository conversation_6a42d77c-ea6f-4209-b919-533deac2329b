# Recover Polygon (hard)

**题目ID**: 690/B3  
**比赛**: Helvetic Coding Contest 2016 online mirror (teams, unrated)  
**年份**: 2016  
**时间限制**: 5.0秒  
**内存限制**: 256MB  

## 题目描述

Zombies have found out about the Zombie Contamination level checker and managed to damage it! Now detecting the shape of their main compound will be a real challenge for <PERSON>. As before, a lair can be represented as a strictly convex polygon on a lattice. Each vertex of the polygon occupies a point on the lattice. However, the damaged Zombie Contamination level checker can only tell, for each cell, whether the level of Zombie Contamination for that cell is in the set {1, 2, 3}. In other words, <PERSON> knows all the cells of the lattice for which the Contamination level is not 0 and not 4.

Given this information, <PERSON> still wants to know the exact shape of the lair to rain destruction on the zombies. Help her!

## 输入格式

The input contains multiple test cases.

The first line of each test case contains two space-separated integers N and M, where N is the size of the lattice grid (5 ≤ N ≤ 100000) and M is the number of lattice points for which the Zombie Contamination level is 1, 2, or 3 (8 ≤ M ≤ 200000).

The second line of each test case contains M pairs of integers x1, y1, ..., xM, yM – coordinates of the cells with Zombie Contamination level not equal to 0 nor 4. It is guaranteed that 1 ≤ xi, yi ≤ N. All pairs xi, yi are different.

Cells are enumerated based on the coordinates of their upper right corner. This means that the bottommost leftmost cell that touches the origin has coordinates (1, 1), and the uppermost leftmost cell is identified as (1, N).

The last line of the file contains two zeroes. This line should not be treated as a test case. The sum of the M values for all tests in one file will not exceed 200000.

## 输出格式

For each test case, the following output is expected:

The first line of the output should contain one integer V, the number of vertices of the polygon that is the secret lair. The next V lines each should contain two integers, denoting the vertices of the polygon in the clockwise order, starting from the lexicographically smallest vertex.

## 样例

### 样例 1

**输入**:
```
8 19
2 3 2 4 2 5 3 3 3 5 4 3 4 5 4 6 5 2 5 3 5 6 6 2 6 3 6 4 6 5 6 6 6 7 7 6 7 7
5 8
2 2 2 3 2 4 3 2 3 4 4 2 4 3 4 4
0 0
```

**输出**:
```
4
2 3
2 4
6 6
5 2
4
2 2
2 3
3 3
3 2
```

## 备注

It is guaranteed that the solution always exists and is unique. It is guaranteed that in the correct solution the coordinates of the polygon vertices are between 1 and N - 1. A vertex (x1, y1) is lexicographically smaller than vertex (x2, y2) if x1 < x2 or $$x_{1}=x_{2}\wedge y_{1}<y_{2}$$.

