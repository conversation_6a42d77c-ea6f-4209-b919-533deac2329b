# Nikita and string

**题目ID**: 877/B  
**比赛**: Codeforces Round 442 (Div. 2)  
**年份**: 2017  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

One day <PERSON><PERSON> found the string containing letters "a" and "b" only.

<PERSON><PERSON> thinks that string is beautiful if it can be cut into 3 strings (possibly empty) without changing the order of the letters, where the 1-st and the 3-rd one contain only letters "a" and the 2-nd contains only letters "b".

<PERSON><PERSON> wants to make the string beautiful by removing some (possibly none) of its characters, but without changing their order. What is the maximum length of the string he can get?

## 输入格式

The first line contains a non-empty string of length not greater than 5 000 containing only lowercase English letters "a" and "b".

## 输出格式

Print a single integer — the maximum possible size of beautiful string <PERSON><PERSON> can get.

## 样例

### 样例 1

**输入**:
```
abba
```

**输出**:
```
4
```

### 样例 2

**输入**:
```
bab
```

**输出**:
```
2
```

## 备注

It the first sample the string is already beautiful.

In the second sample he needs to delete one of "b" to make it beautiful.

## 题解

Let prefa[i] be the count of letter "a" in prefix of length i and prefb[i] be the count of letter "b" in prefix of length i.
Let's fix two positions i and j, 1 ≤ i ≤ j ≤ n, so we remove all "b" from prefix, which ends in i, and suffix, which starts in j, and all "a" between positions i and j. Then length of string is (prefa[n] - prefa[j]) + (prefb[j] - prefb[i]) + (prefa[i]).
Using two for loops we find optimal i and j and calculate answer.

