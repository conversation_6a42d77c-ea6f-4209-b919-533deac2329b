# He<PERSON><PERSON>'s Cup

**题目ID**: 855/C  
**比赛**: <PERSON><PERSON>, Codefest 17  
**年份**: 2017  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON>, <PERSON> and <PERSON><PERSON><PERSON> have figured out that <PERSON><PERSON><PERSON>'s cup is a horcrux. Through her encounter with <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> came to know that the cup is present in <PERSON><PERSON><PERSON>'s family vault in Gringott's Wizarding Bank.

The Wizarding bank is in the form of a tree with total n vaults where each vault has some type, denoted by a number between 1 to m. A tree is an undirected connected graph with no cycles.

The vaults with the highest security are of type k, and all vaults of type k have the highest security.

There can be at most x vaults of highest security.

Also, if a vault is of the highest security, its adjacent vaults are guaranteed to not be of the highest security and their type is guaranteed to be less than k.

<PERSON> wants to consider every possibility so that he can easily find the best path to reach <PERSON><PERSON><PERSON>'s vault. So, you have to tell him, given the tree structure of Gringotts, the number of possible ways of giving each vault a type such that the above conditions hold.

## 输入格式

The first line of input contains two space separated integers, n and m — the number of vaults and the number of different vault types possible. (1 ≤ n ≤ 105, 1 ≤ m ≤ 109).

Each of the next n - 1 lines contain two space separated integers ui and vi (1 ≤ ui, vi ≤ n) representing the i-th edge, which shows there is a path between the two vaults ui and vi. It is guaranteed that the given graph is a tree.

The last line of input contains two integers k and x (1 ≤ k ≤ m, 1 ≤ x ≤ 10), the type of the highest security vault and the maximum possible number of vaults of highest security.

## 输出格式

Output a single integer, the number of ways of giving each vault a type following the conditions modulo 109 + 7.

## 样例

### 样例 1

**输入**:
```
4 2
1 2
2 3
1 4
1 2
```

**输出**:
```
1
```

### 样例 2

**输入**:
```
3 3
1 2
1 3
2 1
```

**输出**:
```
13
```

### 样例 3

**输入**:
```
3 1
1 2
1 3
1 1
```

**输出**:
```
0
```

## 备注

In test case 1, we cannot have any vault of the highest security as its type is 1 implying that its adjacent vaults would have to have a vault type less than 1, which is not allowed. Thus, there is only one possible combination, in which all the vaults have type 2.

## 题解

This problem can be solved by using the concept of dp on trees.
The dp table for this problem will be of the size n·3·x.
We can assume any one node as the root and apply dfs while computing the dp array. Let the root be 1.
Here, dp[curr][0][cnt] represent the number of ways of assigning type to each node in the subtree of curr such that number of nodes with value k in this subtree is cnt and type at curr is less than k.
dp[curr][1][cnt] represent the number of ways of assigning type to each node in the subtree of curr such that number of nodes with value k in this subtree is cnt and type at curr is equal to k.
dp[curr][2][cnt] represent the number of ways of assigning type to each node in the subtree of curr such that number of nodes with value k in this subtree is cnt and type at curr is greater than k.
Now, to compute this dp for a node, curr, we assume this dp array is computed for its children. Then, we can combine them two nodes at a time to form the dp array for the node curr. While assigning the value to dp[curr][1][cnt], we take into account only the values of dp[childofcurr][0][cnt - z]. Similarly for dp[curr][2][cnt], we take into account only dp[child of curr][0][cnt - z] and dp[child of curr][2][cnt - z]. For combining the value we make x * x computations.
Final answer will be $$\sum_{0<i<2} \sum_{0<j<x} dp[1][i][j]$$
The expected time complexity of the solution is: O(n·3·x·x)

