# A <PERSON><PERSON><PERSON> in the Array

**题目ID**: 949/B  
**比赛**: Codeforces Round 469 (Div. 1)  
**年份**: 2018  
**时间限制**: 2.0秒  
**内存限制**: 512MB  

## 题目描述

<PERSON><PERSON> is a beginner programmer. During his working process, he regularly has to repeat the following operation again and again: to remove every second element from the array. One day he has been bored with easy solutions of this problem, and he has come up with the following extravagant algorithm.

Let's consider that initially array contains n numbers from 1 to n and the number i is located in the cell with the index 2i - 1 (Indices are numbered starting from one) and other cells of the array are empty. Each step <PERSON><PERSON> selects a non-empty array cell with the maximum index and moves the number written in it to the nearest empty cell to the left of the selected one. The process continues until all n numbers will appear in the first n cells of the array. For example if n = 4, the array is changing as follows:

You have to write a program that allows you to determine what number will be in the cell with index x (1 ≤ x ≤ n) after <PERSON><PERSON>'s algorithm finishes.

## 输入格式

The first line contains two integers n and q (1 ≤ n ≤ 1018, 1 ≤ q ≤ 200 000), the number of elements in the array and the number of queries for which it is needed to find the answer.

Next q lines contain integers xi (1 ≤ xi ≤ n), the indices of cells for which it is necessary to output their content after <PERSON><PERSON>'s algorithm finishes.

## 输出格式

For each of q queries output one integer number, the value that will appear in the corresponding array cell after <PERSON><PERSON>'s algorithm finishes.

## 样例

### 样例 1

**输入**:
```
4 3
2
3
4
```

**输出**:
```
3
2
4
```

### 样例 2

**输入**:
```
13 4
10
5
4
8
```

**输出**:
```
13
3
8
9
```

## 备注

The first example is shown in the picture.

In the second example the final array is [1, 12, 2, 8, 3, 11, 4, 9, 5, 13, 6, 10, 7].

## 题解

In odd position p value $${ \frac { p + 1 } { 2 } }$$ will be set.
For even position p let's find out position from which value has arrived and iterate over such position until we will arrive to odd position for which we know answer.
At the moment of jumping to cell p there are $$\frac{\pi}{2}$$ elements to the right of the position p. So there are $$n - \frac{p}{2}$$ elements to the right of this position and jump to cell p was done from position $$p + \left( n - \frac{p}{2} \right)$$. During each such jump length of jump decreases at least by 2 times, so there are no more than $${\mathcal {O}}(\log n)$$ jumps and solution works in $${\mathcal{O}}(q\log n)$$.

