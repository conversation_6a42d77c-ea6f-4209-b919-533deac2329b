# Young Photographer

**题目ID**: 14/B  
**比赛**: Codeforces Beta Round 14 (Div. 2)  
**年份**: 2010  
**时间限制**: 2.0秒  
**内存限制**: 64MB  

## 题目描述

Among other things, <PERSON> is keen on photography. Especially he likes to take pictures of sportsmen. That was the reason why he placed himself in position x0 of a long straight racetrack and got ready to take pictures. But the problem was that not all the runners passed him. The total amount of sportsmen, training at that racetrack, equals n. And each of them regularly runs distances within a particular segment of the racetrack, which is the same for each sportsman. For example, the first sportsman runs from position a1 to position b1, the second — from a2 to b2

What is the minimum distance that <PERSON> should move to have a chance to take pictures of each sportsman? Bob can take a picture of a sportsman, if he stands within the segment that this sportsman covers on the racetrack.

## 输入格式

The first line of the input file contains integers n and x0 (1 ≤ n ≤ 100; 0 ≤ x0 ≤ 1000). The following n lines contain pairs of integers ai, bi (0 ≤ ai, bi ≤ 1000; ai ≠ bi).

## 输出格式

Output the required minimum distance in the same units as the positions on the racetrack. If there is no such a position, output -1.

## 样例

### 样例 1

**输入**:
```
3 3
0 7
14 2
4 6
```

**输出**:
```
1
```

