# K-Dominant Character

**题目ID**: 888/C  
**比赛**: Educational Codeforces Round 32  
**年份**: 2017  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

You are given a string s consisting of lowercase Latin letters. Character c is called k-dominant iff each substring of s with length at least k contains this character c.

You have to find minimum k such that there exists at least one k-dominant character.

## 输入格式

The first line contains string s consisting of lowercase Latin letters (1 ≤ |s| ≤ 100000).

## 输出格式

Print one number — the minimum value of k such that there exists at least one k-dominant character.

## 样例

### 样例 1

**输入**:
```
abacaba
```

**输出**:
```
2
```

### 样例 2

**输入**:
```
zzzzz
```

**输出**:
```
1
```

### 样例 3

**输入**:
```
abcde
```

**输出**:
```
3
```

## 题解

At first, notice that the final answer is minimum over answers for each character.
The answer for one character can be obtained like this. Write down lengths of segments between two consecutive occurrences of this character, from the first occurrence to the start of the string and from the last to the end of the string. Take maximum of these values. Answer will be this maximum + 1.
Overall complexity: O(|Alpha|·n).

