# Remove Extra One

**题目ID**: 900/C  
**比赛**: Codeforces Round 450 (Div. 2)  
**年份**: 2017  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

You are given a permutation p of length n. Remove one element from permutation to make the number of records the maximum possible.

We remind that in a sequence of numbers a1, a2, ..., ak the element ai is a record if for every integer j (1 ≤ j < i) the following holds: aj < ai.

## 输入格式

The first line contains the only integer n (1 ≤ n ≤ 105) — the length of the permutation.

The second line contains n integers p1, p2, ..., pn (1 ≤ pi ≤ n) — the permutation. All the integers are distinct.

## 输出格式

Print the only integer — the element that should be removed to make the number of records the maximum possible. If there are multiple such elements, print the smallest one.

## 样例

### 样例 1

**输入**:
```
1
1
```

**输出**:
```
1
```

### 样例 2

**输入**:
```
5
5 1 2 3 4
```

**输出**:
```
5
```

## 备注

In the first example the only element can be removed.

## 题解

In this problem you have to find an element after which removal the number of records is maximum possible.
Let ri be an array consisting of 0 and 1 depending on whether the i-th element was a record initially or not. We can compute it easily in O(N).
Let xi be the difference between the number of records after removal the i-th element and initial number of records.
Let's think of how does removal of ai influence the array ri. First of all, ri becomes 0. rj (j < i) do not change in this case. Some of rj (j > i), change from 0 to 1. These elements are not records initially, but become records after the removal. These elements are elements which have only one greater element in front of them — ai.
Here follows an O(n2) solution. Let's fix ai — the element we are going to remove. Let xi =  - ri  +  the number of such j that j > i, ai > aj, and for all k (k ≠ i, k < j) ak < aj. We can compute this just looping through all j and keeping the maximum over all elements but the i-th.
Now note that it's not required to fix ai. rj can become 1 from 0 only when a certain element from the left is removed. Let's loop through all aj and determine if there is an element to the left such that it is greater than aj, but all other elements are less than aj. We can check this using ordered set. If there is such a ai, then increase xi by 1. After the loop the array xi is fully computed, so we can just find the element which brings the maximum number of records, and minimum among such.

