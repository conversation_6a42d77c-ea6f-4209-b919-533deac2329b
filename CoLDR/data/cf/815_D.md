# Karen and Cards

**题目ID**: 815/D  
**比赛**: Codeforces Round 419 (Div. 1)  
**年份**: 2017  
**时间限制**: 2.0秒  
**内存限制**: 512MB  

## 题目描述

<PERSON> just got home from the supermarket, and is getting ready to go to sleep.

After taking a shower and changing into her pajamas, she looked at her shelf and saw an album. Curious, she opened it and saw a trading card collection.

She recalled that she used to play with those cards as a child, and, although she is now grown-up, she still wonders a few things about it.

Each card has three characteristics: strength, defense and speed. The values of all characteristics of all cards are positive integers. The maximum possible strength any card can have is p, the maximum possible defense is q and the maximum possible speed is r.

There are n cards in her collection. The i-th card has a strength ai, defense bi and speed ci, respectively.

A card beats another card if at least two of its characteristics are strictly greater than the corresponding characteristics of the other card.

She now wonders how many different cards can beat all the cards in her collection. Two cards are considered different if at least one of their characteristics have different values.

## 输入格式

The first line of input contains four integers, n, p, q and r (1 ≤ n, p, q, r ≤ 500000), the number of cards in the collection, the maximum possible strength, the maximum possible defense, and the maximum possible speed, respectively.

The next n lines each contain three integers. In particular, the i-th line contains ai, bi and ci (1 ≤ ai ≤ p, 1 ≤ bi ≤ q, 1 ≤ ci ≤ r), the strength, defense and speed of the i-th collection card, respectively.

## 输出格式

Output a single integer on a line by itself, the number of different cards that can beat all the cards in her collection.

## 样例

### 样例 1

**输入**:
```
3 4 4 5
2 2 5
1 3 4
4 1 1
```

**输出**:
```
10
```

### 样例 2

**输入**:
```
5 10 10 10
1 1 1
1 1 1
1 1 1
1 1 1
1 1 1
```

**输出**:
```
972
```

## 备注

In the first test case, the maximum possible strength is 4, the maximum possible defense is 4 and the maximum possible speed is 5. Karen has three cards:

- The first card has strength 2, defense 2 and speed 5.
- The second card has strength 1, defense 3 and speed 4.
- The third card has strength 4, defense 1 and speed 1.

There are 10 cards that beat all the cards here:

1. The card with strength 3, defense 3 and speed 5.
2. The card with strength 3, defense 4 and speed 2.
3. The card with strength 3, defense 4 and speed 3.
4. The card with strength 3, defense 4 and speed 4.
5. The card with strength 3, defense 4 and speed 5.
6. The card with strength 4, defense 3 and speed 5.
7. The card with strength 4, defense 4 and speed 2.
8. The card with strength 4, defense 4 and speed 3.
9. The card with strength 4, defense 4 and speed 4.
10. The card with strength 4, defense 4 and speed 5.

In the second test case, the maximum possible strength is 10, the maximum possible defense is 10 and the maximum possible speed is 10. Karen has five cards, all with strength 1, defense 1 and speed 1.

Any of the 972 cards which have at least two characteristics greater than 1 can beat all of the cards in her collection.

## 题解

Let's say we have one card, with a1 = 4, b1 = 2 and c1 = 3. For simplicity, we have p = q = r = 5.
Consider which cards will beat this one. Let's fix the c of our card, and see what happens at all various c:
Note that a green cell at (a, b) in grid c represents that the card (a, b, c) can beat the card (4, 2, 3). Hence, the total number of cards that can beat this card is simply the number of green cells across all c grids.
This representation is helpful, because we can easily account for more cards. For example, let's say we have another card a2 = 2, b2 = 3 and c2 = 4:
Now, what happens when we want to consider the cards that beat both of these cards? Well, we simply have to consider the intersection of both sets of grids!
Remember that we are simply trying to count the total number of green cells in all grids.
It turns out that trying to count the number of green cells directly is quite difficult. Instead, it is more feasible to count the number of not green cells, and then simply subtract it from the number of cells pqr.
How could we do this? We should exploit some properties of the grids.
First, for any particular card (ai, bi, ci), all the grids from 1 to ci are the same, and all the grids from ci + 1 to r are the same. This means that we can avoid a lot of redundancy, and only perform some sort of update when we reach the change.
Second, if some cell (a, b) is not green for some fixed c, then neither are the cells (a', b') for all a' ≤ a and b' ≤ b for the same c. This means that we can replace each grid with an array u1, u2, u3, ... where ua is the largest b for which (a, b) is not green. Additionally, u1 ≥ u2 ≥ u3 ≥ ... ≥ up.
Third, for any card, there are only at most two distinct values in u for any fixed c in one card.
Finally, for any card, no value ui in c is less than ui in c' if c < c'.
These properties are all pretty easy to observe and prove, but they will form the bread and butter of our solution.
Let's iterate cards from c = r to c = 1. Suppose we maintain an array s which will at first contain all 0. This will be the number of cells that are not green.
We will update it for all grids first. For each card, we are essentially setting, for all i, si to max(si, ui).
Of course, doing this for each grid will take O(np), which is too slow. To remedy this, initialize s as a segment tree instead. Now, we are basically just setting s1, s2, s3, ..., sai to max(sj, bi) for all cards i.
Because s is essentially a maximum of a bunch of u's, which are all nonincreasing by the second property, it follows that s is also nonincreasing at all times. Therefore, these updates are easy to do; we are essentially setting sk, sk + 1, sk + 2, ..., sai to bi for the smallest k where bi ≥ sk. We can find k using binary search. Binary searching the segment tree can be done in $${\mathcal O}(\log p)$$ time using an implicit binary search by going down the tree; an explicit $${\mathcal O}(\log^{2}p)$$ binary search might have trouble passing the time limit.
Using the aforementioned procedure, we are able to generate s corresponding to the layer c = r in $${\mathcal O}(n\log p)$$ time. Using the segment tree, we should also be able to get the sum of all values in s at all times. This will allow us to count the number of not green cells.
Now, we will go backwards from c = r to c = 1. We should decrement c, and then see which grids changed. (Just sort the cards by ci and do a two-pointers approach.) All the newly-changed grids can then be updated in a similar manner as before. When a grid changes, thanks to the fourth property, there is no worry of any ui getting smaller than it was before; they can only get bigger. So, we have to update two ranges s1, s2, s3, ..., sai to r and sai + 1, sa2 + 1, sai + 3, ..., sp to max(sj, bi). The former is a simple range update, the latter can be done using binary search like before.
After we update all grids for a particular c, get the range sum, and decrement c again, and so on until we reach 1. We will have the found the total number of not green cells in all c, and from there we can recover all the green cells, and hence the final answer.
Sorting the cards by ci takes $$O(n \log n)$$ time, constructing the segment tree s takes O(p) time, there are O(n) updates each taking $${\mathcal O}(\log p)$$ time, and iterating takes O(r) time. The final runtime is therefore $${\mathcal O}\left(n(\log n+\log p)+p+r\right)$$ time, which is sufficient to solve this problem.
This solution can be modified to pass p, q, r ≤ 109, too; however, this was not done as it uses only standard ideas and just contains more tedious implementation. If you want, you can try to implement it.

