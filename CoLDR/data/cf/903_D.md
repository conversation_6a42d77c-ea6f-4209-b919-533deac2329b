# Almost Difference

**题目ID**: 903/D  
**比赛**: Educational Codeforces Round 34 (Rated for Div. 2)  
**年份**: 2017  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

Let's denote a function

$$d(x,y) = \begin{cases} y - x, & \text{if } |x-y| > 1 \\ 0, & \text{if } |x-y| \leq 1 \end{cases}$$

You are given an array a consisting of n integers. You have to calculate the sum of d(ai, aj) over all pairs (i, j) such that 1 ≤ i ≤ j ≤ n.

## 输入格式

The first line contains one integer n (1 ≤ n ≤ 200000) — the number of elements in a.

The second line contains n integers a1, a2, ..., an (1 ≤ ai ≤ 109) — elements of the array.

## 输出格式

Print one integer — the sum of d(ai, aj) over all pairs (i, j) such that 1 ≤ i ≤ j ≤ n.

## 样例

### 样例 1

**输入**:
```
5
1 2 3 1 3
```

**输出**:
```
4
```

### 样例 2

**输入**:
```
4
6 6 5 5
```

**输出**:
```
0
```

### 样例 3

**输入**:
```
4
6 6 4 4
```

**输出**:
```
-8
```

## 备注

In the first example:

1. d(a1, a2) = 0;
2. d(a1, a3) = 2;
3. d(a1, a4) = 0;
4. d(a1, a5) = 2;
5. d(a2, a3) = 0;
6. d(a2, a4) = 0;
7. d(a2, a5) = 0;
8. d(a3, a4) =  - 2;
9. d(a3, a5) = 0;
10. d(a4, a5) = 2.

## 题解

Starting pretty boring this came out as the most fun and controversial problem of the contest...
Well, here is the basis of the solution. You maintain some kind of map/hashmap with amounts each number appeared in array so far. Processing each number you subtract ai i times (1-indexed), add prefix sum up to (i - 1)-th number, subtract cntai - 1 * (ai - 1), cntai * ai and cntai + 1 * (ai + 1) and add ai cntai - 1 + cntai + cntai + 1 times. Then update cnt with ai.
And now we have to treat numbers greater than long long limits. Obviously, you can use built-in bigints from java/python ot write your own class with support of addition and printing of such numbers. However, numbers were up to 1019 by absolute value. Then you can use long double, its precision is enough for simple multiplication and addition. You can also use unsigned long long numbers: one for negative terms and the other one for positive terms, in the end you should handle printing negative numbers.
Overall complexity: $${ \mathcal { O } } ( n \cdot \log n )$$.

