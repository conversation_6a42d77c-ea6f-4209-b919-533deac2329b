# <PERSON><PERSON> Plays With Ch<PERSON><PERSON>

**题目ID**: 896/B  
**比赛**: Codeforces Round 449 (Div. 1)  
**年份**: 2017  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

This is an interactive problem. Refer to the Interaction section below for better understanding.

<PERSON><PERSON> and <PERSON><PERSON><PERSON> want to play a game in order to determine who can use the kitchen tonight.

Initially, <PERSON><PERSON> puts n clear sheets of paper in a line. They are numbered from 1 to n from left to right.

This game will go on for m rounds. In each round, <PERSON><PERSON> will give <PERSON><PERSON><PERSON> an integer between 1 and c, and <PERSON><PERSON><PERSON> needs to choose one of the sheets to write down this number (if there is already a number before, she will erase the original one and replace it with the new one).

<PERSON><PERSON><PERSON> wins if, at any time, all the sheets are filled with a number and the n numbers are in non-decreasing order looking from left to right from sheet 1 to sheet n, and if after m rounds she still doesn't win, she loses the game.

<PERSON><PERSON><PERSON> really wants to win the game as she wants to cook something for <PERSON>. But she doesn't know how to win the game. So <PERSON><PERSON><PERSON> finds you, and your task is to write a program to receive numbers that <PERSON><PERSON> gives <PERSON><PERSON><PERSON> and help her make the decision on which sheet of paper write this number.

## 输入格式

The first line contains 3 integers n, m and c ($$n, m \geq 2, 1 \leq c \leq 1000, 1 \leq n \cdot \left\lceil \frac{c}{2} \right\rceil \leq m \leq 1000$$, $$x$$ means $$x = 2$$ rounded up) — the number of sheets, the number of rounds and the largest possible number Ithea can give to Chtholly respectively. The remaining parts of input are given throughout the interaction process.

## 输出格式

None

## 样例

### 样例 1

**输入**:
```
2 4 4
2
1
3
```

**输出**:
```
1
2
2
```

## 备注

In the example, Chtholly initially knew there were 2 sheets, 4 rounds and each number was between 1 and 4. She then received a 2 and decided to write it in the 1st sheet. Then she received a 1 and wrote it in the 2nd sheet. At last, she received a 3 and replaced 1 with 3 in the 2nd sheet. At this time all the sheets were filled with a number and they were non-decreasing, so she won the game.

Note that it is required that your program terminate immediately after Chtholly wins and do not read numbers from the input for the remaining rounds. If not, undefined behaviour may arise and it won't be sure whether your program will be accepted or rejected. Also because of this, please be careful when hacking others' codes. In the sample, Chtholly won the game after the 3rd round, so it is required that your program doesn't read the number of the remaining 4th round.

The input format for hacking:

- The first line contains 3 integers n, m and c;
- The following m lines each contains an integer between 1 and c, indicating the number given to Chtholly in each round.

## 题解

As the initial grid "has already" in a non-decreasing order (although it has no numbers), what we should do is just "maintain" this order. We use a simple method to do so: find the first grid whose number is greater than the given number (or it's an empty one) and replace it with the new number. It's easy to see that the total sum of the numbers will decrease by at least 1 if we don't fill an empty grid. Thus, the total rounds won't be more than n × c. To pass all the tests, we only need to maintain 2 similar sequences, one non-decreasing from the first and one non-increasing from the last, which makes a total round of $$( n - 1 ) \times \left\lceil { \frac { c } { 2 } } \right\rceil + 1$$, precisely, and use binary search or brute force to complete the "finding" process.

