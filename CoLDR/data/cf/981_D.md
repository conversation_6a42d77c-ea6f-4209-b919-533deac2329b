# Bookshelves

**题目ID**: 981/D  
**比赛**: Avito Code Challenge 2018  
**年份**: 2018  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

Mr <PERSON> is a typical white-collar in Byteland.

He has a bookshelf in his office with some books on it, each book has an integer positive price.

Mr <PERSON> defines the value of a shelf as the sum of books prices on it.

Miraculously, Mr <PERSON> was promoted and now he is moving into a new office.

He learned that in the new office he will have not a single bookshelf, but exactly $$$k$$$ bookshelves. He decided that the beauty of the $$$k$$$ shelves is the bitwise AND of the values of all the shelves.

He also decided that he won't spend time on reordering the books, so he will place several first books on the first shelf, several next books on the next shelf and so on. Of course, he will place at least one book on each shelf. This way he will put all his books on $$$k$$$ shelves in such a way that the beauty of the shelves is as large as possible. Compute this maximum possible beauty.

## 输入格式

The first line contains two integers $$$n$$$ and $$$k$$$ ($$$1 \leq k \leq n \leq 50$$$) — the number of books and the number of shelves in the new office.

The second line contains $$$n$$$ integers $$$a_1, a_2, \ldots a_n$$$, ($$$0 < a_i < 2^{50}$$$) — the prices of the books in the order they stand on the old shelf.

## 输出格式

Print the maximum possible beauty of $$$k$$$ shelves in the new office.

## 样例

### 样例 1

**输入**:
```
10 4
9 14 28 1 7 13 15 29 2 31
```

**输出**:
```
24
```

### 样例 2

**输入**:
```
7 3
3 14 15 92 65 35 89
```

**输出**:
```
64
```

## 备注

In the first example you can split the books as follows:

$$$$$$(9 + 14 + 28 + 1 + 7) \& (13 + 15) \& (29 + 2) \& (31) = 24.$$$$$$

In the second example you can split the books as follows:

$$$$$$(3 + 14 + 15 + 92) \& (65) \& (35 + 89) = 64.$$$$$$

## 题解

Let's build answer from significant bit to least significant bit.
We want to check, is there some partition in $$$k$$$ segments, such that all segment's sums have significant bit?
We can check it with $$$dp_{n, k}$$$ — is there some partition of first $$$n$$$ elements on $$$k$$$ segments such that all segment's sums have significant bit?
And then we will do the same with all other bits, we will try set $$$1$$$ in the bit, and then we want to check for fixed prefix of bits, is there some partition in $$$k$$$ segments, such that all prefixes with same length of all segment's sums are supermasks of current check mask (we will check that with the same dp, $$$dp_{n, k}$$$ — is there some partition of first $$$n$$$ elements on $$$k$$$ segments such that all prefixes of same length of all segment's sums are supermasks of current check mask?)
Bonus 1: Solve the task in $$$O(n^2 \cdot k / 64)$$$
Bonus 2: How many distinct answers you can get in worst case?
solution from cyand1317

