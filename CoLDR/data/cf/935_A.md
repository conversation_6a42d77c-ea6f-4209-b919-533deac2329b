# <PERSON><PERSON><PERSON> and his Company

**题目ID**: 935/A  
**比赛**: Codeforces Round 465 (Div. 2)  
**年份**: 2018  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON><PERSON><PERSON> owns a company that works on huge projects. There are n employees in <PERSON><PERSON><PERSON>'s company. Whenever the company has a new project to start working on, <PERSON><PERSON><PERSON> has to divide the tasks of this project among all the employees.

<PERSON><PERSON><PERSON> finds doing this every time is very tiring for him. So, he decided to choose the best l employees in his company as team leaders. Whenever there is a new project, <PERSON><PERSON><PERSON> will divide the tasks among only the team leaders and each team leader will be responsible of some positive number of employees to give them the tasks. To make this process fair for the team leaders, each one of them should be responsible for the same number of employees. Moreover, every employee, who is not a team leader, has to be under the responsibility of exactly one team leader, and no team leader is responsible for another team leader.

Given the number of employees n, find in how many ways <PERSON><PERSON><PERSON> could choose the number of team leaders l in such a way that it is possible to divide employees between them evenly.

## 输入格式

The input consists of a single line containing a positive integer n (2 ≤ n ≤ 105) — the number of employees in <PERSON><PERSON><PERSON>'s company.

## 输出格式

Print a single integer representing the answer to the problem.

## 样例

### 样例 1

**输入**:
```
2
```

**输出**:
```
1
```

### 样例 2

**输入**:
```
10
```

**输出**:
```
3
```

## 备注

In the second sample Fafa has 3 ways:

- choose only 1 employee as a team leader with 9 employees under his responsibility.
- choose 2 employees as team leaders with 4 employees under the responsibility of each of them.
- choose 5 employees as team leaders with 1 employee under the responsibility of each of them.

## 题解

Let's try all values of l from 1 to n - 1 and check whether the remaining people could be distributed equally over team leaders (that is, l divides n - l). The number of valid values of l is our answer. Complexity: O(n).
The problem is also equivalent to finding the number of ways to divide the n employees into equal teams where each team contains more than one employee. It can also be solved in $$O(\sqrt{n})$$ by finding the number of divisors of n.

