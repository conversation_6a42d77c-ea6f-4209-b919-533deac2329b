# Greed

**题目ID**: 892/A  
**比赛**: Codeforces Round 446 (Div. 2)  
**年份**: 2017  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON><PERSON><PERSON> has n cans of cola. Each can is described by two integers: remaining volume of cola ai and can's capacity bi (ai  ≤  bi).

<PERSON><PERSON><PERSON> has decided to pour all remaining cola into just 2 cans, determine if he can do this or not!

## 输入格式

The first line of the input contains one integer n (2 ≤ n ≤ 100 000) — number of cola cans.

The second line contains n space-separated integers a1, a2, ..., an (0 ≤ ai ≤ 109) — volume of remaining cola in cans.

The third line contains n space-separated integers that b1, b2, ..., bn (ai ≤ bi ≤ 109) — capacities of the cans.

## 输出格式

Print "YES" (without quotes) if it is possible to pour all remaining cola in 2 cans. Otherwise print "NO" (without quotes).

You can print each letter in any case (upper or lower).

## 样例

### 样例 1

**输入**:
```
2
3 5
3 6
```

**输出**:
```
YES
```

### 样例 2

**输入**:
```
3
6 8 9
6 10 12
```

**输出**:
```
NO
```

### 样例 3

**输入**:
```
5
0 0 5 0 0
1 1 8 10 5
```

**输出**:
```
YES
```

### 样例 4

**输入**:
```
4
4 1 0 3
5 2 2 3
```

**输出**:
```
YES
```

## 备注

In the first sample, there are already 2 cans, so the answer is "YES".

## 题解

we sort the capacities in nonincreasing order and let s = capacity1 + capacity2 if

