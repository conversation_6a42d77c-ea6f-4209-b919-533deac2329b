# Magic Breeding

**题目ID**: 878/D  
**比赛**: Codeforces Round 443 (Div. 1)  
**年份**: 2017  
**时间限制**: 4.0秒  
**内存限制**: 1024MB  

## 题目描述

<PERSON><PERSON> and <PERSON> play a computer game where you have to breed some magical creatures. Initially, they have k creatures numbered from 1 to k. Creatures have n different characteristics.

<PERSON> has a spell that allows to create a new creature from two given creatures. Each of its characteristics will be equal to the maximum of the corresponding characteristics of used creatures. <PERSON><PERSON> has a similar spell, but in his spell, each characteristic of the new creature is equal to the minimum of the corresponding characteristics of used creatures. A new creature gets the smallest unused number.

They use their spells and are interested in some characteristics of their new creatures. Help them find out these characteristics.

## 输入格式

The first line contains integers n, k and q (1 ≤ n ≤ 105, 1 ≤ k ≤ 12, 1 ≤ q ≤ 105) — number of characteristics, creatures and queries.

Next k lines describe original creatures. The line i contains n numbers ai1, ai2, ..., ain (1 ≤ aij ≤ 109) — characteristics of the i-th creature.

Each of the next q lines contains a query. The i-th of these lines contains numbers ti, xi and yi (1 ≤ ti ≤ 3). They denote a query:

- ti = 1 means that <PERSON> used his spell to the creatures xi and yi.
- ti = 2 means that <PERSON><PERSON> used his spell to the creatures xi and yi.
- ti = 3 means that they want to know the yi-th characteristic of the xi-th creature. In this case 1 ≤ yi ≤ n.

It's guaranteed that all creatures' numbers are valid, that means that they are created before any of the queries involving them.

## 输出格式

For each query with ti = 3 output the corresponding characteristic.

## 样例

### 样例 1

**输入**:
```
2 2 4
1 2
2 1
1 1 2
2 1 2
3 3 1
3 4 2
```

**输出**:
```
2
1
```

### 样例 2

**输入**:
```
5 3 8
1 2 3 4 5
5 1 2 3 4
4 5 1 2 3
1 1 2
1 2 3
2 4 5
3 6 1
3 6 2
3 6 3
3 6 4
3 6 5
```

**输出**:
```
5
2
2
3
4
```

## 备注

In the first sample, Sasha makes a creature with number 3 and characteristics (2, 2). Nikita makes a creature with number 4 and characteristics (1, 1). After that they find out the first characteristic for the creature 3 and the second characteristic for the creature 4.

## 题解

Let's consider a special case of the problem: all aij are 0 or 1. In this case there are at most 2k different characteristics. So we can use trivial solution, it works in O(q2k). Also we can sped up it using bitset.
Now we reduce the problem to this special case. We have a characteristic with values x1 ≤ x2 ≤ ... ≤ xk. Let's make k characteristics from it. i-th of them is one if and only if the original characteristic is at least xi, and zero otherwise. New characteristics behave correctly during our operations, and we can efficiently get old characteristics from them.
Number of characteristics has increased, but is doesn't matter for our solution for the special case. This solution works in O(q2k).

