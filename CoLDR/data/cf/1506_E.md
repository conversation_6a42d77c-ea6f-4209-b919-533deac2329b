# Restoring the Permutation

**题目ID**: 1506/E  
**比赛**: Codeforces Round 710 (Div. 3)  
**年份**: 2021  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

A permutation is a sequence of $$$n$$$ integers from $$$1$$$ to $$$n$$$, in which all numbers occur exactly once. For example, $$$[1]$$$, $$$[3, 5, 2, 1, 4]$$$, $$$[1, 3, 2]$$$ are permutations, and $$$[2, 3, 2]$$$, $$$[4, 3, 1]$$$, $$$[0]$$$ are not.

<PERSON><PERSON><PERSON><PERSON> was presented with a permutation $$$p$$$ of numbers from $$$1$$$ to $$$n$$$. However, when <PERSON><PERSON><PERSON><PERSON> came home, he noticed that in his pocket, the permutation $$$p$$$ had turned into an array $$$q$$$ according to the following rule:

- $$$q_i = \max(p_1, p_2, \ldots, p_i)$$$.

Now <PERSON><PERSON><PERSON><PERSON> wondered what lexicographically minimal and lexicographically maximal permutations could be presented to him.

An array $$$a$$$ of length $$$n$$$ is lexicographically smaller than an array $$$b$$$ of length $$$n$$$ if there is an index $$$i$$$ ($$$1 \le i \le n$$$) such that the first $$$i-1$$$ elements of arrays $$$a$$$ and $$$b$$$ are the same, and the $$$i$$$-th element of the array $$$a$$$ is less than the $$$i$$$-th element of the array $$$b$$$. For example, the array $$$a=[1, 3, 2, 3]$$$ is lexicographically smaller than the array $$$b=[1, 3, 4, 2]$$$.

For example, if $$$n=7$$$ and $$$p=[3, 2, 4, 1, 7, 5, 6]$$$, then $$$q=[3, 3, 4, 4, 7, 7, 7]$$$ and the following permutations could have been as $$$p$$$ initially:

- $$$[3, 1, 4, 2, 7, 5, 6]$$$ (lexicographically minimal permutation);
- $$$[3, 1, 4, 2, 7, 6, 5]$$$;
- $$$[3, 2, 4, 1, 7, 5, 6]$$$;
- $$$[3, 2, 4, 1, 7, 6, 5]$$$ (lexicographically maximum permutation).

For a given array $$$q$$$, find the lexicographically minimal and lexicographically maximal permutations that could have been originally presented to Polycarp.

## 输入格式

The first line contains one integer $$$t$$$ ($$$1 \le t \le 10^4$$$). Then $$$t$$$ test cases follow.

The first line of each test case contains one integer $$$n$$$ ($$$1 \le n \le 2 \cdot 10^5$$$).

The second line of each test case contains $$$n$$$ integers $$$q_1, q_2, \ldots, q_n$$$ ($$$1 \le q_i \le n$$$).

It is guaranteed that the array $$$q$$$ was obtained by applying the rule from the statement to some permutation $$$p$$$.

It is guaranteed that the sum of $$$n$$$ over all test cases does not exceed $$$2 \cdot 10^5$$$.

## 输出格式

For each test case, output two lines:

- on the first line output $$$n$$$ integers — lexicographically minimal permutation that could have been originally presented to Polycarp;
- on the second line print $$$n$$$ integers — lexicographically maximal permutation that could have been originally presented to Polycarp;

## 样例

### 样例 1

**输入**:
```
4
7
3 3 4 4 7 7 7
4
1 2 3 4
7
3 4 5 5 5 7 7
1
1
```

**输出**:
```
3 1 4 2 7 5 6 
3 2 4 1 7 6 5 
1 2 3 4 
1 2 3 4 
3 4 5 1 2 7 6 
3 4 5 2 1 7 6 
1 
1
```

## 题解

If we want to build a minimal lexicographic permutation, we need to build it from left to right by adding the smallest possible element. If $$$q[i] = q[i-1]$$$, so the new number must not be greater than all the previous ones, and if $$$q[i] > q[i-1]$$$, then necessarily $$$a[i] = q[i]$$$. $$$q[i] < q[i-1]$$$ does not happen, since $$$q[i]$$$ — is the maximum element among the first $$$i$$$ elements.
We get a greedy solution if $$$q[i] > q[i-1]$$$, then $$$a[i] = q[i]$$$, otherwise we put the minimum character that has not yet occurred in the permutation.

