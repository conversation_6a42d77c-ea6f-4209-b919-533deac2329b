# Merge Equals

**题目ID**: 962/D  
**比赛**: Educational Codeforces Round 42 (Rated for Div. 2)  
**年份**: 2018  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

You are given an array of positive integers. While there are at least two equal elements, we will perform the following operation. We choose the smallest value $$$x$$$ that occurs in the array $$$2$$$ or more times. Take the first two occurrences of $$$x$$$ in this array (the two leftmost occurrences). Remove the left of these two occurrences, and the right one is replaced by the sum of this two values (that is, $$$2 \cdot x$$$).

Determine how the array will look after described operations are performed.

For example, consider the given array looks like $$$[3, 4, 1, 2, 2, 1, 1]$$$. It will be changed in the following way: $$$[3, 4, 1, 2, 2, 1, 1]~\rightarrow~[3, 4, 2, 2, 2, 1]~\rightarrow~[3, 4, 4, 2, 1]~\rightarrow~[3, 8, 2, 1]$$$.

If the given array is look like $$$[1, 1, 3, 1, 1]$$$ it will be changed in the following way: $$$[1, 1, 3, 1, 1]~\rightarrow~[2, 3, 1, 1]~\rightarrow~[2, 3, 2]~\rightarrow~[3, 4]$$$.

## 输入格式

The first line contains a single integer $$$n$$$ ($$$2 \le n \le 150\,000$$$) — the number of elements in the array.

The second line contains a sequence from $$$n$$$ elements $$$a_1, a_2, \dots, a_n$$$ ($$$1 \le a_i \le 10^{9}$$$) — the elements of the array.

## 输出格式

In the first line print an integer $$$k$$$ — the number of elements in the array after all the performed operations. In the second line print $$$k$$$ integers — the elements of the array after all the performed operations.

## 样例

### 样例 1

**输入**:
```
7
3 4 1 2 2 1 1
```

**输出**:
```
4
3 8 2 1
```

### 样例 2

**输入**:
```
5
1 1 3 1 1
```

**输出**:
```
2
3 4
```

### 样例 3

**输入**:
```
5
10 40 20 50 30
```

**输出**:
```
5
10 40 20 50 30
```

## 备注

The first two examples were considered in the statement.

In the third example all integers in the given array are distinct, so it will not change.

## 题解

To solve this problem we should use a set of pairs, let's call it $$$b$$$. We will store in it all the elements of the current array (the first number in the pair)and the positions of these elements in the current array (the second number in the pair). The first elements of pairs should have type long long, because the result of merging the array elements can become large and the type int will overflow.
Initially, you need to put in $$$b$$$ all elements of the given array with their positions.
While there are elements in $$$b$$$, we will perform the following algorithm. Let $$$x$$$ be a pair in the beginning of $$$b$$$. Then look at the next element of $$$b$$$. If it does not exist, the algorithm is complete. Otherwise, let the next element is equal to $$$y$$$.
If $$$x.first \neq y.first$$$, then there is no pair number for the element $$$x.first$$$, which is in the position $$$x.second$$$, and it will never appear, because all elements can only increase. So, remove $$$x$$$ from $$$b$$$ and repeat the algorithm from the beginning.
Otherwise, the number at the position $$$x.second$$$ will be deleted, and the number in the position $$$y.second$$$ will be double up. So, remove $$$x$$$ and $$$y$$$ from $$$b$$$, put $$$(y.first \cdot 2, y.second)$$$ in $$$b$$$ and repeat the algorithm from the beginning. For the convenience of restoring the answer, you can mark deleted positions of the given array in an additional array, so, in this case you need to mark $$$x.second$$$ as a deleted position.

