# Nasty<PERSON> and a Wardrobe

**题目ID**: 992/C  
**比赛**: Codeforces Round 489 (Div. 2)  
**年份**: 2018  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON><PERSON><PERSON> received a gift on New Year — a magic wardrobe. It is magic because in the end of each month the number of dresses in it doubles (i.e. the number of dresses becomes twice as large as it is in the beginning of the month).

Unfortunately, right after the doubling the wardrobe eats one of the dresses (if any) with the 50% probability. It happens every month except the last one in the year.

<PERSON><PERSON><PERSON> owns x dresses now, so she became interested in the expected number of dresses she will have in one year. <PERSON><PERSON><PERSON> lives in Byteland, so the year lasts for k + 1 months.

<PERSON><PERSON><PERSON> is really busy, so she wants you to solve this problem. You are the programmer, after all. Also, you should find the answer modulo 109 + 7, because it is easy to see that it is always integer.

## 输入格式

The only line contains two integers x and k (0 ≤ x, k ≤ 1018), where x is the initial number of dresses and k + 1 is the number of months in a year in Byteland.

## 输出格式

In the only line print a single integer — the expected number of dresses <PERSON><PERSON><PERSON> will own one year later modulo 109 + 7.

## 样例

### 样例 1

**输入**:
```
2 0
```

**输出**:
```
4
```

### 样例 2

**输入**:
```
2 1
```

**输出**:
```
7
```

### 样例 3

**输入**:
```
3 2
```

**输出**:
```
21
```

## 备注

In the first example a year consists on only one month, so the wardrobe does not eat dresses at all.

In the second example after the first month there are 3 dresses with 50% probability and 4 dresses with 50% probability. Thus, in the end of the year there are 6 dresses with 50% probability and 8 dresses with 50% probability. This way the answer for this test is (6 + 8) / 2 = 7.

## 题解

Let's present we have initially $$X = Y + \frac{1}{2}$$ dresses.
What does occur in the first month? Initially the number of dresses is multiplied by 2, that is becomes $$2 \cdot Y + 1$$. Then with probability $$50\%$$ the wardrobe eats a dress, that is expected value of the number of dresses becomes $$2 \cdot Y + \frac{1}{2}$$. The same way after the second month expected value becomes $$4 \cdot Y + \frac{1}{2}$$. It's easy to notice that after $$p$$-th month(if $$p \leq k$$) expected value equals $$2^p \cdot Y + \frac{1}{2}$$. Eventually it will be only doubled(as the wardrobe doesn't eat a dress in the last month), that is will be equal $$2^{k+1} \cdot Y + 1$$.
Thus, answer of the problem is $$2^{k+1} \cdot Y + 1$$. Expressing it with $$\chi$$, we get:
$$M$$ = $$2^{k+1} \cdot (X - \frac{1}{2}) + 1$$.
$$M$$ = $$2^{k+1} \cdot X - 2^k + 1$$.
Thus, we need to calculate degree of 2 right up to $${ 1 0 } ^ { 1 8 }$$. Complexity of the soltion is $$O(\log(k))$$.
Let's notice that the case $$x = 0$$ we need to calculate separately, because wardrobe can't eat a dress when it doesn't exist. If $$x \geq 0$$ it's easy to proof that the number of dresses is never negative, that is the formula works.

