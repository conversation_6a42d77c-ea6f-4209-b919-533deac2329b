# Tiles for Bathroom

**题目ID**: 1500/D  
**比赛**: Codeforces Round 707 (Div. 1, based on Moscow Open Olympiad in Informatics)  
**年份**: 2021  
**时间限制**: 5.0秒  
**内存限制**: 512MB  

## 题目描述

<PERSON><PERSON><PERSON> is extremely busy: he is renovating his house! He needs to hand wallpaper, assemble furniture throw away trash.

<PERSON><PERSON><PERSON> is buying tiles for bathroom today. He is standing in front of a large square stand with tiles in a shop. The stand is a square of $$$n \times n$$$ cells, each cell of which contains a small tile with color $$$c_{i,\,j}$$$. The shop sells tiles in packs: more specifically, you can only buy a subsquare of the initial square.

A subsquare is any square part of the stand, i. e. any set $$$S(i_0, j_0, k) = \{c_{i,\,j}\ |\ i_0 \le i < i_0 + k, j_0 \le j < j_0 + k\}$$$ with $$$1 \le i_0, j_0 \le n - k + 1$$$.

<PERSON><PERSON><PERSON> still does not know how many tiles he needs, so he considers the subsquares of all possible sizes. He doesn't want his bathroom to be too colorful. Help <PERSON><PERSON><PERSON> to count for each $$$k \le n$$$ the number of subsquares of size $$$k \times k$$$ that have at most $$$q$$$ different colors of tiles. Two subsquares are considered different if their location on the stand is different.

## 输入格式

The first line contains two integers $$$n$$$ and $$$q$$$ ($$$1 \le n \le 1500$$$, $$$1 \le q \le 10$$$) — the size of the stand and the limit on the number of distinct colors in a subsquare.

Each of the next $$$n$$$ lines contains $$$n$$$ integers $$$c_{i,\,j}$$$ ($$$1 \le c_{i,\,j} \le n^2$$$): the $$$j$$$-th integer in the $$$i$$$-th line is the color of the tile in the cell $$$(i,\,j)$$$.

## 输出格式

For each $$$k$$$ from $$$1$$$ to $$$n$$$ print a single integer — the number of subsquares of size $$$k \times k$$$ with no more than $$$q$$$ different colors.

## 样例

### 样例 1

**输入**:
```
3 4
1 2 3
4 5 6
7 8 9
```

**输出**:
```
9
4
0
```

### 样例 2

**输入**:
```
4 8
1 2 3 4
5 6 7 8
9 1 2 3
4 5 6 7
```

**输出**:
```
16
9
4
0
```

## 备注

In the first example all colors are distinct. Kostya doesn't want the subsquare have more than $$$4$$$ colors, so he can buy any subsquare of size $$$1 \times 1$$$ or $$$2 \times 2$$$, but he can't buy a subsquare of size $$$3 \times 3$$$.

In the second example there are colors that appear multiple times. Because $$$q = 8$$$, Kostya can buy any subsquare of size $$$1 \times 1$$$ and $$$2 \times 2$$$, and any subsquare $$$3 \times 3$$$, because of such subsquare has $$$7$$$ different colors. He can't buy the whole stand $$$4 \times 4$$$, because there are $$$9$$$ colors.

## 题解

Let's denote $$$ans_{i,\,j}$$$ as max "good" subsquare side size with left top angle at $$$(i,\,j)$$$ cell. It's obvious that every subsquare with side less than $$$ans_{i,\,j}$$$ is "good" too. So we need to find $$$ans_{i,\,j}$$$ and then print answer for k-size side as $$$\displaystyle \sum_{x=k}^{n} |\{(i,\,j)\ |\ ans_{i,\,j} = k\}$$$. This value can be found with partial sums on count array of $$$ans_{i,\,j}$$$.
Now we need to calculate $$$ans_{i,\,j}$$$. Let's notice that $$$ans_{i,\,j+1} \ge ans_{i,\,j} - 1$$$, because $$$(i, j + 1)$$$-th square is inside of $$$(i, j)$$$ square if it's side is longer than 1. So, we can use amortized algorithm of sequential increase square's side. Every time when we can increase $$$ans_{i,\,j}$$$ we will do it, then we will go to calculating $$$ans_{i,\,j+1}$$$. Using linear algorithm to check if new square's size is "good", we will get $$$O(n^3)$$$ solution, which is not effective enough, but can be optimized.
Now let's use small constraints for $$$q$$$. For each $$$(i,\,j)$$$ cell we will precalc array $$$colors_{i,\,j}$$$, which will have $$$(q + 1)$$$ nearest colors in line $$$(i',\,j), i' \ge i$$$. For each color, we will keep it's earliest occurrence It can be calculated in $$$O(n^2 q)$$$, because $$$colors_{i,\,j} = colors_{i,\,j + 1} \cup c_{i,\,j}$$$. If $$$c_{i,\,j}$$$ exists in $$$colors_{i,\,j}$$$, we must change earliest occurrence. If it isn't, we should delete the most far color, then insert the current one. All values can be keeped with increasing left occurrence in $$$O(q)$$$ for each cell.
Last step — we can merge $$$colors_{i,\,j},\ \ldots,\ colors_{i + m - 1,\,j}$$$ in $$$O(mq)$$$ — it will be the array of $$$q + 1$$$ values corresponding for earliest colors in line of width $$$m$$$. Merging two lines is almost like merge of two sorted arrays — it is used to find $$$q + 1$$$ minimal left occurrences. Maximal "good" width can be found as left occurrence of $$$(q+1)$$$-th elementh minus $$$j$$$.
Now we need to unite some rows in a structure and merge lines for current square, then erase first row and repeat the process. The structure can be realized as queue on two stacks, each of whose is keeping merge value of all lines in itself. The algorithm is almost like queue with minimal value (https://cp-algorithms.com/data_structures/stack_queue_modification.html). That's how we get solution with complexity $$$O(n^2q)$$$.

