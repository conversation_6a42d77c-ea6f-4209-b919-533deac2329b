# High Cry

**题目ID**: 875/D  
**比赛**: Codeforces Round 441 (Div. 1, by Moscow Team Olympiad)  
**年份**: 2017  
**时间限制**: 1.0秒  
**内存限制**: 512MB  

## 题目描述

Disclaimer: there are lots of untranslateable puns in the Russian version of the statement, so there is one more reason for you to learn Russian :)

<PERSON> and <PERSON><PERSON><PERSON> like to go to the ridge High Cry for crying loudly — there is an extraordinary echo. Recently they discovered an interesting acoustic characteristic of this ridge: if <PERSON> and <PERSON><PERSON><PERSON> begin crying simultaneously from different mountains, their cry would be heard between these mountains up to the height equal the bitwise OR of mountains they've climbed and all the mountains between them.

Bitwise OR is a binary operation which is determined the following way. Consider representation of numbers x and y in binary numeric system (probably with leading zeroes) x = xk... x1x0 and y = yk... y1y0. Then z = x | y is defined following way: z = zk... z1z0, where zi = 1, if xi = 1 or yi = 1, and zi = 0 otherwise. In the other words, digit of bitwise OR of two numbers equals zero if and only if digits at corresponding positions is both numbers equals zero. For example bitwise OR of numbers 10 = 10102 and 9 = 10012 equals 11 = 10112. In programming languages C/C++/Java/Python this operation is defined as «|», and in Pascal as «or».

Help <PERSON> and <PERSON><PERSON><PERSON> calculate the number of ways they can select two mountains in such a way that if they start crying from these mountains their cry will be heard above these mountains and all mountains between them. More formally you should find number of pairs l and r (1 ≤ l < r ≤ n) such that bitwise OR of heights of all mountains between l and r (inclusive) is larger than the height of any mountain at this interval.

## 输入格式

The first line contains integer n (1 ≤ n ≤ 200 000), the number of mountains in the ridge.

Second line contains n integers ai (0 ≤ ai ≤ 109), the heights of mountains in order they are located in the ridge.

## 输出格式

Print the only integer, the number of ways to choose two different mountains.

## 样例

### 样例 1

**输入**:
```
5
3 2 1 6 5
```

**输出**:
```
8
```

### 样例 2

**输入**:
```
4
3 3 3 3
```

**输出**:
```
0
```

## 备注

In the first test case all the ways are pairs of mountains with the numbers (numbering from one):

(1, 4), (1, 5), (2, 3), (2, 4), (2, 5), (3, 4), (3, 5), (4, 5)

In the second test case there are no such pairs because for any pair of mountains the height of cry from them is 3, and this height is equal to the height of any mountain.

## 题解

First we find for each element the nearest element on the left and on the right more than it. It can be done by many ways, for example using stack.
Then you find for each element x the nearest on the left and on the right element y so that x|y > x. For this note that in y must be some bit set, which is not set in x. So you can just pass from left to the right (and then from right to the left) along the array, calculating goi — the nearest on the left (on the right) element in which the bit i equals 1.
We fix the mountain which will be the highest on the segment from the answer (if the heights are equal — the most left, for example). Then the segment must be completely nested in the segment on which the given mountain is the highest and must cross at least one element, OR with which our element is greater than the element itself.
This solution works in O(n) + O(nlogc) + O(n) = O(nlogc).

