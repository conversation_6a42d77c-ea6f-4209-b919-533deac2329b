# <PERSON><PERSON> and Problem Wihtout a Legend

**题目ID**: 713/C  
**比赛**: Codeforces Round 371 (Div. 1)  
**年份**: 2016  
**时间限制**: 5.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON><PERSON> was unable to think of a story for this problem, so here comes the formal description.

You are given the array containing n positive integers. At one turn you can pick any element and increase or decrease it by 1. The goal is the make the array strictly increasing by making the minimum possible number of operations. You are allowed to change elements in any way, they can become negative or equal to 0.

## 输入格式

The first line of the input contains a single integer n (1 ≤ n ≤ 3000) — the length of the array.

Next line contains n integer ai (1 ≤ ai ≤ 109).

## 输出格式

Print the minimum number of operation required to make the array strictly increasing.

## 样例

### 样例 1

**输入**:
```
7
2 1 5 11 5 9 11
```

**输出**:
```
9
```

### 样例 2

**输入**:
```
5
5 4 3 2 1
```

**输出**:
```
12
```

## 备注

In the first sample, the array is going to look as follows:

2 3 5 6 7 9 11

|2 - 2| + |1 - 3| + |5 - 5| + |11 - 6| + |5 - 7| + |9 - 9| + |11 - 11| = 9

And for the second sample:

1 2 3 4 5

|5 - 1| + |4 - 2| + |3 - 3| + |2 - 4| + |1 - 5| = 12

## 题解

Lets first solve easier problem. Given an array of number what it is minimal amount of operations ( + 1 to element,  - 1 to element) to make all numbers in array equal? We need to solve this problem for each prefix. Optimal solution would be making all numbers equal to median value of the prefix (middle element in sorted list). For this problem we can simply use two heaps and insert element in right one (removing elements from inserted if need) to keep heaps equal and fit the constraint max_value(Heap1)  ≤  min_value(Heap2).
Now lets solve harder problem. What is minimal amount of operations ( + 1 to element,  - 1 to element) to make array be arithmetics progression with step 1? We can just reduce number ai in array by value i and will receive previous problem.
Finally we have original problem. Dpi — answer for prefix ending in i, i.e. number of operations to make prefix of first i elements in increasing order. Also for each i will remember minimal last number in resulting sequence. For each i will bruteforce value j (i > j) and calculate answer for j if [i + 1, j] if arithmetics progression with step 1. Also we need to assume if median value in [i + 1, j] is lower than minimal value at i than we cannot update answer for j by answer for i.

