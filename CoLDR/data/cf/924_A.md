# Mystical Mosaic

**题目ID**: 924/A  
**比赛**: VK Cup 2018 - Round 2  
**年份**: 2018  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

There is a rectangular grid of n rows of m initially-white cells each.

<PERSON><PERSON> performed a certain number (possibly zero) of operations on it. In the i-th operation, a non-empty subset of rows Ri and a non-empty subset of columns Ci are chosen. For each row r in Ri and each column c in Ci, the intersection of row r and column c is coloured black.

There's another constraint: a row or a column can only be chosen at most once among all operations. In other words, it means that no pair of (i, j) (i < j) exists such that $$R_i \cap R_j \neq \varnothing$$ or $$C_i \cap C_j \neq \varnothing$$, where ? denotes intersection of sets, and $$\phi$$ denotes the empty set.

You are to determine whether a valid sequence of operations exists that produces a given final grid.

## 输入格式

The first line contains two space-separated integers n and m (1 ≤ n, m ≤ 50) — the number of rows and columns of the grid, respectively.

Each of the following n lines contains a string of m characters, each being either '.' (denoting a white cell) or '#' (denoting a black cell), representing the desired setup.

## 输出格式

If the given grid can be achieved by any valid sequence of operations, output "Yes"; otherwise output "No" (both without quotes).

You can print each character in any case (upper or lower).

## 样例

### 样例 1

**输入**:
```
5 8
.#.#..#.
.....#..
.#.#..#.
#.#....#
.....#..
```

**输出**:
```
Yes
```

### 样例 2

**输入**:
```
5 5
..#..
..#..
#####
..#..
..#..
```

**输出**:
```
No
```

### 样例 3

**输入**:
```
5 9
........#
#........
..##.#...
.......#.
....#.#.#
```

**输出**:
```
No
```

## 备注

For the first example, the desired setup can be produced by 3 operations, as is shown below.

For the second example, the desired setup cannot be produced, since in order to colour the center row, the third row and all columns must be selected in one operation, but after that no column can be selected again, hence it won't be possible to colour the other cells in the center column.

## 题解

No row or column can be selected more than once, hence whenever a row r is selected in an operation, all cells in it uniquely determine the set of columns that need to be selected — let's call it Sr.
Let's assume a valid set of operations exists. Take out any two rows, i and j. If rows i and j are selected in the same operation, we can deduce that Si = Sj; if they're in different operations, we get $$S_i \cap S_j = \varnothing$$. Therefore, if Si ≠ Sj and $$S_{i} \cap S_{j} \neq \varnothing$$ hold for any pair of rows (i, j), no valid operation sequence can be found.
Otherwise (no pair violates the condition above), a valid sequence of operations can be constructed: group all rows with the same S's and carry out an operation with each group.
Thus, it's a necessary and sufficient condition for the answer to be "Yes", that for each pair of rows (i, j), either Si = Sj or $$S_i \cap S_j = \varnothing$$ holds.
The overall complexity is O(n2m). It can be divided by the system's word size if you're a bitset enthusiast, and a lot more if hashes and hash tables release their full power.

