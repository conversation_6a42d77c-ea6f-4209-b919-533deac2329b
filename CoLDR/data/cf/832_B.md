# <PERSON><PERSON> and <PERSON>am

**题目ID**: 832/B  
**比赛**: Codeforces Round 425 (Div. 2)  
**年份**: 2017  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

It's hard times now. Today <PERSON><PERSON> needs to score 100 points on Informatics exam. The tasks seem easy to <PERSON><PERSON>, but he thinks he lacks time to finish them all, so he asks you to help with one..

There is a glob pattern in the statements (a string consisting of lowercase English letters, characters "?" and "*"). It is known that character "*" occurs no more than once in the pattern.

Also, n query strings are given, it is required to determine for each of them if the pattern matches it or not.

Everything seemed easy to <PERSON><PERSON>, but then he discovered that the special pattern characters differ from their usual meaning.

A pattern matches a string if it is possible to replace each character "?" with one good lowercase English letter, and the character "*" (if there is one) with any, including empty, string of bad lowercase English letters, so that the resulting string is the same as the given string.

The good letters are given to <PERSON><PERSON>. All the others are bad.

## 输入格式

The first line contains a string with length from 1 to 26 consisting of distinct lowercase English letters. These letters are good letters, all the others are bad.

The second line contains the pattern — a string s of lowercase English letters, characters "?" and "*" (1 ≤ |s| ≤ 105). It is guaranteed that character "*" occurs in s no more than once.

The third line contains integer n (1 ≤ n ≤ 105) — the number of query strings.

n lines follow, each of them contains single non-empty string consisting of lowercase English letters — a query string.

It is guaranteed that the total length of all query strings is not greater than 105.

## 输出格式

Print n lines: in the i-th of them print "YES" if the pattern matches the i-th query string, and "NO" otherwise.

You can choose the case (lower or upper) for each letter arbitrary.

## 样例

### 样例 1

**输入**:
```
ab
a?a
2
aaa
aab
```

**输出**:
```
YES
NO
```

### 样例 2

**输入**:
```
abc
a?a?a*
4
abacaba
abaca
apapa
aaaaax
```

**输出**:
```
NO
YES
NO
YES
```

## 备注

In the first example we can replace "?" with good letters "a" and "b", so we can see that the answer for the first query is "YES", and the answer for the second query is "NO", because we can't match the third letter.

Explanation of the second example.

- The first query: "NO", because character "*" can be replaced with a string of bad letters only, but the only way to match the query string is to replace it with the string "ba", in which both letters are good.
- The second query: "YES", because characters "?" can be replaced with corresponding good letters, and character "*" can be replaced with empty string, and the strings will coincide.
- The third query: "NO", because characters "?" can't be replaced with bad letters.
- The fourth query: "YES", because characters "?" can be replaced with good letters "a", and character "*" can be replaced with a string of bad letters "x".

## 题解

If pattern doesn't contain "*", to match pattern, it's neccesary that string's size is equal to pattern size , all letters in pattern match with the letters in string, and in the positions on which pattern contains "?", the string contains good characters.
If pattern contains "*", split it into two strings: p1 contains all characters before "*" and p2 after it. Note, that string doesn't match pattern, if it's size is less than |p1| + |p2|. Split the string into three: s1 is prefix of size |p1|, s2 is suffix of size |p2|, and s3 is the remaining substring. The string mathes pattern, if s1 matches p1, s2 matches p2 and s3 contains only bad characters.
Obviously, that we can make all checks in time of O(|s|). Final asymptotics is $$O\left(\sum_{i=1}^{n}|s|\right)$$.

