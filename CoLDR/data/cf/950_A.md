# Left-handers, Right-handers and Ambidexters

**题目ID**: 950/A  
**比赛**: Codeforces Round 469 (Div. 2)  
**年份**: 2018  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

You are at a water bowling training. There are l people who play with their left hand, r people, who play with their right hand, and a ambidexters, who can play with left or right hand.

The coach decided to form a team of even number of players, exactly half of the players should play with their right hand, and exactly half of the players should play with their left hand. One player should use only on of his hands.

Ambidexters play as well with their right hand as with their left hand. In the team, an ambidexter can play with their left hand, or with their right hand.

Please find the maximum possible size of the team, where equal number of players use their left and right hands, respectively.

## 输入格式

The only line contains three integers l, r and a (0 ≤ l, r, a ≤ 100) — the number of left-handers, the number of right-handers and the number of ambidexters at the training.

## 输出格式

Print a single even integer — the maximum number of players in the team. It is possible that the team can only have zero number of players.

## 样例

### 样例 1

**输入**:
```
1 4 2
```

**输出**:
```
6
```

### 样例 2

**输入**:
```
5 5 5
```

**输出**:
```
14
```

### 样例 3

**输入**:
```
0 2 0
```

**输出**:
```
0
```

## 备注

In the first example you can form a team of 6 players. You should take the only left-hander and two ambidexters to play with left hand, and three right-handers to play with right hand. The only person left can't be taken into the team.

In the second example you can form a team of 14 people. You have to take all five left-handers, all five right-handers, two ambidexters to play with left hand and two ambidexters to play with right hand.

## 题解

Iterate over size of the team. Now you know how many players should play with left hand, but are not left-handed (because there are no so much left-handed players). The same with right hand. Just check if sum of these values is not more than number of ambidexters.
BONUS (easy): Solve this problem in O(1) time.
BONUS (easy): Now you have many millipedes with n hands. Each millipede can either play with one hand or is ambidexter and can play with any hand. For each number of hand you know number of millipedes can play with it and number of ambidexters. Find out maximum number of team of millipedes in which there is equal amount of millipedes which play with some hand.

