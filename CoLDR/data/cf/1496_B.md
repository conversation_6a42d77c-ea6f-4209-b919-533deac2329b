# Max and Mex

**题目ID**: 1496/B  
**比赛**: Codeforces Round 706 (Div. 2)  
**年份**: 2021  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

You are given a multiset $$$S$$$ initially consisting of $$$n$$$ distinct non-negative integers. A multiset is a set, that can contain some elements multiple times.

You will perform the following operation $$$k$$$ times:

- Add the element $$$\lceil\frac{a+b}{2}\rceil$$$ (rounded up) into $$$S$$$, where $$$a = \operatorname{mex}(S)$$$ and $$$b = \max(S)$$$. If this number is already in the set, it is added again.

Here $$$\operatorname{max}$$$ of a multiset denotes the maximum integer in the multiset, and $$$\operatorname{mex}$$$ of a multiset denotes the smallest non-negative integer that is not present in the multiset. For example:

- $$$\operatorname{mex}(\{1,4,0,2\})=3$$$;
- $$$\operatorname{mex}(\{2,5,1\})=0$$$.

Your task is to calculate the number of distinct elements in $$$S$$$ after $$$k$$$ operations will be done.

## 输入格式

The input consists of multiple test cases. The first line contains a single integer $$$t$$$ ($$$1\le t\le 100$$$) — the number of test cases. The description of the test cases follows.

The first line of each test case contains two integers $$$n$$$, $$$k$$$ ($$$1\le n\le 10^5$$$, $$$0\le k\le 10^9$$$) — the initial size of the multiset $$$S$$$ and how many operations you need to perform.

The second line of each test case contains $$$n$$$ distinct integers $$$a_1,a_2,\dots,a_n$$$ ($$$0\le a_i\le 10^9$$$) — the numbers in the initial multiset.

It is guaranteed that the sum of $$$n$$$ over all test cases does not exceed $$$10^5$$$.

## 输出格式

For each test case, print the number of distinct elements in $$$S$$$ after $$$k$$$ operations will be done.

## 样例

### 样例 1

**输入**:
```
5
4 1
0 1 3 4
3 1
0 1 4
3 0
0 1 4
3 2
0 1 2
3 2
1 2 3
```

**输出**:
```
4
4
3
5
3
```

## 备注

In the first test case, $$$S=\{0,1,3,4\}$$$, $$$a=\operatorname{mex}(S)=2$$$, $$$b=\max(S)=4$$$, $$$\lceil\frac{a+b}{2}\rceil=3$$$. So $$$3$$$ is added into $$$S$$$, and $$$S$$$ becomes $$$\{0,1,3,3,4\}$$$. The answer is $$$4$$$.

In the second test case, $$$S=\{0,1,4\}$$$, $$$a=\operatorname{mex}(S)=2$$$, $$$b=\max(S)=4$$$, $$$\lceil\frac{a+b}{2}\rceil=3$$$. So $$$3$$$ is added into $$$S$$$, and $$$S$$$ becomes $$$\{0,1,3,4\}$$$. The answer is $$$4$$$.

## 题解

Let $$$a=\max(S),b=\operatorname{mex}(S)$$$.
When $$$k=0$$$, the answer is $$$n$$$.
Otherwise if $$$b>a$$$, then $$$b=a+1$$$ , so $$$\lceil\frac{a+b}{2}\rceil=b$$$ . It's not hard to find out that $$$\max(S\cup\{b\})=b,\operatorname{mex}(S\cup\{b\})=b+1$$$, so the set $$$S$$$ always satisfies $$$\max(S)+1=\operatorname{mex}(S)$$$. So the answer is $$$n+k$$$ when $$$b=a+1$$$.
Otherwise $$$b<a$$$. So $$$b<a \Rightarrow 2b<a+b\Rightarrow \frac{a+b}{2}>b\Rightarrow\lceil\frac{a+b}{2}\rceil>b$$$. In that case $$$\operatorname{mex}(S)=b$$$ is always true. So the element we add in all operations is always $$$\lceil\frac{a+b}{2}\rceil$$$. Just check whether it is in $$$S$$$ at first.
The time complexity is $$$O(n)$$$ or $$$O(n \log n)$$$ for each test case depending on your implementation.

