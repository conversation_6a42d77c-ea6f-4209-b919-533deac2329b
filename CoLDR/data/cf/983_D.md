# Arkady and Rectangles

**题目ID**: 983/D  
**比赛**: Codeforces Round 483 (Div. 1) [Thanks, Botan Investments and <PERSON>!]  
**年份**: 2018  
**时间限制**: 4.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON><PERSON> has got an infinite plane painted in color $$$0$$$. Then he draws $$$n$$$ rectangles filled with paint with sides parallel to the Cartesian coordinate axes, one after another. The color of the $$$i$$$-th rectangle is $$$i$$$ (rectangles are enumerated from $$$1$$$ to $$$n$$$ in the order he draws them). It is possible that new rectangles cover some of the previous ones completely or partially.

Count the number of different colors on the plane after <PERSON><PERSON> draws all the rectangles.

## 输入格式

The first line contains a single integer $$$n$$$ ($$$1 \le n \le 100\,000$$$) — the number of rectangles.

The $$$i$$$-th of the next $$$n$$$ lines contains $$$4$$$ integers $$$x_1$$$, $$$y_1$$$, $$$x_2$$$ and $$$y_2$$$ ($$$-10^9 \le x_1 < x_2 \le 10^9$$$, $$$-10^9 \le y_1 < y_2 \le 10^9$$$) — the coordinates of corners of the $$$i$$$-th rectangle.

## 输出格式

In the single line print the number of different colors in the plane, including color $$$0$$$.

## 样例

### 样例 1

**输入**:
```
5
-1 -1 1 1
-4 0 0 4
0 0 4 4
-4 -4 0 0
0 -4 4 0
```

**输出**:
```
5
```

### 样例 2

**输入**:
```
4
0 0 4 4
-4 -4 0 0
0 -4 4 0
-2 -4 2 4
```

**输出**:
```
5
```

## 备注

That's how the plane looks in the first sample That's how the plane looks in the second sample$$$0$$$ = white, $$$1$$$ = cyan, $$$2$$$ = blue, $$$3$$$ = purple, $$$4$$$ = yellow, $$$5$$$ = red.

## 题解

First let's compress the coordinates. Now all the coordinates are in $$$[0, 2n)$$$.
Now we do scanline on $$$x$$$ coordinate with segment tree on $$$y$$$ coordinate. Let's talk about segment tree structute. In each vertex we store:
• Set of colors which cover the whole segment. If color covers a segment, we don't push it to it childs ($$$colors[v]$$$)
• Maximal visible color in subtree which isn't in the answer ($$$max[v]$$$)
• Minimal visible color in subtree ($$$min[v]$$$)
For the vertex max and min can be calculated as:
• If $$$colors$$$ isn't empty and max value in $$$colors$$$ is more than max in children:   If it's already in the answer or it's less than min in children, $$$max = -1$$$.  Otherwise $$$max = max \ in \ colors$$$
• If it's already in the answer or it's less than min in children, $$$max = -1$$$.
• Otherwise $$$max = max \ in \ colors$$$
• Otherwise $$$max = max \ in \ children$$$
• If $$$colors$$$ isn't empty $$$min = max(max \ in \ colors, min \ in \ children)$$$
• Otherwise $$$min = min \ in \ children$$$
Now in scanline we:
• Add all the segments, starting at this point
• Remove all the segments, ending at this point
• While $$$max[root]$$$ isn't $$$-1$$$ we put it into $$$answer$$$ and recalculate everything by readding this segment to tree.
At the end we know all visible colors and print the number of them.
Asymptotics is $$$O(n \cdot log(n) + n \cdot log^2(n))$$$
Solution

