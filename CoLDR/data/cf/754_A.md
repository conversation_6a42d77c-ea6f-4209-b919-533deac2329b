# Lesha and array splitting

**题目ID**: 754/A  
**比赛**: Codeforces Round 390 (Div. 2)  
**年份**: 2017  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

One spring day on his way to university <PERSON><PERSON> found an array A. <PERSON><PERSON> likes to split arrays into several parts. This time <PERSON><PERSON> decided to split the array A into several, possibly one, new arrays so that the sum of elements in each of the new arrays is not zero. One more condition is that if we place the new arrays one after another they will form the old array A.

<PERSON><PERSON> is tired now so he asked you to split the array. Help <PERSON><PERSON>!

## 输入格式

The first line contains single integer n (1 ≤ n ≤ 100) — the number of elements in the array A.

The next line contains n integers a1, a2, ..., an ( - 103 ≤ ai ≤ 103) — the elements of the array A.

## 输出格式

If it is not possible to split the array A and satisfy all the constraints, print single line containing "NO" (without quotes).

Otherwise in the first line print "YES" (without quotes). In the next line print single integer k — the number of new arrays. In each of the next k lines print two integers li and ri which denote the subarray A[li... ri] of the initial array A being the i-th new array. Integers li, ri should satisfy the following conditions:

- l1 = 1
- rk = n
- ri + 1 = li + 1 for each 1 ≤ i < k.

If there are multiple answers, print any of them.

## 样例

### 样例 1

**输入**:
```
3
1 2 -3
```

**输出**:
```
YES
2
1 2
3 3
```

### 样例 2

**输入**:
```
8
9 -12 3 4 -4 -10 7 3
```

**输出**:
```
YES
2
1 2
3 8
```

### 样例 3

**输入**:
```
1
0
```

**输出**:
```
NO
```

### 样例 4

**输入**:
```
4
1 2 3 -5
```

**输出**:
```
YES
4
1 1
2 2
3 3
4 4
```

## 题解

Only in one case there is no answer. When array consists entirely of zeros. Because all subarrays of such array has zero sum.
Otherwise there are two cases:
Array sum is not zero. In this case we can divide array into one subarray A[1... n].
Array sum is zero. But we know that array has non-zero elements. Then there is exists such prefix A[1... p], which sum is s and s ≠ 0. If sum of array is zero and sum of prefix is s, then sum of remaining suffix is equals to 0 - s =  - s ( - s ≠ 0). Therefore array can be divided into two parts A[1... p] and A[p + 1... n].
Time complexity — O(n).

