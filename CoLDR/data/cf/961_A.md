# Tetris

**题目ID**: 961/A  
**比赛**: Educational Codeforces Round 41 (Rated for Div. 2)  
**年份**: 2018  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

You are given a following process.

There is a platform with $$$n$$$ columns. $$$1 \times 1$$$ squares are appearing one after another in some columns on this platform. If there are no squares in the column, a square will occupy the bottom row. Otherwise a square will appear at the top of the highest square of this column.

When all of the $$$n$$$ columns have at least one square in them, the bottom row is being removed. You will receive $$$1$$$ point for this, and all the squares left will fall down one row.

You task is to calculate the amount of points you will receive.

## 输入格式

The first line of input contain 2 integer numbers $$$n$$$ and $$$m$$$ ($$$1 \le n, m \le 1000$$$) — the length of the platform and the number of the squares.

The next line contain $$$m$$$ integer numbers $$$c_1, c_2, \dots, c_m$$$ ($$$1 \le c_i \le n$$$) — column in which $$$i$$$-th square will appear.

## 输出格式

Print one integer — the amount of points you will receive.

## 样例

### 样例 1

**输入**:
```
3 9
1 1 2 2 2 3 1 2 3
```

**输出**:
```
2
```

## 备注

In the sample case the answer will be equal to $$$2$$$ because after the appearing of $$$6$$$-th square will be removed one row (counts of the squares on the platform will look like $$$[2~ 3~ 1]$$$, and after removing one row will be $$$[1~ 2~ 0]$$$).

After the appearing of $$$9$$$-th square counts will be $$$[2~ 3~ 1]$$$, and after removing one row it will look like $$$[1~ 2~ 0]$$$.

So the answer will be equal to $$$2$$$.

## 题解

The answer will be equal to $$$\min\limits_{i = 1}^{n} cnt_i$$$, where $$$cnt_i$$$ is the number of squares that will appear in the $$$i$$$-th column.

