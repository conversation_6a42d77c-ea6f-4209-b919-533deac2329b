# AND Graph

**题目ID**: 986/C  
**比赛**: Codeforces Round 485 (Div. 1)  
**年份**: 2018  
**时间限制**: 4.0秒  
**内存限制**: 256MB  

## 题目描述

You are given a set of size $$$m$$$ with integer elements between $$$0$$$ and $$$2^{n}-1$$$ inclusive. Let's build an undirected graph on these integers in the following way: connect two integers $$$x$$$ and $$$y$$$ with an edge if and only if $$$x \& y = 0$$$. Here $$$\&$$$ is the bitwise AND operation. Count the number of connected components in that graph.

## 输入格式

In the first line of input there are two integers $$$n$$$ and $$$m$$$ ($$$0 \le n \le 22$$$, $$$1 \le m \le 2^{n}$$$).

In the second line there are $$$m$$$ integers $$$a_1, a_2, \ldots, a_m$$$ ($$$0 \le a_{i} < 2^{n}$$$) — the elements of the set. All $$$a_{i}$$$ are distinct.

## 输出格式

Print the number of connected components.

## 样例

### 样例 1

**输入**:
```
2 3
1 2 3
```

**输出**:
```
2
```

### 样例 2

**输入**:
```
5 5
5 19 10 20 12
```

**输出**:
```
2
```

## 备注

Graph from first sample:

Graph from second sample:

## 题解

Let's build directed graph on $$$m+2^n$$$ vertices. There will be two types of vertices: $$$(x, 1)$$$ is a vertex for $$$x$$$ from input and $$$(x, 2)$$$ is a vertex for all $$$x$$$ between $$$0$$$ and $$$2^{n}-1$$$.
There will be edges of three classes:
• $$$(x, 1) \rightarrow (x, 2)$$$ for all $$$x$$$ from input
• $$$(x, 2) \rightarrow (x | (1 \ll k), 2)$$$ for all $$$x$$$ and $$$k$$$ such that $$$k$$$-th bit of $$$x$$$ is $$$0$$$
• $$$(\sim x, 2) \rightarrow (x, 1)$$$ for all $$$x$$$ from input. Here $$$\sim x = ((2^n-1) - x)$$$ — the complement of $$$x$$$
Let's look at some path of form $$$(a, 1) \rightarrow (x_1, 2) \rightarrow (x_2, 2) \rightarrow \ldots \rightarrow (x_k, 2) \rightarrow (b, 1)$$$. The transition from $$$x_1$$$ to $$$x_k$$$ just change some $$$0$$$ bits to $$$1$$$. So, $$$x_1$$$ is a submask of $$$x_k$$$. $$$a$$$ is $$$x_1$$$ and $$$b$$$ is the complement of $$$x_k$$$. Now it is clear that $$$a \& b = 0$$$. The opposite is also true: whenever $$$a \& b = 0$$$, there is a path from $$$(a, 1)$$$ to $$$(b, 1)$$$.
The solution is simple now: iterate over all $$$x$$$ from input, and if the vertex $$$(x, 1)$$$ is not visited yet, run a DFS from it and increase the answer by $$$1$$$. It is clear that we will run DFS exactly once for each connected component of original graph.
Complexity — $$$O(n2^n)$$$

