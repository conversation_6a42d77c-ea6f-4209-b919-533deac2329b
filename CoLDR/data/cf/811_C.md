# <PERSON><PERSON> and Memorable Trip

**题目ID**: 811/C  
**比赛**: Codeforces Round 416 (Div. 2)  
**年份**: 2017  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON><PERSON> often travels by trains. He remembered some of his trips especially well and I would like to tell you about one of these trips:

<PERSON><PERSON> is at initial train station, and now n people (including <PERSON><PERSON>) want to get on the train. They are already lined up in some order, and for each of them the city code ai is known (the code of the city in which they are going to).

Train chief selects some number of disjoint segments of the original sequence of people (covering entire sequence by segments is not necessary). People who are in the same segment will be in the same train carriage. The segments are selected in such way that if at least one person travels to the city x, then all people who are going to city x should be in the same railway carriage. This means that they can’t belong to different segments. Note, that all people who travel to the city x, either go to it and in the same railway carriage, or do not go anywhere at all.

Comfort of a train trip with people on segment from position l to position r is equal to XOR of all distinct codes of cities for people on the segment from position l to position r. XOR operation also known as exclusive OR.

Total comfort of a train trip is equal to sum of comfort for each segment.

Help <PERSON><PERSON> to know maximal possible total comfort.

## 输入格式

First line contains single integer n (1 ≤ n ≤ 5000) — number of people.

Second line contains n space-separated integers a1, a2, ..., an (0 ≤ ai ≤ 5000), where ai denotes code of the city to which i-th person is going.

## 输出格式

The output should contain a single integer — maximal possible total comfort.

## 样例

### 样例 1

**输入**:
```
6
4 4 2 5 2 3
```

**输出**:
```
14
```

### 样例 2

**输入**:
```
9
5 1 3 1 5 2 4 2 5
```

**输出**:
```
9
```

## 备注

In the first test case best partition into segments is: [4, 4] [2, 5, 2] [3], answer is calculated as follows: 4 + (2 xor 5) + 3 = 4 + 7 + 3 = 14

In the second test case best partition into segments is: 5 1 [3] 1 5 [2, 4, 2] 5, answer calculated as follows: 3 + (2 xor 4) = 3 + 6 = 9.

## 题解

Let's precalc for each x it's frx and lsx — it's leftmost and rightmost occurrences in the array respectively. Now for each range [l, r] we can check, if it can be a separate train carriage, just checking for each ai (l ≤ i ≤ r), that frai and lsai are also in this range.
Now let's define dpi as the answer to the problem for i first people. To update dp we can make two transitions:
• Assume, that there was such train carriage, that finished at position i. Then iterate it's start from right to left, also maintaining maximal ls, minimal fr and xor of distinct codes cur. If current range [j, i] is ok for forming the train carriage, update dpi with value dpj - 1 + cur.
• If there wasn't such train carriage, then last element didn't belong to any train carriage, so we can update dpi with value dpi - 1.
O(n2)

