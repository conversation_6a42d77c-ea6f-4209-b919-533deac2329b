# Ray Tracing

**题目ID**: 724/C  
**比赛**: Intel Code Challenge Final Round (Div. 1 + Div. 2, Combined)  
**年份**: 2016  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

There are k sensors located in the rectangular room of size n × m meters. The i-th sensor is located at point (xi, yi). All sensors are located at distinct points strictly inside the rectangle.

Opposite corners of the room are located at points (0, 0) and (n, m). Walls of the room are parallel to coordinate axes.

At the moment 0, from the point (0, 0) the laser ray is released in the direction of point (1, 1). The ray travels with a speed of $$\sqrt{2}$$ meters per second. Thus, the ray will reach the point (1, 1) in exactly one second after the start.

When the ray meets the wall it's reflected by the rule that the angle of incidence is equal to the angle of reflection. If the ray reaches any of the four corners, it immediately stops.

For each sensor you have to determine the first moment of time when the ray will pass through the point where this sensor is located. If the ray will never pass through this point, print  - 1 for such sensors.

## 输入格式

The first line of the input contains three integers n, m and k (2 ≤ n, m ≤ 100 000, 1 ≤ k ≤ 100 000) — lengths of the room's walls and the number of sensors.

Each of the following k lines contains two integers xi and yi (1 ≤ xi ≤ n - 1, 1 ≤ yi ≤ m - 1) — coordinates of the sensors. It's guaranteed that no two sensors are located at the same point.

## 输出格式

Print k integers. The i-th of them should be equal to the number of seconds when the ray first passes through the point where the i-th sensor is located, or  - 1 if this will never happen.

## 样例

### 样例 1

**输入**:
```
3 3 4
1 1
1 2
2 1
2 2
```

**输出**:
```
1
-1
-1
2
```

### 样例 2

**输入**:
```
3 4 6
1 1
2 1
1 2
2 2
1 3
2 3
```

**输出**:
```
1
-1
-1
2
5
-1
```

### 样例 3

**输入**:
```
7 4 5
1 3
2 2
5 1
5 3
4 3
```

**输出**:
```
13
2
9
5
-1
```

## 备注

In the first sample, the ray will consequently pass through the points (0, 0), (1, 1), (2, 2), (3, 3). Thus, it will stop at the point (3, 3) after 3 seconds.

In the second sample, the ray will consequently pass through the following points: (0, 0), (1, 1), (2, 2), (3, 3), (2, 4), (1, 3), (0, 2), (1, 1), (2, 0), (3, 1), (2, 2), (1, 3), (0, 4). The ray will stop at the point (0, 4) after 12 seconds. It will reflect at the points (3, 3), (2, 4), (0, 2), (2, 0) and (3, 1).

## 题解

Let's simulate the flight of the beam. It can be seen that for any segment between two successive reflections is true that the sum of the coordinates for each point of this segment is constant or the difference of the coordinates is constant.
Thus, we can make lists of sensors for each possible sum and difference of coordinates. Then, when we process the segment of the beam trajectory, we can take all the points from the corresponding list and update result for them.
This solution works fast, because each point is passed by the beam (and therefore viewed by us) not more than two times.

