# Solution for 981_D.md

Generated at: 2025-07-22 06:10:46
Model: deepseek-reasoner

## Reasoning Process

We are given n, k and a list 'a' of n integers (with n, k up to 50, and a_i up to 2^50)
 We must split the sequence of books (in order) into exactly k contiguous non-empty shelves.
 The value of a shelf is the sum of the book prices on that shelf.
 The beauty is the bitwise AND of the k shelf values.

 We wish to maximize the beauty.

 Observation:
 Since the beauty is the AND of the shelf sums, we are essentially looking for a common set of bits in all the shelf sums.

 We can use a bit-by-bit greedy strategy: try to set the high bits first and see if it's possible to have a beauty with that bit and then others.

 How?
 Let X be the beauty we want to achieve. We want the maximum X such that there exists a splitting of the books into k shelves where for every shelf i: 
   (shelf_sum_i) has all the bits of X set? Actually, it's stronger: the AND of all shelf sums must be X.

 Note: The AND of the shelf sums must be at least X. But we can think of: we require that the shelf sums have all the bits of X and possibly more? 
 But the AND will then have at least X. However, we want exactly the bits of X? Actually, we can set the bits independently.

 Alternate approach: 
 We can try to build the answer bit by bit from the highest bit (like bit 50 down to 0). Let ans = 0.
 For bit in range(50, -1, -1):
   candidate = ans | (1 << bit)
   Check if it's possible to split the books into k shelves such that the AND of the shelf sums is at least candidate? 
   But note: we are not just requiring that each shelf has the candidate bits, but also that the AND is at least candidate? Actually, we are building the maximum AND.

 However, note: if we require the AND of the shelf sums to be exactly candidate, that might be too strong. Instead, we can require that the shelf sums all have the bits in candidate? 
 But that only ensures that the AND has at least candidate. However, we are building the candidate by setting bits one by one. But note: we are going from high bit to low, so we are building the maximum.

 Actually, we can use the following: 
   We want to know: is there a splitting into k shelves such that the AND of the shelf sums has at least the bits in candidate? 
   But note: we are building candidate as the set of bits we have set so far. However, we cannot guarantee that the final AND will be exactly candidate because we are going to set more bits? 
   Actually, we are building the candidate as the AND value. We are testing: can we achieve an AND that has the bits of candidate? But we are building the candidate by setting the bits we want.

 However, a better way: we require that in the splitting, every shelf sum has the bits that are set in candidate. That is, for every shelf i, (shelf_sum_i & candidate) == candidate? 
 But note: we don't require that the shelf sum has exactly candidate? Actually, we require that candidate is a subset of the bits of every shelf sum. Then the AND will have at least candidate. 
 But we are trying to maximize the beauty. We are going to set as many bits as possible. 

 But note: if we set the candidate and then check, we are not losing any bit? Actually, if we can achieve candidate, then we can set that bit. But then we try to set the next bit? 
 However, the problem: the candidate we are building is the actual beauty value. Why? Because if we set a bit and then later we set a lower bit, the candidate becomes the actual value.

 However, the requirement for the splitting: we require that each shelf sum has all the bits that are in candidate (the candidate we are testing). 

 But then: if we can have candidate = 110100 (for example) then we set that bit. But then when we try to set the next bit, we require that each shelf sum has the new candidate (which has one more bit set). 

 The algorithm:
   ans = 0
   for bit in range(60, -1, -1):  # since a_i < 2^50, we can go up to 60 to be safe.
        test = ans | (1 << bit)
        # Check if we can split the books into k shelves such that each shelf sum has all the bits in test? 
        # That is, for every shelf, (shelf_sum_i & test) == test.

        How to check?
        We can use dynamic programming: 
            dp[i][j] = whether we can split the first i books into j shelves such that for every shelf (so far) the shelf sum has the bits in test (i.e., the condition holds for the first j shelves)

        But note: the condition must hold for every shelf. So for the current shelf, we must have that the current shelf sum has the bits in test.

        However, we can do:
            Let dp[j] = the set of possible ending positions (or the fact that we can split the prefix ending at a certain position) but actually we don't care about the state beyond the last position.

        Alternatively, we can use a 1D DP: 
            dp[i] = the maximum number of shelves (or the possibility) that we can form from the prefix ending at i with the condition that each shelf sum has the bits in test.

        Actually, we want to know: can we split the entire array into k shelves such that each shelf sum has the bits in test.

        We can do:
            dp[i] = the maximum number of shelves we can form for the prefix ending at i (or we can use a boolean array: dp[i] = True if we can split the prefix ending at i into some shelves (at least one) and each shelf has the condition, but we care about k shelves at the end).

        Alternatively, we can do: 
            Let dp[i] = the number of shelves we can form for the prefix ending at i (we want to maximize the number? Actually, we just want to see if we can form at least k shelves? But note: we must form exactly k shelves for the whole array).

        Actually, we can do:
            dp[i][j] = whether we can split the first i books into j shelves such that each shelf has the condition (shelf sum has the bits in test).

        How to update?
            dp[0][0] = True (0 books, 0 shelves)
            Then for i from 1 to n:
                for j from 1 to min(i, k):   # we are going to form j shelves ending at i
                    # we try every last shelf starting at index p (0<=p<i) such that the shelf from p to i-1 (0-indexed) has sum S and (S & test) == test.
                    # then dp[i][j] = OR over p in [0, i-1] of { dp[p][j-1] is True and the sum from p to i-1 has the bits test }

            Then we check if dp[n][k] is True.

        However, note: the books are in order, so the last shelf is from p to n-1? Actually, in our state: 
            We consider the prefix ending at i (so the books from 0 to i-1). 
            We are iterating i from 0 to n.

        Steps for DP:
            dp = 2D boolean array of size [n+1][k+1]
            dp[0][0] = True

            For i in range(1, n+1):
                for j in range(1, min(i+1, k+1)):   # j from 1 to min(i, k)
                    # we consider the last shelf: which starts at some index p (0<=p<i) and ends at i-1 (so the books from p to i-1)
                    total = 0
                    # iterate p from i-1 down to 0 (or 0 to i-1, but we can do backwards to break early? but n is small)
                    for p in range(i-1, -1, -1):
                        total += a[p]   # because we are going backwards: starting from the end of the segment (i-1) and going to p
                        # if we can form j-1 shelves from the prefix ending at p, and the segment from p to i-1 has sum S such that (S & test) == test, then we can set dp[i][j] to True.
                        if dp[p][j-1]:
                            if (total & test) == test:
                                dp[i][j] = True
                                break   # no need to check more p for this i,j? Actually, we can break inner loop if we found one, but we must check all? Actually, if one works, we can set and break to save time? 
                                        # but we can break the inner p loop for this i,j? It's okay to break since we only need one.
                # Note: we must form at least one book per shelf, so p must be at least j-1? Actually, we are iterating p from 0 to i-1, but the condition j-1 must be at least 0 and we have dp[p][j-1] so p must be at least j-1? 

            Then if dp[n][k] is True, then candidate test is achievable.

        Then we set ans = test.

        But note: we are iterating from high bit to low. So we are building the maximum.

        However, we must note: if we set a bit and then later we cannot set the next bits, that candidate might be the maximum. But by setting the high bits first, we are ensuring that we get the maximum.

        Why is this correct?
            We are testing: can we have a beauty that has the bits we have set so far (including the current bit)? If yes, then we keep the bit; if not, we skip.
            Then we move to the next lower bit.

        Example: 
            Suppose we have candidate = 1000 (binary). We check if we can split so that each shelf sum has the bit 1000. 
            Then we try to set the next bit: candidate = 1100. Check again.

        But note: the condition (total & test) == test requires that the shelf sum has at least the bits in test. It might have more. But that is okay because then the AND will have at least test? 
        However, we are building the candidate as the common bits. The AND might be more than test? But we are testing for a fixed candidate: we are building the candidate as the beauty we want to achieve.

        Actually, the beauty we get at the end might be higher than our candidate? But note: we are testing for candidate. We want to know if there is a splitting that gives at least candidate as the beauty. 
        However, we are building the candidate by setting as many bits as possible. The final candidate will be the maximum beauty.

        But note: we are setting the bits from high to low. So if we can set a bit, we set it and then try to set the next. This is standard in bitmask greedy.

        However, the problem: the candidate we are building is the beauty we are going to output. And we are building the maximum possible.

        But note: the condition we check is: each shelf sum must have the bits in test. Then the AND will have at least the bits in test. But we are building the candidate by setting bits one by one. 
        The final candidate might be less than the actual AND of a particular splitting? However, we are setting the bits greedily and we are building the maximum candidate that is achievable. 
        And the actual AND of the splitting we find might be greater than candidate? Actually, no: because we are only requiring that the shelf sums have the bits in test. The AND might have more bits? 
        But note: we are building the candidate by setting the bits. The candidate we are building is the AND value we are trying to achieve. 

        However, the splitting we get for candidate test might have an AND that is >= test. But we are not forcing the AND to be exactly test. 
        But that is okay: because we are building the candidate by setting the bits. The final candidate we get will be the maximum value such that there exists a splitting with AND at least candidate. 
        But note: if there exists a splitting with AND = X, then for any candidate that is a subset of bits of X, we would have set those bits. However, we are setting bits from the highest and we are building the maximum candidate that is achievable. 

        Actually, the candidate we build is the maximum possible AND. Why?
          Let X be the candidate we build. Then we know that there is a splitting such that the AND is at least X (because every shelf sum has the bits of X). 
          Now, is it possible that the actual AND is greater than X? But note: we set the bits greedily. When we try to set a bit and fail, then that bit cannot be set. 
          However, the actual AND of the splitting we found for X might be more than X? But that doesn't matter because we are building the candidate: we are going to set the next bits? 
          Actually, we are building the candidate by including the bit we are testing only if we can achieve the splitting with candidate that includes that bit. 

        But note: the splitting we found for candidate test might have extra bits. However, when we try to set a lower bit and we can't, then we don't set it. The candidate we build is the maximum common set of bits that we can have.

        However, there is a catch: the splitting we find for candidate test (with the current set of bits) must be such that we can then also set the lower bits? Actually, no: we are building the candidate by setting bits independently. 
        The algorithm is: 
            ans = 0
            for bit from high to low:
                candidate = ans | (1 << bit)
                if (check(candidate)):   # check if we can split the books into k shelves such that each shelf sum has the candidate bits.
                    ans = candidate

        Then we output ans.

        Why is this the maximum AND? 
            We are building the candidate bit by bit. The candidate is the AND value we are going to achieve. 
            The check condition: we require that each shelf sum has the candidate bits. Then the AND will have candidate. 
            But note: the AND might have more bits? Actually, no: because if we cannot set a higher bit, then that bit is not in candidate. And we are setting the bits from high to low. 

        However, the check condition does not require that the shelf sums have exactly the candidate bits. They can have extra bits. But that is okay because the AND of the shelf sums will be at least candidate. 
        But we are building the candidate as the set of bits that we know are common. The actual AND in the splitting we found for candidate test might be higher than candidate? 
        But then why can't we set the next bit? 

        The catch: the splitting we use for candidate test might change when we add more bits. 
        However, note: if we set a bit, we require that in the splitting, each shelf sum has that bit. Then when we check the next lower bit, we are using the same constraint: each shelf sum must have the candidate (which now includes the previous high bit and the current bit). 

        Therefore, the splitting we use for the next bit must have both bits set in every shelf sum. 

        The algorithm is correct because we are building the candidate by including a bit if and only if there exists a splitting that satisfies the condition that every shelf sum has all the bits of the candidate (so far). 

        Therefore, at the end, the candidate we have is the maximum beauty.

        But note: the problem constraints (n, k up to 50) and the numbers are large (50 bits) but we are iterating over 60 bits and then doing a DP that is O(n^2 * k). 
        The DP: 
            We iterate over bits: 60
            For each bit, we do:
                for i in range(1, n+1):   # n=50 -> 50
                for j in range(1, min(i, k)+1):  # j from 1 to k (k<=50) and i from 1 to 50 -> 50*50 = 2500
                    then we iterate p from i-1 down to 0: worst-case 50 per (i,j) -> 2500*50 = 125000 per bit.

            Then 60 * 125000 = 7.5e6, which is acceptable in Python? Actually, worst-case 7.5e6 iterations is acceptable in Pyton if optimized in PyPy or in C++ but in Python we must be cautious.

        But note: the inner loop we are also summing the segment from p to i-1. We can precompute prefix sums to avoid the inner summation? 

        How to avoid recalculating the segment sum each time?
            We can precompute prefix sums: 
                Let P[0] = 0, P[i] = a0 + a1 + ... + a{i-1}

            Then the sum from p to i-1 is P[i] - P[p].

            However, in our inner loop for p in the DP, we are iterating p from i-1 down to 0. We could use the prefix sum array to get the segment sum in O(1).

        But note: in the current code we are doing:
            total = 0
            for p from i-1 down to 0:
                total += a[p]

            This is O(i) per (i,j). But we can replace that by:
                total = P[i] - P[p]

            But then we are not accumulating? Actually, we can precompute the prefix sums once for the entire array.

        However, we are in the inner loop of the candidate check. We can precompute the prefix sums once at the beginning of the entire function.

        Revised DP for a fixed candidate test:
            Precomputation: 
                P[0] = 0
                for i in range(1, n+1):
                    P[i] = P[i-1] + a[i-1]

            Then:
                dp = 2D boolean array of size (n+1) x (k+1), initialized to False.
                dp[0][0] = True

                for i in range(1, n+1):
                    for j in range(1, min(i+1, k+1)):   # j from 1 to min(i, k)
                        for p in range(j-1, i):   # p from j-1 to i-1: because we need at least one book in each of the previous j-1 shelves and the last shelf from p to i-1 has at least one book (so p>=j-1 and p<=i-1). 
                            # But note: our dp state: we require that the prefix ending at p is split into j-1 shelves -> so we need p>=j-1. 
                            # The segment from p to i-1 has length i-p (at least 1) and we are using p as the start index (0-indexed) of the last shelf? Actually, we are considering the books from index p to i-1.

                            # The sum from p to i-1 is P[i] - P[p]
                            s = P[i] - P[p]
                            if (s & test) == test and dp[p][j-1]:
                                dp[i][j] = True
                                break   # break the inner p loop for this i,j? because we found one valid p.

                return dp[n][k]

        However, note: we break the inner loop as soon as we find one valid p? That is acceptable.

        But note: we are iterating p from j-1 to i-1. We can break early if we found one.

        Alternatively, we can iterate p from i-1 down to j-1? Then we break as soon as we find one.

        But the complexity is the same.

        Also note: we must be cautious: the condition (s & test) == test.

        Let's test with the examples.

        Example 1: n=10, k=4, a = [9,14,28,1,7,13,15,29,2,31]
        We know the answer is 24.

        How would we build 24?
          24 in binary: 11000 (16+8=24)

        How do we check candidate=24?
          We need to split into 4 shelves and each shelf sum must have the bits 24? 

          The example split:
            Shelf1: 9+14+28+1+7 = 59 -> 59 in binary: ...00111011 -> does 59 have the bits 24? 
              24 is 11000, 59 is 111011 -> has the bits 11000? 
              Actually: 59 & 24 = 16? because 59:  111011
                                    24:  011000
                                    AND: 010000 -> 16, which is not 24.

          What's wrong?

          Correction: the example says: 
              (9+14+28+1+7) = 59
              (13+15)=28
              (29+2)=31
              (31)=31
          Then 59 & 28 & 31 & 31 = 24.

          But note: we require that every shelf sum has the bits 24? 
          59 has the bits: ...00111011 -> does it have the bits 24? 24 is 11000 -> 16+8=24. 
          The bit 16 (2^4) is set? 59: 32+16+8+2+1 -> 32 is 2^5, 16 is 2^4 -> so it has 16? but 8 is also set? 
          Actually, 59 has the bits 16 and 8? Then 16+8=24? So 59 has the bits 24? 
          But 59 & 24 = 24? 
          Let me check: 59: 0b111011 -> 32+16+8+2+1 = 59, and 24: 0b11000 -> 16+8=24.
          So 59 has both the 16 and 8 bits? Then 59 & 24 = 24. Similarly, 28: 0b11100 -> 16+8+4? -> 16 and 8 are set? so 28 & 24 = 24? 
          Actually, 28: 16+8+4 -> so yes, 16 and 8 are set -> 28 & 24 = 24.
          31: 16+8+4+2+1 -> 16 and 8 are set -> 31 & 24 = 24.

          So each shelf sum has the bits 24.

        Therefore, condition holds.

        Example 2: 
          candidate=64 (binary: 1000000) 
          Split: 
            Shelf1: 3+14+15+92 = 124 -> 124 in binary: 1111100 -> does it have 64? 64 is 1000000 -> so 124 has the 64 bit? 
              124 < 64? -> 64 is 64, 124 is greater than 64? 
              But 124: 64+32+16+8+4 -> so yes, the 64 bit is set? -> 124 & 64 = 64 -> condition holds.
            Shelf2: 65 -> 65: 64+1 -> so 65 & 64 = 64 -> condition holds.
            Shelf3: 35+89=124 -> same as above -> condition holds.

        Therefore, candidate=64 is achievable.

        Now, what if we try to set a bit that is not common? Then the condition fails.

        Implementation:

        Steps:
            Precompute prefix sums: P[0..n]
            ans = 0
            # We go from high bit to low bit. The highest bit we need to consider: since a_i < 2^50, the total sum is at most 50 * 2^50, so we can consider bits from 0 to about 55? 
            # Let's set the bit range: from 0 to 60.

            for bit in range(60, -1, -1):
                test = ans | (1 << bit)
                # Initialize dp: a 2D list of (n+1) rows and (k+1) columns, all False.
                dp = [[False] * (k+1) for _ in range(n+1)]
                dp[0][0] = True

                # Now, for each i from 1 to n:
                for i in range(1, n+1):
                    # j from 1 to min(i, k)
                    for j in range(1, min(i+1, k+1)):
                        # We consider p from j-1 to i-1: because we need at least j-1 books in the previous j-1 shelves? Actually, the last shelf must start at p (with p>=j-1) and go to i-1.
                        # But note: the prefix ending at p must be split into j-1 shelves -> so p must be at least j-1 (because each shelf has at least one book). 
                        # We iterate p from j-1 to i-1 (inclusive). But note: the segment from p to i-1: we require at least one book -> p can be from 0 to i-1? but we require j-1 shelves for the prefix ending at p -> so p must be at least j-1.
                        found = False
                        # We traverse p from i-1 down to j-1: so that we can break early if we find one.
                        # Note: the segment sum = P[i] - P[p]
                        # We want: (P[i]-P[p]) & test == test
                        for p in range(i-1, j-2, -1):   # p from i-1 down to j-1 (inclusive) -> j-1 is the smallest index we consider for the start of the last shelf.
                            # But note: if we go backwards, we break early if we find one.
                            s = P[i] - P[p]
                            # Check condition for the last shelf: 
                            if (s & test) == test:
                                # Then we check if the prefix ending at p has j-1 shelves
                                if dp[p][j-1]:
                                    dp[i][j] = True
                                    break   # break the inner p loop for this i,j
                        # If we didn't break, then dp[i][j] remains False.

                if dp[n][k]:
                    ans = test

            return ans

        But note: we must form exactly k shelves. 

        Let me test with a small example: 
            n=3, k=2, a = [1,2,3]
            We want to split: [1,2 | 3] -> [3,3] -> AND=3? or [1 | 2,3] -> [1,5] -> AND=1? or [1,2,3] not allowed because we need 2 shelves.

            Possibilities: 
                Option1: [1,2] and [3]: shelf1=3, shelf2=3 -> AND=3.
                Option2: [1] and [2,3]: shelf1=1, shelf2=5 -> AND=1.

            Maximum beauty=3.

            How does the algorithm work?
            Precompute P: [0,1,3,6]

            We start with ans=0.

            Consider high bits: 
                bit=1 (say we go from 2 bits: but we go to 60, so we start at 60, but the numbers are small, so we can simulate from 2 down to 0).

            Let's do bit=2 (which is 4) -> test = 0 | 4 = 4.
            Check: 
                dp[0][0]=True
                i=1: j from 1 to min(1,2)=1 -> 
                    p from 0 to 0: 
                        s = P[1]-P[0]=1 -> 1 & 4 = 0 !=4 -> skip.
                    so dp[1][1]=False.

                i=2: j=1: 
                    p from 1 to 1? 
                        s = P[2]-P[1]=3-1=2 -> 2&4=0 -> skip.
                    j=2: 
                        p from 1 to 0? but j-1=1 -> p must be at least 1 -> p=1: 
                        s = P[2]-P[1]=2 -> 2&4=0 -> skip.

                i=3: 
                    j=1: 
                        p from 2 to 2: s= P[3]-P[2]=6-3=3 -> 3&4=0 -> skip.
                    j=2: 
                        p from 2 to 1: 
                            p=2: s= P[3]-P[2]=3 -> 3&4=0 -> skip.
                            p=1: s= P[3]-P[1]=5 -> 5&4=4 -> condition holds. Then check dp[1][1]: which is False? 
                        then j=3? no, k=2.

                So dp[3][2] = False -> candidate 4 fails.

            Then we try bit=1 (which is 2): test = 0 | 2 = 2.
            Check:
                i=1: j=1: p=0: s=1 -> 1&2=0 -> skip -> dp[1][1]=False.
                i=2: j=1: p from 1 to 1: s=2 -> 2&2=2 -> condition holds, then check dp[1][0]? but j-1=0 -> dp[1][0] is not defined? Actually, we have dp[0][0]=True, then for j=1 we check p from 0 to 1? 
                    Actually, we require j-1=0, so p>=0. 
                    p=0: s= P[2]-P[0]=3 -> 3&2=2 -> condition holds, and dp[0][0]=True -> then dp[2][1]=True.

                i=2: j=2: 
                    p from 1 to 1: s= P[2]-P[1]=2 -> 2&2=2 -> condition holds, then check dp[1][1] -> which is False? -> skip.

                i=3: 
                    j=1: 
                        p from 2 to 2: s=3 -> 3&2=2 -> condition holds, and we check dp[2][0]? but we don't have that? Actually, we need j-1=0 -> then p=0: 
                        Actually, for j=1: we require j-1=0 -> so p from 0 to 2? 
                        We iterate p from 2 down to 0: 
                            p=2: s=3 -> condition holds and check dp[2][0]? which is False? 
                            p=1: s=5 -> 5&2=0? -> skip? because 5 has the bit for 2? 5 in binary: 101 -> the second bit is 0? -> wait, 5 has the bit 4 and 1 -> the bit for 2 is not set? 
                                Actually, 5: 101 -> the second bit (bit1: 2^1) is 0? -> so 5 & 2 = 0 -> not 2.
                            p=0: s=6 -> 6 in binary: 110 -> 6&2= 2 -> condition holds, and dp[0][0]=True -> so dp[3][1]=True.

                    j=2: 
                        p from 2 to 1: 
                            p=2: s=3 -> 3&2=2 -> condition holds, check dp[2][1] -> which we set as True (from i=2, j=1) -> so dp[3][2]=True.

                    j=3: not applicable.

                Then candidate 2 is achievable. Then we set ans=2.

            Then bit=0: test = 2 | 1 = 3.
            Now we check candidate=3.

            i=1: j=1: p=0: s=1 -> 1&3=1 !=3 -> skip -> dp[1][1]=False.
            i=2: j=1: 
                p from 0 to 1: 
                    p=0: s=3 -> 3&3=3 -> condition holds, and dp[0][0]=True -> dp[2][1]=True.
            i=2: j=2: 
                p from 1 to 1: 
                    s=2 -> 2&3=2 !=3 -> skip -> dp[2][2]=False.
            i=3: 
                j=1: 
                    p from 0 to 2: 
                        p=0: s=6 -> 6&3=2? -> 6: 110, 3: 011 -> AND=010=2 -> not 3 -> skip.
                        p=1: s=5 -> 5:101, 3:011 -> AND=001 -> skip.
                        p=2: s=3 -> 3&3=3 -> condition holds, and dp[2][0]? -> but we don't have dp[2][0]? Actually, j-1=0 -> we need dp[2][0]? 
                        But we did set dp[0][0]=True, but for p=2 we need dp[2][0] -> which we did not set? 
                    So no for j=1? -> wait, we did set dp[2][1]=True? but for j=1 we are looking for j-1=0 -> so we need dp[?][0] at position 2? 
                    Actually, we set dp[2][1]=True, but we did not set dp[2][0]? and we don't use j=0 for j=1? 
                    Correction: for j=1, we require that the prefix ending at p (which we are taking as the entire prefix for the first j-1=0 shelves) is set at dp[p][0]. 
                    We have dp[0][0]=True, but if we take p=0, then the segment from 0 to 2? -> that would be the entire prefix? But we are at i=3: 
                    Actually, for j=1: we are putting the entire prefix in one shelf. But we are iterating p from 0 to 2: 
                        p=0: we already did: s=6 -> fails.
                        p=1: fails.
                        p=2: we have s=3 -> and we check dp[2][0]? 
                    But we never set dp[2][0]. We set dp[0][0]=True and then for i=2, we set dp[2][1]=True. 
                    We did not set dp[2][0]? How do we set dp[2][0]? 
                    We set dp[i][0] only for i=0? 

                    Actually, we initialize dp[0][0]=True, and for any i>=1, we do j from 1 to ... so we never set dp[i][0] for i>=1. 
                    That is correct: we cannot split a non-empty prefix into 0 shelves? 

                    Therefore, for j=1, we require that the entire prefix is taken as one shelf. Then we must have p=0? because the segment from p to i-1 (which is 0 to 2) is the entire array? 
                    But we tried p=0: s=6 -> fails.

                    So j=1 fails.

                j=2: 
                    p from 1 to 2: 
                        p=2: s=3 -> condition: 3&3==3 -> holds, then check dp[2][1] -> which is True -> so dp[3][2]=True.

                j=3: skip.

            Then candidate=3 is achievable -> ans=3.

            But wait, we know the maximum beauty is 3? 
            Actually, we have two ways to get 3: 
                Option1: [1,2] and [3] -> AND = 3 & 3 = 3.
                Option2: [1] and [2,3] -> AND = 1 & 5 = 1 -> not 3.

            So candidate=3 is achievable.

            Therefore, the algorithm returns 3.

        But note: we started with 0, then set bit1 (2) and then set bit0 (1) to get 3.

        However, we did not check the possibility of having candidate=0? Then we set bits from high to low. The high bit we set was 2 (which is 2) and then 1 (which is 1) to get 3.

        It works.

        Let me run the example with the known answer 3.

        But note: the example in the problem: 
            Example1: answer=24 -> we must get 24.

        How about we simulate the first candidate that we set: the highest bit? 
            The highest bit that might be set: 32? 
            candidate = 32: 
                We need every shelf sum to have the 32 bit? 
                The total sum is 9+14+28+1+7+13+15+29+2+31 = 149 -> which is less than 32? -> no, 149 is about 128+16+4+1 -> so 128 is the highest bit.

            But 32 is 2^5 -> 32, then 64, then 128.

            We start from bit=7 (128) down to 0? Actually, we go to 60.

        We'll trust the algorithm.

        However, we note: the condition (s & test)==test must be checked. The segment sums must be at least test? Not necessarily, but they must have the bits in test.

        For example, if test=24 (binary 11000) and a segment sum is 24, then 24&24=24 -> valid. 
        If a segment sum is 56 (111000), then 56&24=24 -> valid.

        So the condition is correct.

        One more edge: k=1. Then we have one shelf: the entire array. Then the beauty is the entire sum. Then we are building the entire sum? 
        But we are building the maximum AND? which is the entire sum? 
        But the entire sum is fixed. The algorithm: 
            We start with ans=0.
            Then for each bit from high to low: 
                test = ans | (1<<bit)
                Check: we need one shelf: the entire array. Condition: (total_sum & test) == test.
                Then we set ans = test.

            This will build the entire sum? 
            Actually, no: because we are setting the bits that are present in the total sum? 
            For example: total_sum = 5 (101), then we set bit2: 4 -> 5&4=4 -> condition holds -> ans=4.
            Then bit1: 4|2=6 -> 5&6=4 !=6 -> skip.
            Then bit0: 4|1=5 -> 5&5=5 -> condition holds -> ans=5.

            So we get 5.

        Therefore, it works.

        Implementation:

        We'll precompute the prefix sum: P[0..n]

        We iterate bit from 60 down to 0.

        We'll use a 2D dp array of size (n+1) x (k+1). We initialize dp[0][0]=True at the beginning of the candidate check.

        Then we iterate i from 1 to n, and j from 1 to min(i, k) (but we can do j from 1 to k and i from j to n? because we need at least j books for j shelves).

        Actually, we can optimize the loops: 
            for i in range(1, n+1):
                # j from 1 to min(i, k) -> but we can also note that j must be at least 1 and at most k, and i>=j.

        We'll do:

            for j in range(1, k+1):
                for i in range(j, n+1):   # i must be at least j (each shelf at least one book)
                    ... 

        But then we lose the ability to break the inner p loop per i? 

        Alternatively, we can do:

            dp = [False]*(k+1)   # we can do a 1D dp? 
            But note: we need the state for the prefix ending at i and j shelves.

        We'll stick to 2D: 
            dp[i][j] = whether we can split the prefix ending at i (i books) into j shelves satisfying the condition.

        Then we iterate i from 1 to n, and for each i, we iterate j from 1 to min(i, k).

        We break the inner p loop as soon as we find one.

        We'll code accordingly.

        Let's code accordingly.

        Note: We must be careful with the indices for the books: a[0] is the first book.

        Precomputation of prefix sums: 
            P[0] = 0
            for i in range(1, n+1):
                P[i] = P[i-1] + a[i-1]

        Then in the DP: 
            for i in range(1, n+1):
                # j from 1 to min(i, k)
                for j in range(1, min(i, k)+1):   # j from 1 to min(i,k)
                    found = False
                    # p: the start index of the last shelf: from j-1 to i-1 (inclusive). 
                    # We traverse from p = i-1 down to j-1 (so that we can break early if we find one).
                    for p in range(i-1, j-2, -1):
                        # p is the start index (the last shelf starts at p and ends at i-1, so the segment is a[p] to a[i-1])
                        s = P[i] - P[p]
                        if (s & test) == test:
                            if dp[p][j-1]:
                                dp[i][j] = True
                                break
                    # if we did not break, then dp[i][j] remains False (which is the initial value)

            # Then we check dp[n][k]

        But note: we must initialize the dp array for each candidate test.

        We'll write the code accordingly.

        Given n, k <= 50, and 60 bits, and the inner loop is about O(n) per state, the total operations per candidate is O(n^2 * k) = 50*50*50 = 125000 per candidate, and 60 * 125000 = 7500000, which is acceptable in Pyton? 
        We should test in Pyton: 7.5e6 iterations might be acceptable in Pyton in a tight loop? 

        However, we have the inner loop that does a subtraction and a bitwise and. That is cheap.

        But worst-case 7.5e6 iterations in Pyton might be acceptable for n=50? 

        Let me run locally: worst-case 60 * 50 * 50 * 50 = 7.5e6, which is acceptable in Pyton (in C++ it's trivial, in Pyton we hope it runs in a few seconds).

        We'll code accordingly.

        Edge: k=0? but k>=1.

        Another edge: n=k? then each shelf has one book. Then the beauty is the AND of the book prices. Then we can compute that directly? 
        But the algorithm: 
            We are building the candidate: the AND of the book prices? 
            But the condition: we require that each book (as a shelf) has the candidate bits? 
            Then the candidate must be a subset of the bits of every book. The maximum candidate is the AND of all books.

        The algorithm: 
            ans = 0
            for bit from high to low:
                candidate = ans | (1<<bit)
                Condition: for each book, (a_i & candidate)==candidate? 
                But note: we are splitting each book as a shelf? 
                Then the condition is: for each i, (a_i & candidate)==candidate -> which means candidate must be a subset of a_i.

            Then we set ans = candidate.

            The final ans is the AND of all books? 
            Actually, the AND of all books is the maximum candidate? 
            But note: if the AND of all books is X, then we can set X. But we are building the maximum candidate that is a subset of every book. 
            That maximum candidate is the AND of all books? 
            Actually, no: the AND of all books is the common bits. We are building the common bits? 

            Example: books [3,5]: 
                AND=1, but the algorithm: 
                    bit1: candidate=2 -> 3&2=2 -> 2==2? -> fails? because 3 has the bit for 2? -> 3: 11 -> has the bit 2? -> 3&2=2 -> condition holds? 
                    Then we set candidate=2? 
                    Then bit0: candidate=2|1=3 -> 3&3=3 -> holds for 3? but 5: 5&3=1 !=3 -> fails. 
                Then ans=2.

            But the AND of the two books is 1? 

            What is the beauty? 
                The beauty is the AND of the two shelves: shelf1=3, shelf2=5 -> 3 & 5 = 1.

            Why did we get 2? 
            Because the condition we checked: for each book, (a_i & candidate)==candidate? 
                For candidate=2: 
                    book0: 3 & 2 = 2 -> condition holds.
                    book1: 5 & 2 = 0 -> condition fails.

            So candidate=2 fails.

            Then we try candidate=0? then set bit0: candidate=1 -> 
                book0: 3&1=1 -> holds.
                book1: 5&1=1 -> holds.

            Then we set ans=1.

            Then we output 1.

            So it works.

        Therefore, the algorithm is correct.

        We'll code accordingly.

        Let's write the code.

        Note: We must initialize the dp array for each candidate test.

        We'll do:

            def solve(n, k, a):
                # Precompute prefix sum P
                P = [0]*(n+1)
                for i in range(1, n+1):
                    P[i] = P[i-1] + a[i-1]

                ans = 0
                # Iterate bits from 60 down to 0
                for bit in range(60, -1, -1):
                    test = ans | (1 << bit)
                    # dp[i][j]: can we split the first i books into j shelves such that each shelf has the bits in test?
                    dp = [[False] * (k+1) for _ in range(n+1)]
                    dp[0][0] = True   # 0 books, 0 shelves -> valid

                    # Iterate i from 1 to n (number of books processed)
                    for i in range(1, n+1):
                        # j from 1 to min(i, k): because we can have at most i shelves (but we require at least one book per shelf) and at most k shelves.
                        # Actually, j from 1 to min(i, k)
                        for j in range(1, min(i, k)+1):
                            # Consider the last shelf: from p to i-1, where p from j-1 to i-1 (because the prefix ending at p must be split into j-1 shelves, so p must be at least j-1, and the last shelf must have at least one book so p <= i-1)
                            found = False
                            # We iterate p from i-1 down to j-1 (inclusive)
                            for p in range(i-1, j-2, -1):
                                # p is the start index of the last shelf? Actually, the segment is from p to i-1 (0-indexed: books at indices p, p+1, ..., i-1)
                                s = P[i] - P[p]
                                if (s & test) == test:
                                    if dp[p][j-1]:
                                        dp[i][j] = True
                                        break   # break the inner p loop for this i,j
                            # If we don't break, then dp[i][j] remains False (which is the initial value)

                    # Now, we also need to consider the case when we have more than k shelves? 
                    # But we are only interested in j=k at the end: i=n, j=k.
                    if dp[n][k]:
                        ans = test

                return ans

        But note: we have to form exactly k shelves. We check dp[n][k].

        However, what if k==0? but k>=1.

        Also note: we are iterating j from 1 to min(i, k). For i < k, we skip? But we are iterating i from 1 to n, and when i < k, then min(i,k)=i, so j from 1 to i. Then we don't set j=k until i>=k.

        We must also note: when i < j, we skip the inner j loop? because min(i,k) = i < j? Actually, j starts at 1 and goes to min(i,k) which is i, but if i < k, then we don't get j=k. That is correct.

        We'll run the provided examples.

        Example1: n=10, k=4, a=[9,14,28,1,7,13,15,29,2,31] -> output 24.

        Example2: n=7, k=3, a=[3,14,15,92,65,35,89] -> output 64.

        We'll run the examples in the code.

        However, we note: the inner loop for p: we are iterating from i-1 down to j-1. We break early if we find one.

        We'll test with the small example: [1,2,3] with k=2 -> we expect 3.

        Let me run the small example by hand for candidate=3 (which we already did above) and it worked.

        We'll code accordingly.

        But note: the initialization of dp: we set dp[0][0]=True. Then for i>=1, we set dp[i][0]=False? which is the default.

        We'll code and run the examples.

        Due to the constraints, we can run locally.

        However, we'll provide the code.

        One more note: the segment sum s = P[i]-P[p] might be very large? but we are doing bitwise operations which are fast.

        Let's code accordingly.

        We'll write the solution in the given function.

        Note: the function signature: 
            def solve(self, n: int, k: int, a: List[int]) -> int:

        We'll use the above algorithm.

        Let's code.

## Final Solution

### Approach
The problem involves splitting a sequence of books into `k` contiguous non-empty shelves such that the bitwise AND of the sums of the books on each shelf is maximized. The solution leverages a greedy bit manipulation approach combined with dynamic programming to efficiently determine the maximum possible beauty.

1. **Problem Analysis**: 
   - We need to split `n` books into exactly `k` contiguous shelves, with each shelf containing at least one book.
   - The beauty of the shelves is defined as the bitwise AND of the sums of the books on each shelf.
   - The goal is to maximize this beauty value.

2. **Key Insight**:
   - The solution involves checking each bit position from the highest to the lowest to determine if setting that bit in the beauty value is feasible.
   - For each candidate value (built by setting the current bit and all previously set bits), we use dynamic programming to verify if there's a valid shelf partitioning where each shelf's sum includes all the bits of the candidate value.

3. **Dynamic Programming Setup**:
   - Precompute prefix sums to efficiently calculate the sum of any contiguous segment of books.
   - Initialize a DP table where `dp[i][j]` indicates whether the first `i` books can be split into `j` shelves such that each shelf's sum includes all bits of the current candidate value.

4. **Feasibility Check**:
   - For each candidate value, iterate through possible segmentations of the books into `k` shelves.
   - For each segment, check if the segment sum includes all bits of the candidate value and update the DP table accordingly.
   - If the entire sequence of books can be split into `k` shelves satisfying the condition, retain the candidate value; otherwise, discard it.

5. **Complexity Analysis**:
   - **Time Complexity**: O(60 * n^2 * k), where 60 is the number of bits considered (since each book's price is up to 2^50), `n` is the number of books (up to 50), and `k` is the number of shelves (up to 50). This is feasible given the constraints.
   - **Space Complexity**: O(n * k) for the DP table, which is efficient for the problem constraints.

### Solution Code
```python
from typing import List

class Solution:
    def solve(self, n: int, k: int, a: List[int]) -> int:
        if k == 0:
            return 0
        
        P = [0] * (n + 1)
        for i in range(1, n + 1):
            P[i] = P[i - 1] + a[i - 1]
        
        ans = 0
        for bit in range(60, -1, -1):
            test = ans | (1 << bit)
            dp = [[False] * (k + 1) for _ in range(n + 1)]
            dp[0][0] = True
            
            for i in range(1, n + 1):
                for j in range(1, min(i, k) + 1):
                    found = False
                    for p in range(i - 1, j - 2, -1):
                        shelf_sum = P[i] - P[p]
                        if (shelf_sum & test) == test:
                            if dp[p][j - 1]:
                                dp[i][j] = True
                                break
            
            if dp[n][k]:
                ans = test
        
        return ans
```

### Explanation
1. **Prefix Sum Calculation**: The prefix sum array `P` is computed to allow efficient sum queries over any contiguous segment of books.
2. **Bitwise Greedy Approach**: Starting from the highest bit (60th bit) down to the 0th bit, the algorithm checks if setting the current bit in the candidate beauty value is feasible.
3. **Dynamic Programming Check**: For each candidate value, a DP table is initialized where `dp[i][j]` tracks if the first `i` books can be split into `j` shelves such that each shelf's sum includes all bits of the candidate. The DP table is updated by checking all possible segmentations where the last shelf's sum meets the bitwise condition.
4. **Result Construction**: If the entire sequence of books can be split into `k` shelves satisfying the condition for the current candidate, the candidate value is retained. The final result is the maximum feasible candidate value after processing all bits.

This approach efficiently combines bit manipulation and dynamic programming to maximize the beauty of the shelves while adhering to the problem constraints.