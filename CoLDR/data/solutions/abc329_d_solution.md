# Solution for abc329_d.md

Generated at: 2025-07-22 06:39:51
Model: deepseek-reasoner

## Reasoning Process

Alright, I'm going to tackle this election winner problem. Let me start by making sure I understand what's required.

We have N candidates numbered from 1 to N, and M votes. Each vote is for one candidate. We need to process the votes one by one and, after each vote, determine the current winner. The winner is the candidate with the most votes at that point. If there's a tie, the candidate with the smallest number wins.

For example, in the first input: 3 candidates, 7 votes [1,2,2,3,1,3,3]. After each vote, the winner changes as shown in the output: 1,1,2,2,1,1,3.

Constraints are that both N and M can be up to 200,000. So, a brute-force solution that recalculates the winner from scratch after each vote by scanning all candidates would be O(M*N), which is 40e9 operations in the worst case—way too slow. I need an efficient solution.

Let me think about how to approach this. The key is that after each vote, only one candidate's vote count changes. So, instead of recalculating everything, we should be able to update the winner based on the current state and the new vote.

Idea: maintain an array (or dictionary) `votes` of size N+1 (1-indexed) to track current vote counts for each candidate. Also, maintain the current winner. After each new vote, update the count for that candidate and then check if this update could change the winner.

But how to update the winner efficiently? After updating a candidate's vote count, compare it with the current winner. There are a few cases:

1. If the updated candidate has more votes than the current winner, then they become the new winner.
2. If they have the same number of votes, then we pick the candidate with the smaller number. So, if the updated candidate has the same votes as the current winner and has a smaller candidate number, they become the new winner.
3. If the updated candidate is the current winner and they now have more votes, the winner remains the same.
4. If the updated candidate is not the current winner and has fewer votes, the current winner remains.

Wait, but actually, the current winner might be overtaken only by the candidate who just got a vote. However, what if the current winner was tied with another candidate? Actually, in the update, only one candidate's count increases. So the only candidate that could surpass the current winner is the one that just got the vote.

But consider: after updating candidate X, if X has more votes than the current winner, then X becomes the winner. If X has the same votes as the current winner, then we need to compare candidate numbers. If X has a smaller number than the current winner, then X becomes the winner. Otherwise, the current winner remains.

But is that sufficient? Let me test with the example.

Example 1: 
Votes: [1,2,2,3,1,3,3]

Initialize: votes = [0]*4 (index 0 unused), current_winner = 0, but actually we have no votes initially. We start with the first vote.

After vote1: A1=1. 
votes[1]=1, others 0. So current winner is 1. Output 1.

After vote2: A2=2.
votes[2]=1, votes[1]=1. Now, we update candidate 2. Current winner is 1. Candidate 2 has 1 vote, same as current winner. But candidate 2 (number 2) is greater than current winner 1, so we don't change? But the expected output is 1. So that's correct. Winner remains 1.

After vote3: A3=2.
votes[2]=2, votes[1]=1. Now, candidate 2 has 2 votes, which is more than current winner (1 vote). So set winner to 2. Output 2. Correct.

After vote4: A4=3.
votes[3]=1, votes[2]=2, votes[1]=1. Update candidate 3: now has 1 vote. Current winner is 2 with 2 votes. 1 < 2, so winner remains 2. Correct.

After vote5: A5=1.
votes[1]=2, votes[2]=2, votes[3]=1. Now, candidate 1 has 2 votes. Current winner is 2 (with 2 votes). Now, candidate 1 has the same votes as winner, but candidate 1 has a smaller number (1<2), so we set winner to 1. Output 1. Correct.

After vote6: A6=3.
votes[3]=2, votes[1]=2, votes[2]=2. Now, candidate 3 gets updated to 2. Current winner is 1 (with 2 votes). Candidate 3 has 2 votes, but 3>1, so the winner should remain 1? But the expected output is 1. Correct.

After vote7: A7=3.
votes[3]=3, others=2. Candidate 3 now has 3 votes, which is more than current winner (1) who has 2. So set winner to 3. Correct.

So this logic seems to work for the first example.

But what about the second example?
Input: "100 5\n100 90 80 70 60"
After first vote: candidate 100 has 1 vote -> winner 100.
After second: candidate 90 has 1 vote, current winner 100. Then, when we update candidate 90, it has same votes as 100 but 90<100? So would we set winner to 90? But the expected output is 90. Actually, the output is 100,90,80,70,60. So after each vote, the winner is the candidate that just got the vote because they have 1 vote which is more than the others? Actually, no: after the second vote, candidate 100 has 1 and candidate 90 has 1. Since 90<100, but 90 is candidate number 90 and 100 is 100? So 90 is less than 100? Then the winner should be 90 because 90 < 100? But the problem says: if tie, the candidate with the smallest candidate number wins. So 90 is smaller than 100? Then the winner should be 90. But the output is 90 on the second line. Yes.

Similarly, after the third vote: candidate 80 has 1, and current winner is 90 (with 1 vote). Now, 80<90, so we set winner to 80. Correct.

But wait: in the output, the first line is 100, second is 90, third is 80, etc. So that matches.

Now, the third example: 
"9 8
8 8 2 2 8 8 2 2"

After vote1: candidate 8 has 1 -> winner 8.
After vote2: candidate 8 has 2. Current winner 8. So still 8.
After vote3: candidate 2 gets a vote: now 1. Current winner is 8 with 2 votes. 1<2, so winner remains 8.
After vote4: candidate 2 gets another vote: becomes 2. Now, current winner 8 has 2 votes. Candidate 2 has 2 votes. Since 2<8, we set winner to 2. Output: 8,8,8,2 -> then after fifth vote: candidate 8 gets vote: becomes 3. Then, 3>2, so winner becomes 8. Then sixth: 8 becomes 4, so remains 8. Then vote7: candidate 2 becomes 3. 3<4, so winner remains 8. Then vote8: candidate 2 becomes 4. Now, same votes: 4. Then compare candidate numbers: 2 vs 8 -> 2 is smaller, so set winner to 2.

But the expected output is: 
8
8
8
2
8
8
8
2

So after the 8th vote, it's 2. That matches.

So the algorithm per vote would be:
Initialize:
  votes = [0]*(N+1)   # 1-indexed, index 0 unused.
  winner = 0   # or None, but we need to handle first vote.

For i in range(M):
  candidate = A[i]
  votes[candidate] += 1

  if winner == 0:   # first vote
      winner = candidate
  else:
      # Compare candidate and current winner
      # If votes[candidate] > votes[winner]: winner = candidate
      # Else if votes[candidate] == votes[winner] and candidate < winner: winner = candidate
      # Else: winner remains

But wait, what if the updated candidate is the current winner? Then we don't need to compare candidate numbers? Actually, if the current winner gets a vote, then votes[winner] increases. Then we just keep the same winner? Because we don't compare with others? Actually, that's correct because no other candidate's vote count increased. But what if there was another candidate that had the same vote count as the current winner? For example, current state: candidate 1: 3, candidate 2:3, winner is 1. Then candidate 1 gets a vote: becomes 4. Then candidate 1 remains winner. Correct.

But what if the current winner is candidate 2 (with 3 votes) and candidate 1 also has 3 votes? Then candidate 2 gets a vote: becomes 4. Then candidate 2 remains winner. Correct.

However, what if the updated candidate is not the current winner, but there is another candidate that has more votes? For example, current state: candidate 1: 5 votes, candidate 2: 3, candidate 3: 4. Current winner is candidate 1 (5 votes). Then candidate 3 gets a vote: becomes 5. Now, we update candidate 3. Current winner is 1. Now, candidate 3 has 5 votes, same as candidate 1. Then we compare candidate numbers: 3 vs 1 -> 1 is smaller, so we don't change the winner? But that's correct because candidate 1 has the same votes and is smaller.

But wait: what if candidate 3 had 4 votes and then becomes 5, which is equal to candidate 1? Then we compare: 3>1, so we don't change the winner. Correct.

But what if candidate 3 had 5 votes already? Then it becomes 6? Then 6>5, so we set winner to 3.

So the algorithm seems correct. However, is it efficient enough?

We are updating one candidate and doing a constant-time comparison. So for M votes, it's O(M) time, and O(N) space. That should be acceptable for M and N up to 200,000.

But let me think of a potential pitfall.

Consider: 
Current state: 
  candidate 1: 10 votes
  candidate 2: 10 votes
  candidate 3: 9 votes
Current winner: candidate 1 (because 1<2).

Now, candidate 3 gets a vote: becomes 10. Now, when we update, we compare candidate 3 with current winner (1). Candidate 3 has 10 votes, same as candidate 1. But candidate 3 (number 3) is greater than 1, so we don't change the winner. Correct: the winner should still be candidate 1 because among the three with 10 votes, candidate 1 is the smallest.

But what if candidate 3 was number 0? But candidate numbers start from 1. So no issue.

Another case: 
Current winner is candidate 2: 10 votes. Candidate 1 also has 10 votes? But wait, if candidate 1 has 10 and is smaller, then why is the winner candidate 2? That would be a problem.

How did we get into that state? 

Suppose we start:
Vote1: candidate 1 -> winner 1.
Vote2: candidate 2 -> now both 1. Then we compare: 1<2, so winner remains 1? Then how can winner become 2?

Wait, unless we have:
Vote1: candidate 2 -> winner 2.
Vote2: candidate 1 -> then candidate 1 has 1, same as 2. Then we compare: 1<2, so set winner to 1. 

So the state I described (candidate 1 and 2 both 10, and winner is 2) cannot happen because when candidate 1 got its 10th vote, if at that time the current winner was candidate 2 with 10, then we would have compared: 1<2, so set winner to 1. Then candidate 1 would be the winner until someone overtakes.

So the algorithm maintains the correct winner.

But what if the current winner has the same vote count as another candidate that has a smaller number? For example, at some point, candidate 1 has 5, candidate 2 has 5, and current winner is 1 (correct). Then candidate 2 gets another vote: becomes 6. Then we set winner to 2. Then candidate 1 gets a vote: becomes 6. Now, we update candidate 1: same as current winner (2) who has 6. Then compare: 1<2, so set winner to 1. Correct.

So the update rule is robust.

Now, code implementation:

We'll initialize:
  votes = [0] * (N+1)
  winner = 0   # candidate 0 doesn't exist. But for the first vote, we set winner to the candidate.

But for the first vote, we have to set winner to the candidate. Then for each subsequent vote, update and compare.

But what if the current winner is candidate 0? We don't have candidate 0. So we can handle the first vote separately, or we can initialize winner as None and then check.

Alternatively, we can start the loop and set the first vote. Since M>=1 (constraints say M>=1), we can process the first vote outside.

But the problem says: for i=1,2,...,M. So we have to output M lines.

Let me write the code structure:

votes = [0]*(N+1)
winner = -1   # or 0, but we'll use 0-indexed? No, candidates are 1-indexed.

Actually, we can use:
  winner = 0   # meaning invalid candidate
  res = []

  for i in range(M):
      c = A[i]
      votes[c] += 1

      if winner == 0:   # first vote
          winner = c
      else:
          # Compare candidate c and current winner
          if votes[c] > votes[winner]:
              winner = c
          elif votes[c] == votes[winner] and c < winner:
              winner = c
          # else: no change

      res.append(winner)

But what if the current winner is candidate 0? Actually, we set winner to the first candidate, so after the first vote, winner is set to a valid candidate.

Alternatively, we can initialize winner = A[0] and then start the loop from the second vote? But then we have to output the first vote separately.

Better to handle inside the loop uniformly.

But the above code handles the first vote: at the first vote, winner is 0, so we set winner to c. Then for the next votes, we do the comparison.

But what if the first candidate is 0? But candidates are from 1 to N. So no candidate 0. So it's safe.

Let me test with the first example:

After vote1: c=1, votes[1]=1. Then set winner=1. Append 1.

After vote2: c=2, votes[2]=1. Now, votes[2] (1) == votes[1] (1) -> then compare: 2<1? No. So winner remains 1. Append 1.

After vote3: c=2, votes[2]=2. Then 2>1 -> set winner=2. Append 2.

After vote4: c=3, votes[3]=1. Then 1 < votes[winner]=2 -> no change. Append 2.

After vote5: c=1, votes[1]=2. Then 2==votes[2] (which is 2) and 1<2 -> set winner=1. Append 1.

After vote6: c=3, votes[3]=2. Then 2==2, but 3>1 -> no change. Append 1.

After vote7: c=3, votes[3]=3. Then 3>2 (current winner 1 has 2) -> set winner=3. Append 3.

Perfect.

Now, what about space? We use an array of size N+1 -> O(N) space, which is acceptable (200,000 integers, about 1.6 MB in Python? Each integer is 24-28 bytes? So 200000*28 = 5.6e6 bytes, about 5.6 MB. And M is 200000, so the result list is 200000 integers, same. Total about 11 MB, which is acceptable.

Time is O(M), which is optimal.

But wait: what if there are multiple candidates with the same vote count? The above algorithm only checks the updated candidate against the current winner. But what if the updated candidate isn't the current winner and also isn't the only one with high votes? 

For example, current state: 
  candidate 1: 5 votes
  candidate 2: 5 votes
  candidate 3: 4 votes
Current winner: candidate 1 (since 1<2).

Now, candidate 3 gets a vote: becomes 5. Then we update candidate 3. Now, we compare candidate 3 (5 votes) with current winner candidate 1 (5 votes). Since 3>1, we don't change the winner. Correct: the winner is still candidate 1.

But what if candidate 3 was candidate 0? But no, candidate numbers start at 1.

Another scenario: 
  candidate 1: 5
  candidate 2: 5
  candidate 3: 5
Current winner: candidate 1 (smallest).

Now, candidate 3 gets a vote: becomes 6. Then we set winner to 3? Because 6>5. Correct.

Then candidate 1 gets a vote: becomes 6. Then we update candidate 1: same as current winner (3) who has 6? Then compare: 1<3, so set winner to 1.

So it works.

But what if the current winner is not the one with the maximum votes? Actually, the algorithm should maintain that the current winner is the one with the highest votes and smallest number. Because whenever we update, we only change the winner if the updated candidate has strictly more votes, or same votes and a smaller number. And when we update a candidate that is not the current winner, we compare only with the current winner. But if the updated candidate has more votes than the current winner, it becomes the winner. But what if there's another candidate that has more votes than the updated candidate? 

For example: 
  candidate 1: 10 votes (current winner)
  candidate 2: 11 votes (but we don't know because we only compare the updated candidate with the current winner)
  candidate 3: 9 votes

Now, candidate 3 gets a vote: becomes 10. Then we compare candidate 3 (10) with current winner (candidate 1:10). 3>1, so no change. But candidate 2 has 11 votes! So the winner should be candidate 2.

But wait, how did candidate 2 get 11 votes without becoming the winner? 

Actually, that's impossible. Because when candidate 2 got its 11th vote, we would have updated it. Then at that time, we compared: candidate 2 has 11 votes, which is more than the current winner (candidate 1 had 10 at that time). So we would have set the winner to candidate 2. Then when candidate 3 gets the next vote, we compare candidate 3 (10) with current winner candidate 2 (11). 10<11, so we don't change. So the winner remains candidate 2.

So the algorithm maintains the correct winner.

Therefore, the solution is:

Steps:
1. Read N and M.
2. Read the list A of M votes.
3. Initialize an array `votes` of zeros of size N+1.
4. Initialize `winner = 0` (invalid candidate).
5. For each vote in A:
   a. Increment the vote count for that candidate.
   b. If winner is 0, set winner to this candidate.
   c. Else, compare the current candidate (after update) with the current winner:
        if votes[c] > votes[winner]: winner = c
        elif votes[c] == votes[winner] and c < winner: winner = c
        else: do nothing
   d. Append the winner to the result list.
6. Print each winner in the result list on a separate line.

Now, let me test with the provided examples.

Example 2: 
Input: "100 5\n100 90 80 70 60"

votes = [0]*101
winner = 0

After vote1: c=100 -> votes[100]=1. Then set winner=100. Append 100.
After vote2: c=90 -> votes[90]=1. Then compare: 1==1? yes. Then 90<100? yes. So set winner=90. Append 90.
After vote3: c=80 -> votes[80]=1. Then compare: 1==1? yes. 80<90? yes. Set winner=80. Append 80.
After vote4: c=70 -> same: 70<80 -> set winner=70. Append 70.
After vote5: c=60 -> same: 60<70 -> set winner=60. Append 60.

Output: 100,90,80,70,60. Correct.

Example 3: 
Input: "9 8\n8 8 2 2 8 8 2 2"

votes = [0]*10
winner=0

After first vote: c=8 -> votes[8]=1 -> winner=8 -> output 8.
After second: c=8 -> votes[8]=2. Then compare: same votes as winner (which is 8) -> but candidate is 8, same as winner? Then no change? Actually, we don't need to change because candidate is same as winner? But we can still do the comparison: votes[8] (2) > votes[8] (2)? no. Then same, and candidate (8) < winner (8)? no. So winner remains 8. Output 8.
After third: c=2 -> votes[2]=1. Then compare: 1 < votes[8]=2 -> no change. Output 8.
After fourth: c=2 -> votes[2]=2. Then compare: 2==2? yes. Then candidate 2 < winner 8? yes. So set winner=2. Output 2.
After fifth: c=8 -> votes[8]=3. Then 3>2? yes. Set winner=8. Output 8.
After sixth: c=8 -> votes[8]=4. Then 4>2? yes. Set winner=8. Output 8.
After seventh: c=2 -> votes[2]=3. Then 3 < 4? no. So no change. Output 8.
After eighth: c=2 -> votes[2]=4. Then 4==4? yes. Then candidate 2 < winner 8? yes. So set winner=2. Output 2.

So output: 8,8,8,2,8,8,8,2. Correct.

Edge cases: 
- M=1, N=1: 
  Input: "1 1\n1"
  Output: 1.

- M=0? But constraints say M>=1.

- Multiple candidates getting votes, but one candidate dominates.

So I think this solution is correct and efficient.

Now, code implementation:

We'll write the function:

Note: We are to complete the solve method in the Solution class.

Let me write the code accordingly.

We'll use:
  import sys? Actually, the function is given, we just need to return the list.

But we have to read from stdin? The problem says: input from standard input. But the function signature is given: 
  def solve(self, N: int, M: int, A: List[int]) -> List[int]:

So we don't need to read from stdin, we are given N, M, A.

Then we just process and return the list.

Code:

from typing import List

class Solution:
    def solve(self, N: int, M: int, A: List[int]) -> List[int]:
        votes = [0] * (N+1)
        winner = 0
        res = []
        for c in A:
            votes[c] += 1
            if winner == 0:
                winner = c
            else:
                # Check if the current candidate has more votes than the winner
                # or same votes but smaller candidate number
                if votes[c] > votes[winner]:
                    winner = c
                elif votes[c] == votes[winner] and c < winner:
                    winner = c
            res.append(winner)
        return res

But what if the first candidate is candidate 0? But candidates are from 1 to N, so c>=1. So we set winner to c (>=1) and then 0 will never be used again.

But in the condition, after the first vote, winner is set to a non-zero value. Then for subsequent votes, we do the else part.

We can also avoid the if-else by initializing the winner to the first candidate? But we have to process the first vote in the loop.

Alternatively, we can do:

        if winner == 0:
            winner = c
        else:
            # conditions

But we can also combine:

        if winner == 0 or votes[c] > votes[winner] or (votes[c] == votes[winner] and c < winner):
            winner = c

But then we can write:

        if winner == 0:
            winner = c
        elif votes[c] > votes[winner] or (votes[c] == votes[winner] and c < winner):
            winner = c

But note: we have to update the votes for c first. Then check.

Actually, the condition for updating the winner is independent of the previous winner? So we can write:

        votes[c] += 1
        if winner == 0:
            winner = c
        else:
            # After update, check if we should update winner
            if votes[c] > votes[winner] or (votes[c] == votes[winner] and c < winner):
                winner = c

But we can also write without else:

        votes[c] += 1
        if winner == 0:
            winner = c
        elif votes[c] > votes[winner] or (votes[c] == votes[winner] and c < winner):
            winner = c

But note: we are updating the votes for c, then comparing. And the condition for updating the winner is after the update.

This is the same as before.

But let me test the condition: 
  if votes[c] > votes[winner] or (votes[c] == votes[winner] and c < winner)

In the case where c is the current winner, then after updating, votes[c] is increased. Then votes[c] > votes[winner]? Well, winner is c, so votes[winner] is the same as votes[c] (because we just updated). So it would be equal? Then the second part: c < winner? c and winner are the same, so false. So no update? But we don't need to update because the winner is already c.

So it's safe.

But what if we have two conditions: 
  Condition1: votes[c] > votes[winner] 
  Condition2: votes[c] == votes[winner] and c < winner

We can combine with OR.

But note: if votes[c] becomes greater, then we update. If equal and c is smaller, we update.

But if both are true? That's impossible: because if votes[c] is greater, then it's not equal. So the conditions are mutually exclusive.

So the code can be:

        votes[c] += 1
        if winner == 0:
            winner = c
        elif votes[c] > votes[winner] or (votes[c] == votes[winner] and c < winner):
            winner = c

Then append winner.

This is more concise.

I think this is the optimal solution.

Time: O(M), per vote constant time.
Space: O(N) for the votes array, and O(M) for the result, which is acceptable.

Let me run the provided examples with this code.

Example1: 
votes = [0]*4
winner=0
A=[1,2,2,3,1,3,3]

c=1: votes[1]=1 -> winner=0 -> set to 1. Append 1.
c=2: votes[2]=1 -> condition: 1>votes[winner]? 1>1? no. Then (1==1 and 2<1)? no. So winner remains 1. Append 1.
c=2: votes[2]=2 -> 2>1 -> true. Set winner=2. Append 2.
c=3: votes[3]=1 -> 1<2 -> no. Append 2.
c=1: votes[1]=2 -> 2==2 and 1<2? yes. So set winner=1. Append 1.
c=3: votes[3]=2 -> 2==2? and 3<1? no. Append 1.
c=3: votes[3]=3 -> 3>2? yes. Set winner=3. Append 3.

Correct.

So I'll implement this.

One more edge: if the same candidate gets all votes.

Example: N=1, M=3, A=[1,1,1]
After first: winner=1.
After second: votes[1]=2 -> 2>1? yes? Actually, 2>votes[winner] -> votes[winner] is 1? Then yes. So update? But winner is already 1. We can update to 1 again? It's the same. So no harm.

But condition: votes[c] > votes[winner] -> 2>1 -> true -> set winner=1. Then third: 3>2 -> true -> set winner=1. Output: 1,1,1.

But we can avoid updating if it's the same candidate? But it's harmless. Alternatively, we can skip if c==winner? But not necessary.

But if we skip, we might miss a tie? Actually, if the same candidate gets a vote, we don't need to compare with others? But the condition is only about the updated candidate. Since the updated candidate is the winner, then after update, votes[winner] increases. Then we don't need to compare with others because no other candidate's vote count increased. But we are not comparing with others, only with the current winner. And if the updated candidate is the winner, then we are comparing the same candidate. Then:

votes[c] (after update) = votes[winner] (which is the same) +1? Actually, before the update, votes[winner] was k, then we update to k+1. Then we compare: votes[c] = k+1, and votes[winner] was k+1? Because we updated it. Then k+1 > k (the previous value)? But we are comparing after update. So after updating, votes[winner] is k+1, and we compare votes[c] (k+1) with votes[winner] (k+1). Then the condition is: 
  (k+1) > (k+1) -> false, and then (k+1 == k+1 and c<winner) -> false because c==winner. So we don't update. 

But wait, that's not what we did: we updated votes[c] first. Then we have:

if votes[c] > votes[winner] ... 

But at the time of comparison, winner is still the old winner? And votes[winner] has been updated? 

No: because we updated votes[c] and c is the winner? Then votes[winner] is the same as votes[c] because winner is c. 

So if the current winner is candidate X, and we update candidate X, then votes[X] becomes old+1, and then we compare votes[X] (which is now increased) with votes[winner] (which is the same as votes[X] because winner is X). So the condition is: 
  votes[X] > votes[X] -> false, 
  or (votes[X] == votes[X] and X < X) -> false.

So we don't update. 

But in our code, we don't have an else. So we leave winner as is. Which is still X. Correct.

But in the code above, we did:

        elif votes[c] > votes[winner] or (votes[c] == votes[winner] and c < winner):
            winner = c

So when c is the winner, after update, votes[c] becomes k+1, and votes[winner] is also k+1 (because winner is c). So the condition is false. So we don't set winner = c again. So it's efficient.

Therefore, we don't need a special case for c==winner.

So the code is:

from typing import List

class Solution:
    def solve(self, N: int, M: int, A: List[int]) -> List[int]:
        # votes count for each candidate, 1-indexed
        votes = [0] * (N+1)
        winner = 0  # invalid initial winner
        res = []
        for c in A:
            votes[c] += 1
            # If no winner yet, set to c
            if winner == 0:
                winner = c
            else:
                # Check if current candidate c should become the winner
                if votes[c] > votes[winner] or (votes[c] == votes[winner] and c < winner):
                    winner = c
            res.append(winner)
        return res

But we can also write without the else:

        for c in A:
            votes[c] += 1
            if winner == 0 or votes[c] > votes[winner] or (votes[c] == votes[winner] and c < winner):
                winner = c
            res.append(winner)

This is more concise.

But note: if winner is 0, we set winner to c. Then in the next votes, we do the other conditions.

But if winner is 0, then the other conditions might be true? For example, votes[c] is 1, and we compare with votes[0] which is 0. Then votes[c] > votes[0] -> true. So we set winner to c. But that's the same as the if branch. So we can combine:

        for c in A:
            votes[c] += 1
            if winner == 0 or votes[c] > votes[winner] or (votes[c] == votes[winner] and c < winner):
                winner = c
            res.append(winner)

But what if winner is 0? Then votes[winner] would be votes[0]. We have an array of size N+1, index 0 is for unused. votes[0] is 0. Then votes[c] is 1 (after update). Then 1>0 -> true. So we set winner to c. Correct.

But what if we have a candidate 0? We don't, because candidates from 1 to N. So votes[0] is always 0 and never updated. So safe.

But in the condition, when winner is 0, we are accessing votes[0]. That's okay.

So we can write:

        for c in A:
            votes[c] += 1
            if winner == 0 or votes[c] > votes[winner] or (votes[c] == votes[winner] and c < winner):
                winner = c
            res.append(winner)

This is clean.

Let me test with the first vote: 
  c=1: votes[1]=1. Then condition: winner==0 -> true, so set winner=1. Append 1.

Second vote: c=2: votes[2]=1. Then condition:
   winner !=0 -> skip first condition.
   votes[2] (1) > votes[1] (1) -> false.
   votes[2]==votes[1] and 2<1 -> false.
   so condition false, winner remains 1. Append 1.

Same as before.

So we'll use this.

Now, I think we are done.

But what about performance in worst-case? 
  M=200000, N=200000. 
  We do 200000 iterations, each does constant work. 
  The list `votes` is of size 200001, which is acceptable.

So we can run the code.

I'll write the final code.

Note: We have to import List from typing.

But the problem says: "Do not solve the problem yet", but we are to provide the solution. I think we have the solution.

But the problem says: "Please carefully read the following programming problem and explore multiple possible solutions." and then go through steps.

I did explore the brute force? Actually, I started with the optimized solution. But the brute force would be:

  For each prefix i (from 1 to M):
      Count the votes for each candidate from the first i votes.
      Then, find the candidate with the maximum votes; if tie, smallest candidate number.

But that would be O(M*N) per prefix? Actually, we can do the counting in a loop for each prefix? Then total time O(M*N), which is 40e9 for M=N=200000, too slow.

Alternatively, we can maintain counts and then scan the counts array for each vote? That would be O(M*N) as well.

So we need an efficient solution. The solution we have is O(M).

We can also think of using a heap? 

Alternatively, we can use a segment tree or a Fenwick tree? But that would be overkill because we are only updating one element and then we only need to compare with the current winner. The current solution is simpler.

Another approach: use a dictionary and a heap.

We can maintain a max-heap of (vote_count, candidate). But then when we update a candidate, we push the new count. But then we have to check if the top of the heap is the latest. We can use a lazy heap: we push the new count for the candidate, but when we pop, we check if the count in the heap matches the current count. 

But worst-case, we might push M times, so the heap size is M. Then each push is O(log M), total O(M log M). But M=200000, so 200000 * log2(200000) ≈ 200000 * 18 = 3.6e6, acceptable. But our current solution is O(M) and simpler.

But in the heap solution:

  Initialize:
    counts = [0]*(N+1)
    heap = []   # max-heap: we can use negative counts? or min-heap for (-count, candidate) 
    But then if same count, we want smallest candidate? 

  Actually, we can store in the heap: (count, candidate) but we want max count and then min candidate in case of tie? 

  But the heap in Python is min-heap. So we can store (-count, candidate). Then the top would be the one with the largest count and then in case of same -count, the smallest candidate? Actually, the heap compares the first element, then the second. So (-count, candidate). Then the smallest -count (most negative) is the largest count. And if two have same -count, then the smallest candidate? But we want the smallest candidate to win? 

  Example: 
      candidate1: count=1 -> (-1,1)
      candidate2: count=1 -> (-1,2)

  Then in the heap, (-1,1) and (-1,2): the min heap will put (-1,1) before (-1,2). So when we pop, we get (-1,1) which is candidate1. Correct.

  Steps:
      res = []
      counts = [0]*(N+1)
      heap = []   # min-heap for (-count, candidate)

      For i in range(M):
          c = A[i]
          counts[c] += 1
          heappush(heap, (-counts[c], c))

          # Now, the top of the heap might not be updated? But we have pushed the new count.
          # But there might be old entries for the same candidate. So we do lazy deletion.
          while heap:
              neg_count, cand = heap[0]   # look at the top
              # If the stored count is not the current count, pop it.
              if -neg_count != counts[cand]:
                  heappop(heap)
              else:
                  break
          # Now, the top of the heap is the candidate with max count and then smallest candidate in tie.
          res.append(heap[0][1])

      return res

But worst-case, each push might lead to multiple pops? And the heap might have many outdated entries. The total number of pushes is M, and each pop is O(log M). Total time O(M log M). But worst-case, the heap might have M entries, and each pop is O(log M). So overall O(M log M). 

But our current solution is O(M) and simpler. Also, the heap solution uses O(M) extra space for the heap. 

But the current solution is O(1) per vote (amortized) and O(N) space. 

So the heap solution is acceptable, but the direct comparison with the current winner is more efficient and simpler.

Therefore, we choose the direct comparison.

So I think we are done.

Let me write the final code.

## Final Solution

### Step 1: Problem Understanding and Modeling
- **Core Requirements**: The problem involves processing a sequence of M votes for N candidates, updating the winner after each vote. The winner is the candidate with the highest vote count; in case of a tie, the candidate with the smallest number wins.
- **Key Constraints**: N and M can be as large as 200,000, requiring an efficient O(M) or O(M log N) solution.
- **Input/Output**: Input is N (candidates), M (votes), and a list A of M vote values. Output is M lines, each indicating the winner after the first i votes.
- **Edge Cases**:
  - Single candidate (N=1): Winner is always that candidate.
  - All votes for one candidate: Winner remains that candidate.
  - Ties: Winner is the smallest-numbered candidate with the most votes.
  - Increasing vote counts: When a new candidate gets a vote, they may become the winner if their count exceeds or ties with the current winner (and they have a smaller number).

### Step 2: Multiple Solution Exploration
1. **Brute Force**:
   - **Idea**: After each vote, count all votes for each candidate, then find the winner by scanning all counts.
   - **Time Complexity**: O(M*N) per vote, O(M²N) total — infeasible for large M and N.
   - **Space Complexity**: O(N) for counts — acceptable, but time is prohibitive.
   - **Limitations**: Too slow for constraints (up to 40 billion operations).

2. **Heap-Based Solution**:
   - **Idea**: Maintain a max-heap (using negative counts) of (vote_count, candidate). Push updated counts, and lazily remove outdated entries.
   - **Time Complexity**: O(M log M) due to heap operations.
   - **Space Complexity**: O(M) for heap entries.
   - **Limitations**: More complex than necessary; the direct comparison method is simpler and faster.

3. **Direct Comparison (Optimal)**:
   - **Idea**: Track current winner and vote counts. After each vote, update the candidate's count. If the updated candidate has more votes than the current winner, or the same votes and a smaller number, update the winner.
   - **Time Complexity**: O(M) — each vote processed in O(1) time.
   - **Space Complexity**: O(N) for counts and O(M) for output.
   - **Applicability**: Efficiently handles constraints by leveraging the insight that only the current winner and updated candidate need comparison.

### Step 3: Optimal Solution Selection and Implementation
- **Selection Rationale**: The direct comparison method is optimal due to O(M) time and O(N) space, meeting constraints efficiently. It simplifies winner updates by comparing only the updated candidate with the current winner, leveraging the problem's properties.
- **Implementation**:
  - Initialize a `votes` array to track counts for each candidate (1-indexed).
  - Initialize `winner` as 0 (invalid initial value).
  - For each vote:
    - Increment the candidate's vote count.
    - If no winner exists (`winner == 0`), set the winner to this candidate.
    - Else, if the candidate's votes exceed the current winner's votes, or equal with a smaller number, update the winner.
    - Append the current winner to the result list.
- **Key Optimizations**: 
  - Only the updated candidate and current winner are compared, avoiding full scans.
  - Constant-time operations per vote ensure optimal performance.

### Step 4: Verification and Summary
- **Verification**:
  - **Example 1**: Input `N=3, M=7, A=[1,2,2,3,1,3,3]` produces output `[1,1,2,2,1,1,3]`.
  - **Example 2**: Input `N=100, M=5, A=[100,90,80,70,60]` produces `[100,90,80,70,60]`.
  - **Example 3**: Input `N=9, M=8, A=[8,8,2,2,8,8,2,2]` produces `[8,8,8,2,8,8,8,2]`.
- **Summary**:
  - The solution efficiently tracks the winner by focusing on relevant comparisons.
  - Insights: Only the updated candidate can challenge the current winner; no global scan needed.
  - **Further Optimization**: None required; the solution is asymptotically optimal.

### Final Code
```python
from typing import List

class Solution:
    def solve(self, N: int, M: int, A: List[int]) -> List[int]:
        votes = [0] * (N + 1)  # 1-indexed vote counts
        winner = 0  # invalid initial winner
        res = []    # result list for winners after each vote
        
        for c in A:
            votes[c] += 1  # update vote count for candidate c
            # Update winner if:
            # 1. No winner yet (winner == 0)
            # 2. Candidate c has more votes than current winner
            # 3. Or same votes as winner but smaller candidate number
            if winner == 0 or votes[c] > votes[winner] or (votes[c] == votes[winner] and c < winner):
                winner = c
            res.append(winner)
        
        return res
```