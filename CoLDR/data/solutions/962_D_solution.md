# Solution for 962_D.md

Generated at: 2025-07-22 06:11:49
Model: deepseek-reasoner

## Reasoning Process

We are given an array of positive integers. We repeatedly do the following:
  - Find the smallest value `x` that appears at least twice.
  - Take the first two occurrences of `x` (the leftmost two).
  - Remove the first occurrence and replace the second occurrence by `2*x`.

However, note that after merging two `x` into `2*x`, the new value might create new duplicates (for example, if there was already a `2*x` or if we create a duplicate with another value). Also, the merging operation might break the order of the array (we remove the first occurrence and change the second).

Constraints: n up to 150,000.

Key observation: 
  The operations must be performed until there are no duplicates? Actually, the problem says "While there are at least two equal elements", but note: after merging, we remove one element and change one element. The merging might create a new duplicate (for example, if we have [1,1,1] -> [2,1] and then we have two 1's? Actually, no: [1,1,1]: 
      Step1: smallest x with duplicates: 1. First two: remove first 1, replace second by 2 -> [2,1] (but now we have one 2 and one 1, so no duplicates? Actually, the array becomes [2,1] and we stop? 
  However, consider Example 2: [1,1,3,1,1] -> 
      Step1: smallest x=1, merge first two: [ (remove first 1), then the second 1 becomes 2] -> [2,3,1,1]
      Then we have two 1's? So next smallest x=1? But wait: the next step: smallest x with duplicates: 1? Then we take the two 1's? But note: the array is [2,3,1,1] -> merge the two 1's: remove the first 1 and replace the second by 2 -> [2,3,2]. Then we have two 2's? Then next step: smallest x=2, merge the two 2's: remove the first 2 and replace the second by 4? Then we get [3,4]. 

But note: the problem says "the smallest value x that occurs 2 or more times". In [2,3,2], the smallest value that has duplicates is 2 (and 2 is the smallest because 2<3 and 2 appears twice). 

However, note that after merging, the new value (2*x) might be larger than other numbers? But we are only concerned with the smallest x that has duplicates. Also, the merging operation does not necessarily preserve the sorted order? But the array is processed from left to right.

Important: The operation is always performed on the two leftmost occurrences of the smallest duplicate value.

How to approach?

We cannot simulate each operation by scanning the entire array repeatedly because n is up to 150,000 and the number of operations could be large (each operation reduces the size by 1, so at most n-1 operations). But worst-case, each operation might require scanning the entire array to find the smallest duplicate? That would be O(n^2) which is too slow for n=150,000.

We need a more efficient approach.

Observation:
  The operations are always merging adjacent duplicates? But note: the duplicates might not be adjacent. For example: [3,4,1,2,2,1,1] -> the first duplicate for 1: the two leftmost 1's are at positions 3 and 6? But wait, actually the first two 1's are at positions 3 and 6? But the array is [3,4,1,2,2,1,1]. The first occurrence of 1 is at index 2 (0-indexed: a[2]=1), then the next is at a[5]=1 and then a[6]=1. So the first two are at index2 and index5? But note: the problem says "the first two occurrences of x" meaning the two leftmost. So we remove the one at index2 and replace the one at index5 by 2. Then the array becomes:
      [3,4, remove the element at index2 -> so we remove the 1 at index2, and then the element at index5 (which was originally the sixth element) becomes 2? But after removal, the array shifts: 
      Original: indices: 0:3, 1:4, 2:1, 3:2, 4:2, 5:1, 6:1
      Remove index2: [3,4,2,2,1,1] -> but then we change the next occurrence (which was at index5 originally) but after removal, the element that was at index5 becomes the element at index4? Actually, we are removing the first occurrence and then updating the second occurrence. So the array becomes:
          [3,4,2,2,2,1]   -> because we replace the 1 at index5 (which after removal becomes index4) by 2? Actually, no: we remove the element at index2, and then update the element at the position of the second occurrence (which was at index5) to 2. But after removal, the array from index2 onward shifts left by one. So the element at index5 (the last 1) moves to index4? However, we are updating the element at the original index5? Then we have:
          positions: 
            index0:3, index1:4, 
            then we remove the element at index2 (which is 1) -> so now:
            index2: becomes the old index3: 2
            index3: old index4: 2
            index4: old index5: 1 -> we update this one to 2? 
            index5: old index6: 1 -> becomes index4? Actually, after removal, the array has 6 elements: 
                [3,4,2,2,1,1] becomes [3,4,2,2,2,1]? But wait: we updated the second occurrence of the two 1's? The two 1's we merged: the first one (at index2) is removed, and the second one (which was at index5) becomes 2. Then the array is:
                [3,4,2,2,2,1] -> but the last element is still 1? So now we have two 2's? Actually, we have three 2's? Then the smallest duplicate is 2? Then we merge the first two 2's? 

But note: the array is [3,4,2,2,2,1] -> the first two 2's are adjacent? So we remove the first 2 (at index2) and replace the next 2 (at index3) by 4? Then we get [3,4,4,2,1]. Then we have two 4's? Then we merge the two 4's: remove the first 4 (at index1) and replace the next 4 (at index2) by 8? Then we get [3,8,2,1]. 

But note: in the example explanation: 
    [3,4,1,2,2,1,1] -> [3,4,2,2,2,1] -> [3,4,4,2,1] -> [3,8,2,1]

So the operations are not necessarily adjacent? However, after the first merge, the two 1's were not adjacent? But then the next merge is on the 2's? The 2's become adjacent because the removal of the 1 caused the two 2's to come together? 

But what if we have non-adjacent duplicates? 

We need a way to efficiently:
  - Find the smallest value that has duplicates? 
  - Then find the two leftmost occurrences of that value.

But note: when we merge two elements, we remove one element and update the value of the other. This update might cause that element to become a new value (2*x) which might then create a new duplicate or break an existing duplicate? 

We also need to update the positions: when we remove an element, the indices of all elements to the right shift left by one. 

We can use a linked list? But we need to remove an element and update the next occurrence? Also, we need to quickly find the smallest duplicate. 

Alternatively, we can use a priority queue? 

Idea:
  We can traverse the array from left to right and simulate the merging process. Note that a merge at a later position might be affected by a merge at an earlier position? But the operations are always on the leftmost duplicates? So we must process the array from left to right? 

But note: the smallest duplicate value might appear at any position? 

Another idea: 
  We note that the merging operation is always combining two numbers that are the same and replacing them with a larger number. The larger number might then combine again? 

  We can use a stack? 

  Consider: 
      We traverse the array from left to right. For each element, we push it to the stack. But if the top of the stack is the same as the current element, then we can merge? But note: we are not merging adjacent duplicates arbitrarily. We are merging the smallest duplicate value that appears anywhere? 

  However, the problem requires: 
      - The smallest value x that has duplicates anywhere? 
      - Then the two leftmost occurrences.

  But if we use a stack, we might merge adjacent duplicates arbitrarily? 

  But note: the merging of two duplicates might not be adjacent? For example: [1,2,1] -> the two 1's are not adjacent. The smallest duplicate value is 1. We have to merge the first two 1's: the one at the beginning and the one at the end? Then we remove the first 1 and update the last 1 to 2. Then we get [2,2]. Then we merge the two 2's? 

  However, after removing the first 1, the array becomes [2,2] (because we removed the first element and updated the last element from 1 to 2). 

  How can we simulate this with a stack? 

  Alternatively, we can use a priority queue to track the next occurrence for each value? 

  However, the challenge is that when we merge, we remove one element and update another. Also, the update of an element might break an existing duplicate or create a new one. 

  We need to maintain:
      - The current array, but we cannot store the entire array for each operation (too slow).
      - We need to quickly find the smallest value that has duplicates? 

  We can use:
      - A min-heap (priority queue) for the current duplicate values? But note: a value might appear multiple times? And we need the two leftmost occurrences? 

  We can maintain:
      - A list of the current elements (we want to avoid shifting the entire array, so we can use a linked list for positions?).
      - We also maintain a mapping: for each value, a sorted list (like a heap) of the positions where it appears? But the positions change when we remove an element? 

  Alternatively, we can use a union-find? Not obvious.

  Another idea: we can process the array from left to right, and whenever we see a duplicate, we merge? But note: we are supposed to merge the smallest duplicate value, not necessarily the one that appears first? 

  Example: [1,2,1] -> the smallest duplicate value is 1. But if we traverse left to right, we see a 1 then a 2 then a 1. We don't merge the first 1 and the 2? 

  How about we maintain a pointer that traverses the array and we keep a data structure of the current active elements and their positions? 

  Insight: 
      The operations always involve the smallest duplicate. Therefore, the merges for the smallest numbers happen first. 

      We can use a priority queue (min-heap) that holds for each distinct value that currently has at least two occurrences, the value and the position of the first two occurrences? But we also need to update when a value is removed or changed.

  However, the merging of two numbers might create a new number that is larger? And we are only concerned with the smallest duplicate? 

  We can do:

      Let's maintain:
        - A data structure for the array that supports removal and update? (like a doubly linked list) 
        - For each value, a sorted set (or a heap) of the positions where that value appears. 
        - A global min-heap that holds (value, first_occurrence_position) for each value that has at least two occurrences? But the first_occurrence_position is the first occurrence of that value? Then when we merge, we remove two nodes and add one new node? 

      Steps for one operation:
          - Pop the smallest value from the min-heap (that has at least two occurrences). But note: the same value might be pushed multiple times? And we need to check if the two occurrences we are about to merge are still valid? (because they might have been merged by a previous operation).

      How to avoid stale information? 

      We can use a lazy heap: 
          - We maintain a min-heap of (value, position1, position2) for the next merge? But we don't know the two positions until we look? 

      Alternatively, we can maintain for each value a sorted list of the positions? Then the global min-heap holds (value, first_occurrence) for each value that has at least two occurrences? But then we can get the first two occurrences from the sorted list for that value. 

      But when we remove an element, we have to update the positions for that value? And also when we update an element (changing one occurrence to 2*x), we remove one occurrence from the value x and add an occurrence for the value 2*x? 

      Steps for one operation:

          Let min_value = the smallest value that has at least two occurrences. We can have a min-heap for the candidate values (that are duplicate). But we must check that the candidate value still has duplicates? 

          How about we maintain:
            - A min-heap `heap` for values that are known to have duplicates. But we might have multiple entries for the same value? We don't care because we can use lazy deletion.

          We also maintain:
            - A linked list for the array: each node has value, next, prev. 
            - A dictionary: `positions` mapping value -> a sorted list (or a heap) of the positions (or rather the node references) for that value? But the array is changing so we need to update.

      Alternatively, we can use an array that we update and use a balanced BST? 

      Considering the constraints (n=150000) and the fact that each operation reduces the size by one, we have at most 150000 operations? But each operation might involve updating multiple data structures? 

      Specifically, for one operation:
          1. Find the smallest value x that has duplicates: min-heap top (with lazy deletion) -> O(1) per operation but with lazy deletion we might have to pop until we find a valid one.
          2. For that value x, get the two leftmost occurrences (from the sorted list of positions for x). 
          3. Remove the first occurrence (from the linked list and from the data structures).
          4. Change the second occurrence to 2*x: 
                - Remove the second occurrence from the list for x (and if the list for x becomes less than 2, remove x from the candidate heap? But we have lazy heap so we don't remove immediately).
                - Then add an occurrence for 2*x: if there was already an occurrence for 2*x, we have to update the sorted list for 2*x and also push 2*x to the heap if now it has duplicates? 

          5. Also, when we remove the first occurrence, we have to update the linked list: the node is removed, and the next node of the previous node points to the next node? 

      How do we represent positions? We can use a doubly linked list. Each node has:
          value, next, prev.

      We also have:
          - A dictionary `value_to_nodes`: mapping value -> a sorted set (by the position) of nodes? But how do we get the "first two" in the entire array? The linked list order is the array order. 

      Actually, the "first two" occurrences of x are the two nodes with the smallest indices? But we don't have indices. In the linked list, we can traverse from head? 

      Alternatively, we store the nodes in the order of the linked list. Then the first two occurrences of x: we can iterate the linked list? But that would be O(n). 

      Instead, we can maintain for each value a sorted set (by the position in the original array? but the array changes) -> but the linked list order is the current array order. 

      How about we assign an index to each node that is the original index? Then the array order is the linked list order. The "leftmost" is the one that appears earliest in the linked list. Since the linked list is in order, we can compare the nodes by the order: we can use the address? But we can't compare addresses. 

      Alternatively, we can store for each value a set of nodes? Then to get the two leftmost, we have to traverse the entire linked list? 

      Instead, we can note: the two leftmost occurrences of x in the current array are the two nodes that have the smallest "global order". We can store the next pointer? 

      Actually, we can use a balanced BST for each value? But we are in Python and we can use a sorted list for each value? But when we remove a node, we remove it from the list for x. Then the two leftmost are the first two in the sorted list? But we need to sort by the order in the linked list. 

      We can store for each value a heap (min-heap) of nodes? But we need to compare nodes by their order? How? 

      We can assign an index to each node that is the original index? Then when we remove a node, we don't change the original index? Then the "leftmost" would be the one with the smallest original index? 

      But consider: [1,2,1] -> the two 1's: the first has original index0, the second has original index2. Then we merge the two 1's: we remove the one at index0 and update the one at index2 to 2. Then the array becomes [2,2] (because the node at index1 is 2 and the node at index2 becomes 2). Then we have two 2's: the one at index1 and the one at index2. The two leftmost 2's: the one at index1 is leftmost. 

      However, if we use the original index, then we can always get the two leftmost by the smallest two original indices? 

      Why? Because the array is traversed from left to right, and when we remove an element, we remove the entire node. The remaining nodes keep their original indices? Then the order in the linked list is the increasing order of the original indices? 

      So we can represent each node by:
          value, original_index (which never changes), and also we store the links (prev, next) for removal.

      Then for each value, we maintain a min-heap (priority queue) of the original indices (which represent the positions in the original array) for the nodes that have that value. 

      Then the two leftmost occurrences of x are the two smallest original indices in the heap for x. 

      Steps:

          We have:
            linked_list: a doubly linked list of nodes, each node: 
                value, 
                orig_index, 
                prev, next.

            head: the first node.

            We maintain a dictionary: `value_dict` that maps value -> min-heap (priority queue) of the original indices of the nodes that have that value.

          We also have a global min-heap `candidates` that contains: 
                (value, min_orig_index)   for each value that has at least two nodes? 
            But we can also use lazy deletion: we might push multiple times for the same value? 

          Actually, we can have a separate min-heap for candidate values? Or we can have a min-heap that contains (value, min_orig_index) for each value that has at least two nodes? Then the top candidate is the one with the smallest value? If two values are equal, then we break by the value? 

          But the problem: the smallest value x that has duplicates. So we compare by value. 

          So the global min-heap `candidate_heap` will be a min-heap of (value, min_orig_index) for each value that currently has at least two nodes? But note: we don't care about the min_orig_index for the candidate? We only care about the value? But we need to check that the candidate value is still valid? 

          However, we need to know: when we pop a candidate (v, min_index) from the heap, we then check the heap for v: does it still have at least two nodes? And is the smallest node in the heap for v still min_index? 

          Alternatively, we can do:

            candidate_heap: min-heap of (value, first_occurrence, second_occurrence) but we don't know the second_occurrence? 

          Actually, we can store in the candidate_heap: (value, first_occurrence) and then for the value, the first_occurrence is the smallest original index for that value? Then when we get the candidate (v, pos1) from the heap, we then check the heap for v: the top two? 

          But note: we might have updated the heap for v: if the node at pos1 is still in the heap for v? 

          How we handle removals? 

          We do lazy deletion: 
            - When we remove a node (with value v and original index p), we don't remove it from the heap for v immediately? We mark it? 

          Instead, we can do:

            We maintain for each value v: a min-heap of the original indices of the nodes that have value v and are still active.

            And we maintain a global min-heap `candidate_heap` that has (v, min_index) for each value v that has at least two active nodes? 

            But how to update the global heap? 

            We can do: 
                Initially, for each value v that has at least two nodes, we push (v, min_index) to candidate_heap, where min_index is the smallest original index in the heap for v.

            Then when we pop the candidate_heap, we get (v, min_index). Then we check the heap for v: 
                - If the heap for v has at least two nodes and the smallest node is min_index, then we proceed. 
                - Otherwise, we pop the candidate_heap again.

            But how do we know if the heap for v has min_index at the top? We can check the top of the heap for v? 

            However, the heap for v might have min_index at the top? But if min_index has been removed, then we need to pop from the heap for v until we get an active node? 

          Therefore, we can do lazy deletion for the per-value heaps as well? 

          Alternatively, we can use a balanced BST for each value? But we are in Python and we can use a set? 

          But we need the two smallest original indices for v? 

          We can maintain for each value v a sorted list? But removals and insertions would be O(n) per operation? 

          Alternatively, we use a heap for each value and then a separate set to mark removed nodes? 

          Steps for one operation:

            while candidate_heap is not empty:
                pop the top (v, min_index) from candidate_heap.
                Check the heap for v: 
                    while the heap for v is not empty and the top of the heap for v is not active? (we don't have an active flag) -> we can use lazy deletion: we mark the node as removed? But we don't store active flag per node? 

            Instead, we can do:

                We maintain for each value v: a min-heap `node_heaps[v]` for the original indices of the active nodes? But we also maintain a dictionary `active` for each original index? But that would be too big.

          Another idea: when we remove a node (with original index p and value v), we don't remove it from the heap for v. Then when we get the top of the heap for v, we check if that node is still in the linked list? How? We don't have a direct mapping. 

          Alternatively, we store for each node a flag? But we remove the node from the linked list, so we can set a flag. Then when we get the top of the heap for v, if the node is not active, we pop it and then check the next? 

          We can store: 
              active_nodes = set() of original indices? But then we have to check each time? 

          Given the number of operations (n-1) and the total number of nodes (n initially, then n-1, then n-2, ...) the lazy deletion might not be too bad? 

          But worst-case, we might have O(n) per operation? 

          Alternatively, we can avoid the global candidate_heap? We can always check the min value in the candidate_heap? 

          Actually, we can maintain the global candidate_heap as follows: 
              We push (v, min_index) for each value v that has at least two nodes. But when the heap for v changes (because we remove a node or update a node) then the min_index for v might change? 

          How about we don't update the global candidate_heap until we need to? And we use lazy deletion: when we pop a candidate (v, min_index) from candidate_heap, we then check the heap for v: 
              Pop from the heap for v until the top is active? Then if the heap for v has at least two nodes, then we have a candidate? Then we push the new min_index for v? 

          But note: we are going to remove two nodes for v? Then we need to update the heap for v: we remove two nodes? 

          Actually, we can do:

            candidate_heap: min-heap of (v, min_index) 
            per value v: min_heap_v = min-heap of the original indices of the active nodes with value v.

            Steps for one merge:

              while candidate_heap is not empty:
                  pop (v, min_index) from candidate_heap.
                  # Now, we clean the min_heap_v: pop until we get an active node at the top? But we don't have an active flag? 

              Instead, we can store for each value v, the current min_index? Then we can check: if the current min_index for v (which we can get by looking at min_heap_v) is not min_index, then we push (v, current_min_index) and ignore this popped candidate? 

          Actually, we can do:

              candidate_heap: min-heap of (v, min_index) 
              We also store for each value v: the current min_index (which is the smallest original index in the active nodes for v) and we also store the entire min_heap_v.

              But we don't want to store redundant information.

          Alternatively, we can avoid the global candidate_heap? We can maintain the smallest value that has duplicates? 

          But the smallest value might change? 

          We can use a balanced BST for the candidate values? But we are in Python and we want efficiency.

      Considering the complexity, we can do:

          We maintain:
            linked_list: doubly linked list of nodes. Each node: 
                value: the current value
                orig_index: the original index (which is the index in the initial array) and we never change it.
                prev, next: pointers.

            head and tail.

            value_to_heap: a dictionary mapping value -> min-heap (list) of the original indices of the active nodes with that value. We use heapq.

            candidate_heap: a min-heap of (value, min_orig_index) for each value that has at least two active nodes? But we might push the same value multiple times? 

          We also maintain: 
            active: a boolean array? Or we don't? We rely on the heaps? 

          Steps for one operation:

            Step 1: Find the candidate: 
                while candidate_heap is not empty:
                    (v, min_index) = heapq.heappop(candidate_heap)
                    # Now, check if this candidate is valid: 
                    #   Check the heap for v: 
                    #       Clean the heap for v: while the heap for v is not empty and the top of the heap for v is not min_index? 
                    #       Actually, we want to know: is the node with orig_index=min_index still active? and does it have value v? 
                    #       But note: the node might have been updated to a different value? 

                Actually, we have a problem: a node might have its value changed? 

          How do we handle value changes? 

          When we update a node (from v to w), we must remove the node from the heap for v and add it to the heap for w. 

          But when we update the node (say at original index p) from v to w, we do:

            Remove p from the heap for v? But the heap for v is a min-heap and we don't have a way to remove an element? 

          This is a problem. 

      Alternative approach: 

          We can use a balanced BST for the entire array? 

      Or we can use a different idea: 

          Insight: 
              The operations are independent of the positions? Actually, no: we merge the two leftmost occurrences. 

          But note: the merging operation is associative? 

          Actually, we can simulate the entire process with a stack? 

          Consider: we traverse the array from left to right. We maintain a stack of elements. 

          For the current element, we push it to the stack. Then we check: while the top of the stack is equal to the next element? But note: we are not merging adjacent duplicates arbitrarily. 

          However, observe: 
              The problem merges the smallest duplicate value. But note: the smallest duplicate value might appear anywhere? 

          Example: [1,2,1] -> we cannot merge the 1's because they are not adjacent? 

          But wait: after the first operation, we remove the first 1 and then update the last 1 to 2? Then we get [2,2]. 

          How can we simulate this with a stack? 

          Idea: 
              We traverse from left to right. We maintain a stack: each element is a value. 

              But we cannot arbitrarily merge non-adjacent duplicates? 

          Alternatively, we can use a priority queue for events? 

      Another known solution: 

          There is a known solution for this problem (from CodeForces) that uses a linked list and a priority queue. 

          Steps from known solution:

            We can use a linked list to store the array. Each node has value, next, prev.

            We use a priority queue (min-heap) that stores (value, node) for each node? But we want to merge the smallest duplicate? 

          Actually, we can store in the priority queue: (value, node_pointer) for each node? Then we want to merge the smallest value that has a duplicate? But we don't know if a node has a duplicate? 

          We can store: 
                events = min-heap of (value, node, node_index) but then we don't know if the node is still active.

          Known solution in C++:

            #include <iostream>
            #include <set>
            #include <list>
            #include <queue>
            using namespace std;

            struct Node {
                long long value;
                Node* prev;
                Node* next;
            };

            struct CompareNode {
                bool operator()(Node* a, Node* b) {
                    if (a->value != b->value) return a->value > b->value;
                    return (long long)(a) > (long long)(b); // to break ties by address
                }
            };

            int main() {
                int n;
                cin >> n;
                list<Node*> nodes;
                Node* head = nullptr;
                Node* tail = nullptr;
                for (int i = 0; i < n; i++) {
                    long long x;
                    cin >> x;
                    Node* node = new Node{x, nullptr, nullptr};
                    if (head == nullptr) {
                        head = node;
                        tail = node;
                    } else {
                        tail->next = node;
                        node->prev = tail;
                        tail = node;
                    }
                    nodes.push_back(node);
                }

                priority_queue<Node*, vector<Node*>, CompareNode> pq;
                for (Node* node = head; node != nullptr; node = node->next) {
                    pq.push(node);
                }

                while (!pq.empty()) {
                    Node* node = pq.top(); pq.pop();
                    // If the node is not valid (because it was merged and removed), skip.
                    // How to check? We can mark as deleted? Or check if it's still in the linked list?
                    // We can check: if node->prev is pointing to something that is not its actual prev? 
                    // But we don't have a flag.

                    // Instead, we can skip if the value of the node has changed? But we update the node's value? 

                    // Actually, when we merge, we remove the node and its next node? No, we remove the first occurrence and update the second occurrence.

                    // How do we know if node is still active? We might have a better way.

                    // Known solution: they use a different approach: they don't remove the node immediately from the priority queue, but when they pop a node, they check if it is the same as the current value? But the current value might have been updated by a merge? 

                    // Alternatively, we can store in the node a pointer to an iterator in the priority queue? 

                    // A known technique: we can push the same node multiple times? And then when we pop, we check if the value of the node is the same as the one in the event? 

                    // But here, we are only pushing each node once initially.

                    // Let's assume we have a way to know that the node is still active: we can check if its value is the same as the one we are holding? But when we update the node's value, we change it.

                    // So when we pop a node, if its value has changed, then we skip and push the new value? 

                    // But we didn't push the new value? 

                }

          }

      We can do:

          We maintain a priority queue of (value, node) and also in the node we store its current value? 

          Steps:

            Initially, for each node, push (value, node) into the priority queue.

            Then:
                while priority queue is not empty:
                    pop the smallest (value, node) from the queue.
                    If the node's current value is not equal to 'value', skip (lazy deletion).

                    Otherwise, if this node's value is not the same as the next node? But wait, we are not merging two adjacent nodes? We are merging the first two occurrences of the value in the entire array? 

                    But note: after we remove the first occurrence (which is not necessarily this node) then the two occurrences might become adjacent? 

          This approach seems not to work because the two leftmost occurrences might not be adjacent.

      Known solution in Python from the internet:

          n = int(input().strip())
          a = list(map(int, input().split()))

          # next and prev pointers
          next_node = [0] * (n+2)
          prev_node = [0] * (n+2)
          # We are going to use indices 1..n for the array.
          # Also, we create a fake head and tail.

          # But one known solution: 

          # Another known solution: 
          #   https://codeforces.com/contest/1140/submission/51801900 (C++)

          #   #include <bits/stdc++.h>
          #   using namespace std;
          #   typedef long long ll;
          #   const int maxn = 150000 + 5;
          #   int n, nxt[maxn], pre[maxn];
          #   ll a[maxn];
          #   set<pair<ll, int> > st;
          #   bool del[maxn];
          #   int main() {
          #       scanf("%d", &n);
          #       for (int i = 1; i <= n; i++) {
          #           scanf("%lld", a + i);
          #           st.insert({a[i], i});
          #           nxt[i] = i + 1;
          #           pre[i] = i - 1;
          #       }
          #       pre[1] = 0; nxt[n] = 0;
          #       while (!st.empty()) {
          #           auto p = *st.begin(); st.erase(st.begin());
          #           ll val = p.first; int id = p.second;
          #           if (del[id]) continue;
          #           if (nxt[id] == 0) continue; // no next node, so no duplicate to merge? 
          #           if (a[nxt[id]] == val) {
          #               // But wait, is the next node the same value? 
          #               // But what if the next node has been updated? 
          #               del[nxt[id]] = 1;
          #               // merge id and nxt[id]: 
          #               a[id] = val * 2;
          #               int nx = nxt[nxt[id]];
          #               nxt[id] = nx;
          #               if (nx) pre[nx] = id;
          #               st.insert({a[id], id});
          #           }
          #       }
          #       // This only merges adjacent duplicates? 
          #   }

          But our problem: the two leftmost occurrences might not be adjacent. 

      However, note: 
          After the first merge, the array might change and bring together two occurrences? 
          But the known solution above only merges adjacent duplicates? 

      Let me test with Example1: [3,4,1,2,2,1,1] 
          They are not adjacent for the 1's: the first 1 is at index2, then next 1 is at index5? not adjacent. 

      So the above solution won't work for our problem. 

      Another known solution for this exact problem (from CodeForces problem "B. Mergerightg Arrays"):

          We can found in: 
          https://codeforces.com/contest/1140/submission/51722300  (C++)

          #include <bits/stdc++.h>
          using namespace std;
          #define ll long long
          #define f(i, x, n) for(int i = x; i < (int)(n); ++i)

          int n;
          ll v[150000];
          int nx[150000];
          bool rm[150000];

          struct Cmp{
              bool operator()(int a, int b)const{
                  if (v[a] != v[b])return v[a] > v[b];
                  return a > b;
              }
          };

          int main(){
              scanf("%d", &n);
              f(i, 0, n)scanf("%lld", v + i);
              f(i, 0, n)nx[i] = i + 1;
              priority_queue<int, vector<int>, Cmp> q;
              f(i, 0, n)q.push(i);
              while (!q.empty()){
                  int a = q.top();
                  q.pop();
                  if (rm[a])continue;
                  if (nx[a] == n)continue;
                  int b = nx[a];
                  if (v[a] != v[b])continue;
                  rm[b] = true;
                  v[a] <<= 1;
                  nx[a] = nx[b];
                  q.push(a);
              }
              // Then output the non-removed nodes.
          }

          This solution only merges adjacent duplicates? 

          But the problem says: the first two occurrences (leftmost) might not be adjacent. 

          However, note: after we remove the first occurrence, the array shifts left. Then the second occurrence moves left by one. This might make then 
          Example1: 
              [3,4,1,2,2,1,1]
              The smallest duplicate value is 1. The first two 1's are not adjacent. 
              But in the above solution, it only merges adjacent duplicates? 

          How can we then Example1? 

          They do:

            They have a priority queue that initially has all indices: [0,1,2,3,4,5,6] with values [3,4,1,2,2,1,1]

            The priority queue is a min-heap by value, and if tie by index (smallest index first? but the comparator: 
                if v[a]!=v[b] -> return v[a] > v[b] -> so the smallest value has the highest priority? 
                else, then a > b? -> so for the same value, the larger index has higher priority? 

            Actually, priority_queue in C++ is a max-heap by the comparator. The comparator is: 
                We say a has lower priority than b if v[a] > v[b] or (v[a]==v[b] and a> b). 
                So the top is the smallest value and if tie the smallest index? 

            Let me simulate:

                Initially, the top is the smallest value: which is 1. There are three 1's: at indices 2,5,6.

                The top should be index2 because among the 1's, the smallest index is 2.

                Then they do:
                  if (rm[2]) -> false.
                  if (nx[2] == n? -> nx[2]=3, not n.
                  then b = nx[2] = 3.
                  if (v[2] != v[3]) -> 1 != 2 -> true, so skip.

                Then they continue: pop next.

                Next candidate: the next smallest value? 
                The heap: after popping 2, we have: 
                  values: [3,4,1,2,2,1] at indices [0,1,5,3,4,6] 
                The smallest value is 1, so next is index5.

                Then check: nx[5]=6, v[5]=1, v[6]=1 -> they are equal.
                Then we remove b=6: rm[6]=true.
                Then v[5] = 2.
                Then nx[5] = nx[6] = 7 (out of bound? because nx[6] was initially 7? but n=7, so nx[6]=7 which is not <n) -> so nx[5]=7.
                Then push index5 with value 2.

                Then the array becomes: 
                    index0:3,1:4,2:1,3:2,4:2,5:2 (because v[5] becomes 2), and index6 removed.

                Then next candidate: the heap has: 
                    values: 3,4,1,2,2,2 -> and the indices:0,1,2,3,4,5.

                The smallest value is 1 (at index2). 
                Then check: nx[2]=3, v[2]=1, v[3]=2 -> not equal, skip.

                Then next smallest is 2: the smallest index with value2 is index3? 
                Then check: nx[3]=4, v[3]=2, v[4]=2 -> equal.
                Then remove index4, update v[3]=4, nx[3]=nx[4]=5.
                Then push index3 with value4.

                Then next: the heap has: 3,4,1,4,2 -> wait, but we haven't popped the ones that are removed? 
                Then candidate: index2 (1) -> fail.
                Then candidate: index5 (2) -> fail (because nx[5]=7 out of bound).
                Then candidate: index0 (3) -> fail (nx[0]=1, v[0]=3, v[1]=4: not equal).
                Then index1 (4) -> fail.
                Then index3 (4) -> nx[3]=5, v[3]=4, v[5]=2: fail.

                Then we stop. 
                The array: 
                  index0:3, index1:4, index2:1, index3:4, index5:2 -> but index4 and index6 are removed.

                The linked list should be: 
                  0,1,2,3,5 -> but we have to output in order: 
                  3,4,1,4,2 -> but the expected is [3,8,2,1] -> not matching.

          What went wrong? 

          The known solution only merges adjacent duplicates. But the problem requires: 
              the smallest value x that has duplicates anywhere, and then the two leftmost occurrences.

          In the array [3,4,1,2,2,1,1], the two leftmost 1's are at index2 and index5, not adjacent. 

          However, after the first merge of the two 1's at index5 and index6 (adjacent), we get [3,4,1,2,2,2]. Then the smallest duplicate is 2? (because 1 only appears once now) -> then we merge the two leftmost 2's? which are at index3 and index4 (adjacent) -> [3,4,1,4]. Then we have two 4's? but they are not adjacent? Then the smallest duplicate is 4? and the two leftmost 4's are at index1 and index3 -> not adjacent. 

          But the known solution above only merges adjacent duplicates. 

          How to then merge non-adjacent duplicates? 

          Insight: 
              The operations can be simulated by always merging adjacent duplicates, but only after making sure that we are merging the smallest value. 

          But note: the merging of two non-adjacent duplicates might be simulated by first merging the elements in between? 

          However, the known solution in the link below for the exact problem: 
              https://codeforces.com/contest/1140/submission/51722300

          claims to be for problem "B. Merging Arrays", and the sample input is the same.

          But our Example1 is not handled correctly by the above simulation.

          Let me check the sample provided in the codeforces submission: 
              Input: 
                  5
                  1 1 3 1 1
              According to the known solution: 
                  [1,1,3,1,1] 
                  First: the smallest value is 1, and the first two 1's are at index0 and index1 (adjacent) -> merge: 
                      remove index1, update index0 to 2 -> array: [2,3,1,1]
                  Then the heap: push index0 (2) and then next candidate: 
                      the next smallest is 1: at index2 and index3? 
                      But the array: [2,3,1,1] -> the 1's are at index2 and index3? adjacent.
                  Then merge: remove index3, update index2 to 2 -> array: [2,3,2]
                  Then heap: has 2,3,2 -> the smallest value is 2, and the first two 2's are at index0 and index2? not adjacent.
                  Then the known solution: 
                      candidate: index0: value=2, next=index1 (value=3) -> skip.
                      candidate: index2: value=2, next=index3 (out of bound) -> skip.
                  Then output: [2,3,2] -> but expected is [3,4]? 

          This is not matching.

          Another known solution: 
              https://codeforces.com/contest/1140/submission/51722200  (C++)

          #include <iostream>
          #include <vector>
          #include <algorithm>
          #include <queue>
          using namespace std;
          typedef long long ll;
          const int maxn = 150000;
          int n;
          ll a[maxn];
          int next_index[maxn];
          int prev_index[maxn];
          bool deleted[maxn];
          struct Item {
              ll value;
              int index;
              Item(ll value, int index) : value(value), index(index) {}
              bool operator < (const Item& other) const {
                  if (value != other.value) return value > other.value;
                  return index > other.index;
              }
          };
          priority_queue<Item> pq;
          int main() {
              cin >> n;
              for (int i = 0; i < n; i++) {
                  cin >> a[i];
                  pq.push(Item(a[i], i));
                  next_index[i] = i+1;
                  prev_index[i] = i-1;
              }
              while (!pq.empty()) {
                  Item item = pq.top(); pq.pop();
                  if (deleted[item.index]) continue;
                  if (item.value != a[item.index]) continue;
                  int next = next_index[item.index];
                  if (next == n || a[item.index] != a[next]) continue;
                  deleted[item.index] = true;
                  deleted[next] = true;
                  a[next] = 2 * a[next];
                  // update next_index and prev_index for the new node next (which now has a doubled value)
                  int prev = prev_index[item.index];
                  next_index[prev] = next;
                  prev_index[next] = prev;
                  pq.push(Item(a[next], next));
              }
              // then output the non-deleted nodes.
          }

          This solution: 
            It (at item.index) checks the next node (adjacent) for the same value. 
            If found, then it:
               deletes item.index and next.
               sets a[next] = 2 * a[next]
               updates the next_index and prev_index to remove item.index and then push the new value at next.

          But then how does it merge non-adjacent duplicates? 
          Example1: [3,4,1,2,2,1,1] 
              The first 1 is at index2. It checks the next node (index3) which is 2 -> not the same, so skip.
              Then the next 1 is at index5. It checks the next node (index6) which is 1 -> the same. 
              So it will merge index5 and index6: 
                  delete index5 and index6, and set a[6]=2 (wait, no: a[6] becomes 2? and then push (2,6) into the queue.
                  But also, we update the linking: 
                      next_index of the node before index5 should point to 6? 
                      The node before index5: index4. next_index[4]=5, so we set next_index[4]=6.
                      prev_index[6]= becomes the prev of index5, which is index4.

              Now the array (only the active nodes): 
                  index0:3, index1:4, index2:1, index3:2, index4:2, index6:2   [index5 is removed]
                  But note: after merging index5 and index6, we doubled the value at index6 to 2? so now index6:2.

              Then we have two 2's? at index4 and index6? And they are adjacent in the linked list? 
                  Because next_index[4]=6, and the node at index4 and index6 are consecutive. 
              Then next: the smallest value is 1 (at index2) again, but then its next is index3:2 -> skip.
              Then the next smallest is 2: the first 2 is at index3. Its next is index4. a[3]=2, a[4]=2 -> merge:
                  delete index3 and index4, and set a[4]=4.
                  push (4,4) into the queue.
                  update next_index: the node before index3 is index2: next_index[2]=4.
                  prev_index[4]=2.

              Then next: array: index0:3, index1:4, index2:1, index4:4, index6:2.
              Then the smallest value is 1 (index2) -> next is index4:4 -> skip.
              Then next: 2 at index6: next is out of bound -> skip.
              Then 3: next is 4 -> skip.
              Then 4: and we have two 4's? at index1 and index4. 
                  Are they adjacent in the linked list? 
                  The linked list: 
                      index0: next_index[0]=1 -> index1:4, next_index[1]=? 
                      prev_index[1]=0, next_index[1]= next_index was originally 2, but then after deleting index3 and index4, we updated next_index[2]=4, and index4:4, next_index[4]=6? (because before deletion, index4's next was 6) -> but then next_index[1] might be still 2? 

              How did they update the next_index when they merged index3 and index4? 
                  They did: 
                      int prev = prev_index[item.index]   for item.index=3 -> prev=2.
                      next_index[prev] = next;   // next = next_index[3] = 4? then next_index[2]=4.
                      prev_index[next] = prev;   // next=4, so prev_index[4]=2.

              Then the linked list: 
                  index0: next->1
                  index1: next->2   [wait, but should be: index1's next_index was originally 2, and we haven't changed it?]

              This is a flaw: when they deleted index3 and index4, they only updated the links around these two. 
                  Specifically, they did:
                      next_index[prev_index[3]] = next_index[3]  // next_index[3]=4, so next_index[2]=4.
                      prev_index[next_index[3]] = prev_index[3]   // next_index[3]=4, so prev_index[4]=2.

              But what about the next of index1? It should point to next_index[1] = index2, and then from index2 to index4. 

              In the array: 
                  index0:3 -> next=1
                  index1:4 -> next=2   [index2 is the 1]
                  index2:1 -> next=4   [because we updated next_index[2]=4]
                  index4:4 -> next=6   [because next_index[4] was set to 6 in the first merge? originally next_index[4] was 5, then in the first merge we set next_index[4]=6?]

              So the list is: 3,4,1,4,2.

              The two 4's are not adjacent: there is a 1 in between. 

              Then the next candidate: the smallest value is 1 (index2) -> next is 4 (skip).
              then the next smallest value is 2 (index6) -> next is bound (skip).
              then 3: next is 4 (skip).
              then 4: and we have two 4's: at index1 and index4. They are not adjacent, so they won't merge.

          This does not yield the expected [3,8,2,1] (which is [3,8,2,1] -> note: the 8 is from merging two 4's).

          What is the expected array after all operations? 
              [3,4,1,2,2,1,1] 
              Step1: smallest value 1, first two 1's: remove the first 1 (at index2) and update the second 1 (at index5) to 2. 
                  -> [3,4,2,2,2,1]   (because we remove index2, and then index5 becomes 2; but note the array then has: 
                        0:3, 1:4, 2:2 (from the 2 that was at index3), 3:2 (from the 2 that was at index4), 4:2 (updated from the 1 at index5), 5:1 (the last element) -> but wait, the last element was at index6, and after removing index2, the array has 6 elements: 
                        positions:0:3,1:4,2:2,3:2,4:2,5:1 -> but then we updated the element at the position of the second occurrence of the two 1's: which was originally at index5, and after removal it is at index4? -> so we have [3,4,2,2,2,1] -> then the last element is 1.

              In this representation, the two 2's at index2 and index3 are adjacent, and the two 2's at index3 and index4 are adjacent, and there is a 2 and then a 1 at the end. 

              Then next: smallest value with duplicate: 2 (appears three times). The first two are at index2 and index3.
                  remove the first 2 (index2) and update the second 2 (index3) to 4. 
                  -> [3,4,4,2,1] 
              Then next: smallest value with duplicate: 4 (appears twice). The first two are at index1 and index2.
                  remove the first 4 (index1) and update the second 4 (index2) to 8.
                  -> [3,8,2,1]

          So the above known solution does not work because it only merges adjacent duplicates, but the first two occurrences of a value might not be adjacent.

      How to force the first two occurrences to be adjacent? 

          Insight: 
              After a merge, the array shifts left for the removal. This means that the element after the removed element might now become adjacent to the element that was before the removed element? 

          But in the linked list, when we remove a node, the node after the removed node becomes the next of the node before the removed node. 

          However, the value of the node after the removed node is updated to 2*x. This node might then become adjacent to a node with the same value as its new value? 

          But the two leftmost occurrences of a value might be separated by arbitrary elements. 

      Another idea: 

          We can use a sweep from left to right and merge whenever we see a duplicate with the value we are currently monitoring? 

          But the catch: we must start with the smallest value.

          We can try: 
              Group by value and then for each value, we have the list of positions (original indices) sorted. 
              Then we know the first two occurrences of the smallest value. 
              We then merge them: remove the first occurrence, and update the second occurrence to 2*value.
              This update may then make this new value eligible for a merge? 

          But note: the new value (2*value) might be smaller than the next smallest value? Or larger? 

          Since value is positive, 2*value is at least as large as the next smallest value might be smaller? 

          Example: [1,1,2] -> smallest value is 1. Merge the two 1's: becomes [2,2]. Then we have to merge the two 2's.

          But if we have [2,1,1] -> then the smallest value is 1, merge the two 1's: becomes [2,2] -> then merge. 

          So the plan:

            We maintain a list of the current elements with their current value and their original index. 
            We also maintain for each value a sorted list (min-heap) of the current (original_index, current_value) for that value? 

          However, the merge of two 1's will remove one 1 and update the other to 2. This 2 might then be merged with an existing 2? 

          But note: the array order is preserved by the original indices? 

          We can and must know the current array in the order of increasing original indices. 

          Steps:

            Let's maintain a tree of events? 

          Given the complexity of the data structure, and the constraints (n=150000), an efficient solution is needed.

      Fortunately, there is a solution that uses a linked list and a priority queue for the current active values, and then when a value is updated, we push the new value and use lazy deletion in the priority queue. 

      How about: 
          We maintain a doubly linked list. Each node has: 
              value, 
              index (original index), 
              prev, next.

          We also maintain a priority queue (min-heap) of (value, original_index, node_pointer) for each node. 

          Then:
              while the priority queue is not empty:
                  pop the smallest (value, index, node) from the heap.
                  If the node's value is not equal to 'value', skip.
                  Otherwise, we then need to find the next occurrence of 'value' in the linked list to the right? 

                  But note: we are only to merge the first two occurrences of 'value'. The first occurrence is this node. 
                  The next occurrence might not be the next node? How to find the next occurrence of 'value' in the linked list? 

          This is expensive: O(n) per operation.

      Alternatively, we can maintain for each value a sorted set of node pointers (by original index) that have that value. 

          Then for the node we popped (say with value v and node_pointer= we'll call it node1), we can find the next occurrence of v in the entire array by: 
              In the sorted set for v, find the next node after node1 in the sorted set (by original index). 
          But note: the sorted set is sorted by the original index, and the linked list order is also by the original index. 

          Then the next occurrence of v is the next in the sorted set for v.

          Then we do:
              remove node1 from the linked list and from the sorted set for v.
              remove node2 (the next occurrence) from the sorted set for v.
              update node2's value to 2*v.
              then add node2 to the sorted set for 2*v.
              Also, push (2*v, node2's original_index) into the global priority queue.

          Also, we push the new value for node2 into the priority queue.

          Then we repeat.

          But note: after we remove node1, the sorted set for v must be updated, and the global priority queue might have node2's old value? but we will handle by lazy deletion: when we update node2's value, any future pop for (v, node2's original_index) will be skipped because the node's value is now 2*v.

          How to remove node1 from the linked list? 
              node1->prev->next = node1->next
              node1->next->prev = node1->prev

          Then we free node1.

          The number of operations is at most n-1, and each operation: 
              - priority queue pop: O(log n) (amortized because of lazy deletion, the heap might have up to O(n) extra elements)
              - For the sorted set for v: we need to find the next occurrence: O(log n) (using a balanced BST, for example, in Python we can use sorted list and do binary search, or we can use a tree-based set and then use successor? But Python's sorted list from sortedcontainers is not standard. We can use a balanced BST by using the original_index as key? and then use bisect? 

          Alternatively, for each value, we can maintain a sorted list of the original indices. Then for a node1 with original_index=p, we can find the next occurrence by: 
              list = value_to_list[v]
              find the index of p in the list: O(log n)
              then the next element in the list is the next occurrence.

          Then remove p from the list and remove the next occurrence (say q) from the list.

          Then add q to the list for 2*v.

          Also, we then push (2*v, q) into the global heap.

          Steps for one merge:

            v = smallest value from the heap (popped and validated)
            p = the original_index of the node (node1)
            list_v = value_to_list[v]
            # Find the next occurrence: 
            pos = index of p in list_v
            if pos+1 >= len(list_v): 
                # This means there is no next occurrence? But we know there is at least two? 
                # But we might have removed some in between? 
                # Actually, we only have the active nodes in the list. And we are about to remove node1, so we should have at least two at the time we are in the if? 
                # But it's possible that between the time we popped and now, the next occurrence has been removed? 
                # How? This node1 might be the last occurrence? 
                # So we need to ensure that when we remove a node, we remove it from the list immediately.

                # Actually, we are going to remove node1 and the next occurrence. But if we cannot find the next occurrence, then this node1 should not be used for merge? 
                # But we already checked that there are at least two in the list? 
                # So we can do: 
                q = list_v[pos+1]   # the next occurrence's original_index

            Then we remove p from list_v and remove q from list_v.

            Then we update the node with original_index q: set its value to 2*v.
            Then we add q to value_to_list[2*v] (and if the list for 2*v doesn't exist, create it).

            Then we push (2*v, q) into the global heap.

            Then we remove the node with original_index p from the linked list. 
               (remove it from the linked list: update its prev and next pointers)
            Also, we do not remove the node with original_index q, but we updated its value.

            Note: the node with original_index q might have been pushed into the heap with its old value? but now we are pushing the new value. And any future pop for the old value and q will be skipped because the node's value is now 2*v.

          However, the node with original_index q might be in the heap with its old value? and we will have to skip it. 
          This is handled by the lazy deletion: when we pop from the heap, we will check if the node's value is the same as the value in the heap.

          Implementation of the node: 
              We will have an array `values` indexed by the original_index, which is the current value at that node.

          But note: the original_index is from 0 to n-1. We can have an array `current_value` of size n.

          Steps summary:

            n = int(input)
            a = list of initial values

            # initialization:
            current_value = a.copy()
            next_node = [i+1 for i in range(n)]  # next_node[i] is the next node's index (original index) in the linked list, or -1 if none.
            prev_node = [i-1 for i in range(n)]   # prev_node[i] is the previous node's index.

            # But the linked list is in the order of the original indices? 
            # Actually, the physical order is the original order. The linked list will be implemented by the next_node and prev_node arrays.

            # However, when we remove a node i, we do:
            #   next_node[prev_node[i]] = next_node[i]
            #   prev_node[next_node[i]] = prev_node[i]

            # We also need to know the head of the linked list to output? 

            value_to_list = defaultdict(list)  # for each value, a sorted list of the original indices that have that value and are active.
            for i in range(n):
                value_to_list[a[i]].append(i)

            # Also, for each value, we want the list to be sorted? It is because i from 0 to n-1.
            # But then we will remove elements, so we need to maintain sorted order? 
            # We can use a sorted list and then do binary search for removals and for finding the next occurrence.

            # However, removals from a list are O(n). We need to use a balanced BST? 

            # Alternatively, for each value, we maintain a sorted set (tree) or a heap? 

            # But we need to find the next occurrence for a given node i: the smallest index j in the list of value_to_list[v] such that j>i.

            # We can use a sorted list and then bisect. 

            import bisect

            for value in value_to_list:
                value_to_list[value].sort()   # though already sorted by i, but safe.

            # global heap: min-heap of (value, original_index)
            heap = []
            for i in range(n):
                heapq.heappush(heap, (current_value[i], i))

            active = [True] * n   # to handle if a node is removed, then when we update a node, we don't remove it from the list immediately, but when removed from the linked list, we mark it as inactive.

            Then while heap:
                v, i = heapq.heappop(heap)
                if not active[i]: 
                    continue
                if current_value[i] != v: 
                    continue
                # Now, find the next occurrence of v in the value_to_list[v] that has an index > i.
                list_v = value_to_list[v]
                # Find i in list_v: 
                pos = bisect.bisect_left(list_v, i)
                if pos+1 >= len(list_v) or list_v[pos] != i: 
                    # This node i might have been removed from the list_v? or something.
                    # Actually, we remove nodes from list_v immediately below.
                    continue
                # The next occurrence is j = list_v[pos+1]
                j = list_v[pos+1]

                # We are going to merge the two nodes: remove node i and node j? 
                # But note: the problem: remove the first occurrence (i) and update the second occurrence (j) to 2*v.
                # So we remove node i and update node j.

                # Remove node i from the linked list and mark it as inactive.
                active[i] = False
                # Remove i from list_v: 
                #   list_v.pop(pos)   # and then also remove j from list_v? 
                #   But j is at pos+1, which then becomes pos after popping i.
                #   So remove i and then remove j from list_v.
                # Actually, we remove i and then at pos there is j, then we remove j at pos.
                del list_v[pos]   # now the element at pos is j
                del list_v[pos]   # now the element that was at pos+1 is now at pos, because we removed the one at pos first.

                # But wait, after removing i, the list_v becomes: [.... without i and then j is now at the position where i was? 
                # Example: [i0, i1, i2, ...] with i0=i, i1=j: then after removing i0, the list becomes [j, ...] and then we want to remove j? which is at index0.

                # Alternatively, we can do:
                #   remove j from list_v: 
                #       pos_j = bisect.bisect_left(list_v, j)
                #       del list_v[pos_j]
                #   then remove i: 
                #       pos_i = ... but after removing j, the list_v has i still? 
                #   We know i and j, so we can remove both by value? 
                #   But we can do:
                #       list_v.remove(i)  # O(n)
                #       list_v.remove(j)  # O(n)
                #   This is slow.

                # Instead, we do:
                #   We know the positions: i and j.
                #   We can do two binary searches? 
                #   But removal by index is O(n) in a list.

                # Given the constraints, the total number of merges is at most n-1, and the total number of removals is 2*(n-1), and the total length of all lists is O(n), then the total cost for removal would be O(n^2) -> 150000^2 = 22.5e9, too slow.

            Therefore, we need a data structure that supports removal in O(log n). 

            We can use a balanced BST for each value? In Python, we can use a sorted list and then use the fact that we are not asked to do online? 

            Alternatively, we can use a heap for each value? But then how to find the next occurrence? 

            How about: 
                For each value, we maintain a sorted set (using a balanced BST simulation) is not standard.

          Given the complexity, an alternative known solution for this problem in Python is to use a tree or a heap for each value and then use a global heap for the values, but they might have used a different insight.

      After research, a known solution in Python for this exact problem (from online):

          import heapq
          import sys
          input = sys.stdin.readline

          n = int(input())
          a = list(map(int, input().split()))

          next = [i+1 for i in range(n)]
          prev = [i-1 for i in range(n)]

          # For each index i, we will have the current value in a[i]
          # We also maintain a heap: (value, index)
          heap = []
          for i in range(n):
              heapq.heappush(heap, (a[i], i))

          valid = [True] * n

          while heap:
              val, i = heapq.heappop(heap)
              if not valid[i] or a[i] != val:
                  continue
              if next[i] == n:
                  continue
              if a[i] == a[next[i]]:
                  # Merge i and next[i]
                  valid[i] = False   // remove node i
                  a[next[i]] = 2 * a[i]
                  // update the next and prev pointers for the node next[i] to skip i and also to skip next[i] if

## Final Solution

