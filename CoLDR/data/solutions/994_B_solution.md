# Solution for 994_B.md

Generated at: 2025-07-22 06:10:45
Model: deepseek-reasoner

## Reasoning Process

Alright, I'm going to tackle this Knights of a Polygonal Table problem. Let me start by understanding the problem thoroughly.

We have n knights, each with distinct power (p_i) and coins (c_i). Each knight can kill at most k other knights, but only if the knight's power is greater than the victim's. After killing, the knight takes all the victim's coins. For each knight, we need to compute the maximum coins they can have after killing up to k knights.

Constraints are important: n can be up to 10^5, and k is at most 10. So, k is small, which might be a key insight.

First, I think about the problem. Each knight can only kill knights with lower power. So, if we sort the knights by their power, then for any knight, only the knights before him in the sorted order (i.e., with lower power) are potential victims. But note: a knight can choose which knights to kill, as long as he doesn't exceed k kills and only kills lower-powered knights.

The goal for each knight is to maximize his coins. Initially, he has his own coins. Then, by killing others, he can add their coins. But he can only kill up to k knights. So, effectively, he can pick up to k victims from the set of knights with lower power, and he wants to maximize the sum of their coins plus his own.

So, the problem reduces to: for each knight i (after sorting by power), we want to consider all knights j with j < i (since sorted by power, j has lower power) and choose at most k of them such that the sum of their coins is maximized. Then, the answer for knight i would be his own coins plus that maximum sum.

But note: the same victim can't be killed multiple times? Actually, each kill is independent per knight, but the problem says "for each knight, determine the maximum coins he can have", meaning we're considering each knight independently. So, we don't have to worry about overlapping kills; each knight's scenario is independent.

So, the steps I see:

1. Associate each knight's power, coins, and original index (because we have to output in the original order).
2. Sort the knights by power (ascending). Then, the knight at position i can only kill knights at positions 0 to i-1.
3. For each knight i (from 0 to n-1), we want to consider the coins of knights from 0 to i-1 and choose at most k knights with the highest coins (to maximize the sum). Then, the answer for this knight would be his own coins + the sum of the top min(k, i) coins from the first i knights.

But wait: is it always the top k coins? Yes, because we can choose any set of up to k victims, so taking the k highest coin values from the knights we've seen so far would maximize the sum.

However, note that k is small (at most 10), so even if we have many knights, we only need to keep track of the top k coins from the lower-powered knights.

But the challenge: we need to do this for each knight as we traverse the sorted list. How can we efficiently maintain the top k coins from the knights we've encountered so far?

Since k is small (<=10), we can maintain a min-heap (or a list) of the top k coins. Alternatively, we can keep a list of the largest coins we've seen, and update it as we go.

Here's a plan:

- Sort the knights by power.
- Initialize an array (or list) to store the answers for each knight (in the sorted order).
- Also, we need to maintain a data structure that holds the top k coins from the knights processed so far. Since k is small, we can use a min-heap of size k. The min-heap will help us quickly access the smallest of the top k, so when we see a new knight with coins greater than the smallest in the heap, we pop the smallest and push the new one.

But note: we are traversing from the lowest power to the highest. For the current knight i, we have processed knights 0 to i-1. We have a min-heap of the top k coins from these i knights. Actually, we want the top k coins, but if i < k, then we take all.

Then, for knight i, the maximum coins he can add is the sum of all coins in the heap (if we have at least k elements, then the heap has k largest; if less, then all). But wait, no: actually, we can only take up to k victims. So, if the heap has min(i, k) elements, then the sum of all elements in the heap is the maximum sum we can get from the victims? Not exactly: because the heap maintains the top k coins from the victims, so the sum of the heap is the maximum sum for up to k victims.

But actually, we can take any set of up to k victims, so the maximum sum is the sum of the largest min(i, k) coins? Yes, but note: we are not forced to take exactly k; we can take fewer if there are fewer than k. But the heap of size k that contains the top k coins (and if there are fewer than k, then it contains all) is sufficient because the sum of all elements in the heap is the sum of the top coins we can take.

But wait: if we have more than k knights, we only take the top k. So, the heap should be of size k, and we keep the largest k coins. Then, the sum of the heap is the maximum we can get from the victims. For example, if k=2 and we have three knights with coins [1, 2, 3], then the heap would hold [2,3]? But actually, we can only take two victims, so the maximum sum is 3+2=5. However, when we process the third knight (the one with coin 3), we have seen [1,2]. Then, the heap would hold [1,2]? Then, when we get to the third knight, we haven't included 3 in the victims because we haven't processed it? Actually, we process knights in increasing order of power, so the knight with coin 3 would be processed after the others. So, when we process him, the heap for victims is from the knights with lower power (which are the ones with coins 1 and 2). Then, the heap would have the top k=2 coins, which are [1,2]? But that's not the top two; 1 and 2 are the coins, and the top two are 1 and 2? Actually, 1 and 2 are the only ones. Then, the sum is 3. Then, the knight with coin 3 would have 3 (his own) + 3 = 6? But that doesn't match: he can kill the two lower knights, so 3+1+2=6? That's correct.

But in the heap, we have [1,2] and the sum is 3. So, that's correct.

Now, when we process the next knight (if any), say a knight with power higher than 3 and coins 4. Then, the heap currently has [1,2] (from the first two) and the third knight's coin 3 is now considered as a victim? Actually, no: when we process the knight with coins 4, we have already processed the first three. But the heap for the victims should include the top k coins from all knights with lower power. So, when processing the fourth knight, we have seen three knights: coins 1,2,3. The heap (size k=2) would have the top two coins: 2 and 3? So the heap would be [2,3] and the sum is 5. Then, the fourth knight would have 4+5=9.

But how do we maintain the heap? We start with an empty heap. Then, as we process each knight (in increasing power order), we add their coins to the heap if:

- The heap has less than k elements: then add the coin.
- Or, if the heap has k elements, but the current knight's coin is greater than the smallest in the heap: then pop the smallest and push the current coin.

But note: the heap is for the victims available to the next knights. But for the current knight, we are not including himself as a victim (because he can't kill himself). So, when we process knight i, we first calculate his maximum coins: his own coins plus the sum of the heap (which contains the top k coins from knights 0 to i-1). Then, we add his coins to the heap? But no, because he can be a victim for knights with higher power. So, after processing him (for his own answer), we add his coins to the heap (if it's beneficial for the future).

So, the algorithm:

1. Create an array of tuples: (power, coins, original_index) for each knight.
2. Sort this array by power.
3. Initialize a min-heap (priority queue) to maintain the top k coins from the knights processed so far (for victims). Actually, we'll maintain a heap that holds the top k coins from the knights we've seen. But initially, it's empty.
4. Also, initialize an array `ans` of zeros (or None) of length n, which we will update for each knight in sorted order, and then reorder by original index for output.
5. Additionally, we need to keep track of the running sum of the heap to avoid recalculating the sum each time? Because recalculating the sum of the heap by iterating would be O(k) per knight, which is acceptable since k is small (10), but we can also maintain a variable `heap_sum` that tracks the current sum of the heap.

But maintaining the heap and updating the sum:

- When we add a new coin value to the heap:
   - If the heap has less than k elements, we push the coin and add it to `heap_sum`.
   - Else, if the coin is greater than the smallest in the heap (heap[0]), then:
        pop the smallest (min_value) from the heap, subtract min_value from `heap_sum`.
        push the new coin, add it to `heap_sum`.
- Then, for the current knight (before adding his coins to the heap), we can compute his total coins as: his own coins + heap_sum.

But wait: the heap at the time of processing knight i contains the top k coins from the knights 0 to i-1. Then, we calculate his total as above, and then we add his coins to the heap for future knights.

However, note that the current knight is not a victim for himself, so we don't add him until after we've computed his answer.

So, step-by-step for each knight i (in sorted order):

- Compute the current answer for knight i: `ans_i = c_i + heap_sum`
- Then, update the heap with `c_i` (so that future knights can consider him as a victim).

But what about the very first knight? When i=0, the heap is empty, so heap_sum=0. Then, his answer is his own coins. Then, we add his coins to the heap. Since k might be 0? Then, we don't add? Actually, k is the maximum kills, but the heap we are maintaining is for the top k coins, so if k==0, we shouldn't add any coins to the heap. So, we need to consider:

If k==0, then no knight can kill anyone. Then, each knight's answer is just his own coins.

But in the algorithm, we can handle k=0 by simply skipping the heap update if k==0? Actually, we can set up the heap to have max size k. Then, if k==0, the heap remains empty, and heap_sum=0 always. So, that works.

So, the steps:

- Create list `knights = [(p0, c0, 0), (p1, c1, 1), ...]`
- Sort `knights` by p (power)
- Initialize:
   - min-heap `heap` (using heapq, which is a min-heap in Python; but we want to keep the smallest at the top so we can pop it when we have too many)
   - `heap_sum = 0`
   - `ans = [0] * n`
- For each knight in the sorted list (from low power to high power):
   - `current_ans = knight.coins + heap_sum`
   - Set `ans[knight.original_index] = current_ans`
   - Then, update the heap with `knight.coins`:
        If we have less than k elements in the heap, push `knight.coins` and add to `heap_sum`.
        Else, if `knight.coins > heap[0]` (the smallest in the heap) and k>0:
             pop the smallest from the heap, subtract that from `heap_sum`
             push `knight.coins`, add to `heap_sum`
        Else, do nothing? Actually, if k==0, we skip. And if we have k elements and the new coin is not greater than the smallest, we don't add.

But note: if k==0, we skip the heap update. Alternatively, we can condition the heap update on k>0. But the problem says k can be 0.

So, the code for updating the heap:

```python
if k > 0:
    if len(heap) < k:
        heapq.heappush(heap, knight_coins)
        heap_sum += knight_coins
    else:
        if knight_coins > heap[0]:
            removed = heapq.heappushpop(heap, knight_coins)
            heap_sum += knight_coins - removed
```

This is efficient and keeps the heap size at k (or less, if we haven't seen k knights yet).

But one more thing: when we have exactly k elements, we only add the current coin if it is greater than the smallest. Then, we replace the smallest with the current coin, updating the heap_sum accordingly.

Now, after processing all knights, we have the `ans` array with the answers in the original index order? Because we stored the answer at the original index.

Let me test with the examples.

Example 1:
Input: n=4, k=2
Powers: [4,5,9,7] -> distinct, so we can sort.
Coins: [1,2,11,33]

Step 1: create knights:
(4,1,0), (5,2,1), (9,11,2), (7,33,3)

Sort by power: 
(4,1,0), (5,2,1), (7,33,3), (9,11,2)

Now, traverse:

i=0: knight (4,1,0)
   heap is empty, heap_sum=0.
   ans[0] = 1 + 0 = 1.
   Then, update heap: since k=2>0 and heap len=0<2 -> push 1. Now heap=[1] (min-heap), heap_sum=1.

i=1: knight (5,2,1)
   ans[1] = 2 + heap_sum(1) = 3.
   Then, update heap: len(heap)=1<2 -> push 2. Now heap=[1,2] (min-heap: the top is 1). heap_sum=3.

i=2: knight (7,33,3)  [Note: the third knight in sorted order is the one with power 7 and index 3]
   ans[3] = 33 + heap_sum(3) = 36.
   Then, update heap: current coin 33. Compare 33>heap[0]=1 -> replace: pop 1, push 33. Now heap=[2,33] -> min is 2. heap_sum = 3 -1 +33 = 35? But 2+33=35, yes.

i=3: knight (9,11,2)
   ans[2] = 11 + heap_sum(35) = 46.
   Then, update heap: current coin 11. Compare 11>heap[0]=2? Yes. So, pop 2, push 11. Now heap=[11,33] -> min is 11. heap_sum = 35 -2 +11 = 44.

Then, the answers per original index:
ans[0]=1, ans[1]=3, ans[2]=46, ans[3]=36 -> [1,3,46,36] which matches the example.

Example 2:
Input: 5 1
Powers: 1,2,3,4,5 -> sorted is same.
Coins: 1,2,3,4,5

k=1.

Process:

knight0: (1,1,0)
   ans[0]=1
   heap: push 1 -> heap=[1], heap_sum=1.

knight1: (2,2,1)
   ans[1]=2 + 1 = 3
   update: push 2? But k=1, so we have to check: heap has 1, and 2>1 -> replace: pop 1, push 2. heap_sum becomes 2.

knight2: (3,3,2)
   ans[2]=3+2=5
   update: 3>2? yes, replace: pop 2, push 3. heap_sum=3.

knight3: (4,4,3)
   ans[3]=4+3=7
   update: replace 3 with 4 -> heap_sum=4.

knight4: (5,5,4)
   ans[4]=5+4=9

Output: [1,3,5,7,9] as expected.

Example 3: trivial.

So, the algorithm seems correct.

Time complexity: O(n log k) because each insertion into the heap is O(log k), and we do it n times. Since k<=10, log k is about 3.3, so it's effectively O(n). Space is O(k) for the heap and O(n) for storing the knights and the answer.

Now, what about the case when k=0? Then, we skip the heap update, so heap_sum remains 0, and each knight's answer is his own coins. Correct.

Another edge: n=1, k=0. Then, we have one knight: output [c0].

What if k=0 and n>1? Then, each knight's answer is just his own coins.

Also, what if there are negative coins? The problem says c_i >=0, so no negative.

But note: if k=0, we don't update the heap, so we avoid the heap operations.

Implementation details:

- We'll use heapq. Note: heapq is a min-heap. We want to keep the smallest at the top to compare and replace if we have a larger coin.

Code:

```python
import heapq
from typing import List

class Solution:
    def solve(self, n: int, k: int, p: List[int], c: List[int]) -> List[int]:
        # If no knights, return empty list?
        if n == 0:
            return []
        # Create list of tuples (power, coins, index)
        knights = [(p[i], c[i], i) for i in range(n)]
        knights.sort(key=lambda x: x[0])  # sort by power
        
        # Initialize
        heap = []
        heap_sum = 0
        ans = [0] * n
        
        # Traverse each knight in increasing order of power
        for power, coin, idx in knights:
            # The current knight's total coins: his own + sum of the top k (or all if less than k) from lower-powered knights
            ans[idx] = coin + heap_sum
            
            # Now, add this knight's coins to the heap for future knights, if k>0
            if k > 0:
                # If we haven't reached k elements, push and update sum
                if len(heap) < k:
                    heapq.heappush(heap, coin)
                    heap_sum += coin
                else:
                    # If current coin is larger than the smallest in the heap (which is heap[0])
                    if coin > heap[0]:
                        # Replace the smallest with the current coin
                        removed = heapq.heappushpop(heap, coin)
                        heap_sum += coin - removed
        return ans
```

But wait: in the example, the knight with higher power is processed later, and we add his coins to the heap only after computing his own answer. That is correct because he can be a victim for knights with even higher power, but not for himself.

I think this is the optimal solution. Since k is small (<=10), the heap operations are O(1) practically. The sorting is O(n log n), which is acceptable for n=10^5.

Alternative approaches:

1. Brute force: For each knight, consider all knights with lower power and select the top k coins. But that would be O(n^2) which is too slow for n=10^5.

2. We could use a Fenwick tree or segment tree to get the top k sums? But k is small, so maintaining a heap as we go is simpler.

3. Another idea: use a BST or balanced BST? But k is small, so a heap is sufficient.

So, this solution is efficient.

Let me run through the example 1 again with the code:

knights sorted: [(4,1,0), (5,2,1), (7,33,3), (9,11,2)]

- For (4,1,0): ans[0]=1. Then, heap becomes [1] (min-heap), heap_sum=1.
- For (5,2,1): ans[1]=2+1=3. Then, since k=2 and heap size=1<2, push 2: heap=[1,2] (but min-heap, so the smallest is at the front; heap[0]=1). heap_sum=3.
- For (7,33,3): ans[3]=33+3=36. Then, compare 33>heap[0]=1 -> replace: pop 1, push 33. Now heap=[2,33] -> heap[0]=2. heap_sum= 3 -1 +33 = 35.
- For (9,11,2): ans[2]=11+35=46. Then, compare 11>heap[0]=2 -> replace: pop 2, push 11. heap=[11,33] -> heap[0]=11. heap_sum=35-2+11=44.

Output: ans[0]=1, ans[1]=3, ans[2]=46, ans[3]=36 -> [1,3,46,36].

Perfect.

Now, edge: k=0. Then, for each knight, ans is just coin, and heap remains empty.

Another edge: k=1, and we have two knights: first with coin 10, second with coin 5. The second knight can kill the first? Only if his power is higher. So, sorted by power: first knight (p1,10), second (p2,5) with p1<p2. Then:

- First knight: ans = 10. Then, push 10 to heap? k=1: yes. heap=[10], heap_sum=10.
- Second knight: ans = 5 + 10 = 15. Then, update: 5>10? no, so do nothing. Then, output: [10,15]. Correct: the second knight kills the first, so 5+10=15.

But what if the second knight's coin is 20? Then:

- First knight: ans=10, heap=[10], heap_sum=10.
- Second knight: ans=20+10=30. Then update: 20>10? yes, so replace: pop 10, push 20. Now heap=[20], heap_sum=20.

But after that, if there's a third knight with power higher and coin 1? Then:

ans = 1 + 20 = 21.

But the third knight can kill the second knight? No, because the second knight has higher power? Actually, sorted by power: the third knight would be after the second. So, when we process the third knight, the heap contains the top k=1 coins from the lower knights (which are the first two). The heap has 20 (from the second knight). So the third knight can kill the second knight (if his power is higher) and take 20 coins? Then total 21.

But what if the third knight has power between the first and second? Then the sorted order would be first, third, second. Then:

knights: (p1,10,0), (p3,1,2), (p2,20,1)

Then:

- First: ans[0]=10. heap=[10], heap_sum=10.
- Third: ans[2]=1+10=11. Then, update: 1<10? no, so don't add? Then heap remains [10], heap_sum=10.
- Second: ans[1]=20 + 10? =30. Then update: 20>10, so replace: heap becomes [20], heap_sum=20.

But the second knight can kill the first and the third? Only if his power is higher than both. But in the sorted order, the third has lower power than the second? Then, the second knight can kill both? But k=1? So he can only kill one. Then, he should choose the one with the most coins: the first knight has 10, the third has 1. So he kills the first knight and gets 20+10=30. Correct.

But in our algorithm, when processing the second knight (which is last in sorted order), the heap contains the top 1 coins from the lower knights: which are [10] (from the first) and the third knight's coin 1 was not added because it was less than 10? Actually, in the update for the third knight, we didn't add it because we only keep the top k=1, and 1<10 so we skip. Then the heap remains [10]. So the second knight gets 20+10=30.

But if k were 2, then:

- First knight: ans[0]=10, heap=[10] (size=1<2, so add), heap_sum=10.
- Third knight: ans[2]=1+10=11. Then, update: heap has size=1<2, so we add 1: heap=[1,10] (min-heap: top=1), heap_sum=11.
- Second knight: ans[1]=20 + 11? =31. Then, update: 20>1, so replace: pop 1, push 20. heap=[10,20], heap_sum=30.

But the second knight can kill two knights: the first and the third, so 20+10+1=31? Then, that matches.

But wait, the second knight can kill two, but the coins from the victims are 10 and 1, so total 11. Then 20+11=31. Correct.

So, the algorithm works.

I think this is the solution.

Now, write the code accordingly.

Note: we must be cautious that if k==0, we skip the heap update. Also, if k==0, we don't even need to initialize the heap? But we can handle it as above.

Let me code accordingly.

One more thing: if k==0, then the heap remains empty, heap_sum=0, so each knight's answer is his own coin. Correct.

I'll write the code as above.

But what if k is 0? Then, we don't do any heap operations. So, the heap remains empty and heap_sum remains 0. Then, each knight's answer is his own coin. Correct.

Test with k=0 and n=2: powers [2,1] and coins [10,20]. But powers are distinct, so we sort: first knight (1,20,1), second (2,10,0). Then:

- First knight: ans[1]=20. Then, if k>0 we would add 20, but k=0, so skip. heap remains empty, heap_sum=0.
- Second knight: ans[0]=10+0=10.

But the second knight (higher power) can kill the first? But k=0, so no. So the answers are 20 for the first and 10 for the second. But in the output, we have to output in the original index order: original index 0 is the knight with power 2, so ans[0]=10; index1 is the knight with power 1, so ans[1]=20. Output: [10,20]. Correct.

So, the code should be:

```python
import heapq
from typing import List

class Solution:
    def solve(self, n: int, k: int, p: List[int], c: List[int]) -> List[int]:
        # If n is 0, return empty list
        if n == 0:
            return []
        knights = []
        for i in range(n):
            knights.append((p[i], c[i], i))
        knights.sort(key=lambda x: x[0])  # sort by power
        
        # We'll use a min-heap to maintain the top k coins from the knights we've seen so far.
        heap = []
        heap_sum = 0  # current sum of the heap
        ans = [0] * n
        
        for power, coin, idx in knights:
            # For the current knight, the total coins = his own coins + heap_sum (which is the sum of the top min(k, number_of_previous) coins)
            ans[idx] = coin + heap_sum
            
            # If k is 0, we skip the heap update because no knight can kill more than 0
            if k == 0:
                continue
                
            # If we haven't reached k knights, we add the current coin to the heap
            if len(heap) < k:
                heapq.heappush(heap, coin)
                heap_sum += coin
            else:
                # Only if the current coin is greater than the smallest in the heap, we replace
                if heap and coin > heap[0]:
                    # Push the current coin and pop the smallest
                    removed = heapq.heappushpop(heap, coin)
                    heap_sum += coin - removed
                # else, we don't add the current coin to the heap
        return ans
```

But note: the condition `if heap and ...` is redundant because if k>0 and we are in the else branch, then heap must have k elements (so non-empty). So we can just do:

```python
                if coin > heap[0]:
                    removed = heapq.heappushpop(heap, coin)
                    heap_sum += coin - removed
```

But what if k==0? We skip the entire update. So, it's safe.

Let me test with k=1 and two knights: (1,10,0) and (2,20,1). Then:

- First knight: ans[0]=10. Then, heap=[10], heap_sum=10.
- Second knight: ans[1]=20+10=30. Then, since k=1 and heap has one element, we check: 20>10 -> replace: heap becomes [20], heap_sum=20.

But then, if there's a third knight (3,5,2): 
   ans[2]=5+20=25. Then, update: 5>20? no, so skip.

But the third knight (power 3) can kill the second knight (power2) and get 20 coins? Then total 5+20=25. Correct. He cannot kill the first because the second knight is the one with the highest coin and he can only kill one. So he kills the second knight.

But if k=2 for the third knight? Then we would have:

After the second knight, heap=[20] (but k=2, so we actually would have added the second knight without popping? Actually, no: when processing the second knight, k=1? So we replaced. Then the heap has only the top 1. But for the third knight, we are allowed to kill up to 2? But k=1 for each knight? The problem says each knight can kill no more than k knights. So k is the same for all.

In the problem, k is given and is the same for every knight.

So, the algorithm is consistent.

I think we're good.

Let me run the code for the examples.

Example 1: as above, works.

Example 2: as above, works.

Example 3: n=1, k=0: 
   knight: (2,3,0)
   ans[0]=3. Then, k=0, so skip heap update. Output [3].

All set.

I'll write the final code accordingly.

## Final Solution

### Approach
The problem involves determining the maximum coins each knight can accumulate by killing up to `k` other knights with lower power. The solution involves the following steps:

1. **Problem Analysis**: Each knight can only kill knights with strictly lower power. The goal is to maximize the coins collected by each knight by selecting up to `k` victims with the highest coins from those with lower power.

2. **Key Insight**: By sorting the knights in increasing order of their power, we ensure that for any knight, only the knights processed before it (with lower power) are potential victims. This allows us to efficiently maintain the top `k` coins from the knights encountered so far.

3. **Algorithm Selection**: 
   - **Sorting**: Knights are sorted by their power to process them from weakest to strongest.
   - **Min-Heap**: A min-heap of size `k` is used to dynamically track the top `k` coins from the knights processed so far. The smallest coin in the heap is replaced if a larger coin is encountered, ensuring the heap always contains the highest coins.
   - **Sum Tracking**: The sum of coins in the heap is maintained in constant time during heap updates to efficiently compute the total coins each knight can collect.

4. **Complexity Analysis**:
   - **Time Complexity**: Sorting the knights takes O(n log n) time. Processing each knight involves heap operations (O(log k) per knight), leading to an overall complexity of O(n log n + n log k). Given `k` is small (≤ 10), this simplifies to O(n log n).
   - **Space Complexity**: O(n) for storing the knights and answers, and O(k) for the heap, resulting in O(n) space.

### Solution Code
```python
import heapq
from typing import List

class Solution:
    def solve(self, n: int, k: int, p: List[int], c: List[int]) -> List[int]:
        if n == 0:
            return []
        knights = [(p[i], c[i], i) for i in range(n)]
        knights.sort(key=lambda x: x[0])
        
        heap = []
        heap_sum = 0
        ans = [0] * n
        
        for power, coin, idx in knights:
            ans[idx] = coin + heap_sum
            
            if k == 0:
                continue
                
            if len(heap) < k:
                heapq.heappush(heap, coin)
                heap_sum += coin
            else:
                if coin > heap[0]:
                    removed = heapq.heappushpop(heap, coin)
                    heap_sum += coin - removed
                    
        return ans
```

### Explanation
1. **Initialization**: The knights' powers, coins, and original indices are stored in a list of tuples and sorted by power.
2. **Processing Knights**: For each knight in sorted order:
   - **Current Maximum Coins**: The knight's total coins are computed as the sum of his own coins and the sum of coins from the top `k` victims (maintained in the heap).
   - **Heap Update**: The knight's coins are added to the heap if they are among the top `k` coins encountered so far. The heap is updated to maintain only the top `k` coins by replacing the smallest coin if necessary.
3. **Output**: The results are stored in an array according to the knights' original indices and returned.

This approach efficiently leverages sorting and a min-heap to dynamically track the best victims for each knight, ensuring optimal performance even for large inputs.