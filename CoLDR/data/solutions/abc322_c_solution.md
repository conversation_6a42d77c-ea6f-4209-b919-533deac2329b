# Solution for abc322_c.md

Generated at: 2025-07-22 06:30:45
Model: deepseek-reasoner

## Reasoning Process

Alright, so I've got this problem to solve. Let me try to understand it step by step. 

The problem is about a festival that lasts for N days, and on some of these days, fireworks are launched. Specifically, there are M days when fireworks happen, and these days are given in a list A. Importantly, the last day of the festival (day N) always has fireworks, so A_M = N. 

For each day i from 1 to N, I need to find out how many days later the next fireworks will occur. If fireworks happen on day i, then the answer for that day is 0. Otherwise, it's the difference between the next fireworks day and day i.

Let me look at the examples to make sure I get it.

**Example 1:**
Input: N=3, M=2, A = [2,3]
Output: [1, 0, 0]
- For day 1: the next fireworks is on day 2, which is 1 day later -> output 1.
- For day 2: fireworks on the same day -> 0.
- For day 3: same as above -> 0.

**Example 2:**
Input: N=8, M=5, A = [1,3,4,7,8]
Output: [0, 1, 0, 0, 2, 1, 0, 0]
- Day 1: fireworks on day 1 -> 0.
- Day 2: next fireworks on day 3 -> 3-2=1.
- Day 3: 0.
- Day 4: 0 (fireworks on day 4).
- Day 5: next fireworks is day 7 -> 7-5=2.
- Day 6: next fireworks is day 7 -> 7-6=1.
- Day 7: 0.
- Day 8: 0.

Okay, that makes sense. Now, the constraints are that both N and M can be up to 200,000. So, I need an efficient solution because a brute-force approach would be too slow.

### Step 1: Problem Understanding and Modeling
The problem requires, for each day i (1-indexed) in the range 1 to N, the minimum non-negative integer d such that day i+d is in the set of fireworks days. If i is in A, then d=0; otherwise, d is the distance to the next fireworks day.

Key points:
- The set of fireworks days is given as a sorted list A (strictly increasing).
- The last day N is always in A.

Constraints: N and M up to 200,000, so O(N log M) or O(N) solutions are acceptable, but O(N*M) would be too slow.

### Step 2: Multiple Solution Exploration

1. **Brute Force Solution:**
   For each day i from 1 to N, iterate through the list A until we find the first fireworks day that is >= i. Then, the answer is (found_day - i).
   However, worst-case, if we do a linear scan for each day, the time complexity is O(N*M). Since both N and M can be 200,000, worst-case O(40e9) operations which is too slow. So, we need something better.

2. **Optimized Solution using Binary Search:**
   Since the list A is sorted, for each day i, we can do a binary search in A to find the smallest fireworks day >= i. 
   Steps:
      - For each day i (from 1 to N):
          - Use binary search (like bisect_left) on A for the value i.
          - Let idx be the index in A such that A[idx] >= i.
          - Then, the answer for day i is A[idx] - i.

   Time Complexity: For each of the N days, we do a binary search in a list of size M. Each binary search is O(log M). So total time is O(N log M). Given that N and M are up to 200,000, log M is about 18 (since 2^18 ~ 260,000), so 200,000 * 18 = 3.6e6 operations, which is acceptable in Python.

   Space Complexity: O(N) for the output and O(M) for storing A. That's acceptable.

3. **Precomputation with Backward Pass:**
   Another approach is to precompute the next fireworks day for each day in a single pass. 
   Steps:
      - Create an array `ans` of size N+1 (1-indexed) to store the result for each day.
      - Since the fireworks days are given, we know that on days in A, the answer is 0.
      - For the days not in A, we can traverse backwards from the last day to the first, keeping track of the last fireworks day we encountered.
        Specifically:
          - Start from day = N down to 1.
          - Maintain a variable `last_event` which is the next fireworks day after the current day? Actually, if we traverse backwards, we can set the next fireworks day for each day.
          - Alternatively, we can set an array `next_event` of size N+1, initially set to a large number or -1.
          - Since we know the fireworks days, set `ans[day] = 0` for each day in A.
          - Then, traverse backwards from N to 1:
              - If the current day is a fireworks day, set `last = day` and set `ans[day] = 0`.
              - Else, the answer for day i is `last - day`? But wait, if we are going backwards, then from a non-fireworks day, the next fireworks day is the last fireworks day we passed. Actually, when traversing backwards, the next fireworks day for day i is the same as the next fireworks day for day i+1, unless day i+1 is a fireworks day? 

   Alternatively, we can do:
      - Let `res = [0] * (N+1)`
      - Mark the fireworks days: for each a in A, set `res[a] = 0`.
      - Then, traverse from the end backwards to the start:
          - Let `next` be an array that holds the next fireworks day for each day. But we can avoid an extra array.
      - Idea: 
          - We can set an array `ans` for each day.
          - Start from the last day (day N) and go backwards to day 1.
          - Initialize a variable `last_event` to a big number (or -1) meaning we haven't seen a fireworks day yet. But since the last day is a fireworks day, we can set `last_event = N` at the start? Actually, we start at day N, which is a fireworks day. So:
          - For day i from N-1 down to 1:
              - If day i is a fireworks day, then `ans[i] = 0` and update `last_event = i`.
              - Else, `ans[i] = ans[i+1] + 1`? Not exactly, because the next fireworks day might not be consecutive.

   Actually, if we traverse backwards, we can do:
      - Create an array `result` of size N+1 (1-indexed) for days 1 to N.
      - We know that for each day i in A, `result[i] = 0`.
      - We'll have a variable `next_event` that tracks the next upcoming fireworks day. Since we traverse backwards, when we see a fireworks day, we reset `next_event` to that day. For a non-fireworks day, the next fireworks day is `next_event`, so the answer is `next_event - current_day`.
      - Steps:
          - Initialize an array `ans` of size N+1 (index 0 unused, using 1-indexing).
          - Mark the fireworks days: for a in A, set `ans[a] = 0`.
          - Set `next_event = None` (or a large number) and start from the last day (day N) and go backwards to day 1.
          - Since we know day N is a fireworks day, we can set `next_event = N` at day N.
          - Then for day = N-1 down to 1:
              - If day is a fireworks day: set `ans[day] = 0` and update `next_event = day`.
              - Else: `ans[day] = next_event - day`.

   But wait, this requires knowing which days are fireworks days. We can create a boolean list `is_event` of size N+1, initially all False. Then, for each a in A, set `is_event[a] = True`.

   Time Complexity: O(N) for the backward pass and O(M) for marking the fireworks days. So total O(N), which is better than the binary search method (O(N log M)).

   Space Complexity: O(N) for the `is_event` array and the `ans` array. That's acceptable since N is 200,000.

4. **Using Pointers:**
   Alternatively, we can use two pointers. Since the list A is sorted, we can iterate through the days from 1 to N and simultaneously traverse the list A to find the next fireworks day for each day.

   Steps:
      - Initialize a pointer `ptr` to 0 (pointing to the first element in A).
      - For day i from 1 to N:
          - If A[ptr] == i, then answer is 0, and then we can increment ptr (because the next fireworks day after this might be the next in A? But wait, for the next day, we might still have the same or next fireworks day). Actually, we should not increment ptr until we have passed the current fireworks day? 
          - Alternatively, for each day i, we want the smallest A[j] >= i. We can have a pointer that moves only when the current fireworks day is less than i? Actually, we can do:

      - Let ptr = 0 (current index in A)
      - For i from 1 to N:
          - If ptr < M and A[ptr] == i:
              - Then ans[i] = 0
              - ptr += 1   # because the next fireworks day is the next in A?
          - Else:
              - Then the next fireworks day is A[ptr] (if ptr < M) and the answer is A[ptr] - i.

   But wait, if we haven't reached a fireworks day, then ptr might not have been advanced. For example, in the input [8,5] and A=[1,3,4,7,8]. 
   Let's simulate:
      - i=1: A[0]=1 -> so ans[1]=0, then ptr becomes 1.
      - i=2: now A[ptr]=A[1]=3, which is > 2, so ans[2]=3-2=1 -> correct.
      - i=3: A[ptr]=3 -> ans[3]=0, ptr becomes 2.
      - i=4: A[ptr]=4 -> ans[4]=0, ptr becomes 3.
      - i=5: A[ptr]=7 -> ans[5]=7-5=2 -> correct.
      - i=6: A[ptr]=7 -> ans[6]=1 -> correct.
      - i=7: A[ptr]=7 -> ans[7]=0, ptr becomes 4.
      - i=8: A[ptr]=8 -> ans[8]=0, ptr becomes 5.

   This seems to work. However, note that we must ensure that ptr is always pointing to the next fireworks day that is >= current day i. In the above, when we are at i=2, ptr is at 1 (A[1]=3) which is the smallest in A that is >=2. Then at i=3, we check and see that A[ptr] is 3, so we set to 0 and advance ptr. Then for i=4, we have ptr at 2 (A[2]=4), which is the next.

   But what if we skip some fireworks? For example, if we have multiple consecutive fireworks? Actually, the list A is sorted and we advance ptr only when we hit a fireworks day. So, for non-fireworks days, we don't advance ptr. 

   However, what if we have a gap? For example, if A = [1, 5] and N=5. Then:
      - i=1: ans=0, ptr=1 (now pointing to 5).
      - i=2: A[ptr]=5 -> ans=3 (5-2=3).
      - i=3: A[ptr]=5 -> ans=2.
      - i=4: A[ptr]=5 -> ans=1.
      - i=5: ans=0, ptr=2 (end).

   Correct.

   Time Complexity: O(N) because we iterate through each day once and the pointer ptr moves at most M steps (so total iterations N+M, which is O(N)). 

   Space Complexity: O(N) for the answer, and O(M) for A. We don't need extra space for a boolean array.

   This is more efficient than the binary search method because it's O(N) and avoids the logarithmic factor.

### Step 3: Optimal Solution Selection and Implementation

Now, comparing the three approaches:
1. Brute force: O(N*M) -> too slow.
2. Binary search: O(N log M) -> acceptable, but we can do better.
3. Backward pass: O(N) but requires an additional boolean array of size N -> O(N) space, and O(N) time.
4. Two pointers: O(N) time and no extra space beyond the input and output.

The two-pointer method seems the most efficient in both time and space. However, we must note that the two-pointer method traverses from day 1 to day N, and the pointer for A only moves forward. This is efficient and straightforward.

But we must ensure that we don't run out of fireworks days. The problem states that the last day (N) is always in A, so when i reaches N, we will have ptr at the last element of A (which is N). So, we are safe.

Let me write the steps for the two-pointer method:

   - Initialize an empty list `res` for results (size N, but we'll build it).
   - Let ptr = 0 (index for A)
   - For day i from 1 to N:
        - If ptr < M and A[ptr] == i:
            - Append 0 to res for this day.
            - Increment ptr by 1.
        - Else:
            - The next fireworks day is A[ptr] (since ptr is pointing to the smallest fireworks day >= i, because we haven't advanced ptr beyond that). But note that if we haven't advanced ptr, it's because we haven't encountered the next fireworks. Actually, ptr is always pointing to the next upcoming fireworks day that hasn't been passed yet.
            - So, the answer is A[ptr] - i.

   However, what if ptr is beyond M? But since the last day is in A, when we process the last day, we set ptr to M-1 and then increment to M, but then we break. Actually, for days beyond the last fireworks? But the last fireworks is on day N, and we process days only from 1 to N. Also, when i = N, we must have A[ptr] == N (because we haven't skipped it? Actually, when we get to i=N, we check: if ptr < M and A[ptr]==N? Yes, because we have A[M-1] = N (since A is 0-indexed in code, but the last element is at index M-1). Then we set to 0 and increment ptr to M, which is beyond the list. But for days before N, we always have ptr < M until we hit the last day.

   Actually, we can avoid checking `ptr < M` in the else part? Because the last day is always in A, so when we are at the last day, we set ptr to M and then the loop ends. But for days after the last fireworks? There are none because the festival ends at day N. So, we are safe.

   However, we must check that ptr doesn't go beyond M. In the else part, if ptr >= M, then there is no next fireworks? But the problem states that the last day has fireworks, so for all days i from 1 to N, there is always a fireworks on or after i (at least on day N). Therefore, ptr will always be < M until the last day, and then when i=N, we hit the fireworks and increment ptr to M. So, for i from 1 to N-1, ptr remains < M.

   Therefore, the else part only runs when ptr < M, because if ptr reached M, then we are beyond the last fireworks day, but that only happens after processing day N. So, the condition in the else part: we can assume ptr < M.

   But in the if condition, we have `if ptr < M and A[ptr] == i`. So, for the else part, we know that either ptr>=M (which shouldn't happen for i < N) or A[ptr] != i. Actually, we don't need to check ptr in the else part? Because if ptr < M, then A[ptr] is the next fireworks day. But if ptr >= M, then we have no fireworks day? But the problem guarantees fireworks on the last day, so for i <= N, we should have a fireworks day. And we process i from 1 to N. So, when i is N, we will have A[ptr] == N (if ptr < M) and we set it to 0 and increment ptr. Then the loop ends.

   So, for i from 1 to N-1, ptr will be less than M. Therefore, in the else part, we can safely compute A[ptr] - i.

   However, to be safe, we can structure the code to check only in the if condition and in the else part, we assume that ptr is valid. But what if M==0? The constraints say M>=1 because A_M = N. So M>=1. Therefore, ptr starts at 0 and only after processing the last fireworks (at N) we set ptr to M, which is beyond.

   So the code:

        res = []
        ptr = 0
        for i in range(1, N+1):
            if ptr < M and A[ptr] == i:
                res.append(0)
                ptr += 1
            else:
                # Since ptr must be < M at this point (because if ptr==M, then we shouldn't be here because the last day is N and we break after that)
                # But we can check: if ptr >= M, then we have a problem? However, due to the condition above, if ptr>=M, we skip the if. But the last day N must have been handled? Actually, when i=N, if we haven't processed the last fireworks, then we will be in the if condition. So for i < N, ptr < M is guaranteed.
                res.append(A[ptr] - i)

   But wait, what if we have a day i that is beyond the current A[ptr]? For example, if we have A = [1, 5] and at i=2, A[ptr] is 5, so we do 5-2=3. That's correct. But what if we have a day i that is after the last fireworks? That can't happen because the last day is N and we stop at N.

   However, there's a flaw: at i=1, we set res[0]=0 and ptr=1. Then for i=2, we have A[ptr]=5, so we append 3. Then i=3: we are still at ptr=1, so we append 5-3=2. Then i=4: 5-4=1. Then i=5: we check: ptr=1 < M? M=2, so yes, and A[1]=5==5 -> so append 0 and ptr=2. Then i=6? But we stop at N=5. So that's correct.

   But what if we have multiple fireworks? For example, A=[1,2,5] and N=5. 
        i=1: A[0]==1 -> res=[0], ptr=1.
        i=2: A[1]==2 -> res=[0,0], ptr=2.
        i=3: else -> A[ptr]=A[2]=5 -> 5-3=2 -> res.append(2)
        i=4: else -> A[2]=5 -> 1 -> res.append(1)
        i=5: if -> res.append(0), ptr=3.

   Output: [0,0,2,1,0] -> which is correct.

   But the problem states that the fireworks days are strictly increasing, so no duplicates.

   Therefore, the two-pointer method is efficient and straightforward.

   Alternatively, we can use the backward pass without a boolean array? Actually, we can avoid the boolean array by using the list A. But the two-pointer method is O(N) and doesn't require extra space beyond the output.

   So, I'll go with the two-pointer method.

   Implementation details:
      - The input A is given as the list of days, which are 1-indexed.
      - We iterate i from 1 to N.

   Code structure:

        def solve(N, M, A):
            res = []
            ptr = 0
            for i in range(1, N+1):
                if ptr < M and A[ptr] == i:
                    res.append(0)
                    ptr += 1
                else:
                    res.append(A[ptr] - i)
            return res

   Let me test with the examples.

   Example 1: N=3, M=2, A=[2,3]
        i=1: 
            ptr=0 <2 -> A[0]=2 !=1 -> so else: res.append(2-1=1)
        i=2:
            ptr=0 -> but wait, after i=1, we didn't change ptr. Then for i=2: ptr<2 -> A[0]=2 ==2 -> so append 0, ptr becomes 1.
        i=3: 
            ptr=1 <2 -> A[1]=3==3 -> append 0, ptr=2.
        Output: [1,0,0] -> correct.

   But wait, in the first iteration, we didn't update ptr? So for i=1, we didn't advance ptr. Then at i=2, we check A[0] (which is 2) and it equals 2, so we set 0 and advance ptr to 1. Then at i=3, we check A[1] (which is 3) and set 0 and advance to 2.

   Correct.

   Example 2: N=8, M=5, A=[1,3,4,7,8]
        i=1: A[0]=1 -> res.append(0), ptr=1.
        i=2: else -> A[ptr]=A[1]=3 -> 3-2=1 -> res.append(1)
        i=3: A[1]=3 -> res.append(0), ptr=2.
        i=4: A[2]=4 -> res.append(0), ptr=3.
        i=5: else -> A[3]=7 -> 7-5=2 -> res.append(2)
        i=6: else -> A[3]=7 -> 7-6=1 -> res.append(1)
        i=7: A[3]=7 -> res.append(0), ptr=4.
        i=8: A[4]=8 -> res.append(0), ptr=5.
        Output: [0,1,0,0,2,1,0,0] -> matches.

   So it works.

   Edge Cases:
      - M=1: then A=[N]. 
          For i from 1 to N-1: else -> A[ptr]=N, so answer = N-i.
          For i=N: if -> 0.
      - N=1: then M=1, A=[1]. Output: [0].

   This should work.

### Step 4: Verification and Summary

I'll write the code accordingly. The key optimization is the two-pointer method that runs in O(N) time and O(1) extra space (beyond the input and output). 

Let me write the code:

Note: We are given the input as:
    N, M
    A list of M integers

Code:

    from typing import List

    class Solution:
        def solve(self, N: int, M: int, A: List[int]) -> List[int]:
            # We'll create an empty list for the result.
            res = []
            ptr = 0  # pointer for the next fireworks day in A
            # We traverse each day from 1 to N
            for day in range(1, N+1):
                # If the current day is the next fireworks day in A
                if ptr < M and A[ptr] == day:
                    res.append(0)
                    ptr += 1
                else:
                    # Since ptr < M must hold (because the last day is in A and we haven't reached the last day)
                    # We use the current A[ptr] (which is the next upcoming fireworks) to compute the difference
                    res.append(A[ptr] - day)
            return res

But wait, what if ptr becomes M during the loop? For example, after processing the last fireworks day, ptr becomes M. Then for the next days? But the last day is the last day of the festival, so after processing day N, we break. Therefore, we never have day > last fireworks day. So, the else branch is safe for all days from 1 to N because when we are at the last day, we process the if branch and then break.

But what if there are multiple fireworks at the same day? The problem states: "It is guaranteed that fireworks will be launched on the last day of the festival" and "all input values are integers" and "1<=A1<A2<...<AM=N". So no duplicates.

Therefore, the above code should be correct.

I'll run through the examples mentally again.

Example 1: [1,0,0] -> correct.
Example 2: [0,1,0,0,2,1,0,0] -> correct.

Edge: N=1, M=1, A=[1]
    day=1: if ptr<1 -> true, and A[0]==1 -> append 0, ptr becomes 1. Then done. Output [0].

Edge: N=5, M=1, A=[5]
    day1: else -> A[0]=5 -> 5-1=4 -> res=[4]
    day2: else -> 5-2=3 -> [4,3]
    day3: 2 -> [4,3,2]
    day4: 1 -> [4,3,2,1]
    day5: if -> 0 -> [4,3,2,1,0]

But the problem: from day 5, it's 0. But from day 1, the next fireworks is day5, which is 4 days later? So 4 is correct.

Wait, the problem says: "How many days later from the i-th day will fireworks be launched for the first time on or after the i-th day". For day1: 5 is 4 days later? Actually, if day1 is day1, then day5 is 4 days later? Because from day1, the next day is day2 (1 day later), then day3 (2 days later), then day4 (3 days later), then day5 (4 days later). But the problem says "on or after", and the first time is day5, which is 4 days later? 

But the example: in example1, day1: next is day2 -> 1 day later. So the calculation is (fireworks_day - i). 

In the edge: day1: 5-1=4 -> that's 4 days later. Correct.

But the example2: day5: output 2? Actually, day5: next fireworks is day7, which is 7-5=2 days later? Correct.

So the formula is correct.

Therefore, the code is ready.

But what if we run out of ptr? Actually, we have the condition `if ptr < M and A[ptr] == day`. So when ptr >= M, the condition fails and we go to else. But in the else, we do `A[ptr]` which would be out of bounds. That's a problem.

Wait, in the edge case: when day=N, which is the last day, we have A[ptr] = N (if ptr < M) and then we set ptr to M. Then the loop ends. But what if we have a festival of N days, but the fireworks are on day N, so when day=N, we set ptr to M (which is the last index+1). Then we break. So we never access A[ptr] when ptr==M.

But in the else part, we access A[ptr] only when the condition fails. The condition fails when either ptr>=M or A[ptr]!=day. But if ptr>=M, then we are accessing A[M] which is out of bounds. 

How can we avoid that? 

We must note that the last day is always a fireworks day. So when we reach day=N, we must have A[ptr] == N (because we haven't processed it yet) and then we set ptr to M. Therefore, for all days i from 1 to N-1, ptr is < M. And for day=N, we enter the if branch and do not access the else. Therefore, we never access A[ptr] when ptr>=M.

But what if the fireworks are not in order? The problem states they are in increasing order and the last is N, so we are safe.

But to be safe, we can restructure: 

    for day in range(1, N+1):
        if ptr < M and A[ptr] == day:
            res.append(0)
            ptr += 1
        else:
            # We know that there is a next fireworks because the last day is N and we haven't passed it? 
            # But if ptr is at M, then we have no next fireworks? But that should not happen for day < N?
            if ptr >= M:
                # This should not happen because the last day is N, so when day < N, ptr must be < M.
                # But if it does, we can set to a large number? But the problem guarantees fireworks on the last day.
                # So, we can skip this check? Or we can break? 
                # Since the last day is guaranteed, for day < N, ptr < M. And for day = N, we handled in the if branch.
                # So, we can skip the check and just use A[ptr] only when ptr < M? But we are in else and we assume ptr < M.
                # To be safe, we can add an assertion? 
                # But the constraints guarantee the last day is in A, so we can assume ptr < M for all days that reach the else branch.
                res.append(A[ptr] - day)   # but if ptr>=M, this will crash.

So, we must ensure that ptr < M in the else branch. 

But the condition that the last day is in A and that we process the last day in the if branch ensures that for day=N, we don't go to the else branch. And for day < N, the next fireworks day is at most N, and we haven't processed the last fireworks? Actually, the pointer ptr only moves when we hit a fireworks day. So for any day i < N, there must be at least one fireworks day >= i (the one at N). So ptr must be < M until we process the last fireworks.

Therefore, we can assume that in the else branch, ptr is always < M.

But to be safe, we can add an assertion:

        else:
            assert ptr < M, "ptr is beyond M-1, which should not happen for day < N"
            res.append(A[ptr] - day)

But the problem constraints guarantee that the last day is a fireworks day, so we don't need to worry. We can rely on the problem input.

Alternatively, we can avoid the else branch by design. But the above reasoning holds.

I think the code is correct.

Now, let me write the final code.

We'll write it without the assertion for brevity, as the constraints guarantee the condition.

But in a programming competition, it's safe to assume valid input.

So, the final implementation:

    class Solution:
        def solve(self, N: int, M: int, A: List[int]) -> List[int]:
            res = []
            ptr = 0
            for day in range(1, N + 1):
                if ptr < M and A[ptr] == day:
                    res.append(0)
                    ptr += 1
                else:
                    res.append(A[ptr] - day)
            return res

This is the optimal solution.

Summary:
- We use a two-pointer technique to traverse the days and the fireworks days simultaneously.
- The pointer `ptr` points to the next upcoming fireworks day.
- For each day, if it is a fireworks day, we record 0 and move the pointer to the next fireworks day. Otherwise, the difference between the next fireworks day and the current day is recorded.
- The solution is O(N) time and O(N) space for the output, which is optimal.

Further optimization: 
   We cannot avoid O(N) space for the output. The time is optimal.

We can consider if we need to store the entire output? The problem requires printing N lines. So we have to output a list of N integers.

Therefore, the above solution is optimal.

I'll test with the provided examples.

Example1: 
    Input: N=3, M=2, A=[2,3]
    Output: [1,0,0] -> correct.

Example2:
    Input: N=8, M=5, A=[1,3,4,7,8]
    Output: [0,1,0,0,2,1,0,0] -> correct.

Now, I'll write the code accordingly.

## Final Solution

### Solution Analysis
The problem requires determining, for each day in a festival, the number of days until the next fireworks display. Given the constraints (up to 200,000 days), an efficient solution is necessary. The optimal approach uses a two-pointer technique to traverse the days and fireworks events in a single pass, leveraging the sorted list of fireworks days.

#### Approach
1. **Initialization**: Start with an empty list to store results and a pointer `ptr` initialized to 0, pointing to the next fireworks day in the list `A`.
2. **Iterate through each day** from 1 to N:
   - If the current day matches the fireworks day at `A[ptr]`, append 0 to the result and increment `ptr` to move to the next fireworks day.
   - Otherwise, append the difference between the next fireworks day (`A[ptr]`) and the current day.
3. **Termination**: After processing all days, return the result list.

This approach efficiently computes the next fireworks day for each day in O(N) time by traversing the list of days once and the list of fireworks days once. The space complexity is O(N) for storing results, which is optimal.

#### Solution Code
```python
from typing import List

class Solution:
    def solve(self, N: int, M: int, A: List[int]) -> List[int]:
        res = []
        ptr = 0
        for day in range(1, N + 1):
            if ptr < M and A[ptr] == day:
                res.append(0)
                ptr += 1
            else:
                res.append(A[ptr] - day)
        return res
```

#### Explanation
- **Initialization**: The result list `res` collects answers for each day. The pointer `ptr` tracks the next fireworks day in `A`.
- **Loop through days**: For each day from 1 to N:
  - **Fireworks day**: If the current day is a fireworks day (`A[ptr] == day`), record 0 and move `ptr` to the next fireworks day.
  - **Non-fireworks day**: Calculate the days until the next fireworks by subtracting the current day from `A[ptr]`.
- **Efficiency**: Each day and each fireworks day is processed exactly once, resulting in O(N) time complexity. The space complexity is O(N) for the output, which is necessary and optimal.

This approach efficiently handles the constraints by leveraging the sorted nature of fireworks days and a single traversal, ensuring optimal performance.