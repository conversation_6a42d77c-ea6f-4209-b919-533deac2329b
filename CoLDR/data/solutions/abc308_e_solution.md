# Solution for abc308_e.md

Generated at: 2025-07-22 06:24:12
Model: deepseek-reasoner

## Reasoning Process

We are given:
  N: integer (3 to 200,000)
  A: a list of N integers, each in {0,1,2}
  S: a string of length N containing only 'M','E','X'

We need to consider every triplet (i,j,k) such that:
  1 <= i < j < k <= N
  and S_i S_j S_k = "MEX"

For each such triplet, we compute mex(A_i, A_j, A_k) and sum them up.

The mex (minimum excluded value) of three numbers in {0,1,2} can be:
  If the set is {0,1,2} -> mex=3
  Otherwise, the mex is the smallest nonnegative integer not in the set.

Since the numbers are only 0,1,2, the mex can only be 0,1,2, or 3.

We can precompute the mex for every possible triplet of values? Actually, there are only 3*3*3=27 combinations.

But note: we cannot iterate over all triplets (i,j,k) because N can be up to 200,000 -> O(N^3) is too slow.

We need an efficient approach.

Idea:
  We are required to consider triplets that form the string "MEX" in order. That is, we need:
    an index i with S_i='M'
    an index j with S_j='E'
    an index k with S_k='X'
    and i < j < k.

  We can break the problem by iterating over the middle element? (the 'E')?

Alternative approach:
  We can pre-group the indices by the character in S and by the value in A.

  Let:
    M0 = indices i where S_i='M' and A_i=0
    M1 = indices i where S_i='M' and A_i=1
    M2 = indices i where S_i='M' and A_i=2

    Similarly, E0, E1, E2 for 'E'
    and X0, X1, X2 for 'X'

But then how to count triplets (i,j,k) with i in M_a, j in E_b, k in X_c, and i<j<k?

And for each triplet (a,b,c) we know the mex value (which is a function of a,b,c). Let's denote:
  f(a,b,c) = mex(a,b,c)

So the answer is:
  Sum_{a in {0,1,2}} Sum_{b in {0,1,2}} Sum_{c in {0,1,2}} [ f(a,b,c) * (number of triplets (i,j,k) with i in M_a, j in E_b, k in X_c and i<j<k) ]

Now the problem reduces to: for each (a,b,c), count the number of triplets (i,j,k) such that:
  i is in M_a, j is in E_b, k is in X_c, and i < j < k.

How to count the triplets for a fixed (a,b,c) efficiently?

We note that the sets M_a, E_b, X_c are just sorted lists of indices (we can sort them since the indices are natural).

But we cannot iterate over all 27 combinations and then for each combination iterate over j (the E index) and then for each j, count the number of M in M_a with index < j and number of X in X_c with index > j? That would be:

  For a fixed (a,b,c):
      total = 0
      for each j in E_b:
          count_M = number of indices in M_a that are < j
          count_X = number of indices in X_c that are > j
          total += count_M * count_X

Then multiply by f(a,b,c) and sum over a,b,c.

But note: the sets M_a and X_c are independent. We can precompute for each set M_a and X_c, and then for each j in E_b, we can use binary search to count the M_a elements < j and X_c elements > j.

But we have 27 combinations? 3*3*3=27. And for each combination we iterate over the E_b set. The total work over all combinations would be:

  Sum_{b in {0,1,2}} [ |E_b| * (log|M_a| + log|X_c|) ] for each a,c.

Since the total size of all E_b is N, and we have 9 combinations of (a,c) per b? Actually, for each (a,b,c) we have a separate combination. So we do 27 iterations over the E_b set? Actually, we have 3 choices for a, 3 for b, 3 for c. For each b, we have 9 combinations (a,c). So we do 9 * (|E_0| + |E_1| + |E_2|) = 9 * N. And each requires two binary searches (logarithmic time). So total O(9 * N * log N) = O(N log N). Since N is 200,000, log N is about 18, so 9*200000*18 = 32,400,000 which might be borderline in Pyton but we can try to optimize by using precomputed prefix sums.

Alternatively, we can precompute for each set M_a a Fenwick tree? But note we have multiple sets and we only need to count the numbers less than j. We can precompute a global array for M? Actually, we can precompute for each M_a the sorted list, and then for each j we can do a binary search in that sorted list to count the numbers less than j. Similarly for X_c, we can have a sorted list and then for each j we count the numbers greater than j (which is total in X_c minus the count of numbers <= j).

But note: we have 3 sets for M (M0, M1, M2) and 3 sets for X (X0, X1, X2). We can pre-sort each of these 6 sets.

Plan:

  Precomputation:
    M = [ [], [], [] ]   # M[0] = M0, M[1]=M1, M[2]=M2
    E = [ [], [], [] ]
    X = [ [], [], [] ]

    For i in range(N):
        char = S[i]
        a_val = A[i]
        if char == 'M':
            M[a_val].append(i)   # storing the index
        elif char == 'E':
            E[a_val].append(i)
        elif char == 'X':
            X[a_val].append(i)

    Then sort each of the 9 lists? Actually, the indices are increasing? Not necessarily: we are iterating in the order of i, so we can just collect and then sort? Actually, the indices are the original positions (0-indexed). But we store the index i. And we require i<j<k. So we can sort each list? Actually, the indices we store are increasing because we traverse from 0 to N-1? But we are not storing in increasing order? We are appending in increasing index? Actually, we are iterating i from 0 to N-1, so the indices we store are in increasing order? But we can sort to be safe? Actually, we don't need to sort if we store the index as we traverse? The index i is increasing. However, we store by the value in A. So for each group (like M0) we store the indices in increasing order? Yes, because we are going from i=0 to N-1. So we don't need to sort.

  But note: we are storing the actual index (which is the position in the array). So when we later consider j (which is an index from the E group), we want to count the M_a that are less than j. Since our M_a list is sorted (by the natural order of the indices, because we appended in increasing index order), we can use binary search.

  However, we have to be cautious: if we do not sort, we are relying on the fact that we stored in increasing index order. This is true because we iterate i from 0 to N-1.

  Then for each b in [0,1,2]:
      for each j in E[b]:
          for each a in [0,1,2]:
             for each c in [0,1,2]:
                 count_M = number of elements in M[a] that are < j
                 count_X = number of elements in X[c] that are > j
                 total_triplets = count_M * count_X
                 mex_val = mex(a, b, c)   # because the triplet (a,b,c) corresponds to the values
                 answer += mex_val * total_triplets

  But this is 3 * (|E0|+|E1|+|E2|) * 9 = 27 * N operations? And each operation is two binary searches? Actually, we can precompute the counts for M and X for each a and c? But note we are iterating by j, and j is increasing? Actually, we can do binary searches for each j and for each a and each c. The total number of binary searches would be 9 * |E_b| for each b, so total 9 * N. And each binary search is O(log N). So total O(9 * N * log N). Since N<=200000, 9*200000*log2(200000) ~ 9 * 200000 * 18 = 32.4e6, which might be acceptable in Pyton? But note that 32.4e6 operations in worst-case, and each operation is a binary search (which is a few operations). However, worst-case 32.4e6 * (cost of two binary searches) = 32.4e6 * (2 * log2(200000)) ~ 32.4e6 * 36 = 1.166e9 operations? That might be too slow in Python.

We need to optimize.

Alternative: Precompute prefix sums for M and suffix sums for X.

We can create an array for the entire index range? But we have 3 groups for M and 3 groups for X.

We can do:

  Let's create an array for M: for each value a, we can have an array of size N? Actually, we can have a global array that for each index, we mark the counts for M0, M1, M2? Then we can compute a prefix sum for M0, M1, M2.

Similarly, for X we can compute a suffix sum.

But note: we only care about the counts at the positions of E.

Plan for optimization:

  Precomputation:
    Let M = [ [], [], [] ]   # we have the lists, we don't need to change.

  But we can precompute for each a (0,1,2) a Fenwick tree? Or simply an array and then use prefix sums? Actually, we have the entire index range from 0 to N-1.

  We can create an array `cnt_M` of size [3][N] but that would be too big? Actually, we don't need that. Alternatively, we can create a 2D array for the prefix sums for the three M groups.

  Steps:

    Let's define:
        prefix_M[a][i] = number of M_a in the range [0, i-1] (if we use 0-indexed indices)

    How to build?
        We can create an array of zeros of length N for each a.
        For each a, for each index in M[a], mark 1 at that index.
        Then compute prefix sums.

    Similarly, for X we can do suffix sums: 
        suffix_X[c][i] = number of X_c in the range [i+1, N-1]

    Then for each j in E[b] (which is at index j), we can get:
        count_M[a] = prefix_M[a][j]   # because we want indices < j: so [0, j-1] -> prefix_M[a][j] = sum from 0 to j-1?
        Actually, if we define prefix_M[a][j] = number of M_a in [0, j-1], then we can set:

        prefix_M[a][0] = 0
        prefix_M[a][i] = prefix_M[a][i-1] + (1 if i-1 is in M[a]? Actually, we can do:

        Alternatively, we can make an array `arr_M` for each a of length N: 
            arr_M[a][i] = 1 if the element at index i is in M_a (i.e., S[i]=='M' and A[i]==a), else 0.
        Then prefix_M[a][i] = arr_M[a][0] + ... + arr_M[a][i-1]   -> then the count for indices < j is prefix_M[a][j] (because j is the current index, and we want indices strictly less than j).

        Similarly, for X: we want indices > j. We can define suffix_X[c][j] = sum of arr_X[c][j+1] to arr_X[c][N-1].

        But note: we are storing the actual index. So we can build:

          prefix_M[a] = [0]*(N+1)
          for i from 0 to N-1:
              prefix_M[a][i+1] = prefix_M[a][i] + (1 if S[i]=='M' and A[i]==a else 0)   # but wait: we only want to count the M_a? Actually, we can build the entire array for each a.

        However, we don't need the entire array? We only need the value at j for each j that is an E index. But we can precompute the prefix and suffix arrays for each a and c.

        But note: we have 3 for M and 3 for X, so 6 arrays. Then for each j in E, we can do:

          count_M[a] = prefix_M[a][j]   # because prefix_M[a][j] = count in [0, j-1] (so strictly less than j)
          count_X[c] = suffix_X[c][j]   # defined as: suffix_X[c][j] = count in [j+1, N-1]

        How to build suffix_X[c]? 
            suffix_X[c][j] = (total count of X_c) - (prefix_X[c][j+1])   # because prefix_X[c][j+1] counts from 0 to j
            but we can also build backwards.

        Alternatively, we can build:

            suffix_X[c] = [0]*(N+2)
            for i from N-1 down to 0:
                suffix_X[c][i] = suffix_X[c][i+1] + (1 if S[i]=='X' and A[i]==c else 0)

            Then for index j, we want suffix_X[c][j+1] (because we want indices > j, so from j+1 to N-1).

        Then for each j in E (at position j), we get:
            count_M[a] = prefix_M[a][j]   # because prefix_M[a][j] = count of M_a from 0 to j-1
            count_X[c] = suffix_X[c][j+1]   # because suffix_X[c][j+1] = count of X_c from j+1 to N-1

        Then the inner loop is over a and c (9 iterations) for each j.

        The total work: 9 * (number of E's) = 9 * (number of E in the entire string) <= 9 * N.

        This is O(N) per a,c? Actually, 9*N operations which is 9*200000 = 1.8e6, which is acceptable.

  Steps:

      Precomputation:
        prefix_M = [[0]*(N+1) for _ in range(3)]   # for a=0,1,2: each of length N+1
        suffix_X = [[0]*(N+2) for _ in range(3)]    # for c=0,1,2: each of length N+2 (we use N+2 to avoid index issues)

      Build prefix_M:
          for a in range(3):
              for i in range(N):
                  # prefix_M[a][i+1] = prefix_M[a][i] + (1 if S[i]=='M' and A[i]==a else 0)
                  prefix_M[a][i+1] = prefix_M[a][i]
                  if S[i] == 'M' and A[i] == a:
                      prefix_M[a][i+1] += 1

      Build suffix_X:
          for c in range(3):
              # we'll make an array for X_c: then suffix from the end
              # suffix_X[c][i] = count of X_c from i to N-1
              # then we want for j: suffix_X[c][j+1] (which is the count from j+1 to N-1)
              # so we can set:
              for i in range(N-1, -1, -1):
                  # we'll build from the end: suffix_X[c][i] = suffix_X[c][i+1] + (1 if S[i]=='X' and A[i]==c else 0)
                  # but we need to allocate an array of length N+2? We can set suffix_X[c][N]=0, then go backwards.
                  pass
              # Alternatively, we can do:
              #   suffix_X[c][N] = 0
              #   for i from N-1 down to 0:
              #       suffix_X[c][i] = suffix_X[c][i+1] + (1 if S[i]=='X' and A[i]==c else 0)
              # But note: we need to access suffix_X[c][j+1] for j in [0, N-1]. Then suffix_X[c][j+1] = count from j+1 to N-1.
              # Actually, we can build an array of length N+1: let suffix_X[c] = [0]*(N+2) and then set suffix_X[c][N]=0, and then for i from N-1 down to 0: suffix_X[c][i] = suffix_X[c][i+1] + (1 if ...)
          Actually, we can do:

          for c in range(3):
              # initialize the suffix array for c: we'll make an array of length N+2, index from 0 to N+1
              # set suffix_X[c][N] = 0? Actually, we want for any j: count from j+1 to N-1 -> so for j, we look at j+1.
              # We can set:
              #   suffix_X[c][N] = 0
              #   for i from N-1 down to 0:
              #       suffix_X[c][i] = suffix_X[c][i+1] + (1 if S[i]=='X' and A[i]==c else 0)
              # Then for a given j (which is an index in the array, from 0 to N-1), we want suffix_X[c][j+1] = the count from j+1 to N-1.

          But note: our prefix_M is built for indices 0..N-1, and we defined prefix_M[a][j] (for j from 0 to N) as the count from 0 to j-1.

      Then:
          ans = 0
          # Precompute the mex table for (a,b,c) for a,b,c in {0,1,2}
          mex_table = {}
          for a in range(3):
              for b in range(3):
                  for c in range(3):
                      # compute mex(a,b,c): the minimum non-negative integer not in {a,b,c}
                      s = {a,b,c}
                      m = 0
                      while m in s:
                          m += 1
                      mex_table[(a,b,c)] = m

          # Now iterate over each E: for each index j in the entire array where S[j]=='E', then we consider the value b = A[j]
          for j in range(N):
              if S[j] != 'E':
                  continue
              b = A[j]
              # for each a in [0,1,2] and each c in [0,1,2]:
              for a in range(3):
                  for c in range(3):
                      cnt_M = prefix_M[a][j]   # because j is the current index, and prefix_M[a][j] = count of M_a from 0 to j-1 (which are < j)
                      cnt_X = suffix_X[c][j+1]   # because we want indices > j: from j+1 to N-1 -> this is stored in suffix_X[c][j+1]
                      # But note: our suffix_X[c] array: we built such that suffix_X[c][i] = count of X_c from i to N-1? 
                      # Actually, we built: 
                      #   suffix_X[c][i] = (number of X_c from i to N-1) 
                      # Then for j, we want indices > j: that is from j+1 to N-1 -> so that is suffix_X[c][j+1] (because at j+1 we start counting from j+1 to N-1).

                      total = cnt_M * cnt_X
                      mex_val = mex_table[(a,b,c)]
                      ans += mex_val * total

          Then output ans.

  However, note: the suffix_X array we built: 
      We defined: 
          suffix_X[c][i] = (if we built backwards) the count from i to N-1? 
      Then for j, we want the count from j+1 to N-1 -> that is suffix_X[c][j+1].

  But how did we build it?
      We set suffix_X[c][N] = 0, then for i from N-1 down to 0:
          suffix_X[c][i] = suffix_X[c][i+1] + (1 if S[i]=='X' and A[i]==c else 0)

      So suffix_X[c][i] = count of X_c at positions i, i+1, ..., N-1.

      Therefore, for j, the count from j+1 to N-1 is suffix_X[c][j+1].

  This is correct.

  But note: j is from 0 to N-1. When j = N-1, then j+1 = N, and we have suffix_X[c][N] = 0 (which is correct).

  Example: 
      N=4, j=0: we want count of X_c from 1 to 3 -> that's suffix_X[c][1] = count from index1 to index3.

  Also, for prefix_M: 
      prefix_M[a][j] = count of M_a from 0 to j-1. For j=0: prefix_M[a][0]=0.

  So the code:

      Step 1: Read N, list A, string S.

      Step 2: Initialize prefix_M for a in 0,1,2: arrays of length N+1 (index 0..N) with zeros.
               Initialize suffix_X for c in 0,1,2: arrays of length N+2 (index 0..N+1) with zeros.

      Step 3: Build prefix_M:
          for a in range(3):
              prefix_M[a][0] = 0
              for i in range(0, N):
                  prefix_M[a][i+1] = prefix_M[a][i] + (1 if S[i]=='M' and A[i]==a else 0)

      Step 4: Build suffix_X:
          for c in range(3):
              # set the last element: we want suffix_X[c][N] = 0? Actually, we define for i from 0 to N: 
              # We'll set suffix_X[c][N] = 0, then for i from N-1 down to 0:
              suffix_X[c][N] = 0
              for i in range(N-1, -1, -1):
                  suffix_X[c][i] = suffix_X[c][i+1] + (1 if S[i]=='X' and A[i]==c else 0)

          But note: we want to be able to access suffix_X[c][j+1] for j from 0 to N-1. 
          Since we built suffix_X[c] from 0 to N, and we set suffix_X[c][N]=0, then for j=N-1: suffix_X[c][N] is defined.

      Step 5: Precompute mex_table for all (a,b,c) in {0,1,2}^3.

      Step 6: ans = 0
          for j in range(N):
              if S[j]=='E':
                  b = A[j]
                  for a in range(3):
                      for c in range(3):
                          cnt_M = prefix_M[a][j]   # because j is the current index, and we want indices < j: which are from 0 to j-1 -> stored at prefix_M[a][j]
                          cnt_X = suffix_X[c][j+1]  # because we want indices > j: from j+1 to N-1 -> stored at suffix_X[c][j+1] (which we built for index j+1, and we have suffix_X[c] defined for indices 0..N)
                          ans += mex_table[(a,b,c)] * cnt_M * cnt_X

      Step 7: Print ans.

  Let's test with example: 
      Input: 
          4
          1 1 0 2
          MEEX

      A = [1,1,0,2], S = "MEEX"

      Build prefix_M for a in {0,1,2}:

        a=0: 
          i0: S[0]='M', A[0]=1 -> skip
          i1: S[1]='E' -> skip
          i2: S[2]='E' -> skip
          i3: S[3]='X' -> skip
          So prefix_M[0] = [0,0,0,0,0]

        a=1:
          i0: add 1 -> prefix_M[1] = [0,1,1,1,1] (for indices 0 to 4: index0=0, index1=1, index2=1, index3=1, index4=1)
          Actually, we build:
            prefix_M[1][0]=0
            prefix_M[1][1]=0 + (1 if i0? -> yes) = 1
            prefix_M[1][2]=1 + (i1: not M) -> 1
            prefix_M[1][3]=1 + (i2: not M) -> 1
            prefix_M[1][4]=1 + (i3: not M) -> 1

        a=2: no M with value 2 -> [0,0,0,0,0]

      suffix_X for c in {0,1,2}:
        c=0:
          We set suffix_X[0][4]=0
          i=3: S[3]=='X', A[3]=2 -> not 0 -> so suffix_X[0][3]=suffix_X[0][4] + 0 = 0
          i=2: S[2]=='E' -> skip -> suffix_X[0][2]=suffix_X[0][3]=0
          i=1: S[1]=='E' -> skip -> 0
          i=0: S[0]=='M' -> skip -> 0
          So suffix_X[0]=[0,0,0,0,0] (for indices 0,1,2,3,4) -> but we built for indices 0 to 4? Actually, we built from i=3 down to 0.

        c=1: same as c=0 -> all zeros.

        c=2:
          i=3: S[3]=='X' and A[3]=2 -> so suffix_X[2][3] = suffix_X[2][4] + 1 = 0+1=1
          i=2: skip -> suffix_X[2][2]=suffix_X[2][3]=1
          i=1: skip -> 1
          i=0: skip -> 1
          So suffix_X[2] = [1,1,1,1,0] (for indices 0,1,2,3,4)

          But note: we defined for each index i: suffix_X[2][i] = count of X with value 2 from i to 3 (since N=4, indices 0..3). 
          Actually, at index0: suffix_X[2][0]=1? But there's only one X at index3. 
          How did we get 1 at index0? 
            suffix_X[2][0] = suffix_X[2][1] + (if index0: no) -> but we built backwards: 
            We started at i=3: set suffix_X[2][3]=1 (because at index3 we have one, and then from 3 to 3: count=1)
            then i=2: suffix_X[2][2] = suffix_X[2][3] (which is 1) + (if at i=2: no) = 1
            then i=1: same -> 1
            then i=0: same -> 1

          But that is not correct: the count from index0 to the end? Actually, our definition: 
            suffix_X[c][i] = count of X_c from index i to N-1.

          So for j=0: we want the count of X2 from 1 to 3? But our method for j=0: we use suffix_X[2][1] (because j+1=1) -> which is the count from index1 to N-1? 
          But our suffix_X[2][1] = 1 (because from index1 to 3, there is one at index3). That is correct.

          However, the value at suffix_X[2][0] is the count from index0 to 3? which should be 1? And that is correct.

      Now, iterate j where S[j]=='E': at j=1 and j=2.

        j=1: b = A[1]=1
          for a in [0,1,2], c in [0,1,2]:
            cnt_M = prefix_M[a][1]   # which is the count of M_a in [0,0] (since j=1: we look at prefix_M[a] at index1 -> which counts indices <1, i.e., only index0)
            So:
              a=0: prefix_M[0][1]=0
              a=1: prefix_M[1][1]=1? But wait: our prefix_M[1] = [0,1,1,1,1] -> prefix_M[1][1]=1? Actually, at j=1: we are at the second element in the prefix array? 
                  prefix_M[1][0]=0, prefix_M[1][1]=1 -> yes.
              a=2: 0

            cnt_X = suffix_X[c][2]   # because j=1 -> j+1=2
              c=0: suffix_X[0][2]=0
              c=1: 0
              c=2: suffix_X[2][2]=1   (from above)

            So only (a=1, c=2) gives 1*1 = 1 triplet? 
            mex(1,1,2) = mex_table[(1,1,2)]: 
                set = {1,2} -> mex=0? 
                Actually: mex(1,1,2): 
                    min non-negative not in {1,2} -> 0.

            So we add 0 * 1 = 0.

        j=2: b = A[2]=0
          cnt_M: 
            a=0: prefix_M[0][2]=0
            a=1: prefix_M[1][2]=1 (because at index0 we have an M1, and then index1 is skipped? Actually, prefix_M[1] at index2: count from 0 to 1 -> we have one M1 at index0? and index1 is not M -> so 1)
            a=2:0

          cnt_X: j+1=3 -> suffix_X[c][3]
            c=0:0
            c=1:0
            c=2: suffix_X[2][3]=1? Actually, from our building: suffix_X[2][3]=1? (because at index3 we have one, and then from index3 to the end: that's one) -> but wait, our suffix_X[2] for index3: we built as 1? Then from index3 to the end: that's one.

          So only (a=1, c=2): 1*1=1 triplet.

          mex(1,0,2) = mex_table[(1,0,2)] = mex({0,1,2}) = 3.

          So we add 3.

          Total ans = 0+3 = 3.  -> matches.

  But wait: why at j=2 we have prefix_M[1][2]=1? 
        prefix_M[1] = [0,1,1,1,1]: 
            prefix_M[1][0]=0 -> for index0: we haven't processed any element? 
            prefix_M[1][1]=1 -> after index0: we have one M1 at index0.
            prefix_M[1][2]=1 -> after index1: we still have only one (because at index1: S[1]=='E' -> not M) -> so 1.

        So yes.

  However, note: the triplet for j=1: we had (i=0, j=1, k=3): that is (M at0, E at1, X at3). 
        But the string at index0: 'M', index1:'E', index3:'X' -> "MEX" -> valid.

        Why didn't we count it? 
          We did count it: at j=1 (which is the E at index1) we had (a=1, c=2) -> one triplet. But then we multiplied by mex(1,1,2)=0.

  So the code is correct.

  Time complexity: O(N) for building prefix and suffix arrays, and then for each E we do 9 iterations -> O(9 * (# of E)) <= O(9*N) = O(N). 
  This is acceptable.

  Let's test with example 2: 
      Input: 
          3
          0 0 0
          XXX
      There is no E -> so we iterate over j: no j with 'E' -> answer=0.

  Example 3: 15 ... 

  We'll code accordingly.

  Implementation note: 
      We are using 0-indexed indices.

  Code:

      import sys

      data = sys.stdin.read().splitlines()
      N = int(data[0].strip())
      A = list(map(int, data[1].split()))
      S = data[2].strip()

      # Initialize prefix_M: 3 arrays of length N+1 (index 0..N)
      prefix_M = [[0]*(N+1) for _ in range(3)]
      # Build for each a in [0,1,2]
      for a in range(3):
          for i in range(N):
              # prefix_M[a][i+1] = prefix_M[a][i] + (1 if S[i]=='M' and A[i]==a else 0)
              prefix_M[a][i+1] = prefix_M[a][i]
              if S[i]=='M' and A[i]==a:
                  prefix_M[a][i+1] += 1

      # Initialize suffix_X: 3 arrays of length N+2 (index 0..N+1) -> we'll make index from 0 to N (we don't need N+1? We use N+1 as a safe)
      suffix_X = [[0]*(N+2) for _ in range(3)]   # each array has N+2 elements: indices 0 to N+1
      for c in range(3):
          # set suffix_X[c][N] = 0? Actually, we want for j in [0, N-1]: we use suffix_X[c][j+1]. We can set suffix_X[c][N] = 0 and then for i from N-1 down to 0: 
          # But we allocated N+2: so we can set suffix_X[c][N] = 0? Actually, we set from index N to N: we set to 0, then we go down to 0.
          # We'll set suffix_X[c][N] = 0, and then for i from N-1 down to 0:
          #   suffix_X[c][i] = suffix_X[c][i+1] + (1 if S[i]=='X' and A[i]==c else 0)
          # But note: our array suffix_X[c] has length N+2, so indices 0 to N+1. We only use indices 0 to N.
          suffix_X[c][N] = 0   # index N: we set to 0. Then for i from N-1 down to 0, we update.
          for i in range(N-1, -1, -1):
              suffix_X[c][i] = suffix_X[c][i+1] 
              if S[i]=='X' and A[i]==c:
                  suffix_X[c][i] += 1

      # Precompute mex_table: 
      mex_table = {}
      for a in range(3):
          for b in range(3):
              for c in range(3):
                  s = {a, b, c}
                  m = 0
                  while m in s:
                      m += 1
                  mex_table[(a,b,c)] = m

      ans = 0
      # Iterate j from 0 to N-1:
      for j in range(N):
          if S[j]=='E':
              b = A[j]
              for a in range(3):
                  for c in range(3):
                      cnt_M = prefix_M[a][j]   # indices < j: from 0 to j-1
                      cnt_X = suffix_X[c][j+1]   # indices > j: from j+1 to N-1 -> stored at suffix_X[c][j+1]
                      # Note: our suffix_X[c] array: we built so that suffix_X[c][j+1] = count from j+1 to N-1? 
                      # Actually, by our building: 
                      #   suffix_X[c][j+1] = count of X_c from j+1 to N-1? 
                      #   But our building: suffix_X[c][j+1] = count from j+1 to N-1? 
                      #   How? 
                      #       We defined: suffix_X[c][i] = count of X_c from i to N-1.
                      #   Therefore, for i=j+1, it counts from j+1 to N-1 -> that's what we want.
                      ans += mex_table[(a,b,c)] * cnt_M * cnt_X

      print(ans)

  Let's test with example 3? The expected output is 13.

  However, we can run the example 3 to see if we get 13.

  But note: the constraints: N=15, so we can simulate by hand? Maybe not, but we can run the provided example.

  But since we are writing code, we can run the provided examples.

  We'll run the example 1 and 2 to be sure.

  Example 1: N=4, A=[1,1,0,2], S="MEEX" -> we did and got 3.

  Example 2: N=3, A=[0,0,0], S="XXX" -> 0.

  Example 3: 
      Input: 
          15
          1 1 2 0 0 2 0 2 0 0 0 0 0 2 2
          EXMMXXXEMEXEXMM

      Expected output: 13.

  We can run our code with this input.

  However, we are not going to do by hand. We trust the method? But let's check the example 3.

  Alternatively, we can write a brute force for small N and test.

  But note: N=15, so brute force would be C(15,3) = 455, which is acceptable.

  We'll write a helper function to compute mex for three numbers.

  Brute force for example 3:

      def mex(a,b,c):
          s = {a,b,c}
          m = 0
          while m in s:
              m += 1
          return m

      A = [1,1,2,0,0,2,0,2,0,0,0,0,0,2,2]
      S = "EXMMXXXEMEXEXMM"

      triplets = []
      for i in range(15):
          for j in range(i+1,15):
              for k in range(j+1,15):
                  if S[i]+S[j]+S[k] == "MEX":
                      triplets.append((i,j,k))

      Then for each triplet, compute mex(A[i],A[j],A[k]) and sum.

      How many triplets? We can count: 
          The string: 
          E X M M X X X E M E X E X M M
          We need: 
            i: M -> positions: 2,3,8,13,14 -> 5 Ms
            j: E -> positions: 0,7,9,11 -> 4 Es
            k: X -> positions: 1,4,5,6,10,12 -> 6 Xs

          Then we need to count triplets (i,j,k) with i<j<k and i in M, j in E, k in X.

          But note: the condition is the triplet (i,j,k) must be in increasing index order and the characters at i,j,k are 'M','E','X' respectively.

          We can iterate over the Ms, then for each M, then for each E that is after M, then for each X after E.

          Alternatively, we can use our optimized method to compute the count for each (a,b,c) and then multiply by mex(a,b,c) and then we can compare with the brute force.

      We'll do:

          triplets = []
          total = 0
          for i in range(15):
              if S[i]!='M': continue
              for j in range(i+1,15):
                  if S[j]!='E': continue
                  for k in range(j+1,15):
                      if S[k]=='X':
                          total += mex(A[i],A[j],A[k])

          Then total should be 13.

      We'll code that for example 3.

  We'll run the brute force for example 3:

      A = [1,1,2,0,0,2,0,2,0,0,0,0,0,2,2]
      S = "EXMMXXXEMEXEXMM"

      We need to list the triplets (i,j,k) such that S_i='M', S_j='E', S_k='X' and i<j<k.

      M positions: [2,3,8,13,14] -> indices: 2,3,8,13,14 (0-indexed)
      E positions: [0,7,9,11]
      X positions: [1,4,5,6,10,12]

      But note: the triplet must have an M at i, E at j, X at k with i<j<k.

      We cannot have i=2 (which is the first M at index2) then j must be after index2: so E at 7,9,11 -> and then k after j: for each j, the X after j.

      Let's do:

        M at 2: 
            j: E at 7,9,11
                j=7: X after 7: [10,12] -> two Xs: indices10 and 12 -> two triplets: (2,7,10) and (2,7,12)
                j=9: X after 9: [10,12] -> two: (2,9,10) and (2,9,12)
                j=11: X after 11: [12] -> one: (2,11,12) -> total 5 triplets for M at 2.

        M at 3:
            j: E at 7,9,11 -> same as above: 5 triplets: (3,7,10),(3,7,12),(3,9,10),(3,9,12),(3,11,12)

        M at 8:
            j: E at 9,11 -> 
                j=9: X after 9: [10,12] -> two: (8,9,10),(8,9,12)
                j=11: one: (8,11,12) -> total 3

        M at 13: 
            j: E at ? -> after 13: none? -> 0

        M at 14: -> 0

        Total triplets = 5+5+3 = 13 triplets? But we have 13 triplets? Then the sum of mex over 13 triplets is 13? 

        Now we compute the mex for each triplet:

          M at 2: A[2]=2
          E at 7: A[7]=2
          X at 10: A[10]=0 -> mex(2,2,0) = 1 (since 0 and 2 are present -> smallest missing is 1)
          X at 12: A[12]=0 -> mex(2,2,0)=1

          Similarly, 
            (2,7,10): 1
            (2,7,12): 1
            (2,9,10): A[9]=0 -> mex(2,0,0) = 1 (because 0 and 2 -> missing 1)
            (2,9,12): 1
            (2,11,12): A[11]=0 -> mex(2,0,0)=1 -> total for M2: 5

          M at 3: A[3]=0
            (3,7,10): mex(0,2,0)=1 (because {0,2} -> mex=1)
            (3,7,12): 1
            (3,9,10): mex(0,0,0)=1? -> no: mex(0,0,0) = 1? 
                Actually: mex(0,0,0) -> the set {0}, so mex=1.
            (3,9,12): 1
            (3,11,12): 1 -> total 5

          M at 8: A[8]=0
            (8,9,10): mex(0,0,0)=1
            (8,9,12):1
            (8,11,12): mex(0,0,0)=1 -> total 3

          Total sum = 5+5+3 = 13? 

          But the expected answer is 13.

        However, wait: our computed mex for each triplet is 1? Then the total sum would be 13? 

        But the example output is 13.

        So it matches.

        Therefore, our optimized code should output 13.

  But wait: in our optimized code, we broke the triplet by the E. How many E's? 
      E at indices: 0,7,9,11 -> 4.

      For each E at j, we count the Ms before j and the Xs after j, and then for each (a,c) we multiply by mex(a, b, c) (with b=A[j]).

      Let's break down:

        E at 0: skip because we need M before j=0 -> none.

        E at 7: b = A[7]=2
          Ms before 7: 
            M at 2: A[2]=2 -> so a=2 -> count_M[2] = 1 (but note: M at 3 is also before 7? Yes, but we break by a.
            Actually, we have:
                a=0: M0 before 7: M at 3: A[3]=0 -> one M0? and also M at 8 is after 7? -> no, 8>7 -> so only M at 3? 
                    Actually, M at 2: A[2]=2 -> so not M0.
                    M at 3: A[3]=0 -> M0 -> so count_M[0] = 1? 
                a=1: M1? none (because we have M at 2: value2, M at3: value0, M at8: value0 -> no M1)
                a=2: M at 2: value2 -> count_M[2]=1

          Xs after 7: 
            X at 10: A[10]=0 -> X0
            X at 12: A[12]=0 -> X0
            Also, there are X at 4,5,6? But they are before 7? -> so not included.
            So:
                c=0: 2
                c=1: 0
                c=2: 0

          Then we do 9 combinations:

            a=0, c=0: mex(0,2,0)=? 
                set {0,2} -> mex=1 -> 1 * 1 * 2 = 2
            a=0, c=1: mex(0,2,1)=? {0,1,2} -> mex=3 -> 1*0=0? because no X1 -> 0
            a=0, c=2: 0
            a=1, c=0: mex(1,2,0)=? {0,1,2} -> mex=3 -> 0 (because no M1) -> 0
            a=1, c=1:0
            a=1, c=2:0
            a=2, c=0: mex(2,2,0)=1 -> 1 * 1 * 2 = 2
            a=2, c=1:0
            a=2, c=2:0

          Total for E at 7: 2+2 = 4.

        E at 9: b = A[9]=0
          Ms before 9: 
            M at 2: value2 -> a2:1
            M at 3: value0 -> a0:1
            M at 8: value0 -> a0: so total a0:2, a2:1
          Xs after 9: 
            X at 10:0 -> X0:1
            X at 12:0 -> X0:1
            -> c0:2, others 0

          Then:
            a0, c0: mex(0,0,0)=1 -> 2*2=4 -> 1*4=4
            a0, c1:0
            a0, c2:0
            a1, any:0
            a2, c0: mex(2,0,0)=? {0,2} -> mex=1 -> 1*1*2=2
            others:0
          Total for E at9: 4+2=6

        E at 11: b=A[11]=0
          Ms before 11: 
            M at 2: value2 -> a2:1
            M at 3: value0 -> a0:1
            M at 8: value0 -> a0: so a0:2, a2:1
          Xs after 11: 
            X at 12:0 -> c0:1
          Then:
            a0, c0: mex(0,0,0)=1 -> 2*1=2
            a2, c0: mex(2,0,0)=1 -> 1*1=1
            others:0
          Total for E at11: 2+1=3

        Total ans = 4 (from j=7) + 6 (from j=9) + 3 (from j=11) = 13.

      So it matches.

  Therefore, we code accordingly.

  We'll write the code.

  Note: we must use 0-indexed indices.

  One more note: the prefix_M array: we defined prefix_M[a][j] = count of M_a in indices [0, j-1] (so strictly less than j). 
      For j=0: [0, -1] -> 0
      For j=1: [0,0] -> so only index0.

  Similarly, suffix_X[c][j+1] = count of X_c in indices [j+1, N-1]. 

  We have built the arrays accordingly.

  Code:

      We'll write the solution as a function.

  Let's code.

## Final Solution

### Approach
The problem requires finding the sum of the minimum excluded values (mex) for all valid triplets (i, j, k) in a sequence such that the characters at these positions in the string S form the substring "MEX" and the indices are in increasing order. The mex of three numbers is the smallest non-negative integer not present among them.

#### Key Insights:
1. **Problem Analysis**: The problem involves counting valid triplets (i, j, k) where i < j < k, S[i] = 'M', S[j] = 'E', S[k] = 'X', and then computing the mex of their corresponding values in A. Given the constraints (N ≤ 2×10^5), a brute-force approach is infeasible.
2. **Efficient Counting**: For each 'E' in the string (at position j), count the number of 'M's before j and the number of 'X's after j. The mex value depends on the values at these positions.
3. **Prefix and Suffix Arrays**: Precompute prefix sums for 'M's and suffix sums for 'X's to efficiently count valid pairs for any 'E' at position j. The prefix array tracks counts of 'M's with values 0, 1, or 2 up to each position. The suffix array tracks counts of 'X's with values 0, 1, or 2 from each position to the end.
4. **Mex Precomputation**: Precompute mex values for all possible combinations of three values (0, 1, 2) since the values in A are restricted to these.

#### Algorithm:
1. **Prefix Arrays for 'M's**: For each value (0, 1, 2), create a prefix array where each entry at index i represents the count of 'M's with that value up to (but not including) position i.
2. **Suffix Arrays for 'X's**: Similarly, for each value (0, 1, 2), create a suffix array where each entry at index i represents the count of 'X's with that value from position i to the end.
3. **Mex Table**: Precompute a 3x3x3 table storing mex values for all combinations of three values (0, 1, 2).
4. **Iterate over 'E's**: For each 'E' at position j, use the prefix arrays to count 'M's (with values 0, 1, 2) before j and the suffix arrays to count 'X's (with values 0, 1, 2) after j. Multiply these counts by the corresponding mex value and accumulate the result.

#### Complexity Analysis:
- **Time Complexity**: O(N) for building prefix and suffix arrays. The main loop processes each 'E' in O(1) per value (since 3x3 combinations are constant), leading to O(N) overall.
- **Space Complexity**: O(N) for storing prefix and suffix arrays.

### Solution Code
```python
import sys

def main():
    data = sys.stdin.read().splitlines()
    N = int(data[0].strip())
    A = list(map(int, data[1].split()))
    S = data[2].strip()
    
    prefix_M = [[0] * (N + 1) for _ in range(3)]
    for a in range(3):
        for i in range(N):
            prefix_M[a][i + 1] = prefix_M[a][i]
            if S[i] == 'M' and A[i] == a:
                prefix_M[a][i + 1] += 1
                
    suffix_X = [[0] * (N + 2) for _ in range(3)]
    for c in range(3):
        suffix_X[c][N] = 0
        for i in range(N - 1, -1, -1):
            suffix_X[c][i] = suffix_X[c][i + 1]
            if S[i] == 'X' and A[i] == c:
                suffix_X[c][i] += 1

    mex_table = {}
    for a in range(3):
        for b in range(3):
            for c in range(3):
                s = {a, b, c}
                m = 0
                while m in s:
                    m += 1
                mex_table[(a, b, c)] = m

    ans = 0
    for j in range(N):
        if S[j] == 'E':
            b_val = A[j]
            for a in range(3):
                for c in range(3):
                    count_M = prefix_M[a][j]
                    count_X = suffix_X[c][j + 1]
                    mex_val = mex_table[(a, b_val, c)]
                    ans += mex_val * count_M * count_X
    print(ans)

if __name__ == "__main__":
    main()
```

### Explanation
1. **Prefix Arrays for 'M's**: The `prefix_M` array is built such that `prefix_M[a][i]` holds the count of 'M's with value `a` from the start up to (but not including) position `i`.
2. **Suffix Arrays for 'X's**: The `suffix_X` array is built such that `suffix_X[c][i]` holds the count of 'X's with value `c` from position `i` to the end of the string.
3. **Mex Table**: The `mex_table` is precomputed for all possible combinations of three values (0, 1, 2), storing the mex for each combination.
4. **Processing 'E's**: For each 'E' at position `j`, the solution counts all valid 'M's (before `j`) and 'X's (after `j`) for each value combination (0, 1, 2). The mex value for the combination of the 'M' value, 'E' value, and 'X' value is multiplied by the counts and added to the result.
5. **Efficiency**: The use of prefix and suffix arrays allows each 'E' to be processed in constant time (due to fixed value combinations), making the overall solution linear in complexity, O(N), which is optimal for the given constraints.