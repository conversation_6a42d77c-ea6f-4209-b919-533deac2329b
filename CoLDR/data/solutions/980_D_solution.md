# Solution for 980_D.md

Generated at: 2025-07-22 06:14:29
Model: deepseek-reasoner

## Reasoning Process

Alright, I'm trying to solve this programming problem. Let me start by understanding the problem step by step.

The problem has two main parts:

1. **Core Problem**: Given an array of integers, we need to find the minimum number of groups such that in each group, the product of any two integers is a perfect square. Each integer must be in exactly one group, and groups don't have to be contiguous.

2. **Subarray Analysis**: For every contiguous subarray of the original array, we need to compute the minimum number of groups (as defined above) and then count how many contiguous subarrays have an answer equal to k for each k from 1 to n.

The output is an array of n integers where the k-th integer is the count of contiguous subarrays that require exactly k groups.

First, I need to understand the condition for grouping: the product of any two integers in the same group must be a perfect square. What does this imply?

Let me denote two numbers as a and b. Their product a*b is a perfect square. This means that in the prime factorization of a and b, every prime must have an even total exponent in the product.

For example, if a = 12 (2^2 * 3^1) and b = 3 (3^1), then a*b = 36 (6^2) which is a perfect square. But wait, 12*3 = 36, which is indeed a perfect square. However, if I have three numbers, say 12, 3, and 27 (which is 3^3). Then, 12*3 = 36 (perfect square), 3*27 = 81 (9^2, perfect square), but 12*27 = 324 (18^2, also perfect square). So all three can be in the same group? Actually, yes, because the condition is that the product of *any pair* must be a perfect square. So if I have three numbers, they must all pairwise form perfect squares.

But what about negative numbers? The input can include negative integers. For example, -4 and -9: (-4)*(-9)=36, which is a perfect square. Similarly, -4 and 9: -36, which is not a perfect square (since perfect squares are non-negative). So negative numbers complicate things. However, note that the problem says "perfect square", which in mathematics is non-negative. So the product must be a non-negative perfect square. Therefore, two negative numbers multiply to a positive, which can be a perfect square, but a negative and a positive multiply to a negative, which cannot be a perfect square. So, can negative and positive numbers ever be in the same group? Only if their product is non-negative and a perfect square. But wait, if one of them is zero? Zero is a special case.

Wait, zero: 0 multiplied by any number is 0, which is a perfect square. So zero can be grouped with any number? But then, if we have a group with zero and two non-zero numbers, say 0, a, and b. Then 0*a =0 (perfect square), 0*b=0 (perfect square), but what about a*b? If a and b are such that a*b is not a perfect square, then having both a and b in the same group as zero would violate the condition because the product of a and b must also be a perfect square. Therefore, zero doesn't automatically allow any two numbers to be together. Actually, the condition is that *any pair* must have a product that is a perfect square. So if we have zero and two non-zero numbers a and b, we require a*b to be a perfect square. Otherwise, we cannot put them all in one group.

So, the grouping condition is strict: all pairs must have a product that is a perfect square.

Now, the goal is to find the minimum number of groups for a given set (which in our case is a contiguous subarray). How can we model this?

I recall that such problems often involve graph theory. If I consider each number as a vertex, and connect two numbers with an edge if their product is a perfect square, then the condition for a group is that it must be a clique (every two vertices are connected). Then, the problem reduces to covering the graph with the minimum number of cliques. But clique cover is NP-hard in general. However, the constraints say n is up to 5000, but we have to consider all contiguous subarrays (which are O(n^2)), so we cannot do anything exponential.

Alternatively, perhaps there's a better way to represent the numbers so that the grouping condition becomes simpler.

Let me think about the prime factorization. Each integer can be represented by its square-free part. That is, for each integer, we can remove all squared factors. For example, 12 = 2^2 * 3^1, so its square-free part is 3. Similarly, 18 = 2 * 3^2, so square-free part is 2. The product of two numbers is a perfect square if and only if their square-free parts are equal? Not exactly. Let me check: 12 * 3 = 36, which is a perfect square. The square-free part of 12 is 3, and of 3 is 3. So same square-free part. But what about 8 (2^3) and 2 (2^1)? Their product is 16, which is a perfect square. The square-free part of 8 is 2 (because 8 = 2^3 = 2 * 2^2, so removing squares leaves 2), and of 2 is 2. So again same. But what about 6 (2*3) and 24 (2^3*3)? 6*24=144, which is 12^2. The square-free part of 6 is 6, and of 24 is 6 as well (because 24 = 2^3 * 3 = (2^2)*(2*3) so square-free part is 2*3=6). So yes, it seems that two numbers a and b multiply to a perfect square if and only if their square-free parts are equal.

But wait, what about negatives? If a is negative and b is positive? Then the product is negative and cannot be a perfect square. So the sign matters? Actually, the problem states that the product must be a perfect square, which is non-negative. So:

- If both are positive: then we require the square-free parts to be equal.
- If both are negative: same, because the product is positive, and the square-free parts must be equal.
- If one positive and one negative: the product is negative, which is not a perfect square. So they cannot be in the same group.

Also, zero: if one number is zero, then the product with any other number is zero, which is a perfect square. But as we saw earlier, if we have multiple non-zero numbers, they must all have the same square-free part and also the same sign? Actually, no: if we have zero and a positive number, and zero and a negative number, then the two non-zero numbers (one positive and one negative) would have to have their product be a perfect square, which is negative and impossible. So, in a group that contains zero, we cannot have both a positive and a negative number. Actually, we can only have zero and numbers that are all of the same sign? Or wait, no: if we have zero, and then a set of positive numbers that all have the same square-free part, and a set of negative numbers that all have the same square-free part (but then the negatives multiplied together would be positive and a perfect square only if they have the same square-free part, but the issue is between positive and negative: if we have zero, a positive number, and a negative number, then the positive and negative multiplied together must be a perfect square? But that's negative, which is impossible. So actually, in a group containing zero, we can have only non-negative numbers? Or only non-positive? But non-positive would include negative numbers and zero. However, two negative numbers in the group with zero: their product is positive, which must be a perfect square. So if we have zero, we can only have non-zero numbers that are all positive (and have the same square-free part) or all negative (and have the same square-free part). But if we have both positive and negative, even with the same absolute value, then a positive and a negative multiplied would be negative, which is not allowed. Therefore, a group containing zero can only include numbers that are all non-negative? Actually, no: non-negative includes zero and positive. But if we have a positive number and a negative number, even with the same square-free part, they cannot be together because their product is negative. So in a group that contains zero, we can have:

- Only zero: that's a group.
- Zero and one or more positive numbers that all have the same square-free part (and no negatives).
- Zero and one or more negative numbers that all have the same square-free part (and no positives).

But if we have multiple positive numbers with different square-free parts, then even with zero, we cannot put them together because the product of two different positive numbers (with different square-free parts) would not be a perfect square. Similarly for negatives.

So, the grouping condition becomes:

1. Non-zero numbers must be grouped by their square-free part and by sign? Actually, no: for non-zero numbers without zero, we can only group same square-free part and same sign? But wait, two negative numbers: their product is positive, and if they have the same square-free part, then the product is a perfect square. Similarly, two positive numbers with same square-free part: product is a perfect square. But can we mix positive and negative? Only if their product is a perfect square? But that would require the product to be non-negative, which only happens if the product is positive. But the product of a positive and a negative is negative, which cannot be a perfect square. Therefore, positive and negative numbers can never be in the same group, regardless of their square-free part.

So, we can split the numbers by sign? Actually, by sign and then by the square-free part of the absolute value.

But wait: what if we have a negative number? We can take the absolute value and then compute the square-free part. Then, the condition for two negative numbers is the same as for two positive numbers: they must have the same square-free part (from the absolute value). And positive and negative cannot be together.

Now, what about zero? Zero is a special case. However, note that zero can be considered as having a square-free part of 0? Because 0 multiplied by any number is 0, which is a perfect square. But if we have multiple zeros, they can be together. Also, if we have zeros and non-zero numbers, then the non-zero numbers must all be of the same sign and same square-free part? But as we saw, that's restrictive.

But actually, if we have a group with zero and a non-zero number, then the non-zero numbers must form a clique among themselves (because any two non-zero numbers in the group must multiply to a perfect square). Therefore, in a group containing zero, we can only have non-zero numbers that form a clique (i.e., all same square-free part and same sign) and zeros. But if we have zeros and two non-zero numbers with different square-free parts, that's not allowed because the two non-zero numbers' product isn't a perfect square.

Therefore, to minimize the number of groups, we might want to put zeros together with non-zero numbers only if the non-zero numbers form a clique. Otherwise, zeros can form their own group? Actually, zeros can be grouped together without any problem, and then non-zero numbers are separate.

But the problem is to minimize the number of groups. So if we have zeros, we can merge them with a non-zero group if that non-zero group is already present? However, if we have multiple non-zero groups, we cannot merge zeros with more than one group because then the non-zero numbers from different groups would be together and their product might not be a perfect square.

Therefore, the minimal grouping for a set of numbers can be determined by:

1. All zeros can be grouped together? But actually, we can put each zero individually into any group that already contains only zeros or non-zero numbers that are compatible? But to minimize the number of groups, we can put all zeros in one group? Or we can distribute zeros to existing non-zero groups? However, if we have two non-zero groups (say group A: positive with square-free part 1, group B: positive with square-free part 2), then we cannot put zeros in both A and B because then a non-zero from A and a non-zero from B would be in the same group (the group that has zeros and A and B? No, if we put a zero in group A, then group A has zeros and the non-zero A numbers. Similarly, group B has zeros and non-zero B numbers? Then the zeros are split. But actually, each integer must be in exactly one group. So if we have multiple zeros, we can put each zero in a different group? But that would increase the group count.

Actually, we can put all zeros together in one group, and then the non-zero numbers form groups as if the zeros weren't there. But is that minimal? Consider: if we have zeros and a non-zero group (say one positive group with square-free part p). Then we can merge the zeros with that non-zero group. Then the zeros are in the same group as the non-zero numbers. Then we don't need an extra group for zeros. So that saves one group.

But if we have multiple non-zero groups, we can only merge the zeros with one of them. Then, the other non-zero groups remain. So the total number of groups would be (number of non-zero groups) because zeros are merged with one of the groups. If we don't merge, we have (number of non-zero groups + 1) for zeros. So to minimize, we should merge the zeros with one non-zero group. Therefore, the minimal number of groups is:

- If there are no zeros: then the number of groups is the number of distinct square-free parts (for non-zero, considering sign as part of the key? Actually, positive and negative with the same absolute square-free part are different because they cannot be together). So we have to separate by sign and then by square-free part.

- If there are zeros: then the minimal number of groups is the number of distinct non-zero groups (by sign and square-free part) because we can merge zeros with one of the groups. But if there are no non-zero groups (all zeros), then we need only 1 group.

Wait, but if there are zeros and non-zero groups, we can put all zeros into one of the existing non-zero groups. So the total number of groups is the number of distinct non-zero groups (because we don't need an extra group for zeros). However, if there are zeros and no non-zero numbers, then we have one group (for zeros). So:

min_groups = max(1, number_of_distinct_non_zero_groups)

But if there are non-zero groups, then min_groups = number_of_distinct_non_zero_groups (and we merge zeros with one of them). However, what if the non-zero groups are already there, but we have zeros? Then we can merge all zeros into one non-zero group. So we don't need an extra group for zeros. Therefore, the minimal number of groups is the number of distinct non-zero groups (if there is at least one non-zero) or 1 if all are zeros.

But wait, what if there are multiple distinct non-zero groups? Then we cannot put zeros into more than one group? Actually, we put all zeros into one group, but that group can be one of the non-zero groups. Then the zeros are in that one group. So the total groups is the number of distinct non-zero groups (which includes the one that took the zeros). So no extra group.

Therefore, the minimal number of groups for a set S is:

Let T = set of non-zero numbers in S.

Group T by (sign, square-free part of absolute value). Then, the number of distinct groups for T is, say, g.

If T is non-empty, then min_groups = g (because we can put all zeros in S into one of these groups, arbitrarily chosen, but we can choose one group to absorb the zeros without affecting the group's property).

If T is empty (all zeros), then min_groups = 1.

But what about if we have zeros and non-zero, but we don't merge? Then we have g (for non-zero) + 1 (for zeros) = g+1. But merging reduces it to g. So the minimal is g.

However, is it always safe to merge zeros with one non-zero group? Yes, because:

- The zeros and any non-zero number in that group: product is 0, which is a perfect square.

- The zeros and non-zero numbers in other groups: they are in different groups, so no issue.

- Within the non-zero group that has zeros, the non-zero numbers already form a clique (because they have the same sign and same square-free part) so their products are perfect squares.

Therefore, the minimal number of groups is the number of distinct non-zero groups (by sign and square-free part) if there are non-zero numbers, or 1 if all are zero.

But wait, what about the case where we have zeros and multiple non-zero groups? We can merge all zeros into one non-zero group. Then, the zeros are in that group. The other non-zero groups are separate. So the total groups is the number of distinct non-zero groups (which is the same as if there were no zeros). So yes.

Therefore, for a set S, the answer (minimal groups) is:

- If S has at least one non-zero: answer = number of distinct (sign, square-free) pairs for non-zero numbers.

- Else (all zeros): answer = 1.

But what about the case where the set has non-zero numbers and zeros? Then the minimal groups is the number of distinct non-zero groups. Because we can assign all zeros to one of the non-zero groups arbitrarily.

Now, we have to do this for every contiguous subarray. The problem reduces to: for each contiguous subarray, compute the number of distinct (sign, square-free) keys for non-zero elements, and if there are no non-zero elements, then the distinct count is 0, but then the answer is 1. Alternatively, we can define:

For a subarray, let D = distinct keys (for non-zero). Then the minimal groups = D if D>0, else 1.

So the problem becomes: for each contiguous subarray, we want to compute the number of distinct non-zero keys (after normalization to (sign, square-free)) and then set the answer to that distinct count (if >0) or 1 (if no non-zero). Then, we have to count, for each k from 1 to n, the number of contiguous subarrays that have an answer equal to k.

But note: the minimal groups can be 0? Only if the subarray is empty? But the contiguous subarray is non-empty (since the array has n elements, and we consider all contiguous subarrays, which are non-empty). So the minimal groups is at least 1.

So, for each contiguous subarray, the answer is:

ans = distinct_keys if distinct_keys > 0 else 1

Then we want to count the number of contiguous subarrays for which ans = k, for each k from 1 to n.

The challenge: we have up to 5000 elements, so O(n^2) contiguous subarrays. We need an efficient way.

First, we must preprocess each element to get its key.

The key for an element a_i:

- If a_i = 0: then we can ignore? But in the distinct set for non-zero, we skip zeros. So for distinct count, we only consider non-zero.

- For non-zero a_i:

  Let s = sign(a_i) (we can use 1 for positive, -1 for negative)

  Let v = absolute value of a_i.

  Factorize v to get the square-free part. The square-free part is the product of primes that have an odd exponent in the prime factorization.

But note: the constraints say that a_i can be as large as 10^8 in absolute value. Factorizing each number up to 10^8 might be heavy if we do it naively? But we have n=5000, so we can factorize each number once. We need an efficient factorization for numbers up to 10^8.

How to compute the square-free part? We can factorize the number and then for each prime, if the exponent is odd, we include the prime, if even, we exclude. Since the number is up to 10^8, we can precompute a sieve? Or factorize on the fly.

But 10^8 is 100 million, and we have 5000 numbers. We can factorize each number by trial division. The square root of 10^8 is 10000, so we can iterate over primes up to 10000. Actually, we don't need primes necessarily; we can iterate over integers from 2 to sqrt(v). The worst-case is a prime number, which would take about 10000 iterations per number. 5000 * 10000 = 50e6, which is acceptable in Python? Maybe in C++ but in Python it might be borderline. But we can optimize by precomputing a list of primes up to 10000? The number of primes up to 10000 is about 1229. Then we can factorize each number by these primes.

Alternatively, we can do:

For each number, we can remove factors of squares:

```
def square_free(x):
    if x == 0:
        return 0   # but we don't use for zero, so skip?
    # Actually, we are processing non-zero
    res = 1
    # factor out squares
    # consider factors of 2
    cnt = 0
    while x % 2 == 0:
        cnt += 1
        x //= 2
    if cnt % 2 == 1:
        res *= 2

    # odd factors
    f = 3
    while f * f <= x:
        cnt = 0
        while x % f == 0:
            cnt += 1
            x //= f
        if cnt % 2 == 1:
            res *= f
        f += 2
    if x > 1:
        res *= x
    return res
```

But note: what about negative? We are taking the absolute value for v, so we do:

key = (sign, square_free(abs(a_i)))

But for negative, the sign is negative, and the square-free part is from the absolute value.

However, we can represent the key as an integer? But we have to combine sign and the square-free part. We can define:

- For a positive number, key = square_free_value (positive)
- For a negative number, key = - square_free_value? But then negative numbers and positive numbers with the same absolute square-free part would have different keys (one positive and one negative). Alternatively, we can use a tuple: (sign, square_free_value). Then (1, 6) and (-1, 6) are different.

But we can also map these keys to a unique integer. However, for the purpose of distinct count, we can use the tuple.

But note: what if we have a positive number and a negative number with the same absolute square-free part? They get different keys: (1, x) and (-1, x). So they are distinct.

Now, we have an array of keys for each element: for zeros, we can set key to None or a special value that we ignore in the distinct set? But in the distinct count, we skip zeros.

So for each contiguous subarray, we need to compute the distinct count of non-zero keys. Then, the answer for the subarray is that distinct count, unless the distinct count is 0 (meaning the entire subarray is zeros), then the answer is 1.

But note: the distinct count can be 0 only when the subarray has no non-zero elements.

So the problem reduces to:

For each contiguous subarray, let D = number of distinct non-zero keys in that subarray. Then the group count = D if D>0, else 1.

Then we want to count, for each k, the number of subarrays where the group count equals k.

But note: k must be at least 1. The group count is at least 1.

Now, the challenge is to compute over all contiguous subarrays the distinct non-zero keys count (with the adjustment for all zeros).

We can do:

Let A be the original array. Preprocess each element to get a key:

keys = []
for each a_i:
    if a_i == 0:
        keys.append(0)   # but then we have to skip 0? Or we can use a special marker. Actually, we can set key for zero to 0, but then we have to avoid counting 0? But 0 might be a valid key for non-zero? Wait, no: the square-free part for non-zero is at least 1? Because the smallest absolute value is 1. So we can use 0 to represent zero.

Then, for each contiguous subarray, we want the distinct count of keys that are non-zero? But then we have to ignore zeros? Actually, we want to ignore the zeros for distinct count. Alternatively, we can set the key for zero to a special value that we can ignore. But then we have to handle the case when the entire subarray is zeros.

But we can do:

Define an array B where:

B[i] = key for a_i, but if a_i is zero, we set B[i] to a special token, say 0. But then when we compute distinct counts over the subarray, we want to count distinct non-zero keys. However, 0 is not distinct? Actually, we can set the key for non-zero to a non-zero integer (positive or negative). Then, when we see a zero, we set it to 0. Then, the distinct count of non-zero keys in the subarray is the distinct count of the set { B[i] for i in subarray } excluding 0.

But note: if we have multiple zeros, they are all 0. Then the distinct non-zero keys are the distinct non-zero values in the subarray.

Alternatively, we can set the key for zero to a very large constant? But then we have to skip it. It might be easier to handle separately.

We can do:

For each contiguous subarray, we can compute the distinct count of non-zero keys and then set the answer to distinct_count if distinct_count>0, else 1.

But how to compute the distinct count over all contiguous subarrays? There is a standard technique to count distinct elements in all contiguous subarrays, but that counts the distinct elements including all. Here, we want to ignore zeros. Also, we have to account for the fact that if the entire subarray is zeros, the distinct non-zero keys is 0, but the answer is 1.

So we can break the problem:

1. Precompute an array K: for each index, K[i] = 
   - if a_i != 0: (sign, square_free_value) -> we can map this to an integer id to make it easier. For example, we can assign a unique positive integer for each distinct (sign, square_free_value). For negative, we can use negative integers? Or we can assign a unique id regardless of sign. Actually, we can assign an id: we can collect all non-zero keys and map to an integer starting from 1. Then, for zero, we leave as 0.

   Then, the array becomes: for each element, id_i = 
        a unique integer >=1 for non-zero keys, and 0 for zero.

2. Then, for a contiguous subarray, the distinct non-zero keys = distinct non-zero ids in that subarray. Then, the group count = distinct_non_zero if distinct_non_zero>0 else 1.

3. Then, we want to count for each k from 1 to n, the number of contiguous subarrays such that:
   - if distinct_non_zero == 0, then k must be 1.
   - else, k = distinct_non_zero.

But note: distinct_non_zero can be at most the total distinct keys in the entire array. Since n is 5000, the distinct keys are at most 5000.

But the problem: we have to iterate over O(n^2) subarrays. We need an efficient method.

Standard problem: counting distinct numbers in all contiguous subarrays. The standard solution is to use two pointers and frequency arrays? But that usually counts the total distinct elements. However, we are ignoring zeros.

We can use the known method for distinct element counting in subarrays, but skipping zeros. Alternatively, we can use a method that traverses the array and updates the distinct count as we extend the subarray.

But the known efficient methods for distinct element counting over all contiguous subarrays are:

- Using a two-pointer method to avoid recalculating from scratch? But we need counts for each distinct k.

Alternatively, we can use a technique that for each distinct value, we record the positions and then use the fact that the distinct count in a subarray is the number of distinct values that appear at least once. Then, we can use the inclusion-exclusion? But that is complicated.

A common approach is to iterate from left to right and maintain the last occurrence of each non-zero key? Then, we can update the distinct count as we extend the right pointer. But we need to count the number of subarrays that have distinct count equal to d (for d>=0) and then map d to k (if d=0 then k=1, else k=d).

But note: we have to consider all contiguous subarrays. There are n*(n+1)//2 of them.

We can do:

Let f(i) = the distinct count of non-zero keys in the subarray starting at some left and ending at i? Then we can use a dp? Or we can use a two-pointer method to compute the distinct counts for subarrays ending at i.

Actually, we can do:

Let dp[i] be an array of distinct counts for subarrays ending at i? But that would be O(n) per i, so total O(n^2). Since n=5000, O(n^2)=25e6, which might be acceptable in C++ but in Python we have to be careful.

But 5000^2 = 25e6, which in Python might be acceptable? We can try.

Alternatively, we can use a method that updates the distinct count as we extend the subarray. We can maintain an array last_occurrence for each key (for non-zero keys). Then, when moving the right pointer j, we can update the distinct count for subarrays [i, j] for all i? That would be O(n^2).

But we can use a different idea: for each possible distinct count value d, we want to count the number of contiguous subarrays that have distinct non-zero keys equal to d, then we map d to k (if d=0 then k=1, else k=d). Then we aggregate.

So we can iterate over left endpoints and then move right and update distinct count.

Algorithm:

Let L = array of keys (with 0 for zeros).

We precomputed an array L of length n: each element is either 0 (if the original was zero) or an integer >=1 (the id for non-zero).

Then, we can do:

result = [0]*(n+1)   # result[k] for k in 1..n; we'll use index 0 to n

for left in range(n):
    distinct = 0
    freq = {}   # frequency dictionary for non-zero keys? Actually, we only need to know if we've seen the key? We can use a set, but updating set as we extend right. But we can also use an array of zeros for each key, and then update distinct count when a new key is added or when a key count drops to zero? Actually, we can use a frequency array and a distinct counter.

    # But we are going from left to right, so we can extend right until the end.
    # We'll use a frequency array (dictionary) to track the frequency of non-zero keys. But we ignore zeros.

    # Instead, we can do: 
    distinct_count = 0
    # We'll create an array to mark if a key is present? But we don't know the key ids in advance? We can use a set.

    # But using a set and updating as we move right: adding a new element: if it's non-zero, then if it's not in the set, then distinct_count++ and add it. If it's zero, skip.

    # Actually, we can do:

    # Initialize an empty set or a frequency dictionary.

    # Since we are doing O(n) per left, total O(n^2), which is 25e6, acceptable for n=5000? But in worst-case, each inner loop is O(n), so 5000*5000=25e6 iterations. In Python, 25e6 iterations might take a few seconds (about 1-5 seconds in PyPy/C++, but in Python it might be 10-20 seconds? But the constraints say n=5000, and worst-case 25e6 might be acceptable in Pyton? We should test in Pyton: 25e6 iterations of integer operations might be acceptable in Pyton if optimized (using Pyton PyPy or in C++ it's fine, but in Pyton we might need to optimize by using local variables and avoiding function calls).

    # But let's try to design without optimization first.

    freq = {}   # key: non-zero id, value: count (but we only need to know if it's non-zero? Actually, we can use a set and then update distinct_count accordingly.

    # Actually, we can use a boolean array to mark if a key is present? But the keys are mapped to 1..m, where m is the number of distinct non-zero keys. But m can be up to 5000. We can use an array of size m+1.

    # Precomputation: we mapped each non-zero element to an id from 1 to M (M<=5000). Then we can use an array present[1..M] to track if the key is present.

    # Alternatively, we can use a set for the keys that appear. But the set operations are O(1) per operation.

    # Let's do:

    # Precomputation: map non-zero keys to integers starting from 1.
    # Then, in the double loop, we can have an array "count" of size (M+1) (index 0 unused? Or we don't use 0 for non-zero) and a variable distinct_count.

    # How to initialize: for each left, we reset the count array and distinct_count.

    # But resetting an array of size M (which is up to 5000) for each left (5000 times) would be 5000*5000=25e6, which is acceptable? But we can avoid resetting the entire array by using a list that we reset to zeros? Or we can use a global array and update only the ones we change? We can store the changes and then revert? That would be O(1) per left? But the inner loop extends right, so we have to update the counts for each right. Then, at the end of left, we need to reset the counts for the keys that were set.

    # Alternatively, we can use a set and update it as we move left? Actually, we can do:

    # Instead of double loop, we can use a single pointer that moves and we do:

    # Actually, we can use a two pointers that move and update the distinct count? But we need to count the distinct count for every subarray. The standard method is to do two nested loops.

    # Since n=5000, O(n^2) is 25e6, which in Python might run in Pyton in about 10-20 seconds? But the problem constraints might require a more efficient solution? Or perhaps in Pyton we can use pypy or hope that the test machines are fast.

    # But let's think: 25e6 iterations in Pyton: each iteration a few operations. It might pass in Pyton if we code in C++ but in Pyton we need to be efficient.

    # However, we can try to optimize by using a fixed-size array for the frequency and a distinct counter, and then for each left, we do:

    #   reset distinct_count = 0
    #   we have a frequency array (initialized to zeros) of length M+1 (M is total distinct non-zero keys in the entire array). But we cannot reset the entire array for each left because that would be O(M) per left, and M can be 5000, so 5000*5000=25e6 which is acceptable? Actually, 25e6 assignments? That might be heavy.

    # Alternatively, we can avoid resetting the entire array: we can maintain a global frequency array and then for each left, we traverse from left to right and update the frequency, then when we finish a left, we remove the element at left and update. But that would be:

    #   for left from 0 to n-1:
    #       right from left to n-1:
    #           add element at right: if it's non-zero, then update frequency and distinct_count
    #           then, after adding, we have a distinct_count value for the subarray [left, right]
    #           then, we compute k = distinct_count if distinct_count>0 else 1
    #           then, result[k] += 1
    #       then, remove the element at left: if it's non-zero, then decrement frequency, and if frequency becomes 0, then distinct_count--

    # But the removal at the end of the inner loop? Actually, we can do:

    #   Initialize: left=0, then extend right from 0 to n-1, maintaining distinct_count and freq.
    #   Then, for left=0, we have all subarrays starting at 0.
    #   Then, when moving to left=1, we remove the element at index 0, and then extend right from the last position? But we can extend right from left to n-1, and we can update as we move left and right.

    # Actually, we can use a two-pointer method that moves the left pointer and the right pointer only moves forward. This is the standard method for "all subarrays" with two nested loops but the inner loop moves right from left to the end. Then, after we finish a left, we remove the left element.

    # Algorithm:

    freq = [0]*(max_id+1)   # max_id is the maximum id we assigned; if we have M distinct keys, max_id = M (and we use 1-indexing: ids from 1 to M). But we have to map the keys to 1..M. Also, we ignore 0.

    distinct_count = 0
    right = 0
    # But we want to do for each left, so:

    # Initialize: for left=0, we start with an empty array? Then we extend right from left to n-1.

    # Instead, we can do:

    #   for left in range(n):
    #       right = left
    #       while right < n:
    #           add a[right]
    #           update distinct_count
    #           then compute k = distinct_count if distinct_count>0 else 1
    #           then result[k] += 1
    #           right += 1
    #       then remove a[left] and update distinct_count and freq
    #
    # But the inner loop runs n, then n-1, ... so total n(n+1)/2, which is about 12.5e6, which is half of 25e6. So 12.5e6 iterations.

    # How to add an element at index right?

    def add_element(x):
        nonlocal distinct_count
        if x == 0:   # zero: skip
            return
        if freq[x] == 0:
            distinct_count += 1
        freq[x] += 1

    def remove_element(x):
        nonlocal distinct_count
        if x == 0:
            return
        freq[x] -= 1
        if freq[x] == 0:
            distinct_count -= 1

    Then, we can do:

    # Initialize freq array to zeros and distinct_count=0.
    # But we can do the entire process without global initialization? We need to reset for each left? Actually, we can use the fact that we are moving left from 0 to n-1, and we remove the left element at the end of the inner loop.

    # Actually, we can start with left=0, then we extend right from 0 to n-1, then when we move to left=1, we remove the element at index0, then we can extend right from 1 to n-1? But we already extended right to n-1 for left=0. So we can do:

    #   We can use a two-pointer that moves the left pointer and the right pointer starts at the current left and moves to the end.

    # Alternatively, we can do:

    #   freq = [0]*(max_id+1)
    #   distinct_count = 0
    #   for left in range(n):
    #       # Start from the current left, and extend right from left to n-1
    #       # But we want to continue from the state at the end of the previous left? Actually, we can do:

    #   Actually, we can use a single two-pointer that moves the right pointer only forward, and we move left from 0 to n-1, and we update the frequency as we remove the left element.

    #   But for a fixed left, we start with the current state (which is the state after having processed the subarray [left, right] for the previous left? Actually, we want to start at left and extend right from left to n-1.

    #   We can do:

    #   Let r = left   # we start at left, then extend r to n-1
    #   But then we have to add the element at left? Actually, we start with an empty state? Then for each left, we start from left and go to n-1, adding each element.

    #   But that would be O(n^2) and 12.5e6, which is acceptable.

    #   So:

    #   Initialize: 
    #       freq = [0]*(max_id+1)   # we do this once outside the loops? Then we need to reset it? Or we can do:

    #   Actually, we can do:

    #   We'll use one frequency array and one distinct_count variable. We'll use a nested loop and at the end of the inner loop (for a fixed left) we remove the element at left? But then the state for left+1 is the state for [left+1, n-1]? But we have to remove left and then for left+1, we start with [left+1, n-1]? That is not the same as starting from scratch for [left+1, left+1] and then extending.

    #   Alternatively, we can reset the frequency array for each left? But that would be O(M) per left, which is O(n^2) worst-case (M up to 5000, then 5000*5000=25e6, which is acceptable? But then we do 12.5e6 inner loop iterations and also 5000 resets of the frequency array (each reset O(M)=5000, so 5000*5000=25e6) which is 25e6 + 12.5e6 = 37.5e6, which is acceptable.

    #   But we can avoid resetting the entire frequency array by storing the state and then clearing only the keys that were modified? Actually, we can do:

    #   We can use a dictionary for frequency? Then we can clear it by setting to empty, which is O(1)? But then the distinct_count=0.

    #   Then, the inner loop:

    #   for left in range(n):
    #       distinct_count = 0
    #       freq = {}
    #       for right in range(left, n):
    #           x = L[right]   # the key at right
    #           if x != 0:
    #               # if x not in freq, then set to 0
    #               if x not in freq:
    #                   freq[x] = 0
    #               if freq[x] == 0:
    #                   distinct_count += 1
    #               freq[x] += 1
    #           # now, the current distinct_count is the distinct non-zero keys in [left, right]
    #           k_val = distinct_count if distinct_count > 0 else 1   # but distinct_count==0 only if we haven't added any non-zero? Actually, if the entire subarray is zeros, then distinct_count==0.
    #           # But note: if we have non-zero, distinct_count>=1. So if distinct_count==0, then we have no non-zero.
    #           # Then, we do:
    #           if k_val <= n:   # k_val is at most n, so we can
    #               result[k_val] += 1

    #   Then, after inner loop, we move to next left.

    #   Total operations: inner loops run about n(n+1)/2, which is about 12.5e6 for n=5000.

    #   But each inner step: we check if x in freq. Using a dictionary, that is O(1). So total time O(12.5e6), which is acceptable.

    #   But memory: we are creating a new dictionary for each left? Then, we have 5000 dictionaries. But the total memory might be acceptable because the dictionaries for different lefts are independent? But worst-case, the dictionary for a left might have up to (n-left) distinct keys. The total memory would be about the sum_{left} (n-left) distinct keys? But the distinct keys per left are at most the total distinct keys in the entire array? Actually, no: for a fixed left, the distinct keys in the subarray starting at left can be at most the total distinct keys in the entire array, which is M (<=5000). So each dictionary has at most M keys. Then total memory is 5000 * 5000 * (overhead of dictionary) which might be 25e6 keys? That is 25e6 * (some bytes) which might be 100-200 MB? Acceptable.

    #   Alternatively, we can use a single frequency array and reset it by using a list that we reset for each left? But then we have to reset an array of size M (the distinct keys) but we don't know M? Actually, we have mapped the keys to 1..M, so we can have an array of size M+1 (initialized to 0) for the entire run? Then for each left, we set all the entries that were set in the inner loop back to 0? But that requires knowing which indices were set? Or we can use a list of length M+1 and then we have to clear only the ones we set? But we don't track which we set.

    #   We can use an array "freq" of size max_id+1 (initialized to 0) and then for each left, we iterate the inner loop. Then, after the inner loop, we have to reset the frequencies of the keys that we set to 0? How? We can store the keys that we modified in the inner loop? Then we can iterate over that list and set to 0.

    #   Alternatively, we can use a global frequency array and then for each left, we start from the state of the previous left, but we remove the element at left. Then, we don't need to reset the entire array.

    #   How about:

    #   We do:

    #   freq = [0]*(max_id+1)
    #   distinct_count = 0
    #   # we start with left=0, right=0, then we extend right to n-1, then we remove left=0, then we move to left=1 and we have the state for [1, n-1]? But we want to start at left=1 with an empty state? Actually, no: we want to consider all subarrays starting at left=1, which are [1,1], [1,2], ... [1, n-1]. So we have to start from scratch for left=1? 

    #   Therefore, we cannot reuse the state from left=0? Because at the end of left=0, we have the entire array, and then we remove a[0], then we have the state for [1, n-1]. But for left=1, we want to consider only [1,1] first? Then we have to remove everything from 1 to n-1? That is messy.

    #   So the simpler solution: for each left, we start with an empty state and then extend right from left to n-1. We use a dictionary for frequency. Since the total number of operations is 12.5e6, and dictionary operations are O(1) amortized, it should be acceptable.

    Steps:

        Precomputation:
            Map each non-zero element to a key: 
                key = (sign, square_free_value) -> mapped to an integer id. We can do:

                mapping = {}
                next_id = 1
                keys = []
                for a_i in a:
                    if a_i == 0:
                        keys.append(0)
                    else:
                        sign = 1 if a_i>0 else -1
                        sq_free = square_free(abs(a_i))
                        # represent the key as (sign, sq_free)
                        # but we can use a tuple (sign, sq_free)
                        t = (sign, sq_free)
                        if t not in mapping:
                            mapping[t] = next_id
                            next_id += 1
                        keys.append(mapping[t])

            Then, we have an array "keys" of length n.

        Then, initialize result = [0]*(n+1)   # index 0 to n; we will use index 1 to n.

        For left in range(n):
            freq = {}   # dictionary for frequency of non-zero keys
            distinct_count = 0
            for right in range(left, n):
                k_val = keys[right]
                if k_val == 0:
                    # zero, skip
                    pass
                else:
                    # if k_val not in freq, then we initialize to 0
                    if k_val not in freq:
                        freq[k_val] = 0
                    if freq[k_val] == 0:
                        distinct_count += 1
                    freq[k_val] += 1

                # Now, for the subarray [left, right], the group count is:
                if distinct_count == 0:
                    group_count = 1
                else:
                    group_count = distinct_count

                # But note: group_count might be greater than n? No, because distinct_count is at most (right-left+1) and that is <=n, but we have to cap? Actually, k from 1 to n, so if group_count>n, we skip? But it won't happen because distinct_count is at most the total distinct keys in the entire array, which is <= n.

                if group_count <= n:
                    result[group_count] += 1

        Then, output: for k from 1 to n, output result[k]

        But note: we are counting from k=1 to n, and the array result has size n+1 (index 0..n). We output for k=1 to n: result[1] to result[n]

        Example: n=2, a=[5,5]

        Precompute keys:
            For 5: sign=1, square_free(5)=5 -> t=(1,5). Assign id=1.
            Then keys = [1,1]

        Then, for left=0:
            right=0: k_val=1 -> distinct_count=1, group_count=1 -> result[1] +=1
            right=1: k_val=1 -> distinct_count remains 1 (because we have two 1s: freq[1] becomes 2, but distinct_count is 1). Then group_count=1 -> result[1] +=1
        For left=1:
            right=1: k_val=1 -> distinct_count=1, group_count=1 -> result[1] +=1
        So result[1]=3, result[2..n]=0.

        Then output: for k=1:3, for k=2:0 -> "3 0"

        Example 2: n=1, a=[0]
            keys = [0]
            left=0: 
                right=0: k_val=0 -> distinct_count=0 -> group_count=1 -> result[1] +=1
            Output: [1] -> "1"

        Example 3: n=5, a=[5, -4, 2, 1, 8]

        Let's compute keys:

        5: (1,5) -> id1
        -4: (-1, square_free(4)): 4 = 2^2 -> square_free=1? So (-1,1) -> id2
        2: (1,2) -> id3
        1: (1,1) -> id4? But 1: square_free(1)=1, so (1,1) -> id4
        8: (1, square_free(8)): 8=2^3 -> square_free=2 -> (1,2) -> id3

        So keys = [1, 2, 3, 4, 3]

        Now, compute for each contiguous subarray:

        We have to compute the distinct non-zero keys.

        The subarrays:

        [5] -> distinct=[1] -> group=1
        [5,-4] -> distinct=[1,2] -> group=2
        [5,-4,2] -> distinct=[1,2,3] -> group=3
        [5,-4,2,1] -> distinct=[1,2,3,4] -> group=4
        [5,-4,2,1,8] -> distinct=[1,2,3,4] (because 8 has id3, which is already present) -> group=4

        [-4] -> group=1
        [-4,2] -> distinct=[2,3] -> group=2
        [-4,2,1] -> distinct=[2,3,4] -> group=3
        [-4,2,1,8] -> distinct=[2,3,4] (because 8 is 3, already there) -> group=3

        [2] -> group=1
        [2,1] -> distinct=[3,4] -> group=2
        [2,1,8] -> distinct=[3,4] (8 is 3, so distinct: 3 and 4) -> group=2

        [1] -> group=1
        [1,8] -> distinct=[4,3] -> but 3 is already in the set? Actually, 1 is id4, 8 is id3 -> distinct [4,3] -> group=2

        [8] -> group=1

        Now, count by group count:

        group=1: 
            [5], [-4], [2], [1], [8] -> 5
        group=2:
            [5,-4], [-4,2], [2,1], [1,8], [5,-4,2,1,8]? Wait, no: 
            [5,-4] ->2
            [-4,2] ->2
            [2,1] ->2
            [1,8] ->2
            and also [2,1,8]? But [2,1,8]: distinct keys: 3 (for 2) and 4 (for 1) and 3 (for 8, but duplicate) -> distinct_count=2 -> group=2 -> so that's 5 subarrays for group=2? But the example output says for k=2:5? Actually, the example output is "5 5 3 2 0", meaning for k=1:5, k=2:5, k=3:3, k=4:2, k=5:0.

        Now, group=2: 
            [5,-4] ->2
            [-4,2] ->2
            [2,1] ->2
            [1,8] ->2
            [2,1,8] -> distinct_count=2 -> group=2 -> that's 5? But wait, we have:

            Also, [5,-4,2]? That's group=3 -> not 2.
            [5,-4,2,1] -> group=4 -> not 2.
            [5,-4,2,1,8] -> distinct_count=4? Actually, we have keys: 1,2,3,4 -> distinct_count=4 -> group=4.

            Also, [ -4,2,1] -> group=3 -> not 2.

            What about [5,-4,2,1,8]? We already did: distinct_count=4.

            But wait, the subarray [5,-4,2,1,8] has distinct non-zero keys: 
                5 (id1), -4 (id2), 2 (id3), 1 (id4), 8 (id3) -> distinct: 1,2,3,4 -> distinct_count=4 -> group=4.

            Then, how do we get 5 for group=2? 

            We have:
                [5] ->1
                [5,-4] ->2
                [5,-4,2] ->3
                [5,-4,2,1] ->4
                [5,-4,2,1,8] ->4
                [-4] ->1
                [-4,2] ->2
                [-4,2,1] ->3
                [-4,2,1,8] ->3 (because keys: -4(id2),2(id3),1(id4),8(id3) -> distinct:2,3,4 -> distinct_count=3 -> group=3
                [2] ->1
                [2,1] ->2
                [2,1,8] -> distinct: 2 (id3), 1 (id4), 8 (id3) -> distinct:3,4 -> count=2 -> group=2
                [1] ->1
                [1,8] ->2
                [8] ->1

            Now, count for group=1: 5 (as above: [5], [-4], [2], [1], [8])
            group=2: [5,-4], [-4,2], [2,1], [1,8], [2,1,8] -> 5
            group=3: [5,-4,2], [-4,2,1], [-4,2,1,8] -> 3? But wait, [5,-4,2] is group=3, [-4,2,1] is group=3, and [-4,2,1,8] is group=3? That's 3.
            group=4: [5,-4,2,1], [5,-4,2,1,8] -> 2
            group=5:0

            So the counts: k=1:5, k=2:5, k=3:3, k=4:2, k=5:0 -> matches the example.

        Therefore, the code should be:

        Steps:

        Precomputation for square-free:

            def get_square_free(x):
                # x is positive integer
                if x == 0:
                    return 0
                res = 1
                # factor out 2
                cnt = 0
                while x % 2 == 0:
                    cnt += 1
                    x //= 2
                if cnt % 2 == 1:
                    res *= 2
                f = 3
                while f * f <= x:
                    cnt = 0
                    while x % f == 0:
                        cnt += 1
                        x //= f
                    if cnt % 2 == 1:
                        res *= f
                    f += 2
                if x > 1:
                    res *= x
                return res

        Then, for each element in the array:

            if a_i == 0: key = 0
            else:
                sign = 1 if a_i>0 else -1
                sqf = get_square_free(abs(a_i))
                key_tuple = (sign, sqf)

        Then, map key_tuple to an integer id.

        Then, create the keys array.

        Then, initialize result = [0]*(n+1)   # index 0 to n

        Then, for left in range(n):
            freq = {}   # dictionary for non-zero keys: key (id) -> count
            distinct = 0
            for right in range(left, n):
                k_val = keys[right]
                if k_val != 0:
                    # if we haven't seen k_val, initialize to 0
                    if k_val not in freq:
                        freq[k_val] = 0
                    if freq[k_val] == 0:
                        distinct += 1
                    freq[k_val] += 1
                # Now, the group count for this subarray:
                if distinct == 0:
                    group_count = 1
                else:
                    group_count = distinct
                if group_count <= n:
                    result[group_count] += 1

        Then, output: for k in range(1, n+1): print(result[k], end=' ')

        But note: the problem says: output n space-separated integers, so we output for k=1 to k=n.

        However, we must consider the performance: for n=5000, the inner loop runs about 12.5e6 times. In Python, 12.5e6 iterations might be acceptable? Let's test: in Pyton, a simple loop of 12.5e6 might take about 1-2 seconds? But we have dictionary operations inside. Each dictionary operation (lookup and update) is O(1) amortized, so total 12.5e6 * (a few operations) should be acceptable.

        But worst-case, the dictionary might be large? But the total distinct keys per left is at most the number of distinct keys in the entire array, which is at most 5000. So the dictionary is efficient.

        Let me test with n=5000: worst-case, we have 12.5e6 iterations. In Pyton, each iteration is a few operations. We can assume 100ns per iteration? Then 12.5e6 * 100ns = 1.25e9 ns = 1.25 seconds. That should be acceptable.

        However, worst-case, the square-free factorization might be heavy for 5000 numbers? The get_square_free function: for each number, we iterate up to sqrt(x). The worst-case x is 10^8, which sqrt is 10000. Then the inner loop for factors: we iterate over odd numbers from 3 to 10000, which is about 5000 numbers. Then worst-case per number: 5000 iterations. Then for 5000 numbers: 5000*5000=25e6, which is acceptable.

        But we can optimize the factorization by breaking early? The function I wrote breaks when f*f<=x.

        Alternatively, we can precompute the smallest prime factor (spf) for numbers up to 10000? But 10000 is not too big. However, we only have 5000 numbers, and each number is factorized once, so 5000 * (number of primes) is acceptable.

        But the current get_square_free function uses trial division by integers. We can optimize by iterating only over primes. We can precompute primes up to 10000 (which is about 1229 primes). Then the inner loop runs 1229 iterations per number. Then 5000*1229 = 6.145e6, which is acceptable.

        Steps for precomputing primes up to 10000:

            max_val = 100000000  # 10^8
            # but we need primes up to sqrt(max_val)=10000
            sieve = [True]*(10001)   # index 0 to 10000
            sieve[0]=sieve[1]=False
            primes = []
            for i in range(2, 10001):
                if sieve[i]:
                    primes.append(i)
                    for j in range(i*i,10001,i):
                        sieve[j]=False

            Then, in get_square_free, we use these primes.

        But the problem: the input numbers can be negative? But we take absolute value, so positive.

        Alternatively, we can do without the primes? The current trial division by integers might be acceptable? The worst-case is a prime number: then we iterate from 2 to sqrt(x). For x=10^8, sqrt=10000, which is about 10000/2 = 5000 odd numbers? Then 5000 per number, and 5000 numbers: 25e6, which is acceptable in Pyton? It might be borderline in Pyton (in C++ it's acceptable). But we can try to use the prime list for efficiency.

        Since we are in Pyton, let's precompute primes up to 10000 to reduce the inner loop.

        Revised get_square_free with primes:

            def get_square_free(x, primes):
                if x == 0:
                    return 0
                res = 1
                # factor out primes
                for p in primes:
                    if p * p > x:
                        break
                    if x % p == 0:
                        cnt = 0
                        while x % p == 0:
                            cnt += 1
                            x //= p
                        if cnt % 2 == 1:
                            res *= p
                if x > 1:
                    res *= x
                return res

        But note: we have to include the prime 2? The primes list should include 2.

        Precompute primes:

            n_val = 10000
            sieve = [True]*(n_val+1)
            sieve[0]=sieve[1]=False
            primes = []
            for i in range(2, n_val+1):
                if sieve[i]:
                    primes.append(i)
                    for j in range(i*i, n_val+1, i):
                        sieve[j] = False

        Then, call get_square_free(x, primes) for each non-zero absolute value.

        But note: what if x==1? Then the loop breaks immediately and returns 1. Correct.

        What about x=4? 
            primes: 2 -> 4%2==0, then cnt=2 (even) so not multiplied, then x becomes 1 -> returns 1. Correct.

        x=8: 
            p=2: 8%2=0 -> cnt=3 (odd) -> multiply by 2, then x becomes 1 -> res=2. Correct.

        x=9: 
            p=2: skip, p=3: 9%3=0 -> cnt=2 (even) -> res=1, then x=1. Correct.

        x=12: 12=2^2*3 -> p=2: cnt=2 (even) -> x=3; then p=3: 3%3=0 -> cnt=1 (odd) -> res=1*3=3. Correct.

        So it's correct.

        Now, the code:

            import math
            n_val = 10000
            # Precompute primes up to 10000
            sieve = [True] * (n_val+1)
            sieve[0] = sieve[1] = False
            primes = []
            for i in range(2, n_val+1):
                if sieve[i]:
                    primes.append(i)
                    for j in range(i*i, n_val+1, i):
                        sieve[j] = False

            # Now, read input
            n = int(input().strip())
            a = list(map(int, input().split()))

            # Precompute the keys for each element
            mapping = {}
            next_id = 1
            keys = [0] * n

            for idx, num in enumerate(a):
                if num == 0:
                    keys[idx] = 0
                else:
                    sign = 1 if num>0 else -1
                    abs_num = abs(num)
                    sqf = get_square_free(abs_num, primes)
                    t = (sign, sqf)
                    if t not in mapping:
                        mapping[t] = next_id
                        next_id += 1
                    keys[idx] = mapping[t]

            # Now, initialize result array
            res_arr = [0] * (n+1)   # index 0 to n

            for left in range(n):
                freq = {}
                distinct = 0
                for right in range(left, n):
                    k_val = keys[right]
                    if k_val != 0:
                        if k_val not in freq:
                            freq[k_val] = 0
                        if freq[k_val] == 0:
                            distinct += 1
                        freq[k_val] += 1
                    # Compute group count for this subarray
                    if distinct == 0:
                        group_count = 1
                    else:
                        group_count = distinct
                    if group_count <= n:
                        res_arr[group_count] += 1

            # Output: for k from 1 to n: res_arr[k]
            output = []
            for k in range(1, n+1):
                output.append(str(res_arr[k]))
            print(" ".join(output))

        Let me test with the examples.

        Example 1: n=2, a=[5,5] -> keys=[1,1] -> then res_arr[1]=3, then output "3 0"

        Example 2: n=1, a=[0] -> keys=[0] -> for left=0: distinct=0 -> group_count=1 -> res_arr[1]=1 -> output "1"

        Example 3: n=5, a=[5,-4,2,1,8] -> we computed the keys as [1,2,3,4,3] and then the counts: 
            k=1:5, k=2:5, k=3:3, k=4:2, k=5:0 -> output "5 5 3 2 0"

        But the example output is "5 5 3 2 0", which matches.

        However, the problem says: 
          Input: 5
                  5 -4 2 1 8
          Output: 5 5 3 2 0

        So we are good.

        But note: what if the same key appears multiple times? The distinct count only counts distinct keys. The frequency dictionary is updated correctly.

        One more test: two zeros. 
          n=2, a=[0,0]
          keys=[0,0]
          For left=0: 
              right=0: distinct=0 -> group_count=1 -> res_arr[1] +=1
              right=1: distinct=0 -> group_count=1 -> res_arr[1] +=1
          For left=1: 
              right=1: distinct=0 -> group_count=1 -> res_arr[1] +=1
          Then res_arr[1]=3, output "3 0"

        This matches the first example? Actually, the first example was [5,5] -> output "3 0", and [0,0] also gives "3 0".

        But the problem: the condition for zeros: we can put them in the same group. So the minimal groups for [0,0] is 1? Then the entire subarray [0,0] has group count 1. And [0] has group count 1, [0] (the other) has group count 1. So three subarrays, all with group count 1.

        So it's correct.

        Now, we have to consider the constraints: n=5000. The inner loop runs about 12.5e6 times. Let me test in Pyton: 12.5e6 iterations with dictionary operations.

        We can run a simple test: 
            for i in range(5000):
                for j in range(i,5000):
                    do a few dict operations

        How long? 

        But worst-case, the dictionary operations are O(1) amortized, so it should be around 12.5e6 * constant.

        I think it will run in Pyton in 5-10 seconds? But the problem constraints say n=5000, and worst-case 12.5e6. In Pyton, we might need to optimize by using pypy or by using local variables.

        Alternatively, we can try to optimize the inner loop by using a list for frequency? But we mapped the keys to 1..M, and M is the total distinct non-zero keys in the entire array. Then we can use an array "freq" of size (M+1) and a distinct_count variable. Then, for each left, we reset the freq array to zeros? But we only need to reset the part that we use? 

        How to reset: we can use a list of zeros of length M+1. Then for each left, we reset the entire array? That is O(M) per left, which is 5000 * 5000 = 25e6, which is acceptable? But then the inner loop:

            for left in range(n):
                # reset freq: we set all to 0? But we can do:
                #   freq = [0]*(M+1)
                distinct_count = 0
                for right in range(left, n):
                    k_val = keys[right]
                    if k_val == 0:
                        ...
                    else:
                        if freq[k_val] == 0:
                            distinct_count += 1
                        freq[k_val] += 1
                # Then, after the inner loop, we don't need to reset because we will reset at the start of the next left.

        But the problem: we are creating a new list of size M+1 for each left. The time to create the list: M+1 per left, which is 5000 * 5000 = 25e6, which is acceptable? And the space: we create 5000 lists of size M+1? Each list is about 5000 * 4 bytes? Then 5000 * 5000 * 4 = 100e6 bytes = 100 MB? Acceptable.

        But the inner loop then does:

            for right from left to n-

## Final Solution

