# Solution for abc306_e.md

Generated at: 2025-07-22 06:26:16
Model: deepseek-reasoner

## Reasoning Process

We are given: N, K, Q and a list of Q updates (X_i, Y_i)
We start with an array A of N zeros.

After each update (which changes A[X_i] to Y_i), we need to compute:
  f(A) = sum of the top K elements in A.

Constraints: 
  N, Q up to 500,000 and Y_i up to 10^9.

Brute force: 
  After each update, we could sort the entire array (or use a heap) and take the top K.
  Time: O(Q * N log N) -> too slow.

We need an efficient solution.

Idea:
  We note that we only care about the top K elements. However, when we update one element, 
  it might enter the top K, leave the top K, or remain in the top K but change the sum.

We can maintain:
  - The current array A of size N (we can update in O(1) per update).
  - A data structure that can efficiently track the top K elements and update when one element changes.

We can consider using two multisets (or two heaps) for the top K and the rest. However, we have to handle duplicates and removals.

Alternatively, we can use a Fenwick tree or segment tree for order statistics and sum of the largest K? 
But note: we need to update one element and then quickly get the sum of the top K.

Another idea:
  Maintain a data structure that can:
    - Insert a value
    - Remove a value
    - Get the sum of the largest K elements.

We can use two balanced BSTs (or simulated by two heaps, but removal in heap is tricky) or a balanced BST that supports order statistic and sum.

But note: we have to update an element. Actually, each update changes one element: 
  We have the old value at position X_i (say old_val) and the new value (Y_i).

So we need to:
  - Remove old_val from the data structure and insert new_val.

And then compute the sum of the top K.

We can maintain:
  - A multiset (or sorted list) for all elements, but then getting the top K sum would be O(K) per query, which is O(Q*K) = 500000*500000 -> 250e9, too slow.

We need a way to get the sum of the top K quickly without iterating over K elements.

We can use a Fenwick tree or segment tree for the array of values? But note: the values are not fixed and we have to consider the entire array.

Alternatively, we can maintain:
  - A max-heap for the entire array? But then how to get the top K quickly?

We can use a segment tree that stores the entire array and supports updating one element and then querying the top K? 
But building a segment tree that aggregates the entire array by value (like a value set) is possible with coordinate compression and a Fenwick tree for the sum.

Approach with Fenwick tree (or segment tree) for the frequency and sum over values:

  Steps:
    1. We will have coordinate compression for the possible values (but note: values are up to 10^9 and Q up to 500000, so we can compress the update values and initial zeros? But note: initially all zeros, and then each update changes one element to a value in [0, 10^9]. Also, we have Q updates, so at most 2*Q distinct values? Actually, we have initial zeros and then update values, so distinct values = 1 (for zeros) + distinct update values? But updates can repeat the same value? So we compress all values that appear in the updates and zero.

    2. We maintain:
        - A Fenwick tree (or segment tree) for the frequencies of the compressed values, and another for the sum of the values (i.e., the value multiplied by the frequency). But note: we are not summing over indices but over the entire array of values.

        However, we need the top K largest elements. We can do a binary search over the values to find the threshold such that the count of numbers >= threshold is at least K, and then sum the numbers above that threshold and a part of the threshold.

        Specifically, we can:
          - Find the smallest value x such that the count of numbers >= x is <= K? Actually, we want the K-th largest element and then the sum of all numbers above that and the K-th largest element might be duplicated.

        Actually, we can do:
          Let T be the value such that the count of numbers > T is < K, and the count of numbers > T-1 is >= K? 
          Alternatively, we can use the Fenwick tree to get the kth largest element? Then:
            Step 1: Find the kth largest element (say x) and then:
            Step 2: Sum = (sum of all numbers > x) + (K - count of numbers > x) * x.

        But note: if there are duplicates, we might have more than one x. We need to know how many numbers are strictly greater than x, and then take the remaining from x.

        This can be done in O(log(max_value)) per query? But we have Q queries, so O(Q * log(max_value)) = 500000 * log(10^9) ~ 500000 * 30 = 15e6, which is acceptable.

        However, the problem is that we have to update the Fenwick trees for each update. Each update: we remove the old value and add the new value. Then we do a query for the top K.

        Steps for one update:
          old = A[X_i-1]   (since we use 0-indexed)
          new = Y_i
          Update A[X_i-1] = Y_i

          Then:
            Decrement the frequency of old in the Fenwick trees (both frequency and sum) and increment for new.

          Then, to compute f(A):
            We want to find the kth largest element (k = K) and then compute the sum as described.

        But note: kth largest is the (total elements - k + 1)th smallest? Alternatively, we can build the Fenwick tree for the values in descending order? 

        Actually, we can compress the values and then create a Fenwick tree that goes from high to low? Or we can use the same Fenwick tree and then do the kth largest by doing:

          total = N   (because we have N elements, but note: we update one element at a time, but the array size is fixed to N; we are storing the entire array)

          Actually, we have N elements. The kth largest element is the (N - K)th smallest? No, that would be the (K+1)th largest? 

        Alternatively, we can store the Fenwick tree for the entire value set (compressed) and then:

          We want to get the kth largest: 
            We can do a binary search over the values (from high to low) to find the smallest value x such that the cumulative frequency from x to max_value is >= K.

          Then, we can break the sum into:
            Let:
              count_above = frequency of values > x
              count_x = frequency of x
            Then we need to take all values above x and then (K - count_above) values from x.

          The sum would be:
            sum_above = the sum of all values > x
            sum_partial_x = (K - count_above) * x

          Then f(A) = sum_above + sum_partial_x

        How to get the cumulative frequency and sum from x to max_value? We can store:

          Let the compressed array of distinct values be in ascending order: v[0] < v[1] < ... < v[m-1]

          We want to get:
            freq_ge(x) = frequency of values >= x
            sum_ge(x) = sum of values >= x

          But note: we want to break at x? Actually, we are going to use x as the threshold. We want the smallest value x such that the frequency of values >= x is at least K.

          Then we break the sum: we take all values above x (which are > x) and then as many x as needed to make up K.

        However, note: we have to avoid taking too many x. So:

          Let:
            total_freq_ge_x = freq_ge(x)
            total_freq_ge_x_plus = freq_ge(x+1)   [which is the frequency of values > x]

          Then the number of x we need is: K - total_freq_ge_x_plus

          Then the sum is: 
            sum_ge(x+1) + (K - total_freq_ge_x_plus) * x

        So we need to be able to get:
          freq_ge(x): the frequency of values >= x
          sum_ge(x): the sum of values >= x

        But note: we can get freq_ge(x) = total_frequency - freq_lt(x) = freq_ge(x) we can compute if we have a Fenwick tree for frequencies that supports prefix sums? Actually, if we build a Fenwick tree for the entire range (from min to max) in ascending order, then:

          freq_ge(x) = query_range(x, max_value)   -> but Fenwick tree typically does prefix. We can do:

          If we index the Fenwick tree from 1 to m (the compressed array), then:
            freq_ge(x) = total_frequency - prefix_sum(x-1)   [if we store frequencies in a Fenwick tree for prefix sums from 0 to x]

          Alternatively, we can store the Fenwick tree in descending order? Or we can store the Fenwick tree for the entire array and then for the descending part we can use:

          Actually, we can also build a Fenwick tree for the suffix? Or we can simply use the same Fenwick tree and compute:

          total_frequency = N (but note: we are updating, so we have the entire array? Actually, we have N elements, so total_frequency is always N? Yes, because we are replacing one element with another.

          So we can precompute the entire Fenwick tree for the compressed values. The values range from 0 to 10^9, but we compress to at most 500000 + 1 (for zero) + distinct update values? Actually, Q updates, so at most Q distinct values? But we have initial zeros, so distinct values: initial zero and then the update values. So at most Q+1 distinct values? Actually, we have the old values too? But the old values are either zero or a value from a previous update. So the distinct values are at most Q+1? Actually, the array starts with zeros and then each update sets one element to a new value. The distinct values that ever appear: the initial zeros and then every update value. But the update values are arbitrary. So we need to compress all values that appear in the updates and zero.

          Steps for compression:
            Collect: 
              all Y_i (for i in range(Q)) and 0.

          Then the distinct values are at most Q+1, which is 500001, so we can compress.

        We maintain two Fenwick trees (or segment trees) for the frequencies and for the sums? Actually, we can have one Fenwick tree for frequencies and one for the sums? 

        Specifically:
          Let F be a Fenwick tree for frequencies: F[i] = frequency of the compressed value at index i (in ascending order).
          Let S be a Fenwick tree for the values: but actually, we store the sum of the values? But note: each value has a compressed index, and we want the sum of the values? Actually, we can store the product: value * frequency? 

          Alternatively, we can have a Fenwick tree for the sum of the values: at index i, we store the value v[i] multiplied by the frequency? But then when we update, we update the frequency and the sum: 
            For a value 'val' (which compresses to index i), we add (or subtract) 1 to the frequency and add (or subtract) val to the sum.

          Actually, we can maintain two Fenwick trees:
            freq_tree: for frequencies
            sum_tree: for the total sum of the values (i.e., for a given index i, we store: v[i] * (frequency of v[i])? But no, Fenwick tree is built on the indices. We can store for each compressed index the value v[i] and then the Fenwick tree for frequencies and the Fenwick tree for the total value would be built as:

            For the frequency tree: we store the frequency at each index.
            For the sum tree: we store v[i] * (frequency at index i) at the index i? But then when updating, we need to update the frequency and then the sum tree would be updated as: 
                update_sum(i, delta * v[i])

          So we can do:

            For an update (change from old_val to new_val):
                Let old_compressed = comp[old_val]
                Let new_compressed = comp[new_val]

                Update frequency tree at old_compressed: -1
                Update frequency tree at new_compressed: +1

                Update sum tree at old_compressed: -old_val
                Update sum tree at new_compressed: +new_val

          Then, to compute the top K sum:

            We want to find the smallest value x (by compressed index, but we are going from large to small) such that the frequency of values >= x is at least K? Actually, we want the threshold value that is the kth largest.

            Alternatively, we can do a binary search over the compressed indices from the high end to the low end to find the threshold.

            How?
              We traverse the compressed array from the largest value down to the smallest. But we can use the Fenwick tree to get the cumulative frequency and cumulative sum from an index to the end? 

            Actually, we can precompute the entire array of compressed values in descending order? But then our Fenwick tree is built for ascending order. We can compute:

              Let the compressed array in ascending order: v0, v1, ..., v_{m-1} (v0 < v1 < ... < v_{m-1})

            Then the cumulative frequency from a given index i to the end (v_i to v_{m-1}) is:
                freq_ge(i) = total_frequency - freq_tree.prefix(i-1)   [if we use 1-indexed and our Fenwick tree for prefix sums from 1 to i]

            Similarly, the sum_ge(i) = total_sum - sum_tree.prefix(i-1)

            But note: we don't have the entire array stored as a Fenwick tree for the entire range? Actually, we have built the Fenwick trees for the entire compressed array.

            However, we want to find the smallest value (in the original value, but we are going from large to small) such that the cumulative frequency from that value to the top is at least K.

            Actually, we are going to find the threshold value x (which is one of the compressed values) such that:
                Let i = index of x in the compressed array (ascending order). Then the cumulative frequency from i to m-1 (which is the frequency of values >= x) is >= K.

            And we want the smallest x (i.e., the one with the largest index? because we want the top to be included) that satisfies this? Actually, we want the smallest x such that the cumulative frequency from x to max is >= K? Then that x will be the kth largest? 

            But note: the kth largest element is the smallest value that is at least the kth largest. 

            Actually, we want the threshold value for which the top K elements are: all values greater than the threshold and then enough of the threshold to make K.

            How to find the threshold?

              We can do a binary search on the compressed indices from 0 to m-1 (ascending) but we are interested in the cumulative from the top. Alternatively, we can traverse the compressed array from the largest to the smallest? 

            Since the compressed array is sorted in ascending order, the cumulative frequency from a given index i to m-1 is non-increasing as i decreases? Actually, as i increases (because we are going to higher values) the cumulative frequency decreases? 

            Actually, the cumulative frequency from index i to the end (i.e., values >= v[i]) is decreasing as i increases? 

            But we want the smallest x (i.e., the smallest value) for which the cumulative frequency >= K? Actually, no: we want the threshold that is the kth largest. 

            Let me reframe: 
              We want to find the kth largest element. 

            How about we find the largest value x such that the cumulative frequency of values >= x is at least K? Then that x is the kth largest? 

            Why? 
              Example: 
                Array: [5, 3, 3, 1], K=3.
                We want the 3rd largest: which is 3.

                Let x = 5: cumulative freq: [5,3,3,1] -> values>=5: 1 -> <3 -> not enough.
                x=3: cumulative freq: [5,3,3,1] -> values>=3: 3 (the two 3's and the 5) -> >=3 -> so x=3 is the threshold.

            So we want the largest value x such that freq_ge(x) >= K.

            Then the top K elements are:
              all elements > x (which are > x) and then (K - count_ge(x+1)) elements from the x's.

            Then the sum = (sum of all elements > x) + (K - count_ge(x+1)) * x.

            How to compute?
              count_ge(x) = freq_ge(x)   (we have that from the Fenwick tree: cumulative from x to max_value)
              count_ge(x+1) = cumulative from the next value? 

            But note: in our compressed array, the next value after x might not be x+1. So we need to find the next value in the compressed array that is strictly greater than x. 

            Actually, we can define:
              Let i = index of x in the compressed array (ascending). Then:
                count_ge(x) = cumulative_frequency from index i to m-1.
                count_ge(x+1) = cumulative_frequency from the next index j (where j is the smallest index such that v[j] > x) to m-1.

            But we can precompute the next index? Alternatively, we can store the compressed array and then for a given x, we know that the next value is the next index in the compressed array. 

            However, note: we are doing binary search for x. So when we find x, we can get the index i for x, then the next value is at index i+1? But only if i+1 < m? 

            So:
              count_ge(x+1) = cumulative_frequency from index i+1 to m-1.

            Then the number of x we need = K - count_ge(x+1)

            And the sum of elements > x = cumulative_sum from index i+1 to m-1.

            Then total = (cumulative_sum from i+1 to m-1) + (K - count_ge(x+1)) * x.

            But note: the cumulative_sum from i+1 to m-1 is the sum of all values at indices from i+1 to m-1? 

            We can get that from the sum_tree: 
                total_sum_ge_i+1 = total_sum - prefix_sum(i)   [if our Fenwick tree for sum_tree is built for prefix sums?]

            Actually, our Fenwick trees are built for prefix sums (from index 0 to index i). Then:
                cumulative_frequency from i to j = prefix(j) - prefix(i-1)

            But we want from i to m-1? 
                cumulative_frequency_ge_i = prefix(m-1) - prefix(i-1)

            Similarly, cumulative_frequency_ge_{i+1} = prefix(m-1) - prefix(i)

            And cumulative_sum_ge_{i+1} = total_sum_tree - prefix_sum_tree(i)

            However, we have two Fenwick trees: one for frequency and one for the sum of values? Actually, we have:
                total_sum = sum_tree.prefix(m)   (if we have m elements in the compressed array, then we index from 0 to m-1, and Fenwick tree typically 1-indexed to m)

            Steps for one update:

              Update the Fenwick trees for the old value and the new value.

              Then, we want to find the largest value x (in the compressed array) such that cumulative_frequency_ge(x) >= K.

              How to find x? 
                We can do a binary search over the compressed indices? But note: the cumulative_frequency_ge(x) is non-decreasing as x decreases? Actually, as we move to a lower value, cumulative_frequency_ge(x) increases. 

              Actually, we want the largest x (meaning the highest value) such that cumulative_frequency_ge(x) >= K. Since if we take a higher value, the cumulative frequency is smaller.

              So we can do a binary search over the indices from 0 to m-1? But the condition is: 
                We are going to consider the values in descending order? 

              Alternatively, we can iterate the compressed array from the highest value downward until we get cumulative_frequency_ge(x) >= K? But that would be O(m) per query, which is O(Q * m) = 500000 * 500000 -> 250e9, too slow.

            We can do a binary search over the compressed indices? But the cumulative_frequency_ge(x) is a function of x that is non-increasing as x increases? 

            Actually, if we consider the compressed array in ascending order (v0, v1, ... v_{m-1}), then the cumulative_frequency_ge(v_i) is non-increasing as i increases (because v_i increases). 

            So we can do a binary search on i from 0 to m-1 to find the smallest index i (meaning the smallest value? but we want the largest value x) such that cumulative_frequency_ge(v_i) >= K? 

            Actually, we want the largest value x that satisfies the condition. The largest value x that has cumulative_frequency_ge(x)>=K is the same as the largest v_i for which cumulative_frequency_ge(v_i)>=K? 

            How about we do:

              We want the largest v_i such that cumulative_frequency_ge(v_i) >= K.

            We can do a binary search over the indices i from 0 to m-1? But the condition is: 
                F(i) = cumulative_frequency_ge(v_i) = (total_frequency - prefix_freq(i-1)) 
                But note: total_frequency is N (always) and prefix_freq(i-1) is the frequency of values < v_i.

            Actually, we can do:

              low = 0, high = m-1
              candidate = 0   # the value 0 is always present (since we have zeros initially) so we can set candidate to the lowest value?

            But we want the largest x such that cumulative_frequency_ge(x)>=K.

            Since the array is sorted in ascending order, the cumulative_frequency_ge(v_i) is decreasing as i increases (because v_i increases and we are including fewer and fewer values). 

            So we can do:

              low = 0, high = m-1
              ans_index = -1
              while low <= high:
                  mid = (low+high)//2
                  if cumulative_frequency_ge(v_mid) >= K:
                      ans_index = mid   # and then we try to go higher? 
                      low = mid+1   # because we want a larger value? 
                  else:
                      high = mid-1

            Then the threshold value x = v[ans_index]? 

            But note: when we set low = mid+1, we are moving to a higher index (which corresponds to a higher value) but if cumulative_frequency_ge(v_mid)>=K, then we want to see if a larger value (with a higher index) also satisfies? 

            Actually, we are searching for the largest value that satisfies the condition. The condition is: cumulative_frequency_ge(v_i) >= K.

            Since the cumulative_frequency_ge(v_i) is decreasing with increasing i, then:

              If for mid we have condition true, then we can try a higher index (larger value) because we want the largest value that satisfies. So we set low = mid+1.

            Then when we break, ans_index is the last index that satisfied? Actually, we break when low>high, and then the candidate is at high? 

            Standard binary search for the last true:

              We can do:

                low = 0, high = m-1
                ans_index = 0   # but if none? but K>=1 and we have at least one element? 
                while low <= high:
                    mid = (low+high)//2
                    if cumulative_frequency_ge(v_mid) >= K:
                        ans_index = mid
                        low = mid+1
                    else:
                        high = mid-1

            Then the threshold value is v[ans_index].

            Then:
              cumulative_frequency_ge(v_{ans_index}) = freq_ge = (N - prefix_freq(ans_index-1))

              cumulative_frequency_ge(v_{ans_index+1})? But note: we need the cumulative_frequency for values strictly greater than v[ans_index]. 

              How to get the index for the next value? 
                The next value is the next index in the compressed array: if ans_index < m-1, then next_index = ans_index+1, otherwise if ans_index==m-1, then there are no values greater.

              So:
                if ans_index < m-1:
                    count_above = cumulative_frequency_ge(v[ans_index+1])   # which is (N - prefix_freq(ans_index)) 
                    sum_above = cumulative_sum_ge(v[ans_index+1])   # which is (total_sum - prefix_sum(ans_index))
                else:
                    count_above = 0
                    sum_above = 0

            Then the number of v[ans_index] we need = K - count_above

            Then total_sum = sum_above + (K - count_above) * v[ans_index]

          However, note: the cumulative_sum_ge(v[ans_index]) = total_sum - prefix_sum(ans_index-1)   [if our prefix_sum(i) is the sum of values at indices <= i]

          But we don't need that? We need the sum of values strictly above v[ans_index]? which is the cumulative_sum_ge(v[ans_index+1])? 

          So we have:

            count_above = (if ans_index < m-1) ? (N - prefix_freq(ans_index)) : 0
            sum_above = (if ans_index < m-1) ? (total_sum - prefix_sum(ans_index)) : 0

          But note: our Fenwick trees for prefix_freq and prefix_sum are defined for the entire array? And we update them with the frequencies and the actual values? 

          Actually, we have two Fenwick trees:
            freq_tree: which stores the frequency at each compressed index. Then prefix_freq(i) = sum_{j=0}^{i} freq_tree[j]
            sum_tree: which stores the total value at each compressed index (i.e., value * frequency) at that index? Then prefix_sum(i) = sum_{j=0}^{i} (v[j] * freq_tree[j])

          Then:
            cumulative_frequency_ge(v[i]) = N - prefix_freq(i-1)   [because prefix_freq(i-1) is the frequency of values < v[i]]
            cumulative_frequency_ge(v[i+1]) = N - prefix_freq(i)

          Similarly:
            cumulative_sum_ge(v[i+1]) = total_sum - prefix_sum(i)

          But note: total_sum = prefix_sum(m-1)

          So we can compute:

            total_sum = prefix_sum(m-1)   [which we can get from the Fenwick tree]

          However, we don't need to compute the entire Fenwick tree for the prefix at every step? We can get prefix_freq(i) and prefix_sum(i) in O(log m) per query.

        Steps for one update:

          Step 1: Update the array A at index X (0-indexed) from old_val to new_val.

          Step 2: Update the Fenwick trees for the old_val and new_val:

            For old_val:
                comp_old = comp[old_val]   -> index in the compressed array
                update_freq(comp_old, -1)
                update_sum(comp_old, -old_val)

            For new_val:
                comp_new = comp[new_val]
                update_freq(comp_new, +1)
                update_sum(comp_new, +new_val)

          Step 3: Update total_sum? Actually, we can compute total_sum = total_sum - old_val + new_val, but we also have it as prefix_sum(m-1). But we don't need to store separately.

          Step 4: Binary search for the threshold index i (from 0 to m-1) that is the largest index such that cumulative_frequency_ge(v_i) >= K.

            cumulative_frequency_ge(v_i) = N - prefix_freq(i-1)   [if we define prefix_freq(i-1) for indices < i]

          How to compute prefix_freq(i-1) in the Fenwick tree? We have a Fenwick tree that supports prefix sums for frequencies.

          Step 5: Let i0 = the found index (the threshold).

          Step 6: 
            count_above = 0
            sum_above = 0
            if i0 < m-1:
                count_above = N - prefix_freq(i0)   # because prefix_freq(i0) = sum of frequencies from 0 to i0 -> so values > v[i0] are from i0+1 to m-1 -> count_above = N - prefix_freq(i0)
                sum_above = total_sum - prefix_sum(i0)   # prefix_sum(i0) = sum of values for indices in [0, i0] -> then the rest is above.

          Step 7: 
            need = K - count_above   # the number of v[i0] we need to take
            result = sum_above + need * v[i0]

          Step 8: Output result.

        But note: what if K=0? Then we skip, but K>=1 per constraints.

        However, what if the entire array is zeros and K=1? 
          Then we have:
            cumulative_frequency_ge(0) = N >=1 -> true.
            Then we set i0 to the index of 0? 
            Then count_above = 0 (because there is no value above 0) -> so need = 1 - 0 = 1.
            result = 0 + 1 * 0 = 0 -> correct.

        Another test: 
          Example: [5, 1, 3, 2] and K=2 -> we want the top 2: 5 and 3 -> 8.

          Compressed values: [0,1,2,3,5] -> m=5, indices: 0:0, 1:1, 2:2, 3:3, 4:5.

          We want to find the largest value x such that cumulative_frequency_ge(x)>=2.

          Try x=5: cumulative_frequency_ge(5)=1 (only the 5) -> <2 -> false -> so skip.
          Then x=3: cumulative_frequency_ge(3)=? 
            values>=3: 3 and 5 -> count=2 -> >=2 -> true -> so candidate=3 (index=3) and then we try a larger value? (x=5 failed) so 3 is the largest that satisfies.

          Then:
            count_above = cumulative_frequency_ge(4) -> but the next value after 3 is 5, which is index4 -> so we use i0=3, then next index=4.
            count_above = cumulative_frequency_ge(5) = 1   [because only 5 is >=5] -> but note: we defined count_above as the frequency of values strictly greater than 3? which is values>=5? Actually, strictly greater than 3? but in our compressed array, the next value is 5. So we take the cumulative_frequency_ge(5) = 1.

            sum_above = cumulative_sum_ge(5) = 5.

            need = 2 - 1 = 1.
            result = 5 + 1 * 3 = 8 -> correct.

        But note: the value 3 is present? We have one 3. And we need one 3. So that's correct.

        However, what if we had two 3's? 
          Example: [5,3,3,2] and K=2: 
            Then the top 2: 5 and 3 -> 8? But wait, we have two 3's, but we only need one 3? Actually, we take the two largest: 5 and 3 (the largest 3) -> then 5+3=8.

          Now, with our method:
            cumulative_frequency_ge(3) = 3 (because 5,3,3) -> >=2 -> candidate=3 (index of 3) is the largest satisfying.

            Then count_above = cumulative_frequency_ge(5)=1 -> so need = 2-1 = 1.
            result = 5 + 1*3 = 8 -> correct.

        But what if K=3? 
            We want 5+3+3=11.
            Then candidate: 
              x=5: cumulative_frequency_ge(5)=1 -> <3 -> false.
              x=3: cumulative_frequency_ge(3)=3 -> >=3 -> true -> candidate=3.
            Then count_above = cumulative_frequency_ge(5)=1 -> so need = 3-1=2.
            result = 5 + 2*3 = 5+6=11 -> correct.

        What if K=4? 
            candidate: 
              x=2: cumulative_frequency_ge(2)=4 (all values) -> true -> but we want the largest x that satisfies? 
              Then we check x=3: 3>=3 -> true -> candidate=3? 
              Then x=5: false -> so candidate=3? 
            Then count_above = cumulative_frequency_ge(5)=1 -> need=4-1=3 -> result=5+3*3=5+9=14? 
            But the actual sum is 5+3+3+2=13.

            Why? 
              We are taking: 
                values strictly above 3: only 5 -> count_above=1 -> then we take 3 of the 3's? but there are only two 3's? 

            How do we avoid overcounting?

            The problem: the threshold value is 3, but the frequency of 3 is: 
                freq_ge(3) = 3 (5,3,3) -> but we break it into:
                  count_above = 1 (the 5) and then we take 3 (the need) from the 3's? but there are only two 3's.

            Actually, the cumulative_frequency_ge(3) = 3 (the two 3's and the 5). So we have two 3's. We cannot take three 3's.

            How did we compute the frequency of 3? 
                It is: cumulative_frequency_ge(3) - cumulative_frequency_ge(5) = 3 - 1 = 2.

            So we should take min(need, frequency_of_3) * 3? but wait, we computed need = K - count_above = 4-1=3, but we only have 2 available.

            What went wrong? 

            The condition: 
              We want the threshold value to be the kth largest. But when we have K=4, the kth largest is the 4th largest, which is 2. 

            How do we get 2 as the threshold? 

            We are looking for the largest x such that cumulative_frequency_ge(x)>=4? 
              x=2: cumulative_frequency_ge(2)=4 -> true -> candidate=2? 
            Then we do:
                count_above = cumulative_frequency_ge(3) = 3   [because next value after 2 is 3] -> but wait, we defined count_above as the frequency of values strictly greater than 2? which is values>=3 -> 3.
                need = 4-3 = 1 -> then we take 1 * 2 = 2.
                Then total = (sum_above: cumulative_sum_ge(3)) + 2 
                cumulative_sum_ge(3) = 5+3+3 = 11 -> total = 11+2=13 -> correct.

            So the issue: we must set the threshold to the largest x such that cumulative_frequency_ge(x)>=K. In the case of K=4, the candidate is 2, not 3.

            Why? Because the condition for 3: cumulative_frequency_ge(3)=3 <4 -> fails. So we go to lower values: then 2: cumulative_frequency_ge(2)=4>=4 -> true -> candidate=2.

            Therefore, the algorithm for binary search:

              We are searching for the largest index i (corresponding to the largest value) such that cumulative_frequency_ge(v[i])>=K.

            How to compute cumulative_frequency_ge(v[i])? 
                = N - prefix_freq(i-1)

            We do:

              low = 0, high = m-1
              candidate_index = -1
              while low <= high:
                  mid = (low+high)//2
                  cnt = N - (prefix_freq(mid-1))   # prefix_freq(mid-1) = sum_{j=0}^{mid-1} freq_tree[j] -> which is the frequency of values < v[mid] 
                  if cnt >= K:
                      candidate_index = mid   # and we try a larger value? meaning higher index?
                      low = mid+1
                  else:
                      high = mid-1

            Then after the loop, candidate_index might be set? Actually, if we break and high is the last that we know? 

            Actually, the above binary search will set candidate_index to the last mid that satisfied the condition? But note: we are moving low=mid+1 when we have a candidate, so at the end, candidate_index might not be the last candidate? 

            Let me simulate:

              Array: [0,1,2,3,5] for the compressed values.
              For the array [5,3,3,2] and K=4:

                We check mid = (0+4)//2 = 2: v[2]=2 -> cumulative_frequency_ge(2)=4>=4 -> candidate_index=2, then low=3.
                Then mid = (3+4)//2=3: v[3]=3 -> cumulative_frequency_ge(3)=3 <4 -> so set high=2.
                Then low=3, high=2 -> break. candidate_index=2 -> correct.

            Then we compute:
                i0 = 2 -> value=2.
                next index = 3 (if exists) -> so count_above = cumulative_frequency_ge(v[3]) = cumulative_frequency_ge(3)=3? 
                But wait: we defined count_above as the frequency of values strictly greater than 2? which is values>=3 -> 3.

                Then need = 4 - 3 = 1.
                sum_above = cumulative_sum_ge(3) = 5+3+3 = 11.
                total = 11 + 1*2 = 13 -> correct.

            But note: the frequency of the threshold value (2) is: 
                cumulative_frequency_ge(2) - cumulative_frequency_ge(3) = 4-3=1 -> so we have one 2.

            So we take one 2 -> correct.

        However, note: we do not explicitly use the frequency of the threshold value? We use the count_above and then need = K - count_above.

        Why is that the same as the frequency of the threshold value? 
          cumulative_frequency_ge(threshold) = count_above + (frequency of threshold)
          => frequency of threshold = cumulative_frequency_ge(threshold) - count_above.

          But we don't need the frequency of the threshold? We are going to take min(need, frequency of threshold)? 

          Actually, we have:
            need = K - count_above

          And since cumulative_frequency_ge(threshold) = count_above + (frequency of threshold) >= K   (by our condition) 
          => frequency of threshold >= K - count_above = need.

          So we can safely take need copies of the threshold? 

          But wait: what if cumulative_frequency_ge(threshold) is exactly K? then frequency of threshold = K - count_above = need -> so we take all the threshold values? 

          What if cumulative_frequency_ge(threshold) > K? 
            Then frequency of threshold = cumulative_frequency_ge(threshold) - count_above > need -> we only need 'need' copies.

          So the formula holds.

        Therefore, we can compute:

          candidate_index = the index we found by binary search (the largest index i such that cumulative_frequency_ge(v_i)>=K)

          Then:
            count_above = cumulative_frequency_ge(v_{candidate_index+1})   [if candidate_index < m-1, else 0]
            But how to compute cumulative_frequency_ge(v_{candidate_index+1})? 
                = N - prefix_freq(candidate_index)   [because prefix_freq(candidate_index) = frequency of values <= v_{candidate_index}? Actually, no: prefix_freq(candidate_index) = frequency of values in [0, candidate_index] (which are <= v_{candidate_index}? because the compressed array is sorted). 

            Actually, note: our Fenwick tree for frequencies: 
                prefix_freq(i) = frequency of values with compressed index <= i -> which are values <= v_i? because v_i is increasing.

            Therefore, cumulative_frequency_ge(v_{candidate_index+1}) = frequency of values >= v_{candidate_index+1] = N - prefix_freq(candidate_index)   [because prefix_freq(candidate_index) = frequency of values <= v_{candidate_index}]

          Similarly:
            cumulative_sum_ge(v_{candidate_index+1}) = total_sum - prefix_sum(candidate_index)

          Then need = K - count_above

          Then result = cumulative_sum_ge(v_{candidate_index+1}) + need * v[candidate_index]

        But note: if candidate_index is the last index (m-1), then there is no next value, so:
            count_above = 0
            sum_above = 0
            need = K
            result = 0 + K * v[candidate_index]

          Example: [1,1,1] and K=2: 
            Compressed: [1] -> m=1, candidate_index=0.
            Then count_above=0, need=2, result=2*v[0]=2 -> correct.

        However, what if we have two distinct values and the threshold is the largest? 
          Example: [3,3,2] and K=2: 
            Compressed: [2,3] -> m=2.
            We search: 
              i=0: v=2 -> cumulative_frequency_ge(2)=3>=2 -> candidate_index=0 -> then we set low=1 -> then i=1: v=3 -> cumulative_frequency_ge(3)=2>=2 -> candidate_index=1 -> then low=2, high=1 -> break -> candidate_index=1.
            Then:
                count_above = cumulative_frequency_ge(v[2]) -> but candidate_index=1 and m=2, so candidate_index+1=2 -> which is beyond? so we set count_above=0? 
                Then need=2, result=0+2*3=6 -> correct (because top2: 3+3=6).

        But wait: the array [3,3,2] -> the top2: two 3's -> 6.

        However, if we have [3,3,2] and K=3: 
            We want the top3: 3+3+2=8.

            candidate: 
                i=0: v=2 -> cumulative_frequency_ge(2)=3>=3 -> candidate_index=0 -> then we try i=1: v=3 -> cumulative_frequency_ge(3)=2<3 -> so candidate_index=0? 
            Then:
                count_above = cumulative_frequency_ge(v[1]) = cumulative_frequency_ge(3)=2 -> because we use next index=1? 
                need = 3-2 = 1 -> result = cumulative_sum_ge(3) + 1*2 = (3+3) + 2 = 8? -> but cumulative_sum_ge(3)=3+3=6? 
                Then 6+2=8 -> correct.

        Therefore, the algorithm:

          Precomputation:
            Collect all values: initial zeros and all update Y_i.
            Compress the distinct values to an array `vals` and create a mapping `comp` from value to index (0-indexed).

            Initialize:
              FenwFreq = FenwickTree(m)   # for frequencies
              FenwSum = FenwickTree(m)     # for the total value (value * frequency) at that index

            Also, we have an array A of length N, initially zeros.

            Then, we initialize the Fenwick trees for the initial state: 
              All zeros: so we have N zeros.
              index0 = comp[0]
              FenwFreq.update(index0, N)   # but wait, we can only update one index at a time? Actually, we do:
                FenwFreq.update(index0, N)   -> but then when we update, we update the difference. However, we start with zeros.

          Then for each update (index X, new value Y):
            old_val = A[X]
            new_val = Y
            A[X] = Y

            If old_val == new_val, then skip? But we update the same? Actually, we still need to update? But if same value, then no change? 

            Steps for update:
              idx_old = comp[old_val]
              idx_new = comp[new_val]

              FenwFreq.update(idx_old, -1)
              FenwFreq.update(idx_new, +1)

              FenwSum.update(idx_old, -old_val)
              FenwSum.update(idx_new, +new_val)

            Then do the binary search for candidate_index:

              low = 0
              high = m-1
              candidate_index = 0   # if none found? but there should be at least one? 
              while low <= high:
                  mid = (low+high)//2
                  # Compute cumulative_frequency_ge(vals[mid]): 
                  #   = N - (FenwFreq.prefix(mid-1)) 
                  # But note: prefix(mid-1) = sum of frequencies from index0 to index(mid-1) -> which is the frequency of values < vals[mid]? 
                  #   Actually, because the compressed array is sorted, the values at indices [0, mid-1] are < vals[mid]? 
                  #   But what if there are duplicates? The compressed array has distinct values. So yes, the values at indices < mid are strictly less than vals[mid]? 

                  # How to get prefix(mid-1): 
                  if mid-1 < 0:
                      prefix_freq = 0
                  else:
                      prefix_freq = FenwFreq.prefix(mid-1)   # prefix from 0 to mid-1

                  cnt_ge = N - prefix_freq

                  if cnt_ge >= K:
                      candidate_index = mid
                      low = mid+1
                  else:
                      high = mid-1

            Then:
              # Now candidate_index is the threshold index.

              if candidate_index < m-1:
                  # next index: candidate_index+1
                  # count_above = cumulative_frequency_ge(vals[candidate_index+1]) = N - FenwFreq.prefix(candidate_index)   # because prefix(candidate_index) = frequency of values <= vals[candidate_index]? 
                  # But note: our FenwFreq.prefix(i) returns the sum of frequencies from index0 to index i (inclusive).
                  # So prefix(candidate_index) = frequency of values <= vals[candidate_index] -> then 
                  count_above = N - FenwFreq.prefix(candidate_index)   # because values >= vals[candidate_index+1] are from candidate_index+1 to m-1 -> and FenwFreq.prefix(candidate_index) is the frequency of values <= vals[candidate_index] -> then the rest are > vals[candidate_index] -> which is count_above.

                  sum_above = FenwSum.range_query(candidate_index+1, m-1)   # we can do: total_sum - FenwSum.prefix(candidate_index)
                  total_sum = FenwSum.prefix(m-1)   # or we can maintain a variable? but we update the FenwSum so we can get prefix(m-1) quickly? Actually, we don't have to store total_sum separately? We can compute it as FenwSum.prefix(m-1)

                  # But we can also compute: 
                  #   sum_above = total_sum - FenwSum.prefix(candidate_index)
                  # However, note: FenwSum.prefix(candidate_index) = sum of values for indices [0, candidate_index] -> which are the values <= vals[candidate_index]? 

              else: # candidate_index == m-1
                  count_above = 0
                  sum_above = 0

              need = K - count_above
              result = sum_above + need * vals[candidate_index]

            Then output result.

        However, note: we need to compute total_sum? We can maintain it separately: 
          total_sum = total_sum - old_val + new_val

          Then we don't need to do FenwSum.prefix(m-1) every time? 

        But we can also use FenwSum.prefix(m-1) to get total_sum. Since we update the FenwSum, it will be current.

        Alternatively, we can maintain a global variable for total_sum and update it: 
          total_sum = total_sum - old_val + new_val

        Then we can use that.

        But then for sum_above: 
          if candidate_index < m-1:
              # We want the sum of values strictly greater than vals[candidate_index]? which is the values at indices from candidate_index+1 to m-1.
              # How to get that from FenwSum? 
              #   We can do: 
              #       sum_above = total_sum - FenwSum.prefix(candidate_index)
              #   But note: FenwSum.prefix(candidate_index) = sum of values at indices [0, candidate_index] -> which are the values <= vals[candidate_index]? 
              #   Then total_sum - FenwSum.prefix(candidate_index) = sum of values at indices [candidate_index+1, m-1] -> which are the values strictly greater than vals[candidate_index]? 
              #   But what if there are values equal to vals[candidate_index] at indices <= candidate_index? 
              #   Actually, the compressed array: each distinct value has one index. So all the values equal to vals[candidate_index] are stored at index candidate_index? 
              #   Therefore, the values at indices [0, candidate_index] are <= vals[candidate_index]? and the values at indices [candidate_index+1, m-1] are > vals[candidate_index]? -> yes.

          So we can do: 
              sum_above = total_sum - FenwSum.prefix(candidate_index)

        However, we must be cautious: FenwSum.prefix(candidate_index) includes the value at candidate_index? and we want to exclude the value at candidate_index? 
          Actually, we want the values strictly greater than candidate_index? which is the values at indices from candidate_index+1 to m-1. 

          And total_sum - FenwSum.prefix(candidate_index) is the sum of values at indices from candidate_index+1 to m-1. -> that's correct.

        Therefore, we can avoid the FenwSum.range_query and use the prefix.

        Steps for one update:

          total_sum = total_sum - old_val + new_val   # we maintain this

          Update FenwFreq: 
            FenwFreq.update(idx_old, -1)
            FenwFreq.update(idx_new, +1)

          Update FenwSum:
            FenwSum.update(idx_old, -old_val)
            FenwSum.update(idx_new, +new_val)

          Then binary search for candidate_index (as above).

          Then:
            if candidate_index < m-1:
                count_above = N - FenwFreq.prefix(candidate_index)   # because FenwFreq.prefix(candidate_index) = frequency of values <= vals[candidate_index] -> then values > vals[candidate_index] = N - that = count_above.
                sum_above = total_sum - FenwSum.prefix(candidate_index)   # because FenwSum.prefix(candidate_index) = sum of values <= vals[candidate_index] -> then values > vals[candidate_index] = total_sum - that.
            else:
                count_above = 0
                sum_above = 0

          Then need = K - count_above
          result = sum_above + need * vals[candidate_index]

        However, note: we can compute FenwFreq.prefix(candidate_index) in O(log m) and FenwSum.prefix(candidate_index) in O(log m). And the binary search is O(log m) * O(log m) because each step we do a FenwFreq.prefix? 

        Actually, the binary search does O(log m) iterations, and each iteration does one FenwFreq.prefix (which is O(log m)). So total per query: O((log m)^2) which is about (log(500000))^2 ~ (19)^2 = 361, and 500000 queries -> 500000 * 361 = 180.5e6, which might be borderline in Pyton? But note: constraints say 500000, and we are in Pyton, but we can try to optimize.

        Alternatively, we can store the entire FenwFreq tree? and then update and then the binary search can be done with a Fenwick tree that supports getting cumulative frequency for a given index? 

        But we are already using FenwFreq for prefix queries.

        We can also avoid the binary search by maintaining a Fenwick tree that supports getting the kth largest? 

        However, the above is straightforward.

        Let me code the Fenwick tree for frequencies and for sums.

        Fenwick tree implementation (1-indexed) for two trees.

        Steps:

          We'll create a FenwTree class that supports:
            __init__(n)
            update(i, delta)   # update the element at index i (0-indexed) by delta? or 1-indexed? 

          But note: our compressed indices are 0-indexed. We can use 1-indexed internally.

          For FenwFreq and FenwSum, we do:

            FenwFreq = FenwTree(m)
            FenwSum = FenwTree(m)

          Then:
            prefix(i) for FenwFreq: the sum from index0 to index i (0-indexed) -> we can map: 
              internally, we use index i+1 for the 0-indexed index i.

          Alternatively, we can make the Fenwick tree 0-indexed? But typically Fenwick trees are 1-indexed.

          Let's define:

            class FenwTree:
                def __init__(self, size):
                    self.n = size
                    self.tree = [0]*(self.n+1)

                def update(self, index, delta):
                    # index is 0-indexed. We convert to 1-indexed by adding 1 to index?
                    i = index + 1
                    while i <= self.n:
                        self.tree[i] += delta
                        i += i & -i

                def prefix(self, index):
                    # returns the prefix sum [0, index] (0-indexed, inclusive)
                    if index < 0:
                        return 0
                    i = index+1
                    s = 0
                    while i:
                        s += self.tree[i]
                        i -= i & -i
                    return s

          Then for the binary search, we need to do:

            low = 0, high = m-1
            candidate_index = 0
            while low <= high:
                mid = (low+high)//2
                # get prefix_freq for mid-1: 
                pre = (FenwFreq.prefix(mid-1) if mid-1>=0 else 0)
                cnt_ge = N - pre   # frequency of values >= vals[mid]
                if cnt_ge >= K:
                    candidate_index = mid
                    low = mid+1
                else:
                    high = mid-1

          Then we get:

            if candidate_index < m-1:
                # prefix_freq for candidate_index: 
                pre_freq = FenwFreq.prefix(candidate_index)
                count_above = N - pre_freq   # because values > vals[candidate_index] are at indices [candidate_index+1, m-1] -> frequency = N - (frequency of values <= candidate_index) = N - pre_freq

                pre_sum = FenwSum.prefix(candidate_index)
                sum_above = total_sum - pre_sum

            else:
                count_above = 0
                sum_above = 0

          Then need = K - count_above
          result = sum_above + need * vals[candidate_index]

        But note: total_sum is maintained separately: 
            total_sum = total_sum - old_val + new_val

        However, we can also compute total_sum = FenwSum.prefix(m-1)   # which is the entire array? 

        But we are updating FenwSum? So we can do:

            total_sum = FenwSum.prefix(m-1)

        Then we don't need to maintain a separate variable.

        But then we do an extra O(log m) per query? which is acceptable.

        Alternatively, we can maintain total_sum as a global and update it as:
            total_sum = total_sum - old_val + new_val

        This is O(1).

        I think we should maintain total_sum separately.

        Steps for the entire solution:

          Step 0: Precomputation
            Read N, K, Q
            updates = list of Q updates.

            Collect all distinct values: 
                values_set = set()
                values_set.add(0)   # initial value
                for (x, y) in updates:
                    values_set.add(y)

            distinct_vals = sorted(values_set)   # sorted in ascending order
            m = len(distinct_vals)
            comp = {}
            for idx, val in enumerate(distinct_vals):
                comp[val] = idx

            Initialize:
                A = [0] * N
                total_sum = 0

                FenwFreq = FenwTree(m)
                FenwSum = FenwTree(m)

                # Initially, all zeros: 
                idx0 = comp[0]
                FenwFreq.update(idx0, N)
                FenwSum.update(idx0, 0)   # but we add 0*N = 0? 
                # Actually, we don't need to update the FenwSum with 0 for N times? We can update once with 0*N? 
                # But we are going to update one element at a time? We start with total_sum=0, and we have N zeros.

          Step 1: Process each update

            results = []
            for each update (x, y) in updates:
                index_in_A = x-1   # 0-indexed
                old_val = A[index_in_A]
                new_val = y

                # Update A and total_sum
                A[index_in_A] = new_val
                total_sum = total_sum - old_val + new_val

                idx_old = comp[old_val]
                idx_new = comp[new_val]

                FenwFreq.update(idx_old, -1)
                FenwFreq.update(idx_new, 1)

                FenwSum.update(idx_old, -old_val)
                FenwSum.update(idx_new, new_val)

                # Now binary search for candidate_index in the compressed array [0, m-1] (0-indexed indices)
                low = 0
                high = m-1
                candidate_index = 0   # if we don't find any, then we use the smallest value? but we should always have at least the zeros? 
                # But note: if K=0? but K>=1 per constraints.
                while low <= high:
                    mid = (low+high)//2
                    # For value distinct_vals[mid], we need cumulative_frequency_ge = N - (prefix_freq(mid-1))
                    if mid-1 >= 0:
                        pre = FenwFreq.prefix(mid-1)
                    else:
                        pre = 0
                    cnt_ge = N - pre
                    if cnt_ge >= K:
                        candidate_index = mid
                        low = mid+1
                    else:
                        high = mid-1

                # Now candidate_index is set
                if candidate_index < m-1:
                    pre_freq = FenwFreq.prefix(candidate_index)   # frequency of values <= distinct_vals[candidate_index]
                    count_above = N - pre_freq   # frequency of values > distinct_vals[candidate_index]

                    pre_sum = FenwSum.prefix(candidate_index)   # sum of values <= distinct_vals[candidate_index]
                    sum_above = total_sum - pre_sum
                else:
                    count_above = 0
                    sum_above = 0

                need = K - count_above
                res = sum_above + need * distinct_vals[candidate_index]
                results.append(res)

            return results

        Let me test with the example: 
          Example: N=4, K=2, updates: 
            (1,5): 
                old_val=0, new_val=5
                total_sum = 0 -0 +5 = 5
                Update FenwFreq: 
                  idx_old = comp[0] -> 0? (if distinct_vals=[0,5] then m=2, comp[0]=0, comp[5]=1)
                  FenwFreq: update(0, -1) -> tree: [1-> becomes 3? because initially we had 4 at 0? Actually, initially we set FenwFreq.update(0, N) -> which is 4? Then we update: 4-1=3? Then update(1,1) -> so now frequencies: 0:3, 1:1.

                FenwSum: 
                  Initially: FenwSum at 0: 0 (from the initial update: we did FenwSum.update(0, 0) -> but wait, we did:
                    FenwSum.update(idx0, 0)   # but we did not add the initial zeros? We only did one update of 0? 

                Correction: 
                  Initially, we have N zeros. So we should update FenwSum at idx0 with 0*N = 0? 
                  But then when we update an element from 0 to 5, we do:
                    FenwSum.update(0, -0) -> that does nothing? 
                    and then FenwSum.update(1, 5) -> so now total_sum=5.

                Then the binary search:
                  distinct_vals = [0,5] -> m=2
                  candidate_index: 
                    low=0, high=1
                    mid=0: 
                         cnt_ge = N - (prefix_freq(-1)) -> but we defined prefix_freq(-1)=0 -> cnt_ge=4>=2 -> candidate_index=0, then low=1.
                    mid=1: 
                         cnt_ge = N - prefix_freq(0) = 4 - FenwFreq.prefix(0) -> FenwFreq.prefix(0) = frequency of values <=0? 
                         Our FenwFreq: 
                            index0: 3 (because we updated: initially 4, then -1 -> 3; and then +1 at index1 -> so the FenwFreq.prefix(0)=3? 
                         cnt_ge = 4-3 = 1 <2 -> false -> so candidate_index=0.

                Then:
                  candidate_index=0 (value=0) and since 0<m-1 (which is 1) -> 
                    pre_freq = FenwFreq.prefix(0) = 3
                    count_above = 4-3 = 1
                    pre_sum = FenwSum.prefix(0) = 0? (because we did FenwSum.update(0,0) initially and then we did FenwSum.update(0, -0) -> which did nothing? and then we did FenwSum.update(1,5) -> so the prefix_sum at index0 is 0? 
                    sum_above = total_sum (5) - 0 = 5
                    need = 2 - 1 = 1
                    res = 5 + 1*0 = 5 -> correct.

            Then update (2,1): 
                old_val=0, new_val=1 -> but 1 is not in distinct_vals? 

          We did not compress 1? 

          Correction: we collected the distinct values: we added 0 and all update Y_i. So we must add 1.

          So distinct_vals = sorted([0,5,1, ...]) = [0,1,5] -> then m=3.

          Then for the second update: 
            old_val=0 -> idx_old=0
            new_val=1 -> idx_new= comp[1] = 1

            total_sum = 5 - 0 + 1 = 6

            FenwFreq: update(0, -1) -> now 3-1=2 at index0? and then update(1,1) -> index1 becomes 1 (was 0)

            FenwSum: update(0,0) -> no change? and update(1,1) -> now FenwSum: 
                index0: 0
                index1: 1
                index2: 5   (from the first update) -> but note: we have a third value? 

            Actually, the FenwSum stores the total value for each compressed index? So:
                index0: 0 * frequency0 = 0? but we don't store the product? We store the sum of the values? 
                We update: 
                  FenwSum.update(0, -0) -> no change.
                  FenwSum.update(1, 1) -> add 1 at index1.

                Then the FenwSum tree for prefix: 
                  prefix(0)=0
                  prefix(1)=0+1=1
                  prefix(2)=0+1+5=6

            Then binary search for candidate_index:
                distinct_vals = [0,1,5]
                low=0, high=2
                mid=1: 
                    cnt_ge = N - prefix_freq(0) = 4 - FenwFreq.prefix(0) = 4 - 2 (because FenwFreq: index0 has 2, so prefix(0)=2) -> 2>=2 -> true -> candidate_index=1, then low=2.
                mid=2: 
                    cnt_ge = N - prefix_freq(1) = 4 - FenwFreq.prefix(1) 
                         FenwFreq.prefix(1)= frequencies at index0 and index1 = 2+1=3 -> so cnt_ge=1<2 -> false -> so candidate_index=1.

                Then:
                  candidate_index=1 -> value=1
                  candidate_index < m-1 (which is 2) -> so:
                    pre_freq = FenwFreq.prefix(1) = 3
                    count_above = 4-3 = 1
                    pre_sum = FenwSum.prefix(1) = 1
                    sum_above = 6 - 1 = 5
                    need = 2-1=1
                    res = 5 + 1*1 = 6 -> correct.

        However, note: the top2 are 5 and 1 -> 5+1=6.

        Next update: (3,3) -> 
            old_val=0 -> idx_old=0
            new_val=3 -> but 3 is not in distinct_vals? 

        So we must collect all update values. 

        We did: 
            values_set = {0} and then for each update we add y.

        But in the example, the updates: 
            1:5, 2:1, 3:3, 4:2, 2:10, 1:0, 4:0, 3:1, 2:0, 3:0

        So we should have distinct_vals = {0,1,2,3,5,10} -> sorted: [0,1,2,3,5,10]

        So we need to collect all update values.

        Implementation:

          We'll do:

            s = set()
            s.add(0)
            for i in range(Q):
                x, y = updates[i]
                s.add(y)

            distinct_vals = sorted(s)

        Then comp = {val: idx for idx, val in enumerate(distinct_vals)}

        Now, the Fenw trees: we update with the actual value (for FenwSum) and the frequency.

        Time complexity: 
          Q up to 500000, m up to 500000? 
          Each update: 
            update FenwFreq: two updates -> O(log m) each -> 2*O(log m)
            update FenwSum: two updates -> 2*O(log m)
            binary search: O(log m) * O(log m) (because each step we do a FenwFreq.prefix which is O(log m)) -> O((log m)^2)

          Total per query: O((log m)^2) + O(log m) = O((log m)^2) ~  (log2(500000))^2 ~ (19)^2 = 361 per query, and 500000 queries -> 180.5e6 operations.

          In Python, this might be borderline? But we can try to optimize by using a Fenwick tree that supports O(1) for the binary search? Actually, there is a Fenwick tree that can do the kth element in O(log n). 

        Alternative: 
          We can use a Fenwick tree to find the kth largest element? 

          How: 
            We have a Fenw tree for frequencies over the compressed values (in descending order? or ascending?) 

          Actually, the kth largest element: 
            We want the smallest value x such that the cumulative frequency of values >= x is at least k? -> but that's what we did.

          There is a method to get the kth largest: 
            We can store the Fenw tree in descending order? 

          But the above method is standard.

        We can also use a segment tree to do the binary search in O(log m) without the inner O(log m) for the prefix? 

          We can build a segment tree that stores the frequency for each compressed value and then we can do a binary search from the root? 

          But we are not going to do that for now.

        We'll implement with Fenwick trees and hope that 180e6 operations in Pyton might pass in Pyton? But worst-case 500000*361 = 180.5e6, which is acceptable in Pyton in C++ but in Python? 

        We can try to optimize by using 1-based Fenwick trees and using local variables.

        Let me write the FenwTree class and then run the example.

        However, note: the problem constraints are high. We might need to optimize the binary search to use a Fenwick tree that supports the "find the kth largest" operation in O(log n). 

        How to get the kth largest in a Fenw tree? 
          We can store the Fenw tree for frequencies in ascending order? Then the kth largest is the (N - k + 1)th smallest? 
          But note: the kth largest: 
            total_freq = N
            kth largest = (N - k)th smallest? -> no, the 1st largest is the largest, which is the last element? 

          Actually, if we store the Fenw tree in ascending order, then the kth largest is the element at the position: 
            We want the smallest x such that the cumulative frequency from the largest down to x is at least k? 

          Alternatively, we can build the Fenw tree for the compressed array in descending order? 

          But we can also invert the indices: 
            Let the compressed array in ascending order: v0, v1, ..., v_{m-1}
            Then the kth largest is the element at index i such that:
                cumulative_frequency_ge(v_i) = cumulative_frequency from i to m-1 >= k

## Final Solution

