# Solution for abc311_d.md

Generated at: 2025-07-22 06:30:24
Model: deepseek-reasoner

## Reasoning Process

We are given grid dimensions N, M and a list of strings `grid` of length N, each string of length M.
The grid has rocks ('#') and ice ('.'). The outer periphery is rock, and the starting point is (2,2) (which is grid[1][1] in 0-indexing).

The player starts at (1,1) (0-indexed: row1, col1). In one move, the player chooses a direction and slides in that direction until hitting a rock. The player stops on the last ice square before the rock.

We need to count the number of ice squares that the player can touch (either by resting on or passing through) during any sequence of moves.

Important note: The player can make multiple moves. However, note that when sliding in a direction, the player passes through every ice square in that direction until stopped by a rock. But also note that the same ice square might be passed multiple times in different slides? However, we are only counting distinct squares that are touched at least once.

Challenges:
- The grid size is up to 200x200 (so at most 40,000 cells). 
- We have to consider that the player can change direction after each slide.

Observation:
The problem is about reachable ice squares via a sequence of slides. However, note that the player does not necessarily have to rest on every square they pass. For example, when sliding from (2,2) to the right, they pass through (2,3), (2,4), etc. until they hit a rock. So in that one move, they touch every ice square in that row from (2,2) to the stopping point.

But note: the problem does not require that the player stops on the square to count it. It counts if the player passes through or rests on.

However, the problem becomes: which ice squares are reachable (in the sense that they lie on a path that the player can take) by a sequence of moves? 

Important: The starting square (2,2) is ice and is the initial rest position.

We can think of the grid as a graph where:
- Nodes: ice squares? But note: the player can pass through squares without stopping. However, we are only counting distinct squares that are touched.

But note: when the player slides in one direction, they cover a contiguous segment of ice squares. Therefore, if the player can get to a particular ice square (either by stopping there or by passing through it in a slide) then we count it.

However, note that the player might pass a square in one slide and then later stop on it? But the problem asks for the set of ice squares that the player can touch at least once.

We can use BFS or DFS to traverse the states? But what is the state? The state is the current rest position (the square the player is currently standing on). But note: the problem does not require the player to stop on a square to count it. They might have passed through it in a previous slide.

So we have two sets:
1. The set of squares that the player has rested on.
2. The set of squares that the player has passed through (but not necessarily rested on).

But note: the problem does not distinguish: any touch (passing or resting) counts.

However, when the player slides from a rest position, they cover the entire segment from the rest position until the rock. That segment is then "touched".

Therefore, the problem reduces to: we need to find the union of all segments that the player can cover in any sequence of moves.

But note: the starting segment from (2,2) in each direction is covered initially? Actually, the player starts at (2,2). Then in the first move, they can choose a direction and then cover the entire segment in that direction until a rock.

But the problem allows multiple moves. So after the first move, the player is at a new rest position. Then from that rest position, they can slide in a new direction, covering a new segment.

But note: when sliding from a rest position, the entire segment from that rest position until the rock is covered. However, if the segment overlaps with a previously covered segment, we don't count duplicates.

So the goal: the union of all ice squares that are ever traversed in any slide during any sequence of moves.

How to compute this?

We can use BFS starting from (2,2) (0-indexed: (1,1)) and simulate the slides. However, we have to remember which squares we have already covered (the entire segment) and which rest positions we have visited.

But note: the same rest position might be reached by different paths, but we don't need to process it twice.

However, a key challenge: even if we have visited a rest position, we might have covered a segment in one direction from that rest position, but what about another direction? But note: from a rest position, we can slide in any direction, and we should do that only once per rest position per direction? 

But also: when we slide from a rest position in a direction, we cover a contiguous segment. Then we stop at the last ice square in that direction. That becomes a new rest position (if we haven't been there before) and we can then slide from that rest position.

However, the problem: we are not only interested in the rest positions, but the entire path of ice squares that are traversed.

We can do:

1. We maintain two sets (or arrays):
   - `visited`: the set of rest positions that we have already visited (so we don't process the same rest position multiple times).
   - `passed`: a 2D boolean grid that marks which ice squares have been touched (either by being passed through or rested on). We want to count the total distinct ice squares that are touched.

But note: when we slide from a rest position in a direction, we traverse a contiguous segment. We need to mark every ice square in that segment as touched. And then we get a new rest position at the end (if it's not a rock and we can stop there). However, the rest position at the end is the last ice square in that direction.

But note: the starting rest position is (1,1). We mark that as touched and as visited rest position.

Then we consider each direction from (1,1). For each direction, we traverse the entire segment until we hit a rock. We mark every ice square in that segment as touched. Then we get a new rest position at the last ice square in that segment (if that rest position has not been visited, we add it to the queue for BFS).

However, note: the same ice square might be traversed in two different slides? For example, we might traverse a row and then later a column that crosses that row. Then that square is touched twice, but we only count it once.

So the algorithm:

- Create a grid `touched` (size N x M) of booleans, initially all False.
- Create a grid `visited_rest` (size N x M) of booleans, initially all False.

- Start at (1,1): 
   - Mark (1,1) as touched and as visited_rest.
   - For each of the four directions, we simulate a slide from (1,1) in that direction:
        - We traverse in the direction until we hit a rock. For every ice square we traverse, mark it as touched.
        - The last ice square we land on (if any) is the rest position at the end. But note: the starting rest position is the first square, and we slide until we hit a rock. The rest position we land on is the last ice square before the rock. However, note: we might start at (1,1) and then slide right: we go from (1,1) to (1,2), (1,3), ... until we hit a rock at (1,k). Then we stop at (1,k-1). So the rest position is (1,k-1). If we haven't visited that rest position, we add it to the queue.

- Then we process each rest position in the queue similarly.

But note: when we slide from a rest position, we might cover a segment that we have already covered partially? But we still have to traverse the entire segment to mark any new ice squares that we haven't touched before? Actually, we can mark the entire segment as we traverse, but if we encounter an already touched square, we don't stop? We must traverse until the rock because we might have new squares beyond the previously touched ones? 

However, consider: if we have already covered some part of the segment, we still need to traverse the entire segment because there might be untouched squares beyond. But note: the entire segment is contiguous. We can stop at the rock regardless.

But the problem: we must mark every ice square from the rest position until the rock. We do not skip even if we see a touched square. However, we can break early if the entire segment has been touched? Actually, no: because we don't know if beyond the last touched square there is more ice? We must go until the rock.

But note: the grid is static. So the segment from a rest position in a fixed direction is fixed. Therefore, we can simulate the slide until we hit a rock, and mark every ice square along the way.

But we must do this for every unvisited rest position in every direction that we haven't processed? 

However, the same rest position might be reached again? We are marking rest positions as visited_rest so we won't process the same rest position twice.

But note: the same ice square might be traversed multiple times in different slides? But we mark it only once.

So the algorithm:

1. Initialize:
   - touched[1][1] = True   (since we start at (1,1))
   - visited_rest[1][1] = True
   - queue = collections.deque([(1,1)])

2. While queue is not empty:
   - pop a rest position (r, c)
   - For each direction (dr,dc) in [(0,1),(0,-1),(1,0),(-1,0)]:
        - We set a temporary position (nr, nc) = (r+dr, c+dc)
        - We will traverse in that direction until we hit a rock. But note: we must also mark every ice square we traverse. However, we might traverse through some squares that are already touched? But we still need to check for new ones? Actually, we must traverse until the rock regardless to get the next rest position. And along the way, we mark every ice square as touched.

        - How to traverse? 
            Let i = 0
            current = (r, c) -> but we start from the next cell? Actually, we are going to move one step at a time.

        Instead, we can do:
            Let path = []   # we don't need to store the entire path, but we do need to mark each ice square. Also, we need to know the last ice square before the rock.

        We start from (r, c) and then we step in the direction until we hit a rock. However, note that we are already at (r,c) and we are going to move to the next. But we are going to cover (r,c) again? Actually, we don't need to: we already marked (r,c) as touched. However, we are going to traverse the entire segment including the starting rest position? 

        But note: the slide starts at the rest position and then moves in the direction. The rest position is already touched. We are going to traverse the entire segment until the rock. So we should start from the rest position and then step in the direction until we hit a rock. However, the rest position is already marked. So we can:

            Let cr = r, cc = c   # we are going to traverse from the rest position? Actually, no: we start at the rest position, then we try to move one step. But the rest position is already marked. So we are going to traverse the next squares.

        Actually, we can simulate:

            We start at (r, c) and then we move step by step in the given direction until the next square is rock. But note: the problem says: "if the next square is ice, go to that square". So we start at (r,c) and then we look at (r+dr, c+dc). If that is ice, then we move to it and then look at (r+2*dr, c+2*dc), etc.

        Therefore, we can:

            current = (r, c)   # we have already marked this as touched? Yes, but we are going to traverse the entire segment including the starting point? Actually, the starting point is already touched. But we are going to traverse the entire segment again? 

        However, we want to mark every ice square from (r, c) until the rock. But note: the segment from (r,c) to the rock is contiguous. We can traverse and mark every ice square we meet until we hit a rock.

        Steps for one direction:

            Let nr = r + dr, nc = c + dc   # the next cell
            We are going to traverse from (r, c) to the rock? Actually, we don't need to mark (r,c) again? But we can: it's harmless to mark again? But we want to avoid extra work. However, the grid is small.

        Alternatively, we can start from the rest position and then move in the direction, but we know the rest position is already touched. So we can start from the next cell. But then we might miss the rest position? We don't: because we are going to mark the entire segment? Actually, the rest position is already marked. But the segment we are about to traverse includes the rest position? 

        Actually, the slide moves from the rest position and then continues. The rest position is the starting point and then we move. But the rest position is already part of the touched set. So we can:

            path = []   # we are going to collect the entire segment? Actually, we don't need to store the entire segment, but we do need to mark each ice square we step on. However, we must traverse until the rock to find the next rest position.

            We set cr = r, cc = c -> we start at the rest position. Then we consider the next step: but note, the rest position is already marked. So we can skip marking it again? But we can do:

            cr, cc = r, c
            We mark (cr,cc) as touched? But it is already. Then we step to (cr+dr, cc+dc). Then we mark that if it's ice? Then we step again? 

        Actually, we can do:

            We start at (r, c) and then we step in the direction until we hit a rock. But we are going to mark every ice square we meet, including the starting rest position? That's okay because it's already marked. But we don't want to do extra work.

        Alternatively, we can traverse the entire segment without worrying about duplication. We just set:

            cr = r
            cc = c
            We mark (cr,cc) as touched? -> but it's already, so we can skip? But we are going to step to the next. However, we need to know the entire segment to get the next rest position.

        Actually, we can:

            We know the starting rest position (r,c) is already touched. Then we simulate:

            next_r = r + dr
            next_c = c + dc

            Then we set a variable `new_rest` = None.

            While the next square (next_r, next_c) is within the grid and is ice ('.'):
                Mark (next_r, next_c) as touched.   # This square is traversed in this slide.
                Then we move: set cr = next_r, cc = next_c, then next_r = cr+dr, next_c = cc+dc.

            But then we stop: the last ice square we marked is the rest position at the end.

            However, note: we started from (r,c) and then we mark (r+dr, c+dc) and beyond. But what about the starting rest position? We already marked it. So we don't need to mark it again.

            Then the rest position at the end is (cr, cc) (the last ice square we visited).

            But note: it is possible that we don't move at all? For example, if the next cell is rock. Then we don't mark any new square and the rest position remains (r,c). But we don't add (r,c) again because it's already visited.

        However, we might have marked new squares: the entire segment from (r+dr, c+dc) to (cr,cc) (if we moved at least one step).

        Then if the rest position (cr, cc) is not the same as the starting rest position (r,c) and we haven't visited (cr,cc) as a rest position, then we add it to the queue and mark it as visited_rest.

        But note: what if we slide and the rest position at the end is the same as the starting rest position? That happens if the next cell is rock. Then we skip.

        However, we must be careful: we might have marked new squares even if we didn't move? Actually, no: because we started at (r,c) and then the next cell is rock: so we don't mark any new square. Then we skip.

        But what if the segment we are traversing has some squares that are already touched? We still need to mark the ones that are not. But we are going to traverse the entire segment and mark every ice square we meet. Even if we mark a square that was already touched, it's a no-op. So it's okay.

        Implementation for one direction:

            dr, dc = direction vector (e.g., (0,1) for right)
            cr, cc = r, c   # we start at the rest position
            next_r = r + dr
            next_c = c + dc

            # We'll traverse as long as the next cell is ice
            # But note: we must also check that we are within the grid? Actually, the grid has rocks on the border, so we will hit a rock eventually.

            # Actually, we can traverse step by step until we hit a rock.

            # We'll create a temporary variable to store the current ice square we are about to step on? Actually, we start by stepping from (r,c) to (r+dr, c+dc). Then we check that (r+dr, c+dc) is ice? 

            temp_path = []   # we don't need the entire path, but we need the last ice square we land on (if we moved at least one step) and we need to mark the squares.

            # We start from (r+dr, c+dc) and then keep going until we hit a rock.
            cr2 = r + dr
            cc2 = c + dc

            # But what if (cr2, cc2) is rock? Then we break immediately: no new squares and the rest position remains (r,c) -> but we don't add (r,c) again.

            while 0 <= cr2 < N and 0 <= cc2 < M and grid[cr2][cc2] == '.':
                # This is ice: mark it as touched
                # But note: we might have already marked it? But we mark it again? We can use a set or a grid of booleans: setting to True multiple times is okay.
                # Mark (cr2, cc2) as touched
                if not touched[cr2][cc2]:
                    touched[cr2][cc2] = True
                # Then we move to the next in this direction
                cr2 += dr
                cc2 += dc

            # After the while loop, we have gone one step beyond the last ice? 
            # The last ice square is (cr2-dr, cc2-dc). But note: we broke when (cr2,cc2) is out of bounds or rock. So the last ice is (cr2-dr, cc2-dc).

            last_r = cr2 - dr
            last_c = cc2 - dc

            # Now, if (last_r, last_c) is not the same as (r,c) and we haven't visited (last_r, last_c) as a rest position, then we add it to the queue.

            if not visited_rest[last_r][last_c]:
                visited_rest[last_r][last_c] = True
                queue.append((last_r, last_c))

        But note: we have to consider that we might have marked some squares that were not marked before. And we are adding the rest position at the end.

        However, there is a catch: what if the segment we just traversed is already partially marked? It doesn't matter: we mark every ice square we traverse. And we are going to add the rest position if we haven't visited it.

        But note: we are traversing the entire segment from the starting rest position until the rock. We mark every ice square in that segment. Then we get the rest position at the end.

        But what if the rest position at the end is already visited? Then we don't add it to the queue.

        This BFS will cover all rest positions that are reachable and then mark all the segments we traverse.

        However, we are not storing the entire segment in the BFS state, so we are using the `touched` grid to remember which squares are touched.

        This algorithm will mark all ice squares that are in any segment that starts from a rest position that is reachable.

        But note: the starting rest position (1,1) is the first. Then we traverse the four directions from it, mark the segments, and then we get the rest positions at the ends. Then we process those.

        This should cover all reachable ice squares? Why? Because any ice square that is touched must be in a contiguous segment from some rest position. And we start from (1,1) and propagate to all reachable rest positions.

        However, consider: an ice square that is not directly in the same segment as a rest position? But note: to get to a rest position, we have to slide to it. And when we slide to a rest position, we traverse the entire segment from the previous rest position to the rock, which includes the new rest position and all the ice squares in between. Then from the new rest position, we can traverse in other directions.

        Therefore, this BFS should cover all.

        But note: we are marking the entire segment every time we slide. That might be inefficient? The grid is 200x200 and each rest position has at most 4 directions. In each direction, we traverse at most the entire row or column (which is 200). And the number of rest positions? It cannot exceed the number of ice squares? But note: we only add a rest position if it's not visited. So the total number of rest positions is at most 200*200 = 40000? Actually, the grid has at most 200*200=40000 ice squares? But the outer periphery is rock, so the inner is (N-2)*(M-2) which is at most 198*198=39204. So the BFS will visit at most 39204 rest positions? And for each we do 4 directions, each direction we traverse at most 200 steps. So total steps: 39204 * 4 * 200 = about 31.3e6, which in Python might be borderline in PyPy/C++ but in Python it might be too slow? 

        However, note: the grid is only 200x200, but the number of rest positions might be much less? Because we only add a rest position if we haven't visited it. And we mark them. But worst-case, every ice square can become a rest position? Why? Because from a rest position, we can slide to any ice square that is the last ice square in one of the four directions? 

        Actually, not every ice square: only those that are adjacent to a rock in at least one direction? But note: the rest position is defined as the last ice square in the direction we are sliding. However, an ice square that is in the middle of a row and not adjacent to a rock in any direction might never become a rest position? Actually, it might: if we slide from above and stop at that square because the next square is rock? But wait: we stop at the last ice square. So if the square is in the middle, we might not stop there? 

        Example: 
            row: [rock, ice, ice, ice, rock]
            If we start at the first ice and slide right: we will stop at the last ice (adjacent to the rock on the right). Then from that rest position, we can slide left? Then we stop at the first ice? 

        Actually, we can stop at the middle? 

        How do we get to the middle? 
            If we start at the first ice and slide right: we traverse the entire row and stop at the last ice. Then from the last ice, we slide left: we traverse the entire row and stop at the first ice. But we never stop at the middle? 

        But note: when we slide from the first ice to the right, we traverse the entire row. The middle ice is passed, so it's marked. But we don't set the middle as a rest position. 

        However, the problem: the rest position is the last ice square we land on. In the slide from the first ice to the right: we start at the first ice, then we step to the next ice: the second ice, then the third ice, then we try to step beyond and hit a rock. So the rest position at the end is the third ice. 

        Similarly, from the third ice we slide left: we step to the second ice, then the first ice? Then we stop at the first ice? 

        But note: when we slide from the third ice to the left: 
            We start at the third ice -> then we step to the second ice: mark it? but it is already marked. Then step to the first ice: mark it? Then step to the left of the first ice: rock. So we stop at the first ice.

        How do we get the second ice as a rest position? 

        We don't. Therefore, the rest positions are only the ice squares that are adjacent to a rock in the direction we are sliding? Actually, no: the rest position is the last ice square in the direction, which must be adjacent to a rock? 

        But note: we stop when the next square is rock. Therefore, the rest position must be adjacent to a rock? Not necessarily: we stop at the last ice square, and the next square is rock. So the rest position is adjacent to a rock? 

        Actually, the rest position is adjacent to a rock in the direction we just slid? 

        Therefore, the rest positions are the ice squares that are adjacent to at least one rock? But note: the rest position might be adjacent to a rock in the opposite direction? 

        Actually, no: the rest position is adjacent to a rock in the direction we slid to get there? 

        However, from that rest position, we can slide in other directions? 

        But the key: the set of rest positions we consider are exactly the ice squares that are adjacent to a rock? 

        Actually, not necessarily: consider a T-junction:

            #####
            #...#
            #.#.#
            #...#
            #####

        The center is ice. When we slide from (1,1) to the right: we stop at (1,3) (because the next is rock). Then from (1,3), we slide down: we stop at (3,3) (because the next is rock). Then from (3,3) we slide left: we stop at (3,1). Then from (3,1) we slide up: we stop at (1,1). 

        The center (2,2) is ice. How do we mark the center? 

        When we slide from (1,1) to the right: we cover the entire first row: (1,1), (1,2), (1,3). So (1,2) is marked. Then from (1,3) down: we cover (1,3), (2,3), (3,3). Then from (3,3) left: (3,3), (3,2), (3,1). Then from (3,1) up: (3,1), (2,1), (1,1). 

        The center (2,2) is not marked? 

        But wait: we started at (1,1) and slid right: we passed (1,2) and (1,3). Then from (1,3) slid down: we passed (2,3) and (3,3). Then from (3,3) slid left: we passed (3,2) and (3,1). Then from (3,1) slid up: we passed (2,1) and (1,1). 

        The center (2,2) is never passed? 

        How can we get to (2,2)? 

        Actually, we cannot? Because to stop at (2,2), we would have to slide from above, below, left, or right and stop at (2,2). But from above: we start at (1,2) and slide down: but (1,2) is ice and we can slide down? But note: from (1,1) we slid right and then we were at (1,3). Then from (1,3) we slid down to (3,3). We never visited (1,2) as a rest position? 

        Actually, we did: when we slid from (1,1) to the right: we passed (1,2). But (1,2) is passed, so it is marked. But we didn't stop at (1,2) because we kept sliding until we hit the rock at (1,4) (if the row is 5 long, then (1,4) is rock? Actually, in the example above, the grid is 5x5. The first row: "#####", then second row: "#...#" -> so (1,0) to (1,4): index0: '#', index1: '.', index2: '.', index3: '.', index4: '#'? 

        So when we slide from (1,1) to the right: we go (1,1) -> (1,2) -> (1,3) -> then we hit (1,4) which is rock -> so we stop at (1,3). 

        Then we have rest position (1,3). Then we slide down from (1,3): we go (2,3) -> (3,3) -> then we hit (4,3) which is rock? Actually, the last row is "#####", so (3,3) is the last ice? Then we stop at (3,3). 

        Now, how to get to (1,2) as a rest position? 

        We don't. Then how to mark (2,2)? 

        We don't. But wait: the center (2,2) is ice. How can we get to (2,2)? 

        We have to slide from (1,2) to down? But we never rest at (1,2). 

        But note: we passed (1,2) when we slid from (1,1) to the right. So (1,2) is marked. Then from (1,2), we can slide down? However, we are not at rest at (1,2). We are only at rest at (1,1) and then (1,3) and then (3,3) and then (3,1). 

        The problem: we are only starting a slide from a rest position. So we cannot start a slide from (1,2) because we never rested there. 

        Therefore, the center (2,2) is not touched? 

        But the example: the player can make the following moves:

            Start at (1,1) -> slide down: stop at (3,1) (because below (3,1) is rock? Actually, the grid:

                row0: "#####"
                row1: "#...#" -> (1,1) to (1,3) are ice, but (1,4) is rock.
                row2: "#.#.#" -> (2,1) is '#'? then (2,2) is '.', (2,3) is '#'? Actually, the example says: 
                    "#.#.#" -> so (2,1): '#' -> rock, (2,2): '.' -> ice, (2,3): '#' -> rock.

                So if we start at (1,1) and slide down: 
                    (1,1) -> then down: (2,1) is rock? So we don't move. Then we try right: (1,1) to (1,2): ice -> then (1,3): ice -> then (1,4): rock -> stop at (1,3).

                Then from (1,3) down: (2,3) is rock? So we don't move. Then we try left: (1,3) to (1,2): ice -> then (1,1): ice? But we started at (1,3) and slid left: we go to (1,2) and then (1,1) and then (1,0) is rock? So we stop at (1,1). 

                Then we are stuck.

        How to get to the center? 

        Actually, we cannot. So the center (2,2) is not touched. 

        Therefore, the algorithm we described does not mark the center? 

        But the problem: the example we described above (the T-junction) is not given. However, the example in the problem:

            Input: 
                6 6
                ######
                #....#
                #.#..#
                #..#.#
                #....#
                ######

            Output: 12.

            How many ice squares? 
                Row1: (1,1) to (1,4): 4
                Row2: (2,1), (2,2) -> skip (2,3) because it's rock? Actually, the second row: "#....#" -> so (1,0) to (1,5): in 0-indexed:
                    row0: "######"
                    row1: "#....#" -> so ice at (1,1) to (1,4) -> 4
                    row2: "#.#..#" -> ice at (2,1) and (2,3) and (2,4) -> but wait: 
                        Actually: 
                            row2: index0: '#' 
                            index1: '.' -> (2,1)
                            index2: '#' -> rock
                            index3: '.' -> (2,3)
                            index4: '.' -> (2,4)
                            index5: '#' -> so 3 ice?
                    row3: "#..#.#" -> ice: (3,1), (3,4) -> 2
                    row4: "#....#" -> ice: (4,1) to (4,4) -> 4

                Total ice: 4+3+2+4 = 13? But the output is 12.

            The problem says: the player cannot pass or rest on (3,4). Actually, (3,4) is ice? In row3: 
                row3: "#..#.#" -> 
                    index0: '#' 
                    index1: '.' -> (3,1)
                    index2: '.' -> no: the string is "#..#.#" -> 
                        Actually: 
                            [0]: '#' 
                            [1]: '.' -> (3,1)
                            [2]: '.' -> (3,2) is ice? but the problem says it's rock? 
                    Wait: the example input says:
                        "#..#.#" -> 
                        So: 
                            [0]: '#' 
                            [1]: '.' -> ice
                            [2]: '.' -> ice? 
                            [3]: '#' -> rock
                            [4]: '.' -> ice
                            [5]: '#' -> rock

                    Actually, no: the example says: 
                        "#..#.#" -> 
                        So: 
                            [0]: '#' 
                            [1]: '.' -> ice
                            [2]: '.' -> ice? 
                            [3]: '#' -> rock
                            [4]: '.' -> ice
                            [5]: '#' -> rock

                    But then the ice in row3: (3,1), (3,2), (3,4) -> 3.

                Then total ice: 4 (row1) + 3 (row2: (2,1), (2,3), (2,4)) + 3 (row3) + 4 (row4) = 14.

            However, the output is 12.

            The problem says: the player can rest on (5,5) (which is (4,4) in 0-indexed) and can pass (2,4) (which is (1,3) in 0-indexed? Actually, row2: row index=1: (1,3) is the fourth column? 0-indexed: row1, col3).

            And the player cannot pass or rest on (3,4) -> which in 0-indexed is (2,3) or (2,4)? 

            Actually, the problem says: "The player cannot pass or rest on (3,4)".

            In 1-indexed: (3,4) -> 0-indexed: row=2, col=3. 

            In row2: 
                row2: "#.#..#" -> 
                    In 0-indexed: 
                    row2: 
                        index0: '#' 
                        index1: '.' -> (2,1)
                        index2: '#' -> rock
                        index3: '.' -> (2,3) -> this is (3,4) in 1-indexed? 
                            Actually: 
                                row index: 2 -> 1-indexed row3? 
                                But the grid: 
                                    row0: ###### -> row1 in 1-indexed?
                                    row1: #....# -> row2 in 1-indexed?
                                    row2: #.#..# -> row3 in 1-indexed? 
                                Then (2,3) in 0-indexed is row3, column4? 

            Actually, the problem uses 1-indexed: (i,j) for the i-th row and j-th column. The input has N rows. The first row is row1. So in our 0-indexed, row0 is the first row.

            So the grid:

                0: "######" -> 1-indexed row1: all rock.
                1: "#....#" -> row2: ice at (2,2) to (2,5) -> 1-indexed: (2,2) to (2,5) -> but in 0-indexed: row1, col1 to col4.
                2: "#.#..#" -> row3: ice at (3,2) and (3,4) and (3,5) -> 1-indexed: (3,2), (3,4), (3,5) -> but wait: 
                    In 0-indexed row2: 
                        col0: '#' 
                        col1: '.' -> row2, col1 -> 1-indexed (3,2)
                        col2: '#' 
                        col3: '.' -> row2, col3 -> 1-indexed (3,4)
                        col4: '.' -> row2, col4 -> 1-indexed (3,5)
                        col5: '#'

            So (3,4) in 1-indexed is (3,4) -> which is row2, col3 in 0-indexed? 

            The problem says: the player cannot touch (3,4). 

            How do we get 12? 

            Total ice squares: 
                row1: 4
                row2: 3
                row3: 3
                row4: 4 -> total 14.

            We are missing 2.

            The problem says: the player can touch 12 ice squares. 

            So two ice squares are not touched? 

            We must simulate:

                Start at (2,2) in 1-indexed -> (1,1) in 0-indexed.

                From (1,1) we can slide:

                    Right: (1,1) -> (1,2) -> (1,3) -> (1,4) -> then (1,5) is rock -> stop at (1,4). This segment: (1,1), (1,2), (1,3), (1,4) -> 4 squares.

                    Down: (1,1) -> (2,1) -> (3,1) -> (4,1) -> (5,1) is rock -> stop at (4,1). This segment: (1,1), (2,1), (3,1), (4,1) -> 4 squares.

                    But wait: we already counted (1,1) twice? 

                Then from (1,4) (0-indexed: row1, col4) we can slide:

                    Down: (1,4) -> (2,4) -> (3,4) -> but wait: (3,4) in 0-indexed: row3, col4? 

                    Actually, row2: "#.#..#" -> row index=1: 
                        row1: [0]='#', [1]='.', [2]='#', [3]='.', [4]='.', [5]='#'
                    So from (1,4) (0-indexed: row1, col4) sliding down:
                        row2: (2,4) -> ice? 
                        row3: (3,4) -> ice? 
                        row4: (4,4) -> ice? 
                        then row5: rock -> stop at (4,4).

                    This segment: (1,4), (2,4), (3,4), (4,4) -> 4 squares.

                Then from (4,1) (0-indexed: row4, col1) we can slide:

                    Right: (4,1) -> (4,2) -> (4,3) -> (4,4) -> then (4,5) is rock -> stop at (4,4). 
                    This segment: (4,1), (4,2), (4,3), (4,4) -> 4 squares.

                Then from (4,4) we can slide up? 
                    (4,4) -> (3,4) -> (2,4) -> (1,4) -> then (0,4) is rock -> stop at (1,4). 
                    But we have already visited (1,4). 

                Now, the set of touched ice squares: 
                    We have:
                    row1: (1,1), (1,2), (1,3), (1,4) -> 4
                    row2: (2,1) -> from the down slide from (1,1); (2,4) -> from the down slide from (1,4) -> 2
                    row3: (3,1) -> from down slide from (1,1); (3,4) -> from the down slide from (1,4) -> 2
                    row4: (4,1), (4,2), (4,3), (4,4) -> 4

                    Total: 4+2+2+4 = 12.

                And note: the ice squares that are not touched: 
                    row2: (2,3) -> 0-indexed: row1, col2? -> but wait: row2 (0-indexed row1): 
                        row1: "#....#" -> so col1 to col4: all ice -> we have (1,1) to (1,4) -> so that's row1: all 4 are touched.
                    row2: 0-indexed row1: already done? 
                    Then row2: 0-indexed row2: 
                        row2: "#.#..#" -> ice at col1, col3, col4 -> we have (2,1) and (2,4) -> but (2,3) is col3? -> not touched? 
                    row3: 0-indexed row3: 
                        row3: "#..#.#" -> ice at col1, col2, col4 -> we have (3,1) and (3,4) -> but (3,2) is col2? -> not touched? 

                So two ice squares: (2,3) and (3,2) in 0-indexed? 

                But the problem says: (3,4) in 1-indexed is not touched -> that is (2,3) in 0-indexed? 

                And we see that (2,3) in 0-indexed (which is row2, col3) is not touched. 

                The other one: (3,2) in 0-indexed: row3, col2 -> which is ice? 
                    row3: "#..#.#" -> 
                        0: '#' 
                        1: '.' -> ice at col1 -> (3,1) is touched? 
                        2: '.' -> ice at col2 -> (3,2) -> not touched? 
                        3: '#' 
                        4: '.' -> (3,4) -> touched? 
                        5: '#' 

                So indeed, (3,2) is ice and not touched.

                But the problem only mentioned (3,4) in 1-indexed? 

            Therefore, the algorithm we described above: 

                We start at (1,1) (0-indexed). 
                We slide right: mark (1,1), (1,2), (1,3), (1,4) -> then rest at (1,4). 
                We slide down from (1,1): mark (1,1) [already], (2,1), (3,1), (4,1) -> rest at (4,1). 
                Then we process (1,4): 
                    slide down: mark (1,4) [already], (2,4), (3,4), (4,4) -> rest at (4,4). 
                Then we process (4,1): 
                    slide right: mark (4,1) [already], (4,2), (4,3), (4,4) -> rest at (4,4) -> already visited? 
                Then we process (4,4): 
                    slide up: mark (4,4)[already], (3,4)[already], (2,4)[already], (1,4)[already] -> rest at (1,4) -> visited.

                Then we stop.

                The marked ice squares: 
                    row0: none
                    row1: [1,2,3,4] -> 4
                    row2: col1 and col4 -> 2
                    row3: col1 and col4 -> 2
                    row4: [1,2,3,4] -> 4
                    total 12.

            So the algorithm as described above would work for the example.

            But why didn't we mark (1,2) in row2 (0-indexed row1, col2) as a rest position? 

            When we slid right from (1,1): we started at (1,1) and then moved to (1,2): marked, then (1,3): marked, then (1,4): marked. Then we stopped at (1,4). We never stopped at (1,2) because we kept going until we hit a rock. So (1,2) is not a rest position. 

            Similarly, (1,3) is not a rest position: we stopped at (1,4). 

            Therefore, the rest positions are only the ice squares that are adjacent to a rock? 

            In the example: 
                (1,4): adjacent to rock at (1,5) -> so rest position.
                (4,1): adjacent to rock at (5,1) -> rest position.
                (4,4): adjacent to rock at (4,5) -> rest position.

            The center (2,3) (0-indexed: row1, col2) is not adjacent to a rock? 

            Actually, in row2 (0-indexed row1): 
                col1: ice, col2: ice, col3: ice, col4: ice. 
                The rocks are at col0 and col5. 

            So the rest positions in row1: only col1 (if we slide from above) and col4 (if we slide from the right). But we didn't get col1 as a rest position from the right slide? We got col4. 

            How did we get (4,1)? From sliding down from (1,1). 

            So the rest positions are the ice squares that are adjacent to a rock in the direction we slid to get there? And also, we can get rest positions that are adjacent to a rock in the opposite direction? 

            Actually, when we slide from (1,1) down: we stop at (4,1) because below (4,1) is rock. So (4,1) is adjacent to rock below. Similarly, (1,4) is adjacent to rock to the right. (4,4) is adjacent to rock below and to the right.

            Therefore, the rest positions are the ice squares that are adjacent to a rock in at least one direction? 

            But note: we might slide to a rest position that is adjacent to a rock in a direction that is not the direction we came from? For example, an ice square that is adjacent to a rock above, but we slid from the left? Then we stop there because we hit a rock to the right? 

            Actually, no: we stop because we hit a rock in the direction we are sliding? 

            Therefore, the rest position we land on must be adjacent to a rock in the direction we slid? 

            So the algorithm: we only consider rest positions that are adjacent to a rock in at least one direction? And we propagate to all such rest positions that are reachable. 

            But note: we might have an ice square that is adjacent to a rock but not reachable? Then we don't mark it. 

            So the BFS we described: 
                We start at (1,1) (0-indexed) -> which is adjacent to a rock? 
                    (0,1): rock above? -> so adjacent to rock above? 
                Then we slide in each direction until we hit a rock, and then we get a rest position that is adjacent to that rock? 

            Therefore, the algorithm is:

                Let touched = 2D boolean grid, size N x M, all False.
                Let visited_rest = 2D boolean grid, size N x M, all False.

                Queue = deque([(1,1)])
                touched[1][1] = True
                visited_rest[1][1] = True

                While queue:
                    pop (r, c)
                    for each direction (dr,dc) in [(0,1),(0,-1),(1,0),(-1,0)]:
                        cr, cc = r, c
                        nr = r + dr, nc = c + dc
                        # We are going to traverse from (r+dr, c+dc) until we hit a rock? But note: we start at (r,c) and then move to (r+dr, c+dc). 
                        # Instead, we can traverse the entire segment until the rock, marking every ice square.

                        # We'll create a list for the segment? We don't need to store, we just mark and then get the last ice.

                        # But we must traverse one step at a time.

                        # We are going to simulate:

                        # Start from (r+dr, c+dc) and then keep moving in (dr,dc) until we hit a rock or go out of bounds.

                        cr2 = r + dr
                        cc2 = c + dc
                        segment = []   # we don't really need to store, but we need to know the last ice square we were on.

                        # But we must mark every ice square we land on.

                        while 0<=cr2<N and 0<=cc2<M and grid[cr2][cc2]=='.':
                            # Mark this square as touched
                            if not touched[cr2][cc2]:
                                touched[cr2][cc2] = True
                            # Also, we note that we are going to use this square to continue? 
                            # Then we move one more step in the direction.
                            cr2 += dr
                            cc2 += dc

                        # Now, we have gone one step beyond the last ice? So the last ice square is (cr2-dr, cc2-dc)

                        last_r = cr2 - dr
                        last_c = cc2 - dc

                        # Now, if (last_r, last_c) is not the same as (r,c) [which it won't be if we moved at least one step] and we haven't visited (last_r, last_c) as a rest position, then we add it to the queue.

                        if (last_r, last_c) != (r, c) and not visited_rest[last_r][last_c]:
                            visited_rest[last_r][last_c] = True
                            queue.append((last_r, last_c))

                Then the answer is the total number of True in `touched`.

            However, note: we mark the starting rest position (1,1) as touched. Then in the slides from (1,1) we mark the entire segment. Then we get new rest positions and then from there we mark the entire segment. 

            But what about the starting rest position: we mark it at the beginning. Then in the slides, we might traverse it again? But we mark it again? That's harmless.

            But note: when we slide from (1,1) in a direction, we start at (1,1) and then move to (1,1)+direction. We do not mark (1,1) again in the slide? Actually, we mark the next square. But we already marked (1,1) at the start. 

            However, what if we don't move? Then we skip. 

            This algorithm should work.

            Let's test with the example of the center square that is not adjacent to a rock? 

            Example: 
                #####
                #...#
                #.#.#
                #...#
                #####

            We start at (1,1) (0-indexed: the ice at row1, col1). 
            Mark (1,1) as touched.

            Then slide right: 
                (1,1) -> then (1,2): ice -> mark (1,2); (1,3): ice -> mark (1,3); (1,4): rock -> last ice is (1,3). 
                Then we add (1,3) to the queue.

            Then slide left: 
                (1,1) -> (1,0): rock -> so we don't mark any new square? and last ice is (1,1) -> same as start -> skip.

            Then slide down:
                (1,1) -> (2,1): rock -> skip.

            Then slide up: 
                (1,1) -> (0,1): rock -> skip.

            Then we process (1,3):

                Slide left: 
                    (1,3) -> (1,2): ice -> mark (1,2) [already marked] -> (1,1): ice -> mark (1,1) [already] -> then (1,0): rock -> last ice is (1,1). 
                    Then we try to add (1,1) -> but already visited -> skip.

                Slide right: 
                    (1,3) -> (1,4): rock -> skip.

                Slide down: 
                    (1,3) -> (2,3): rock -> skip.

                Slide up: 
                    (1,3) -> (0,3): rock -> skip.

            Then the marked ice squares: 
                row1: (1,1), (1,2), (1,3) -> 3.
                row2: none? 
                row3: none? 
                row4: none? 

            The center (2,2) (0-indexed: row2, col2) is not marked. 

            So the algorithm does not mark it. 

            But we cannot reach it? 

            Therefore, the algorithm is correct.

            Time complexity: 
                The grid has at most 200*200=40000 ice squares? 
                But the rest positions: only the ice squares that are adjacent to a rock? How many? 
                The grid has outer periphery rock. The inner ice squares: 
                    Adjacent to a rock: 
                        They must be on the border of the inner grid? 
                    Actually, an ice square is adjacent to a rock if it has a rock neighbor? 

                How many ice squares are adjacent to a rock? 
                    The rocks are on the outer border and also inside? 
                    The inner ice squares: 
                        The ones that are adjacent to a rock: 
                            They are either adjacent to the outer rock wall or to an internal rock.

                The maximum number of rest positions: the entire inner ice grid might be adjacent to a rock? 

                Actually, worst-case: a grid with no internal rocks. Then the rest positions are the ice squares adjacent to the outer rock wall? 
                    The outer rock wall: 
                        row0 and rowN-1: all rock -> adjacent to row1 and rowN-2? 
                        col0 and colM-1: all rock -> adjacent to col1 and colM-2? 

                So the rest positions are the ice squares in row1, rowN-2, col1, colM-2? 

                How many? 
                    The first inner row: row1: M-2 ice squares? 
                    The last inner row: rowN-2: M-2 ice squares? 
                    The first inner col: col1: but excluding the ones in row1 and rowN-2? -> N-4 ice squares? 
                    Similarly for colM-2: N-4? 

                Total: 2*(M-2) + 2*(N-4) = about 2*200 + 2*200 = 800? 

                But worst-case: the grid has internal rocks? Then the rest positions are the ice squares adjacent to any rock (outer or inner). 

                The maximum: if the grid is a checkerboard? Then every ice square is adjacent to at least one rock? Then the rest positions are all ice squares. 

                So worst-case: 40000 rest positions? 

                Then for each rest position: we do 4 directions, each direction we traverse at most 200 steps. 

                Total: 40000 * 4 * 200 = 32e6 -> which in Python might be acceptable? 

            But note: the grid size is 200x200, so the total number of ice squares is at most 40000? Actually, the outer periphery is rock, so the inner is (N-2)*(M-2) which is at most 198*198 = 39204.

            And 39204 * 4 * 200 = 31.3e6, which is acceptable in Pyton? 

            However, worst-case in Python might be 31.3e6 iterations, which is acceptable in Pyton (if each iteration is simple). 

            But note: in the inner loop for one direction, we traverse until a rock. The worst-case for one direction is the entire row or column (200 steps). 

            So worst-case 31.3e6 iterations is acceptable in Pyton? 

            We should test on the maximum input: 200x200, and worst-case (all ice) and then with the rest positions being all ice squares (39204). 

            But note: the grid is all ice? Then the rest positions are the ice squares adjacent to the outer rock? 

                Actually, if the grid is all ice, then the rest positions are the ice squares adjacent to the outer rock? That is the entire inner grid? No: the rest positions are the ice squares adjacent to the outer rock? 

                Actually, the rest positions are defined by the last ice square in a slide. In an all ice grid, when we slide from (1,1) to the right, we stop at (1, M-2) (because (1, M-1) is rock). Similarly, we get rest positions at (1, M-2), (N-2,1), (N-2, M-2), and also from (1,1) down: (N-2,1). 

                Then from (1, M-2): we slide down: stop at (N-2, M-2). 
                Then from (N-2,1): we slide right: stop at (N-2, M-2). 
                Then we have the entire border of the inner grid as rest positions? 

                But note: we start at (1,1) and then we get (1, M-2) and (N-2,1). Then from (1, M-2) we get (N-2, M-2). From (N-2,1) we get (N-2, M-2). 

                Then we don't get the other ice squares in the inner border? 

                How to get (1,2)? 
                    From (1,1) we slid right: we passed (1,2) and (1,3) ... but we did not stop at (1,2). 
                    Then we never start a slide from (1,2). 

                Therefore, the rest positions are only the corners? 

                But wait: from (1, M-2) we can slide left? 
                    We start at (1, M-2) and slide left: we go to (1, M-3), (1, M-4), ... until (1,1). Then we stop at (1,1) (because we hit rock at (1,0)). 

                Then we don't get any new rest position? 

                How to get (1,2) as a rest position? 

                We don't. 

                Therefore, the rest positions are only the four corners? 

                Then the total rest positions is 4? 

                But wait: from (1,1) we can slide down: to (N-2,1). Then from (N-2,1) we can slide right: to (N-2, M-2). Then from (N-2, M-2) we can slide up: to (1, M-2). Then from (1, M-2) we can slide left: to (1,1). 

                And we mark every ice square we traverse? 

                In the first slide from (1,1) to the right: we mark the entire row1: (1,1) to (1, M-2). 
                In the slide from (1,1) down: we mark the entire col1: (1,1) to (N-2,1). 
                Then from (1, M-2) down: we mark the entire col (M-2): (1, M-2) to (N-2, M-2). 
                Then from (N-2,1) right: we mark the entire row (N-2): (N-2,1) to (N-2, M-2). 

                Then we have marked:
                    row1: all ice -> M-2 squares.
                    rowN-2: all ice -> M-2 squares.
                    col1: excluding row1 and rowN-2? -> N-4 squares? 
                    colM-2: excluding row1 and rowN-2? -> N-4 squares? 

                Total: (M-2) + (M-2) + (N-4) + (N-4) = 2*(M-2) + 2*(N-4).

                But the entire inner grid has (N-2)*(M-2) ice squares. 

                We are missing the inner part: for rows from 2 to N-3 and cols from 2 to M-3.

                Therefore, the algorithm would only mark the border? 

                But the problem: the player can only slide in straight lines. So to get to the center, we need to have a rest position in the center? 

                But the center is not adjacent to a rock? So we cannot stop there? 

                Then we cannot mark the center? 

                Therefore, the algorithm is correct.

            So we implement the algorithm as described.

            Steps:

                Let N, M = input dimensions.
                grid = list of strings.

                touched = [[False]*M for _ in range(N)]
                visited_rest = [[False]*M for _ in range(N)]

                from collections import deque
                q = deque()
                start_r, start_c = 1, 1   # 0-indexed: because the grid has outer rock, and (2,2) in 1-indexed is (1,1) in 0-indexed.

                touched[1][1] = True
                visited_rest[1][1] = True
                q.append((1,1))

                dirs = [(0,1), (0,-1), (1,0), (-1,0)]

                while q:
                    r, c = q.popleft()
                    for dr, dc in dirs:
                        # We are going to traverse in direction (dr,dc) until we hit a rock.
                        # Start from (r+dr, c+dc) and then keep going until we hit a rock.
                        cr = r + dr
                        cc = c + dc
                        # We'll traverse as long as we are in the grid and the cell is ice.
                        while 0<=cr<N and 0<=cc<M and grid[cr][cc]=='.':
                            # Mark this ice square as touched.
                            # But note: we might have already marked it? But we set it to True.
                            touched[cr][cc] = True
                            # Move one step in the direction.
                            cr += dr
                            cc += dc

                        # After the while loop, we have gone one step beyond the last ice? 
                        # So the last ice square is (cr-dr, cc-dc)
                        last_r = cr - dr
                        last_c = cc - dc

                        # Now, if we moved at least one step? Then (last_r, last_c) is not the same as (r,c)? 
                        # But note: it might be the same if the next cell is rock? 
                        # Actually, if the next cell (r+dr, c+dc) is rock, then the while loop condition fails immediately -> then we don't enter the loop. Then last_r = (r+dr) - dr = r, last_c = (c+dc) - dc = c.
                        # So if (last_r, last_c) == (r, c), then we skip adding.

                        if last_r == r and last_c == c:
                            # We did not move: so skip adding this as a new rest position.
                            continue

                        # Now, if we have not visited the rest position (last_r, last_c), then we add it to the queue.
                        if not visited_rest[last_r][last_c]:
                            visited_rest[last_r][last_c] = True
                            q.append((last_r, last_c))

                # Then we count the total number of ice squares that are marked as touched.

                But note: we have marked the entire segment we traversed, including the starting rest position? 
                However, we marked the starting rest position (1,1) initially. Then in the slides from (1,1), we mark the next squares. 

                But what about the rest positions we land on: we don't mark them in the BFS? 
                    Actually, we mark them during the slide: because the slide covers the entire segment from the starting rest position to the rock, which includes the rest position at the end. 

                However, we do: in the while loop, we mark every ice square we step on. 

                But note: the rest position at the end is an ice square, so we marked it during the slide. 

                Therefore, we only need to count the touched ice squares.

                Then we iterate over the entire grid and count:
                    count = 0
                    for i in range(N):
                        for j in range(M):
                            if grid[i][j] == '.' and touched[i][j]:
                                count += 1

                But wait: the starting rest position is ice and we marked it. 

                Alternatively, we can just count the True in `touched` for the ice squares? 

                Actually, we marked only ice squares: because we only run the while loop for ice squares.

                But note: we mark the entire segment we traverse, and we traverse only ice squares. 

                However, we mark an ice square only if we traverse it? 

                But what if an ice square is never traversed? Then it remains False.

                Therefore, we can simply sum up the `touched` for the ice squares? 

                But note: we might mark a rock? No: we only mark when the grid[cr][cc]=='.' -> then we mark it. 

                Actually, we mark only the ice squares that we traverse.

                However, the problem: we are only to count the ice squares that are touched. 

                Therefore, the answer is the total number of ice squares that are marked as touched.

                But note: we don't mark rocks. 

                So we can do:

                    count = 0
                    for i in range(N):
                        for j in range(M):
                            if touched[i][j]:
                                count += 1

                Because we only mark ice squares (and we mark an ice square if we traverse it). 

            But wait: what if the grid has an ice square that we don't traverse? Then we don't mark it. 

            So we don't count it.

            Therefore, we can just count the True in `touched`.

            However, we can also avoid the double loop by keeping a running count? But the grid is small.

            Let's run the example:

                Example: 6x6 grid.

                We have 12 touched ice squares. 

            So we output 12.

            Now, test with the example that has the center not touched: we output 3 (the border of the inner grid) for the 5x5 grid? 

            Actually, the 5x5 grid (5 rows, 5 columns) in the T-junction example:

                Marked: 
                    row1: [1,2,3] -> 3
                But the grid has ice in row1: [1,2,3] (0-indexed) -> 3, and row3: [1,3] -> but we didn't mark row3? 

                Actually, we only marked row1? 

                So the algorithm: 
                    Start at (1,1): 
                    Slide right: mark (1,1), (1,2), (1,3) -> then last ice is (1,3). 
                    Then we add (1,3) to the queue.

                    Then from (1,3): 
                    Slide left: mark (1,3) [already], (1,2)[already], (1,1)[already] -> last ice is (1,1) -> already visited, skip.

                    Then done.

                So we only mark 3? 

                But wait: we also did slide down from (1,1)? 
                    (1,1) down: (2,1) is rock -> so we break immediately? 
                    Then we don't mark (2,1) because it's rock? 

                Actually, the grid row2 (0-indexed row1) is "#...", then row2 (0-indexed row2) is "#.#" -> so (2,1) is rock? 

                Then the down slide from (1,1): 
                    We start at (1,1) -> next is (2,1) -> rock -> so we break the while loop condition: grid[2][1] is '#' -> so we don't enter the while loop. 
                    Then last_r = (2,1)- (0,1) = (2,1) - (1,0) -> but direction (1,0): so (2,1) - (1,0) = (1,1). 
                    Then we skip because last_r, last_c = (1,1) which is the same as the start.

                So we don't mark any new square? 

            Therefore, we only mark 3? 

            But the example has 3 ice squares: the entire first row of the inner grid? 

            So the output is 3.

            But the example grid has 9 ice squares? 
                row1: 3
                row3: 3
                row2: 1? 
                Actually, the grid:

                    row0: "#####" 
                    row1: "#...#" -> ice at (1,1), (1,2), (1,3) -> 3
                    row2: "#.#.#" -> ice at (2,1), (2,3) -> 2? 
                    row3: "#...#" -> ice at (3,1), (3,2), (3,3) -> 3
                    row4: "#####"

                Total ice: 8.

            We marked 3.

            So the algorithm is correct: we cannot reach the others.

            Therefore, we implement the algorithm.

            Let's test with the provided examples.

            Example 1: 
                Input: 
                    6 6
                    ######
                    #....#
                    #.#..#
                    #..#.#
                    #....#
                    ######

                We expect 12.

            Example 2: 
                Input: 
                    21 25
                    ... (as provided)

                Output: 215.

            We'll code accordingly.

            One more note: 
                In the while loop for the slide, we are moving step by step. 
                But we are marking the square we are on and then moving to the next? 

                Specifically:

                    cr = r+dr, cc = c+dc
                    while in bounds and ice:
                        mark (cr,cc) as touched
                        then cr += dr, cc += dc

                This will mark (r+dr, c+dc) and then (r+2*dr, c+2*dc) etc. 

            This matches the problem: the slide moves from the rest position to the next ice, then the next, until the next is rock.

            Therefore, we mark every ice square from (r+dr, c+dc) to the last ice.

           

## Final Solution

