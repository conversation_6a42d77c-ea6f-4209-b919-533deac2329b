# Solution for abc335_c.md

Generated at: 2025-07-22 06:50:13
Model: deepseek-reasoner

## Reasoning Process

Alright, I'm going to tackle this problem step by step. Let me start by understanding what the problem is asking.

We have a dragon made of N parts, numbered from 1 to N. Initially, part i is at (i, 0). So, for example, if N=5, the parts are at (1,0), (2,0), (3,0), (4,0), (5,0). Part 1 is the head.

There are two types of queries:
1. `1 C`: Move the head one unit in direction C (which can be 'R', 'L', 'U', or 'D'). After moving the head, each subsequent part (from part 2 to part N) moves to the position that the part in front of it was at before the move. So, part 2 goes to where part 1 was, part 3 goes to where part 2 was, and so on.
2. `2 p`: Output the current coordinates of part p.

Constraints are large: N up to 10^6 and Q up to 200,000. So, we have to be efficient.

First, let me consider a brute force approach. In the brute force, we could maintain an array of the current positions of all N parts. For each "1 C" query, we update the head's position, and then for each part from 2 to N, we set its position to the previous position of the part in front of it. But this would be O(N) per move query. With Q up to 200,000 and N up to 10^6, worst-case Q * N is 200,000 * 10^6 = 20e12 operations, which is way too slow. So, we need a better approach.

Next, I need to think of an optimized solution. The key observation is that each part follows the part in front of it. This is similar to a snake game. The movement of the entire body is determined solely by the movement of the head and the history of the head's positions.

Specifically, at any time, the position of part i (for i>=2) is the position that the head was at (i-1) moves ago. But wait, is that exactly true?

Let me simulate with the example to see.

Example input:
5 9
2 3
1 U
2 3
1 R
1 D
2 3
1 L
2 1
2 5

Initial state: parts at (1,0), (2,0), (3,0), (4,0), (5,0)

Query 1: "2 3" -> output (3,0)
Then, "1 U": move head up. Head (part1) moves from (1,0) to (1,1). Then, each part from 2 to 5 moves to the previous position of the part in front. So:
- Part2 moves to (1,0) (where part1 was)
- Part3 moves to (2,0) (where part2 was)
- Part4 moves to (3,0) (where part3 was)
- Part5 moves to (4,0) (where part4 was)

Now, positions are:
1: (1,1)
2: (1,0)
3: (2,0)
4: (3,0)
5: (4,0)

Next query: "2 3" -> output (2,0) which matches the example.

Then, "1 R": move head right. Head moves from (1,1) to (2,1). Then:
- Part2 moves to (1,1) (previous head position)
- Part3 moves to (1,0) (where part2 was)
- Part4 moves to (2,0) (where part3 was)
- Part5 moves to (3,0) (where part4 was)

Now, positions:
1: (2,1)
2: (1,1)
3: (1,0)
4: (2,0)
5: (3,0)

Then, "1 D": move head down. Head moves from (2,1) to (2,0). Then:
- Part2 moves to (2,1) (previous head)
- Part3 moves to (1,1) (previous part2)
- Part4 moves to (1,0) (previous part3)
- Part5 moves to (2,0) (previous part4)

Now, positions:
1: (2,0)
2: (2,1)
3: (1,1)
4: (1,0)
5: (2,0)

Then, "2 3": output part3 -> (1,1). But the example output is "1 1", which matches.

Then, "1 L": move head left. Head moves from (2,0) to (1,0). Then:
- Part2 moves to (2,0) (previous head)
- Part3 moves to (2,1) (previous part2)
- Part4 moves to (1,1) (previous part3)
- Part5 moves to (1,0) (previous part4)

Now, positions:
1: (1,0)
2: (2,0)
3: (2,1)
4: (1,1)
5: (1,0)

Then, "2 1": output (1,0) and "2 5": output (1,0). Which matches the example.

So, from this simulation, we can observe that the position of part i at time t is the position that the head had at time (t - (i-1)). But only if the head has moved at least (i-1) times. Otherwise, for initial positions, when the head hasn't moved enough, the part i would still be at the initial position? Actually, no. Initially, the head hasn't moved, so part i is at (i,0). But the head's initial position is (1,0). So, if we consider time 0 (initial state), then at time 0, head is at (1,0). Then, after the first move (time step 1), the head moves, and part2 goes to (1,0) which was the head at time0.

So, indeed, part i at time t is the position of the head at time (t - (i-1)), provided that (i-1) <= t. If (i-1) > t, then the part hasn't been affected by any moves and is still at (i,0). But actually, the initial position of part i is (i,0). The head starts at (1,0). So, for part i, before the head has moved (i-1) times, the position of part i is (i - (number of moves that have propagated to it)? Actually, we can think of the head's position history.

Thus, the solution is to record the entire history of the head's positions. Let me denote:
- Let time = 0: head is at (1,0)
- Then, for each move query (type "1 C"), we update the head's position and record it.

But the problem is that we have Q up to 200,000. So, we can store an array `head_pos` of size Q+1 (for time 0 to time T, where T is the number of move queries so far). Then, for a query "2 p" at time t (where t is the current number of moves that have been processed), we want to get the head position at time (t - (p-1)).

But note: if (p-1) > t, then we are asking for a time before 0. But time 0 is the initial state. So, for any part p, if (p-1) <= t, then we use head_pos[t - (p-1)]. Otherwise, if (p-1) > t, then the part p has not been moved by the head's movements enough; it's still at its initial position? Actually, no. Because the initial position of part p is (p,0). But how does that relate to the head's history?

Wait, at time 0, the head is at (1,0). Then, for part p, at time 0, it is at (p,0). But if we think about the head's history, we have only recorded the head's positions. How do we get the initial position of part p? The head's history at time 0 is (1,0). But part 2 at time0 is (2,0). So, we cannot use the head's history to get (2,0) at time0? We need to account for that.

Alternatively, we can store the initial positions as well. But note: the initial state is that each part i is at (i,0). Then, after the first move, the head moves and then each part moves to follow. So, the position of part i at time t (current time) is:

if t < i-1: the part hasn't moved from its initial position: (i, 0)
else: the head's position at time t - (i-1)

But wait, after the first move (time1), the head moves to a new position. Then, part2 moves to the head's initial position (time0). Part3 is still at (3,0) because the movement hasn't propagated that far? Actually, after the first move, part2 goes to (1,0) and part3 stays? No, according to the problem, each part moves to the previous position of the part in front. So, after the first move, part2 moves to where part1 was (which is (1,0)), part3 moves to where part2 was (which was (2,0)), and so on. So, actually, the initial positions are shifted back: part2 becomes (1,0), part3 becomes (2,0), etc. But that means that after the first move, part3 is at (2,0), which is the same as the head's position at time0? No, the head at time0 was (1,0). So part3 at time1 is (2,0). How do we get that from the head's history?

We see that at time0, head is at (1,0). At time1, head moves to (1,1) (if moving up). Then, part2 at time1 is head at time0: (1,0). Part3 at time1 is (2,0). But (2,0) is the head's position at time -1? We don't have time -1.

Alternatively, we can consider the entire body's movement as a queue. The head moves, and then the tail follows the history of the head. But the initial body is a straight line from (1,0) to (N,0). So, the head's history for the first N moves would be:

time0: (1,0)
time1: (1,1)   [if first move is U]
time2: (2,1)   [if next is R]
...

But for part i, at time t, we need the head position at time (t - (i-1)). However, for t < i-1, we don't have that time. So, we need to store the initial positions for the entire body? Actually, we can handle the initial positions separately.

Alternatively, we can prefill the head's history for negative times? Actually, we can note that at time t=0, the head is at (1,0). For any part i, if we consider the time when the head was at a position that would now be the position of part i, that time is t - (i-1). But for t < i-1, t - (i-1) is negative. So, we can define for negative times: at time t = -k (for k>0), the head was at (1 - k, 0)? That doesn't make sense because the head started at (1,0). Actually, no.

Wait, the initial positions: part i is at (i,0). But the head is part1 at (1,0). The positions of the other parts are determined by the initial setup. How can we model the history for the head so that at time t = - (i-1), we have the position of part i? 

At time t = - (i-1), the head would have been at (1 - (i-1), 0)? That is (2 - i, 0). Then, for example, at time t = -1 (for part2: i=2, so t=-1), head would be at (2-2,0) = (0,0). But that doesn't match because the head was at (1,0) at time0 and we don't have a time before that.

Alternatively, we can store the entire history of the head from time0 onward, and for any time index that is negative, we compute the initial position of part i as (i,0). But actually, that's not consistent. Because after the first move, part3 is at (2,0), which is not the head's position at any time? The head was at (1,0) at time0, then (1,1) at time1. (2,0) is the head's position at time0? No, it was at (1,0). So how do we get (2,0) for part3 at time1?

I think I made a mistake. After the first move (time1), the head moves to (1,1). Then, part2 moves to the head's previous position (1,0). Then, part3 moves to part2's previous position, which was (2,0). So, the position of part3 at time1 is (2,0). But the head was at (1,0) at time0 and (1,1) at time1. So, if we look at the head's position at time (1 - (3-1)) = 1-2 = -1, we don't have that.

Alternatively, we can store the positions of the entire body for the initial state. Then, as moves occur, we update the head and then shift the body. But that's O(N) per move, which is too slow.

Another idea: instead of storing the entire history of the head, we can store the entire history of the head's moves. Then, the position of the head at any time can be computed by starting from (1,0) and applying the first t moves. But then, for each query "2 p", we would have to compute the head's position at time (t - (p-1)) by applying the moves up to that time. But worst-case, t can be 200,000, and we might have 200,000 queries, each requiring up to 200,000 moves to compute? That's O(Q * Q) which is 40e9, too slow.

We need a more efficient way.

We can precompute the head's positions for all times. Since we have up to Q (200,000) moves, we can store an array `head_pos` of size Q+1 (for time0 to timeQ). Time0 is initial: (1,0). Then, for each move, we update the head's position from the previous position and the direction.

But then, for a query "2 p" at a time when we have processed `t` moves, we want the head position at time (t - (p-1)). However, if (p-1) > t, then we need the initial position of part p, which is (p,0). But wait, after t moves, the position of part p is the initial position of part (p - t) or something? Actually, no. The propagation hasn't reached that far? 

Wait, after t moves, the head's movement has only propagated t parts down the body. So, for parts p where p-1 > t, they haven't moved from their initial positions. So, we can say:
if t < p-1:
    result = (p, 0)
else:
    result = head_pos[t - (p-1)]

But let me test with the example.

After 0 moves (t=0): 
- Query part3: p=3, t=0 -> 0 < 3-1=2 -> true, so (3,0). Correct.

After first move (t=1):
- Query part3: p=3, t=1 -> 1 < 2? true? no, 1<2 is true? Actually, 1 < 2 is true, so we use (3,0)? But in the example, after first move, part3 is (2,0). So that doesn't match.

Wait, I see the issue: at time t=1, for part3: we need to check if t < p-1 -> 1 < 2? Yes, so we return (3,0). But actually, after the first move, part3 is at (2,0). So that's incorrect.

Why? Because the movement has propagated to part3: the head moved, then part2 moved to the head's old position, then part3 moved to part2's old position. So, part3 has moved. But according to the condition, t=1 and p-1=2, so 1<2 is true, and we return (3,0). But that's not what happened.

Therefore, my condition is wrong. The propagation has reached part3. Actually, the movement of the head at time1 affects part2 at time1 and part3 at time1? Actually, in one move, the head moves, then each part moves one step. So, after one move, part2 is updated (to the head's old position) and part3 is updated to part2's old position. So, yes, part3 has moved.

But the head's history: at time0, head was at (1,0). Then, at time1, head moves to (1,1). Then, the position of part3 at time1 is the head's position at time1 - (3-1) = time1-2 = time -1, which is negative. So, we don't have that.

We need to store the initial positions of all the body parts? But that's O(N) space, which is acceptable (10^6 integers), but then how do we update? We cannot update each part for each move.

Alternative insight: the body is just a queue that follows the head. The entire body's movement is a sequence of the head's past positions. Specifically, at time t, the head is at position P_t. Then, part1 is at P_t, part2 is at P_{t-1}, part3 is at P_{t-2}, ..., part k is at P_{t - (k-1)}. 

But for times before 0, we need to define the positions. For the initial state (time0), we have:
- P_0 = (1,0)
- But what is P_{-1}? It should be (2,0) because that becomes the position of part2 at time0? Actually, no. At time0, part1 is at P0 = (1,0), part2 is at (2,0). Then, at time1, part2 moves to P0 = (1,0), part3 moves to (2,0) which is the old position of part2. But (2,0) is not in the head's history. The head never was at (2,0). 

So, we need to store the entire initial body as the "history" for times before 0. Specifically, at time0, the head is at (1,0). Then, we can define:
- For time -1, the head would have been at (2,0)
- For time -2, the head would have been at (3,0)
- ...
- For time - (N-1), the head would have been at (N,0)

Then, the position of part i at time t is the head's position at time (t - (i-1)). And for negative times, we have stored the initial positions.

So, we can do:

Precomputation:
- We know that at time0, head is at (1,0)
- For time -1, we set (2,0)
- time -2: (3,0)
- ...
- time - (i): (i+1,0)

But note, we have N parts, so the tail is part N, which would require time -(N-1). 

We can create an array (or a deque) that stores the head's positions for times from -(N-1) to the current time. But the total time steps we need to store is (N-1) initial (negative times) plus up to Q (positive times). Since N is 10^6 and Q is 200,000, total is 1.2e6, which is acceptable.

But how do we handle the moves? 

We can maintain:
- An array `history` that maps time index to (x, y). The time index ranges from `-(N-1)` to `current_time` (which starts at 0 and increases by 1 for each move).
- But the array size would be (N-1 + Q) which is about 1.2e6, which is acceptable.

Alternatively, we can use a deque. We initialize the deque with the initial body positions from part N to part1? Because the head is part1 at time0: (1,0). Then, part2 at time0 corresponds to time -1: (2,0), part3 at time0 is time -2: (3,0), ... partN is time -(N-1): (N,0). 

So, we can initialize the deque with:
- positions: [ (N,0), (N-1,0), ..., (2,0), (1,0) ]

But note: the head is at the front? Actually, we want the most recent head position at the front? Or the oldest? 

Alternatively, we can store the history in a circular buffer or a list where we prepend the initial positions. But the total size is N + Q, which is 1.2e6, acceptable.

Steps:
1. Precompute an array `hist` for time indices from `start_time = -(N-1)` to `end_time = 0` (initial state). 
   - For time = -k (where k from 0 to N-1), the head's position at time -k is (N - k, 0)? Wait, no.

   Actually, we want:
   - time = 0: head is at (1,0)
   - time = -1: we want the position for part2 at time0, which is (2,0). But we are modeling the head's history, so at time -1, the head was at (2,0)? That's arbitrary, but we can set:
        time = -i: (i+1, 0)   for i from 0 to N-1? 
   Actually, for i from 0 to N-1, time = -i: (i+1, 0) doesn't work because then at time0, we have (1,0) which is correct, at time -1: (2,0), time -2: (3,0), ... time -(N-1): (N,0). That's perfect.

   So, we can initialize a list `hist` of size N: 
        hist[0] = (1,0)   # time0
        but we also need negative times: 
        for i in range(1, N): 
            time = -i: position = (i+1, 0)

   Actually, we can store the positions in a list, and we'll index by time offset. But we can store the entire history in a deque that has the positions from time = -(N-1) to time0.

   Specifically, the deque should have:
        leftmost element: time = -(N-1) -> (N,0)
        then time = -(N-2) -> (N-1,0)
        ...
        time = -1 -> (2,0)
        time = 0 -> (1,0)

   So, we can initialize:

        from collections import deque
        hist = deque()
        for i in range(N, 0, -1):
            hist.append( (i, 0) )

   But this would have:
        hist[0] = (N,0)   # which is time -(N-1) 
        hist[1] = (N-1,0) # time -(N-2)
        ...
        hist[N-1] = (1,0) # time0

   Actually, no: if we append from N down to 1, then the last element is (1,0). Then, when we add a new move, we will push to the right? 

   How do we use it?
   For a move at time1: 
        current head is at hist[-1] = (1,0)
        move in direction C: so new head = (1,0) + (dx,dy) 
        then, we push the new head to the right: hist.append(new_head)
        and then we pop from the left? Why? Because the history for the oldest time (time -(N-1)) is no longer needed? Actually, no: because the body has length N, so we only need the last N head positions. 

   Why? Because the part N (the tail) is at the head position from time (current_time - (N-1)). So, we need to store the last N head positions. 

   But note: the total history we need is the last N positions. Because at time t, we only need positions from time (t - (N-1)) to time t. 

   So, we can maintain a deque (or circular buffer) of fixed size N. 

   Steps:
        Initialize:
            hist = deque(maxlen=N)   # but we need to initialize with the initial body?
            Actually, we start with the initial body: we have N positions: from time -(N-1) to time0. So, the deque has exactly N elements.

        Then, for each move query:
            current_head = hist[-1]   # last element is the head at the current time (which is the last time we recorded)
            move head to new position = current_head + (dx,dy)
            append the new head to the deque. Since the deque has maxlen=N, it will automatically pop the leftmost element (the oldest).

        Then, for a query "2 p": 
            the part p is at position = hist[- (p)] ??? 

        How do we index? 

        In the deque, the rightmost element (index -1) is the head at current time t.
        Then, the element to the left of that (index -2) is the head at time t-1.
        Then, index -3: time t-2, and so on.

        For part p, we need the head at time (t - (p-1)). Since the deque stores from time (t - (N-1)) to time t (with t being the latest), we can access the element at index = - (p) ? 

        Example: part1 (head): we want time t, which is at index -1.
        part2: we want time t-1, which is at index -2.
        part3: time t-2, at index -3.

        So, in general, the position for part p is at index -p in the deque.

        But note: the deque has size N. The indices are from 0 to N-1, and we can use negative indexing.

        Example: 
            N=5, then initially, the deque has:
                positions: [ (5,0), (4,0), (3,0), (2,0), (1,0) ]
                indices:    0: (5,0), 1: (4,0), 2: (3,0), 3: (2,0), 4: (1,0)   # if we use list, but deque also allows negative indices.

            Negative indices: 
                -1: (1,0)
                -2: (2,0)
                -3: (3,0)
                -4: (4,0)
                -5: (5,0)

            Now, for part1 (p=1): index -1 -> (1,0) -> correct.
            part2: p=2 -> index -2 -> (2,0) -> correct? But at time0, part2 should be at (2,0). Correct.

            After one move: 
                move 'U': head moves from (1,0) to (1,1). 
                We append (1,1) to the deque. Since maxlen=5, we pop the leftmost (5,0). 
                Now, deque: [ (4,0), (3,0), (2,0), (1,0), (1,1) ]

            Now, indices:
                0: (4,0)
                1: (3,0)
                2: (2,0)
                3: (1,0)
                4: (1,1)
            Negative indices:
                -1: (1,1)
                -2: (1,0)
                -3: (2,0)
                -4: (3,0)
                -5: (4,0)   # but we popped 5,0, so now the leftmost is 4,0.

            Now, query part3: p=3 -> index -3 -> (2,0). Correct? In the example, after first move, part3 is at (2,0). Correct.

            Then, move 'R': head moves from (1,1) to (2,1). Append (2,1). Deque becomes: [ (3,0), (2,0), (1,0), (1,1), (2,1) ]

            Query part3: p=3 -> index -3 -> (1,0). But in the example, after second move, part3 is at (1,0). Correct.

            Then, move 'D': head moves from (2,1) to (2,0). Append (2,0). Deque: [ (2,0), (1,0), (1,1), (2,1), (2,0) ]

            Query part3: index -3 -> (1,1). Correct? In the example, after three moves, part3 is at (1,1). Correct.

            Then, move 'L': head from (2,0) to (1,0). Append (1,0). Deque: [ (1,0), (1,1), (2,1), (2,0), (1,0) ]

            Query part1: p=1 -> index -1 -> (1,0). Correct.
            Query part5: p=5 -> index -5 -> (1,0). In the example, output (1,0). Correct.

        So, this works.

        Steps for the algorithm:

        Precomputation:
            Initialize a deque with maxlen = N.
            For i from N down to 1: 
                append (i, 0)   -> so the deque has: [ (N,0), (N-1,0), ..., (1,0) ]

        Then, let t = 0 (number of moves processed so far? actually, we don't need t explicitly for the deque, but we can process each query).

        For each query:
            If it's "1 C":
                current_head = hist[-1]   # the last element in deque
                dx, dy = direction_map[C]   # e.g., 'R' -> (1,0), 'L' -> (-1,0), 'U' -> (0,1), 'D' -> (0,-1)
                new_head = (current_head[0] + dx, current_head[1] + dy)
                hist.append(new_head)   # automatically pops from the left if size > N

            If it's "2 p":
                # we need the element at index = -p (0-indexed from the right, so the last is -1, then -2, ...)
                pos = hist[-p]   # because part1 is at -1, part2 at -2, etc.
                output f"{pos[0]} {pos[1]}"

        However, note: the deque in Python: if we have a deque of size N, then the valid indices are from 0 to N-1 (positive) and from -1 to -N (negative). So, for part p, we use index -p. But if p=1, -1 is the last element. p=2, -2 is the second last. And p must be between 1 and N.

        Example: for N=5, p=5 -> index -5: which is the first element (if we think as a list). But in the example above, after the moves, when we appended, the leftmost element was the oldest. And we use negative indexing: -1: last, -2: second last, ... -5: first element.

        This matches.

        But note: when we initialize the deque with [ (5,0), (4,0), (3,0), (2,0), (1,0) ], then:
            hist[0] = (5,0), hist[1] = (4,0), ... hist[4] = (1,0)
            hist[-1] = (1,0)
            hist[-2] = (2,0)
            hist[-5] = (5,0)

        So, yes, part p: use hist[-p].

        However, after we append a new head, the deque changes. But the negative indexing still works: the last element is the most recent head, then the one before is the previous head, etc.

        So, the algorithm:

        from collections import deque

        # Directions mapping
        dirs = {
            'R': (1, 0),
            'L': (-1, 0),
            'U': (0, 1),
            'D': (0, -1)
        }

        Initialize:
            hist = deque(maxlen=N)
            for i in range(N, 0, -1):
                hist.append( (i, 0) )   # so the last element is (1,0) at the start

        Then, for each query:
            if query[0] == '1':
                c = query[2]  # actually, the query string: "1 C", so split? 
                # Alternatively, we can split the string.
                parts = query.split()
                if parts[0]=='1':
                    c = parts[1]
                    dx, dy = dirs[c]
                    x, y = hist[-1]   # last element
                    new_head = (x+dx, y+dy)
                    hist.append(new_head)
            else: # '2'
                parts = query.split()
                p = int(parts[1])
                # get the position at index -p
                # Note: p is between 1 and N
                pos = hist[-p]
                # store this output

        But note: the deque might have less than N elements? Actually, no, because we initialize with N, and then each append keeps the size as N (because maxlen=N). So, we always have N elements.

        However, initially we have N elements. Then, when we append, we pop the leftmost. So, the size remains N.

        Therefore, the negative index -p is always valid because p is between 1 and N.

        But what if we haven't done any moves? Then, for part p: hist[-p] is (p,0)? 

        Example: N=5, p=3: hist[-3] = (3,0). Correct.

        So, this should work.

        Let me test with the example step by step.

        Example: N=5, Q=9
        Initialize: hist = deque([ (5,0), (4,0), (3,0), (2,0), (1,0) ], maxlen=5)

        Query1: "2 3" -> output hist[-3] = (3,0) -> "3 0" -> correct.

        Query2: "1 U": 
            current_head = hist[-1] = (1,0)
            new_head = (1,0) + (0,1) = (1,1)
            append: now hist = [ (4,0), (3,0), (2,0), (1,0), (1,1) ]

        Query3: "2 3": 
            hist[-3] = (2,0) -> output "2 0" -> correct.

        Query4: "1 R":
            current_head = (1,1)
            new_head = (1+1,1) = (2,1)
            append: hist = [ (3,0), (2,0), (1,0), (1,1), (2,1) ]

        Query5: "1 D":
            current_head = (2,1)
            new_head = (2,1-1) = (2,0)
            append: hist = [ (2,0), (1,0), (1,1), (2,1), (2,0) ]

        Query6: "2 3":
            hist[-3] = (1,1) -> output "1 1" -> correct.

        Query7: "1 L":
            current_head = (2,0)
            new_head = (2-1,0) = (1,0)
            append: hist = [ (1,0), (1,1), (2,1), (2,0), (1,0) ]

        Query8: "2 1":
            hist[-1] = (1,0) -> output "1 0" -> correct.

        Query9: "2 5":
            hist[-5] = (1,0) -> output "1 0" -> correct.

        Perfect.

        Time complexity: 
            Initialization: O(N)
            Each move: O(1) (append and pop from deque is O(1))
            Each query type 2: O(1) (indexing in deque is O(1))

            Total: O(N + Q)

        Space: O(N) for the deque.

        This meets the constraints.

        Edge Cases:
            - N=2: 
                Initialize: deque = [ (2,0), (1,0) ]
                Move: say "1 U": then deque becomes [ (1,0), (1,1) ]
                Then, part1: hist[-1] = (1,1)
                part2: hist[-2] = (1,0)

            - Query type2 for p=1 and p=N.

        So, we can implement accordingly.

        Code Implementation:

        We are given input as:
            first line: "N Q"
            then Q lines of queries.

        We need to output the answers for each type2 query in order.

        Steps:

        Read N, Q.
        Initialize deque with (i,0) for i from N down to 1.

        For each of the Q queries:
            if the first token is '1', then get the direction, compute new head, append to deque.
            else, get p, get the position at -p, and store it to output later.

        Then, output all the stored answers (each on a new line, as "x y")

        Note: we have to output the answers in the order of the queries.

        Let me write the code accordingly.

        However, note: the problem input format: each query is a string. For example, "1 U", "2 3".

        We can split each string.

        Example: 
            queries = [
                "2 3",
                "1 U",
                "2 3",
                ...
            ]

        But the first line is "N Q", then the rest are queries.

        So, we can do:

            import sys
            from collections import deque

            data = sys.stdin.read().splitlines()
            first_line = data[0].split()
            N = int(first_line[0])
            Q = int(first_line[1])

            queries = data[1:1+Q]

        Then, initialize the deque.

        Directions mapping.

        Then, results = []
        for each query in queries:
            parts = query.split()
            if parts[0] == '1':
                c = parts[1]
                dx, dy = dirs[c]
                x, y = hist[-1]
                new_head = (x+dx, y+dy)
                hist.append(new_head)
            else: # parts[0]=='2'
                p = int(parts[1])
                x, y = hist[-p]
                results.append(f"{x} {y}")

        Then, output each result.

        But note: the deque is initialized with the initial body: from N to 1.

        Let me test with N=2, Q=2: 
            queries: 
                "2 1"
                "2 2"

            hist = deque([(2,0), (1,0)], maxlen=2)
            Query "2 1": hist[-1] = (1,0) -> output "1 0"
            Query "2 2": hist[-2] = (2,0) -> output "2 0"

        Correct.

        Another test: N=1? But N>=2 per constraints. So, no.

        This should be the optimal solution.

        Why is it optimal?
            Time: O(N + Q) which is linear in the input size (N up to 1e6, Q up to 200k -> 1.2e6 operations, acceptable).
            Space: O(N) for the deque.

        Alternative solutions? 
            We could use a circular array instead of deque. But deque in Python is efficient for appending and popping from both ends. However, for fixed size, we can use a list and manage two pointers. But the deque in Python is implemented with a doubly linked list? Actually, for fixed maxlen, it's efficient.

        Since we are only appending to the right and popping from the left, and we don't need random access? Actually, we do: we need to access any index from 0 to N-1 (via negative indexing). But in Python, accessing a deque by index is O(1) because it's implemented as a doubly linked list of blocks? Actually, the documentation says: "Deques support thread-safe, memory efficient appends and pops from either side of the deque with approximately the same O(1) performance in either direction. Though list objects support similar operations, they are optimized for fast fixed-length operations and incur O(n) memory movement costs for pop(0) and insert(0, v) operations which change both the size and position of the underlying data representation."

        And for indexing: "Indexed access is O(1) at both ends but slows to O(n) in the middle. For fast random access, use lists instead."

        But in our case, we are accessing by negative index: for part p, we access the element at position (current_length - p). Since p can be up to 10^6, and if we access in the middle, it might be O(n). But wait, the deque is implemented in a way that we can access by index, but it's O(n) in the worst case? 

        Actually, the deque in Python is implemented as a doubly linked list of fixed-size blocks. So, indexing might be O(n) in the worst-case? 

        How can we avoid that?

        We can use a circular buffer (a list of size N, and two pointers: start and current_end). But then, we have to manage modulo arithmetic.

        Alternatively, we can use a list `arr` of length N, and an integer `start` that points to the beginning of the valid data. Initially, start=0, and the list is [ (N,0), (N-1,0), ... (1,0) ].

        Then, when we append a new head, we do:
            arr[start] = new_head   # overwrite the oldest element
            start = (start+1) % N   # the next element becomes the new start? Actually, we want to maintain the entire buffer as a circular queue.

        But then, the positions are stored in a fixed-size circular array. The current head is at index (start-1) mod N? 

        Actually, we can maintain a pointer `end` that points to the next available slot? 

        Alternatively, we can store the last N elements. The newest element is at index (start) mod N? 

        Let me define:
            We have a list `arr` of length N.
            We have two pointers: `head_index` (the current head) and `tail_index`? Actually, we only need one pointer: `start` (the beginning of the queue) and then we know the elements are stored from `start` to `start + N - 1` modulo N.

        But note: we are always adding one element and removing one element? Actually, we are replacing the oldest element.

        Steps:
            Initialize:
                arr = [0]*N
                # We want to store the initial history: for time from -(N-1) to 0: 
                # time0: (1,0) -> stored at index0? 
                # Actually, we can store the initial body in the array: 
                # Let index0: time0 -> (1,0)
                # index1: time -1 -> (2,0)
                # ...
                # index N-1: time -(N-1) -> (N,0)

                But we want the newest element (time0) to be at a known position, and then when we add a new move, we overwrite the oldest (which is at time -(N-1)).

            We can use:
                arr = [ (i+1,0) for i in range(N) ]   # but then index0: (1,0), index1: (2,0), ... index N-1: (N,0) -> this is the initial history? But we need the time0 to be the head? 

            Actually, we want the head at time0 to be at index? It should be the most recent. Then, when we add a new head, we overwrite the oldest.

            We can maintain a pointer `cur` that points to the current head index. Initially, `cur = 0`? 

            Then, the sequence of times: 
                time0: index0
                time-1: index1
                time-2: index2
                ...
                time-(N-1): index N-1.

            Then, for a move:
                new_head = current_head + move
                Then, we want to store the new_head at the position of the oldest time? which is time-(N-1) (but we are going to time1). Actually, we are moving to time1, and the oldest time we need to remember is time1 - (N-1) = 1 - (N-1) = 2-N. The oldest time we stored was time-(N-1). Now, we want to store time1 at the position of time-(N-1), and then shift the time axis.

            Actually, we can store the history in a circular array without keeping the absolute time. We just need the last N positions. The current head is at the most recent index. The previous head is at the next index (in the circular array). 

            We can have:
                arr = [0]*N
                # Initialize: the initial body: 
                #   For i in range(N):
                #       arr[i] = (N-i, 0)   # so that arr[0] = (N,0), arr[1] = (N-1,0), ... arr[N-1] = (1,0)
                #   Then, the current head is at arr[N-1] = (1,0)

                Now, we maintain an index `pos` that points to the current head. Initially, `pos = N-1`.

            Then, for a move:
                new_head = current_head (arr[pos]) + move
                Then, we want to store the new_head at the position that is next in the circular array? Actually, we want to overwrite the oldest element. The oldest element is at (pos+1) mod N? 

            Actually, the array is arranged as a circular queue. The next position to write is (pos+1) mod N? 

            Then, we set:
                pos = (pos+1) % N
                arr[pos] = new_head

            Then, the array now has the new_head at index pos, and the previous heads are in the next N-1 positions (going backwards).

            How do we access the head at time t - k? 

            The current head is at pos.
            The previous head is at (pos-1) mod N? 
            But wait: 
                Initially: 
                    arr = [ (N,0), (N-1,0), ... (1,0) ]   # indices 0,1,...,N-1
                    pos = N-1 (which is the last index)

                Then, first move: 
                    new_head = (1,0) + (0,1) = (1,1)
                    pos = (N-1+1) mod N = 0
                    arr[0] = (1,1)

                Now, the array is: 
                    [ (1,1), (N-1,0), ... (1,0) ]? Actually, we overwrote the first element.

                Then, for part1: head is at arr[0] -> (1,1)
                part2: should be the head at time0: which was (1,0). How do we get that? 
                    It is at the previous head: which is at pos-1? (0-1 mod N) = N-1 -> arr[N-1] = (1,0). Correct.

                Then, part3: head at time-1: which is (2,0). How? 
                    We need to go back two steps: from the current head (at pos=0), we go back two: (0-2) mod N = (N-2) -> arr[N-2] = (2,0)? 

                But in the initial array, at index N-2 we stored (2,0). Correct.

                So, the position for part p is at (pos - (p-1)) mod N? But then we have to do modulo arithmetic.

                Specifically: 
                    index = (pos - (p-1)) % N   # but if negative, modulo will make it positive.

                Example: 
                    After first move: 
                        part1: (0 - 0) % N = 0 -> arr[0] = (1,1) -> correct.
                        part2: (0-1) %5 (if N=5) = 4 -> arr[4] = (1,0) -> correct.
                        part3: (0-2) %5 = 3 -> arr[3] = (2,0) -> correct.

                But wait: in the example above (N=5, after first move), we had:
                    arr[0] = (1,1)
                    arr[1] = (4,0)   # but wait, we overwrote the first element? 
                    Actually, we initialized as:
                        arr[0] = (5,0)
                        arr[1] = (4,0)
                        arr[2] = (3,0)
                        arr[3] = (2,0)
                        arr[4] = (1,0)
                    Then, after first move, we set:
                        pos = (4+1)%5 = 0
                        arr[0] = (1,1)

                    So the array becomes:
                        arr[0] = (1,1)
                        arr[1] = (4,0)
                        arr[2] = (3,0)
                        arr[3] = (2,0)
                        arr[4] = (1,0)

                Then, part3: p=3 -> index = (0 - 2) %5 = ( -2 %5 ) = 3? 
                    arr[3] = (2,0) -> correct.

            So, we can also implement with a circular array and a pointer.

            Steps:
                arr = [ (i,0) for i in range(N,0,-1) ]   # arr[0]=N, arr[1]=N-1, ... arr[N-1]=1? 
                But wait, we want the initial head (which is at time0) to be at the last element? 

                Actually, we can initialize the array with the initial body from time0 to time-(N-1) in reverse? 

                Alternatively, we can initialize:
                    for i in range(N):
                        arr[i] = (N - i, 0)   # so arr[0]=N, arr[1]=N-1, ... arr[N-1]=1 -> but then the head (time0) is at arr[N-1]=1.

                Then, set pos = N-1.

                Then, for a move:
                    new_head = (x,y) of current head (arr[pos]) + move
                    pos = (pos+1) % N
                    arr[pos] = new_head

                For a query "2 p":
                    index = (pos - (p-1)) % N
                    But if negative, modulo arithmetic will handle it? 
                    Example: pos=0, p=2: index = (0-1) % N = ( -1 % N) = N-1 -> arr[N-1] = 1 -> but that is the head at time0? But we want the head at time0 for part2? Actually, part2 at the current time (after first move) is the head at time0, which is at arr[old pos]? 

                Actually, after the move, we updated pos to 0, and stored the new head at arr[0]. The head at time0 is stored at the old pos? which is N-1? 

                How do we get the head at time0? 
                    We stored it at arr[N-1] initially. Then, we moved pos to 0 and stored the new head at arr[0]. The head at time0 is now at index (0-1) mod N = N-1? 
                    And arr[N-1] is 1? But we want (1,0) for the head at time0. Correct.

                So, the formula: index = (pos - (p-1)) % N   gives:
                    for p=2: (0 -1) %5 = 4 -> arr[4] = (1,0) -> correct.

                But note: the array was initialized as:
                    arr[0]=5, arr[1]=4, arr[2]=3, arr[3]=2, arr[4]=1
                Then, after first move (U):
                    new_head = (1,0)+(0,1)=(1,1)
                    pos = (4+1)%5 = 0
                    arr[0] = (1,1)

                Now, the array: 
                    arr[0] = (1,1)
                    arr[1] = 4
                    arr[2] = 3
                    arr[3] = 2
                    arr[4] = 1   # but this is (1,0)? No, wait, the initial arr[4] was (1,0). Then, we did not change it.

                Then, for part2: index = (0-1)%5=4 -> arr[4]=(1,0) -> correct.

            So, this also works.

            But note: the modulo operation for negative: 
                (0-1) %5 = (-1)%5 = 4, which is correct.

            Therefore, we can do:

                arr = [0]*N
                for i in range(N):
                    arr[i] = (N-i, 0)   # so arr[0] = (N,0), arr[1]=(N-1,0), ... arr[N-1]=(1,0)
                pos = N-1   # because the head is at the last element.

                Then, for each move:
                    dx,dy = dirs[c]
                    x,y = arr[pos]
                    new_head = (x+dx, y+dy)
                    pos = (pos+1) % N
                    arr[pos] = new_head

                For query "2 p":
                    idx = (pos - (p-1)) % N
                    # but note: if (pos - (p-1)) is negative, modulo makes it positive.
                    x, y = arr[idx]
                    results.append(f"{x} {y}")

            Why is this better? 
                Because array indexing is O(1) and doesn't have the potential overhead of deque's block structure.

            But in practice, for N=1e6, the deque might be efficient enough? Or the circular array might be faster? 

            However, the problem says constraints up to 10^6 and Q up to 200k, so both are acceptable.

            But the deque solution is simpler and more readable. However, if we are concerned about the O(n) for indexing in the middle of the deque, we should use the circular array.

            According to Python documentation: for deque, while appends and pops from the ends are efficient, accessing elements by index is O(n) in the worst case. But in practice, for the worst-case index (the middle), it would be about n/2. With n=1e6, that would be 500k operations per query. And we have up to 200k queries? Then worst-case total operations 200k * 500k = 100e9, which is too slow.

            So, we cannot use the deque if we are accessing by index arbitrarily. 

            Therefore, we must use the circular array.

            Alternatively, we can use a list as a circular buffer with modulo indexing. 

            Steps for circular array:

                arr = [0] * N   # we'll use this as fixed-size circular buffer
                # Initialize: we want the initial body: 
                #   time0: (1,0) -> stored at index0? 
                #   time-1: (2,0) -> index1?
                #   ... 
                #   time-(N-1): (N,0) -> index N-1?
                # But then, the next time we write, we write at index0? and then move to index1? 

                Actually, we want to store the history in the order from oldest to newest? 

            Alternatively, we can do as above: 
                for i in range(N):
                    arr[i] = (N - i, 0)   # so arr[0] = (N,0) [oldest], arr[1]=(N-1,0), ... arr[N-1]=(1,0) [newest]
                pos = N-1   # the current head is at index N-1.

            Then, when we do a move, we update:
                next_index = (pos+1) % N
                new_head = (arr[pos][0] + dx, arr[pos][1] + dy)
                arr[next_index] = new_head
                pos = next_index

            Then, for part p: we need the head at time (current_time - (p-1)). 
            The current_time is the number of moves we have done? Actually, we don't need to store it. 

            The history we have in the array: 
                The current head is at pos (which is the latest).
                The head one step before is at (pos-1) mod N? 
                Two steps: (pos-2) mod N.

            So, the index for the head at k steps ago: (pos - k) mod N.

            For part p, k = p-1.

            Therefore, index = (pos - (p-1)) % N.

            But note: modulo arithmetic: if negative, we add N until positive? Actually, in Python, the modulo operator % for negative numbers returns a non-negative result. So, (pos - (p-1)) % N is safe.

            Example: N=5, pos=0, p=2: (0-1) %5 = 4 -> correct.

            So, we'll implement the circular array.

        Code:

            N, Q = map(int, first_line)
            arr = [(0,0)] * N
            # Initialize: 
            for i in range(N):
                # arr[i] = (N - i, 0)
                arr[i] = (N - i, 0)

            pos = N - 1   # current head is at the last index of the array

            results = []
            for each query in queries:
                parts = query.split()
                if parts[0]=='1':
                    c = parts[1]
                    dx,dy = dirs[c]
                    x,y = arr[pos]
                    new_head = (x+dx, y+dy)
                    pos = (pos+1) % N
                    arr[pos] = new_head
                else:
                    p = int(parts[1])
                    # k = p-1
                    idx = (pos - (p-1)) % N
                    # if negative, modulo makes it positive
                    x,y = arr[idx]
                    results.append(f"{x} {y}")

        Let me test with the example: N=5, Q=9.

        Initialize:
            arr = [ (5,0), (4,0), (3,0), (2,0), (1,0) ]
            pos = 4

        Query1: "2 3" -> p=3, k=2, idx = (4-2)%5 = 2 -> arr[2] = (3,0) -> correct.

        Query2: "1 U": 
            c='U' -> (0,1)
            current_head = arr[4] = (1,0) -> new_head = (1,1)
            pos = (4+1)%5 = 0
            arr[0] = (1,1)   # so now arr = [ (1,1), (4,0), (3,0), (2,0), (1,0) ]

        Query3: "2 3" -> p=3, k=2, idx = (0-2)%5 = ( -2 %5) = 3? -> arr[3] = (2,0) -> output "2 0" -> correct.

        Query4: "1 R": 
            current_head = arr[0] = (1,1) -> new_head = (2,1)
            pos = (0+1)%5 = 1
            arr[1] = (2,1)   # arr = [ (1,1), (2,1), (3,0), (2,0), (1,0) ]

        Query5: "1 D":
            current_head = arr[1] = (2,1) -> new_head = (2,0)
            pos = (1+1)%5 = 2
            arr[2] = (2,0)   # arr = [ (1,1), (2,1), (2,0), (2,0), (1,0) ]? Wait, no: we set arr[2] to (2,0). So:
                index0: (1,1)
                index1: (2,1)
                index2: (2,0)
                index3: (2,0)   # but originally was (2,0) from the initial? Actually, we are overwriting. 
                index4: (1,0)

        Query6: "2 3": p=3, k=2, idx = (2-2)%5 = 0 -> arr[0]=(1,1) -> output "1 1" -> correct.

        Query7: "1 L":
            current_head = arr[2] = (2,0) -> new_head = (1,0)
            pos = (2+1)%5 = 3
            arr[3] = (1,0)   # so arr[3] becomes (1,0)

        Query8: "2 1": p=1, k=0, idx = (3-0)%5=3 -> arr[3]=(1,0) -> output "1 0"

        Query9: "2 5": p=5, k=4, idx = (3-4)%5 = (-1)%5=4 -> arr[4]=(1,0) -> output "1 0"

        Correct.

        Therefore, we'll implement with the circular array.

        This is efficient: 
            Time: O(N + Q) 
            Space: O(N)

        We'll write the code accordingly.

        Note: we are storing a tuple for each position. The total memory is about 2 integers per element, so 2*4*N bytes? For N=10^6, that's 8e6 bytes = 8 MB, acceptable.

        Now, code implementation.

        Let me write the function accordingly.

        We are given: 
            N, Q, and a list of Q queries (strings)

        We return a list of strings for the answers.

        Steps:

            dirs = {'R': (1,0), 'L': (-1,0), 'U': (0,1), 'D': (0,-1)}

            arr = [ (i, 0) for i in range(N, 0, -1) ] 
            # But this creates a list: [N, N-1, ... 1] -> which is (N,0), (N-1,0), ... (1,0). 
            # Actually, we can do: 
            #   arr = []
            #   for i in range(N, 0, -1):
            #       arr.append( (i,0) )
            #   But then we have a list of N elements.

            Alternatively, we can do:

                arr = [(0,0)] * N
                for i in range(N):
                    arr[i] = (N - i, 0)

            But note: N-i for i in [0, N-1] gives N, N-1, ... 1. Correct.

            Then, pos = N-1.

            results = []
            for each query in queries:
                if it starts with '1', process move
                else, process query type2

            Then return results.

        Let me test with the example: N=5, then arr = [ (5,0), (4,0), (3,0), (2,0), (1,0) ]

        Then, proceed as above.

        I think this is the solution.

        We'll implement accordingly.

        Edge: N=2, as above, works.

        Let me test with the provided example.

        Code:

        class Solution:
            def solve(self, N: int, Q: int, queries: List[str]) -> List[str]:
                from typing import List
                # If not imported, we can assume it's given.

                dirs = {
                    'R': (1, 0),
                    'L': (-1, 0),
                    'U': (0, 1),
                    'D': (0, -1)
                }
                # Create a circular buffer of size N
                arr = [(0,0)] * N
                # Initialize with the initial body: from part N to part1: so positions (N,0), (N-1,0), ... (1,0)
                # But we store in the array: index0: (N,0), index1: (N-1,0), ... index N-1: (1,0)
                for i in range(N):
                    arr[i] = (N - i, 0)
                pos = N - 1   # the head is at the last index initially

                results = []
                for query in queries:
                    parts = query.split()
                    if parts[0] == '1':
                        c = parts[1]
                        dx, dy = dirs[c]
                        x, y = arr[pos]
                        new_head = (x + dx, y + dy)
                        pos = (pos + 1) % N
                        arr[pos] = new_head
                    else:  # parts[0] == '2'
                        p = int(parts[1])
                        # k = p-1
                        idx = (pos - (p-1)) % N
                        x, y = arr[idx]
                        results.append(f"{x} {y}")
                return results

        Let me run the example queries:

        queries = [
            "2 3", 
            "1 U", 
            "2 3", 
            "1 R", 
            "1 D", 
            "2 3", 
            "1 L", 
            "2 1", 
            "2 5"
        ]

        Step-by-step:

        Initialize: arr = [ (5,0), (4,0), (3,0), (2,0), (1,0) ], pos=4.

        Query0: "2 3" -> p=3, idx = (4 - 2) %5 = 2 -> arr[2] = (3,0) -> "3 0"

        Query1: "1 U" -> new_head = (1,0)+(0,1)=(1,1); pos = (4+1)%5=0; arr[0]=(1,1) -> now arr[0] updated to (1,1), others unchanged: [ (1,1), (4,0), (3,0), (2,0), (1,0) ]

        Query2: "2 3" -> p=3, idx = (0-2)%5 = 3 -> arr[3] = (2,0) -> "2 0"

        Query3: "1 R" -> current_head = arr[0]=(1,1); new_head=(2,1); pos=(0+1)%5=1; arr[1]=(2,1) -> arr: [ (1,1), (2,1), (3,0), (2,0), (1,0) ]

        Query4: "1 D" -> current_head = arr[1]=(2,1); new_head=(2,0); pos=(1+1)%5=2; arr[2]=(2,0) -> arr: [ (1,1), (2,1), (2,0), (2,0), (1,0) ]

        Query5: "2 3" -> p=3, idx = (2-2)%5=0 -> arr[0]=(1,1) -> "1 1"

        Query6: "1 L" -> current_head = arr[2]=(2,0); new_head=(1,0); pos=(2+1)%5=3; arr[3]=(1,0) -> arr: [ (1,1), (2,1), (2,0), (1,0), (1,0) ]? 
            Actually, we set arr[3] to (1,0). So index3: (1,0)

        Query7: "2 1" -> p=1, idx = (3-0)%5=3 -> arr[3]=(1,0) -> "1 0"

        Query8: "2 5" -> p=5, idx = (3-4)%5 = ( -1 %5) = 4 -> arr[4]=(1,0) -> "1 0"

        Output: ["3 0", "2 0", "1 1", "1 0", "1 0"] -> matches the example.

        But the example output is:

            3 0
            2 0
            1 1
            1 0
            1 0

        However, the example input has 9 queries, and the output has 5 lines (for the 5 type2 queries). The example output in the problem is:

            3 0
            2 0
            1 1
            1 0
            1 0

        But the example input output:

            Input: 
                5 9
                2 3
                1 U
                2 3
                1 R
                1 D
                2 3
                1 L
                2 1
                2 5

            Output:
                3 0
                2 0
                1 1
                1 0
                1 0

        So, the first output is "3 0", then "2 0", then "1 1", then "1 0", then "1 0". 

        Our results list: 
            after query0: "3 0"
            after query2: "2 0"
            after query5: "1 1"
            after query7: "1 0"
            after query8: "1 0"

        So, the order is correct.

        Therefore, we'll implement this.

        Let me test with a small example: N=2, Q=3, queries: 
            ["1 U", "2 1", "2 2"]

        Steps:
            Initialize: arr = [ (2,0), (1,0) ] -> but by our initialization: 
                for i in range(2): 
                    i=0: arr[0]= (2-0,0) = (2,0)
                    i=1: arr[1]= (2-1,0) = (1,0)
                pos=1

            Query0: "1 U": 
                current_head = arr[1] = (1,0) -> new_head = (1,1)
                pos = (1+1)%2 = 0
                arr[0] = (1,1)   # now arr = [ (1,1), (1,0) ]? Actually, we overwrote index0: was (2,0) -> now (1,1)

            Query1: "2 1": p=1 -> idx = (0-0)%2=0 -> arr[0]=(1,1) -> "1 1"
            Query2: "2 2": p=2 -> idx = (0-1)%2 = (-1)%2=1 -> arr[1]=(1,0) -> "1 0"

        Correct.

       

## Final Solution

