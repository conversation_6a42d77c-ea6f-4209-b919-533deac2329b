# Solution for abc329_c.md

Generated at: 2025-07-22 06:35:31
Model: deepseek-reasoner

## Reasoning Process

Alright, I'm going to tackle this problem step by step. Let me start by understanding what the problem is asking.

The problem is to find the number of non-empty substrings of a given string S that consist of repetitions of a single character. Importantly, even if there are multiple ways to obtain the same substring (like "a" appearing in different places), we count each distinct substring only once. 

For example, in the string "aaabaa", the valid substrings are "a", "aa", "aaa", and "b". Even though "a" appears multiple times, we count it only once. So the output is 4.

Constraints: The string length N can be up to 200,000. So, a brute force approach that enumerates all substrings (which would be O(N^2)) is not feasible.

First, I need to model the problem. The key observation is that a substring of repeated characters is defined by the character and its length. For instance, "aaa" is different from "aa" and "a". However, if the same substring (like "a") appears in different parts of the string, it's still the same substring and should be counted only once.

Wait, but actually, the problem says: "two substrings that are equal as strings are not distinguished even if they are obtained differently." So, we are counting distinct substring values, not distinct occurrences. Therefore, the set of substrings we are interested in is the set of all strings of the form c^k (c repeated k times) for some character c and k>=1, that appear as a contiguous substring in S.

But note: The same substring value (like "a") might appear multiple times in the string, but we count it only once. So, the problem reduces to: How many distinct strings of the form c^k (for any character c and integer k>=1) are contiguous substrings of S?

However, here's a catch: The distinctness is by the actual string. So, "a" and "b" are different, but two "a"s of the same length are the same. So, for each distinct character, we have multiple possible substrings: "a", "aa", "aaa", etc. But we only count a particular string if it appears at least once as a contiguous substring in S.

But note: If the string has "aaa", then it automatically has "a" and "aa" as well? Actually, no. Because if we have "aaa", then the substring "a" is contained within it, and so is "aa". However, the problem says that we are to count distinct substrings that are repetitions of one character. So, each distinct string (like "a", "aa", etc.) is a separate substring.

But then, in the example "aaabaa", why are we counting 4? The valid substrings are "a", "aa", "aaa", and "b". So, for the character 'a', we have three: "a", "aa", "aaa". And for 'b', one: "b". 

But note: Could we have "bb"? The string doesn't have consecutive 'b's, so no. Similarly, the last part has "aa", but that's already counted as "a" and "aa" (which are the same as the ones from the first part). So, we don't count them again.

So, the problem is: For each character c, what are the distinct lengths k (>=1) for which the substring c^k (c repeated k times) appears in S? Then, the answer is the sum over all characters of the number of distinct k for which c^k appears.

But wait: Actually, for a fixed character c, the distinct substrings of c's that appear are determined by the maximum consecutive occurrences of c. For example, if the string has a run of 3 consecutive 'a's, then we have the substrings "a", "aa", "aaa". So, for a run of length L of character c, we get L distinct substrings: c^1, c^2, ..., c^L.

However, if the same character c has multiple runs, then the distinct substrings for c are determined by the maximum run length in each run? Actually, no. Consider: if we have two runs of 'a': one of length 2 and one of length 3, then the distinct substrings for 'a' are: "a", "aa", "aaa". Because the run of 3 gives all three, and the run of 2 only adds "a" and "aa", which are already covered by the run of 3. So, for the entire string, for a given character c, the distinct substrings of c's that appear are exactly the ones from 1 up to the maximum run length of c in the entire string? 

Wait, that's not true. Actually, no: Consider the string "aaabaa". The runs of 'a' are: first run of 3, then a run of 2. The distinct substrings for 'a' are "a", "aa", "aaa". But the run of 2 does not add a new distinct substring that we haven't already seen from the run of 3. So, for each character c, the distinct substrings that appear are the set of all c^k for k from 1 to the maximum run length of c in the entire string? 

But wait: What if we have a character that appears in two runs that are separated, and the runs have different maximums? Actually, the distinct substrings are the set of all k such that k is less than or equal to at least one run of c. So, if we have two runs: one of length 2 and one of length 3, then k=1,2,3 are covered. Because k=3 is covered by the run of 3, and k=1,2 are covered by both. 

Therefore, for a character c, the distinct substrings of c's that appear in the entire string are exactly the integers from 1 up to the maximum run length of c that appears anywhere in the string. 

Is that correct? Let me test with an example: "aaba": 
- Runs of 'a': first run length 2, then run length 1. 
- The distinct substrings for 'a' are "a" and "aa". 
- The maximum run length of 'a' is 2. So, we get k=1 and k=2. That matches.

Another example: "ababa": 
- Runs: 'a' (length 1), then 'b' (1), then 'a' (1), then 'b' (1), then 'a' (1). 
- For 'a', the maximum run length is 1. So, only "a". 
- For 'b', maximum run length is 1. So, only "b". 
- Total distinct substrings: 2. 

But what are the distinct substrings? The only ones are "a" and "b". So, that's correct.

But wait: what about the possibility of having a substring that spans two runs? For example, can we have a substring "aaa" by taking from the first run and the last run? No, because the problem requires contiguous substrings, and between the two runs of 'a' is a 'b'. So, we cannot take a substring that skips the 'b'. 

Therefore, the contiguous substring must be entirely within one run.

So, the problem reduces to:

1. For each maximal contiguous run of the same character c of length L, we can form the substrings c^1, c^2, ..., c^L.

2. However, if the same substring (same c and same k) appears in two different runs, it is still the same substring and should be counted only once.

But note: the distinct substrings we are counting are defined by the string value. So, two runs of 'a' of length 2 both produce the substring "aa", but we count "aa" only once.

Therefore, for each character c, we don't need to know each run length, but only the maximum run length for that character in the entire string. Because if we have a run of length L for c, then we have all k from 1 to L. And if we have multiple runs, the distinct substrings for c are still only the k from 1 to the maximum L (because the maximum run already covers all k up to that maximum).

So, the answer would be: for each distinct character c that appears in the string, add the maximum run length of c. Then, the total distinct substrings are the sum of the maximum run lengths for each distinct character.

But wait: in the example "aaabaa": 
- For 'a', the maximum run is 3 -> adds 3.
- For 'b', the maximum run is 1 -> adds 1.
- Total: 4. That matches.

Example 2: "x" -> max run for 'x' is 1 -> output 1.

Example 3: "ssskkyskkkky"
Let me break it down:
- 's': run of 3 -> max 3
- 'k': first run of 2, then a run of 4 (because "kkkk")? Actually, the string is "ssskkyskkkky". 
  So, runs: 
    s: 3
    k: 2
    y: 1
    s: 1
    k: 4
    y: 1
- For 's': max run = max(3,1)=3 -> adds 3
- For 'k': max run = max(2,4)=4 -> adds 4
- For 'y': max run=1 -> adds 1
Total: 3+4+1=8. Which matches the example.

So, the solution is:
1. Traverse the string to compute the maximum run length for each distinct character.
2. Sum these maximum run lengths.

But wait: what about characters that do not appear? We only consider characters that appear at least once.

How to compute the maximum run length for each character?
- We can traverse the string and for each character, check consecutive same characters and update the current run length. Then update the maximum run length for that character.

But note: we need to do this for all 26 letters? Actually, we can do:

Initialize:
  max_run = {}  # dictionary to hold max run per char

Then, traverse the string with a pointer, and:
  current_char = S[0]
  current_run = 0
  for i in range(N):
      if S[i] == current_char:
          current_run += 1
      else:
          # when the character changes
          # update max_run for the current_char: set to max(current_run, existing max for that char)
          if current_char in max_run:
              if current_run > max_run[current_char]:
                  max_run[current_char] = current_run
          else:
              max_run[current_char] = current_run
          # reset current_char to S[i] and current_run to 1
          current_char = S[i]
          current_run = 1

  # after loop, update the last run
  if current_char in max_run:
      if current_run > max_run[current_char]:
          max_run[current_char] = current_run
  else:
      max_run[current_char] = current_run

Then, the answer is the sum of all values in max_run.

But wait: what if the same character appears in non-adjacent runs? We are only tracking the maximum run per character, regardless of where it appears. So, the above algorithm would work.

But note: we are traversing and grouping consecutive same characters. Then, for each character, we record the maximum consecutive run we've seen.

This algorithm is O(N) and uses O(1) extra space (since the dictionary will have at most 26 keys). So, it's efficient.

Let me test with a small example: "aaba"
- Start: current_char='a', current_run=1
- Next: 'a' -> current_run=2
- Then 'b': 
   - update for 'a': max_run['a'] = 2
   - set current_char='b', current_run=1
- Then 'a':
   - update for 'b': max_run['b']=1
   - set current_char='a', current_run=1
- After loop, update 'a': current_run=1, but max_run['a'] is already 2 -> so set to 2? Actually, we compare and keep 2.

Then, sum = max_run['a'] + max_run['b'] = 2+1=3.

But what are the distinct substrings? 
- For 'a': "a", "aa" -> 2
- For 'b': "b" -> 1
Total: 3.

But the distinct substrings that are repetitions of one character are: "a", "aa", "b". So, 3. Correct.

Another test: "ababa" as before: 
- Runs: 
  a:1 -> max_run['a']=1
  b:1 -> max_run['b']=1
  a:1 -> update 'a'? current_run=1, which is not greater than existing 1 -> no change
  b:1 -> same
  a:1 -> same
So, max_run['a']=1, max_run['b']=1 -> total=2. Correct.

But wait: in "ababa", the runs are broken. The algorithm will:

i=0: 'a' -> current_char='a', current_run=1
i=1: 'b' -> different. So update 'a': max_run['a']=1. Then set current_char='b', current_run=1.
i=2: 'a' -> update 'b': max_run['b']=1. Then set current_char='a', current_run=1.
i=3: 'b' -> update 'a': no change (max_run['a'] is 1, current_run=1 -> no update). Then set current_char='b', current_run=1.
i=4: 'a' -> update 'b': no change. Then set current_char='a', current_run=1.
After loop: update 'a' -> no change.

So, indeed, we have max_run['a']=1, max_run['b']=1. Sum=2.

Now, what about a character that has two runs and the second run is longer? For example: "abbccc"

Runs:
a:1
b:2
c:3

So, max_run for a=1, b=2, c=3 -> total=6.

But the distinct substrings: 
a: "a" ->1
b: "b", "bb" ->2
c: "c","cc","ccc"->3
Total: 6. Correct.

Another test: "aaaabba"
Runs: 
a:4
b:2
a:1

Then max_run for a: max(4,1)=4, for b:2 -> total=6.

Distinct substrings for a: k=1,2,3,4 -> 4
for b: k=1,2 -> 2
Total:6.

But are there any duplicates? The substring "a" appears in multiple runs, but we count it only once. So, yes, distinct substrings are 6.

Therefore, the solution is to compute the maximum run length for each character and sum them.

But wait: what if the string is empty? But constraints say N>=1, so we don't have to handle empty.

Implementation:

Steps:
1. Read N and the string S.
2. Initialize:
   current_char = S[0]
   current_run = 1
   max_run = {}   # dictionary for max run per char

3. Traverse from i=1 to N-1:
   for each char in S[1:]:
        if char == current_char:
            current_run += 1
        else:
            # update max_run for current_char
            if current_char in max_run:
                if current_run > max_run[current_char]:
                    max_run[current_char] = current_run
            else:
                max_run[current_char] = current_run
            # reset
            current_char = char
            current_run = 1

4. After the loop, update the last run.

5. Sum all the values in max_run.

But note: what if the entire string is one run? Then we never update in the loop? So we must update after the loop.

Alternatively, we can update at the beginning of the loop? Or just update at the end.

I'll write the code accordingly.

Edge case: N=1
Then, we skip the loop. Then update after: current_char is the only char, current_run=1. Then set max_run[char]=1. Then sum=1.

Code:

class Solution:
    def solve(self, N: int, S: str) -> int:
        if N == 0:
            return 0
        max_run = {}
        current_char = S[0]
        current_run = 1
        
        # If there's only one character, then we traverse no further, but we have to update max_run for that char.
        for i in range(1, N):
            if S[i] == current_char:
                current_run += 1
            else:
                # update for current_char
                if current_char in max_run:
                    if current_run > max_run[current_char]:
                        max_run[current_char] = current_run
                else:
                    max_run[current_char] = current_run
                current_char = S[i]
                current_run = 1
        
        # After the loop, update the last run
        if current_char in max_run:
            if current_run > max_run[current_char]:
                max_run[current_char] = current_run
        else:
            max_run[current_char] = current_run
        
        return sum(max_run.values())

But wait: what if a character appears only once? Then we set its max_run to 1. Then it's included.

But what if the same character appears in two separate runs and the first run is longer? Then the max_run is updated to the longer one. So, correct.

Let me test with "aaabaa": 
Runs: 
- Start: current_char='a', current_run=3 (because we have 'a','a','a') then at index3: 'b' -> update 'a' to 3.
- Then current_char='b', current_run=1 -> then at next 'a', update 'b' to 1.
- Then current_char='a', current_run=2 -> then end, update 'a': max_run['a'] is 3, current_run=2 -> no update.
Then sum = 3 (from 'a') + 1 (from 'b') =4. Correct.

But note: the last run of 'a' is 2, but we already have 3 for 'a', so we don't update.

So, the code seems correct.

However, is there a possibility that we miss a character that doesn't appear in any run? No, because we traverse the entire string.

But what about the last run? We update after the loop, so that's covered.

Complexity: O(N) time and O(1) space (since the dictionary has at most 26 keys). So, it meets the constraints.

Therefore, I'll implement accordingly.

But wait: the problem says two substrings that are equal as strings are not distinguished. So, if the same substring (like "a") appears in two different runs, we are counting it only once because we are counting distinct strings by their value. However, in our solution, we are not explicitly storing the distinct substrings. Instead, we are counting the distinct substrings by the maximum run per character. And that gives the number of distinct substrings? 

Yes, because the distinct substrings of repeated c's are exactly the strings c, cc, ccc, ... up to the maximum run. And each of these is distinct (because they are different lengths). And for different characters, they form different sets. So, the total distinct substrings is the sum for each character c of (max_run[c]).

So, the approach is solid.

Now, let me write the code accordingly.

But note: we can also use a list for max_run? Actually, we can use an array of size 26, but then we have to map character to index. But using a dictionary is simpler and the dictionary will have at most 26 keys.

Alternatively, we can do:

max_run = [0]*26
and then for a character c, index = ord(c)-ord('a')
But then we have to update for each run.

But the logic is the same.

I'll stick with the dictionary for clarity.

But for performance: dictionary is O(1) per operation, so it's efficient.

Now, code:

I'll test with the examples.

Example 1: "aaabaa" -> 4
Example 2: "x" -> 1
Example 3: "ssskkyskkkky" -> 8

I think it's correct.

But wait: what if a character does not appear at all? Then we don't have it in the dictionary, so we don't add anything. Correct.

So, the final code is as above.

But I can make the update part more concise:

After the loop, we have to update the last run. We can do the same update as in the loop.

Alternatively, we can handle the last element by including it in the loop. But the current method is clear.

We can also write a helper function or use defaultdict, but since we are not using any extra libraries, we can stick with the current.

Alternatively, we can initialize max_run for each character we meet on the fly.

We can also refactor the update step:

max_run[current_char] = max(max_run.get(current_char, 0), current_run)

So:

In the loop when we meet a different character:
    max_run[current_char] = max(max_run.get(current_char, 0), current_run)

Similarly, after the loop:
    max_run[current_char] = max(max_run.get(current_char, 0), current_run)

So, we can write:

class Solution:
    def solve(self, N: int, S: str) -> int:
        if N == 0:
            return 0
        max_run = {}
        current_char = S[0]
        current_run = 1
        
        for i in range(1, N):
            if S[i] == current_char:
                current_run += 1
            else:
                # update for the current run of current_char
                max_run[current_char] = max(max_run.get(current_char, 0), current_run)
                current_char = S[i]
                current_run = 1
        
        # update the last run
        max_run[current_char] = max(max_run.get(current_char, 0), current_run)
        
        return sum(max_run.values())

This is cleaner.

Test with "aaabaa": 
- After first run (3 a's): max_run['a']=3
- Then run of 'b':1 -> max_run['b']=max(0,1)=1
- Then run of 'a':2 -> when updating at the end: max_run['a']=max(3,2)=3
Then sum=3+1=4. Correct.

Test with "aaba": 
- First run: 'aa' -> max_run['a']=2
- Then 'b': max_run['b']=1
- Then last 'a': max_run['a']=max(2,1)=2 -> then sum=2+1=3. Correct.

So, this version is better.

Now, what if the entire string is one run? 
Example: "aaa"
- Loop: i=1: same -> current_run=2
- i=2: same -> current_run=3
- Then after loop: update: max_run['a']=max(0,3)=3 -> sum=3. Correct.

What if we have a run that breaks the current maximum? 
Example: "aabaaa": 
- First run: 'aa' -> max_run['a']=2
- Then 'b': max_run['b']=1
- Then 'aaa': at the end, update 'a': max(2,3)=3 -> then total=3+1=4.

The distinct substrings: 
a: "a","aa","aaa" -> 3
b: "b" ->1
Total:4. Correct.

So, I think this solution is correct and efficient.

Time complexity: O(N)
Space complexity: O(1) [because the dictionary size is bounded by 26]

Therefore, we can write the code as above.

Let me run through the provided examples:

Example 1: Input "aaabaa" -> output 4
Example 2: Input "x" -> output 1
Example 3: Input "ssskkyskkkky" -> output 8

All match.

Edge: very long string? With 200,000 characters. The algorithm is linear, so it should be efficient.

I'll implement accordingly.

Final code:

## Final Solution

To solve this problem, we need to count the number of distinct non-empty substrings in a given string that consist of repetitions of a single character. The key insight is that each distinct substring of this type is uniquely determined by the character and its length. For example, "a", "aa", and "aaa" are distinct substrings. 

### Approach
1. **Problem Analysis**: The problem requires counting all distinct substrings formed by repeating a single character. The challenge is to efficiently compute these substrings without enumerating all possible substrings, which would be computationally expensive for large strings (O(N^2)).
2. **Key Insight**: For any character in the string, the distinct substrings it forms are determined by the maximum consecutive occurrences (run length) of that character. For instance, if the maximum run of 'a' is 3, the distinct substrings are "a", "aa", and "aaa".
3. **Algorithm Selection**: We traverse the string once to compute the maximum run length for each character. This involves:
   - Tracking the current character and its run length as we iterate through the string.
   - Updating the maximum run length for each character whenever a different character is encountered or at the end of the string.
4. **Complexity Analysis**: The algorithm runs in O(N) time, where N is the length of the string, since it processes each character exactly once. The space complexity is O(1) because we use a dictionary to store at most 26 entries (one for each lowercase English letter).

### Solution Code
```python
class Solution:
    def solve(self, N: int, S: str) -> int:
        if N == 0:
            return 0
        max_run = {}
        current_char = S[0]
        current_run = 1
        
        for i in range(1, N):
            if S[i] == current_char:
                current_run += 1
            else:
                max_run[current_char] = max(max_run.get(current_char, 0), current_run)
                current_char = S[i]
                current_run = 1
        
        max_run[current_char] = max(max_run.get(current_char, 0), current_run)
        
        return sum(max_run.values())
```

### Explanation
1. **Initialization**: We start by checking if the string is empty (though constraints ensure N ≥ 1). We initialize a dictionary `max_run` to store the maximum run length for each character, and set `current_char` to the first character of the string with `current_run` as 1.
2. **Traversal**: For each subsequent character in the string:
   - If the character matches `current_char`, we increment `current_run`.
   - If a different character is encountered, we update `max_run` for `current_char` with the maximum of its existing value (if any) and `current_run`. We then reset `current_char` to the new character and `current_run` to 1.
3. **Final Update**: After processing all characters, we update `max_run` for the last `current_char`.
4. **Result Calculation**: The result is the sum of all values in `max_run`, representing the total distinct substrings formed by repeating each character up to its maximum run length.

This approach efficiently computes the desired result by leveraging the observation that each character's distinct substrings are entirely determined by its longest consecutive run in the string. The solution optimally processes the string in linear time with constant space.