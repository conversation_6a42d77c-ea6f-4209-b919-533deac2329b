# Solution for abc314_f.md

Generated at: 2025-07-22 06:33:12
Model: deepseek-reasoner

## Reasoning Process

We are given an integer N and a list of N-1 matches. Each match is represented by two players (p_i, q_i), meaning that in the i-th match, the team containing p_i goes first and the team containing q_i goes second.

The tournament starts with each player as a one-person team. In each match, two teams are merged. The winning probability for the first team is a/(a+b) and for the second team is b/(a+b), where a and b are the sizes of the two teams.

We are to compute for each player i the expected number of wins for the team that includes player i throughout the tournament.

Note: The entire tournament has N-1 matches. Each match produces one win. Therefore, the total wins across all teams is N-1. However, the expectation for each player is not necessarily 1 because the wins are distributed according to the process.

Key observations:

1. Linearity of Expectation: The total expected wins for a player is the sum over matches of the probability that the team containing the player wins that match.

2. However, note that in a match the player's team might not be involved. So we need to consider only the matches in which the player's team participates? Actually, the player's team will participate in several matches until it gets merged and then the new merged team continues. But note: once a team is merged, the player is part of a bigger team.

3. The challenge: The probability that a particular team wins a match in which it is involved depends on the sizes of the two teams at the time of the match. Moreover, the matches are predetermined in order (the input order) and the merging affects the team sizes for subsequent matches.

But note: the input order of matches is fixed. However, the structure of the tournament is a tree: each match merges two teams. The entire process forms a tree: the tournament is a full binary tree? Actually, it's not necessarily binary because a team can have any size. But the merging is two at a time. The entire process is a rooted tree with the final team as the root.

We can think of the tournament as a process that builds a tree. Each match corresponds to a node (representing the merged team) that has two children (the two teams that merged). The leaves are the initial players.

But note: the matches are given in an arbitrary order. The tree is built from the bottom up. However, the order of matches matters because the team sizes depend on the order of merging.

How to model the entire process?

We note:

- The tournament structure is a rooted tree. The leaves are the players. The internal nodes are the matches. However, the input does not specify the tree structure explicitly. Instead, we are given an unordered set of matches? Actually, the input order is the order of the matches. But note: the problem states that just before the i-th match, the players p_i and q_i are in different teams. So the matches are played sequentially.

We can build the entire merging history as a tree? Actually, we know that at the end, there is one team. We can use union-find to track the current teams? But we also need to know the entire history? That seems too heavy.

Alternative approach:

We can use linearity of expectation: for each match, we can compute the probability that a particular player's team wins that match. Then the expected wins for a player is the sum of the probabilities of his team winning in each match.

But note: for a match that does not involve the player's team, the probability that the player's team wins is 0. For a match that does involve the player's team, then the player's team is either the first team or the second team.

However, the complication is that the player's team might be merged in an earlier match, and then the merged team participates in subsequent matches.

But we can reframe: for a fixed player, we are only concerned with the matches that involve the team that currently contains the player. And note that the player's team participates in several matches until it is merged into a bigger team that continues. In fact, each player is involved in a sequence of matches until the entire team is merged into the final team.

Specifically, each player will be in exactly (number of matches that the player's team participates in) = (the number of merges that the player's initial team undergoes until the entire team is merged) = (the size of the player's initial team minus one) plus the matches that the merged team (containing the player) participates in? Actually, the entire tournament from the perspective of a player: the player starts in a team. Then the team participates in a match and wins or loses. If it wins, it continues to the next match it is involved in. Actually, the tournament is fixed: the matches are predetermined. But the player's team might be involved in multiple matches.

Actually, the player's team will be involved in a sequence of matches: starting from the first match that involves the player's team until the match in which the player's team is merged and then no longer exists. Then the merged team (which now includes the player) will be involved in subsequent matches until it is merged again, etc.

Therefore, the player's entire history is the chain of matches that the team (that contains the player) participates in, until the team becomes the final team. The final team does not participate in any match (because there are no more matches).

How many matches does the player's team participate in? It is exactly the number of edges in the path from the player (leaf) to the root in the tournament tree? Actually, not exactly: the team containing the player participates in a match for every time it is merged with another team. And the player is merged exactly (N-1) times? Actually, the entire tournament has N-1 merges. But the player is merged (merged into a larger team) exactly (the number of merges that include the player's team) until the team becomes the entire set. How many merges? The player is in a team that is merged until the entire set is formed. The number of merges that the player's team undergoes is the number of times the team that the player belongs to is merged with another team. This is equal to the depth of the player in the tree? Actually, if we build the tree, the player is at a leaf. The leaf has an edge to the first merge, then to the next, until the root. The number of merges that the player's team goes through is the number of edges from the leaf to the root? Actually, the entire chain from the leaf to the root has (number of merges = the depth). But note: the player's team is involved in every merge that occurs on the path from the leaf to the root? Actually, each merge on the path from the leaf to the root is a match that the player's team is involved in.

But wait: the team that the player is in is involved in a match only when it is merged. And each merge involves two teams. The player's team is one of them. So the number of matches the player's team participates in is the number of times the team containing the player is merged, which is the number of merges that occur in the subtree of the player? Actually, the entire tournament tree: the player's node (leaf) is merged (as a team) exactly once (to become part of a larger team). Then the larger team is merged again, etc. So the chain from the leaf to the root: each edge represents a merge that the player's team (at that time) was involved in. Therefore, the player's team is involved in exactly the number of matches equal to the depth of the leaf (if we count the edges from leaf to root) which is the number of merges that the player's team undergoes.

But note: the entire tournament tree has N leaves and N-1 internal nodes. The path from the leaf to the root has length (number of edges) = the depth. Therefore, the player's team participates in exactly (depth) matches.

However, the matches are given in a fixed order. How do we relate the matches to the tree? We can build the tree by processing the matches? Actually, the matches tell us which two teams (identified by a player) are merged. We can use a DSU (disjoint set union) to simulate the merging and build a tree.

But note: we are required to compute the expected wins for each player. How can we compute the probability that the player's team wins a particular match that it participates in?

Let me denote for a player u:

E_u = sum_{match j that the team containing u participates in} [probability that the team containing u wins match j]

In a match j, if the team containing u is the first team (the one that goes first) and has size a, and the other team has size b, then the probability that the team wins is a/(a+b). Similarly, if it is the second team, then the probability is b/(a+b).

But note: the match j is described by (p_j, q_j). The team that contains p_j goes first, and the team that contains q_j goes second. Therefore, if the team containing u is the one that contains p_j, then it is first and wins with probability a/(a+b). If the team containing u is the one that contains q_j, then it is second and wins with probability b/(a+b). Otherwise, the team is not involved? Actually, the match involves two specific teams: the team of p_j and the team of q_j. So if the player u is in the team of p_j, then we are in the first case. If in the team of q_j, then the second case. Otherwise, the player's team is not involved? Actually, the problem states that the two teams are chosen and they are different, and the player u must be in one of the two teams if the match j involves the team containing u? Actually, no: the match j involves two specific teams (the team of p_j and the team of q_j). So if the player u is in the team of p_j, then we are in the first case. If the player u is in the team of q_j, then we are in the second case. If the player u is in neither, then the match j does not involve the team of u, so the contribution is 0.

Therefore, we can write:

E_u = sum_{j: the team of u is the team of p_j} [a_j/(a_j+b_j)] + 
       sum_{j: the team of u is the team of q_j} [b_j/(a_j+b_j)]

where a_j = size of the team of p_j at match j, and b_j = size of the team of q_j at match j.

But note: the sizes a_j and b_j are known at the time of the match? And we know the entire merging sequence.

However, the challenge is that the matches are sequential and the team sizes change as merges occur.

How can we compute the team size at the time of a match for a given player? We can simulate the entire process with DSU? But we have up to 200,000 matches and players. We cannot for each player iterate over all matches (that would be O(N^2)).

We need a more efficient method.

Idea: build the entire tournament tree. We can do:

- Start with each player as a node (with size 1).
- For each match (p_i, q_i), we merge the two teams containing p_i and q_i. We can create a new node that represents the merged team. The new node has two children: the roots of the two teams. The new node's size is the sum of the two teams' sizes.

But note: the match order is given. So we can build the tree by processing the matches in order. We can use union-find to locate the current root of p_i and q_i, then create a new node that is the parent of these two roots. Then update the union-find to point to the new node.

But what is the root? The root of a team is the node that represents the entire merged team. Actually, the entire tournament tree will have the final root as the last merged node.

Then, the entire structure is a rooted tree with the final root at the top. The leaves are the players.

Now, consider a match j. The match j corresponds to an internal node in the tree. The internal node has two children: the two teams that merged. The match j is represented by this internal node.

In this tree:

- The match j (the internal node) has two children: let's call them left and right. The left child corresponds to the first team (the one that goes first, which is the team of p_j) and the right child corresponds to the second team (the team of q_j). The size of the left child's team is the size of the subtree of the left child? Actually, we built the tree by merging: the size of a node is the total number of leaves in its subtree.

So at the match j (the internal node), the size of the first team is the size of the subtree of the left child, and the size of the second team is the size of the subtree of the right child? Actually, when we merge, the two teams are the entire subtrees of the two children. Therefore, a_j = size(left child) and b_j = size(right child). But note: we built the tree by merging the two teams. The new node has size = a_j + b_j.

Therefore, for a match j (the internal node) the probability that the first team wins is a_j/(a_j+b_j) and the second team wins is b_j/(a_j+b_j).

Now, for a player u (a leaf), the matches that the player's team participates in are the matches that occur along the path from the leaf u to the root. Specifically, the player u is in the left subtree of an ancestor node only if the match at that node was a match that the player's team participated in? Actually, the player's team is involved in every match that occurs at an internal node that is an ancestor of the leaf u.

Why? Because the player u is initially in a team. Then the first time it is merged (at the parent node of u) the team containing u is one of the two teams. Then the merged team (which now includes u) moves up and is merged again at the next parent, etc. until the root.

But note: the tree we built is a binary tree? Actually, each internal node has exactly two children. The entire tree is a binary tree with N leaves and N-1 internal nodes.

Therefore, the player u will be in the left or right subtree of every ancestor node (from u up to the root). For an internal node that is an ancestor of u:

- If u is in the left subtree of that node, then in that match the team containing u was the first team (because the left child is the first team, as per the match: the team of p_j is first). Then the probability of winning at that match for u's team is (size_left) / (size_left+size_right).

- If u is in the right subtree, then the team containing u was the second team, and the probability of winning is (size_right) / (size_left+size_right) ??? Actually, no: the second team wins with probability (size_second_team)/(size_first+size_second). But note: the first team is the left child (size_left) and the second team is the right child (size_right). So if the player is in the right team, then the win probability is (size_right)/(size_left+size_right).

Therefore, for each player u, we can traverse the path from u to the root and for each internal node (match) j on the path, we add:

  if u is in the left subtree: win_prob = size_left / (size_left+size_right)
  if u is in the right subtree: win_prob = size_right / (size_left+size_right)

But note: the tree is built arbitrarily. The left child is the team of p_j and the right child is the team of q_j. However, when we build the tree, we know the sizes and we know the left and right children.

But how do we know the entire tree? We can build the tree as follows:

We maintain:
  - A DSU to track the current root of each connected component (team). Initially, each player is a component and the root is itself (and we also create a node for each player).
  - An array to store the children of a new node.

Steps for building the tree:

  nodes = [0..N-1] for the players. We'll create new nodes from N to N + (N-1) - 1? Actually, we have N leaves and N-1 internal nodes.

  size[i] = 1 for i in [0, N-1] (the players), and for internal nodes, we set the size as the sum of the children's sizes.

  parent array for the tree? Or we can build the tree as a graph.

  We process each match (p, q) (0-indexed?):

      Let r1 = find(p)  [the root of the component of p]
      Let r2 = find(q)  [the root of the component of q]

      Create a new node x (with id = current internal node id, starting from N)
      Set the children of x: left child = r1, right child = r2? But note: the match specifies that the team of p goes first and the team of q goes second. So the left child should be the team of p (r1) and the right child the team of q (r2).

      Then, we merge the two components: the new component has root x. The size of x is size[r1] + size[r2].

      Then we set parent[r1] = x and parent[r2] = x? And then in DSU we set the parent of r1 and r2 to x? Actually, the DSU only needs to track the root of the component. We can update: 
          parent[r1] = x
          parent[r2] = x
          and then the DSU union: we set the root of the new component to x.

  After processing all matches, the root of the entire tree is the last internal node.

Then we have the entire tree.

But note: we have to store the tree:

  We can store:
      left_child[x] = r1
      right_child[x] = r2
      size[x] = size[r1] + size[r2]

Then, for each player (leaf) u, we traverse up the tree until the root. At each step, we know the current node and its parent. At the parent node, we know:

  The parent node has two children: left and right. We know which child u is: either the left or the right.

  Then we compute:

      total = size[parent]   [which is size_left + size_right, but we have stored the size of the parent? Actually, the size of the parent is stored. But we also know the sizes of the children? Actually, we stored the two children. We can compute the size of the left child as the entire subtree of the left child? Actually, we stored the size of the parent, but we don't have stored the size of the left and right at the time of the match? 

But note: when we built the parent node, we set:
      size[parent] = size[r1] + size[r2]

But note: the size[r1] and size[r2] are the sizes at the time of the merge. However, we stored the sizes for each node. So for a parent node x, we have:

      left_size = size of the left child? Actually, we know the entire subtree of the left child: it is the size stored at the left child? But wait: the left child might be an internal node and its size is the total number of leaves in its subtree. Similarly for the right child.

Therefore, at the parent node x, the two teams have sizes: 
      a = size of the left subtree = size of the left child (which is the entire subtree of the left child) 
      b = size of the right subtree = size of the right child

So we can precompute the sizes for all nodes (we did during the merge). So we have an array `sz` such that for a node i, sz[i] is the total number of leaves in the subtree.

Then, for an internal node x (which is the parent of u and also the parent of the other child), we can get:

      a = sz[left_child[x]]
      b = sz[right_child[x]]

But note: if u is the left child, then the team of u (at the time of the match) has size a, and the other team has size b. The win probability for the first team (which is the left child, the team of p_j) is a/(a+b). 

If u is the right child, then the team of u is the second team, and the win probability is b/(a+b)? Actually, no: the win probability for the second team is b/(a+b). 

So:

  if u is the left child: add a/(a+b)
  if u is the right child: add b/(a+b)

But note: the entire expression must be modulo 998244353. We are dealing with fractions. We need modular inverses.

Therefore, we can precompute the inverse of (a+b) mod 998244353, then:

  if u is left: add (a * inv(a+b)) mod mod
  if u is right: add (b * inv(a+b)) mod mod

Then E_u is the sum of these contributions over all ancestors (matches) that the player u participates in.

But note: the player u is a leaf. How many ancestors? The depth of the leaf: which can be O(N) in the worst-case (if the tree is a chain). And we have N players: total operations O(N^2) which is too slow for N=200000.

We need a more efficient way.

We can do a DFS from the root? Actually, we can precompute the entire tree and then do a DFS to compute the expected wins for each player. How?

We can propagate the contributions? Actually, we can do:

  Let F(u) be the expected wins for player u.

  F(u) = 0 at the beginning.

  Then we traverse the path from u to the root and add the contributions. But if we do that for each u, the total time is O(N^2). 

Alternative: we can use a Fenwick tree or segment tree to update the path? Actually, the tree is static. But the contributions are additive and independent per node? Actually, each internal node contributes to the two players in the two subtrees? Actually, no: each internal node contributes to every leaf in its left subtree an amount a/(a+b) and to every leaf in its right subtree an amount b/(a+b). 

Therefore, we can do:

  For each internal node x (with left child L and right child R, and sizes a = sz[L], b = sz[R]):

      It adds a/(a+b) to every leaf in the left subtree.
      It adds b/(a+b) to every leaf in the right subtree.

Therefore, the expected wins for a leaf u is the sum of the contributions from every internal node that is an ancestor of u.

We can do a DFS from the root and accumulate the contributions? Actually, we can do a DFS that starts at the root and propagates a value "add" downward. 

Specifically:

  Let ans[leaf] = 0 for each leaf.

  We do:

      stack = [root]
      add = 0
      while stack:
          pop node x
          if x is an internal node:
              Let a = sz[left_child[x]], b = sz[right_child[x]]
              For the left child: we push it with add = current_add + a * inv(a+b) mod mod
              For the right child: we push it with add = current_add + b * inv(a+b) mod mod
          else: # x is a leaf (player)
              ans[x] = current_add

But note: the tree we built: the root is the last merged node. The leaves are the players. The internal nodes are the matches.

However, the DFS must traverse the entire tree. The tree has 2N-1 nodes.

This DFS is O(N). Then we can compute the answer for each leaf in O(1) per leaf.

Therefore, the algorithm:

1. Precompute an array `sz` for all nodes (leaves and internal). The leaves: sz[i] = 1 for i in [0, N-1]. The internal nodes: we'll have ids from N to N + (N-2) (if we index from 0).

2. Use DSU to build the tree. We also need to record the children of each internal node and the parent of each node (for the tree). But for the DFS we only need the children.

   We'll create:
        children = [ [] for _ in range(2*N-1) ]  # Actually, we have N leaves and N-1 internal nodes -> total nodes = 2*N-1.

   But we'll index the leaves from 0 to N-1, and internal nodes from N to N + N-2.

   Alternatively, we can create a list of nodes and store the children for each internal node.

3. We also need to know the root of the entire tree: the last node we create.

4. Precompute the modular inverses? We need to compute for each internal node: 
        inv_denom = pow(a+b, mod-2, mod)   [since mod is prime]

   Then the contribution for the left subtree: (a * inv_denom) % mod
   And for the right subtree: (b * inv_denom) % mod

5. Then do a DFS (or BFS) from the root, and for each leaf, we assign the accumulated sum.

6. Then output the answer for the players in order: player 0 to player N-1.

Steps in detail:

  Initialize:
      parent_dsu = list(range(2*N))   # we'll have 2*N-1 nodes, but we can use 0 to 2*N-2.
      Actually, we have nodes 0 to 2*N-2. Leaves: 0 to N-1. Internal: N to 2*N-2.

      sz = [0] * (2*N)   # we only need 2*N-1, but let's make 2*N
      left_child = [0] * (2*N)   # for internal nodes: store the left child
      right_child = [0] * (2*N)  # for internal nodes: store the right child

      For i in range(N): 
          sz[i] = 1

      DSU: we maintain an array `root` for each current component? Actually, we can use a standard DSU with path compression? But note: we are building a tree and we need the entire tree structure. We cannot use path compression because it breaks the tree. We need to know the actual parent-child links.

      Instead, we can use a DSU that only does union by size and without path compression? But we don't need path compression: we are building the tree. We can store the current root of each component and update the parent pointers for the DSU to the new node.

      We'll maintain:
          dsu_parent[i] = the root of the component that i belongs to. Initially, dsu_parent[i] = i (for i in [0, N-1]).
          Then when merging two components with roots r1 and r2, we create a new node x (starting from N, then N+1, ...). Then we set:
              dsu_parent[r1] = x
              dsu_parent[r2] = x
              and then the root of the new component is x.

      We also set:
          left_child[x] = r1
          right_child[x] = r2
          sz[x] = sz[r1] + sz[r2]

      Then we update the current root for the new component: we can store an array `comp_root` for the current root of each component? Actually, we don't need to store per component, because we are merging. We can just update the DSU parent for the two roots to x, and then we know that the root of the new component is x.

      How to find the root of a node? We can do:

          def find(x):
              while dsu_parent[x] != x:
                  x = dsu_parent[x]
              return x

      But wait: we set dsu_parent[r1] = x and dsu_parent[r2] = x. Then the root of the new component is x, and dsu_parent[x] should be set to x? Actually, we should set dsu_parent[x] = x? Because the new node is the root of the new component.

      Alternatively, we can set:

          dsu_parent = [i for i in range(2*N)]   # initially, each node is its own parent.

          Then when merging r1 and r2 (which are roots: dsu_parent[r1]==r1, dsu_parent[r2]==r2) we create a new node x (which is initially: dsu_parent[x]=x? Actually, we haven't set the parent of x in the DSU? We are going to set the parent of r1 and r2 to x, and then the root of the new component is x.

          Steps:
              node_id = current_internal_node_id (starting at N, then N+1, ... up to N+N-2 = 2*N-2)
              r1 = find(p-1)   # if players are 1-indexed, we convert to 0-indexed.
              r2 = find(q-1)

              # Now create new node: node_id
              left_child[node_id] = r1
              right_child[node_id] = r2
              sz[node_id] = sz[r1] + sz[r2]

              # Merge: set the parent of r1 and r2 to node_id in the DSU.
              dsu_parent[r1] = node_id
              dsu_parent[r2] = node_id

              # Then we move to the next.

          The root of the entire tree is the last node_id we create? Actually, after N-1 matches, we have one component. The last node created is the root.

      But note: we must not use path compression in the DSU because we want to keep the tree structure. However, we are only doing find for the root? Actually, when we do find, we are traversing the parent pointers. Since we set the parent for r1 and r2 to x, and x is a new node, and we haven't set the parent of x to anything? Actually, we set dsu_parent[x] = x? Initially, for the new node, we haven't set dsu_parent[x]. So we must initialize dsu_parent for the new node:

          We can initialize dsu_parent for the new node x to x? Actually, when we create x, we set dsu_parent[x] = x? 

          But then when we set dsu_parent[r1]=x, and then we call find on r1: 
              r1 -> x -> then we check dsu_parent[x]: which is x -> so we return x.

          So we should set dsu_parent for the new node to x (itself) at creation.

      How to initialize the DSU for the new nodes? We can initialize an array of size 2*N for dsu_parent, and for i in range(2*N): dsu_parent[i] = i. Then for the new node x (which is in the range [N, 2*N-2]), we set dsu_parent[x] = x. Then we update the two roots.

      Actually, we can initialize:

          dsu_parent = list(range(2*N))

      Then we start:

          current_node_id = N   # we'll create internal nodes starting from N

          for i in range(N-1):
              p, q = matches[i]  # each is a pair, 1-indexed
              u = p-1; v = q-1

              r1 = find(u)   # using the DSU find (with path compression? We don't want to break the tree? Actually, we don't need path compression because we are building the tree. But without compression, the chain might be long and worst-case O(N) per find? Then total O(N^2) which is too slow.

          We need to use union by size/rank for DSU? Actually, the problem doesn't require the tree to be balanced. But we are building the tree arbitrarily. However, the order of matches is fixed. So we cannot change the order. But we do need to update the DSU parent pointers.

          Without path compression: worst-case the chain might be long. We have up to 200000 nodes. The depth of a node in the DSU tree (the tree of parent pointers) is the depth of the actual tournament tree? Which can be O(N). Then the total cost of all finds: O(N^2) which is 40e9, too slow.

          We must use path compression? But note: we are building the actual tournament tree. If we use path compression, we break the parent pointers and we lose the structure.

          Alternative: we do not use DSU for building the tree? Actually, we are only using the DSU to get the current root of a player's team. But we are building the tree by the matches. We can process the matches and for each match, we know the two players. We need to know the current root of the two players. 

          How can we avoid long chains? We can store the current root for each player? Actually, we can maintain an array `root` for the current root of each player? But when two teams merge, the entire team gets a new root. How to update the root for all players in the team? That would be O(N) per merge -> O(N^2).

          Alternatively, we can use a DSU that stores the root of the component. We don't need path compression for the entire tree structure, but we want to quickly get the root of a player. We can store:

              root[i] for the current root of the component that contains player i.

          But then when we merge two components, we update the root for the entire component? How? We would have to update all players in the two components? That is O(size) per merge, and total O(N^2).

          We need a better way.

          Actually, we don't need the root for every player at every step. We only need the root for the two players given in the match. Then we can use DSU with union by size and without path compression, and we store for each node (in the DSU tree) the root of the component. How? 

          We can store an array `dsu_root[i]` for the root of the component of node i. Initially, for each leaf i, dsu_root[i] = i.

          Then when we merge two roots r1 and r2, we create a new node x. Then we set:

              dsu_parent[r1] = x
              dsu_parent[r2] = x
              dsu_root[x] = x   # the root of the new component is x.

          And then for the players? We don't update the root for every player in the component. But we don't need to: when we are given a player u, we can traverse up the DSU tree until we get to the root? But that is O(N) per find. 

          However, we are going to do 2*(N-1) finds (each match gives two players). So worst-case O(N^2). 

          We need to update the root for the entire component? That is too expensive.

          Alternatively, we can store the root for each component in a separate array and update only the representative. Actually, we can use a DSU that only stores the root for the entire component. We don't care about the entire tree for the DSU, we only care about the current root of the component that a node belongs to.

          We can use:

              leader[i] = the root of the component of i.

          Then when we merge two components with roots r1 and r2, we create a new node x. Then we update:

              for every node j in the component of r1 and r2, set leader[j] = x? But that is O(N) per merge.

          Total O(N^2).

          This is too expensive.

          How to avoid? We note that when we are given a player u, we only need the current root of u. We can store the root in an array `current_root[u]` and update it only when u is merged? But then when a component is merged, we have to update the root for all nodes in the component? That is O(size) per merge, and the total cost is O(N^2) (since the last merge is O(N)).

          But worst-case N=200000, and O(N^2) is 40e9 operations, which is too slow.

      Alternative: we do not update the root for every player. Instead, we store the DSU parent pointers without compression, and then when we want to find the root of a player, we traverse the parent pointers. But the depth of the tree is the number of merges that the player has undergone, which is O(N). Then the total cost for all finds is O(N * depth) = O(N^2). 

      However, the total number of find operations is 2*(N-1) (each match we look up two players). So worst-case 2*(N-1)*O(N) = O(N^2) = 40e9 for N=200000, which is too slow.

      We need a faster method.

      How about we store the current root for each component? We can use a DSU that stores the root of the component in the representative of the component. Then we can do:

          parent_dsu[i] = the parent in the DSU tree (for the purpose of union-find) for non-root nodes. For the root, parent_dsu[i] = i.

          Then we can have:

              def find(x):
                  if parent_dsu[x] != x:
                      parent_dsu[x] = find(parent_dsu[x])
                  return parent_dsu[x]

          But wait: we are building a tree and we need the entire tree structure (left_child, right_child). If we do path compression, we break the parent pointers? Actually, we don't care about the entire tree for the DSU? We only care about the current root of the component. However, we are also building a tournament tree (the left_child and right_child). 

          The tournament tree is separate. We are using the DSU only to get the root of the component for a player. And then we build the tournament tree by creating a new node that becomes the parent of the two roots.

          If we use path compression for the DSU, we can quickly find the root. But note: the tournament tree is the actual tree we are building. The DSU with path compression will create a different parent structure? 

          Actually, we do not want to use path compression for the tournament tree. The tournament tree must be the one we built: the new node becomes the parent of the two roots. 

          However, the DSU with path compression is only for the purpose of finding the root of the component for a given player. The tournament tree is built by:

              r1 = find(p)
              r2 = find(q)

              new_node = current_id
              left_child[new_node] = r1
              right_child[new_node] = r2
              sz[new_node] = sz[r1] + sz[r2]

              # Then we merge: set the DSU parent of r1 and r2 to new_node? But then we set:

              parent_dsu[r1] = new_node
              parent_dsu[r2] = new_node

          And for the new_node, we set parent_dsu[new_node] = new_node.

          Then when we do find(p): 
              we start at p, then go to ... until we hit a node whose parent_dsu is itself. But we set the parent_dsu of the new_node to itself. So the root of the new component is new_node.

          However, the path compression will set parent_dsu[p] to the root (new_node) after the first find? That breaks the tournament tree? Actually, the tournament tree is stored in left_child and right_child. The DSU parent pointers are only for the purpose of union-find. We do not use them for the tournament tree DFS. 

          But note: we are using the DSU parent pointers only to find the root. The tournament tree structure is stored in left_child and right_child. So we can safely use path compression for the DSU.

          Steps for DSU with path compression:

              parent_dsu = list(range(2*N))
              size_dsu = [1] * (2*N)   # we don't need size_dsu for the DSU? We have our own sz for the tournament tree.

              def find(x):
                  stack = []
                  while parent_dsu[x] != x:
                      stack.append(x)
                      x = parent_dsu[x]
                  root = x
                  for y in stack:
                      parent_dsu[y] = root
                  return root

          But wait: we are going to update the parent_dsu for the roots r1 and r2 to new_node. Then when we call find(p) for a player p that was in the component of r1, we will get new_node? And we do path compression: we set parent_dsu[p] = new_node? Then the entire path from p to r1 to new_node is compressed to p->new_node.

          This is acceptable because the tournament tree structure is stored separately. We are not using the DSU parent pointers to traverse the tournament tree. We only use the tournament tree (left_child, right_child) for the DFS propagation.

          Therefore, we can use DSU with path compression to find the root of a player quickly.

          Steps for building the tree:

            Initialize:
                parent_dsu = list(range(2*N))
                # For leaves 0 to N-1: parent_dsu[i]=i initially.
                # For internal nodes: we'll set parent_dsu[node_id]=node_id initially? Actually, we create them as we go.

                sz = [0]*(2*N)
                for i in range(N):
                    sz[i] = 1

                left_child = [0]*(2*N)
                right_child = [0]*(2*N)

                current = N   # next available internal node id

            For each match (p, q) (0-indexed players):

                u = p; v = q   # already 0-indexed

                r1 = find(u)   # returns the root of u's component
                r2 = find(v)   # returns the root of v's component

                # Create a new node: current
                left_child[current] = r1
                right_child[current] = r2
                sz[current] = sz[r1] + sz[r2]

                # Merge the two components: set the parent_dsu of r1 and r2 to current.
                parent_dsu[r1] = current
                parent_dsu[r2] = current

                # Also, set parent_dsu for current to current? Actually, we don't set it now, but we should have initialized parent_dsu for current to current? We can do:

                parent_dsu[current] = current   # but wait, we are going to update the parent_dsu for r1 and r2 to current. Then we don't want to change the parent_dsu of current? Actually, we want the root of the new component to be current. So we set parent_dsu[current]=current.

                # Then increment current for the next internal node.

            Then the root of the entire tree is the last internal node created? Actually, we create N-1 internal nodes. The last one is the root.

          But note: when we set parent_dsu[r1]=current, then if we call find(r1), we get current. And then parent_dsu[current]=current, so it returns current.

          Then we can do the DFS from the root (which is current-1, because we start at N and then current increases for each match).

          Actually, we create the internal nodes in order: the first match: node N, then N+1, ... the last match: node N+N-2.

          So the root = N + (N-2) = 2*N-2? Actually, we create N-1 nodes, so the last node is N + (N-2) = 2*N-2? But note: we start at current = N, then we do current+1 after each match? 

          Actually, we do:

              for i in range(N-1):
                  ... 
                  current = N + i   # then we can use current, and then after the loop, current = N + (N-1) - 1 = 2*N-2

          So the root is 2*N-2? But note: we set parent_dsu for the last node to itself, so it is the root.

          Then we do:

              root_node = 2*N-2   # the last internal node

          Then we do a DFS from root_node to the leaves, propagating the accumulated win probability.

          However, note: the DFS must traverse the entire tree (which has 2*N-1 nodes). This is O(N).

      Summary of building:

          We use DSU with path compression to quickly find the root of a player's component at the time of a match.

          We build the tournament tree by storing left_child and right_child for each internal node.

          We store the size of each node (the entire subtree).

          Then we run a DFS (or BFS) from the root (node 2*N-2) to the leaves. For an internal node x:

              a = sz[left_child[x]]
              b = sz[right_child[x]]
              denom = a+b
              inv_denom = pow(denom, mod-2, mod)
              left_contribution = a * inv_denom % mod
              right_contribution = b * inv_denom % mod

          Then when we traverse:

              For the left child: we add left_contribution to the accumulated value and then traverse left child.
              For the right child: we add right_contribution and then traverse.

          For a leaf, we store the accumulated value as the answer for that player.

      Then output the answer for the leaves in the order of players: player 0 to player N-1.

6. Edge: the tournament tree might be built arbitrarily. The DFS is a standard tree DFS.

7. Complexity: O(N * alpha(N)) for the DSU (with path compression) and O(N) for the DFS. Total O(N).

8. Example with N=5:

      Players: 0,1,2,3,4.

      Matches: 
          1 2  -> (0,1)
          4 3  -> (3,2)   [if 4 is player 3? Actually, the input: 
                  "1 2" -> (0,1)
                  "4 3" -> (3,2)  because 4 is the 4th player -> index 3? and 3 -> index 2? 
          "5 3" -> (4,2)   [5 is the 5th player -> index 4, 3 -> index 2]
          "1 4" -> (0,3)   [1->0, 4->3]

      But note: the team of player 3 (index=3) and player 4 (index=4) might be already merged? Actually, the matches are in order.

      We'll build:

          Match1: (0,1): 
              r0 = find(0)=0, r1 = find(1)=1.
              new_node = N (which is 5)
              left_child[5] = 0, right_child[5] = 1, sz[5]=1+1=2.
              parent_dsu[0]=5, parent_dsu[1]=5, parent_dsu[5]=5.

          Match2: (3,2): 
              r3 = find(3)=3, r2 = find(2)=2.
              new_node = 6
              left_child[6] = 3, right_child[6] = 2, sz[6]=1+1=2.
              parent_dsu[3]=6, parent_dsu[2]=6, parent_dsu[6]=6.

          Match3: (4,2): 
              But note: player 4 is index 4? And we haven't seen 4 before? 
              Actually, we have players 0,1,2,3,4. So we have player 4 (index=4). 
              Then: r4 = find(4)=4, r2 = find(2)=6? Because we set parent_dsu[2]=6, and then find(2) will go to 6? 
              But we have path compression: 
                  find(2): 
                      parent_dsu[2]=6 -> then we check parent_dsu[6]=6 -> so return 6.
              Then we have two roots: 4 and 6.
              new_node = 7
              left_child[7]=4, right_child[7]=6, sz[7]=1+2=3.

          Match4: (0,3): 
              r0 = find(0)=5 (because parent_dsu[0]=5, then parent_dsu[5]=5 -> returns 5)
              r3 = find(3)=6? But we set parent_dsu[3]=6, and then parent_dsu[6]=7? So we do:
                  find(3): 
                      3 -> parent_dsu[3]=6 -> then 6: parent_dsu[6]=7 -> then 7: parent_dsu[7]=7 -> so returns 7.
              Then we have roots 5 and 7.
              new_node=8
              left_child[8]=5, right_child[8]=7, sz[8]=2+3=5.

          Then the root of the entire tree is 8.

      Now do a DFS from 8:

          Node8: left_child=5, right_child=7; a=2, b=3; denom=5.
            left_contribution = 2 * inv(5) mod mod
            right_contribution = 3 * inv(5) mod mod

          Then we go to left child (5) and add left_contribution to the current accumulated value? Actually, the DFS starts at 8 with 0.

          For node5: it is an internal node. Then we add to the left child (0) the value: current + (contribution from node5) but note: the match at node5: the team of 0 was first and wins with probability 1/(1+1)=1/2.

          Actually, at node5: 
                a = sz[0]=1, b=sz[1]=1 -> so a/(a+b)=1/2.

          Similarly, for node6: 
                a = sz[3]=1, b=sz[2]=1 -> 1/2.

          For node7: 
                a = sz[4]=1, b=sz[6]=2 -> 1/3? 
                But wait: the match at node7: first team is the team of 4 (which is a team of size 1) and the second team is the team of 6 (which is a team of size 2). Then the win probability for the first team is 1/(1+2)=1/3, and for the second team is 2/3.

          Then the DFS:

              Start at 8: accumulated=0.
              Then we traverse the left child (5): 
                  accumulated_left = 0 + 2 * inv(5)   [for the match at 8, the left team wins with probability 2/5? Actually, the player in the left subtree (which are the two players in the team of 5: players 0 and 1) get an additional 2/5? 
                  Then at node5: 
                      we traverse the left child (0): 
                         accumulated0 = 2/5 (from the root) + 1/2 (from node5)   -> because at node5, the left child (0) is the first team and wins with probability 1/2.
                  Then the right child (1) of node5: 
                         accumulated1 = 2/5 + 0? But wait: at node5, the right child (1) is the second team? Actually, the match at node5: 
                             first team: player0 -> wins with probability 1/2.
                             second team: player1 -> wins with probability 1/2? But note: the player1 is in the second team, so the win probability for the second team is 1/2? But the DFS propagation: for the left child (0) we added the left_contribution of node5 (which is 1/2) and for the right child (1) we add the right_contribution of node5? Actually, no: at node5, the left child (0) is the first team, so we add 1/2 to the left subtree. The right child (1) is the second team, so we add 1/2 to the right subtree? 

                  Actually, our DFS propagation:

                      When we are at node5 (which is the left child of 8), we have an accumulated value of 2/5 (from the root match). Then we break the match at node5: 
                         The left subtree (0) gets an additional 1/2? 
                         The right subtree (1) gets an additional 1/2? 

                  So the DFS:

                      We start at 8: we have a value for the root: 0.
                      Then we are going to push the left child (5) with additional value = 0 + 2/5? But note: the 2/5 is the win probability that the left team wins at node8? And that win is for the entire left subtree? So every player in the left subtree (players0 and player1) gets an additional 2/5.

                  Then at node5: we break the match: 
                         The left subtree (player0) gets an additional 1/2? 
                         The right subtree (player1) gets an additional 1/2? 

                  So for player0: 2/5 + 1/2
                  For player1: 2/5 + 1/2? 

                  But wait: the match at node8: the left team (which is the entire team of players0 and 1) wins? Then both players get that 2/5? 

                  Then the match at node5: player0's team wins? That win is counted for player0? And player1's team? Actually, the win at node5 is for the team that contains player0? Then only player0's team wins? But the entire team (player0 and player1) is formed by the win at node5? Actually, no: at node5, the two teams (player0 and player1) are playing. The win at node5 is for the entire match that forms the team. Then the win is for the team that wins the match? Then the player0's team (which is just player0) wins the match? Then the win is counted for player0? Similarly, if player1's team wins, then it is counted for player1.

                  But note: after the match, the two teams are merged. The win is for the entire new team? But the problem: the team with the player wins. So if the team that contains player0 wins, then player0 gets a win. Similarly for player1.

                  Therefore, each player gets the wins from the matches that his team wins. 

                  So for player0: 
                      Match1 (node5): his team wins? -> 1/2
                      Match4 (node8): his team (which is the entire team of 0 and 1) wins? -> 2/5? 
                  But note: at node8, the team containing player0 is the entire team of players0 and 1. And that team wins? Then the win is counted for player0? 

                  Therefore, the expected wins for player0: 1/2 + 2/5 = 9/10.

                  Similarly, player1: 
                      Match1 (node5): his team (player1) wins? -> 1/2? Actually, in the first match, the two teams are player0 and player1. The win for the first team (player0) is 1/2, and for the second team (player1) is 1/2. 
                      Match4 (node8): his team (the entire team of 0 and 1) wins? -> 2/5? 
                  So player1: 1/2 + 2/5 = 9/10.

                  The example output: E1 = 9/10, E2=9/10, which matches.

                  Now player3 and player4? 

                  For the root node8: the right subtree (7) gets 3/5? 

                  Then at node7: 
                      left_child=4, right_child=6.
                      a = sz[4]=1, b=sz[6]=2 -> so the win for the first team (player4) is 1/3, and for the second team (the team of 6, which is players2 and 3) is 2/3.

                  Then at node6: 
                      left_child=3, right_child=2.
                      a=1, b=1 -> win for 3: 1/2, win for 2: 1/2.

                  So for player3 (index=3):
                      node8: 3/5? (from the root: because the entire team of the right subtree (players2,3,4) wins? Actually, the entire team of the right subtree wins the root match? Then the win is counted for player3? 
                      node7: the team of 6 (which is players2 and 3) is the second team? Then the win probability for the second team is 2/3? 
                      node6: the team of player3 is the first team? Then win probability 1/2? 
                  So total: 3/5 + 2/3 + 1/2 = 30/50 + ...? 

                  Let me compute: 
                      3/5 = 18/30
                      2/3 = 20/30? -> no, better common denominator 30: 3/5=18/30, 2/3=20/30, 1/2=15/30 -> total = 18+20+15 = 53/30? 

                  The example: E3 = 53/30.

                  Similarly, player2 (index=2): 
                      node8: 3/5
                      node7: 2/3? (because the team of 6 (which includes player2) is the second team? So wins with probability 2/3? 
                      node6: player2 is the second team? wins with 1/2? 
                  So same as player3: 3/5 + 2/3 + 1/2 = 53/30.

                  Player4 (index=4):
                      node8: 3/5
                      node7: the first team? wins with 1/3.
                      node6: not involved? 
                  So total: 3/5 + 1/3 = 14/15.

                  The example: E5=14/15.

                  Therefore, the DFS propagation:

                      For the root (node8): 
                          left_child: 5 -> add 2/5 to the entire left subtree (players0,1)
                          right_child: 7 -> add 3/5 to the entire right subtree (players2,3,4)

                      Then at node5: 
                          left_child:0 -> add 1/2
                          right_child:1 -> add 1/2

                      Then at node7:
                          left_child:4 -> add 1/3
                          right_child:6 -> add 2/3   (for the entire subtree of 6: players2 and 3)

                      Then at node6:
                          left_child:3 -> add 1/2
                          right_child:2 -> add 1/2

                  Then:
                      player0: 2/5 + 1/2
                      player1: 2/5 + 1/2
                      player2: 3/5 + 2/3 + 1/2
                      player3: 3/5 + 2/3 + 1/2
                      player4: 3/5 + 1/3

          How do we propagate? We do:

              We start at the root (8) with value 0.

              Then we traverse to the left child (5) and the right child (7). For the left child, we pass 0 + 2/5. For the right child, we pass 0 + 3/5.

              Then at node5: 
                  We traverse to left child (0): pass (2/5) + (1/2)   -> then at leaf0, we set ans[0]=2/5+1/2.
                  We traverse to right child (1): pass (2/5) + (1/2)   -> ans[1]=2/5+1/2.

              At node7: 
                  We traverse to left child (4): pass (3/5) + (1/3)   -> ans[4]=3/5+1/3.
                  We traverse to right child (6): pass (3/5) + (2/3)   -> then at node6: 
                      Then at node6: 
                          traverse to left child (3): pass (3/5+2/3) + (1/2) -> ans[3]=3/5+2/3+1/2.
                          traverse to right child (2): pass (3/5+2/3) + (1/2) -> ans[2]=3/5+2/3+1/2.

          This matches.

      Therefore, we implement:

          mod = 998244353

          Precomputation: 
              We'll create arrays: 
                  parent_dsu = list(range(2*N))
                  sz = [1] * N + [0] * (N)   # the first N for leaves, the next N for internal nodes (we only need N-1, but we use 2*N for safety)
                  left_child = [0] * (2*N)
                  right_child = [0] * (2*N)

          Steps:

              current = N   # next internal node id

              for i in range(N-1):
                  p, q = matches[i]   # each is 1-indexed, so convert to 0-indexed: p0 = p-1, q0 = q-1.

                  r1 = find(parent_dsu, p0)   # returns the root of p0
                  r2 = find(parent_dsu, q0)   # returns the root of q0

                  # Create new node: current
                  left_child[current] = r1
                  right_child[current] = r2
                  sz[current] = sz[r1] + sz[r2]

                  # Merge: set parent_dsu[r1] = current, parent_dsu[r2] = current
                  parent_dsu[r1] = current
                  parent_dsu[r2] = current

                  # Also, set parent_dsu[current] = current? Actually, we want the new node to be the root of the new component, so we set parent_dsu[current]=current.

                  current += 1   # for the next internal node

              root_node = current - 1   # the last created node

          Then we do a DFS/BFS from root_node to the leaves, propagating the accumulated value modulo mod.

          We'll use a stack or queue. We store (node, accum)

          For the root_node: accum=0.

          For a node x:
              if x is a leaf (x < N): then ans[x] = accum
              else:
                  a = sz[left_child[x]]
                  b = sz[right_child[x]]
                  denom = a + b
                  inv_denom = pow(denom, mod-2, mod)   # modular inverse of denom mod 998244353
                  left_win = a * inv_denom % mod
                  right_win = b * inv_denom % mod

                  # Then for the left child: we add left_win? Actually, no: 
                  #   For the left child: the win at this match for the left team is a/(a+b). Then the entire left subtree gets this win? 
                  #   But note: the win is for the entire team. So every player in the left subtree gets this win? 
                  #   Similarly, the right subtree gets the win for the right team? 
                  #   However, the problem: the win is counted for the entire team, which includes every player in that team. So every player in the left subtree gets an additional left_win? Actually, no: the win is for the team, and the team wins the match. Then every player in the team gets this win? 
                  #   But note: the problem says "the team with that player wins" -> so if the player is in the team that wins, then it counts. 
                  #   Therefore, if the left team wins, then every player in the left subtree gets a win? And if the right team wins, then every player in the right subtree gets a win? 
                  #   However, in the DFS we are propagating the expected wins for the player. The win at the current node: 
                  #       If the player is in the left subtree, then the win for this match is a/(a+b). 
                  #       If the player is in the right subtree, then the win for this match is b/(a+b).
                  #   Therefore, we do not add the same value to both children. Instead, we add different values: 
                  #       For the left child: we add left_win (because the left team wins with probability a/(a+b), and the player in the left subtree is in the left team) 
                  #       For the right child: we add right_win.

                  # Then we push the left child with (accum + left_win) mod mod
                  # and the right child with (accum + right_win) mod mod.

          Then after DFS, we have an array ans[0..N-1] for the players.

          Then output: ans[0], ans[1], ... ans[N-1]

      But note: the DFS must traverse the entire tree. The tree has 2*N-1 nodes, so it's O(N).

      Important: the DSU find function with path compression.

          We write:

          def find(parent, x):
              stack = []
              while parent[x] != x:
                  stack.append(x)
                  x = parent[x]
              root = x
              for y in stack:
                  parent[y] = root
              return root

      However, we are updating the parent during the union. The DSU parent array is being modified.

      We must be cautious: we are building the entire tree and then we do the DFS. The DSU parent array is used only for building the tree. After building, we do the DFS independently.

      Therefore, we can do:

          parent_dsu = list(range(2*N))
          # Initialize: for i in range(2*N): parent_dsu[i]=i

          sz = [0] * (2*N)
          for i in range(N):
              sz[i] = 1

          left_child = [0] * (2*N)
          right_child = [0] * (2*N)

          current_node = N
          for i in range(N-1):
              u, v = matches[i]
              u0 = u-1
              v0 = v-1

              ru = find(parent_dsu, u0)
              rv = find(parent_dsu, v0)

              # Create new node: current_node
              left_child[current_node] = ru
              right_child[current_node] = rv
              sz[current_node] = sz[ru] + sz[rv]

              parent_dsu[ru] = current_node
              parent_dsu[rv] = current_node
              parent_dsu[current_node] = current_node   # set the root of the new component to itself

              current_node += 1

          root = current_node - 1

      Then do DFS/BFS.

      However, note: the DSU find function might break the parent pointers for the tree? Actually, we are storing the tree structure in left_child and right_child. The DSU parent array is only for the union-find during building. After building, we don't need it. The DFS uses the tree structure: left_child and right_child.

      Therefore, we can safely use path compression in the DSU for building.

      Let me test with a small example: N=3.

          Players: 0,1,2.

          Matches: 
              match1: 1 2 -> (0,1)
              match2: 1 3 -> (0,2)   [but after the first match, the team of 0 is merged?]

          Steps:

            parent_dsu = [0,1,2, 3,4,5]  # for 2*N=6, indices 0..5
            sz = [1,1,1,0,0,0]   # then we set for the leaves: indices0,1,2 have sz=1.

            current_node = 3

            Match1: (0,1)
                ru = find(0): 
                    0: parent_dsu[0]=0 -> returns 0.
                rv = find(1): returns 1.
                left_child[3]=0, right_child[3]=1, sz[3]=2.
                parent_dsu[0]=3, parent_dsu[1]=3, parent_dsu[3]=3.

            Match2: (0,2)
                ru = find(0): 
                    0: parent_dsu[0]=3 -> then find(3): parent_dsu[3]=3 -> returns 3. Then we set parent_dsu[0]=3? Actually, the find function: 
                         stack = [0] -> then x=3 -> then set parent_dsu[0]=3 -> returns 3.
                rv = find(2): returns 2.
                Then create new node 4:
                    left_child[4]=3, right_child[4]=2, sz[4]=2+1=3.
                    parent_dsu[3]=4, parent_dsu[2]=4, parent_dsu[4]=4.

            Then root=4.

            DFS:
                Start at 4: 
                    left_child=3, right_child=2.
                    a= sz[3]=2, b=sz[2]=1, denom=3.
                    left_win = 2 * inv(3) mod mod
                    right_win = 1 * inv(3) mod mod

                Then for left child (3): 
                    at node3: 
                        left_child=0, right_child=1.
                        a=1, b=1 -> denom=2, left_win = 1 * inv(2) mod mod, right_win = 1 * inv(2) mod mod.

                    Then for 0: 
                         accum = 2/3 + 1/2
                    For 1:
                         accum = 2/3 + 1/2?   # but note: at node4, the left team (which is the entire team of 0 and 1) wins? Then both 0 and 1 get 2/3? Then at node3: 
                             0: in the left team: wins with 1/2? 
                             1: in the right team: wins with 1/2? 
                    Then for 2: 
                         accum = 1/3? 

            So expected wins: 
                player0: 2/3+1/2 = 7/6? 
                player1: 2/3+1/2 = 7/6?
                player2: 1/3?

            But the total wins: 2 matches. So total expected wins = 7/6+7/6+1/3 = (7+7+2)/6 = 16/6 = 8/3, which is 2? -> 8/3 ≈ 2.666... which is not 2.

            What's wrong?

            Actually, the match at node4: the team of 0 and 1 (which is the left child) wins with probability 2/3? Then that win is counted for both 0 and 1? But the problem: the win is for the team. The team wins the match. Then the entire team gets one win? Then each player in the team gets one win? 

            That would be: each player in the winning team gets one win? Then the expected wins for player0: 
                wins from match1: if the team of 0 wins? -> 1/2? 
                wins from match2: if the merged team wins? -> 2/3? 
                So total: 1/2 + 2/3 = 7/6? 
            Similarly for player1: 1/2 + 2/3 = 7/6? 
            For player2: 
                wins from match2: if the team of 2 wins? -> 1/3? 
                But wait: the team of 2 is the second team in match2? and it wins? Then player2 gets one win? 
                And player2 did not participate in match1? 
            So total wins: 7/6 + 7/6 + 1/3 = 7/6+7/6+2/6 = 16/6 = 8/3? but there are only 2 wins.

            How can the expected wins be 8/3? 

            The problem: the win is for the team, but the team wins the match. The entire team gets one win? Then the expected wins for a player is the sum of the probabilities of his team winning the matches that the team participates in. 

            This is additive: 
                Match1: 
                    player0: wins with probability 1/2 -> expected wins 1/2
                    player1: wins with probability 1/2 -> expected wins 1/2
                Match2:
                    The two teams: 
                         Team0: players0 and 1 -> wins with probability 2/3? Then the entire team wins -> then each player in that team gets a win? 
                         Team1: player2 -> wins with probability 1/3? Then player2 gets a win? 
               

## Final Solution

