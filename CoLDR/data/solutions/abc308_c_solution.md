# Solution for abc308_c.md

Generated at: 2025-07-22 06:27:03
Model: deepseek-reasoner

## Reasoning Process

Alright, let's tackle this coin toss success rate sorting problem. 

First, I need to understand the problem clearly. We have N people, each with a certain number of heads (A_i) and tails (B_i) from their coin tosses. The success rate for person i is defined as A_i / (A_i + B_i). Our goal is to sort these people in descending order of their success rates. If two people have the same success rate, we break the tie by sorting them in ascending order of their assigned numbers (which are 1 through N).

Constraints are important here: N can be up to 200,000, and A_i and B_i can be up to 10^9. Also, each person has at least one toss (A_i+B_i >=1). All values are integers.

Given the large input size, we need an efficient solution. A brute force approach might work for small inputs but could be too slow for the upper limits.

Now, let's think about how to compare two people's success rates. For person i and j, we want to know if i has a higher success rate than j. That is, is A_i/(A_i+B_i) > A_j/(A_j+B_j)? 

But comparing fractions directly using floating-point division might be problematic because of precision issues, especially since A_i and B_i can be as large as 10^9. For example, consider two very close fractions: 10^9/(2*10^9) and (10^9-1)/(2*10^9-1). These are both approximately 0.5, but if we use floating point, we might lose precision and incorrectly sort them. Also, the problem states that all input values are integers, so we should stick to integer arithmetic to avoid precision problems.

So, how can we compare two fractions without using floating point? We can cross-multiply. The condition A_i/(A_i+B_i) > A_j/(A_j+B_j) is equivalent to:

A_i * (A_j + B_j) > A_j * (A_i + B_i)

Let me expand that:

Left side: A_i * A_j + A_i * B_j
Right side: A_j * A_i + A_j * B_i

Subtract A_i*A_j from both sides:

A_i * B_j > A_j * B_i

So, the condition simplifies to: A_i * B_j > A_j * B_i.

Wait, that's interesting. So person i has a higher success rate than person j if and only if A_i * B_j > A_j * B_i. 

But what if the success rates are equal? Then A_i * B_j = A_j * B_i. In that case, we break the tie by the person's number: lower number comes first (but note: in the problem, for ties, we break by ascending order of assigned numbers, meaning lower number should come before higher number).

However, when sorting in descending order of success rate, if two have the same rate, we want the person with the smaller index to appear first. But in the sorted list, if we are going from highest to lowest, when two are equal, the one with the smaller number should come before the one with the larger number? Actually, looking at the example: input 2: "1 3" and "2 6" have the same rate (1/4 = 2/6=1/3) so we output "1 2". So in the sorted list, person 1 comes before person 2 because 1<2.

But in the sort, if we are doing descending by success rate, when the success rates are equal, we want ascending by index. So, in the sort function, we can define a key that first considers the success rate (descending) and then the index (ascending).

Alternatively, we can define a custom comparator. In Python, we can use a tuple in the sort key. Since we want descending order by success rate, we can use negative of the success rate? But that might not work well with fractions. Alternatively, we can represent the success rate in a way that the natural tuple sort gives the desired order.

Another idea: since we have a way to compare two fractions without division (using cross multiplication), we can define a key function that returns a tuple. But how?

Alternatively, we can define a custom comparator function that, given two people i and j (with their A_i, B_i and index i, and j's A_j, B_j and index j), returns:

- If A_i * (A_j+B_j) != A_j * (A_i+B_i) [which is equivalent to A_i*B_j != A_j*B_i as above], then compare by the fraction.
But actually, we can compare by the cross product: if A_i * B_j > A_j * B_i, then i has higher success rate than j, so i should come before j. If it's less, then j should come before i. If equal, then compare by index: lower index should come first (so if i < j, i should come before j).

Wait, but in the sorted list, we are going from highest to lowest. So, in the comparator function, if we are comparing person i and j:

We want to return -1 if i should come before j (meaning i has higher success rate, or same rate and lower index), and 1 if i should come after j.

But actually, in Python's sorted function, we can use a key that returns a tuple. However, using a comparator function for 200,000 items might be inefficient because each comparison is O(1) but the sort is O(N log N) with each comparison being more expensive. But actually, the cross multiplication is O(1) per comparison, so overall O(N log N) which is acceptable for N=200,000? Probably, but we can avoid a custom comparator by using a key that is a tuple of the success rate representation and the index.

But how to represent the success rate without floating point? We can represent it as a rational number: (A_i, A_i+B_i) but then comparing two fractions (a, b) and (c, d) is equivalent to comparing a*d and c*b. But we can use this in the key? Actually, we can define the key as: (-success_rate_representation, index). But wait, the negative might not work.

Alternatively, we can note that for descending order of success rate, we can use a key that is (success_rate, ...) and then reverse? Or we can use negative of the success rate? But with fractions, that's tricky.

Better approach: we can use a key that is a tuple of two elements:

1. The "value" for the success rate in such a way that a higher success rate gets a lower value? Or we can use negative, but then we have to be cautious with the multiplication.

Alternatively, we can use the fact that:

We want to sort by success rate descending, then by index ascending.

We can represent each person by a key: ( -A_i/(A_i+B_i), i) and then sort in ascending order? Then the negative of the success rate would make higher success rates become more negative? Actually, that might work with floating point but we want to avoid floating point.

So, without floating point, we can represent the key as:

key(i) = ( - (A_i * (some scaling)) / (A_i+B_i) ) ... but again, we are back to fractions.

Alternatively, we can use the cross multiplication in a custom key? Actually, in Python, the sorted function doesn't support that directly. But we can use a key that is a tuple of:

( - (A_i * something), i) ... that doesn't help.

Another idea: we can use the fraction in the form of a tuple (A_i, A_i+B_i) and then use a custom comparator? But the sorted function can take a key that is a tuple, but the tuple of two numbers doesn't automatically compare as fractions.

But wait, we can use the decimal value? But with integers as big as 10^9, the decimal might lose precision. For example, 10^9/(2*10^9) is 0.5, but 333333333/999999999 is 1/3, which is periodic. So we cannot rely on floats.

Therefore, we need a way to compare two fractions without converting to float. One common method is to use the cross multiplication during the sort. But in Python, the sorted function can take a key that returns a tuple, but that tuple must be comparable. We can represent the fraction in a way that the tuple (numerator, denominator) doesn't naturally compare correctly. 

Alternatively, we can use a key that returns an object that defines its own comparison? But that would require a custom class with __lt__ method. Then we can use that as the key? Actually, we can use a key that is a tuple of two elements: the first element is an object that represents the fraction, and the second element is the index. Then, when comparing two such tuples, the first elements (fraction objects) are compared by the cross multiplication, and then the second element (index) is compared.

But actually, the sorted function in Python can take a key function that returns a tuple of values. The tuple comparison is lexicographical. So if we can express the success rate as a value that can be compared without a custom comparator per element, that would be efficient.

But how to express the fraction in a comparable form without per-comparison computation? 

Wait, we can use the idea: instead of storing the fraction, we can store the value of A_i * (A_j+B_j) for each? No, because that depends on j.

Alternatively, we can use a transformation. Note that the fraction A_i/(A_i+B_i) is equivalent to 1 - B_i/(A_i+B_i). So sorting by descending success rate is the same as ascending by B_i/(A_i+B_i). Then we can compare two fractions: B_i/(A_i+B_i) and B_j/(A_j+B_j). Similarly, we can compare by cross multiplication: B_i*(A_j+B_j) < B_j*(A_i+B_i) for ascending order.

But then we have to break ties: if the fractions are equal, we break by ascending index. So the key would be: ( B_i/(A_i+B_i) , i) in ascending order? But again, same floating point issue.

Alternatively, we can use the key as: ( (A_i+B_i) - A_i = B_i? No, that's not the fraction.

Wait, we can express the key as a tuple: ( A_i * (A_j+B_j) - A_j*(A_i+B_i) )? But that expression is relative to two elements.

So, I think the most straightforward way is to use a custom key function that returns a tuple where the first element is the negative of the success rate? But without floating point, we cannot.

Alternatively, we can return a tuple ( -A_i, A_i+B_i, i) but that doesn't represent the fraction.

Wait, there's a standard technique for sorting fractions without floating point: using continued fractions? Or using logarithms? But logarithms also have precision issues.

Actually, the standard way is to use a custom comparator that uses cross multiplication. Since the numbers can be as large as 10^9, the product can be as large as 10^18, which in Python is acceptable because Python integers are arbitrary precision and comparisons of such integers are O(1) for practical purposes.

So, for two people i and j, we want to compare:

if A_i/(A_i+B_i) > A_j/(A_j+B_j) then i comes before j.
else if equal, then if i < j, i comes before j.

But how to express this in a key? Actually, we can use the fact that we can represent the fraction as a tuple that is compared by cross multiplication? 

But the tuple would have to be (success_rate_representation, index). The success_rate_representation must be such that when two representations are compared, we do the cross multiplication. But we cannot do that without a custom comparator.

So, I think we have two options:

Option 1: Use a custom comparator function and pass it to sorted. However, in Python, the comparator function (using cmp_to_key from functools) is less efficient than the key function because it calls the function O(N log N) times, each time with two elements. But for N=200,000, O(N log N) is about 200000 * 18 = 3.6e6 comparisons, which might be acceptable in PyPy or PyPy with PyPy, but in Python it might be borderline in speed. However, we must avoid floating point.

Option 2: Find a way to represent the fraction in a key that doesn't require per-comparison computation. 

There's a known technique: we can use the fact that the fraction A_i/(A_i+B_i) is equivalent to (A_i, A_i+B_i). Then, we can normalize the fraction? But that doesn't help because the denominators are different. 

Alternatively, we can use the key: ( -A_i, A_i+B_i) but that doesn't work: for example, person1: (1,3) -> 1/4, person2: (2,2) -> 2/4=1/2. Then -1 and 4 vs -2 and 4: -1 > -2, so person1 would come after person2? But we want descending by success rate, so person2 should come first. So if we use (-A_i, A_i+B_i) then we are not correctly representing the fraction.

But note: the fraction is A_i/(A_i+B_i). We want to sort by that descending. We can represent the fraction as a rational number and use the product to avoid division. 

Actually, we can define a key function that returns an object that implements __lt__ by doing cross multiplication. This way, when sorted, it will use our custom __lt__ for the fraction part. Then we can have the key be (FractionKey(A_i, A_i+B_i), i). 

But that might be acceptable. How?

Let me define a class for the fraction:

class FractionKey:
    def __init__(self, a, b):
        self.a = a
        self.b = b   # here, the fraction is a/(a+b)? Actually, no: the success rate is a/(a+b). But we can store numerator a and denominator s = a+b.

    Then, for two fractions f1 = (a1, s1) and f2 = (a2, s2), we want f1 > f2? But for descending order, we want the larger fraction to be considered "smaller" in the key? Or we can have the key return an object that when compared, returns the reverse.

Actually, we want the list sorted in descending order by the fraction. So in the key, if we return an object that for the fraction part, when we compare two, we return f1 < f2 meaning that if the fraction of the first is greater than the second, then the object for the first should be less than the object for the second? That might be confusing.

Alternatively, we can define the key to return (success_rate, index) but with the success_rate represented in a way that a higher rate is "less" than a lower rate? Then we can sort in ascending order. But then the index part: for the same success rate, we want ascending index, so the index should be in ascending order? Then the key would be: (custom_representation, index) and then we sort in ascending order? Then the entire list would be from high success rate to low.

How to do that? 

Define the custom_representation such that for two fractions:

if a1/s1 > a2/s2, then we want custom_representation for the first to be less than that for the second? 

But we can do:

We can define the custom_representation as (-a1 * s2) or something? Actually, no.

Alternatively, we can use the idea of negative of the fraction. But without floating point, we can define:

key_i = ( - (a_i * some_denom), index_i ) 

But that doesn't help.

Wait, we can represent the fraction a_i/s_i (where s_i = a_i+b_i) as (-a_i, s_i) and then sort by that? Then, (-a1, s1) and (-a2, s2): if -a1 < -a2, that is a1>a2, then (-a1, s1) is less than (-a2, s2) only if a1>a2? But that ignores the denominator.

Example: 
Person1: (1,3) -> a1=1, s1=4 -> (-1,4)
Person2: (2,2) -> a2=2, s2=4 -> (-2,4)

We want person2 to be before person1 because 2/4 > 1/4. So in the key, we want (-2,4) to be less than (-1,4). And that is true: -2 < -1. So if we use the tuple (-a_i, s_i), then for person2: (-2,4) and person1: (-1,4): since -2 < -1, person2 comes before person1. But what about two different denominators?

Example: 
Person3: (3,1) -> a3=3, s3=4 -> (-3,4)
Person4: (1,1) -> a4=1, s4=2 -> (-1,2)

Person3: 3/4=0.75, person4: 1/2=0.5. So we want person3 to come before person4. With key: person3: (-3,4), person4: (-1,2). -3 < -1, so person3 comes before person4. That's correct.

But wait, what about fractions that are not so straightforward? 
Example: 
Person5: (2,3) -> 2/5=0.4 -> key: (-2,5)
Person6: (1,2) -> 1/3≈0.333 -> key: (-1,3)
We want person5 to come before person6. Keys: (-2,5) and (-1,3). Since -2 < -1, person5 comes before person6. Correct.

But what if the fractions are equal? 
Person7: (1,3) -> 1/4=0.25 -> key: (-1,4)
Person8: (2,6) -> 2/8=0.25 -> key: (-2,8)
We want the tie broken by the index: if person7 has index 1, person8 has index 2, then we want person7 to come before person8. But with the key: (-1,4) and (-2,8): -1 > -2, so (-1,4) is greater than (-2,8) -> so person7 would come after person8? That's the opposite of what we want.

So this representation (-a_i, s_i) doesn't work for ties? Actually, in the case of equal success rate, we have a1/s1 = a2/s2, so a1*s2 = a2*s1. But the keys (-a1, s1) and (-a2, s2) are different. We want that when the fractions are equal, the key for the two should be considered equal? But then we can use the index. But in the tuple, if the first element is the same, then we use the second. But here, the first element is -a_i, which is not the same for equal fractions. 

In the example: person7: (-1,4) and person8: (-2,8). The first components are -1 and -2, which are different. So the keys are different even though the fractions are equal.

So this method fails for equal fractions.

Alternative: use the fraction representation as (a_i, s_i) and then define a custom key class that implements __lt__ to do the cross multiplication.

Let me define:

class FractionKey:
    def __init__(self, a, s, idx):
        # We don't need idx here because we'll use it in the outer tuple
        self.a = a
        self.s = s   # s = a+b

    def __lt__(self, other):
        # We want to compare two fractions: self = a1/s1, other = a2/s2
        # We need to check if a1/s1 < a2/s2? But no: we want descending order by the fraction. 
        # Actually, for the sort, we are going to use the key: (FractionKey(...), index)
        # And then for two elements, if the FractionKey is less, then it comes first? But we want higher fraction to come first.
        # So if a1/s1 > a2/s2, then we want self to be considered less than other? 
        # Because when we sort, if self < other, then self comes before other -> which is what we want: higher fraction comes first.

        # So condition: return whether a1/s1 > a2/s2? But we can compute without division: a1 * s2 > a2 * s1.

        # However, we have to be cautious: if we define __lt__, then self < other returns True if self is less than other. But we want that if self has a higher success rate, then it is "less" in the ordering of the key? 

        # Actually, the key is for the entire element. We want the list to be sorted in descending order by success rate. We can achieve by having the key return an object that, when compared, returns True when the fraction is greater, meaning that for two fractions f1 and f2, if f1 > f2, then key1 < key2. So in __lt__:

        # We want: if self is greater than other (meaning self has higher success rate) then __lt__ should return False? Actually, no: the __lt__ defines the natural order. But we can define:

        # We want: if self has a higher success rate than other, then self should be considered less than other? Then in the sorted list, self will come before other.

        # So: 
        # if self.a / self.s > other.a / other.s, then we want self < other to return True? 
        # But we can compare by: self.a * other.s > other.a * self.s

        # So:
        # return self.a * other.s > other.a * self.s

        # However, note: if we do that, then when self is greater, we return True, meaning self is less than other? That is, self comes before other. 

        # But wait: __lt__ is for the < operator. So if we have:
        # self < other: we are returning self.a * other.s > other.a * self.s
        # That means: when self has a greater fraction, we say self is less than other? That might be confusing.

        # Actually, we want: 
        # if self has a higher success rate, then self should be considered "less" than other so that it comes first. So in the sorted order, we want the higher fraction to have a lower key value.

        # So in __lt__, we want to return True when the current object (self) has a higher success rate than other? But that's not exactly. 

        # Let me write:

        # We have two fractions: f1 = self, f2 = other.
        # We want: if f1 > f2, then we want self < other to be True? So that in the sorted list, f1 comes before f2.

        # So: 
        # __lt__(self, other) should return True if we want self to come before other? But no: __lt__ defines the natural order. Actually, the sorted function will put element x before y if key(x) < key(y). 

        # Therefore, if we want x to come before y (because x has a higher success rate), then we need key(x) < key(y). 

        # How do we define the key? The key for x is (FractionKey(x), x_index). The FractionKey for x should be such that when x has a higher success rate than y, then FractionKey(x) < FractionKey(y). 

        # Therefore, in FractionKey.__lt__: 
        # if self (which is for x) has a higher success rate than other (which is for y), then we want self < other to be True.

        # How to check: self has higher success rate: 
        # self.a/self.s > other.a/other.s  => self.a * other.s > other.a * self.s.

        # But note: if we do that, we might get large numbers, but that's acceptable.

        # However, what if there is overflow? The numbers are up to 10^9, so the product is up to 10^18, which in Python is acceptable (Python integers are arbitrary size, but the operations on integers of 10^18 are O(1) in practice for comparison).

        # So in __lt__:
        # return self.a * other.s > other.a * self.s

        # But wait, what if the fractions are equal? Then we don't want to return True or False here, because then we want the tie broken by the index. However, in the key, we have a tuple: (FractionKey, index). So if the FractionKey is equal (meaning __lt__ returns False and the reverse __lt__ also returns False), then the next element in the tuple (the index) is compared. And we want the index to be in ascending order? 

        # Actually, for the entire key, we return (fraction_key, index). Then, if fraction_key for two items are considered equal (neither __lt__ returns True for (a,b) nor for (b,a)), then the second element (index) is compared. And we want ascending order of index. But in the tuple, if the fraction_keys are equal, then the tuple (fraction_key1, i) and (fraction_key2, j) will be compared by the next element: so we want that if i < j, then (fraction_key1, i) < (fraction_key2, j). 

        # Therefore, we set the key for each person i as: (FractionKey(A_i, s_i), i)

        # Then, the tuple comparison: first compare the two FractionKeys. If they are not equal, then order by that. If equal, then compare i and j. And we want ascending by i? So if i < j, then (fk, i) < (fk, j). That's the natural behavior.

        # Therefore, the plan:

        # For each person i, we have (A_i, B_i), and index i (remember: the person's number is i+1? Actually, no: the input order is person1: A1,B1; person2: A2,B2; ... and we must output the person's number.

        # But in the function, we are given a list of [A_i, B_i] for i from 0 to N-1, and the person's number is i+1. 

        # So we can create an array of tuples: (fraction_key, index) where index = i (and then the person number is i+1). But actually, we can store the person number as the index? Actually, the output must be the person's number. So we can store the person number (which is i+1) in the tuple.

        # However, for the key, we need to sort by the fraction and then by the person number (in ascending order for the same fraction). But note: the problem says "with ties broken in ascending order of their assigned numbers". The assigned numbers are 1,2,...,N.

        # So in the key, the second element should be the person number? Or the index? Actually, the person number is the identifier. But we are processing the input in order: the first input is person1, the second is person2, etc. So the assigned number is the index in the input plus one.

        # Therefore, we can create a list of (fraction_key, person_id) for each person, and then sort by the key: (fraction_key, person_id) but with fraction_key defined to be "less" when the success rate is higher, and then for the same fraction_key, we want person_id in ascending order.

        # But in the tuple (fraction_key, person_id), if we define fraction_key so that it is compared as above, then for the same fraction_key, the natural comparison of person_id is ascending? That is, (fk, 1) < (fk, 2) because 1<2. So that's what we want.

        # However, note: in the tuple, if the fraction_keys are equal, then the person_id is compared. And we want ascending order, which is the natural order. So yes.

        # But how do we build fraction_key? We store (a, s) = (A_i, A_i+B_i). Then, in the __lt__ method, we compare two fraction_key objects by: 
        #   self.a * other.s > other.a * self.s   --> then return True for self < other.

        # However, wait: we want that if self has a higher success rate, then self is less than other. So:

        #   if self.a/self.s > other.a/other.s, then we want self < other to return True.

        # And that condition is equivalent to: self.a * other.s > other.a * self.s.

        # Therefore, in the __lt__ method of FractionKey:

        #   def __lt__(self, other):
        #       return self.a * other.s > other.a * self.s

        # But wait, is that correct? Let me test with two examples.

        # Example 1: 
        #   person1: (1,3) -> a=1, s=4
        #   person2: (2,2) -> a=2, s=4
        #   We want person2 to come before person1 -> so key for person2 should be less than key for person1.
        #   For person1: FractionKey(1,4)
        #   For person2: FractionKey(2,4)
        #   Now, when comparing person2 and person1: 
        #       self = person2, other = person1: 
        #       self.a * other.s = 2*4 = 8
        #       other.a * self.s = 1*4 = 4
        #       8 > 4 -> returns True.
        #   So __lt__ returns True for person2 < person1? That means in sorted order, person2 comes before person1? Yes, that's what we want.

        # Example 2: 
        #   person3: (1,3) -> a=1, s=4
        #   person4: (2,6) -> a=2, s=8 -> same fraction: 1/4 and 2/8=1/4.
        #   Then, when comparing person3 and person4: 
        #       self = person3: (1,4), other=person4: (2,8)
        #       self.a * other.s = 1*8 = 8
        #       other.a * self.s = 2*4 = 8
        #       8 > 8 is False -> returns False.
        #   Then, check the reverse: person4 < person3? 
        #       self = person4, other=person3: 2*4 =8, 1*8=8 -> 8>8 is False -> also False.
        #   So the two FractionKeys are considered equal? Then the next element in the tuple (the person_id) is compared. 
        #   If we have for person3: (FractionKey(1,4), 1) and for person4: (FractionKey(2,8), 2), then when comparing these tuples: first elements are equal, then compare 1 and 2 -> 1<2, so person3 comes before person4. That is correct.

        # But wait: in the problem, the input "2 6" is the second person? Actually, in the example input: 
        #   "2
        #    1 3
        #    2 6"
        #   So person1: (1,3) -> id=1, person2: (2,6) -> id=2.
        #   Then we output "1 2". So in the sorted list, person1 comes first, then person2. 
        #   In our keys: 
        #       person1: key = (FractionKey(1,4), 1)
        #       person2: key = (FractionKey(2,8), 2)
        #   When comparing, the FractionKeys are equal, then compare the id: 1<2, so (fk1,1) < (fk2,2) -> so person1 comes before person2. Correct.

        # Example 3: 
        #   person5: (3,1) -> a=3, s=4 -> key=(3,4)
        #   person6: (1,1) -> a=1, s=2 -> key=(1,2)
        #   Compare: self=person5: 3*2=6, other=person6: 1*4=4 -> 6>4 -> returns True? Then person5 < person6? So in sorted order, person5 comes before person6. Correct.

        # However, what if we have two fractions that are not equal but cross multiplication gives equality? Actually, the fractions are defined by a/s, and if a1/s1 = a2/s2, then a1*s2 = a2*s1. So the __lt__ will return False and the reverse will return False, so they are considered equal. So that's correct.

        # But what if we have a fraction that is not in reduced terms? That doesn't matter because we are not reducing. The cross multiplication is valid regardless.

        # So this method should work.

        # However, note: the __lt__ method must satisfy the requirements for a total ordering? We have to be cautious: if we have three fractions, we must have transitivity. But the cross multiplication defines a total order? Actually, no: because if a1/s1 > a2/s2 and a2/s2 > a3/s3, then a1/s1 > a3/s3? Yes, because the fractions are rational numbers and the usual order is total. But the way we define __lt__: 
        #   self < other  <=> self.a * other.s > other.a * self.s
        # This is equivalent to the fraction self being greater than other. So the relation we are defining for FractionKey is: 
        #   key1 < key2  if and only if the success rate of the person for key1 is greater than that for key2.

        # This is a strict weak ordering? Actually, it is a total order? Or is it? 

        # Actually, the set of rational numbers is totally ordered. But our definition of __lt__ is: 
        #   key1 < key2 iff success_rate1 > success_rate2.
        # This is a total order? Actually, no: because if we have three keys: 
        #   k1: (3,4) -> 3/4
        #   k2: (2,3) -> 2/3
        #   k3: (1,2) -> 1/2
        # Then: 
        #   k1 < k2? Because 3/4=0.75 > 2/3≈0.666 -> so k1 < k2 is True.
        #   k2 < k3? Because 2/3 > 1/2 -> True.
        #   Then k1 < k3? 3/4>1/2 -> True. So transitivity holds.

        # Also, if two are equal, then neither k1<k2 nor k2<k1. So it's a total order? Actually, the relation defined by the fraction order is total, and we are just reversing it: so the keys are ordered in the reverse of the natural fraction order. 

        # But note: the __lt__ method should define a strict weak ordering that is consistent. The problem is: if we have two fractions that are not comparable by the cross product? But they are rationals, so they are comparable.

        # Therefore, we can use this FractionKey class.

        # Implementation:

        # Steps:
        #   Precompute for each person i: 
        #       a = A_i
        #       s = A_i + B_i
        #   Then, create a list of tuples: (fraction_key, person_id) where fraction_key = FractionKey(a, s) and person_id = i+1
        #   Then, sort this list by the tuple: because the tuple will be compared lexicographically: first the fraction_key (using __lt__), then the person_id (which we want ascending).
        #   Then, extract the person_id from the sorted list.

        # However, we must be cautious: the __lt__ method must not have integer overflow? The numbers are up to 10^9, so the product is up to 10^18, which in Python is acceptable.

        # But note: we are storing a and s, which are integers. 

        # Alternatively, we can avoid creating a class by using a tuple and a custom key function? Actually, we can use the key parameter to return a tuple that we define in a special way? But without the class, we cannot override __lt__.

        # Another alternative: we can use the key function to return (success_rate_represented_as_float, i) but that has precision issues.

        # Or, we can use the key function to return a tuple: 
        #   (- (A_i / (A_i+B_i)), i)   -> but then we use float and precision might be lost.

        # Given the constraints (A_i and B_i up to 10^9), the floating point might not be precise enough. For example, the difference between two fractions might be very small. The problem even has an example: 
        #   Example 3: 
        #       999999999 1000000000
        #       333333333 999999999
        #       1000000000 999999997
        #       999999998 1000000000
        #   We must output: 3 1 4 2

        # Let me compute the fractions for example 3:

        # Person1: 999999999/(999999999+1000000000) = 999999999/1999999999 ≈ 0.49999999975
        # Person2: 333333333/(333333333+999999999) = 333333333/1333333332 ≈ 0.249999999...
        # Person3: 1000000000/(1000000000+999999997) = 1000000000/1999999997 ≈ 0.50000000075
        # Person4: 999999998/1999999998 ≈ 0.499999999

        # So the order: 
        #   person3: ~0.50000000075
        #   person1: ~0.49999999975
        #   person4: ~0.499999999
        #   person2: ~0.25

        # So output: 3, 1, 4, 2.

        # If we use float, can we represent these fractions exactly? 
        #   Python float has about 15-16 decimal digits of precision. The numbers here are 10^9, so the quotient has about 9-10 digits before the decimal and then 9-10 after? Actually, the quotient is between 0 and 1. 

        # But the difference between person3 and person1 is about 1.5e-9. A float has about 15-16 significant digits, so 1e-9 is representable, but we have to be cautious: 
        #   person3: 1000000000/1999999997 = 0.500000000750000...
        #   person1: 999999999/1999999999 = 0.499999999750000... 
        #   The difference is about 1e-9, which is within the precision of float? Actually, for numbers around 0.5, the absolute precision of float is about 1e-15? But the relative precision is. However, the absolute difference is 1e-9, which is representable. 

        # But let me test in Python:

        #   a = 1000000000
        #   b = 999999997
        #   rate3 = a/(a+b)  # 1000000000/1999999997
        #   a1 = 999999999
        #   b1 = 1000000000
        #   rate1 = a1/(a1+b1) # 999999999/1999999999

        #   rate3 - rate1 should be positive.

        #   But when I compute:

        #   rate3 = 1000000000 / 1999999997
        #   rate1 = 999999999 / 1999999999

        #   Let me compute the difference:

        #   rate3 - rate1 = (1000000000 * 1999999999 - 999999999 * 1999999997) / (1999999997 * 1999999999)

        #   Numerator: 1000000000*1999999999 - 999999999*1999999997
        #   = 1000000000*(2000000000-1) - 999999999*(2000000000-3)
        #   = 1000000000*2000000000 - 1000000000 - (999999999*2000000000 - 3*999999999)
        #   = 2000000000000000000 - 1000000000 - (1999999998000000000 - 2999999997)
        #   = 2000000000000000000 - 1000000000 - 1999999998000000000 + 2999999997
        #   = (2000000000000000000 - 1999999998000000000) + (2999999997 - 1000000000)
        #   = 2000000000 + 1999999997
        #   = 4000000000 - 3? Actually, wait: 2000000000 + 1999999997 = 3999999997

        #   Denominator: 1999999997 * 1999999999 = about 4e18.

        #   So the difference is 3999999997 / (1999999997 * 1999999999) which is about 4e9 / 4e18 = 1e-9. 

        #   In Python:

        #   rate3 = 1000000000 / 1999999997
        #   rate1 = 999999999 / 1999999999
        #   diff = rate3 - rate1   # should be positive and about 1e-9.

        #   But will float represent that without rounding to 0? 

        #   Let me compute:

        #   >>> a = 1000000000
        #   >>> b = 999999997
        #   >>> rate3 = a / (a+b)
        #   >>> a1 = 999999999
        #   >>> b1 = 1000000000
        #   >>> rate1 = a1 / (a1+b1)
        #   >>> rate3
        #   0.5000000007500001
        #   >>> rate1
        #   0.49999999975000006
        #   >>> rate3 - rate1
        #   1.000000000999917e-09

        #   So yes, the difference is positive and representable. 

        #   But what about person4: 999999998/1999999998 = 0.4999999990000000

        #   rate4 = 999999998/1999999998
        #   Then rate1 - rate4 = 0.49999999975000006 - 0.4999999990000000 = 7.5e-10, which is also representable.

        #   So in this example, using float might work. 

        #   But is it guaranteed for all inputs? Consider two fractions that are very close, say:

        #   a = x, b = 2*x+1, and a'=x+1, b'=2*x+3. Then the fractions are x/(3*x+1) and (x+1)/(3*x+4). The difference is:

        #   (x*(3*x+4) - (x+1)*(3*x+1)) / (...) = (3x^2+4x - (3x^2+x+3x+1)) = (3x^2+4x - 3x^2-4x-1) = -1 / denominator. 

        #   So one is less than the other by 1/denom. But if x is large, say 10^9, then the difference is about 1/(3*10^9)^2 = 1e-18, which is beyond the precision of float (which has about 15-16 decimal digits). 

        #   Therefore, if we have two fractions that are very close (differing by less than 1e-15), the float might round them to the same value. Then the tie would be broken by the index, which is not correct because one is actually greater.

        # Example: 
        #   person i: a=10**9, b=2*10**9+1 -> fraction = 10**9/(3*10**9+1) = 1/(3+1e-9)
        #   person j: a=10**9+1, b=2*10**9+3 -> fraction = (10**9+1)/(3*10**9+4) = (1+1e-9)/(3+4e-9)

        #   Then the true fractions: 
        #       1/(3+1e-9) = 1/3.000000001 ≈ 0.3333333322222222
        #       (1+1e-9)/(3+4e-9) = 1.000000001/3.000000004 ≈ 0.3333333326666667

        #   So j has a higher success rate than i? Let me compute:

        #   i: a_i = 10**9, s_i = 3*10**9+1
        #   j: a_j = 10**9+1, s_j = 3*10**9+4

        #   Compare: a_i * s_j = 10**9 * (3*10**9+4) = 3*10**18 + 4*10**9
        #            a_j * s_i = (10**9+1)*(3*10**9+1) = 10**9*3*10**9 + 10**9*1 + 1*3*10**9 + 1*1 = 3*10**18 + 4*10**9 + 1
        #   So a_i * s_j = 3e18 + 4e9
        #      a_j * s_i = 3e18 + 4e9 + 1
        #   So a_i * s_j < a_j * s_i  => j has a higher success rate.

        #   But if we compute with float:
        #      rate_i = 1e9 / (3e9+1) = 0.3333333322222222
        #      rate_j = (1e9+1) / (3e9+4) = 0.3333333326666667
        #   Then in float, we can see that rate_j > rate_i.

        #   But what if the difference was 1e-18? Then float might round both to the same value. 

        #   Actually, the gap between the two fractions is about 4.44e-10, which is above the absolute resolution of float for numbers around 0.333333332? The float has 52 bits of mantissa, so the smallest representable difference around 0.3 is about 0.3 * 2**(-52) ≈ 0.3 * 2e-16 = 6e-17. And 4.44e-10 is greater than that, so it should be representable. 

        #   But if we have two fractions that are closer than 1e-16? For example, if we have:

        #   a_i = x, s_i = s
        #   a_j = x+1, s_j = s+1

        #   Then the difference might be very small. 

        #   Therefore, to be safe, we should avoid floating point.

        # Conclusion: we should use the FractionKey class with integer cross multiplication.

        # However, we can also use the key function with a tuple that does:

        #   key = ( - (A_i * (A_j+B_j) ) / (A_j * (A_i+B_i)) ) ... no, that's per pair.

        # Alternatively, we can use the decimal with high precision? But that might be slower and use more memory.

        # So I'll go with the FractionKey class.

        # Implementation:

        #   class FractionKey:
        #       __slots__ = ('a', 's')   # for efficiency
        #       def __init__(self, a, s):
        #           self.a = a
        #           self.s = s
        #       def __lt__(self, other):
        #           # We want to return True if self has higher success rate than other, because then self is "less" than other in the key.
        #           # But note: what if s or other.s is zero? The problem says A_i+B_i>=1, so s>=1 and other.s>=1. So no division by zero.
        #           return self.a * other.s > other.a * self.s

        #   Then, create list: 
        #       arr = []
        #       for i in range(N):
        #           a, b = people[i]
        #           s = a + b
        #           arr.append( (FractionKey(a, s), i+1) )
        #       arr_sorted = sorted(arr)   # because we want by key ascending, and our key is defined so that higher success rate gives a "smaller" key.
        #       Then extract the i+1 from each element.

        #   But note: the sorted function by default sorts in ascending order. And we want the list from highest success rate to lowest. And our key is such that a higher success rate is represented by a FractionKey that is "less" than one with a lower success rate. So when we sort the list of tuples in ascending order, the element with the smallest key (which is the highest success rate) comes first, then next, etc. So that's exactly what we want.

        #   And for ties in FractionKey (i.e., the FractionKey for two are equal), the next element in the tuple (the person_id) is compared in ascending order. And that's what we want.

        #   But note: the FractionKey class must be hashable? Not necessarily, because we are not using it in a set. But for sorting, we only need __lt__.

        #   However, the documentation says: The sort algorithm uses < for comparisons. So __lt__ is sufficient.

        #   But what if the two fractions are not comparable by __lt__ but are equal? Then we break by the next element. So it's fine.

        #   However, to be safe, we can also define __eq__? But the sort only uses __lt__.

        #   Alternatively, we can avoid the class by using a tuple and a key function that returns a tuple that we can compare without a custom class? 

        #   There's a known trick: we can use the sign of the cross product. But how?

        #   We want to sort by the fraction in descending order. We can use the key: ( -1, ... ) doesn't help.

        #   Alternatively, we can map the fraction to a value that we can compare? But we cannot.

        #   Another idea: we can use the key as a tuple: ( (A_i+B_i) - A_i, A_i+B_i, ... ) but that doesn't represent the fraction.

        #   We can also use the key: ( (A_i * 10**18) // (A_i+B_i), i)   -> but that uses integer division and might lose precision. And the numbers are huge.

        #   So I think the custom class is acceptable.

        #   However, we can also use the key function that returns a tuple: 
        #       key = ( -A_i/(A_i+B_i), i)   -> but that's float.

        #   Or: we can return a tuple that is (success_rate_desc_primary, i_asc_secondary) but without float.

        #   There is a trick: we can return ( (other.s * self.a - other.a * self.s) > 0 ) ... but that's not a value, it's a boolean.

        #   Actually, we can use the fact that we can return a tuple ( success_rate_represented_by_long_integer, i) but how?

        #   Notice: the condition for two fractions: 
        #       if a1/s1 > a2/s2, then we want (a1,s1) to come before (a2,s2). 
        #       We can map the fraction a/s to a * 10^something? But that something would have to be huge.

        #   Alternatively, we can use continued fraction expansion? But that's complex.

        #   Another idea: we can use the fact that the fraction a/s can be represented as (a, s) and then we can sort by the ratio in a way that uses integer comparison? 

        #   Actually, we can use the key: ( a_i * (10**18) // s_i, i)   -> but then we are doing integer division which truncates, and we lose precision. And the quotient might be huge: 10^9 * 10^18 = 10^27, which is acceptable in Python? But we are dividing by s_i which is at least 1, so the result is an integer of up to 10^27. But then the quotient might be an integer of 10^9, which is acceptable. But the problem is: the division is integer division, so we lose the fractional part. 

        #   We can use fractions.Fraction? But that might be slower and use more memory.

        #   Let me test with Fraction:

        #       from fractions import Fraction
        #       key = ( Fraction(A_i, A_i+B_i), i)
        #       but then we want descending by fraction, so we can use: (-Fraction, i) ? But Fraction is comparable.

        #   But the problem: creating Fraction objects for 200,000 items? And each Fraction does gcd? That might be O(log(min(a,s))) per fraction, so overall O(N log(min)), which is acceptable? 

        #   But the constraints: a and s up to 10^9, gcd is O(log(10^9)) which is about 30. So 200000*30 = 6e6, which is acceptable in Pyton? Probably.

        #   However, it might be slower than the cross multiplication in the __lt__ method.

        #   Also, the Fraction objects are comparable by cross multiplication internally. 

        #   So we can do:

        #       key = ( Fraction(-A_i, A_i+B_i), i)   # but negative fraction? Then we sort in ascending order? 

        #   Or: 
        #       key = ( - (A_i / (A_i+B_i)), i)   -> but then we use float? No, we can use Fraction and then take negative.

        #   Actually, we can do:

        #       key = ( Fraction(A_i, A_i+B_i).denominator, ...) no.

        #   Alternatively, we can do:

        #       key = ( -Fraction(A_i, A_i+B_i), i)   # then sort in ascending order: so the largest fraction (which becomes the most negative) comes first? Then the next.

        #   Example: 
        #       person2: 3/4 -> Fraction(3,4) -> key = (-3/4, 2)
        #       person3: 2/4 -> Fraction(2,4)=1/2 -> key = (-1/2, 3)
        #       Then in ascending order: (-3/4,2) < (-1/2,3) because -0.75 < -0.5 -> so person2 comes before person3. Correct.

        #   But then for equal fractions: 
        #       person1: 1/4 -> key = (-1/4, 1)
        #       person4: 2/8=1/4 -> key = (-1/4, 4)
        #       Then (-1/4,1) and (-1/4,4): in ascending order, 1<4, so (-1/4,1) comes first. Correct.

        #   And it uses integer arithmetic internally? The Fraction class uses integers.

        #   But the problem is the overhead: creating a Fraction for each of 200,000 items. Each Fraction does gcd? 

        #   We can test, but with 200,000 items, it might be acceptable in Pyton in C++ but in Python it might be borderline in speed? 

        #   Alternatively, we can use the key: ( - (A_i / (A_i+B_i) , i) as a tuple of floats, but we know that might be imprecise.

        #   Given the constraints, and the example with very close fractions, I think the custom class with cross multiplication is safer and might be faster.

        #   So I'll implement the custom class.

        #   But note: the __lt__ method is called O(N log N) times, and each call does two multiplications of integers of size up to 10^9, which is 10^18, and comparisons of integers of that size. How expensive is that? In Python, multiplication of two integers of size 10^9 is O(1) because it's done in hardware for numbers that fit in word size? But 10^9 is 30 bits, so 10^9 * 10^9 = 10^18 is 60 bits, which fits in one machine word (64 bits). So it should be fast.

        #   Therefore, the plan is:

        #       class FractionKey:
        #           __slots__ = ('a', 's')
        #           def __init__(self, a, s):
        #               self.a = a
        #               self.s = s
        #           def __lt__(self, other):
        #               # We want self to be considered < other if self has a higher success rate.
        #               # i.e., if self.a/self.s > other.a/other.s, then self < other returns True.
        #               return self.a * other.s > other.a * self.s

        #       Then:
        #       arr = []
        #       for i in range(N):
        #           a, b = people[i]
        #           s = a + b
        #           # person index is i+1
        #           arr.append( (FractionKey(a, s), i+1) )
        #       arr_sorted = sorted(arr)   # ascending by the tuple: first by FractionKey (which we defined so that higher success rate is "less"), then by i+1 in ascending.
        #       result = [idx for fk, idx in arr_sorted]

        #   Return result.

        #   Let me test with the examples.

        #   Example1: 
        #       Input: 
        #           3
        #           1 3   -> person1: a=1, s=4 -> key1 = (1,4)
        #           3 1   -> person2: a=3, s=4 -> key2 = (3,4)
        #           2 2   -> person3: a=2, s=4 -> key3 = (2,4)
        #       Then we create:
        #           arr = [ (key1, 1), (key2, 2), (key3, 3) ]
        #       Now, sort:
        #           Compare key2 and key3: 3*4=12, 2*4=8 -> 12>8, so key2 < key3 -> True? Then key2 comes before key3.
        #           Compare key2 and key1: 3*4=12, 1*4=4 -> 12>4, so key2 < key1 -> True? Then key2 comes before key1.
        #           Then key3 and key1: 2*4=8, 1*4=4 -> 8>4, so key3 < key1 -> True? Then order: key2, key3, key1 -> persons: 2,3,1 -> output [2,3,1] -> matches.

        #   Example2:
        #       2
        #       1 3 -> a=1, s=4 -> key1=(1,4)
        #       2 6 -> a=2, s=8 -> key2=(2,8)
        #       Then: 
        #           Compare key1 and key2: 
        #              1*8=8, 2*4=8 -> 8>8 is False -> so key1<key2 is False.
        #              Also, key2<key1: 2*4=8, 1*8=8 -> 8>8 is False -> so they are equal.
        #           Then compare the next element: 1 and 2 -> so (key1,1) < (key2,2) because 1<2? 
        #           So the sorted list: [ (key1,1), (key2,2) ] -> output [1,2] -> matches.

        #   Example3: 
        #       4
        #       999999999 1000000000  -> a1=999999999, s1=1999999999
        #       333333333 999999999   -> a2=333333333, s2=1333333332
        #       1000000000 999999997  -> a3=1000000000, s3=1999999997
        #       999999998 1000000000  -> a4=999999998, s4=1999999998
        #       We expect output: 3,1,4,2.

        #       Compare:
        #         We need to compare the FractionKeys for person3, person1, person4, person2.

        #         We know: 
        #           person3: 1000000000 * s1 = 1000000000 * 1999999999 = 1999999999000000000
        #                    vs a1 * s3 = 999999999 * 1999999997
        #           But we don't need to compute all, we can rely on the __lt__.

        #       We'll build the list:
        #         arr = [ (key1,1), (key2,2), (key3,3), (key4,4) ]
        #         Then sort.

        #       We know that person3 should be first. Compare key3 and key1: 
        #           a3 * s1 = 1000000000 * 1999999999 = 1999999999000000000
        #           a1 * s3 = 999999999 * 1999999997
        #           Let me compute a1 * s3: 
        #                = (1000000000-1) * (2000000000-3)
        #                = 1000000000*2000000000 - 1000000000*3 -1*2000000000 + 3
        #                = 2000000000000000000 - 3000000000 - 2000000000 + 3
        #                = 2000000000000000000 - 5000000000 + 3
        #                = 1999999950000000003
        #           So a3 * s1 = 1999999999000000000
        #              a1 * s3 = 1999999950000000003
        #           Then: a3 * s1 = 1999999999000000000 > a1 * s3 = 1999999950000000003 -> True, so key3 < key1 -> True, so key3 comes before key1.

        #       Compare key3 and key4: 
        #           a3=1000000000, s3=1999999997
        #           a4=999999998, s4=1999999998
        #           a3 * s4 = 1000000000 * 1999999998 = 1999999998000000000
        #           a4 * s3 = 999999998 * 1999999997
        #           Compute a4*s3: 
        #                = (1000000000-2) * (2000000000-3)
        #                = 1000000000*2000000000 - 1000000000*3 -2*2000000000 + 6
        #                = 2000000000000000000 - 3000000000 - 4000000000 + 6
        #                = 2000000000000000000 - 7000000000 + 6
        #                = 1999999930000000006
        #           So 1999999998000000000 > 1999999930000000006 -> True, so key3 < key4 -> True, so key3 comes before key4.

        #       Then compare key1 and key4: 
        #           a1=999999999, s1=1999999999
        #           a4=999999998, s4=1999999998
        #           a1 * s4 = 999999999 * 1999999998
        #           a4 * s1 = 999999998 * 1999999999
        #           Now, note: 
        #               a1 * s4 = 999999999 * 1999999998
        #               a4 * s1 = 999999998 * 1999999999
        #           And we can write:
        #               a1 * s4 = (a4+1) * s4 = a4*s4 + s4
        #               a4 * s1 = a4 * (s4+1) = a4*s4 + a4
        #           Then: a1 * s4 - a4 * s1 = s4 - a4
        #           s4 = 1999999998, a4=999999998
        #           s4 - a4 = 1000000000
        #           So a1 * s4 - a4 * s1 = 1000000000 > 0, so a1 * s4 > a4 * s1 -> so key1 < key4? 
        #           But wait: our condition in __lt__ for key1 < key4 is: a1 * s4 > a4 * s1 -> which is True? 
        #           So key1 < key4 -> True, so key1 comes before key4.

        #       Therefore, the order: key3, key1, key4, key2? 
        #       Then the output: [3,1,4,2] -> which matches.

        #   But we didn't check key2: 
        #       key2: a2=333333333, s2=1333333332
        #       We know it's the smallest.

        #   Therefore, the code should be:

        #       class FractionKey:
        #           __slots__ = ('a', 's')
        #           def __init__(self, a, s):
        #               self.a = a
        #               self.s = s
        #           def __lt__(self, other):
        #               return self.a * other.s > other.a * self.s

        #       Then in the solve function:
        #       arr = []
        #       for i in range(N):
        #           a, b = people[i]
        #           s = a + b
        #           arr.append( (FractionKey(a, s), i+1) )
        #       arr_sorted = sorted(arr)
        #       return [idx for fk, idx in arr_sorted]

        #   But note: we are storing the FractionKey and the index. The memory is O(N), which is acceptable.

        #   Time complexity: O(N log N) comparisons, each comparison O(1) (multiplication of two integers that are up to 10^9, which is constant time in practice). 

        #   Therefore, this should be efficient.

        #   Let me code accordingly.

        #   One more note: the problem says the input can have zeros, but s = a+b >=1, so no division by zero.

        #   We'll implement accordingly.

        #   However, we can avoid the class by using a key function that does the comparison without storing a class? But we cannot if we want to avoid floating point.

        #   Alternatively, we can use:

        #       sorted_indices = sorted(range(1, N+1), key=lambda i: ( FractionKey for i) ... but the key function is for one element, not for comparison.

        #   Or, we can use:

        #       sorted_indices = sorted(range(N), key=lambda i: ( -people[i][0]/(people[i][0]+people[i][1]), i) )   -> but float.

        #   Or, we can use a key function that returns a tuple ( success_rate_represented_as_integer_ratio, i) but that is two integers: (a, s) and then we have the same problem: we need to compare a_i/s_i and a_j/s_j.

        #   The sorted function does not support custom comparators on the fly for the key function. So we have to use the class or the cmp_to_key with an old-style comparator.

        #   We can use:

        #       from functools import cmp_to_key
        #       def compare(i, j):
        #           # i, j are indices? But we need the values for the people.
        #           # We are not passing the values, we have to capture them.
        #           # Alternatively, we can create a list of the data and then use the indices to look up.
        #           a_i, b_i = people[i-1]   # if we are using person index, but we are using the index in the list.
        #           a_j, b_j = people[j-1]
        #           s_i = a_i + b_i
        #           s_j = a_j + b_j
        #           # We want: if a_i/s_i > a_j/s_j, then return -1 (meaning i comes before j)
        #           #           if equal, then if i < j, return -1
        #           # But the comparator function: 
        #           #   returns negative if i should come before j, 
        #           #   positive if i should come after j,
        #           #   zero if equal.
        #           prod1 = a_i * s_j
        #           prod2 = a_j * s_i
        #           if prod1 > prod2:
        #               return -1
        #           elif prod1 < prod2:
        #               return 1
        #           else:
        #               # equal success rate: then compare the person index: if i < j, then i comes first -> return -1
        #               # but we are comparing the indices i and j? Here i and j are the person indices? 
        #               # Actually, in the compare function, we are comparing two people: let's say we are comparing (i_data, i_index) and (j_data, j_index). But the function compare(i_index, j_index) and we have to get the data from the input.
        #               # Actually, if we do:
        #               #   sorted_range = sorted(range(1, N+1), key=cmp_to_key(compare))
        #               # Then the compare function will be called with two person numbers? But the person numbers are 1,2,...,N. But we don't have the data for the person by the number. We have the data in the list `people` indexed by 0 to N-1. So for person number i, the data is at people[i-1].
        #               # So we can do:
        #               if i < j:
        #                   return -1
        #               elif i > j:
        #                   return 1
        #               else:
        #                   return 0
        #       Then:
        #       result = sorted(range(1, N+1), key=cmp_to_key(compare))

        #   But the problem: the compare function will be called O(N log N) times, and each time we do two multiplications of up to 10^9. And we are also doing two lookups. But the total is O(N log N * 1) which is acceptable.

        #   However, the class method is more efficient because the key is computed once per element. In the comparator function, the multiplications are done per comparison, so each element is involved in O(log N) comparisons, and the same multiplication (for the same element) is done multiple times. But that's also the case for the class method? Actually, in the class method, the FractionKey is created once per element, and then the __lt__ is called per comparison. So the multiplications are done per comparison. So the total number of multiplications is the same: O(N log N). 

        #   But in the class method, we precompute s = a+b once per element, and in the comparator method, we compute s_i and s_j per comparison. So the comparator method does

## Final Solution

