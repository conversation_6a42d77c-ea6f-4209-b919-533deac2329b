# Solution for abc321_c.md

Generated at: 2025-07-22 06:31:53
Model: deepseek-reasoner

## Reasoning Process

Alright, let me try to figure out how to solve this problem. The problem is about finding the K-th smallest 321-like number. A 321-like number has digits that are strictly decreasing. For example, numbers like 321, 96410, and even single-digit numbers are 321-like. 

First, I need to understand the pattern of these numbers. Since the digits must be strictly decreasing, each digit in the number must be unique and in descending order. This reminds me of combinations of digits. For instance, the number 32 is a combination of 3 and 2, and it's written in decreasing order. Similarly, 96410 is a combination of 9,6,4,1,0.

So, essentially, each 321-like number is formed by selecting a non-empty subset of distinct digits from 0 to 9, arranging them in decreasing order. But note that the number cannot start with 0. So, for any subset that includes 0, it must not be the first digit. Therefore, the first digit must be from 1 to 9.

Now, if I think about the possible 321-like numbers, they can be of different lengths: 1-digit, 2-digit, up to 10-digit (since we have 10 distinct digits). For each length L (from 1 to 10), the number of possible 321-like numbers is the number of ways to choose L distinct digits from 0-9, and then arrange them in decreasing order. However, we must ensure that the first digit isn't zero.

Wait, actually, if we choose a set of digits that includes 0, then when we arrange the digits in decreasing order, 0 would be at the end. So the only constraint is that if 0 is included, the number of digits must be at least 2, and 0 will be the last digit. But the first digit would be non-zero. So, actually, the count of numbers with L digits is C(10, L) for L from 1 to 10? But wait, if L=1, we cannot choose 0 because then the number would be 0, which is not positive. So for L=1, we can only choose digits from 1 to 9. For L>1, we can include 0 as long as we choose at least one non-zero digit (which will be the first digit).

Alternatively, we can think that for a number of length L, we choose L distinct digits from 0-9, but we must have at least one non-zero. The total number of such numbers for a given L is the number of ways to choose L digits from 0-9, minus the cases where we choose 0 and the rest are zeros? Actually, if we choose L digits including 0, we can only form one number because the digits must be arranged in decreasing order. So each subset corresponds to exactly one number (except the subset {0} which is invalid). So, the total numbers for length L is C(10, L) for L=1 to 10, but subtract the cases where the subset is only zeros? Actually, the subset cannot consist of only zeros because we need a positive integer. So for L=1, we have 9 numbers (digits 1-9). For L>=2, we can include 0, and the count is C(10,L). But wait, when L=1, C(10,1)=10, which includes 0. We exclude 0, so 9. For L=2, we can have subsets that include 0 and one non-zero: C(9,1) =9? Actually, no: for L=2, the total subsets are C(10,2)=45, but each subset gives one number. The subset {0,1} gives the number 10, {0,2} gives 20, etc. So yes, all these are valid. Similarly, for L=3, C(10,3)=120, and so on.

So the total numbers for each length L (from 1 to 10) are:

- L=1: 9 (digits 1-9)
- L=2: C(10,2)=45? But wait, if we choose two digits, we can form only one number per subset. The count is the number of ways to choose L distinct digits from 0-9, excluding the case for L=1 that is 0. Actually, for any L, the count is C(10,L) for L from 1 to 10, but for L=1 we have 9 (excluding the digit 0). Alternatively, we can think that the set of all 321-like numbers is in bijection with the non-empty subsets of {0,1,2,...,9} that contain at least one non-zero. So the total number of such numbers is (2^10 - 1) - 1? Because the total non-empty subsets of 10 elements is 2^10 -1=1023, but the subset {0} is invalid? Actually, the subset {0} would correspond to the number 0, which is not positive. So we must exclude any subset that does not contain a non-zero digit. The only such subset is {0}. So total valid numbers = total non-empty subsets of {0,1,...,9} minus the subset {0}, which is 1023 -1 = 1022? But wait, the subset {0} is one, so excluding that, we have 1022 numbers.

But let me verify: for L=1: 9 numbers (1-9). L=2: C(10,2)=45? Actually, no: the number of ways to choose 2 digits from 10 is 45. Then L=3: C(10,3)=120, L=4:210, L=5:252, L=6:210, L=7:120, L=8:45, L=9:10, L=10:1. Now, if I sum these: 9+45+120+210+252+210+120+45+10+1. But wait, for L=1, we have 9, but the rest are C(10,L). So total = (sum_{L=1}^{10} C(10,L)) - 1 (because for L=1, we excluded 0). The sum of C(10,L) for L=1 to 10 is 2^10 - C(10,0)=1024-1=1023. Then subtract the one invalid (the digit 0), so 1022. Alternatively, we can calculate:

9 (for L=1) + 45 (L=2) + 120 (L=3) + 210 (L=4) + 252 (L=5) + 210 (L=6) + 120 (L=7) + 45 (L=8) + 10 (L=9) + 1 (L=10) = ?

Let me add step by step: 
9+45=54
54+120=174
174+210=384
384+252=636
636+210=846
846+120=966
966+45=1011
1011+10=1021
1021+1=1022.

Yes, 1022. So there are 1022 such numbers in total. But the problem says "At least K 321-like Numbers exist", meaning K can be up to 1022? Or beyond? The constraint says "At least K exist", so K can be large? But wait, the problem states that there are only 1022 such numbers. So K is at least 1 and at most 1022? But the examples: K=777 is given, which is less than 1022. So we are safe.

But the problem says "At least K 321-like Numbers exist", meaning that K is such that we have at least K numbers. Since the total is 1022, K must be between 1 and 1022. So we can assume K<=1022.

Now, how do we find the K-th smallest? 

The numbers are ordered from smallest to largest. The smallest numbers are the 1-digit: 1,2,...,9. Then the 2-digit: 10,20,21,30,31,32,40,... etc.

Notice that the numbers are not ordered by length first. The 1-digit numbers are the smallest, then 2-digit, then 3-digit, etc. But within the same length, how are they ordered? For example, 2-digit: 10 (which is 10), then 20,21 (20 is less than 21? 20 is 20, 21 is 21, so 20<21). Similarly, 30,31,32, etc.

But wait, 10 is 10, which is less than 20 (20). Then 20 is less than 21? Yes, because 20 is 20 and 21 is 21. Similarly, 30, then 31, then 32. So for the same set of digits, the number is fixed. But when the lengths differ, the 1-digit numbers are all less than 2-digit numbers? Actually, no: 9 is 9, and 10 is 10. So 9 < 10? Actually, no: 9 is less than 10? Yes, 9<10. So the ordering is:

1,2,3,...,9, then 10,20,21,30,31,32,40,41,42,43,... etc.

So the numbers are ordered by their numeric value. But note that 10 is 10, which is greater than 9. So the ordering is: all 1-digit (1-9) in increasing order, then 2-digit in increasing order, then 3-digit, etc. Within the same length, the numbers are ordered by their numeric value, which is equivalent to lexicographical order because they are of same length? But actually, for same length, the numbers are formed from different sets. For example, 2-digit numbers: the smallest is 10, then 20, 21, 30, 31, 32, 40, etc. So how do we generate them in increasing order?

Alternatively, we can generate all the numbers and sort them? But that's 1022 numbers, which is acceptable? But the problem constraints don't specify the maximum K, but since we know there are only 1022 numbers, we could precompute all and then sort. Then for a given K, we output the K-th one. But the problem examples: K=777. We can precompute all 1022 numbers, sort them, and then output the K-th. But is that efficient? 1022 numbers is very small, so we can do that. But let me see if there's a smarter way.

But the problem says "All input values are integers" and "At least K 321-like Numbers exist", and we know K<=1022. So we can generate all 321-like numbers, sort them, and then pick the K-th smallest. This would be O(2^10 * log(2^10)) which is about 1024*10=10240 operations, which is acceptable.

But let me think about how to generate them. We can generate all non-empty subsets of {0,1,...,9} that are not {0}. For each subset, we form the number by sorting the digits in descending order. Then, we convert that to an integer and add to a list. Then sort the list. Then output the K-th element (with K being 1-indexed).

But note: the number formed from the subset {1,0} is 10, which is the same as the subset {0,1}. So we can represent each subset as a sorted tuple? Actually, we don't need to sort the subset as a set; we can generate the number by taking the digits in descending order.

But how to generate all subsets? We can use bit masks from 1 to 1023 (since 0 is excluded, and we skip the subset {0} which corresponds to the bit mask with only the 0th bit set? Or we can skip the mask 1<<0? Actually, the subset {0} is the bit mask 1 (if we consider the bits representing digits 0 to 9). Alternatively, we can iterate over all masks from 1 to 1023, and for each mask, if the mask does not have any non-zero digit, skip? Actually, the only mask that has no non-zero digit is the mask that has only digit 0. So we can skip mask==1? Because if we map:

digit 0: bit0
digit 1: bit1
...
digit 9: bit9

So the mask for {0} is 1 (the 0th bit set). The mask for {1} is 2 (bit1 set). The mask for {0,1} is 1<<0 | 1<<1 = 1 | 2 = 3.

So we can iterate mask from 1 to 1023, and skip if the mask has only the 0th bit? Or, we can check that the mask has at least one bit from 1 to 9 set? Actually, the condition is that the set must have at least one non-zero digit. So if the mask has only bit0 set, skip. Otherwise, form the number.

Steps for brute force:

1. Precompute an empty list: numbers = []
2. For mask in range(1, 1024): # 1 to 1023
   a. Let digits = []
   b. For each digit d from 0 to 9:
        if mask has the d-th bit set? Then append d to digits.
   c. If the digits set is exactly [0] (i.e., only 0), skip? Actually, we can check: if the set has no non-zero, then skip. But we can also check: if the mask has no bit set in positions 1 to 9? Actually, if the set has at least one non-zero, then proceed. So condition: if (mask & 0b1111111110) == 0 and mask has bit0 set, then skip? Alternatively, we can check: if there is no digit from 1 to 9, then the set must be either empty or only 0. But we are iterating non-empty masks. So if the mask is 1 (only bit0), skip. Else, process.

   Alternatively, we can skip the mask 1? But also, if the mask has no non-zero, then it must be mask 1 (because if mask has no non-zero, then the only possible set is {0}). So we can skip mask==1.

   Actually, mask 1 (binary 1) has only digit0. So skip mask==1.

3. For the current mask, form the set of digits. Then sort the digits in descending order. Then form the number. For example, digits [0,1] sorted descending is [1,0] -> number 10.
4. Append this number to the list.
5. After all masks, sort the list of numbers.
6. Then for a given K, output the (K-1)th element (0-indexed).

But note: the problem says the 15th smallest is 32. Let me verify with the example.

List of numbers:

1-digit: 1,2,3,4,5,6,7,8,9 -> 9 numbers.

Then 2-digit: 
Subsets of size 2 that are not {0}? Actually, we skip {0} only. So all 2-digit subsets: 
{0,1}:10
{0,2}:20
{0,3}:30
{0,4}:40
...
{0,9}:90
Then {1,2}:21? But wait, the numbers formed: 
{0,1} -> 10
{0,2} -> 20
{1,2} -> 21
{0,3} ->30
{1,3}->31
{2,3}->32
{0,4}->40
...

So the 2-digit numbers in increasing order: 10,20,21,30,31,32,40,41,42,43,50,... 

So the 10th number is the last 1-digit: 9? Then the 10th smallest is 9? Then the 11th is 10? 12th is 20? 13th is 21? 14th is 30? 15th is 31? 16th is 32? 

But the example says the 15th smallest is 32. So:

1:1
2:2
3:3
4:4
5:5
6:6
7:7
8:8
9:9
10:10
11:20
12:21
13:30
14:31
15:32

So 15th is 32. But in our list, 10 is the 10th, 20 is the 11th, 21 is the 12th, 30 is the 13th, 31 is the 14th, 32 is the 15th. Correct.

But wait, after 10, we have 20? But 20 is 20 which is greater than 10. Then 21 is 21, which is greater than 20. Then 30>21? Yes, 30>21. So the ordering is numeric.

But when we form the number from the subset, we form the largest possible number from the digits? Actually, we are forming the only strictly decreasing number from that set. So each subset gives one number.

But when we generate the numbers from the subsets, we get:

mask 2 (bit1): [1] -> 1 (but we skip mask1? Actually, mask1 is {0}, skipped. mask2 is {1} -> digit 1. Then mask3: {0,1} ->10. mask4: {2} ->2. mask5: {0,2} ->20. mask6: {1,2}->21. mask7: {0,1,2}->210? But wait, we are only considering the entire set? Actually, we are iterating all masks. So the number for mask7 (0,1,2) is 210. But that's a 3-digit number.

So the list we generate will have numbers of different lengths. Then we sort the entire list. Since 1 (mask2) is 1, then 2 (mask4) is 2, then 10 (mask3) is 10, then 20 (mask5) is 20, then 21 (mask6) is 21, then 210 (mask7) is 210? But 210 is a 3-digit number, which is greater than 21? But 21 is 21, and 210 is 210, which is larger. So when we sort the entire list as integers, the order is correct.

But in the example, the 15th is 32. So we need to generate all numbers and sort them. The total is 1022 numbers. Then we can do:

Precomputation:

numbers = []
for mask in range(1, 1024): # 0 to 1023, but mask0 is empty set -> skip. So from 1 to 1023.
    if mask == 1: # which is only digit0 -> skip
        continue
    digits = []
    for d in range(0,10):
        if mask & (1 << d):
            digits.append(d)
    # form the number: sort digits in descending order, then combine to form an integer.
    digits.sort(reverse=True)
    # if the first digit is 0? Then we have a problem. But we skipped mask==1. Also, if the set has multiple digits including 0, the first digit should be non-zero because we sorted descending. So the first digit is the largest, which is non-zero (since the set has at least one non-zero). So no problem.
    num = 0
    for digit in digits:
        num = num * 10 + digit
    numbers.append(num)

Then sort numbers: numbers.sort()
Then for a query K, we return numbers[K-1]

But wait, the numbers we generate: for a subset, we form the number by the digits in descending order. But when we sort the entire list, we get the increasing order. This should work.

But the total numbers: 1023 - 1 (mask1) = 1022. Then we sort 1022 integers. This is efficient.

But let me test with K=15. We'll generate all numbers and then pick the 15th smallest.

But is there a more efficient way? Since K is at most 1022, it's acceptable. But the problem examples: K=777. 1022 is small, so it's fine.

But let me think: the problem constraints don't specify K, but we know K<=1022. So precomputation is acceptable.

Alternatively, we can generate the numbers in increasing order without generating all? But that might be more complex. Since the total is small, we can do the brute force.

But let me write the code and test with the examples.

Example 1: K=15 ->32.

Example 2: K=321 ->9610. Example3: K=777->983210.

But if we precompute all, then we can store in a list and then for each query, we just return the K-1 index. But the problem says the input is given as K. So we can precompute all 1022 numbers, sort them, and then for any K, we return the (K-1)-th element.

But the problem: we are to write a function that takes K. Should we precompute once? Or each time? Since the constraints are small, we can precompute once and store the sorted list as a global variable. Then each call to solve just returns the (K-1)th element.

But in the starter code, it's a class. So we can precompute in the constructor or in the solve method. But if we precompute each time solve is called, it might be inefficient for multiple calls? But the problem says the input is given from standard input, so probably one call per run. But the problem statement says: "The input is given from Standard Input in the following format: K". So one integer K.

But to be safe, we can precompute once when the class is initialized. Or we can precompute every time, but since it's only 1022 numbers, it's acceptable even if called multiple times? But the problem doesn't specify multiple test cases. So we can precompute each time.

But let me write the code accordingly.

Steps:

1. Precompute a list of all 321-like numbers (1022 numbers).
   - Skip mask 1 (only digit0).
   - For mask in range(1, 1024): skip if mask==1, else convert mask to set of digits, sort in descending, form integer, and add to list.
2. Sort the list.
3. Then for a given K, return the element at index K-1.

But let me test with small K.

K=1: smallest is 1. 
When mask=2 (binary 10): digit1 ->1 -> number 1.
mask=3: {0,1} -> [1,0] ->10
mask=4: digit2 ->2
mask=5: {0,2} ->20
mask=6: {1,2} ->21
mask=7: {0,1,2}->210
mask=8: digit3->3
mask=9: {0,3}->30
mask=10: {1,3}->31
mask=11: {0,1,3}->310
mask=12: {2,3}->32
mask=13: {0,2,3}->320
mask=14: {1,2,3}->321
mask=15: {0,1,2,3}->3210

But wait, the list we get:

mask2:1 ->1
mask4:2 ->2
mask8:3->3
mask16:4->4
...
mask512:9->9
Then mask3:10
mask5:20
mask6:21
mask9:30
mask10:31
mask12:32 -> this is mask12: digits? mask12: 1100 in binary? bits: 2 and 3 (since 2^2=4, 2^3=8, so 4+8=12) -> digits 2 and 3? Then the number is 32. 

So the list of numbers (so far) in the order of mask: 1,2,3,4,5,6,7,8,9,10,20,21,30,31,32,... 

But when we sort the entire list, the first 9 are 1,2,3,...,9. Then 10, then 20,21,30,31,32,...

So the sorted list: [1,2,3,4,5,6,7,8,9,10,20,21,30,31,32,...]

So K=1:1, K=2:2,...,K=9:9, K=10:10, K=11:20, K=12:21, K=13:30, K=14:31, K=15:32. Correct.

Now, example2: K=321. The output should be 9610.

We can compute: the 321st number. Since we generate all and sort, we can just pick the 320th index (0-indexed).

Similarly, example3: K=777 -> 983210.

But we can write the code and test? Or we can compute manually? Not necessary, because we trust the precomputation.

But let me check the count for each length:

- 1-digit: 9 numbers (1-9): indices 0-8 -> K=1 to 9
- 2-digit: 45 numbers? Actually, how many 2-digit subsets? Choose 2 digits from 10: C(10,2)=45. But we have to exclude the set {0}? No, because we skip only the set {0}. The 2-digit numbers include sets that contain 0? Actually, the 2-digit numbers: the set must have two digits, and if one of them is 0, then the number is like 10,20, etc. But also sets without 0: like 21,32, etc. So total 2-digit: C(10,2)=45. Then 3-digit: C(10,3)=120, 4-digit:210, 5-digit:252, 6-digit:210, 7-digit:120, 8-digit:45, 9-digit:10, 10-digit:1.

Now, cumulative:

After 1-digit: 9 numbers.
After 2-digit: 9+45=54
After 3-digit: 54+120=174
After 4-digit: 174+210=384
After 5-digit: 384+252=636
After 6-digit: 636+210=846
After 7-digit: 846+120=966
After 8-digit: 966+45=1011
After 9-digit:1011+10=1021
After 10-digit:1021+1=1022.

Now, the 321st number is at index 320 (0-indexed). Since 321 is greater than 174 (3-digit cumulative) and less than 384 (4-digit cumulative), so it's a 4-digit number. The 321st number is the (321-174)th number in the 4-digit numbers? But the entire list is sorted numerically, so we cannot assume that all 3-digit are before 4-digit? Actually, yes: because the largest 3-digit number is 987 (or 987 is the largest? Actually, the largest 3-digit is 987? Or 987 is the largest? 987 is 987, which is less than 1000? But 1000 is not a 321-like number. The next is 4-digit: the smallest 4-digit is 1023? No, the smallest 4-digit is 3210? Actually, no: the smallest 4-digit 321-like number is 3210? Or 1023? But 1023: digits 1,0,2,3? But the digits must be strictly decreasing. So 1023: 1,0,2,3 -> not decreasing. So the smallest 4-digit is 3210? Or 4321? Actually, the smallest 4-digit should be the smallest number that has 4 strictly decreasing digits. So the smallest is 3210? But 3210 is 3210. Then 4210, 4320, 4321, then 5210, etc.

But in numeric order, the smallest 4-digit is 1023? But wait, 1023 is not strictly decreasing. So the smallest 4-digit is actually 3210? Or 2100? No, because digits must be strictly decreasing and distinct. So the smallest 4-digit number: the smallest number that can be formed by 4 distinct digits in decreasing order. The smallest possible number would be the one with smallest most significant digit? And then next, etc. So the smallest 4-digit number is 3210? But 3210 is 3210. Then 4210, 4320, 4321, 5210, etc. But 3210 is 3210, which is greater than 999 (the largest 3-digit). So yes, all 3-digit numbers are less than all 4-digit numbers. Similarly, all 2-digit are less than 3-digit? Actually, the largest 2-digit is 98? Then the smallest 3-digit is 210? 98 < 210? 98 is 98, 210 is 210, so 98<210? Yes. So the numbers are grouped by length: all 1-digit < all 2-digit < all 3-digit < ... 

Therefore, we can also compute without generating all: we can determine the length L by the cumulative counts, then generate all numbers of length L, sort them, and then pick the (K - cumulative[L-1])-th number. But that would be similar in total effort? Since the total numbers are small, we can just generate all and sort.

But for the sake of efficiency, we can do:

cumulative = [0, 9, 54, 174, 384, 636, 846, 966, 1011, 1021, 1022]

Then for a given K, we find the smallest L such that cumulative[L] >= K. Then we know the number is of length L. Then we generate all numbers of length L? Then sort them? Then pick the (K - cumulative[L-1] -1)th.

But generating all numbers of a fixed length L: we can generate all combinations of L distinct digits from 0-9, but excluding those that have no non-zero? Actually, we can use combinations: from itertools import combinations. For each combination, if the combination has at least one non-zero, form the number by sorting the combination in descending order. Then we get the list of numbers for length L. Then sort that list. Then pick the (K - cumulative[L-1] -1)th.

But the total for each L: the maximum is 252 for L=5. So we can generate combinations for a given L: C(10,L) is at most 252. Then sort: 252*log(252) is negligible.

But then we have to do this for each query? But the problem has only one query. So we can precompute for all L? Or do it on the fly.

But the brute force method of generating all 1022 numbers is simpler and acceptable. So I'll go with that.

Let me code accordingly.

But in the starter code, it's a class. We can precompute all numbers once and store as a class variable? So that if the solve method is called multiple times, we don't recompute.

But the problem says one input. But to be efficient for multiple calls, we can precompute once.

Implementation:

We'll have a class variable `all_numbers` that holds the sorted list of all 321-like numbers.

We can do:

class Solution:
    all_numbers = None

    def __init__(self):
        if Solution.all_numbers is None:
            Solution.all_numbers = self.generate_all_numbers()
    
    def generate_all_numbers(self):
        numbers = []
        # iterate mask from 1 to 1023
        for mask in range(1, 1024):
            if mask == 1:  # only digit0
                continue
            digits = []
            # iterate d from 0 to 9
            for d in range(0, 10):
                if mask & (1 << d):
                    digits.append(d)
            # sort digits in descending order
            digits.sort(reverse=True)
            # form the number
            num = 0
            for d in digits:
                num = num * 10 + d
            numbers.append(num)
        numbers.sort()
        return numbers

    def solve(self, K: int) -> int:
        # K is 1-indexed
        return Solution.all_numbers[K-1]

But we can avoid the class variable and just precompute each time? But if solve is called multiple times, we precompute each time. 1022 is small, but still, we can do it once.

Alternatively, we can precompute at the module level.

But the problem: we don't know if multiple instances are created. So using a class variable is safe: it will be computed once per class.

But let me test with the examples.

Example1: K=15 ->32. Correct.

Example2: K=321 ->9610.

How is 9610 formed? The digits: 9,6,1,0? Sorted descending: 9,6,1,0 -> 9610. Is 9610 the 321st? 

From cumulative: 
After 1-digit: 9
After 2-digit: 54
After 3-digit: 174
Then the 4-digit numbers: from 175th to 384th. The 321st overall is the (321-174)=147th 4-digit number? But 4-digit numbers total 210. The 147th in the 4-digit numbers.

But how are the 4-digit numbers ordered? They are ordered numerically. The smallest 4-digit is 3210? Then 4210, 4320, 4321, 5210, ... 9870,9871,9872,9873,9874,9875,9876? Then 98760? No, 4-digit only.

So the 4-digit numbers: we generate all combinations of 4 distinct digits from 0-9 that have at least one non-zero. Then form the number by sorting the digits descending. Then sort all these numbers.

The smallest: the smallest number is the one with smallest digits? But in descending order. The smallest number would be the one that has the smallest thousands digit? Then hundreds, etc. So the smallest is 3210. Then 4210, 4310, 4320, 4321? Then 5210, 5310, 5320, 5321, 5410, etc.

But 3210, then 3210 is the smallest? Then 4210 (which is 4000+210=4210) is next? Then 4310? Then 4320? Then 4321? Then 5210? 

But 3210 < 4210 < 4310 < 4320 < 4321 < 5210 < ... 

Now, what is the 147th 4-digit number? It's hard to compute manually. But we trust the code.

But we can check with the example: K=321 ->9610.

Similarly, for K=777: the output is 983210. This is a 6-digit number. 

Cumulative counts: 
After 5-digit: 636 numbers. Then 6-digit: 210 numbers. So the 777th is the (777-636)=141st 6-digit number. The 141st 6-digit number is 983210? 

The 6-digit numbers: they start from 543210, then 643210, 653210, 654210, 654310, 654320, 654321, then 743210, etc.

But 983210: digits 9,8,3,2,1,0. So the number is 983210. 

Now, why is it the 141st? We don't need to verify manually. We trust the precomputation.

But we can write a helper to see if we generate 983210 for mask corresponding to {9,8,3,2,1,0}. The mask: bits for digits 0,1,2,3,8,9. Then sorted descending: 9,8,3,2,1,0 ->983210.

So the code should work.

But let me write the code accordingly.

Edge: K=1 ->1, K=1022: the largest number: 9876543210.

Now, code:

We'll use a class variable to store the sorted list.

But in Python, we can use bit masks. Alternatively, we can use itertools.combinations? But we need to iterate over all non-empty subsets. The bit mask is straightforward.

But we can also use:

from itertools import combinations

But the problem says no extra imports? The starter code doesn't have any. So we can use bit masks.

Alternatively, we can generate for each length from 1 to 10, and for each length, iterate over combinations of 10 digits taken L at a time. Then form the number. Then append. Then at the end, sort the entire list. That would also work.

But we did the bit mask method.

Now, code:

We'll iterate mask from 1 to 1023 (inclusive). Skip mask==1 (only digit0). For each mask, we extract the set of digits by checking the bits. Then form the number.

But we can optimize the inner loop: instead of iterating d from 0 to 9, we can use bit manipulation to get the digits? But 10 iterations per mask is 10*1023=10230, which is acceptable.

So the code:

class Solution:
    all_nums = None

    def __init__(self):
        if Solution.all_nums is None:
            nums = []
            # mask from 1 to 1023
            for mask in range(1, 1024):
                if mask == 1:  # only 0, skip
                    continue
                digits = []
                for d in range(0, 10):
                    if mask & (1 << d):
                        digits.append(d)
                digits.sort(reverse=True)
                num = 0
                for digit in digits:
                    num = num * 10 + digit
                nums.append(num)
            nums.sort()
            Solution.all_nums = nums
    
    def solve(self, K: int) -> int:
        return Solution.all_nums[K-1]

But we can avoid the class variable by computing once and storing in a module-level variable? But the problem: the solution class might be instantiated multiple times. So class variable is better.

But we can also precompute in the solve method if it's the first time? But then we need to store. Alternatively, we can precompute at module level. But the problem says we have to write the class.

Alternatively, we can precompute outside the class? But the starter code is inside the class.

But the problem: the constraints say that at least K numbers exist, meaning K>=1 and K<=1022. So we can assume K is in that range.

Let me test with K=1, K=15, and the others.

I'll write a small test:

sol = Solution()
print(sol.solve(15)) # should be 32
print(sol.solve(321)) # should be 9610
print(sol.solve(777)) # should be 983210

But I can run locally to test.

Alternatively, we can compute the total numbers: len(Solution.all_nums) should be 1022.

But we can also test with K=1022: should be 9876543210.

Now, I think the code is correct.

But let me run with K=1 to 15 and see:

We can print the first 15:

1:1
2:2
3:3
4:4
5:5
6:6
7:7
8:8
9:9
10:10
11:20
12:21
13:30
14:31
15:32

Yes, matches.

So the code is ready.

But note: the inner loop for digits: we iterate from 0 to 9. We can iterate from 9 down to 0? But we sort in reverse anyway. So it doesn't matter.

Alternatively, we can form the number without sorting? We can just take the digits in the order of the mask? But no, we need strictly decreasing. So we must sort the digits descending.

Another idea: the number for a mask is the integer formed by the digits in descending order. We can compute without converting to an integer? But we do.

But the current code is clear.

Time complexity: O(1023 * 10) = 10230, then sorting 1022 integers: O(1022 * log(1022)) ~ 1022*10=10220. So total acceptable.

Space: storing 1022 integers, which is about 1022*4 bytes? Or more? But in Python, integers are bigger, but 1022 is small.

So we are good.

Final code:

We'll write as described.

But note: the problem says "All input values are integers" and K>=1, and at least K numbers exist, so we are safe.

Let me write the code accordingly.

## Final Solution

To solve this problem, we need to find the K-th smallest 321-like number. A 321-like number is defined as a positive integer where the digits are strictly decreasing from left to right. Single-digit positive integers are considered 321-like numbers by default. 

### Approach
1. **Problem Analysis**: The key observation is that each 321-like number corresponds to a unique non-empty subset of distinct digits from 0 to 9, arranged in strictly decreasing order. The only exception is the subset containing only the digit 0, which is invalid since it would form the number 0 (not positive). Therefore, the total number of valid 321-like numbers is \(2^{10} - 1 - 1 = 1022\) (total non-empty subsets minus the invalid subset {0}).

2. **Intuition**: The solution involves generating all possible valid 321-like numbers by considering all non-empty subsets of digits (0-9) except {0}. For each subset, we sort the digits in descending order to form the number. After generating all such numbers, we sort them in ascending order to easily retrieve the K-th smallest number.

3. **Algorithm Selection**: 
   - **Precomputation**: Precompute all valid 321-like numbers by iterating over all possible bitmasks (from 1 to 1023). Each bitmask represents a subset of digits. For each bitmask, if it is not the subset {0}, we extract the digits, sort them in descending order, and form the corresponding number.
   - **Sorting**: After generating all numbers, sort them in ascending order for efficient retrieval.
   - **Query Processing**: For any given K, return the (K-1)-th element from the sorted list (since the list is 0-indexed).

4. **Complexity Analysis**:
   - **Time Complexity**: The precomputation involves \(O(1023 \times 10)\) operations to generate all numbers (1023 masks, each processed in 10 steps) and \(O(N \log N)\) time to sort the 1022 numbers, where \(N = 1022\). This results in \(O(1)\) time per query after precomputation.
   - **Space Complexity**: \(O(1022)\) space to store all generated numbers.

### Solution Code
```python
class Solution:
    all_nums = None

    def __init__(self):
        if Solution.all_nums is None:
            nums = []
            for mask in range(1, 1024):
                if mask == 1:
                    continue
                digits = []
                for d in range(0, 10):
                    if mask & (1 << d):
                        digits.append(d)
                digits.sort(reverse=True)
                num = 0
                for digit in digits:
                    num = num * 10 + digit
                nums.append(num)
            nums.sort()
            Solution.all_nums = nums

    def solve(self, K: int) -> int:
        return Solution.all_nums[K-1]
```

### Explanation
1. **Initialization**: The `Solution` class uses a class variable `all_nums` to store the sorted list of all 321-like numbers. This list is generated once when the first instance of the class is created.
2. **Number Generation**: For each bitmask from 1 to 1023 (representing all non-empty subsets of digits 0-9):
   - Skip the bitmask representing the subset {0}.
   - For other bitmasks, collect the digits corresponding to set bits, sort them in descending order, and form the number by concatenating these digits.
3. **Sorting**: The generated numbers are sorted in ascending order to facilitate quick retrieval of the K-th smallest number.
4. **Query Handling**: The `solve` method simply returns the (K-1)-th element from the precomputed sorted list, leveraging the initial setup for efficient query responses.

This approach efficiently precomputes all possible valid numbers, ensuring quick responses to any query with minimal overhead after the initial setup.