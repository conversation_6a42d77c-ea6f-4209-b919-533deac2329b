# Solution for 982_C.md

Generated at: 2025-07-22 06:14:54
Model: deepseek-reasoner

## Reasoning Process

Alright, I'm going to tackle this problem step by step. Let me start by understanding what the problem is asking.

We have a tree with `n` vertices. The task is to remove as many edges as possible so that every remaining connected component has an even number of vertices. We need to output the maximum number of edges we can remove, or -1 if it's impossible.

First, I recall that a tree is an undirected graph with no cycles, and it has exactly `n-1` edges. Removing edges from a tree will break it into multiple connected components, each of which is a tree (or a forest). The key requirement is that every component must have an even size.

Let me think about the properties of even-sized components. Since the entire graph has `n` vertices, if we break it into components of even sizes, the total `n` must be even. Why? Because the sum of even numbers is even. So, if `n` is odd, it's impossible. For example, in the second example, `n=3` (odd), so the answer is -1.

So, the first check: if `n` is odd, return -1.

Now, assuming `n` is even, what's the approach? We need to remove edges to split the tree into components of even sizes. Each time we remove an edge, we split the tree into two components. For the removal to be valid, both resulting components must have even sizes? Actually, no. The problem allows multiple removals, so we can have more than two components. But after all removals, every component must be even.

However, note that when we remove an edge, we split one connected component into two. Initially, the whole tree is one component. After removing an edge, we have two components. Then, we can remove another edge in one of the components to split it further, and so on. So each removal increases the number of components by one.

But the requirement after each removal? Actually, no, the removals are done in such a way that at the end, all components are even. So, we can remove multiple edges one after the other. But the key point: each edge removal splits a component into two. Therefore, the entire process is about partitioning the tree into disjoint even-sized connected components.

Now, what's the condition for being able to split a tree into even-sized components? One important observation: if a connected component has even size, we can possibly split it further by removing an edge, but that edge must split it into two even-sized components? Not necessarily. It could split into two odd-sized components? But wait, the sum of two odd numbers is even. But each component must be even, so if we split an even-sized component, we must split it into two even-sized components. Why? Because if one part is odd and the other is odd, then each is odd, which is not allowed. So, to split an even-sized component, we must remove an edge that results in two even-sized components.

But is that always possible? Not necessarily. For example, consider a star with 4 vertices: center connected to three leaves. The entire component has 4 (even). If we remove an edge from the center to a leaf, we get a component of size 1 (leaf) and the rest (center and two leaves) of size 3. Both are odd. So that removal is invalid. Alternatively, we can remove an edge that splits the graph into two components of size 2? But in a star, each edge removal gives a leaf and the rest. So, we cannot split the 4-node star into two 2-node components by removing one edge? Actually, no, because the star is not linear. But wait, we can remove two edges? The problem asks for the maximum number of edges we can remove, meaning we can remove multiple edges as long as at the end, every component is even.

But in the star example (n=4), if we remove two edges: say, remove two edges from the center to two leaves. Then we have two isolated leaves (each size 1, odd) and a component with the center and one leaf (size 2, even). But the leaves are odd, which is invalid. Alternatively, we can only remove one edge? But that leaves one leaf (odd) and the rest (odd). So, actually, we cannot remove any edge? Then the entire tree is one even component (size 4), which is valid. So the maximum number of edges we can remove is 0? But that doesn't seem right because the example input 2 gives 0. But for n=4, the example output is 1.

Wait, look at Example 1: Input is 4 with edges [2,4], [4,1], [3,1]. This is a tree. The edges are between 2-4, 4-1, and 3-1. So, it's like vertex 1 connected to 4 and 3, and 4 connected to 2. So the tree is: 1 is the center? Actually, 1 is connected to 3 and 4, and 4 is connected to 2. So it's a chain? Or better: 2-4-1-3. So it's a linear tree: 2-4-1-3.

In this tree, we can remove the edge between 1 and 4. Then we get two components: one with vertices [2,4] (size 2) and the other with [1,3] (size 2). Both even. So we removed one edge. Therefore, the answer is 1.

But if we had a star: one center connected to three leaves, and then one of the leaves connected to a fourth node? Actually, no, a star with four nodes: center and three leaves. Then the center has three edges. Removing any one edge leaves one leaf (size 1) and the rest (size 3: center and two leaves). Both odd. So we cannot remove that edge. Then what? We cannot remove any edge? Then the entire graph is one component of size 4 (even), which is valid. So we can remove 0 edges. But the problem says "maximum possible number of edges that can be removed". So 0 is acceptable. But why in the linear tree we can remove one? So it depends on the tree structure.

So, the key is: we can remove an edge only if it splits the tree into two even-sized components. Why? Because if we remove an edge and one part is of size `s`, the other is `n-s`. For both to be even, `s` must be even and `n-s` must be even. Since `n` is even, if `s` is even, then `n-s` is even. So, we can remove an edge if one of the resulting components has even size (then the other automatically does too).

But note: when we remove an edge, we are splitting a component. Initially, the whole tree is even. We can remove an edge if we can split it into two even components. Then, each of these components is even, and we can try to split them further. But to split a component, we need an edge that, when removed, splits that component into two even parts. So the condition for each removal is the same: the subtree we are disconnecting must have even size.

Therefore, the problem reduces to: we can remove an edge if the subtree on one side of that edge has an even number of nodes. Then, if we remove all such edges? But note: we can only remove edges that are present. Also, removing one edge might affect the sizes of other subtrees? Actually, no, because the tree is static. But when we remove an edge, we break the tree, so the subtree sizes change? Actually, in the entire process, we are going to remove multiple edges. However, we can simulate this by considering the entire tree and counting the subtree sizes.

But note: if we remove an edge, the part that gets disconnected is a subtree. So, if a subtree has even size, we can safely remove the edge above it? Because then we disconnect an even-sized component, leaving the rest as even (since total is even). Then, we can continue processing the remaining tree.

So, the algorithm:

1. If `n` is odd, return -1.
2. Build an adjacency list for the tree.
3. Do a DFS (or BFS) starting from a root (say 0) to compute the size of each subtree.
4. Count the number of nodes in each subtree. For each node, if the subtree size is even, then we can remove the edge above it? But note: the root has no edge above, so we don't count that.

Wait, but if we remove the edge connecting a node to its parent, then the subtree rooted at that node becomes a disconnected component. The condition is that the subtree size must be even. Then, we can remove that edge. Then, the rest of the tree (the parent's part) will have `n - subtree_size`, which is also even (because n is even and subtree_size is even). So, each such removal is valid.

But can we remove all such edges? Consider: when we remove an edge, we break the tree. Then, the parent's subtree size decreases. But we are computing the subtree sizes in the initial tree. So, if we remove an edge, the sizes for the ancestors change. Therefore, we cannot just count all subtrees with even size (excluding the root) because removals are not independent.

However, note: the removal of an edge only affects the sizes of its ancestors. But if we process the tree from the bottom up, we can do the removals in post-order. That is, we remove an edge only when the subtree it disconnects is even, and then we remove that entire subtree and reduce the size of the parent's subtree. Then, we continue.

But in the DFS, we compute the initial sizes. Then, if a subtree (excluding the root) has even size, we can remove the edge above it. Then, we set the size of that subtree to zero for the parent? Or, we can simulate removals by doing a DFS and whenever we find a subtree (node) with even size (and the node is not the root), we remove it and set its size as 0 for the parent.

So, the algorithm:

- Start DFS from root (say node 0).
- For each child, recursively compute the subtree size.
- After processing children, the subtree size of the current node is 1 + sum of subtree sizes of children.
- Then, if the subtree size of the current node is even and the current node is not the root, we can remove the edge from its parent to it. Then, we set the size of this node to 0? Actually, no: because we are removing the entire subtree, so in the parent's context, this branch is gone. So, we can set the size of this subtree as 0 for the parent? Alternatively, we can count the removal and then not include this subtree in the parent's size.

Wait, but in the DFS, we are computing the sizes. How can we update the parent's size if we remove the edge? Actually, we are not actually removing edges during the DFS; we are counting the number of edges that can be removed. So, if a subtree has even size and is not the root, we count one removal, and then we don't consider that subtree for the parent's size? That is, we subtract the entire subtree from the parent? Actually, no: because the DFS is computing the total subtree size. But if we remove the edge, then that subtree is disconnected, so for the parent's component, that subtree is gone. Therefore, the parent's subtree size should be computed without that child's subtree.

So, we can do:

- Let `size[u] = 1` (the node itself)
- For each child `v` of `u`:
   - Recurse to `v`
   - After recursion, if `size[v]` is even, then we remove the edge `(u, v)`, and we do not add `size[v]` to `size[u]` (because it's disconnected). Otherwise, we add `size[v]` to `size[u]`.

But then, what about the rest of the tree? We are effectively building the connected component that includes the root by ignoring the even-sized subtrees that we disconnect. Then, at the end, the root's component must be even? Since we started with even `n` and we've disconnected several even-sized subtrees, the root's component must be even as well (because even minus even is even). So, we don't need to check the root? Actually, the root's component at the end will be `n - sum(disconnected subtrees)`, which is even because both `n` and each disconnected subtree is even.

Therefore, the algorithm:

1. If `n % 2 != 0`: return -1.
2. Build graph from edges.
3. Initialize an array `size` of length `n+1` with 0 (or 1? we'll set each to 1 initially for the node itself).
4. Use DFS (post-order) starting from root (node 1, but we can use 0-indexed: nodes 0 to n-1).
   - For each node, set `size[node] = 1`.
   - For each neighbor (child) of the node (but avoid parent), recursively call DFS on the neighbor.
   - Then, if the child's subtree size is even, then we remove the edge to that child: so we increment removal count and do not add the child's size to the current node's size.
   - Else, we add the child's size to the current node's size.

But wait: if the child's subtree size is even, we remove the edge and do not add the size. Otherwise, we add. So the current node's size becomes 1 + sum of sizes of children that have odd subtree sizes.

But note: the child's subtree size is computed including itself. If we don't remove the edge, then the child's subtree is still attached, so we add its size. If we remove, we don't.

Then, the removal count is the number of edges we removed.

But is that correct? Let me test with the example.

Example 1: n=4, edges: [2,4], [4,1], [3,1]. Let me reindex: vertices 1,2,3,4. Let me use 0-indexed: 0:1, 1:2, 2:3, 3:4.

Edges: 
  2-4: which is 1-3 (if 1: index1, 2: index0? Actually, let's map: 
  vertex 1 -> index0, vertex2->index1, vertex3->index2, vertex4->index3.

Edges: 
  [2,4] -> [1,3] -> index1 and index3
  [4,1] -> [3,0] -> index3 and index0
  [3,1] -> [2,0] -> index2 and index0

So the tree: node0 (vertex1) connected to node3 (vertex4) and node2 (vertex3). Then node3 (vertex4) connected to node1 (vertex2).

So the tree: 
  0 -- 3 -- 1
  |
  2

If we root at 0:

Node0: connected to 3 and 2.
Node3: connected to 1 (and 0, which is parent).

Now, DFS:

Start at 0. Then, for each child: 3 and 2.

First, go to child 3 of 0.

At node3: it has neighbors: 0 (parent) and 1. So go to 1.

At node1: no children (except parent 3). So size[1] = 1 (odd). Then, return to node3.

Now, node3: has child 1 with size 1 (odd), so add to node3: size[3] = 1 (itself) + 1 (from child1) = 2 (even). Then, since node3 is not the root, and its size is even: so we remove the edge from 0 to 3? Then, we do not add 2 to node0? And we count one removal.

But then, node0 also has child 2.

At node2: no children (only parent). So size[2] = 1 (odd). Then, we don't remove the edge? So we add to node0: size[0] = 1 (itself) + 1 (from child2) = 2? Then, the root's component is 2 (even). But we removed one edge.

So total removals: 1. Correct.

But in this process, we removed the edge from 0 to 3 because the subtree at 3 had size 2 (even). Then, for the root, we only added the size from child2 (which is 1) so root's size becomes 2.

But actually, after removing the edge to 3, the root is connected to node2 only, so the root's component is two nodes: 0 and 2? Then, the subtree at 3 is 3 and 1? So two components: [0,2] and [3,1]. Both size 2.

But in the DFS, we didn't consider the root's component? Actually, we did: the root's size after processing is 2, which is even. But we don't remove the root's parent edge because it doesn't exist.

So the algorithm: do DFS from root. For each child, after processing, if the child's subtree size is even, remove the edge (so count removal) and do not add the child's size to the parent. Otherwise, add the child's size.

But what if the child's subtree is odd? Then we add it to the parent. Then, the parent's subtree size becomes larger. Then, when the parent is processed, its size is 1 + sum of odd-sized children. 

But is it possible that a subtree that is odd cannot be split? Actually, we are not splitting it at this step; we are just adding it to the parent. Then, when we process the parent, the parent's subtree might become even, and then we remove the edge above the parent? But the parent's subtree then becomes disconnected, and the root's component is reduced.

But note: the entire tree is even. So the root's component at the end must be even. And each removal disconnects an even-sized subtree.

Now, test with the star example: center 0 connected to leaves 1,2,3. But n=4? Then the star has 4 nodes. Center 0, and leaves 1,2,3. Then:

Root at 0.

For each child (1,2,3):

At child1: size=1 (odd). Then, we add to parent: so parent0 will have 1 (itself) + 1 (from child1) = 2? Then, we haven't removed child1's edge. Then, for child2: size=1, odd, then parent0: 2+1=3. Then for child3: size=1, odd, then parent0: 3+1=4. Then, since the parent0's subtree size is 4 (even) and it is the root? We don't remove any edge because we only remove edges to children that have even subtree. But in this case, no child had even subtree. So we don't remove any edge. Then the entire tree is one component of size 4 (even), so we can remove 0 edges. Correct.

But wait, can we remove two edges? Then we get two isolated leaves and a component of the center and one leaf: which has two leaves (each size 1, odd) and the center and one leaf (size 2, even). Then we have two odd components. So invalid. So we cannot remove any edge. So the algorithm returns 0, which is correct.

But the problem says maximum number of edges we can remove. In this star, we can remove 0. But in the linear tree, we could remove 1. So the algorithm must return 0 for the star.

Now, the example 3: n=10, and output is 4. So we should be able to remove 4 edges.

Let me see: if we can remove 4 edges, then we have 5 components? Because each removal increases the number of components by one. And each component must be even. So the sizes: even numbers that sum to 10. And we have 5 components. The only possibility is all components of size 2? Because 5*2=10. So we must have 5 pairs.

So, the algorithm: if we can remove 4 edges, then we must have 4 subtrees of even size? Actually, each removal disconnects one even-sized subtree. But after 4 removals, we have the root's component which is also even. So we have 5 even-sized components. But the algorithm counts each removal when we disconnect an even-sized subtree. So the removal count is 4, which is the number of edges we removed. Then the root's component is the fifth even component.

So the algorithm: the number of removals is the number of subtrees (excluding the root) that have even size, but only if we adjust the sizes as we remove? Actually, in the DFS we described, we only count a child's subtree as removable if its initial computed size is even? But we are adjusting: because if a child's subtree is not even, we add it to the parent, which may cause the parent to become even? Then, we remove the parent's edge? But in the DFS, we are processing from the bottom up. So when we process a node, we have the size including all children that were not removed. Then, if that size is even and the node is not the root, we remove the edge above and count it.

But in the DFS, we are not actually removing the edges physically; we are just deciding at each child whether to remove the edge to the parent. And if we remove, then we don't add the child's size to the parent. Otherwise, we do.

So the algorithm is:

def dfs(u, parent):
    size[u] = 1
    for each child v of u (excluding parent):
        dfs(v, u)
        if size[v] % 2 == 0:
            removal_count += 1
        else:
            size[u] += size[v]

Then, after DFS, removal_count is the answer.

But note: we only remove when the subtree size is even. And we don't add the size if we remove. So the parent's size only accumulates the odd-sized subtrees and itself.

But then, the root: we never remove the root's parent (it doesn't have one), so we don't count the root.

Now, test with the linear tree of 4 nodes: 0-3-1 and 0-2. Actually, in the example:

Nodes: 0, 1, 2, 3.

Edges: 
  0-3, 0-2, 3-1.

So the graph: node0 connected to 3 and 2; node3 connected to 1.

Root at 0.

DFS(0, -1):
   size[0] = 1
   children: 3 and 2.
   First, child 3: 
        DFS(3,0):
            size[3] = 1
            children: 1 and 0 (0 is parent, so skip). Then, child 1:
                DFS(1,3): 
                    size[1]=1 -> no children? 
                    then, for each child of 1: none, so return.
                Then, at 3: check child1: size[1]=1 (odd) -> so add to size[3]: 1+1=2.
                Then, since size[3] is even, and parent exists, we remove the edge from 0 to 3? removal_count +=1 -> 1.
                And we don't add size[3] to 0? 
   Then, next child of 0: 2.
        DFS(2,0):
            size[2]=1 -> no children? so size[2] is 1 (odd). Then, we add to size[0]: 1 (current) + 1 = 2.
   Then, at 0: size[0]=2, but we don't remove because it's the root? So removal_count=1.

Correct.

Now, for the star of 4: center0 connected to 1,2,3.

DFS(0,-1):
   size[0]=1
   for each child: 1,2,3.
      child1: size[1]=1 -> odd -> add to 0: size[0] becomes 2? 
      Then, we check: after adding child1, size[0] becomes 2? Then, we don't remove the edge to child1 because we only remove if the child's subtree is even? Here, child1's subtree was 1 (odd), so we don't remove and add it. Then move to child2: same: size[2]=1 (odd) -> add: size[0]=3. Then child3: size=1, add: size[0]=4. 
   Then, we don't remove any edge? removal_count=0.

Correct.

Now, what about a larger example? Example3: n=10, output 4.

We have the input: 
10
7 1
8 4
8 10
4 7
6 5
9 3
3 5
2 10
2 5

Let me map the vertices to 0-indexed.

Vertices: 1 to 10 -> indices 0 to 9.

Edges:
7 1 -> 6,0
8 4 -> 7,3
8 10 -> 7,9
4 7 -> 3,6
6 5 -> 5,4
9 3 -> 8,2
3 5 -> 2,4
2 10 -> 1,9
2 5 -> 1,4

So edges:

(6,0)
(7,3)
(7,9)
(3,6) -> same as (6,3)
(5,4) -> edge between node5 and node4
(8,2)
(2,4) -> node2 and node4
(1,9)
(1,4) -> node1 and node4

Wait, this is messy. Let me try to build the graph.

List of edges (0-indexed):

0: vertex1 -> index0
1: vertex2 -> index1
2: vertex3 -> index2
3: vertex4 -> index3
4: vertex5 -> index4
5: vertex6 -> index5
6: vertex7 -> index6
7: vertex8 -> index7
8: vertex9 -> index8
9: vertex10-> index9

Edges:

7 1 -> 6,0 -> edge (6,0)
8 4 -> 7,3 -> edge (7,3)
8 10->7,9 -> edge (7,9)
4 7 -> 3,6 -> edge (3,6)
6 5 -> 5,4 -> edge (5,4)
9 3 -> 8,2 -> edge (8,2)
3 5 -> 2,4 -> edge (2,4)
2 10->1,9 -> edge (1,9)
2 5 ->1,4 -> edge (1,4)

So the edges:

(6,0)
(7,3)
(7,9)
(3,6) -> same as (6,3) -> duplicate? Or undirected, so same.
(5,4)
(8,2)
(2,4)
(1,9)
(1,4)

But wait, the input has 9 edges? Because n=10, so n-1=9.

Now, we have 9 edges. Let me try to build the graph.

Alternatively, we can draw the tree. But it's complex. Instead, we can run the algorithm and see if we get 4 removals.

But to reason: the algorithm counts an edge removal for every subtree (not root) that has even size after accumulating the odd-sized children. So, if we do the DFS, we might get 4.

I'll assume that the algorithm is correct. Because the problem constraints are 10^5, and DFS is O(n). 

But what about the order? We process from bottom up. So we must do DFS in post-order. 

Implementation: we can use recursion, but n=10^5, so recursion depth might be 10^5, which in Python might cause recursion limit. So we should use iterative DFS? Or set recursion limit? Or use BFS for the tree (like a stack).

Alternatively, we can do a BFS for topological order (leaves to root). 

But the algorithm:

- Build graph as an adjacency list.
- We need to track parent to avoid going back.
- We can use a stack for DFS, or recursion.

But to avoid recursion depth, we can use iterative DFS.

Steps for iterative DFS:

1. Build graph.
2. Create an array `size` of length n, initialized to 1.
3. Create an array `parent` or use a stack that stores (node, parent).
4. Alternatively, do a BFS to get the order? Actually, we need to process children first. So we can do:

   - First, do a BFS/DFS to get the order of nodes from leaves to root (reverse topological order). 
   - We can compute the in-degree? But tree: leaves have degree 1 (except root). 
   - Actually, we can use a queue for leaves: start with leaves (degree 1) and then process, then reduce the degree of the neighbor, etc. But that is BFS from leaves.

But for a tree, we can do:

   - Create an array `deg` for degrees? Actually, we can use a stack for DFS.

Alternatively, we can do:

   - Use a stack for DFS: push root, then for each node, push its children (excluding parent). But we need to process children first. So we can do:

        stack = [root]
        order = []
        while stack:
            u = stack.pop()
            order.append(u)
            for each neighbor v of u, except parent, push v and set parent[v]=u.

        Then, reverse the order to get from leaves to root.

Then, traverse the order in reverse:

        for node in reversed(order):
            size[node] = 1
            for each neighbor v (children only? but in the tree, we have parent link) -> actually, we have parent array, so we can skip the parent.
            Then, for each child v of u (v != parent[u]), then:
                if size[v] is even: then removal_count++ 
                else: size[u] += size[v]

But note: in the reverse order, when processing a node, all its children have been processed.

So:

1. Precompute parent and order.

   - We can do BFS from root to get the parent and the order. Then, we know the children for each node? Or we can store children.

   Steps:

      Build graph: `graph = [[] for _ in range(n)]`
      For each edge (u,v):
          graph[u].append(v)
          graph[v].append(u)

      Then, from root (say 0), do BFS:

          from collections import deque
          parent = [-1] * n
          order = []
          q = deque([0])
          parent[0] = -1
          while q:
             u = q.popleft()
             order.append(u)
             for v in graph[u]:
                 if v == parent[u]:
                     continue
                 parent[v] = u
                 q.append(v)

          Then, reverse the order.

      Then, initialize `size = [1] * n`
      removal_count = 0
      For node in reversed_order:
          for each child v of node (v != parent[node]? Actually, we can store children? Or in the graph, we can skip the parent. But we can also precompute children:

          children = [[] for _ in range(n)]
          For each node, for neighbor in graph[node]:
              if neighbor != parent[node]:
                 children[node].append(neighbor)

      Then, in reverse order:

          for u in reversed_order:
              for v in children[u]:
                  if size[v] % 2 == 0:
                      removal_count += 1
                  else:
                      size[u] += size[v]

      Then, output removal_count.

But note: the root has no parent, so we don't remove the root's edge. And we only consider children.

This should work.

But what if a node has no children? Then it's a leaf, and in reverse order, we process it first. Then, for the leaf, we don't have any children, so we skip. Then the leaf's size is 1.

Now, test with the linear tree of 4 nodes: nodes 0,1,2,3.

Structure: 0 is root. 0 connected to 1 and 2. And 1 connected to 3.

Wait, in the example, we had: 
  0 connected to 3 and 2, and 3 connected to 1.

So, for node0: children: 3 and 2.
Node3: children: 1 (and parent 0, which is skipped).
Node1: no children? (leaf).
Node2: leaf.

So, reverse order: leaves first? Actually, BFS order from root: 

Start at 0: 
   queue: [0]
   Pop 0: add to order: [0]. Then push children: 3 and 2 -> queue=[3,2]
   Pop 3: add to order: [0,3]. Then push children of 3: 1 -> queue=[2,1]
   Pop 2: add to order: [0,3,2] -> then no children? 
   Pop 1: add: [0,3,2,1]

Reverse order: [1,2,3,0]

Now, process:

u=1: leaf -> no children? skip. size[1]=1.
u=2: leaf -> skip. size[2]=1.
u=3: children: [1] (because we stored children? How? 

In the BFS, we set parent for each child. Then, for node3, we get children: all neighbors except parent (which is 0). So neighbor 1: not parent? parent[1]=3? So yes, so 1 is a child of 3.

So at u=3: 
   for child v=1: 
        size[1]=1 -> odd -> so add to size[3]: 1 (itself) +1 = 2 -> even. Then, because 2 is even, and 3 is not root? Then, we remove the edge from 3 to its parent? But the parent is 0. Then removal_count +=1.

Then, at u=0: 
   children: [3,2]? But we stored children: for node0, neighbors: 3 and 2 (and parent -1, so skip parent). So children: 3 and 2.
   Now, for child v=3: 
        size[3] is 2 -> even -> removal_count +=1? Then we remove the edge from 0 to 3? But we already removed the edge from 3 to 0? Actually, in the reverse order, we process 3 and then 0. 

Wait, no: we removed the edge at node3: that is the edge from 3 to its parent (0). Then, at node0, when we process, we see child 3: but we have already removed that edge? How do we know? 

Actually, in our algorithm, when we remove an edge, we don't physically remove it from the graph. But at node3, we decided to remove the edge to its parent (0). Then, at node0, we are iterating over its children: which includes 3. But then, for child 3, we check size[3]. We just set size[3]=2 (because we added the child 1? even though we removed the edge? Actually, no: in the removal, we are not changing the size of the subtree? 

But in the algorithm: we are accumulating the size of the subtree that remains attached. When we remove the edge from parent to child, we are disconnecting that entire subtree. But in the reverse order, when processing node3, we set its size to 2 and then we remove the edge to its parent. Then, when we process the parent (node0), we should not consider that child? But in the children list for node0, we still have 3. 

This is a problem: because the edge is removed, so the subtree at 3 is disconnected. Then, for node0, we should not include the size of 3? But in our algorithm, we are processing nodes independently. 

In the reverse order: we process 1, then 2, then 3, then 0.

At node3: we have child 1. We add its size (1) to node3? Then node3 becomes 1+1=2. Then, because 2 is even and node3 is not the root, we remove the edge to its parent (0). Then, we count removal.

At node0: we have two children: 3 and 2. But for child3: we check size[3]=2 (even) -> so we remove the edge? Then removal_count +=1? Then we remove the same edge twice? That's not possible.

So the issue: we are counting the same edge twice? 

Actually, no: the edge from 0 to 3 is the same as from 3 to 0. At node3, we remove the edge to its parent (0). Then, at node0, we see the same edge and check the same condition? But we already removed it? How does the algorithm know that we removed it?

In the algorithm, we don't mark the edge as removed. So when we process node0, we see child3: then we check size[3]=2 (which we set at the processing of node3). Then we say: even, so remove again? Then we count two removals for the same edge.

But that's incorrect.

Therefore, we must avoid counting the same edge twice. The solution: we only remove the edge at the child side. That is, when we process a node (which is not the root), if after accumulating the sizes of its children that were not removed (so the ones that had odd size and were kept), the total size of the node becomes even, then we remove the edge from this node to its parent. And we count one removal.

But note: we do not add the size to the parent? Actually, in the algorithm, we are not updating the parent's size at the time of removal. But when we process the parent, we look at the child's size. But if we removed the edge at the child, then the child's subtree is disconnected, so the parent should not include that child in its size. 

But in the reverse order, we process the child before the parent. So when we process the child and remove the edge, then we don't update the parent's size? How? 

In the algorithm: 

   We have an array `size` for each node. Initially 1.

   Then, for each node in reverse order (from leaves to root):

        for each child of the node:
            if the child's size is even: 
                removal_count += 1   # remove the edge to this child
            else:
                size[node] += size[child]   # keep the child and add its size

        Then, after processing all children, if the node's size is even and the node is not the root, we remove the edge above? But wait, no: in the above algorithm, we don't do that. 

Actually, the above algorithm only removes the edge to the child if the child's subtree is even. But what if the child's subtree is odd, and then the current node becomes even when we add all odd children? Then, we cannot remove the edge to the child, but we can remove the edge to the parent? 

So the algorithm should be:

   for node in reversed_order:
        for each child in children[node]:
            if size[child] % 2 == 0:
                removal_count += 1
            else:
                size[node] += size[child]
        # and then, after processing children, we don't check the current node's size for removal? 

But then, we are only removing edges to children that are even. And if a child is odd, we add it to the parent. Then, the parent's size becomes larger. Then, when we process the parent, we might remove the parent's edge if the parent's size becomes even? But no, because we are only checking the children's sizes, not the parent's own size after accumulation.

So the algorithm we described earlier does not account for the possibility that a node might become even after accumulating odd children. 

But actually, the condition for removal is: we remove the edge to the parent if the entire subtree of the current node (after accumulating all children that were not removed) is even. So we should check after processing the children, the total size of the node. Then, if it's even and the node is not the root, we remove the edge to the parent. But then, we don't add the size to the parent? But the parent is processed later. 

But the algorithm we have only removes edges to children. How do we remove the edge to the parent? 

We must note: the edge from a node to its parent is considered at the node itself. So:

   When processing a node (in reverse order), we:
        set size[node] = 1 (the node itself)
        for each child:
            if the child's subtree was even, then we remove the edge to that child (so we don't add the child's size)
            else, we add the child's size to the current node.
        Then, after processing children, the current node's size is the size of the connected component that includes the node and the children that were not removed (and their odd-sized subtrees). 
        Then, if this size is even and the node is not the root, we remove the edge from the node to its parent. But how? 

But wait, we don't have an explicit step to remove the edge to the parent. In the algorithm, we are only removing edges to children. 

Actually, the edge to the parent is the same as the edge from the parent to the node. So we should only remove it once. We can remove it when processing the node (the child side) by checking the entire subtree of the node. 

But in the algorithm, we are not checking the entire subtree of the node at the end. We are only checking each child individually.

So the corrected algorithm:

   for node in reversed_order:
        for each child in children[node]:
            # we do not remove the edge to the parent here; the edge from node to child is considered at the child's processing? 
            # Actually, no: the edge from node to child is considered when we process the child? No, we process the child and then we look at the child's size to decide if we remove the edge from the node to the child.

        Then, after processing all children, the node has a total size (1 + sum of sizes of children that were not removed).

        Then, if the node is not the root and the total size is even, we remove the edge to the parent? But then, we don't add the node's size to the parent? But the parent hasn't been processed yet.

        How do we communicate to the parent that we removed the edge? 

        Actually, we don't need to: because when we process the parent, we skip the children that were removed? But we have the children list. 

        Alternatively, we don't remove the edge at the child? But we remove the edge at the parent side? 

        Actually, we can remove the edge at the child node: meaning, when processing the child, we decide to remove the edge to the parent. Then, we do not add the child's size to the parent. 

        But how do we not add the child's size to the parent if we remove the edge? 

        The algorithm:

            We do not remove the edge at the parent's processing. We only remove at the child's processing.

        So at the child's processing (in reverse order), after we have computed the child's total size (including all its children that were not removed), we check: if the child's total size is even, then we remove the edge from the child to the parent. Then, we do not add the child's size to the parent. 

        But if the child's size is odd, we do add.

        Therefore, the parent's size is computed as: 1 + sum of the sizes of all children for which we did not remove the edge (i.e., the ones that had odd size).

        And then, the parent's size might be even or odd. But we don't remove the edge at the parent's level? 

        Actually, no: because the edge from the parent to the parent's parent will be considered when we process the parent. 

        So the algorithm is:

            removal_count = 0
            size = [1] * n
            # Get reverse BFS order
            for u in reversed_order:
                if u == root:   # skip root for removal
                    continue
                if size[u] % 2 == 0:
                    removal_count += 1
                else:
                    # if the child's subtree is odd, then we add it to the parent's size
                    size[parent[u]] += size[u]

            Then, output removal_count.

        But wait, is that correct? 

        Test with the linear tree: nodes 0 (root), 3, 2, 1. And edge: 0-3, 0-2, 3-1.

        Reverse order: [1,2,3,0] (if BFS order: [0,3,2,1] then reversed: [1,2,3,0])

        Process u=1: 
            parent[1]=3
            size[1]=1 -> odd -> so we do: size[parent[1]] = size[3] += size[1] -> size[3] becomes 1 (initial) +1 = 2.
        Then, removal_count: 0 (because we didn't remove? But we didn't check for removal at u=1: we check if size[u] is even? At u=1: 1 is odd, so we don't remove and add to parent.

        Then, u=2: 
            parent[2]=0
            size[2]=1 -> odd -> so add to parent: size[0] +=1 -> size[0] becomes 1 (initial) +1 = 2.
        Then, u=3:
            parent[3]=0
            size[3]=2 -> even -> removal_count +=1 -> becomes 1.
            Then, we do not add to parent.
        Then, u=0: root, skip.

        So removal_count=1. Correct.

        Now, the star: center0, and leaves 1,2,3.

        Reverse order: leaves then center. 
        BFS order: [0,1,2,3] -> reversed: [3,2,1,0]

        u=3: parent=0, size=1 -> odd -> add to parent: size[0] +=1 -> size[0]=1+1=2.
        u=2: parent=0, size=1 -> odd -> add: size[0]=2+1=3.
        u=1: parent=0, size=1 -> odd -> add: size[0]=3+1=4.
        u=0: skip.

        Then, removal_count=0. Correct.

        Now, what if a node becomes even only after adding an odd child? 

        Example: 

        Root: 0
        Child: 1 (edge1: 0-1)
        Child of 1: 2 and 3 (edges: 1-2, 1-3)

        So the tree: 0 connected to 1, and 1 connected to 2 and 3.

        Reverse order: BFS: [0,1,2,3] -> reversed: [3,2,1,0]

        Process u=3: parent=1, size=1 -> odd -> add to parent: size[1] +=1 -> size[1]=1 (initial) +1=2.
        Then, u=2: parent=1, size=1 -> odd -> add: size[1]=2+1=3.
        Then, u=1: parent=0, size=3 -> odd -> add to parent: size[0] =1+3=4.
        Then, u=0: skip.

        So removal_count=0.

        But can we remove any edge? The entire tree is size 4 (even). We can remove the edge 0-1: then we have two components: [0] (size1) and [1,2,3] (size3) -> both odd -> invalid. Or remove an edge from 1 to 2: then we have [0,1,3] (size3) and [2] (size1) -> invalid. So we cannot remove any edge. Correct.

        Another example: 

        Node0 connected to 1, node1 connected to 2 and 3, and node2 connected to 4 and 5. n=6.

        We want to remove as many as possible.

        Structure: 

            0
            |
            1
           / \
          2   3
         / \
        4   5

        We can remove edge 1-2: then we have a subtree [2,4,5] (size3) and the rest: [0,1,3] (size3) -> both odd -> invalid.

        Alternatively, remove edge 2-4: then we have [4] (size1) and the rest? which is size5 -> invalid.

        Alternatively, remove edge 2-4 and 2-5? Then we have [4] and [5] and the rest [0,1,2,3] (size4). Then we have two odd and one even? invalid.

        Alternatively, remove edge 1-2 and then edge 0-1? 

        First, remove edge 1-2: then we have two components: [2,4,5] and [0,1,3]. Then, in [0,1,3]: remove edge 0-1? Then we have [0] and [1,3] and [2,4,5]. Then we have three components: [0] (1), [1,3] (2), [2,4,5] (3). Only [1,3] is even. 

        Alternatively, remove edge 0-1 first: then we have [0] and [1,2,3,4,5]. Then, in the big component, we can remove the edge 2-4? Then we have [4] and the rest: [1,2,3,5] (size4) -> but [1,2,3,5]: is it connected? Without 4, and the edge 1-2 is still there? Then the component is connected. Then we have [0] (1), [4] (1), and [1,2,3,5] (4). Then two odd and one even: invalid.

        How to get two removals? 

        We can remove edge 1-2: then we have two components: [2,4,5] (size3) and [0,1,3] (size3). Then, we cannot remove any more edges because each removal would require an even-sized component to split, but both are odd. So we cannot remove more. 

        Actually, the entire tree has size 6. We can split into three components of size 2. 

        How? Remove the edge 1-2 and the edge 1-3? 

        After removing 1-2 and 1-3: then we have [0,1] (size2), [2,4,5] (size3), [3] (size1). -> invalid.

        Alternatively, remove edge 2-4 and 2-5? Then we have [4], [5], and [0,1,2,3] (size4). Then, in the size4 component, we can remove edge 0-1? Then we have [0], [1,2,3], [4], [5]. -> [0] and [1,2,3] (size3) and [4],[5]: invalid.

        How about: remove edge 1-0 and edge 2-1? 

        But edge 2-1 is the same as 1-2. 

        Actually, we can remove edge 1-0 and edge 2-4 and 2-5? 

        Then components: [0], [1,2,3], [4], [5] -> invalid.

        What if we remove edge 0-1, edge 2-4, edge 2-5? Then we have [0], [1,2,3] (size3), [4], [5]: invalid.

        Alternatively, remove edge 1-3 and edge 2-4, edge 2-5? Then we have [0,1,2] (size3) and [3], [4], [5]: invalid.

        Maybe we need to remove edges that disconnect even components:

        Remove the edge above node2: which is edge 1-2. But node2's subtree: [2,4,5] has size 3, which is odd, so we don't remove.

        Then, what if we remove the edge above node4? Then we disconnect node4 (size1) -> but then we get an odd component. So we cannot.

        How about: 

        Step1: remove edge 2-4: then we have component [4] (size1) -> invalid? But we are not done; we can remove more edges. But then the rest of the tree is size5, which is odd -> cannot be split into even components. 

        So actually, we can only remove edges that disconnect even components. 

        How to get even components? 

        Notice: the subtree of node2: [2,4,5] has size3 (odd). The subtree of node1: [1,2,3,4,5] has size5 (odd). The subtree of node0: [0,1,2,3,4,5] has size6 (even). 

        The only even subtrees are the leaves? But leaves are size1. 

        Actually, the entire tree is even. But to remove an edge, we need a subtree of even size. 

        What are the even-sized subtrees? 

        The entire tree: size6 -> but we cannot remove the root's edge (it doesn't exist). 

        The subtree rooted at node1: size5 (odd). 
        The subtree at node2: size3 (odd). 
        The subtree at node3: size1 (odd). 
        The subtree at node4:1, node5:1.

        Then, are there any even-sized subtrees? 

        Consider: the subtree that includes node2, node4, node5: size3. 

        Or, if we root at a different node? 

        The problem does not specify a root. The tree is unrooted. But our algorithm picks a root arbitrarily. 

        But note: the condition for removing an edge is: when we remove an edge, one of the resulting components must be even. 

        For edge 0-1: if we remove it, the component containing 0 is size1, the other is size5: both odd. 
        For edge 1-2: removing it: component with 2,4,5 (size3) and the rest (0,1,3) size3: both odd.
        For edge 1-3: removing it: component [3] (size1) and the rest size5: odd.
        For edge 2-4: removing: [4] (1) and the rest (5) -> invalid.
        Similarly for 2-5.

        So no edge can be removed. Therefore, we must have removal_count=0. 

        But the entire tree is even, so we can have 0 edges removed. Then, the answer is 0? 

        But the problem: maximum number of edges we can remove. We can remove 0 edges and have one component (even). So 0 is valid.

        So the algorithm: 

            if n % 2 != 0: return -1
            build tree, pick root 0.
            BFS to get parent and reverse order.
            size = [1]*n
            removal_count = 0
            for node in reversed_order (excluding root):
                if size[node] % 2 == 0:
                    removal_count += 1
                else:
                    size[parent[node]] += size[node]

            return removal_count

        This algorithm will return 0 for the above 6-node tree. Correct.

        Now, what if we have a tree where a node becomes even after adding an odd child? 

        Example: 

        Node0 (root)
          |
        Node1
         /  \
        Node2 (leaf)  Node3 (leaf)

        So edges: 0-1, 1-2, 1-3.

        Reverse order: BFS: [0,1,2,3] -> reversed: [3,2,1,0]

        Process node3: size=1 -> odd -> add to parent: size[1] +=1 -> size[1]=1+1=2.
        Then, node2: size=1 -> odd -> add to parent: size[1]=2+1=3.
        Then, node1: size=3 -> odd -> add to parent: size[0] =1+3=4.
        Then, root skipped.

        Then removal_count=0.

        But can we remove any edge? 

        The entire tree is size4 (even). We can remove an edge? 

        Remove edge 1-2: then we have components: [0,1,3] (size3) and [2] (1): both odd -> invalid.
        Remove edge 1-3: similar.
        Remove edge 0-1: then [0] and [1,2,3] (size3): invalid.

        So 0 removals. Correct.

        But consider a different tree of 4 nodes: a chain 0-1-2-3.

        If we root at 0:

           0
           |
           1
           |
           2
           |
           3

        Reverse order: [3,2,1,0]

        Process 3: parent=2, size=1 -> odd -> add to 2: size[2]=1+1=2.
        Then, 2: parent=1, size=2 -> even -> removal_count +=1 (so 1), and do not add to 1.
        Then, 1: parent=0, size=1 (because we didn't add 2) -> odd -> add to 0: size[0]=1+1=2.
        Then, 0: skip.

        So removal_count=1. Correct.

        Now, what about a more complex tree? 

        Example3: n=10, output=4.

        We trust that the algorithm will output 4.

        Complexity: O(n). 

        Implementation:

        Steps:

        1. If n % 2 != 0: return -1.
        2. Build graph: list of lists, size n.
        3. Use BFS (queue) to compute parent and get the BFS order. Then reverse.
        4. Initialize size = [1] * n
        5. removal_count = 0
        6. For each node in reversed_order (if the node is root, skip):
                if size[node] % 2 == 0:
                    removal_count += 1
                else:
                    size[parent[node]] += size[node]
        7. Print removal_count.

        But note: when we remove an edge (i.e., when we find an even size at a non-root node), we do nothing to the parent? We just count. The parent's size remains unchanged. Only if the child's subtree is odd, we add its size to the parent.

        This matches the condition: we remove the edge if the subtree we are disconnecting is even. Then, the parent's component loses that subtree. Otherwise, we keep the subtree and add its size to the parent.

        So we'll implement accordingly.

        Edge Cases:

        - n=1: then n is odd -> return -1.
        - n=2: even. Then tree: one edge. 
                Root at 0: 
                    BFS: [0,1] -> reversed: [1,0]
                    Process 1: parent=0, size=1 -> odd -> so add to parent: size[0] = 1+1=2.
                    Then, removal_count=0. 
                So output 0. Correct: we cannot remove any edge? Actually, we can remove the edge? But if we remove the edge, we get two components of size1 (each odd) -> invalid. So we must not remove any edge. So 0 is correct.

        Example2: n=3 -> odd -> return -1.

        So we'll code accordingly.

        Let me write the code.

        Note: we use 0-indexed. The root is 0.

        Steps in code:

            if n % 2 != 0:
                return -1

            graph = [[] for _ in range(n)]
            for edge in edges:
                u = edge[0]-1
                v = edge[1]-1
                graph[u].append(v)
                graph[v].append(u)

            # BFS to get parent and order
            from collections import deque
            parent = [-1] * n
            order = []
            q = deque([0])
            parent[0] = -1
            while q:
                u = q.popleft()
                order.append(u)
                for v in graph[u]:
                    if v == parent[u]:
                        continue
                    parent[v] = u
                    q.append(v)

            # reverse the order
            rev_order = order[::-1]
            size = [1] * n
            removals = 0
            # Traverse from leaves to root (excluding root)
            for u in rev_order:
                if u == 0:  # root, skip
                    continue
                if size[u] % 2 == 0:
                    removals += 1
                else:
                    size[parent[u]] += size[u]

            return removals

        Let me test with the provided examples.

        Example1: n=4, edges: [2,4] -> (1,3), [4,1] -> (3,0), [3,1] -> (2,0). 
        So edges: (1,3), (3,0), (2,0) in 0-indexed: 
            vertices: 0:1, 1:2, 2:3, 3:4.

        So edges: 
            (1,3): node1 and node3
            (3,0): node3 and node0
            (2,0): node2 and node0

        Actually, the input: 
            "4
             2 4
             4 1
             3 1"

        So: 
            edge1: 2-4 -> 1 and 3 (0-indexed: 1->1, 4->3 -> so nodes 1 and 3)
            edge2: 4-1 -> 3 and 0 (4->3, 1->0)
            edge3: 3-1 -> 2 and 0 (3->2, 1->0)

        So edges: (1,3), (3,0), (2,0). 

        Graph:
            node0: [3, 2]  # because edges to 3 and 2
            node1: [3]     # edge to 3
            node2: [0]     # edge to 0
            node3: [1,0]   # edges to 1 and 0

        Then, BFS from 0:
            queue: [0]
            Pop 0: visit 3 and 2. 
                From 0, go to 3: parent[3]=0 -> queue=[3,2]
                From 0, go to 2: parent[2]=0 -> queue=[3,2] (then push 3 and 2)
            Pop 3: visit neighbors: 1 and 0. 0 is parent, so skip. Then 1: parent[1]=3 -> queue=[2,1]
            Pop 2: neighbors: 0 (parent) -> skip. No new nodes.
            Pop 1: neighbors: 3 (parent) -> skip.

            Order: [0,3,2,1] -> reversed: [1,2,3,0]

        Then, 
            u=1: parent[1]=3, size[1]=1 (odd) -> so add to parent: size[3] +=1 -> size[3] becomes 1+1=2.
            u=2: parent[2]=0, size[2]=1 -> odd -> add to parent: size[0] +=1 -> size[0]=1+1=2.
            u=3: parent[3]=0, size[3]=2 -> even -> removals +=1 -> removals=1.
            u=0: skip.

        So return 1. Correct.

        Example3: n=10, we should get 4.

        We'll run the code with the provided edges and see.

        But since the input is given, we can trust.

        Now, what about a tree that allows multiple removals? 

        Example: 

        Root: 0
        Children: 1,2,3
        Then, node1 has children: 4,5
        Node2 has children: 6,7
        Node3 has children: 8,9

        So a tree of 10 nodes: root0, then three children, and each child has two children.

        Then, we should be able to remove the three edges from 0 to 1, 0 to 2, 0 to 3? Because each of 1,2,3 has a subtree of size 3? (themselves and two children: 1+2=3) -> odd. 

        Then, we cannot remove the edges from 0 to 1,2,3? 

        But we can remove the edges from 1 to 4, 1 to 5? Then the subtree of 4: size1 (odd) -> then we add to 1: but 1 then becomes 1+1+? 

        Actually, for node1: 
            size[1]=1
            Then, for child4: size=1 (odd) -> add to 1: size[1]=2
            Then, for child5: size=1 (odd) -> add to 1: size[1]=3 -> then we don't remove the edge from 0 to 1? 

        Alternatively, we can remove the edges from 1 to 4 and 1 to 5? 

        How? In the reverse order:

            Process leaf4: parent=1, size=1 -> odd -> add to 1: so size[1] becomes 1 (itself) +1=2.
            Then, leaf5: parent=1, size=1 -> but then we check: size[5] is 1 -> odd -> add to 1: size[1]=2+1=3.
            Then, at node1: size=3 -> odd -> add to parent0: size[0] +=3.

            Similarly for nodes 2 and 3.

            Then at the end, the root0: size=1 + 3+3+3 = 10? 
            Then we don't remove any edge? 

        But wait, we can remove the edges from 4,5,6,7,8,9? 

        Actually, the leaves: when we process a leaf, we do:

            if size[leaf] %2==0 -> but 1 is odd, so we add to parent.

        So we don't remove any edge.

        But we can remove the edges from 1 to 4 and 1 to 5? How? 

        The algorithm: we only remove the edge from a node to its parent if the entire subtree of the node is even. 

        For node4: size=1 -> odd -> so we don't remove the edge to parent. Then we add to parent. 

        Then, for node1: we have added two leaves: size=1+1+1=3 -> odd, so we don't remove the edge to parent0.

        How to remove the edges to the leaves? 

        Actually, the leaves have size1 (odd), so we cannot remove the edge above a leaf because it would leave an odd component. 

        So we cannot remove any edge? 

        But the entire tree is even. We can leave it as one component? Then 0 removals. 

        But the problem: maximum number of edges we can remove. We might be able to remove edges that are not above leaves? 

        Consider: 

        Remove the edge from 0 to 1: then we get a component of [0,2,3,6,7,8,9] (size7) and [1,4,5] (size3) -> both odd: invalid.

        Remove the edge from 1 to 4: then we get [4] (size1) and the rest: size9 -> invalid.

        How to get even components? 

        We can remove the edge from 1 to 4 and 1 to 5? Then we get two leaves: [4] and [5] (each size1) and the rest: [0,1,2,3,6,7,8,9] (size8). Then we have two odd and one even: invalid.

        Alternatively, remove the edge from 0 to 1, but then the component [1,4,5] is size3 (odd) -> invalid.

        So we cannot remove any edge? 

        But the example says n=10 gives 4. 

        Let me reexamine the example3 input.

        The example3 input:

        10
        7 1
        8 4
        8 10
        4 7
        6 5
        9 3
        3 5
        2 10
        2 5

        After mapping to 0-indexed:

        Vertex: 1->0, 2->1, 3->2, 4->3, 5->4, 6->5, 7->6, 8->7, 9->8, 10->9

        Edges:

        7 1 -> (6,0)
        8 4 -> (7,3)
        8 10-> (7,9)
        4 7 -> (3,6)
        6 5 -> (5,4)
        9 3 -> (8,2)
        3 5 -> (2,4)
        2 10-> (1,9)
        2 5 -> (1,4)

        So edges:

        (6,0)
        (7,3)
        (7,9)
        (3,6)   -> duplicate of (6,3) -> skip? Or we have two-way. Actually, we add both directions in the graph.
        (5,4)
        (8,2)
        (2,4)
        (1,9)
        (1,4)

        Now, let me build the graph:

        Node0: [6]
        Node1: [9,4]   # because (1,9) and (1,4)
        Node2: [8,4]   # (8,2) -> (2,8) and (2,4) -> (2,4) is also an edge
        Node3: [7,6]   # (3,7) and (3,6) -> but 3 is index2? Wait, no: vertex3 is index2? 
            Actually, the edge "4 7" -> vertices 4 and 7 -> indices 3 and 6 -> so edge (3,6). 
            And edge "3 5" -> vertex3 and5 -> indices2 and4 -> edge (2,4). 
            And edge "9 3" -> vertex9 and3 -> indices8 and2 -> edge (8,2). 

        So:

        Node0: [6]
        Node1: [9,4]   # because edges (1,9) and (1,4) -> vertex1 is index0? No, vertex1 is index0? 

        Actually, we mapped:
            vertex1 ->0
            vertex2 ->1
            vertex3 ->2
            vertex4 ->3
            vertex5 ->4
            vertex6 ->5
            vertex7 ->6
            vertex8 ->7
            vertex9 ->8
            vertex10->9

        So:

        Edge (7,1) -> (6,0): so node0 is connected to node6.
        Edge (8,4) -> (7,3): node7 to node3.
        Edge (8,10) -> (7,9): node7 to node9.
        Edge (4,7) -> (3,6): node3 to node6.
        Edge (6,5) -> (5,4): node5 to node4.
        Edge (9,3) -> (8,2): node8 to node2.
        Edge (3,5) -> (2,4): node2 to node4.
        Edge (2,10) -> (1,9): node1 to node9.
        Edge (2,5) -> (1,4): node1 to node4.

        So:

        node0: [6] 
        node1: [9,4]   # from (1,9) and (1,4) -> but vertex2 is node1? 
            Actually, vertex2 is index1. The edge "2 10" is vertex2 to vertex10 -> index1 to index9: so node1 connected to node9.
            The edge "2 5" is vertex2 to vertex5 -> index1 to index4: so node1 connected to node4.

        node2: [8,4]   # from (9,3): which is vertex9 to vertex3 -> but wait, "9 3" is vertex9 to vertex3 -> index8 to index2: so node8

## Final Solution

