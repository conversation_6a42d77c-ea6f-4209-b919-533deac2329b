# Solution for abc302_e.md

Generated at: 2025-07-22 06:21:47
Model: deepseek-reasoner

## Reasoning Process

Alright, I've got this problem to solve. Let me try to understand it step by step.

We start with a graph of N vertices and no edges. We're going to process Q queries. After each query, we need to output the number of vertices that are isolated, meaning they have no edges connected to them.

The queries can be of two types:
1. `1 u v`: This adds an edge between u and v. It's guaranteed that when this query is given, there's no existing edge between u and v.
2. `2 v`: This removes all edges connected to vertex v. Note that v itself remains in the graph, just without any edges.

Constraints are high: N and Q up to 300,000. So, we need an efficient solution.

First, let me think about what happens when we process each query.

Initially, all vertices are isolated because there are no edges. So, after 0 queries, we have N isolated vertices. But we start processing from the first query.

Let me consider Example 1 to make sure I understand.

Input:
3 7
1 1 2
1 1 3
1 2 3
2 1
1 1 2
2 2
1 1 2

After each step, we output the number of isolated vertices.

Step 0: Start with 3 isolated vertices: {1}, {2}, {3}. But we don't output until after the first query.

After query1: "1 1 2" -> we add an edge between 1 and 2. Now, vertices: 1 and 2 are connected, 3 is isolated. Output: 1.

After query2: "1 1 3" -> add edge between 1 and 3. Now, 1,2,3 are all connected. So, no isolated vertices. Output: 0.

After query3: "1 2 3" -> but wait, 2 and 3 are already connected through 1? So adding an edge between 2 and 3 doesn't change the connectivity. But the problem says that right before the query, there is no edge between u and v. So we add the edge, but the graph was already connected. Then, after this, still no isolated vertices. Output: 0.

After query4: "2 1" -> remove all edges incident to 1. That means removing the edges (1,2) and (1,3). Now, what remains? The edge (2,3) is still there. So vertices 2 and 3 are connected, and vertex 1 is isolated. Output: 1.

After query5: "1 1 2" -> add edge between 1 and 2. Now, 1 is connected to 2, and 2 is connected to 3, so 1 is connected to 3 via 2? So all are connected again? Then output: 0.

After query6: "2 2" -> remove all edges incident to 2. That means removing the edge (1,2) and (2,3). Now, we have vertex 1 and 3 isolated. Also, vertex 2 is isolated? After removing, 2 has no edges, but also 1 and 3 have no edges. So now, we have three isolated vertices: 1, 2, 3. Output: 3.

After query7: "1 1 2" -> add edge between 1 and 2. Now, 1 and 2 are connected, 3 is isolated. Output: 1.

So the outputs are: 1,0,0,1,0,3,1.

Now, how can we efficiently compute the number of isolated vertices after each query?

The straightforward way is to maintain the graph and after each query, traverse to count isolated vertices. But that would be O(N) per query, which is O(N*Q) = 300,000 * 300,000 = 90e9, which is too slow.

So we need a smarter way.

We can maintain a count of isolated vertices and update it as we add or remove edges.

Let me denote:
- `isolated_count`: the current number of isolated vertices.

We also need to track the degree of each vertex. Because a vertex is isolated if its degree is 0.

So we can have:
- `deg[v]` = degree of vertex v (number of edges incident to v).

Initially, `deg[1..N] = 0`, so `isolated_count = N`.

When we add an edge (u, v):
- Before adding, we have two vertices u and v. Their current degrees are `deg[u]` and `deg[v]`.
- When we add the edge:
  - If `deg[u]` was 0, then u was isolated, so we lose one isolated vertex. Similarly for v.
  - After adding, `deg[u]` increases by 1, and `deg[v]` increases by 1.
  - So, the change in isolated_count:
      - If u was isolated (deg[u]==0) then it becomes non-isolated: subtract 1.
      - If v was isolated (deg[v]==0) then subtract 1.
  - Therefore, the total change is: 
        delta = 0
        if deg[u] == 0: delta -= 1
        if deg[v] == 0: delta -= 1
  - Then update deg[u] and deg[v] (increment by 1).
  - Then isolated_count += delta.

But note: if u and v are the same? No, because u != v.

Also, when we remove an edge, but actually for type 2 query, we remove all edges incident to a vertex.

For type 2 query: "2 v"
- We need to remove all edges connected to v. So we need to know all neighbors of v.

So we have to maintain the graph structure? Or at least the adjacency.

But when we remove edges from v, we are affecting:
- The degree of v: becomes 0 (so v becomes isolated).
- The degrees of all neighbors of v: each neighbor w will lose one edge, so deg[w] decreases by 1.

Also, when deg[w] decreases, if it becomes 0, then we gain an isolated vertex.

But also, if deg[w] was 1 and becomes 0, then we add one to isolated_count.

So steps for "2 v":
1. Let current degree of v be d = deg[v]. 
2. We set deg[v] = 0, so v becomes isolated. But note: before removal, if v had edges, it wasn't isolated. After removal, it becomes isolated. So we add 1 for v? But wait: we are going to remove each edge, which affects neighbors.

Actually, we have two effects:
- v becomes isolated: if v was not isolated before (which it wasn't, because it had edges) then we are adding one isolated vertex? But actually, we are setting its degree to 0.
- For each neighbor w of v:
   - Before: deg[w] is some value (say, k). We decrease deg[w] by 1.
   - Then if deg[w] becomes 0, then w becomes isolated, so we add one to isolated_count.
   - Also, if deg[w] was 1 and becomes 0, then we add one. But if deg[w] was 2 and becomes 1, then it was not isolated (because isolated means 0) so no change? Only when it becomes 0, we add one.

But also, when we remove the edge (v, w), we are disconnecting w from v. So we have to update the adjacency lists.

But note: when we remove edges from v, we have to update each neighbor's adjacency: remove v from their lists.

But the challenge is: for a type 2 query, if v has many edges, we have to iterate over all neighbors. In worst case, a vertex can have up to N-1 edges. And if we do many type 2 queries on high-degree vertices, worst-case time could be O(N*Q) which is 90e9, too slow.

So we need to avoid iterating over all edges of a vertex in a type 2 query.

How can we optimize?

We must note that each edge is removed at most once. Because when we remove an edge via a type 2 query, that edge is gone. But an edge can be added and then removed multiple times? Actually, no: because when we add an edge, it's only added once (guaranteed that when adding, the edge doesn't exist). And when we remove an edge, we remove it. But note, we can add the same edge again? The problem doesn't say that an edge once removed cannot be added again. Example: after removing an edge, we can add it again? But the guarantee is only that when adding, the edge is not present at that moment.

So, an edge can be added, then removed (via type2 on either vertex), then added again. So we have to consider that.

But if we use an adjacency list, we can remove edges. But the problem is that a type2 query on a vertex with high degree will be slow.

Alternative idea: use a data structure that supports fast removal of edges? Or use lazy deletion? Or maybe we can avoid explicitly iterating over every edge by maintaining the degrees and updating the isolated count without iterating? But we have to update the neighbors' degrees and their adjacency.

Wait, when we remove an edge (v, w), we must:
- remove w from v's adjacency list
- remove v from w's adjacency list

But for a type2 query, we are removing all edges incident to v. So we have to do for each neighbor w in adj[v]:
   - remove v from adj[w]
   - decrease deg[w] by 1
   - check if deg[w] becomes 0 -> then isolated_count++

And then set adj[v] to empty, and set deg[v]=0.

But the problem is that if a vertex v has many neighbors, this operation is O(degree(v)).

And worst-case, over Q queries, the total work could be O(Q * degree) and the sum of degrees might be large.

But note: each edge is added once and removed at most once? Actually, no: because an edge can be added and then removed by a type2 query on one of its endpoints. But the same edge cannot be added again? Actually, it can: after we remove it, we can add it again. So the same edge might be added and removed multiple times? 

But the input guarantees that when adding, the edge is not present. So if we remove an edge, we can add it again only after it's been removed. So each time we add an edge, it's a new instance? Or same edge? Actually, the graph is undirected and we are just adding and removing edges. But the same pair can be added, removed, added again.

So, we cannot assume that each edge is removed only once. Therefore, worst-case, if we have many type2 queries on the same high-degree vertex, and then re-add the edges and remove again, we might do a lot of work.

But worst-case total operations: if we have Q queries, and each type2 query removes many edges, and we have many such queries, the total cost could be O(Q * N) which is 90e9, too slow.

So we need a more efficient method.

How can we avoid iterating over all edges in a type2 query?

Alternative approach: use a union-find? But the type2 query removes all edges incident to a vertex, which might remove multiple edges. Union-find is for connectivity, but removal is hard.

Wait, the problem doesn't ask for connectivity, only for the count of isolated vertices (degree 0). So we don't care about the entire connectivity, just the degrees.

But when we remove edges, we only care about the degrees and when they become 0.

So maybe we can maintain:
- `deg[i]` for each vertex i
- `isolated_count` = count of vertices with deg[i] == 0

But for type2 query on v:
  - Let d = deg[v] (which is the current degree of v). Then we set deg[v] = 0, so we add 1 to isolated_count? But wait: if v was already isolated (d=0) then we do nothing? Actually, if v was isolated, then we don't have edges to remove. But the problem says "remove all edges that connect vertex v and the others", so if v has no edges, then nothing to remove. Then we set deg[v] to 0? It's already 0. So no change? But we are setting it to 0 again. So we don't need to do anything for v? Actually, we don't change the count for v because it was already isolated.

But wait: if v was not isolated, then we set its degree to 0 and we have to account for that: we are adding one isolated vertex (v) but we are also reducing the degree of its neighbors.

But the problem is that we have to update the neighbors.

So the challenge is updating the neighbors without iterating over them? That doesn't seem possible.

But note: each edge is removed at most once per addition. Because when we add an edge, it's in the graph until we remove it. And when we remove it (via type2 query on one of its endpoints), we remove it. But if we add the same edge again, that's a new addition. So the same edge can be added and removed multiple times, but each time we add an edge, we have to account for its removal when we do type2 on either endpoint.

So, if we consider the entire process, the total number of edge removals is at most the total number of edges that were added and then removed. Since each edge is added exactly once per query type1, and each removal of an edge is done when one of its endpoints has a type2 query. 

But the same edge might be added and removed multiple times? Actually, the problem doesn't prevent that. For example, we can have:
  1 1 2   # add edge (1,2)
  2 1      # remove all edges from 1: removes (1,2)
  1 1 2   # add again
  2 1      # remove again

So the same edge (1,2) is added and then removed twice. So each time we remove it, we have to process it.

Therefore, the total work over all type2 queries is the sum of the degrees of the vertices at the time of the type2 query. Since each edge is removed each time one of its endpoints is type2'd, and if we add it again, it's a new removal.

But worst-case, if we have a type2 query on a vertex with degree 300,000, and we have 300,000 queries, the total work could be 300,000 * 300,000 = 90e9, which is too slow in Python.

So we need to avoid iterating over the entire adjacency list for a vertex in a type2 query.

How can we do that?

Idea: use lazy deletion. Instead of immediately removing the edges from the neighbors, we can mark the edges as removed and then update the degrees lazily? But the problem is that when we process the neighbors, we need to know that the edge is gone.

Alternatively, we can maintain the degree of each vertex, and for each type2 query on v, we set deg[v] to 0 and then for each neighbor w that was connected to v, we decrease deg[w] by 1. But we have to update the neighbor's adjacency: remove v from their lists. But if we don't update, then when we do a type2 query on w, we will try to remove the edge (w, v) which is already gone? So we must update.

But if we don't remove v from w's adjacency list, then when we process a type2 on w, we will try to remove the edge (w, v) even if it was already removed by a previous type2 on v. That would be incorrect.

So we must maintain consistency.

Alternative idea: use an array `alive` for edges? But we don't have edge ids.

We could store for each vertex, a set of edges. But removing all edges for a vertex would be O(degree) anyway.

Wait, but the total number of edge removals over all queries might be high. The problem is that worst-case, we can have a vertex that is constantly being connected to many edges and then type2'd. 

But note: the total number of edge additions is at most Q (because each type1 adds one edge). And each edge is removed at most twice: once for each endpoint? Actually, no: an edge can be removed multiple times? Actually, no: because when we remove an edge, we remove it from the graph. But if we add it again, it's a new edge? So the total number of edge removals is the same as the total number of edges that have been added and then removed. 

But each edge, when added, can be removed either by a type2 on u or on v. And then if added again, it's a new instance.

So the total number of edge removals is at most the total number of type1 queries? Because each type1 adds one edge, and each edge is removed at most once? Or can an edge be removed multiple times? 

Wait, no: once an edge is removed (via either type2 on u or type2 on v), it's gone. Then if we add it again, that's a new edge. So the same pair (u,v) can be added and removed multiple times, but each removal is for a distinct edge (the one that was added). 

Therefore, the total number of edge removals over all queries is exactly the total number of edges that were ever added and then removed. But note: an edge might be removed by a type2 on u, then added again, then removed again. So the total number of removal operations per edge is one per addition. 

Therefore, the total number of edge removals (each edge removal being a single operation) is at most Q. Because we have at most Q type1 queries, and each type1 query adds one edge, and each edge is removed at most once (if it is removed multiple times, that's for multiple additions). Actually, each addition leads to one removal? Not necessarily: an edge might remain until the end. 

But in any case, the total number of edge removals (counting each removal of a specific edge) is at most the total number of edges added. So the total work over all type2 queries is O(total_edges_added) = O(Q). 

Wait, is that correct?

Let me see: 
- Each type1 query adds an edge: so total edges added is at most Q.
- Each edge, when removed, is removed exactly once (because after removal, that particular edge is gone). 
- In a type2 query on vertex v, we remove all edges incident to v that are currently present. The number of edges removed is the current degree of v. 

But the total work for all type2 queries is the sum over all type2 queries of the degree of the vertex at the time of the query. 

But note: each edge is removed exactly once (per addition), and when it is removed, it is because of a type2 query on one of its endpoints. Therefore, the total work (over all type2 queries) of the removal operations is the total number of edges that are removed. 

But each edge is removed once, so the total work is O(total_edges_removed) = O(Q). 

But wait, in a type2 query, we also have to update the isolated_count. For each edge (v, w) we remove:
   - We update w: deg[w] decreases by 1, and if deg[w] becomes 0, we increment isolated_count by 1.
   - And we remove w from v's list and v from w's list.

So each edge removal operation is O(1). Therefore, the total work for all type2 queries is O(total_edges_removed) = O(Q).

Similarly, for type1 queries, each one adds an edge, which is O(1).

So overall, the entire process is O(Q).

But what about the isolated_count? We have to update it for each edge addition and removal.

Let me formalize:

Data structures:
   - deg: array of degrees for each vertex (size N+1)
   - adj: array of adjacency lists for each vertex (could be sets or lists? but we need to iterate for removal, so set might be better for removal? but we are going to remove all edges from a vertex in type2, so we can use a set or a list. But when we remove an edge from w's perspective, we need to remove v from w's set. So we need to be able to remove a specific neighbor from a set. So sets would be good.

Alternatively, we can use a list and then remove by value? But removal in a list is O(n). So sets are better.

But sets use more memory? But we have at most Q edges.

So:
   - deg = [0]*(N+1)
   - adj = [set() for _ in range(N+1)]
   - isolated_count = N   (initially)

For each query:

Type1: "1 u v"
   - Check current deg[u] and deg[v]:
        if deg[u] == 0: then u is isolated -> we will lose u: so isolated_count -= 1
        if deg[v] == 0: then isolated_count -= 1
   - Then add the edge: 
        adj[u].add(v)
        adj[v].add(u)
        deg[u] += 1
        deg[v] += 1
   - Then after adding, if deg[u] becomes 1? Then we already subtracted when it was 0, so no further change. Similarly for v.

Type2: "2 v"
   - We iterate over all neighbors w in adj[v]:
        For each w:
            Remove v from adj[w]: adj[w].remove(v)
            deg[w] -= 1
            If after decrement, deg[w] becomes 0, then we have a new isolated vertex: isolated_count += 1
        And we remove the edge (v, w) from the graph.
   - Then, we note: we are removing all edges from v. So:
        Let d = deg[v]  (which is the current degree, and also len(adj[v]))
        We set deg[v] = 0
        adj[v] = set()   (or clear it)

        But also, before removal, if v was not isolated (d>0), then we are making it isolated: so we add one to isolated_count? 
        However, during the removal, we didn't account for v becoming isolated. 

        Actually, we set deg[v] to 0. But we didn't adjust for v: 
          - Before: deg[v] = d (>=1) -> v was not isolated.
          - After: deg[v] = 0 -> v is isolated.

        So we have to add one for v.

        But note: if v was already isolated (d=0), then we do nothing? Actually, the query says "remove all edges incident to v", so if there are none, then we do nothing. Then v was already isolated, so we don't add again.

        Therefore, we need to:
          isolated_count += 1   for v? But wait, if v was not isolated, then we are turning it to isolated. If it was isolated, then after removal, it is still isolated -> no change.

        Actually, we can do: 
          if deg[v] > 0: 
              isolated_count += 1   # because v becomes isolated

        But then, during the removal of edges, we are updating the neighbors: each neighbor w loses an edge, so if that causes w to become isolated, we add one. And we remove the edges.

        However, note: when we remove an edge, we are effectively removing two half-edges? But we are processing both endpoints.

        But also, when we remove the edges, we are reducing the degree of w. And if w becomes isolated, we add one. So we have to account for that.

   - Steps for type2 on v:
        if deg[v] > 0:
            isolated_count += 1   # because v becomes isolated (if it wasn't already) -> but wait, if we set deg[v] to 0, then we are adding one isolated vertex (v) that wasn't isolated before.

        Then, for each w in adj[v]:
            # Remove the edge from w's side
            adj[w].remove(v)
            deg[w] -= 1
            if deg[w] == 0:
                isolated_count += 1   # w becomes isolated

        Then, clear adj[v] and set deg[v] = 0.

        But wait: the above if deg[v] > 0: we added one for v. But actually, if v had edges, then we are removing all its edges, so v becomes isolated. So that is correct.

        However, consider: before the removal, v was not isolated (deg[v] = d > 0). Then we set v to isolated: so we add one. Then, for each neighbor w, we reduce their degree. And if w becomes isolated, we add one.

        But actually, we are adding one for v and then possibly adding more for each w that becomes isolated. That seems correct.

        But let me test with example: 
          After the 4th query in Example1: "2 1"
          Before: all three vertices are connected. deg[1]=2, deg[2]=2, deg[3]=2. isolated_count=0.
          We do "2 1": 
             v=1: deg[1]=2>0 -> isolated_count +=1 -> becomes 1.
             Then, for w in adj[1]: which are [2,3]
                 For w=2: 
                     remove 1 from adj[2]: now adj[2] becomes {3} -> deg[2] becomes 1 (from 2) -> not 0, so no change.
                 For w=3:
                     remove 1 from adj[3]: now adj[3] becomes {2} -> deg[3] becomes 1 -> no change.
             Then set adj[1]=empty and deg[1]=0.

          So now isolated_count=1, which is correct: only vertex1 is isolated? But wait, what about vertices 2 and 3? They are connected by the edge (2,3). So they are not isolated. So output 1. Correct.

        Now, after the 6th query: "2 2"
          Before: we have edge (1,2) and (2,3) [from the re-additions]. 
          Actually, let's see the state before the 6th query:

          After the 5th query: "1 1 2" -> we add edge (1,2). 
          Then the graph: 
             1-2 and 2-3? Actually, after the 4th query, we had edge (2,3) still? 
             After the 4th query: 
                 We removed edges incident to 1: so we removed (1,2) and (1,3). Then the graph has only (2,3). 
             Then we did "1 1 2": so we added (1,2). Then the graph: 
                 vertices: 1-2 and 2-3 -> so 1 is connected to 2, and 2 to 3. So 1,2,3 are connected? 
          Then we do "2 2": 
             v=2: deg[2]=2>0 -> isolated_count +=1 -> becomes 0+1 = 1? But then we process neighbors of 2: which are [1,3]
                 For w=1: 
                     remove 2 from adj[1]: adj[1] becomes empty -> deg[1] was 1, becomes 0 -> so isolated_count +=1 -> now 2.
                 For w=3:
                     remove 2 from adj[3]: adj[3] becomes empty -> deg[3] was 1, becomes 0 -> isolated_count +=1 -> now 3.
             Then set adj[2] to empty and deg[2]=0.
          Then isolated_count=3. Correct.

        But note: we started with isolated_count=0 (before the query). Then we added 1 for v=2, then 1 for w=1, then 1 for w=3: total 3.

        However, we also set deg[2]=0, but we already added for v=2? Yes.

        But wait: when we remove the edges from v=2, we are updating the neighbors and then setting v=2 to 0. And we added one for v=2 at the beginning.

        But what if v was already isolated? 
          Example: "2 1" when 1 is already isolated: 
             deg[1]=0 -> we skip the if condition? Then we do nothing. Correct.

        But note: if a vertex has no edges, then adj[v] is empty, so we don't loop. 

        So the steps for type2:
          if deg[v] > 0:
              isolated_count += 1   # v becomes isolated (from non-isolated)
              # Then we remove each edge
              for w in list(adj[v]):   # we use list(adj[v]) to avoid changing the set while iterating? Or we can use a copy.
                  # Remove the edge (v, w) from w's adj
                  adj[w].remove(v)
                  deg[w] -= 1
                  # If w becomes isolated, then add to isolated_count
                  if deg[w] == 0:
                      isolated_count += 1
                  # Also, we can remove w from adj[v]? Actually, we are iterating and then we will clear adj[v] anyway.
              # Clear adj[v] and set deg[v] to 0? But we are going to set deg[v]=0 and clear the set.
              adj[v].clear()
              deg[v] = 0

        But wait: when we remove w from adj[v], we are modifying adj[w] and then we clear adj[v]. That is correct.

        However, note: we are doing `for w in list(adj[v])` so that we iterate over a copy. Because if we do `for w in adj[v]` and we remove w from adj[v] during the loop, it might cause issues? Actually, no: because we are not removing from adj[v] during the loop, we are removing from adj[w]. But we are iterating over adj[v] which we are going to clear anyway. But the set adj[v] is not being modified during the iteration? We are not adding or removing from adj[v] in the loop. We are only removing from adj[w]. So we can iterate over the set.

        But to be safe, we can iterate over a copy: `for w in list(adj[v])` or `set.copy`.

        But worst-case, the degree might be high, but total over all queries is O(Q) as argued.

        But wait: what if we do a type2 on a vertex that has many edges? The iteration for that one query is O(deg(v)), but the sum of all deg(v) over all type2 queries is the total number of edges removed, which is O(Q). So overall, it's O(Q).

Therefore, we can implement:

Initialize:
   deg = [0]*(N+1)
   adj = [set() for _ in range(N+1)]
   isolated_count = N
   results = []

For each query in queries:
   if query[0] == 1:
        u, v = query[1], query[2]
        # Before adding the edge, check if u and v are isolated
        if deg[u] == 0:
            isolated_count -= 1
        if deg[v] == 0:
            isolated_count -= 1

        # Add edge
        adj[u].add(v)
        adj[v].add(u)
        deg[u] += 1
        deg[v] += 1

        results.append(isolated_count)

   else: # query[0] == 2
        v = query[1]
        if deg[v] > 0:
            # v becomes isolated: so we add one
            isolated_count += 1

            # Now, remove all edges from v
            # Make a copy of the neighbors list because we are going to modify adj[v] and also adj[w]
            for w in list(adj[v]):
                # Remove v from w's adjacency
                adj[w].discard(v)   # or remove, but it must be there
                deg[w] -= 1
                # If w becomes isolated, then we have one more isolated vertex
                if deg[w] == 0:
                    isolated_count += 1
            # Clear v's adjacency and set degree to 0
            adj[v] = set()
            deg[v] = 0

        results.append(isolated_count)

But wait, what if we do a type2 on a vertex that is already isolated? Then we skip the if deg[v]>0, so we just append the current isolated_count? That is correct.

But let me test with the example: 
   Input: 
        2 1
        2 1

   Initially: N=2, isolated_count=2.
   Query: "2 1": 
        v=1, deg[1]=0 -> skip the if -> append isolated_count=2. Correct.

Another test: 
   N=2, queries:
        1 1 2   -> then 
           u=1, v=2: both deg[1] and deg[2] are 0 -> isolated_count: 2 -> then subtract for u: 1, then for v: 0. So isolated_count becomes 0.
        Then type2 on 1: 
           v=1: deg[1]=1>0 -> isolated_count +=1 -> becomes 1.
           Then for w in adj[1]: which is [2] -> 
                adj[2].remove(1) -> now adj[2] becomes empty -> deg[2] becomes 0 -> so we do: isolated_count +=1 -> becomes 2.
           Then set adj[1]=set(), deg[1]=0.
           Then append 2.

        Then output: [0, 2]

        But after the first query: we have edge (1,2) -> no isolated vertices: output 0 -> correct.
        After the second query: remove all edges from 1: which removes (1,2). Then both vertices are isolated: output 2 -> correct.

But wait, after removing the edge (1,2) from v=1, we update w=2: 
   deg[2] was 1 -> becomes 0 -> so we add one to isolated_count. That is correct.

But note: when we remove the edge (1,2) from the graph, we are effectively making both vertices 1 and 2 isolated? But we already accounted for 1: by adding one at the beginning (because v=1 becomes isolated) and then for w=2, we added one when it became isolated. So total 2.

But actually, before the type2 query, we had:
   deg[1]=1, deg[2]=1 -> isolated_count=0.
Then for the type2 on 1:
   isolated_count +=1 -> becomes 1 (for vertex1 becoming isolated) 
   then we remove the edge from 2: 
        deg[2] becomes 0 -> isolated_count +=1 -> becomes 2.

So that's correct.

But what if a vertex has multiple edges? 
   Example: 
        N=3, 
        queries:
          1 1 2
          1 1 3
          2 1

        After the first two queries: 
           1 connected to 2 and 3. 
           deg[1]=2, deg[2]=1, deg[3]=1 -> isolated_count=0.
        Then "2 1": 
            v=1: deg[1]=2>0 -> isolated_count +=1 -> becomes 1.
            Then for w in adj[1]: [2,3]
                For w=2: 
                    adj[2].remove(1) -> deg[2] becomes 0 -> isolated_count +=1 -> becomes 2.
                For w=3:
                    adj[3].remove(1) -> deg[3] becomes 0 -> isolated_count +=1 -> becomes 3.
            Then set adj[1] to empty and deg[1]=0.

        Then output: 3.

        But after removing all edges from 1, we have:
            vertex1: isolated, vertex2: isolated, vertex3: isolated -> 3 isolated. Correct.

But wait, is there any edge between 2 and 3? No, because we only added edges (1,2) and (1,3). So when we remove both, then all three are isolated.

So the algorithm works.

But note: we are using sets for adjacency. The removal of an element from a set is O(1). The initial copy of the list for neighbors: that is O(deg(v)) per type2 query, but the total over all queries is O(total_edges_removed)=O(Q). So overall O(Q).

Also, we are storing the graph, which is O(N+Q) in space.

Now, let's run through the Example1 step by step to verify:

Step0: 
   N=3, deg=[0,0,0,0] (index0 unused), adj=[set(), set(), set(), set()], isolated_count=3.

Query1: "1 1 2"
   u=1, v=2: 
        deg[1]==0 -> isolated_count=3-1=2
        deg[2]==0 -> isolated_count=2-1=1
        Then add edge: adj[1]={2}, adj[2]={1}, deg[1]=1, deg[2]=1.
        Append 1.

Query2: "1 1 3"
   u=1, v=3:
        deg[1]=1 -> not 0 -> so no subtraction for u.
        deg[3]=0 -> subtract: isolated_count=1-1=0
        Then add edge: adj[1]={2,3}, adj[3]={1}, adj[2] remains {1}; deg[1]=2, deg[2]=1, deg[3]=1.
        Append 0.

Query3: "1 2 3"
   u=2, v=3:
        deg[2]=1 (not 0) -> no subtraction
        deg[3]=1 (not 0) -> no subtraction
        Add edge: adj[2].add(3) -> {1,3}; adj[3].add(2) -> {1,2}; deg[2]=2, deg[3]=2, deg[1]=2.
        Append 0.

Query4: "2 1"
   v=1: deg[1]=2>0 -> isolated_count +=1 -> becomes 1.
        Then for w in adj[1]: [2,3] (copy)
           w=2: 
               adj[2].remove(1) -> adj[2] becomes {3}; deg[2] becomes 1 -> not 0 -> no change to isolated_count.
           w=3:
               adj[3].remove(1) -> adj[3] becomes {2}; deg[3] becomes 1 -> no change.
        Then clear adj[1] and set deg[1]=0.
        Append 1.

Query5: "1 1 2"
   u=1, v=2:
        deg[1]=0 -> subtract 1: isolated_count=1-1=0
        deg[2]=1 -> not 0 -> no subtraction.
        Add edge: adj[1]={2}, adj[2]={3,1} (so now 2 has two edges: to 3 and 1), deg[1]=1, deg[2]=2, deg[3]=1.
        Append 0.

Query6: "2 2"
   v=2: deg[2]=2>0 -> isolated_count +=1 -> becomes 1.
        Then for w in adj[2]: [3,1] (copy)
           w=3: 
               adj[3].remove(2) -> adj[3] becomes empty; deg[3] becomes 0 -> isolated_count +=1 -> becomes 2.
           w=1:
               adj[1].remove(2) -> adj[1] becomes empty; deg[1] becomes 0 -> isolated_count +=1 -> becomes 3.
        Then clear adj[2] and set deg[2]=0.
        Append 3.

Query7: "1 1 2"
   u=1, v=2:
        deg[1]=0 -> subtract 1: 3-1=2
        deg[2]=0 -> subtract 1: 2-1=1
        Then add edge: adj[1]={2}, adj[2]={1}, deg[1]=1, deg[2]=1, deg[3]=0 -> so vertex3 is still isolated? 
        Then isolated_count=1? 
        But wait: vertex3 is isolated? deg[3]=0 -> so we have one isolated vertex? But the algorithm: 
            We subtracted for u=1 (because deg[1] was 0) -> then isolated_count becomes 3-1=2.
            Then we subtracted for v=2 (because deg[2] was 0) -> becomes 1.
            Then we added the edge: now deg[1]=1, deg[2]=1, deg[3]=0 -> so vertex3 is isolated. 
            Therefore, isolated_count should be 1? 
        Then we append 1.

Output: [1,0,0,1,0,3,1] -> matches.

But in query7: 
   Before: 
        deg[1]=0, deg[2]=0, deg[3]=0 -> isolated_count=3.
   Then we subtract for u=1: becomes 2, then for v=2: becomes 1.
   Then we add the edge: now deg[1]=1, deg[2]=1, deg[3]=0 -> so vertex3 is isolated. 
   Therefore, after the addition, isolated_count=1. Correct.

So the algorithm is correct.

But note: when we remove an edge from a neighbor w, we use `adj[w].discard(v)` or `remove`? 
   Since we are iterating over adj[v], and we know that w has an edge to v (because the graph is undirected and we maintain both adjacencies), so we can use `remove` and it must be there. But if there is a concurrent modification? We are not modifying adj[w] from anywhere else. So we can use `remove`.

But to be safe, we can use `discard` which doesn't throw an error if the element is not present. But it should be present. But if there's a bug? 

But the problem constraints guarantee that when we remove, the edges exist. So we can use `remove` for a bit of speed? Or `discard` to be safe? 

I think `discard` is safer and O(1) same as `remove`. So we can use `discard`.

But in our algorithm, we are iterating over adj[v] which is the neighbors of v, and for each w, we are removing v from adj[w]. Since the graph is undirected, if (v, w) is an edge, then w must be in adj[v] and v must be in adj[w]. So `remove` would be safe. But if we have a bug, then `discard` avoids crashing.

But we don't want to crash. So we can use `discard`.

But the problem says that when we add an edge, we add both. And when we remove, we remove both. So it should be consistent.

But we are also doing `adj[v].clear()` at the end. That is fine.

Alternatively, we can do:

   for w in list(adj[v]):
        # Remove the edge from w's side
        if v in adj[w]:
            adj[w].remove(v)
            deg[w] -= 1
            if deg[w] == 0:
                isolated_count += 1

But we know that v must be in adj[w]? So we don't need to check.

But to be safe from bugs, we can use `discard` and avoid the check.

So:

   adj[w].discard(v)
   deg[w] -= 1
   if deg[w] == 0:
        isolated_count += 1

But then, if the edge was not present, we do not want to decrement deg[w]. But it should be present.

But to be safe and efficient, we can check if we actually removed it? But `discard` doesn't tell us. So we can do:

   if v in adj[w]:
        adj[w].remove(v)
        deg[w] -= 1
        if deg[w] == 0:
            isolated_count += 1

But that would be two lookups? Actually, `remove` also does a lookup. So we can do:

   if v in adj[w]:
        adj[w].remove(v)
        ...

But we know it should be there. So we can skip the check.

I think we can safely use `adj[w].remove(v)`.

But in Python, set.remove(v) raises KeyError if v is not in the set. So if there's a bug, we crash. 

Alternatively, we can do:

   if v in adj[w]:
        adj[w].remove(v)
        ...

But that's an extra lookup. Since we are sure, we can avoid.

But for safety, maybe we can use:

   if v in adj[w]:
        adj[w].remove(v)
        deg[w] -= 1
        if deg[w] == 0:
            isolated_count += 1

But that's O(1) per edge removal, and total is O(Q), so acceptable.

But the total work is still O(Q). 

Alternatively, we can rely on the data structure consistency and use `discard` and then decrement without checking? Because if we discard and v wasn't there, then we shouldn't decrement. But that would be a bug. 

But the problem states that the graph is maintained consistently. 

But to avoid a crash, we can use:

   if v in adj[w]:
        adj[w].remove(v)
        deg[w] -= 1
        if deg[w] == 0:
            isolated_count += 1

But that's an extra check. Alternatively, we can just use `discard` and then if we discard, then we decrement? But discard doesn't return a value.

We can do:

   if adj[w].discard(v) is not None: 
        -> but discard doesn't return anything.

So we can do:

   if v in adj[w]:
        adj[w].discard(v)   # which is redundant? 
        deg[w] -= 1
        ...

But that's the same as the above.

I think we can assume that the graph is maintained correctly and just do:

   if v in adj[w]:
        adj[w].remove(v)
        deg[w] -= 1
        if deg[w] == 0:
            isolated_count += 1

But actually, we don't need to check: because we are iterating over adj[v], so w is a neighbor of v, so the edge (v,w) exists. Therefore, v must be in adj[w]. 

So we can just remove without checking.

So we'll do:

   adj[w].remove(v)
   deg[w] -= 1
   if deg[w] == 0:
        isolated_count += 1

But if we are concerned about performance, the total is O(Q) so it's acceptable.

Now, what about clearing the set? We do:

   adj[v].clear()

But we can also do:

   deg[v] = 0

   But note: we are going to reuse the set? Actually, we can clear the set: adj[v].clear()

But we can also set it to a new set? But that might be more efficient? 

But in the loop, we are iterating over a copy of the old set. Then we clear the set. That is fine.

Alternatively, we can do:

   old_adj = adj[v]
   adj[v] = set()   # new empty set
   for w in old_adj:
        ... 

But then we don't need to clear. And the old set will be garbage collected.

But in the next type2 on v, if any, we set adj[v] to a new set. But we are not accumulating sets. 

But the problem: if we do multiple type2 on v, we are replacing the set. But the old set is then discarded. That is acceptable.

But also, we can avoid creating a new set each time? We can clear the existing set. 

But in the loop, we are iterating over a copy of the set (using list(adj[v])). Then we can clear the set.

So:

   neighbors = list(adj[v])
   for w in neighbors:
        adj[w].remove(v)
        deg[w] -= 1
        if deg[w] == 0:
            isolated_count += 1
   adj[v].clear()
   deg[v] = 0

But if we do `adj[v] = set()`, then we are replacing the set. But that is also acceptable.

But creating a new set might be more expensive? 

Alternatively, we can do:

   adj[v] = set()

But if we do that, then the old set is left for garbage collection. But we are only storing one set per vertex. 

But if we do many type2 queries, we might create many sets. But the total number of type2 queries is at most Q, so the total sets created is O(N) (each vertex might be set to a new set multiple times). But worst-case, if we do Q type2 queries, and Q=300,000, then we create 300,000 sets? But we have N=300,000 vertices, so each vertex might be type2'd many times. 

But we can reuse the set by clearing it. 

So:

   if deg[v] > 0:
        isolated_count += 1
        # Make a copy of the current neighbors
        neighbors = list(adj[v])
        # Clear the set now? But then we can reuse the set.
        adj[v].clear()
        deg[v] = 0   # set now, but then we update neighbors

        for w in neighbors:
            # But note: we cleared adj[v], so if in the loop we have a reference to the old set? But we took neighbors as a copy.
            adj[w].discard(v)   # but we already removed v from adj[w]? Actually, we haven't. 
            deg[w] -= 1
            if deg[w] == 0:
                isolated_count += 1

        # Actually, we can set deg[v] after the loop? But we set it to 0 already? 
        # But the deg[v] is set to 0 at the beginning of the type2 block? But we are in the if, so we set deg[v]=0 and clear the set, then process the neighbors. 

        But what if while processing a neighbor w, that neighbor does a type2 query? Not in the same thread, but in the sequential processing. So no.

        But the problem: if we clear the set first, then during the loop, if we access adj[v] again? We don't. 

        However, when we process w, we remove v from adj[w]. But that's independent.

        But if we clear the set first, then the set is empty, but the neighbors we are iterating are from the copy. So it's safe.

        But note: we set deg[v]=0 at the beginning. Then, if during the loop we have a query that accesses deg[v]? But we are processing sequentially, so no.

        However, if we set deg[v]=0 at the beginning, then if in the loop we have a reference to adj[v] (which is now empty) and we don't use it. 

        But the problem: if we set deg[v]=0 and then we process w, and then we remove v from adj[w], then we update deg[w]. That is correct.

        But what if in the loop, we have a neighbor w that then does something that triggers a type2 on v? No, because we are in the middle of processing type2 on v. 

        So it's safe.

        But the algorithm: 
            deg[v] = 0   # now, v is isolated
            adj[v].clear()
            then for w in old_neighbors: 
                remove v from adj[w] -> which might be already done? No, because we just cleared our own set, but w's set still has v.

        So we must remove v from w's set.

        Therefore, we can do:

            if deg[v] > 0:
                isolated_count += 1
                old_adj = list(adj[v])
                adj[v].clear()
                deg[v] = 0   # set v to isolated

                for w in old_adj:
                    if w in adj:   # always true, w is between 1 and N
                        # But if we have already removed w? No, w is a vertex.
                        adj[w].discard(v)
                        deg[w] -= 1
                        if deg[w] == 0:
                            isolated_count += 1

        This is the same as before.

        But note: we set deg[v]=0 at the beginning. Then, if during the loop, we have a type1 query that adds an edge to v? But we are in the same query. So no.

        Actually, we are processing one query at a time.

        So it's safe.

But the order: 
   We set deg[v]=0 and clear adj[v] first, then we remove v from the neighbors' sets. 
   But if during the removal, a neighbor w is processed and it sees that v has degree 0? Then what? But we are not doing anything else. 

   For example, if we have two type2 queries on two adjacent vertices? But we process sequentially. 

   After we set deg[v]=0, then if we process w and then w does a type2 query? But we are not doing that in the loop. 

   Actually, in the loop, we are only updating the graph. 

   So it's safe.

But the initial version that we had (which iterates first and then clears) is also acceptable.

So we can choose either.

I'll stick to:

   if deg[v] > 0:
        isolated_count += 1
        neighbors = list(adj[v])   # make a copy
        for w in neighbors:
            adj[w].remove(v)
            deg[w] -= 1
            if deg[w] == 0:
                isolated_count += 1
        adj[v].clear()
        deg[v] = 0   # actually, we set to 0, but we already cleared.

But we did set deg[v] to 0 at the end? But during the loop, deg[v] is still the old value? But we don't use it in the loop. We only use adj[v] for the copy at the beginning.

So that is acceptable.

Now, we can code accordingly.

Edge Cases:
- When N=0? But N>=2.
- When Q=0? Then we output nothing.

Also, the vertex indices: from 1 to N.

Let me test with the second example:

Input: 
   "2 1"
   Then: 
        N=2, Q=1, queries: [[2,1]]

   Initialize: deg=[0,0,0] (index0 unused), adj[1]=set(), adj[2]=set(), isolated_count=2.

   Query: type2 on vertex1: deg[1]=0 -> skip -> append 2.

So output: [2]

Now, what if a vertex is type2'd multiple times? 
   Example: 
        N=2
        queries:
          1 1 2
          2 1
          2 1

        After first query: 
            deg[1]=1, deg[2]=1, isolated_count=0.
        Then first type2 on 1: 
            deg[1]=1>0 -> isolated_count +=1 -> becomes 1.
            neighbors = [2] -> 
                adj[2].remove(1) -> deg[2] becomes 0 -> isolated_count +=1 -> becomes 2.
            Then clear adj[1] and set deg[1]=0.
            Append 2.
        Then second type2 on 1: 
            deg[1]=0 -> skip -> append 2.

        So output: [0, 2, 2]? 
        But after the first type2: we have two isolated vertices: 1 and 2. Then second type2: still two isolated vertices. So output 2 is correct.

So the algorithm handles repeated type2 on the same vertex.

Now, we can implement.

But note: we are storing the graph in adj as a list of sets. The memory: O(N + total_edges) but total_edges added is O(Q). But worst-case, we have Q type1 queries, so total_edges stored at any time is at most Q. But when we remove, we remove from the sets, so the total memory is O(N + current_edges). But we are storing the sets for each vertex, and the size of each set is the current degree. The total memory is O(N+Q) because we only store each edge twice (in both adjacencies). And we remove edges when we do type2, so the memory doesn't grow without bound.

Therefore, we can proceed.

Let me write the code accordingly.

We'll create a class Solution as per the starter.

Note: the input is given as:
   N, Q
   then Q lines.

But the function is given: 
   def solve(self, N: int, Q: int, queries: List[List[int]]) -> List[int]:

So we have to return a list of integers of length Q.

We'll implement as described.

Code:

   deg = [0] * (N+1)
   # adj: list of sets, index 0 to N (index0 unused)
   adj = [set() for _ in range(N+1)]
   isolated_count = N
   results = []

   for query in queries:
        if query[0] == 1:
            u = query[1]
            v = query[2]
            if deg[u] == 0:
                isolated_count -= 1
            if deg[v] == 0:
                isolated_count -= 1
            adj[u].add(v)
            adj[v].add(u)
            deg[u] += 1
            deg[v] += 1
            results.append(isolated_count)
        else:  # query[0] == 2
            v = query[1]
            if deg[v] > 0:
                isolated_count += 1   # v becomes isolated
                # Make a copy of the list of neighbors
                neighbors = list(adj[v])
                for w in neighbors:
                    # Remove v from w's adjacency list
                    # But note: it's possible that w has already removed the edge? No, because we haven't processed this edge yet.
                    adj[w].discard(v)   # or remove, but discard is safe
                    deg[w] -= 1
                    if deg[w] == 0:
                        isolated_count += 1
                # Clear v's adjacency and set degree to 0
                adj[v] = set()   # or clear, but we'll assign a new set to avoid any reference?
                # But if we assign a new set, we lose the old set. But we have processed it.
                # Alternatively, we can clear the existing set: 
                #   adj[v].clear()   # but we have assigned a new set? 
                # Actually, we can do: 
                #   adj[v] = set()   # this is what we did above? 
                # But in the loop, we used the old set to get neighbors. Then we assign adj[v] to a new set. 
                deg[v] = 0
                results.append(isolated_count)
            else:
                results.append(isolated_count)

   return results

But wait, in the type2 branch, if we do `adj[v] = set()`, then we are replacing the set. But in the loop we used the old set (by making a copy). So that's safe.

But what if we do `adj[v].clear()`? That would clear the old set that we used to make the copy. Then we can reuse the same set. But that might be more efficient? 

But creating a new set might be more expensive. So we can do:

   if deg[v] > 0:
        isolated_count += 1
        neighbors = list(adj[v])
        # Clear the set for v: we can reuse it
        adj[v].clear()
        deg[v] = 0
        for w in neighbors:
            adj[w].discard(v)
            deg[w] -= 1
            if deg[w] == 0:
                isolated_count += 1
        results.append(isolated_count)

But note: we are using the same set and clearing it. Then we don't need to allocate a new set.

But the memory usage: we are keeping the set for each vertex. Clearing the set doesn't free the memory, but it's O(1) to clear? Actually, clearing a set is O(size) in Python? I think it is. But we are going to clear a set of size d, which we are already iterating over. So we are doing O(d) for clearing? But we are already doing O(d) for the loop. 

Alternatively, we can leave the set as is and clear it? Or we can just leave the set and then it will be garbage collected? 

But if we do `adj[v].clear()`, that is O(size) operation? Actually, in Python, set.clear() is O(n) where n is the size? But we are already iterating over the set (to make the copy and then the loop). So the total is O(d) for the copy and O(d) for the clear? Then total O(d). 

But we can avoid the clear? Because after we set deg[v]=0 and remove all references to the edges, the set is empty? Actually, we are going to reuse the same set for future edges? But if we add edges again to v, we can use the same set.

But in the next type1 query that adds an edge to v, we will use adj[v] and add to it. So we must have an empty set, not a set with old data. 

But we are clearing the set by iterating and removing from the neighbors? Then the set for v is still full? 

No: in the loop, we did not remove from adj[v]. We only removed from adj[w]. Then adj[v] is still the full set. 

Therefore, we must clear adj[v] explicitly. 

So we can do:

   if deg[v] > 0:
        isolated_count += 1
        # We need to clear adj[v] and also remove the edges from the neighbors.
        # But we need the neighbors to iterate, so we make a copy.
        neighbors = list(adj[v])
        # Now, we can clear adj[v] immediately? 
        adj[v].clear()
        deg[v] = 0
        for w in neighbors:
            # Now, we remove v from w's set
            if w < len(adj) and v in adj[w]:   # but w is between 1 and N, and adj is size N+1, so we don't need to check w<len(adj)
                adj[w].discard(v)
                deg[w] -= 1
                if deg[w] == 0:
                    isolated_count += 1
        results.append(isolated_count)

But we set deg[v]=0 and cleared adj[v] before the loop. Then if during the loop, we have a query? No, we are in the same function.

But the graph is consistent: 
   After we clear adj[v] and set deg[v]=0, then if we look at the graph, v is isolated. But the neighbors still have v in their sets? Then when we remove v from the neighbors, we update. 

But the graph is in an intermediate state: during the loop, the graph is not consistent? But we are processing the query, and no other queries are interleaved. 

So it's acceptable.

But to be consistent, we should remove the edges from the neighbors first, then clear v's set. 

But the order doesn't matter because we are not doing anything else. 

However, if we clear adj[v] first, then when we remove v from adj[w], we are using the set adj[w] which still has v. Then we remove it. Then the graph becomes consistent.

But if we clear adj[v] first, then the set for v is empty, which is consistent with deg[v]=0. Then we remove the other references. 

So it's acceptable.

But I think the original order (copy, then remove from neighbors, then clear adj[v]) is more natural.

So I'll do:

   if deg[v] > 0:
        isolated_count += 1
        neighbors = list(adj[v])
        for w in neighbors:
            adj[w].discard(v)
            deg[w] -= 1
            if deg[w] == 0:
                isolated_count += 1
        adj[v].clear()
        deg[v] = 0
        results.append(isolated_count)

But note: we are using `discard` to avoid KeyError. But we are iterating over adj[v], so w is a neighbor, so the edge should exist. But what if the same edge was removed by an earlier operation? No, because we are processing sequentially. 

But if the graph is undirected, when we remove v from w's set, we are removing one edge. Then the graph becomes consistent.

But if we remove v from w, then the set adj[w] is updated. Then when we later clear adj[v], we have already processed all the neighbors.

So it's safe.

Now, we can test with the examples.

I think we are ready to code.

But note: when we do `adj[v].clear()`, we are modifying the set in place. Then we set `deg[v]=0`. 

But what if we don't clear? Then the set remains? But we have already iterated over the neighbors. Then in the next query, if we add an edge to v, we start from an empty set? No, we cleared it. 

Actually, we are clearing it. So it's empty.

But if we do `adj[v] = set()` then we are replacing the set. But that might be more expensive. 

We'll do `adj[v].clear()` for efficiency.

So final code:

   for query in queries:
        if query[0] == 1:
            u, v = query[1], query[2]
            if deg[u] == 0:
                isolated_count -= 1
            if deg[v] == 0:
                isolated_count -= 1
            adj[u].add(v)
            adj[v].add(u)
            deg[u] += 1
            deg[v] += 1
            results.append(isolated_count)
        else:
            v = query[1]
            if deg[v] > 0:
                isolated_count += 1
                # Make a copy of the neighbors because we are going to modify the graph
                neighbors = list(adj[v])
                for w in neighbors:
                    # Remove v from w's adjacency list
                    adj[w].discard(v)   # discard is safe and avoids KeyError if for some reason not present
                    deg[w] -= 1
                    if deg[w] == 0:
                        isolated_count += 1
                # Clear v's adjacency list and set degree to 0
                adj[v].clear()
                deg[v] = 0
                results.append(isolated_count)
            else:
                results.append(isolated_count)

   return results

But note: in the type1 branch, we are adding the edge and then updating the degree. Then we append the isolated_count.

This should be correct.

Let me test with a small example: adding an edge and then removing it by type2 on one vertex.

   N=2
   Query1: 1 1 2 -> 
        u=1, v=2: 
            deg[1]==0 -> isolated_count=2-1=1
            deg[2]==0 -> isolated_count=1-1=0
            add edge: adj[1]={2}, adj[2]={1}, deg[1]=1, deg[2]=1
            append 0.

   Query2: 2 1 -> 
        v=1: deg[1]=1>0 -> isolated_count +=1 -> becomes 1.
        neighbors = list(adj[1]) = [2]
        for w in [2]:
            adj[2].discard(1) -> adj[2] becomes empty -> deg[2] becomes 0 -> then isolated_count +=1 -> becomes 2.
        Then clear adj[1] -> set to empty, deg[1]=0.
        append 2.

   Then output: [0,2]

But after query1: no isolated vertices -> output 0 -> correct.
After query2: both isolated -> output 2 -> correct.

Now, what if we remove the same edge by type2 on the other vertex?

   Query3: 2 2 -> 
        v=2: deg[2]=0 -> skip, append 2.

   So output: [0,2,2] -> correct.

Now, what if we add the edge again?

   Query4: 1 1 2 -> 
        u=1, v=2: 
            deg[1]=0 -> isolated_count=2 -> then subtract 1: becomes 1
            deg[2]=0 -> subtract 1: becomes 0
            add edge: adj[1]={2}, adj[2]={1}, deg[1]=1, deg[2]=1
            append 0.

   Then output: [0,2,2,0]

So the algorithm is consistent.

Therefore, we can implement accordingly.

Now, we write the code.

Note: we are using a list of sets for adj. The size is N+1. 

Complexity: O(Q) per total.

Let me run the first example with the code to see the steps.

But we did that manually.

I think it's good.

We'll write the solution accordingly.

## Final Solution

### Solution Implementation
```python
from typing import List

class Solution:
    def solve(self, N: int, Q: int, queries: List[List[int]]) -> List[int]:
        # Initialize degree array and adjacency list
        deg = [0] * (N + 1)
        adj = [set() for _ in range(N + 1)]
        isolated_count = N  # Initially, all vertices are isolated
        results = []
        
        for query in queries:
            if query[0] == 1:  # Add edge between u and v
                u, v = query[1], query[2]
                # If u is currently isolated, it will lose isolation
                if deg[u] == 0:
                    isolated_count -= 1
                # If v is currently isolated, it will lose isolation
                if deg[v] == 0:
                    isolated_count -= 1
                # Add edge in both directions
                adj[u].add(v)
                adj[v].add(u)
                deg[u] += 1
                deg[v] += 1
                results.append(isolated_count)
            else:  # Remove all edges incident to v
                v = query[1]
                if deg[v] > 0:  # Only process if v has edges
                    # v becomes isolated
                    isolated_count += 1
                    # Create a copy of neighbors to avoid modification during iteration
                    neighbors = list(adj[v])
                    for w in neighbors:
                        # Remove v from w's adjacency list
                        adj[w].discard(v)
                        deg[w] -= 1
                        # If w becomes isolated after removal
                        if deg[w] == 0:
                            isolated_count += 1
                    # Clear v's adjacency list and reset degree
                    adj[v].clear()
                    deg[v] = 0
                    results.append(isolated_count)
                else:
                    results.append(isolated_count)
                    
        return results
```

### Explanation
1. **Initialization**: 
   - `deg` array tracks the degree (number of edges) for each vertex.
   - `adj` is a list of sets representing the adjacency list for efficient edge additions and removals.
   - `isolated_count` counts vertices with degree 0, initialized to `N` since all vertices start isolated.

2. **Processing Queries**:
   - **Type 1 (Add Edge `1 u v`)**:
     - If either `u` or `v` is isolated (degree 0), decrement `isolated_count`.
     - Add the edge in both directions in the adjacency list and update degrees.
     - Append the current `isolated_count` to results.
   - **Type 2 (Remove All Edges `2 v`)**:
     - If `v` has edges (degree > 0), increment `isolated_count` (since `v` becomes isolated).
     - Iterate over all neighbors of `v` (using a copy to avoid modification issues):
       - Remove `v` from each neighbor's adjacency list and decrement their degree.
       - If a neighbor's degree drops to 0, increment `isolated_count`.
     - Clear `v`'s adjacency list and set its degree to 0.
     - Append the updated `isolated_count` to results. If `v` had no edges, simply append the current count.

3. **Efficiency**:
   - **Time Complexity**: O(Q) amortized. Each edge is added once and removed at most once. Operations on adjacency sets (add/discard) are O(1).
   - **Space Complexity**: O(N + Q) to store adjacency lists and degrees, where Q is the maximum number of edges.

This approach efficiently maintains the graph state and isolated vertex count by leveraging adjacency sets for dynamic edge management and degree tracking. The solution handles each query in constant or amortized constant time, making it suitable for large inputs.