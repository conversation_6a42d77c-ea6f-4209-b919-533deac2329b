# Solution for abc328_e.md

Generated at: 2025-07-22 06:44:54
Model: deepseek-reasoner

## Reasoning Process

Okay, I'm going to tackle this problem step by step. Let me start by understanding the problem clearly.

We have a connected, undirected graph with N vertices and M edges. Each edge has a weight. We need to find a spanning tree of this graph such that the sum of the edge weights modulo K is minimized. The output should be that minimum modulo cost.

Constraints are important here. N is at most 8, which is very small. M can be up to 28 (since for N=8, the maximum edges in a complete graph is 28). K can be as large as 10^15, which is huge, so we have to be careful with modulo operations.

Since N is small (2 to 8), a brute-force approach might be feasible. But let me think about what brute-force would entail.

The brute-force solution would generate all possible spanning trees, compute the sum of weights modulo K for each, and take the minimum. How many spanning trees are there? For a complete graph with N nodes, the number of spanning trees can be as high as N^(N-2) by <PERSON><PERSON><PERSON>'s formula. For N=8, that's 8^6 = 262,144. That's manageable.

But wait, the graph isn't necessarily complete. However, M is at least N-1 and at most 28. So the number of spanning trees might be less, but worst-case (complete graph) we have 262,144 which is acceptable.

So, one approach is to generate all spanning trees, compute the total weight modulo K for each, and then take the minimum. But how do we generate all spanning trees?

We can use recursion or iterative methods to consider all combinations of edges that form a spanning tree. Alternatively, we can use Kirchhoff's theorem to get the number of spanning trees, but that doesn't list them.

Another approach: since N is small, we can use bitmask DP? But the state would be which nodes are included, but edges are many. Alternatively, we can use recursion to try all combinations of N-1 edges and check if they form a spanning tree.

But checking every combination of M choose N-1: worst-case M=28, N-1=7, so C(28,7) is about 1,184,040. That's also acceptable for a modern computer, especially in Pyton if optimized in PyPy or with pypy, but even in CP Python, 1e6 iterations is acceptable.

But wait, actually, worst-case M=28 and N-1=7, so C(28,7) = 1184040. And for each combination, we need to check if it forms a spanning tree. How to check? We can use union-find or BFS/DFS to check connectivity. Since the set has exactly N-1 edges and N nodes, we just need to check if it connects all nodes without cycles. Union-find would be efficient for that.

So steps for brute-force:
1. Generate all combinations of edges taken N-1 at a time.
2. For each combination, use union-find to check if it forms a spanning tree (i.e., no cycles and connects all nodes).
3. If it is a spanning tree, compute the total weight mod K.
4. Track the minimum mod value.

But wait, the modulo operation: we have to take the total weight mod K, and then find the minimum among these mod values. But note: the problem says "minimum cost", and cost is defined as (sum mod K). So we are comparing modulo values. However, modulo values are in [0, K-1]. But note: it's possible that we have two different sums: one gives mod value a and another gives b. We want the min between a and b. However, because modulo wraps around, we cannot simply compare the modulo values. For example, if K is 100, then 99 and 1: 1 is less than 99. But wait, actually, the problem says "minimum cost", and cost is defined as the modulo. So we are to minimize the modulo value. So we want the smallest nonnegative residue. Therefore, we can just compute the modulo of the total weight and then take the minimum of that modulo value over all spanning trees.

But note: two different spanning trees might have the same modulo value? Then we just take that value. But we are to find the minimum modulo cost. So yes, we compute each modulo and take the min.

However, there is a catch: the total weight might be huge? But K is up to 10^15, and the sum of weights: worst-case, each edge weight is up to K (but actually, w_i < K), and we have up to 7 edges (N-1=7). So the maximum sum is 7*(K-1) which is about 7e15, which is within Python integers, but modulo will bring it to [0, K-1]. So we can compute the total sum and then mod K.

But wait: we have to be cautious because if the total sum is huge, but Python integers are arbitrary precision, so no overflow, but modulo with K is safe.

So the brute-force with combinations is O( C(M, N-1) * O(N) ) because union-find for N nodes and N-1 edges is O(N). Since C(M, N-1) is about 1e6 for M=28 and N-1=7, and 1e6 * 8 = 8e6, which is acceptable in Python? Maybe in PyPy, but in CP Python, 8e6 operations might be borderline in Pyton, but since constraints are small (N=8 worst-case) and M is 28, it should be acceptable.

But wait: worst-case M=28 and N=8, so we are taking 7 edges: C(28,7)=1184040. Then for each combination, we do union-find which is about 7 operations per set? Actually, union-find with union by rank and path compression is nearly O(1) per operation, but even without, since N=8, we can just use a simple union-find without optimizations and do 7 unions and check if we have one connected component.

Alternatively, we can use BFS/DFS on the subgraph of 8 nodes and 7 edges. But building the graph each time might be O(7) and then BFS is O(8), so total per combination is O(15) which is acceptable.

But I think union-find is simpler.

But wait, is there a better way? Since N is only 8, we can also use the matrix-tree theorem to get the determinant? But that gives the number of spanning trees, not the list of them. We need the actual sums.

Alternatively, we can iterate over all possible spanning trees without generating all combinations? There are algorithms to iterate over spanning trees, like the one by Gabow and Myers, but that might be complicated and since N is small, combinations might be simpler.

So brute-force with combinations is acceptable.

But wait: what if M is exactly N-1? Then there is only one spanning tree. Then we can just compute that one. But the combinations method would still work: C(M, N-1) = 1.

But what about the modulo? We have to compute the sum mod K.

But there is a catch: the minimum modulo value. Note that the modulo operation might wrap around. But the problem says the cost is the sum mod K. So we are to minimize that value. However, note that the modulo operation can result in a small value even if the actual sum is large. For example, if we have two spanning trees with sums 100 and 150, and K=100, then the costs are 0 and 50, so 0 is the minimum. But if we have sums 99 and 100, then modulo 100: 99 and 0, so 0 is the minimum. So we have to compute modulo for each.

But wait: the minimum modulo value might not correspond to the smallest actual sum. For example, if we have two spanning trees: one with sum 199 and another with 100, and K=100, then 199 mod 100 = 99, 100 mod 100 = 0. So 0 is the minimum. So we cannot just compare the sums without modulo.

Therefore, for each spanning tree, we must compute the total weight mod K and then take the minimum of these mod values.

But note: it's possible that we get multiple spanning trees with the same mod value, but we just need the minimum.

So the algorithm:

1. Preprocess: read N, M, K, and the list of edges.
2. Generate all combinations of edges of size N-1.
3. For each combination:
   a. Initialize a union-find structure for N nodes (nodes 1 to N).
   b. For each edge in the combination:
        if the two nodes are already in the same set, then this combination has a cycle -> skip (break early).
        else, union them.
   c. After processing all edges in the combination, check if the union-find has only one connected component (i.e., all nodes are connected). Since we have N-1 edges, if there's no cycle, then it must form a spanning tree. Actually, no: if there's no cycle and we have N-1 edges, then it must be a spanning tree? Because a tree with N nodes has N-1 edges and is connected. So if we have exactly N-1 edges and no cycle, then it's a spanning tree. But wait: what if the edges don't connect all nodes? Then we would have multiple components and the union-find would have more than one component. Actually, if we have N-1 edges and no cycle, then the graph is a tree only if it's connected. But without cycles and with N-1 edges, if it's not connected, then it would have at least two components and the number of edges would be less than required for connectivity. Actually, the condition for acyclic and n-1 edges implies connected. Yes: any acyclic graph with n nodes and n-1 edges is a tree (hence connected). So actually, if we have no cycles in the combination and we have exactly n-1 edges, then it must be connected. Therefore, we only need to check for cycles.

But note: the combination step gives us a set of edges. We are using union-find to check for cycles. If at any edge we try to union two nodes that are already connected, then we break. Then, if we successfully union all edges without a cycle, then it's a spanning tree.

So we don't need to check the connectivity at the end? Because we have N-1 edges and no cycles, so it must be connected. So we can break early if we find a cycle.

Steps for each combination:

- Initialize parent array for N nodes (index 0 to N-1 for nodes 1 to N).
- For each edge in the combination (in any order):
    u, v, w = edge
    find the root of u and root of v
    if same root -> cycle, break out of this combination.
    else, union them.
- If we didn't break (i.e., no cycles), then this combination is a spanning tree. Then compute the total weight = sum(w for each edge) mod K. Then update min_cost = min(min_cost, total_weight_mod)

But note: we must consider that the graph is connected? But as reasoned, having no cycles and exactly N-1 edges in a graph of N nodes implies connected. So we are safe.

But wait: what if the combination doesn't include all nodes? Actually, with N-1 edges and no cycles, we must have a tree that includes all N nodes. Because if a node is missing, then we would have only N-1 nodes? But no: each edge connects two nodes. We have N nodes and N-1 edges. If the graph is not connected, then we would have at least two components and the number of edges would be less than (number of nodes in the component) for each component. Actually, the condition for acyclic: the number of edges must be <= (number of nodes) - (number of components). But we have exactly N-1 edges and if there are c components, then the graph must have at least N - c edges to be acyclic. So N-1 = N - c => c=1. Therefore, it must be connected. So no problem.

So the algorithm should work.

But what about the time? Worst-case M=28, N=8, so N-1=7. Then the number of combinations is C(28,7)=1184040. For each combination, we do 7 union-find operations. Each union-find operation with path compression? Actually, we can implement union-find without path compression since N is small. Even with a simple union-find that uses O(N) per union, total per combination is 7*8=56, which is acceptable. But we can do union by size or rank to make it efficient, but N=8, so it's negligible.

But we can also do without union-find? We can build a graph from the 7 edges and then do BFS/DFS to check connectivity and acyclicity? But union-find is easier and efficient enough.

So I'll go with union-find.

But note: we need the total weight of the spanning tree. We can precompute the sum as we go? Or after we know it's a tree, then we sum the weights. But we can accumulate the weight during processing the edges? Actually, no: during the union-find we are processing each edge, so we can accumulate the total weight as we go.

But if we break early due to a cycle, we skip the rest. So we can do:

total = 0
for each edge in combination:
    total += w
    if union-find finds that u and v are connected -> break and skip
else: # no break -> then update min_cost = min(min_cost, total % K)

But wait: we are adding the weight even if we break? We shouldn't. So we can do:

total = 0
valid = True
for each edge in combination:
    if u and v are connected: valid=False; break
    else: union them and add w to total
if valid: 
    candidate = total % K
    min_cost = min(min_cost, candidate)

But note: the modulo operation: if total is huge, but we can do modulo at the end? Since we are adding 7 numbers each less than K, the total is at most 7*(K-1) < 7*K, so modulo K is just total - K * floor(total/K). But we can do modulo at the end.

But we could also do modulo during the accumulation? Actually, we don't have to because the total is not going to be astronomically huge? 7*K is about 7e15, which is an integer that Python can handle. But modulo at the end is fine.

But what if the total is 0? Then modulo is 0. So no problem.

Now, what about the union-find implementation? We can write a simple one:

We'll create a parent list for nodes 0 to N-1 (since nodes are 1-indexed).

For each combination, we initialize:
    parent = list(range(N))   # so parent[i] = i for node i (0-indexed)
    # and we can have a rank array for union by rank? Or just a list for parent.

But without union by rank, the worst-case tree depth is 8, so find is O(8) per operation, which is acceptable.

Alternatively, we can do union by size or rank to make it a bit faster, but not necessary.

But for correctness, we'll implement:

def find(x, parent):
    if parent[x] != x:
        parent[x] = find(parent[x], parent)
    return parent[x]

But wait, we are going to call find for each edge? Then we can use path compression. But note: we are initializing the union-find for each combination. So the entire union-find state is reset. And we are doing only 7 unions per combination. So without path compression, worst-case the tree can be deep, but N=8, so depth at most 7. So it's acceptable.

But path compression is easy to add and might help a bit.

Alternatively, we can avoid recursion for path compression? We can do iterative:

def find(x, parent):
    stack = []
    while parent[x] != x:
        stack.append(x)
        x = parent[x]
    root = x
    for node in stack:
        parent[node] = root
    return root

But again, for N=8, we don't need to worry.

I'll use recursion? But recursion depth is 8, so safe.

But to avoid recursion, we can do iterative. But let's keep it simple.

Alternatively, we can use iterative without compression? Actually, we can do iterative without compression and it would be O(8) per find.

But I'll do with path compression.

Steps for union-find for one combination:

parent = list(range(N))
rank = [0]*N   # for union by rank? Actually, we don't need rank, but it's easy.

But since we are doing only 7 unions, we can do union by setting parent arbitrarily.

But to make the tree flat, we do path compression in find.

But union by rank: we can skip? It's optional.

So for each edge in combination:

    u, v, w = edge (note: u and v are given as 1-indexed, so we convert to 0-indexed: u1 = u-1, v1 = v-1)

    ru = find(u1, parent)
    rv = find(v1, parent)
    if ru == rv: break the loop (invalid, cycle)
    else: 
        total += w
        parent[rv] = ru   # or without rank, just set parent[rv]=ru

But without union by rank, the tree might be deep, but then the next find operations might be slower? But only 7 times, and N=8, so it's negligible.

But if we do union by rank, we can minimize the depth. But again, not necessary.

So I'll skip union by rank and just set parent[rv] = ru.

But then we do path compression in find? Yes, to flatten the tree.

So the find function:

def find(x, parent):
    if parent[x] != x:
        parent[x] = find(parent[x], parent)
    return parent[x]

But this is recursive. Alternatively, iterative:

def find(x, parent):
    root = x
    while root != parent[root]:
        root = parent[root]
    while x != root:
        nxt = parent[x]
        parent[x] = root
        x = nxt
    return root

But again, for small N, it doesn't matter. Let me use iterative to avoid recursion limits? But N=8, recursion depth 8 is safe. But I'll use iterative for clarity and avoid recursion.

Actually, the iterative with path compression is efficient and clear.

So I'll implement:

def find(x, parent):
    root = x
    while root != parent[root]:
        root = parent[root]
    # Path compression
    while x != root:
        next_node = parent[x]
        parent[x] = root
        x = next_node
    return root

But note: we are modifying the parent array for this combination. Since each combination gets its own parent array, it's safe.

Then for union:

ru = find(u, parent)
rv = find(v, parent)
if ru == rv: cycle -> break
else: parent[ru] = rv? Or parent[rv] = ru? It doesn't matter. We can set parent[ru] = rv.

But then we are changing the root of ru to rv.

So:

ru = find(u, parent)
rv = find(v, parent)
if ru == rv: break
else: parent[ru] = rv   # or parent[rv] = ru? 

Actually, both are fine. But note: if we set parent[ru] = rv, then rv becomes the root of the merged set.

So:

ru = find(u, parent)
rv = find(v, parent)
if ru == rv: 
    break out, invalid
else:
    parent[ru] = rv
    total += w

But then we have to do the union.

Now, what about the initial parent array? We create it for each combination: parent = list(range(N))

But nodes are 0 to N-1.

So the algorithm for each combination:

parent = [i for i in range(N)]
total_weight = 0
is_tree = True
for edge in combination_edges:
    u, v, w = edge
    u_idx = u-1
    v_idx = v-1
    ru = find(u_idx, parent)
    rv = find(v_idx, parent)
    if ru == rv:
        is_tree = False
        break
    else:
        parent[ru] = rv   # or parent[rv] = ru? 
        total_weight += w

if not is_tree:
    continue
else:
    # we have a spanning tree
    cost = total_weight % K
    min_cost = min(min_cost, cost)

But wait: when we set parent[ru]=rv, we are making rv the parent of ru. Then the root of ru becomes rv. So that's correct.

But what if we set parent[rv]=ru? Then ru becomes the root. It doesn't matter. But we must be consistent.

Actually, we can set either. I'll set parent[ru] = rv.

But note: we are modifying the parent array for this combination. Since we break out on cycle, the next combination gets a fresh parent array.

But what if we don't break? Then we use the parent array for the next edges. So it's correct.

But after we break, we skip the rest of the edges in the combination and move to the next combination.

Now, what about the possibility that the combination has a cycle? Then we skip.

But what if the combination doesn't have a cycle? Then we have a spanning tree.

But note: what if the combination has less than N-1 edges? But we are taking exactly N-1 edges. So the for loop runs for each edge in the combination (N-1 times). So we break only if we find a cycle.

Now, we have to initialize min_cost to a big number? Since K can be up to 10^15, we can set min_cost = K (because modulo values are in [0, K-1], so the minimum is at most K-1). But we can set min_cost = K, then at the end if min_cost is still K, then there is no spanning tree? But the graph is connected, so there is at least one. So we can set min_cost = K.

But then we take min with cost, which is < K? So if we set min_cost = K, then the first cost will set it to a value in [0, K-1]. Then we update.

Alternatively, set min_cost = None, then if we have a candidate, set it. Then at the end, we return min_cost.

But we know there is at least one spanning tree.

So the overall algorithm:

min_cost = K   # or K (because modulo values are < K, so min_cost will be set to a value < K) but we can do min_cost = 10**18
But let me set min_cost = float('inf'), but then we have to compare integers. Alternatively, set min_cost = K (which is at least 1, so modulo values are in [0, K-1], so min_cost = K is safe as an initial large value).

But then min_cost = min(min_cost, cost). And cost is in [0, K-1], so min_cost will become the minimum cost.

But if we set min_cost = K, then since cost < K, it will be updated.

So:

min_candidate = K   # because we want the minimum modulo value, which is at most K-1

for each combination of edges of size N-1:
    # do union-find as above
    if no cycle:
        cost = total_weight % K
        if cost < min_candidate:
            min_candidate = cost

But what if we have a cost 0? Then we can break early? But not necessary because we have to check all? Actually, we are looking for the minimum modulo value. So if we get 0, we can break? But the problem says "minimum cost", and 0 is the smallest possible. So we can break early if cost==0? But it might not happen, so we can add an optimization: if at any point we get cost==0, we break out and return 0.

But that's optional. Since we are iterating over all spanning trees, and if we find one with cost 0, we can break early. But worst-case, we might not find 0 until the end, so it doesn't affect worst-case complexity. But it's an easy optimization.

So:

if cost == 0:
    min_candidate = 0
    break out of the combination loop? Actually, we can break out of the outer loop? But we are iterating over combinations. We can break the outer loop if we set a flag.

But for clarity, we can just check min_candidate==0 and then break.

But let me not do that for now. It's simple to add.

Now, how to generate combinations? We can use itertools.combinations. Since M is at most 28, and N-1=7, we can do:

from itertools import combinations

all_combinations = combinations(edges, N-1)

Then iterate over each comb in all_combinations.

But the edges are given as a list. So we can do:

edges_list = the input list of edges.

Then:

for comb in combinations(edges_list, N-1):
    # process this combination

But note: the edges are represented as [u, v, w]. So each element in comb is an edge.

So the code structure:

def solve(N, M, K, edges):
    from itertools import combinations
    min_cost = K  # initial value

    # Precompute: we don't need to sort? Because we are taking combinations, not based on weight.
    # But note: the combination is taken arbitrarily.

    # If M is exactly N-1, then only one combination, so we can break early? But itertools.combinations will generate only one, so no problem.

    for comb in combinations(edges, N-1):
        parent = list(range(N))
        total_weight = 0
        is_tree = True
        # We need a find function? We'll define it inside or outside? Inside, but we can define a helper function inside solve.

        # Define find as a nested function? But we can define it inside the loop? Actually, no: we can define it once.

        # Let me define a helper function for find that uses iterative path compression.

        # But to avoid defining the function repeatedly in the loop, we can define it outside the loop.

        # Actually, we can define a function inside solve but outside the loop.

        def find(x, parent_arr):
            # We are passing the parent array
            root = x
            while root != parent_arr[root]:
                root = parent_arr[root]
            while x != root:
                next_node = parent_arr[x]
                parent_arr[x] = root
                x = next_node
            return root

        # But note: we are passing the parent array for this combination.

        # Alternatively, we can write it without a function? Since N is small, we can do without path compression? Then we can do:

        # Actually, for simplicity, we can avoid the function and do iterative without path compression? Because N is 8.

        # But let me write the function.

        # However, we are in a loop and the function is defined only once? Actually, in Python, defining a function inside a loop: each iteration we define a new function? That might be inefficient. But we have 1e6 iterations? Then we are creating 1e6 function objects? That might be heavy.

        # So better to define the function once outside the loop.

        # How? We can define the function at the beginning of solve.

        # But the function needs to be defined to take parent_arr. So:

        # Option: define the find function once at the top of solve.

        # But the union-find algorithm is the same for every combination. So:

        # Let me define:

        # Actually, we can write without a helper function: just inline the path compression? But that would be messy.

        # Alternatively, we can define the find function once at the top of solve.

        # I'll do that.

    But wait, we are going to use the same function for different parent arrays. So the function must take the parent array as an argument.

    So:

    def find(x, par):
        # iterative path compression
        root = x
        while root != par[root]:
            root = par[root]
        while x != root:
            nxt = par[x]
            par[x] = root
            x = nxt
        return root

    Then in the loop:

        parent_arr = list(range(N))
        total_weight = 0
        valid = True
        for edge in comb:
            u, v, w = edge
            u_idx = u-1
            v_idx = v-1
            ru = find(u_idx, parent_arr)
            rv = find(v_idx, parent_arr)
            if ru == rv:
                valid = False
                break
            else:
                parent_arr[ru] = rv   # union: attach ru to rv
                total_weight += w

        if not valid:
            continue

        cost = total_weight % K
        if cost < min_cost:
            min_cost = cost
            # if min_cost == 0: break? We can break early for optimization.
            if min_cost == 0:
                # We can break out of the outer loop? But we are in a for loop over combinations. How to break?
                # We can set a flag and break the inner, then break the outer? Or we can break twice? Not directly.

                # Instead, we can do: break the inner loop, then break the outer loop by setting a flag.

        # End if valid
    # End for comb

    return min_cost

But we can break the outer loop if min_cost==0 by using a flag:

    min_cost = K
    found_zero = False
    for comb in combinations(edges, N-1):
        ... 
        if not valid: continue
        cost = total_weight % K
        if cost < min_cost:
            min_cost = cost
            if min_cost == 0:
                found_zero = True
                break   # breaks the inner for, then we break the outer
        # end if
        if found_zero:
            break   # this breaks the outer for loop? But the inner break already broke the inner, so we break again? Actually, we break the inner loop, then we break the outer.

    Actually, we can do:

        if min_cost == 0:
            break   # break the outer for loop

    But after the inner loop, we break the outer.

    But the break after the inner loop: we can do:

    for comb in combinations(edges, N-1):
        ... 
        if valid:
            cost = total_weight % K
            if cost < min_cost:
                min_cost = cost
            if min_cost == 0:
                break   # break the outer for loop
        ...

    Then we break the outer loop.

    But what if we break during the inner loop? Then we skip the cost update, so we don't set min_cost=0? Actually, if we break the inner loop due to cycle, we skip. Only when we have a spanning tree and we get cost=0, we break.

    So:

    for comb in combinations(edges, N-1):
        parent_arr = list(range(N))
        total_weight = 0
        valid = True
        for edge in comb:
            u, v, w = edge
            u_idx = u-1
            v_idx = v-1
            ru = find(u_idx, parent_arr)
            rv = find(v_idx, parent_arr)
            if ru == rv:
                valid = False
                break
            else:
                parent_arr[ru] = rv
                total_weight += w
        if not valid:
            continue
        # valid, so we have a spanning tree
        cost = total_weight % K
        if cost < min_cost:
            min_cost = cost
            if min_cost == 0:
                break   # break the outer loop

    return min_cost

But note: what if the first spanning tree has cost 0? Then we break. Otherwise, we continue until we find 0 or we finish.

But worst-case we might have to check all combinations, but if we find 0, we break early.

This should be efficient.

But what if there is no spanning tree with cost 0? Then we do all combinations.

But worst-case is 1e6, which is acceptable.

Now, let me test with the examples.

Example 1: 
Input: 
5 6 328
1 2 99
1 3 102
2 3 86
2 4 94
2 5 95
3 4 81

We need to get 33.

How many combinations? C(6,4)=15? Because N-1=4. So we'll iterate over 15 combinations.

But the example says the spanning tree with edges 1,3,5,6: 
Edge1: 1-2:99
Edge3: 2-3:86? But wait: the edges are given as:

1: 1-2,99
2: 1-3,102
3: 2-3,86
4: 2-4,94
5: 2-5,95
6: 3-4,81

So the spanning tree: edges 1,3,5,6: 
1:1-2 (99)
3:2-3 (86) -> connects 1,2,3
5:2-5 (95) -> connects 5
6:3-4 (81) -> connects 4

Total = 99+86+95+81 = 361, 361 mod 328 = 33.

But in the combination, we are taking 4 edges: [edge1, edge3, edge5, edge6]. 

We need to check if we generate that combination. Then we compute total=361, mod328=33.

But are there other spanning trees with cost mod <33? The problem says the minimum cost is 33.

So we should get 33.

Now, how many spanning trees? For a graph of 5 nodes, the number of spanning trees is more than one. But we are generating all.

But 15 combinations is manageable.

But note: in the combination, we are taking 4 edges arbitrarily, but we skip if there is a cycle. So we only process the ones that are trees.

Now, Example 2: 
Input:
6 5 998244353
1 2 337361568
1 6 450343304
2 3 61477244
2 5 745383438
4 5 727360840

There is only one spanning tree? Because M=5, N=6, so N-1=5. So only one combination: all edges.

So we should compute the sum: 337361568+450343304+61477244+745383438+727360840 = 

But the example output is 325437688.

So we must get that.

But we can compute:

Let me compute: 

337361568 + 450343304 = 787704872
+61477244 = 849182116
+745383438 = 1594565554
+727360840 = 2321926394

Then mod 998244353: 2321926394 % 998244353 = ?

But 998244353 * 2 = 1996488706
2321926394 - 1996488706 = 325437688.

So yes.

Now, Example 3: 
Input: 
8 28 936294041850197
... 28 edges

We need to get 11360716373.

But we are generating C(28,7)=1184040 combinations. For each combination, we do 7 union-find operations. So 1184040 * 7 = 8288280 operations, which is acceptable in Python? I think so.

But wait: in Python, 8e6 operations might take a few seconds? But 8 million is acceptable in Pyton in a compiled language, but in Python, it might be around 10 seconds? But we can test.

But the constraints say N=8, and worst-case M=28, so we have to do it. And 8e6 operations is acceptable in Pyton in many cases.

But we can try to optimize by using a break condition: if we get cost 0, we break.

But in this case, we might not get 0? So we do all 1e6 combinations.

But 1e6 combinations * 7 edges = 7e6, which is acceptable.

So I'll code accordingly.

But what about the worst-case time? 1184040 combinations * 7 edges * (each union-find operation: the find operation might take O(log N) but without compression, worst-case O(N) per find. But with path compression, it becomes O(α(N)) which is nearly constant. But we are doing path compression, so each find is almost O(1). So total operations is about 7 * 1e6 * 1 = 7e6, which is acceptable.

But without path compression, worst-case the tree might be deep, but N=8, so each find is at most 7 steps, so 7*7=49 per combination, then 1184040 * 49 = 58,017,960 which is about 58 million, which is acceptable in Pyton? In Pyton, 10e6 operations per second? So 58 million might take 5 seconds? But we can use path compression to reduce to 7e6 operations, which is better.

So I'll use path compression.

Now, code the find function.

But note: we are passing the parent_arr to the find function. And we are modifying it in place. That's correct.

But in the union step, we set parent_arr[ru]=rv. Then we call find again, which compresses the path.

But we are only processing one combination at a time, so it's safe.

Now, what about the modulo operation? We do total_weight % K at the end of each valid combination.

But total_weight is the sum of 7 numbers, each < K, so total_weight < 7*K, which is 7e15, which is a big integer but Python handles it. Then modulo K is one operation per combination: 1e6 modulo operations? That's acceptable.

So the code:

Steps:

1. Import combinations from itertools.
2. Define a find function (iterative with path compression) that takes x and parent_arr and returns the root and compresses the path.
3. Initialize min_cost = K.
4. Iterate over each combination of edges of size N-1:
    a. Initialize parent_arr = list(range(N)) and total_weight = 0, valid = True.
    b. For each edge in the combination:
            u, v, w = edge
            u_idx, v_idx = u-1, v-1
            ru = find(u_idx, parent_arr)
            rv = find(v_idx, parent_arr)
            if ru == rv:
                valid = False
                break
            else:
                parent_arr[ru] = rv   # union: attach ru's tree to rv's tree
                total_weight += w
    c. If not valid, skip to next combination.
    d. Else, compute cost = total_weight % K
    e. If cost < min_cost: min_cost = cost. And if min_cost==0, break the outer loop.
5. Return min_cost.

But wait: in the union step, we set parent_arr[ru] = rv. But then we don't update the root of other nodes? The find function with path compression will update when called. But in the same combination, when we process the next edge, we call find again, which will update the parent of the nodes we've seen.

But we are not compressing the entire tree at every step? Actually, the find function we wrote does path compression: so when we call find(u_idx, parent_arr), it compresses the path from u_idx to root. Similarly for v_idx.

So it's efficient.

But note: in the union step, we are not updating the root of every node? But we don't need to. The next find calls will update.

So the code should be correct.

Now, let me test with a small example.

Example: 3 nodes, 3 edges, K=100.

Edges:
1-2: 10
1-3: 20
2-3: 30

The spanning trees:

Tree1: edges [1,2]: 10+20=30 mod100=30
Tree2: edges [1,3]: 10+30=40 mod100=40
Tree3: edges [2,3]: 20+30=50 mod100=50

Minimum is 30.

But also: we can take the edge indices: 
edge0: [1,2,10]
edge1: [1,3,20]
edge2: [2,3,30]

Combinations of 2 edges:

Comb1: edge0, edge1: 
    Process edge0: u=1,v=2: ru=find(0)=0, rv=find(1)=1 -> different, so set parent[0]=1, total=10.
    Then edge1: u=1,v=3: find(0): parent[0]=1, so find(0): goes to 1, then parent[1]=1 -> root=1. 
        v=3: root=2? Initially parent[2]=2. 
        ru=1, rv=2 -> different, so set parent[1]=2, total=10+20=30. Then valid, cost=30.

Comb2: edge0, edge2: 
    edge0: 1-2: set parent[0]=1, total=10.
    edge2: 2-3: u=2-> index1, v=3->index2: 
        find(1): parent[1]=1 -> root=1; find(2): root=2 -> different, so set parent[1]=2, total=10+30=40. Cost=40.

Comb3: edge1, edge2:
    edge1: 1-3: set parent[0]=? Initially: [0,1,2]. 
        u=1->0, v=3->2: find(0)=0, find(2)=2 -> different, set parent[0]=2? Then parent[0]=2, and parent[2]=2. 
        total=20.
    edge2: 2-3: u=2->1, v=3->2: 
        find(1)=1 (because parent[1]=1), find(2)=2 -> different, set parent[1]=2, total=20+30=50. Cost=50.

Then min_cost = min(30,40,50)=30. Correct.

But in comb3, when we set parent[0]=2 for the first edge, then for the second edge: u=2-> node1, v=3-> node2: 
    find(1)=1, find(2)=2 -> different. Then we set parent[1]=2. Then the entire tree: 0->2, 1->2, 2->2. So all connected.

So correct.

But note: in comb1, after the two edges, the parent array: 
Initially: [0,1,2]
After edge0: set parent[0]=1 -> [1,1,2]
Then for edge1: u=1->0, v=3->2: 
    find(0): 
        root: 0->parent[0]=1, then 1->parent[1]=1 -> root=1.
        v=2: parent[2]=2 -> root=2.
    Then set parent[1]=2 -> so parent[1]=2. Then the array: [1,2,2]. 
    Then when we call find(0): 
        0: parent[0]=1 -> then 1: parent[1]=2 -> then 2: parent[2]=2 -> so root=2. Then compress: set parent[0]=2, parent[1]=2.

But we don't do that until we call find. So after the two unions, the parent array is [1,2,2]. Then if we call find(0) again, it becomes 2.

But we don't call again in this combination. So it's okay.

Now, what about the edge case: when we have a cycle?

Example: same graph, but we take edge0 and edge1 and edge2: but we are only taking 2 edges, so we don't have that.

But consider a 3-node graph with edges: 1-2, 2-3, 1-3, and we take edge0 and edge2: which are 1-2 and 2-3: no cycle? Then we have a tree.

But if we take edge0 and edge1: 1-2 and 1-3: no cycle.

Only if we take edge0 and edge0? But we take distinct edges.

So the cycle check is correct.

Now, what if the graph is disconnected? But the problem states the graph is connected. So we don't have to worry.

Now, code for the example 3: the large one. We might not run it in our head, but we trust the algorithm.

But we can test with the provided examples.

Now, let me write the code accordingly.

But wait: what if the graph has multiple edges? The problem says "simple", so no multiple edges.

So we are safe.

Now, let me code.

But note: we are using parent_arr = list(range(N)) for each combination. Then we call find for each edge.

We'll implement the find function as described.

But the find function: we are modifying the parent_arr in place. For the next combination, we create a new parent_arr, so it's safe.

Now, I'll code the function.

But note: the worst-case number of combinations: when M=28 and N=8, we have 1184040 combinations. We can precompute the combinations? But itertools.combinations is efficient in terms of memory? It generates on the fly.

But the memory is O(r) per combination? But we are storing the combination as a tuple of edges? So each combination has 7 edges. Total memory: 1184040 * 7 * 3 integers? Each edge has 3 integers. So about 1184040 * 21 integers? Each integer is 24 bytes in Python? So 1184040 * 21 * 24 = about 1184040 * 504 = 596, million bytes? 596 MB? That's a lot.

But wait: itertools.combinations doesn't store all combinations in memory at once. It generates one at a time. So the memory is O(r) for the current combination. So we are only storing one combination at a time. Then we process it and discard. So memory is O(N-1) for the combination and O(N) for the parent array. So total memory is O(M) for the edge list and O(N) per combination. So it's acceptable.

But if we use combinations, we are not storing all combinations in memory. So we can use it.

But the time is 1e6 combinations, which is acceptable.

So the code:

import itertools

class Solution:
    def solve(self, N: int, M: int, K: int, edges: List[List[int]]) -> int:
        # If there is exactly one spanning tree? Then we can compute directly? But the brute-force will work.
        # Define find function
        def find(x, parent_arr):
            # Iterative path compression
            root = x
            while root != parent_arr[root]:
                root = parent_arr[root]
            # Path compression
            curr = x
            while curr != root:
                nxt = parent_arr[curr]
                parent_arr[curr] = root
                curr = nxt
            return root
        
        min_cost = K  # initial value, which is larger than any modulo result (since modulo results are in [0, K-1])
        
        # Generate all combinations of edges taken N-1 at a time
        # But note: if M < N-1, but the graph is connected and M>=N-1 by constraints, so we don't need to check.
        for comb in itertools.combinations(edges, N-1):
            parent_arr = list(range(N))
            total_weight = 0
            valid = True
            for e in comb:
                u, v, w = e
                # Convert to 0-indexed
                u_idx = u-1
                v_idx = v-1
                ru = find(u_idx, parent_arr)
                rv = find(v_idx, parent_arr)
                if ru == rv:
                    valid = False
                    break
                else:
                    # Union: attach ru to rv
                    parent_arr[ru] = rv
                    total_weight += w
            if not valid:
                continue
            # This is a spanning tree
            cost = total_weight % K
            if cost < min_cost:
                min_cost = cost
                if min_cost == 0:
                    break
        return min_cost

But wait: in the union step, we do parent_arr[ru] = rv. But then the root of ru's tree becomes rv. But we don't update the parent of other nodes in ru's tree? But when we call find again, it will update. So it's okay.

But in the same combination, when we process the next edge, we call find, which compresses the path for the nodes we are accessing. But for nodes not accessed, we don't compress. But that's okay because we only need to check connectivity for the edges we are processing.

So the code should be correct.

Now, test with the provided examples.

Example 1: we expect 33.

Example 2: we expect 325437688.

But we can run the code with the examples.

But since I can't run here, I'll do a dry run for example 1.

Example1 edges:

edges = [
    [1,2,99],
    [1,3,102],
    [2,3,86],
    [2,4,94],
    [2,5,95],
    [3,4,81]
]

N=5, so we take combinations of 4 edges.

The spanning trees: 

We know the one with edges: [0,2,4,5] (0-indexed: first edge is index0)

But we'll generate all combinations of 4 edges and check which are trees.

Total combinations: C(6,4)=15.

But we can skip the ones that have cycles.

The spanning tree we want: edges0: [1,2,99], edges2: [2,3,86], edges4: [2,5,95], edges5: [3,4,81]

In the list, the edges are:

index0: [1,2,99]
index1: [1,3,102]
index2: [2,3,86]
index3: [2,4,94]
index4: [2,5,95]
index5: [3,4,81]

So the combination: (index0, index2, index4, index5)

Process:

parent_arr = [0,1,2,3,4] (for 5 nodes: 0 to 4)

edge0: u=1->0, v=2->1: 
    find(0)=0, find(1)=1 -> different. Set parent_arr[0]=1. total=99.
    Now parent_arr: [1,1,2,3,4]

edge2: u=2->1, v=3->2: 
    find(1)=1 (because parent_arr[1]=1), find(2)=2 -> different. Set parent_arr[1]=2. total=99+86=185.
    parent_arr: [1,2,2,3,4]

edge4: u=2->1, v=5->4: 
    find(1): 
        1: parent_arr[1]=2 -> then 2: parent_arr[2]=2 -> root=2.
    find(4)=4 -> different. Set parent_arr[2]=4? Or wait: we set the root of u to the root of v? 
        ru = 2, rv=4 -> set parent_arr[2]=4.
    Now parent_arr: [1,2,4,3,4]
    total=185+95=280.

edge5: u=3->2, v=4->3: 
    u=3: node index2? No: the edge is [3,4,81] -> u=3-> index2? Wait: nodes: 
        node1:1, node2:2, node3:3, node4:4, node5:5 -> indices: 0,1,2,3,4.

    So edge5: u=3 -> index2, v=4 -> index3.
    find(2): 
        2: parent_arr[2]=4 -> then 4: parent_arr[4]=4 -> root=4.
    find(3): 3: parent_arr[3]=3 -> root=3.
    ru=4, rv=3 -> different. So set parent_arr[4]=3? 
        Then parent_arr: [1,2,4,3,3]? But we set parent_arr[ru]=rv? So set parent_arr[4]=3 -> so parent_arr[4]=3.
    total=280+81=361.

Then cost = 361 % 328 = 33.

Then min_cost = min(K, 33)=33.

Now, are there other spanning trees with cost mod <33? The problem says 33 is the minimum. So we should get 33.

But we must check if we break when we get 0? We don't get 0 here.

But we continue to check other combinations? Then we update min_cost to 33, and then if we find a tree with cost mod less than 33, we update. But we don't, so at the end we return 33.

So it should be correct.

Now, example2: only one combination: all 5 edges.

So we process:

edges: 
[1,2,337361568] -> u=0, v=1: set parent_arr[0]=1, total=337361568
[1,6,450343304] -> u=0, v=5: 
    find(0): 0->1 (because parent_arr[0]=1), then 1: parent_arr[1]=1 -> root=1.
    find(5)=5 -> set parent_arr[1]=5? Or set parent_arr[1]=5? Then total=337361568+450343304=787704872
[2,3,61477244] -> u=1, v=2: 
    find(1): 1->5 (because parent_arr[1]=5), then 5: parent_arr[5]=5 -> root=5.
    find(2)=2 -> set parent_arr[5]=2? Then total +=61477244 -> 787704872+61477244=849182116
[2,5,745383438] -> u=1, v=5: 
    find(1): 1->5->2 (because parent_arr[5]=2) -> then 2: parent_arr[2]=2 -> root=2.
    find(5): 5->2 -> root=2 -> same? 
    But wait: the edge is [2,5] -> u=2 (node2) -> index1? Or the input: 
        The edge: [2,5,745383438] -> u=2, v=5 -> so u=2 -> index1? But node2 is index1? 

    Actually, nodes: 
        node1: index0
        node2: index1
        node3: index2
        node4: index3
        node5: index4
        node6: index5

    So edge [2,5] -> u=2 -> index1, v=5-> index4.

    Then: 
        find(1): 
            1: parent_arr[1]=5? But wait: 
                Initially: 
                    edge0: [1,2] -> u=0, v=1: set parent_arr[0]=1 -> then parent_arr[0]=1, parent_arr[1]=1, others separate.
                edge1: [1,6] -> u=0, v=5: 
                    find(0): 0->1 -> root=1
                    find(5)=5 -> set parent_arr[1]=5 -> then parent_arr: [1,5,2,3,4,5]? For 6 nodes: indices 0 to 5.
                Then edge2: [2,3] -> u=2, v=3: 
                    u=2-> index1? No: wait, the edge [2,3]: node2 is index1? Actually, node2 is index1, node3 is index2.
                    So u=1, v=2: 
                    find(1): 1->5 -> parent_arr[5]=5 -> root=5.
                    find(2)=2 -> set parent_arr[5]=2 -> then parent_arr[5]=2.
                Then edge3: [2,5] -> u=2, v=5: 
                    u=2: index1? No: the edge [2,5] -> u=2 (which is node2, index1) and v=5 (node5, index4? But wait, we have 6 nodes: indices 0 to 5 for nodes 1 to 6.

                So node5 is index4? And node6 is index5? 

                In the edge [1,6]: u=1->0, v=6->5? Because node6 is the 6th node -> index5.

                So edge [2,5]: u=2->1, v=5->4? Because node5 is the 5th node -> index4.

                So we have:
                    u=1 (node2, index1), v=4 (node5, index4)
                Then:
                    find(1): 
                        1: parent_arr[1]=5? But wait: after edge0: parent_arr[0]=1, parent_arr[1]=1 -> then edge1: we set parent_arr[1]=5 -> so parent_arr[1]=5.
                        Then edge2: [2,3] -> u=1? No: [2,3]: u=2 (index1), v=3 (index2). 
                        Then we did: 
                            ru = find(1) -> 
                                1: parent_arr[1]=5 -> then 5: parent_arr[5]=? Initially 5, but then we set? 
                                After edge1: parent_arr[1]=5, and parent_arr[5]=5 (initially). 
                                Then for edge2: 
                                    ru = find(1): 1->5 -> root=5.
                                    rv = find(2)=2 (because parent_arr[2]=2 initially).
                                    Then set parent_arr[5]=2 -> so parent_arr[5]=2.
                        Then edge3: [2,5] -> u=1, v=4: 
                            ru = find(1): 
                                1: parent_arr[1]=5 -> then 5: parent_arr[5]=2 -> then 2: parent_arr[2]=2 -> root=2.
                            rv = find(4)=4 (initially).
                            Then set parent_arr[2]=4? Or set parent_arr[ru]=rv -> ru=2, rv=4 -> set parent_arr[2]=4.

                Then total +=745383438 -> 849182116+745383438 = 1,594,565,554
                Then last edge: [4,5] -> u=4, v=5: 
                    u=4: index3? The edge [4,5] -> u=4 (node4, index3), v=5 (node5, index4). 
                    Then:
                        ru = find(3)=3
                        rv = find(4)=4 (because parent_arr[4]=4 initially? But after setting parent_arr[2]=4, but we haven't set parent for index3 and 4? 
                    Then set parent_arr[3]=4? Then total +=727360840 -> 1,594,565,554+727,360,840 = 2,321,926,394
                    Then mod 998244353 = 2,321,926,394 - 2*998244353 = 2,321,926,394 - 1,996,488,706 = 325,437,688.

                So we get 325437688.

So the code should be correct.

Now, we run the code for the examples. But in the competition, we trust.

But what if we have a spanning tree that doesn't use the first edges? We are iterating over all, so we should get it.

Now, the example3: the output is 11360716373. We trust the algorithm.

But note: worst-case M=28, N=8, so 1184040 combinations. In Python, this might run in a few seconds? We can test locally.

But since the problem constraints are small, we can use this.

But is there a more efficient solution? 

Alternative approach: 

Since N is small (up to 8), we can use Kirchhoff's theorem to get the number of spanning trees, but that doesn't give the sums.

We can use a state space of bitmask of nodes? But we need the sum of weights modulo K, and K is huge.

We can do DP over subsets? 

State: dp[mask][i] = the minimum sum (or all possible sums modulo K) for a tree that covers the set of nodes in mask and having i as the root? But we don't care about the root. But the problem: we need to consider all spanning trees and their total weight mod K.

But storing all possible modulo values? K is 10^15, so we cannot store an array of size K.

Alternatively, we can store a set of all possible total weights modulo K for the spanning tree. But the total weight modulo K can be in the range [0, K-1], but K is huge (10^15) so we cannot store an array of size K.

But the number of spanning trees is only 262144 (for complete graph N=8), so we can store all the total weights? But the total weights are huge: up to 7*K, which is 7e15, so we cannot store all distinct sums. But we only care about the minimum modulo value.

But we cannot avoid iterating over all spanning trees? 

Alternatively, we can use a different method: 

We can use the recursive enumeration of spanning trees? But that would still be O(number of spanning trees).

So the brute-force is acceptable.

But there is an advanced method: 

We can use the matrix-tree theorem to compute the generating function (with edge weights as variables) and then find the minimum exponent modulo K? But that's complicated and not efficient for N=8.

So I think the brute-force is the best.

But note: we can avoid generating combinations that cannot form a tree? But we are already skipping cycles. But we generate all combinations of N-1 edges and then skip the ones with cycles. The number of combinations is C(M, N-1), which is the same as the number of spanning trees plus the ones that have cycles. But we skip the ones with cycles. But we still generate them. 

We can generate only the spanning trees? But the number of spanning trees is the same as the number we are checking that are valid. But the generation of combinations is C(M, N-1) which is larger than the number of spanning trees. But worst-case, the graph is complete, then the number of spanning trees is 8^6=262,144 and C(28,7)=1184040, which is about 4.5 times larger. But we skip the ones with cycles (which are 1184040 - 262144) quickly? Because we break at the first cycle.

In the worst-case (complete graph), the probability of a combination being a tree is 262144/1184040 ≈ 0.22. So we skip 78% quickly? Because we break at the first cycle.

But worst-case, the cycle might be detected at the last edge? Then we do almost N-1 operations. 

But on average, we break early for non-trees.

But worst-case total operations: 

Number of trees * (N-1) + number of non-trees * (average edges until cycle)

But worst-case for a non-tree: we process all N-1 edges and then skip? But we break as soon as we find a cycle. So for a non-tree, we break at the edge that creates a cycle. The cycle can be created at the first edge? Only if we have a self-loop? But we don't. The graph is simple. So the earliest cycle can be at the third edge? (if we have a triangle). 

So worst-case for a non-tree: we process all edges? Or break at the last edge? 

Actually, the cycle might be detected at any edge. The average might be early.

But worst-case we do (N-1) operations for each non-tree.

So total operations: 
    trees: T * (N-1) 
    non-trees: (C(M, N-1) - T) * (average number of edges until cycle)

But worst-case the average is O(N) per non-tree? Then total operations is O( C(M, N-1) * (N-1) ), which is 1184040 * 7 ≈ 8.3e6, which is acceptable.

So we can stick with the combinations.

Now, we write the code.

But note: the edge list might be large? But M is 28, so we can store.

Now, I'll write the code accordingly.

But one more thing: the node indices: we have 1-indexed in the input, and we convert to 0-indexed internally.

We'll test with the examples.

Let me run the code for the small example: 3 nodes, 3 edges, K=100, we expect 30.

Code:

edges = [[1,2,10],[1,3,20],[2,3,30]]
N=3, M=3, K=100
Combinations: 3 choose 2 = 3.

Comb0: edges [0,1]: 
    edge0: u=1->0, v=2->1: 
        parent_arr=[0,1,2] -> find(0)=0, find(1)=1 -> different: set parent_arr[0]=1. total=10.
    edge1: u=1->0, v=3->2: 
        find(0): 0->1 -> root=1. find(2)=2 -> different: set parent_arr[1]=2? Then total=30. Then cost=30%100=30. min_cost=min(100,30)=30.

Comb1: edges [0,2]: 
    edge0: [1,2,10]: set parent_arr[0]=1, total=10.
    edge2: [2,3,30]: u=2->1, v=3->2: 
        find(1)=1, find(2)=2 -> set parent_arr[1]=2, total=40. cost=40. min_cost=min(30,40)=30.

Comb2: edges [1,2]: 
    edge1: [1,3,20]: set parent_arr[0]=? 
        u=1->0, v=3->2: set parent_arr[0]=2? Then total=20.
    edge2: [2,3,30]: u=2->1, v=3->2: 
        find(1)=1 (parent_arr[1]=1 initially), find(2)=2? 
        Then set parent_arr[1]=2. total=50. cost=50.

Then min_cost=30.

Correct.

So we return 30.

Now, we run for example1 and example2.

But we'll submit the code.

But note: the constraints say that w_i < K and K can be up to 10^15, so we use big integers. Python handles.

Now, code:

I'll write the final code.

But note: we import itertools. That's acceptable.

Let me write.

One optimization: if M is large, but N is small, we can break early when min_cost==0.

Now, the complete code:

## Final Solution

To solve this problem, we need to find the minimum cost of a spanning tree in a given connected, undirected graph, where the cost is defined as the sum of the weights of the edges in the spanning tree modulo a given integer \( K \). The solution involves generating all possible spanning trees of the graph, computing their costs, and selecting the minimum cost.

### Approach
1. **Problem Analysis**: The problem requires finding a spanning tree (a connected acyclic subgraph that includes all vertices) of the given graph such that the sum of its edge weights modulo \( K \) is minimized. Given the constraints (vertices \( N \leq 8 \)), a brute-force approach is feasible.
2. **Key Insight**: For small \( N \), the number of spanning trees is manageable. We can generate all combinations of \( N-1 \) edges (since a spanning tree must have exactly \( N-1 \) edges) and check if they form a valid spanning tree (i.e., no cycles and connects all vertices).
3. **Algorithm Selection**: 
   - **Combination Generation**: Use itertools.combinations to generate all possible sets of \( N-1 \) edges.
   - **Cycle Detection**: For each combination, use Union-Find (Disjoint Set Union, DSU) to detect cycles. If adding an edge connects two vertices already in the same set, the combination contains a cycle and is invalid.
   - **Cost Calculation**: For valid spanning trees, compute the sum of edge weights modulo \( K \) and track the minimum such value.
4. **Optimization**: Early termination if a spanning tree with cost 0 is found, as 0 is the smallest possible modulo value.

### Solution Code
```python
import itertools
from typing import List

class Solution:
    def solve(self, N: int, M: int, K: int, edges: List[List[int]]) -> int:
        def find(x: int, parent_arr: List[int]) -> int:
            root = x
            while root != parent_arr[root]:
                root = parent_arr[root]
            current = x
            while current != root:
                next_node = parent_arr[current]
                parent_arr[current] = root
                current = next_node
            return root
        
        min_cost = K
        
        for comb in itertools.combinations(edges, N - 1):
            parent_arr = list(range(N))
            total_weight = 0
            valid = True
            for e in comb:
                u, v, w = e
                u_idx = u - 1
                v_idx = v - 1
                ru = find(u_idx, parent_arr)
                rv = find(v_idx, parent_arr)
                if ru == rv:
                    valid = False
                    break
                else:
                    parent_arr[ru] = rv
                    total_weight += w
            if not valid:
                continue
            cost = total_weight % K
            if cost < min_cost:
                min_cost = cost
                if min_cost == 0:
                    break
        return min_cost
```

### Explanation
1. **Union-Find Data Structure**: The `find` function implements path compression to efficiently determine the root of a node and merge sets during the union operation.
2. **Combination Processing**: The algorithm iterates over all combinations of \( N-1 \) edges. For each combination:
   - **Initialization**: A new Union-Find structure is initialized for the current combination.
   - **Cycle Check**: Each edge in the combination is processed. If an edge connects two nodes in the same set, the combination is invalid (contains a cycle).
   - **Valid Spanning Tree**: If no cycles are found, the total weight of the edges is computed. The cost is the total weight modulo \( K \).
3. **Early Termination**: If a spanning tree with cost 0 is found, the loop terminates early since 0 is the smallest possible cost.
4. **Result**: The minimum cost encountered during the iteration is returned.

This approach efficiently explores all possible spanning trees, leveraging the constraints (small \( N \)) to ensure feasibility. The Union-Find structure ensures cycle detection is optimal, and early termination optimizes the best-case scenario.