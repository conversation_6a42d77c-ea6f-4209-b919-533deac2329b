# Solution for abc321_b.md

Generated at: 2025-07-22 06:34:52
Model: deepseek-reasoner

## Reasoning Process

We are given:
  N: total number of rounds (so far we have N-1 scores, and one more round to go)
  X: the target final grade (we want the final grade to be at least X)
  A: list of N-1 integers (the scores of the first N-1 rounds)

The final grade is computed as the sum of the scores after excluding the highest and the lowest. 
But note: the exclusion is done after sorting the entire set of N scores (including the last round). 
Specifically, we sort all N scores: S[0] <= S[1] <= ... <= S[N-1]. Then the final grade is:
  S[1] + S[2] + ... + S[N-2]

We can also note that the final grade is the total sum minus the minimum and the maximum.

However, caution: if there are multiple occurrences of the minimum or maximum, we remove only one of each.
Example: [0,0,99,99] -> sorted: [0,0,99,99]; we remove one 0 (the smallest) and one 99 (the largest), leaving [0,99] -> sum=99.

But note the problem says: "excluding the highest and lowest", meaning one highest and one lowest. 
So the formula for the final grade is: 
  total_sum - min(score_list) - max(score_list)

But wait: is that always the same as the sum from index 1 to N-2? 
  Example: [0,0,99,99]: 
      total_sum = 0+0+99+99 = 198
      min = 0, max = 99 -> 198 - 0 - 99 = 99.
      And the middle part: [0,99] -> 0+99 = 99.

  But if we have [10,20,30,40]: 
      total_sum = 100
      min=10, max=40 -> 100-10-40 = 50.
      The middle part: [20,30] -> 50.

So yes, we can also compute as: total_sum - min - max.

However, note that the problem states: "the sequence of the scores earned in the rounds sorted in ascending order, then the final grade is S2+S3+...+S_{N-1}" (which is from index 1 to index N-2 in zero-indexing).

Therefore, we can express the final grade as:
  final_grade = total_sum - min_score - max_score

But note: the last round's score is variable (call it s, which we can choose from 0 to 100). We are to find the minimum s in [0,100] such that the final grade is at least X. If no such s exists, output -1.

Let:
  total_current = sum of the given N-1 scores (A)
  Then, after adding s, total_sum = total_current + s.

The final grade is: total_current + s - min(new_min, min_current) - min(new_max, max_current) ??? 

But wait, the entire set changes. The minimum of the entire set (N scores) is min(min_current, s) and the maximum is max(max_current, s).

So:
  total_sum = total_current + s
  min_total = min(min_current, s)
  max_total = max(max_current, s)

Then final_grade = total_current + s - min_total - max_total.

Alternatively, we can break into cases:

Case 1: s is between the current min and max (inclusive). Then:
  min_total = min_current (because min_current <= s) and max_total = max_current (because s <= max_current). 
  So final_grade = total_current + s - min_current - max_current.

Case 2: s < min_current. Then:
  min_total = s, and max_total = max_current (because max_current is still the max).
  So final_grade = total_current + s - s - max_current = total_current - max_current.

Case 3: s > max_current. Then:
  min_total = min_current, and max_total = s.
  So final_grade = total_current + s - min_current - s = total_current - min_current.

Therefore, we have:
  If s <= min_current: final_grade = total_current - max_current
  If s >= max_current: final_grade = total_current - min_current
  If min_current <= s <= max_current: final_grade = total_current + s - min_current - max_current

But note: what if the current list has multiple min or max? It doesn't matter because we are only removing one min and one max. 

However, let me test with Example 2: 
  Input: "3 100\n100 100"
  Here: N=3, X=100, A=[100,100]
  total_current = 200
  min_current = 100, max_current = 100.

  If we choose s=0 (which is less than min_current=100), then:
      final_grade = total_current - max_current = 200 - 100 = 100 -> which is >=100 -> so output 0.

  If we choose s=50 (which is between 100? no, because 50<100) -> so actually in the case s=50, it falls in case 1? 
  But wait, 50 is less than min_current=100 -> so it falls in case 1? Actually, no: 
      min_current=100, so if s=50, then the new min is 50, and the new max is 100 (because 100 is still the max). 
      Then final_grade = 200+50 - 50 - 100 = 100.

  Alternatively, using the case breakdown: 
      s=50: since 50<min_current, then we are in case 1? Actually, the case breakdown above was:
        Case 1: s between min_current and max_current -> inclusive? But 50 is less than min_current, so it's case 1? 
        Actually, I defined:
          Case 1: s between min_current and max_current (inclusive) -> then we use the formula with s in the middle.
          Case 2: s < min_current -> then we use total_current - max_current? But that would be 200-100=100.

      However, when s=50, the actual final_grade is 100, and 200 - max_current = 100 -> so that matches.

  Similarly, if s=150 (which is > max_current=100), then:
        final_grade = total_current - min_current = 200 - 100 = 100.

  But wait: if s=150, then the scores are [100,100,150]. 
      Sort: [100,100,150] -> remove the smallest (100) and the largest (150) -> leaves [100] -> sum=100.

  So the formulas hold.

Therefore, we can write the final_grade as a function of s:

  f(s) = 
      if s <= min_current: total_current - max_current
      if s >= max_current: total_current - min_current
      else: total_current + s - min_current - max_current

We are to find the minimum s in [0,100] such that f(s) >= X.

But note: the function f(s) is not linear everywhere. However, we can break the domain [0,100] into three segments:

  Segment 1: s in [0, min_current] -> f(s) = total_current - max_current (constant)
  Segment 2: s in (min_current, max_current) -> f(s) = total_current - min_current - max_current + s (linear increasing)
  Segment 3: s in [max_current, 100] -> f(s) = total_current - min_current (constant)

Therefore, we can check:

1. If the constant in Segment 1 (which is total_current - max_current) is already >= X, then we can choose the smallest s (which is 0) and that would work. So the answer would be 0.

2. Otherwise, we look at Segment 2: we have an increasing linear function. We require:
        total_current - min_current - max_current + s >= X
        => s >= X - (total_current - min_current - max_current)

   Since we are in the segment (min_current, max_current), the minimal s in that segment that satisfies is:
        s_min = max(min_current+1, ceil(X - (total_current - min_current - max_current))) 
   but wait: we are looking for an integer s. Actually, since s must be an integer, we can set:
        s0 = X - (total_current - min_current - max_current)
        Then we require s >= s0. But note: s0 might be fractional? but X, total_current, min_current, max_current are integers -> so s0 is integer.

   However, we must also consider that s must be at least min_current+1? Actually, no: the formula for the middle segment is valid for s in [min_current, max_current]? 

   Let me reexamine the cases: 
        We defined:
          if s <= min_current -> use constant1
          if s >= max_current -> use constant2
          else -> the linear part.

   But what about s = min_current? 
        Then it falls in the first case? because s<=min_current -> so we use constant1.

   Similarly, s = max_current falls in the third case.

   So the linear part is for s strictly between min_current and max_current? 

   Actually, we can redefine the cases to be inclusive? But note: if s = min_current, then the new min is s (which equals min_current) and the new max is max_current (since s<=max_current). 
        Then: final_grade = total_current + s - min_current - max_current? 
        But also: if s=min_current, then we could have considered it as the min, so then the formula for the linear part would be: total_current + min_current - min_current - max_current = total_current - max_current, which is the same as the first constant.

   Similarly, if s = max_current, then the linear part: total_current + max_current - min_current - max_current = total_current - min_current, which is the same as the third constant.

   So actually, we can merge: the linear part applies for s in [min_current, max_current]? Then the function becomes:

        f(s) = 
          if s < min_current: total_current - max_current
          if s > max_current: total_current - min_current
          else: total_current + s - min_current - max_current

   But wait: at s=min_current, we have two expressions? 
        The linear part: total_current + min_current - min_current - max_current = total_current - max_current -> same as the first constant.
        So we can write:

          f(s) = 
            if s < min_current: total_current - max_current
            if min_current <= s <= max_current: total_current + s - min_current - max_current
            if s > max_current: total_current - min_current

   But note: if s=min_current, then the linear part and the first constant yield the same. Similarly, s=max_current: the linear and the last constant yield the same.

   Alternatively, we can write:

          f(s) = total_current + s - min(min_current, s) - max(max_current, s)

   This formula holds for all s.

   However, for the purpose of solving, we can use the three cases.

So the plan:

  Let:
      total_current = sum(A)
      minA = min(A)
      maxA = max(A)

  We consider the three intervals:

      Interval 1: s in [0, minA) -> f(s) = total_current - maxA
      Interval 2: s in [minA, maxA] -> f(s) = total_current + s - minA - maxA
      Interval 3: s in (maxA, 100] -> f(s) = total_current - minA

  We are to find the minimum s in [0,100] such that f(s) >= X.

  Steps:

      1. Check the constant in Interval 1: if total_current - maxA >= X, then we can choose s=0 (which is the minimum possible) and we return 0.

      2. Else, check the linear part (Interval 2). We require:
             total_current + s - minA - maxA >= X
          => s >= X - (total_current - minA - maxA)

          Let needed_s = ceil( X - (total_current - minA - maxA) )? 
          But since we are dealing with integers, we can set:
             s_min2 = max( minA, X - (total_current - minA - maxA) )
          However, note: if we require s_min2 to be at least minA? and we also must have s_min2 <= maxA (because we are in interval2). 

          So if we set s_min2 = max(minA, X - (total_current - minA - maxA)) and if s_min2 <= maxA, then the answer is s_min2 (because we can choose s_min2, and it's the minimum in this interval).

      3. If the above does not yield a solution (i.e., the linear part doesn't achieve the goal, or s_min2>maxA), then we check Interval 3:
             if total_current - minA >= X, then we can choose s = maxA+1? But wait: the function in interval3 is constant for any s>=maxA? Actually, we can choose the smallest s in interval3, which is maxA+1? 
          But note: the function in interval3 is constant: total_current - minA. And that constant is independent of s. So if that constant is >= X, then we can choose s = maxA? Actually, wait: if we set s = maxA, then note that maxA is in the linear part? 

          Actually, we defined the linear part as [minA, maxA] (inclusive). So if we set s = maxA, then we are in the linear part and we have:
                f(maxA) = total_current + maxA - minA - maxA = total_current - minA.

          Therefore, we don't need to set s above maxA to get the constant total_current - minA. We can get it at s=maxA. So if the linear part did not yield a solution (because the required s_min2 was above maxA) but then we find that total_current - minA >= X, then the answer is maxA? 

          However, note: we are looking for the minimum s. And if we can achieve the goal at s = maxA (which is in the linear part) then we should have found it in step2? 

          Therefore, step2 must consider the entire interval [minA, maxA]. So if the required s_min2 is greater than maxA, then we skip to step3? and then step3 is: we require the constant for the third interval: total_current - minA >= X? 

          But note: the constant for the third interval is the same as at s=maxA. So if we didn't get a solution in step2 because the required s_min2 is greater than maxA, then we have to check: is total_current - minA >= X? 
          If yes, then we can set s = maxA? But wait: why didn't step2 catch s=maxA? 

          Actually, step2: 
             We require s >= needed_s, where needed_s = X - (total_current - minA - maxA). 
             Then we set s_min2 = max(minA, needed_s). 
             If s_min2 <= maxA, then we use s_min2. 
             If s_min2 > maxA, then the linear part cannot provide the solution (because we cannot choose s>maxA and be in the linear part). Then we check the third interval: 
                 The third interval gives the same value as at s=maxA: total_current - minA. 
                 So if total_current - minA >= X, then we can choose s = maxA? But wait: we are allowed to choose s = maxA and that is in the linear part. 

          Therefore, we must note: the linear part covers s in [minA, maxA]. So if needed_s is greater than maxA, that means that even if we set s=maxA, we get:
                f(maxA) = total_current + maxA - minA - maxA = total_current - minA.

          So if total_current - minA >= X, then we can achieve the goal at s=maxA. 

          However, we are looking for the minimum s. The minimum s that achieves the goal might be in the linear part? 

          But if needed_s is greater than maxA, that means that to achieve the goal in the linear part we would need s = needed_s, but needed_s > maxA -> so we cannot achieve in the linear part. Then the next option is the third interval: but the third interval requires s>maxA, and the function value is constant: total_current - minA. And if that constant is >= X, then we can choose any s in [maxA+1, 100]? Then the minimal s in that interval is maxA+1? 

          However, wait: if we set s = maxA, that is in the linear part and we get total_current - minA. So if total_current - minA >= X, then we can achieve at s = maxA. 

          Therefore, we don't need to go above maxA. 

          So the issue: in step2, we should not have set s_min2 = max(minA, needed_s) and then required it to be <= maxA? Actually, we should cap it by maxA? Because if needed_s is greater than maxA, then we can't use the linear part to get the goal? But we can use the linear part at s=maxA to get the value total_current - minA. 

          How does that relate to needed_s? 
                We require: total_current + s - minA - maxA >= X
                => s >= X - (total_current - minA - maxA) = needed_s.

          At s = maxA, the value is total_current - minA. And if total_current - minA >= X, then the condition is satisfied at s = maxA. But note: the condition for the linear part is satisfied at s = maxA? 
                Actually, the linear part formula applies at s = maxA, so we can use it. 

          Therefore, we can set: 
                s_min2 = max(minA, needed_s)   [but we must not exceed maxA? no, because if needed_s is 200 and maxA is 100, then we cannot choose s=200? But we are constrained to [0,100]. 

          Actually, we are constrained to s in [0,100]. So if needed_s is greater than maxA, then we cannot choose s in the linear part to satisfy the condition? Because we are limited to s<=maxA in the linear part. 

          However, we have an alternative: at s=maxA, we get the value total_current - minA. And that value might be >=X even if the linear condition for the middle segment is not satisfied for s<=maxA? 

          But note: the condition for the linear part: 
                f(s) = total_current + s - minA - maxA
          At s = maxA: 
                f(maxA) = total_current + maxA - minA - maxA = total_current - minA.

          And the condition we require: total_current - minA >= X? 

          How does that relate to needed_s? 
                needed_s = X - (total_current - minA - maxA) 
                Then if needed_s > maxA, that means:
                     X - (total_current - minA - maxA) > maxA
                     => X > total_current - minA - maxA + maxA = total_current - minA.

          So if needed_s > maxA, then X > total_current - minA.

          Therefore, at s=maxA, we get total_current - minA, which is less than X? So we cannot achieve the goal at s=maxA? 

          Then we must go to the third interval? But the third interval: 
                f(s) = total_current - minA   [for any s>maxA] 
          which is the same as at s=maxA? So if total_current - minA < X, then we cannot achieve the goal in the third interval either.

          Therefore, we must check:

            Step1: if total_current - maxA >= X -> then return 0.

            Step2: 
                Let needed_s = X - (total_current - minA - maxA)
                If needed_s <= maxA: then the minimal s that works in the linear part is max(minA, needed_s). But note: we must also ensure that max(minA, needed_s) is at most 100? but we are in the linear part which is up to maxA (<=100). 

                However, we must also check: what if needed_s is negative? then max(minA, needed_s) might be minA? but that is acceptable.

            Step3: 
                If we didn't return from step2, then we check the constant at the third interval: total_current - minA. 
                But note: we just argued that if needed_s > maxA, then X > total_current - minA, so the third interval constant is less than X? 

          Therefore, we only have one more possibility: the third interval constant? Actually, no: we just deduced that if needed_s>maxA then total_current - minA < X.

          So then we must check the third interval: if total_current - minA >= X, then we can choose s = maxA? But wait, we already saw that at s=maxA we get total_current - minA, and that is the same as the third interval. And we just deduced that if needed_s>maxA then total_current - minA < X? 

          Therefore, if step1 and step2 don't yield a solution, then we check step3: 
                if total_current - minA >= X, then we can choose s = maxA? 
          But note: we already computed that needed_s>maxA implies total_current - minA < X? 

          So if step2 fails (because needed_s>maxA), then step3 must also fail? 

          Therefore, we can combine: 

            if (total_current - maxA) >= X:
                candidate1 = 0   # because we can use s=0

            candidate2 = None
            # For the linear part: we require s in [minA, maxA] and s>=needed_s.
            needed_s = X - (total_current - minA - maxA)
            # But note: needed_s might be fractional? but all integers -> so integer.
            # We require s to be at least needed_s and at least minA, and at most maxA? 
            # Actually, we can set candidate2 = max(minA, needed_s) but only if max(minA, needed_s) <= maxA.

            if needed_s <= maxA:
                candidate2 = max(minA, needed_s)

            # For the third interval: we don't need to check because the value at s>=maxA is total_current - minA. 
            # But if we can get that value by setting s=maxA (which is in the linear part) then we would have already considered candidate2? 
            # However, if needed_s>maxA, then candidate2 is not set. Then we check: 
            if candidate2 is None:
                # Then we didn't get a candidate from the linear part. Then we check the constant for the third interval: total_current - minA >= X?
                if total_current - minA >= X:
                    candidate3 = maxA   # because at s=maxA we get that value? but wait: s=maxA is in the linear part, and we already considered the linear part? 
                    # But note: we set candidate2 only if needed_s<=maxA. If needed_s>maxA, we skip candidate2. 
                    # However, at s=maxA, we get total_current - minA. And we are now checking if total_current - minA>=X, then we can use s=maxA? 
                    candidate3 = maxA   # because s=maxA is the smallest s that gives the constant? but note: we can also get that constant by any s>=maxA, but the smallest is maxA.

            Then we take the minimum candidate from candidate1 (if set) and candidate2 (if set) and candidate3 (if set).

          However, wait: we already argued that if needed_s>maxA, then total_current - minA = (total_current - minA) < X? 
          Why? 
                needed_s = X - (total_current - minA - maxA) > maxA
                => X > (total_current - minA - maxA) + maxA = total_current - minA.

          So total_current - minA < X. Therefore, the condition total_current - minA>=X is false.

          So we don't need to check the third interval? 

          Therefore, the algorithm:

            total = sum(A)
            min_val = min(A)
            max_val = max(A)

            # Case 1: s in [0, min_val) -> constant: total - max_val
            if total - max_val >= X:
                return 0

            # Case 2: s in [min_val, max_val] -> f(s) = total + s - min_val - max_val
            # We require: total + s - min_val - max_val >= X  -> s >= X - (total - min_val - max_val)
            needed = X - (total - min_val - max_val)
            if needed <= max_val:
                candidate2 = max(min_val, needed)
                # candidate2 is a valid candidate because we can choose s = candidate2 and it is in [min_val, max_val] and candidate2<=max_val (by our if condition) and candidate2>=min_val (by the max).
                # But note: we must also ensure candidate2 is at most 100? but max_val is at most 100, so candidate2 is at most max_val (<=100). Also, candidate2 is at least min_val (>=0) so it's in [0,100].
                # However, we must also check: what if needed is negative? then candidate2 = min_val, which is valid.

            else:
                candidate2 = None

            # If candidate2 is set, then we have a candidate in the linear part.

            # Case 3: s in (max_val, 100] -> constant: total - min_val
            # But note: we can also get this constant at s = max_val (which is in the linear part) so we don't need to consider s above max_val? 
            # However, we already considered s = max_val in the linear part? 
            # But if needed <= max_val, then we set candidate2. And if candidate2 is set, then we have a candidate. 
            # If needed>max_val, then the linear part doesn't yield a solution? and then the constant from the linear part at s=max_val is total-min_val, but we have:
            #   needed>max_val -> X > total - min_val, so total-min_val < X -> so the constant doesn't meet the requirement.

            # Therefore, if candidate2 is not None, then we return candidate2.

            # But wait: we also have case1 which returns 0. And if case1 fails, then we have candidate2? 

            # However, what if candidate2 is not set? Then we have no solution? 

            if candidate2 is not None:
                return candidate2

            # Now, if we are here, then case1 and case2 did not yield a candidate.

            # Check case3: 
            if total - min_val >= X:
                # But we know: if we are here, then candidate2 was None -> meaning needed>max_val -> which implies total-min_val < X? 
                # So this condition should be false? 
                # Therefore, we don't need to check? 
                # Actually, by our earlier reasoning, if needed>max_val then total-min_val < X, so this condition will be false.
                # But what if there is an error in the reasoning? 

                # Alternatively, we can note: the value we get for any s>=max_val is total-min_val. 
                # So if total-min_val>=X, then we can choose s = max_val? But wait: we already considered s=max_val in the linear part? 
                # And in the linear part, we had: 
                #       needed = X - (total - min_val - max_val)
                #       and we set candidate2 = max(min_val, needed) only if needed<=max_val.
                # But if total-min_val>=X, then:
                #       total-min_val >= X
                #       => X <= total-min_val
                #       => needed = X - (total - min_val - max_val) 
                #                 = X - total + min_val + max_val
                #                 <= (total-min_val) - total + min_val + max_val = max_val
                #       so needed <= max_val -> then candidate2 would have been set? 
                # Therefore, if total-min_val>=X, then needed<=max_val, so candidate2 would have been set and we would have returned it.

                # So we should never get here? 

                # Therefore, we don't need this check? 

                # But to be safe, let us reexamine: 
                #   We have: 
                #       needed = X - (total - min_val - max_val)
                #   and if total-min_val>=X, then:
                #       X <= total - min_val
                #       => needed = X - (total - min_val - max_val) 
                #                 <= (total-min_val) - (total - min_val - max_val) = max_val.
                #   So needed<=max_val -> so candidate2 is set.

            # Therefore, we can conclude: if we get to the end (without returning), then there is no solution? 

            return -1

  But wait: what if we have candidate2 set to a value that is above 100? 
        candidate2 = max(min_val, needed)
        and we have needed<=max_val, and max_val<=100, so candidate2<=max_val<=100.

  So it's safe.

  However, there is one more possibility: the constant in the first interval (s in [0,min_val)) is total - max_val. We already checked that and if it was >=X we returned 0.

  Therefore, the algorithm:

      total = sum(A)
      minA = min(A)
      maxA = max(A)

      if total - maxA >= X:
          return 0

      # Compute the requirement for the linear part
      needed_s = X - (total - minA - maxA)

      # If needed_s is greater than maxA, then the linear part cannot help (even at s=maxA we get total-minA, which we know is <X because needed_s>maxA implies X>total-minA) so skip.
      if needed_s <= maxA:
          candidate = max(minA, needed_s)
          # Since needed_s<=maxA, then candidate = max(minA, needed_s) is at most maxA (<=100) and at least minA (>=0) -> valid.
          return candidate

      # Now, if we are here, then needed_s > maxA -> which implies total-minA < X.
      # But also, we have the constant in the third interval: total-minA, which is <X -> so no solution in the third interval.

      # Therefore, we return -1.

  But wait: what about the third interval? The third interval gives total-minA. And we just said that if needed_s>maxA then total-minA < X -> so we cannot achieve.

  Therefore, we can simply:

      if total - maxA >= X:
          return 0
      needed_s = X - (total - minA - maxA)
      if needed_s <= maxA:
          return max(minA, needed_s)
      else:
          # Then we know: total-minA < X (because needed_s>maxA implies X> total-minA) 
          # But what if the constant in the third interval is >=X? 
          # However, we just argued it's not? 
          # But to be safe, we check: if total-minA >= X, then we can set s = maxA? 
          # But note: if total-minA>=X, then needed_s = X - (total-minA-maxA) <= maxA (as argued above) -> so we would have already returned from the linear part. 
          # Therefore, we return -1.

  However, let me test with Example 3:
        Input: "5 200\n0 0 99 99"
        N=5, X=200, A=[0,0,99,99]
        total = 0+0+99+99 = 198
        minA = 0
        maxA = 99

        Step1: total - maxA = 198-99 = 99 -> 99>=200? no.
        Step2: needed_s = 200 - (198 - 0 - 99) = 200 - (99) = 101.
               101<=maxA? maxA=99 -> no -> so we go to else and return -1.

  That matches.

  Example 2: 
        Input: "3 100\n100 100"
        total = 200
        minA = 100
        maxA = 100
        Step1: total - maxA = 200-100=100>=100 -> true -> return 0? 
        But the example output is 0? 

        However, wait: the example input: "3 100\n100 100" -> so we return 0? 

        But the example says output 0.

  Example 1: 
        "5 180\n40 60 80 50"
        total = 40+60+80+50 = 230
        minA = min(40,60,80,50)=40
        maxA = 80
        Step1: total - maxA = 230-80=150 -> 150>=180? no.
        Step2: needed_s = 180 - (230-40-80) = 180 - (110) = 70.
               70<=maxA? maxA=80 -> yes -> candidate = max(40,70)=70 -> return 70.

  Example 4: 
        "10 480\n59 98 88 54 70 24 8 94 46"
        total = 59+98+88+54+70+24+8+94+46 = 
          59+98=157, 157+88=245, 245+54=299, 299+70=369, 369+24=393, 393+8=401, 401+94=495, 495+46=541
        minA = min(59,98,88,54,70,24,8,94,46) = 8
        maxA = 98
        Step1: total - maxA = 541-98=443 -> 443>=480? no.
        Step2: needed_s = 480 - (541-8-98) = 480 - (541-106) = 480-435=45.
                45<=98 -> yes -> candidate = max(8,45)=45.

        Output:45 -> matches.

  But wait: what if the needed_s is negative? 
        Example: 
            N=5, X=100, A=[90,90,90,90] -> total=360, minA=90, maxA=90.
            Step1: total - maxA = 360-90=270>=100 -> true -> return 0.

        That's correct: if we get 0 in the last round, then the scores: [0,90,90,90,90] -> remove min=0 and max=90 -> then the sum of the middle three: 90+90+90 = 270 -> which is >=100. So we can get away with 0.

  However, consider: 
        Example: 
            N=3, X=0, A=[0,0] -> total=0, minA=0, maxA=0.
            Step1: total - maxA = 0-0=0>=0 -> true -> return 0.

        But if we set s=0, then the scores: [0,0,0] -> remove min and max: both are 0, so the middle is [0] -> sum=0. So that's correct.

  Another test: 
        N=3, X=0, A=[1,1] -> total=2, minA=1, maxA=1.
        Step1: total - maxA = 2-1=1>=0 -> true -> return 0.

        Then the scores: [1,1,0] -> sorted: [0,1,1] -> remove min=0 and max=1 -> leaves [1] -> sum=1>=0 -> correct.

  What if X is 0 and we have negative scores? But the problem says scores between 0 and 100, inclusive.

  Therefore, the solution:

      total = sum(A)
      minA = min(A)
      maxA = max(A)

      if total - maxA >= X:
          return 0

      needed_s = X - (total - minA - maxA)
      if needed_s <= maxA:
          return max(minA, needed_s)

      return -1

  But wait: what if needed_s is negative? 
        Then we return max(minA, needed_s) = minA? 
        Example: 
            N=5, X=50, A=[100,100,100,100] -> total=400, minA=100, maxA=100.
            Step1: total - maxA = 300>=50 -> true -> return 0? 
            Actually, that's correct: we can get 0, then scores: [0,100,100,100,100] -> remove 0 and 100 -> middle: 100+100+100=300>=50.

        So we never get to step2 in this case.

  Another example that goes to step2 with negative needed_s: 
        Suppose we have: 
            N=5, X=50, A=[50,50,50,50] -> total=200, minA=50, maxA=50.
            Step1: total - maxA = 200-50=150>=50 -> true -> return 0.

        So we don't get negative needed_s in step2? 

        How about: 
            N=5, X=150, A=[50,50,50,50] -> total=200, minA=50, maxA=50.
            Step1: 200-50=150>=150 -> true -> return 0.

        So we don't need step2? 

        How about: 
            N=5, X=151, A=[50,50,50,50] -> 
                Step1: 150>=151? false.
                Step2: needed_s = 151 - (200-50-50) = 151 - (100)=51.
                Then candidate = max(50,51)=51 -> return 51.

        Then the scores: [50,50,50,50,51] -> sorted: [50,50,50,50,51] -> remove min=50 and max=51 -> leaves [50,50,50] -> sum=150 -> but we wanted 151? 

        Why? 

        We computed: 
            total = 200+51? no, wait: we have the last round score is 51? then total_sum = 200+51=251.
            min_total = min(50,51)=50
            max_total = max(50,51)=51
            final_grade = 251 - 50 - 51 = 150.

        But we expected 150? 

        How do we get 151? 

        Actually, the formula for the linear part: 
            f(s) = total_current + s - minA - maxA = 200 + s - 50 - 50 = 100+s.
            We set s=51 -> 100+51=151.

        But the actual computation: 
            scores: [50,50,50,50,51] -> sorted: [50,50,50,50,51]
            remove one smallest and one largest: 
                smallest: 50 (one of them) and largest:51 -> leaves [50,50,50] -> sum=150.

        Why the discrepancy? 

        The issue: the problem says the final grade is the sum of the N-2 scores excluding the highest and lowest. 
        But note: when we remove the highest and lowest, we remove one occurrence of the lowest and one occurrence of the highest. 
        In our list, the lowest is 50 (appearing 4 times) and the highest is 51 (appearing once). 
        So we remove one 50 and the 51. 

        The remaining: three 50's -> 150.

        But our formula for the linear part: 
            We assumed: 
                if s in [minA, maxA] -> then min_total = minA and max_total = maxA? 
            But here, minA=50, maxA=50 (from the first four rounds). Then we added s=51. 
            Now, min_total = min(50,51)=50, max_total = max(50,51)=51.
            Then total_sum = 200+51=251.
            Then final_grade = 251 - 50 - 51 = 150.

        The linear part formula: 
            f(s) = total_current + s - minA - maxA = 200 + 51 - 50 - 50 = 151.

        Why the difference? 

        The problem: the minA and maxA we are using are the min and max of the first N-1 rounds? 
        But in the linear part, we assumed that the min_total = minA (the min of the first rounds) and max_total = maxA (the max of the first rounds). 
        However, when we add s, if s is between minA and maxA, then the min_total is minA and the max_total is maxA? 
        That is true only if minA<=s<=maxA. But note: in the example, s=51 is greater than maxA (which was 50). 
        Therefore, we are not in the linear part? We are in the third case? 

        But our condition for the linear part: [minA, maxA] -> and minA=50, maxA=50, and s=51 is not in [50,50]? 

        So we should have used the third case: 
            f(s) = total_current - minA = 200 - 50 = 150.

        Therefore, our algorithm for the example: 
            Step1: 200-50=150>=151? false.
            Then we compute needed_s = 151 - (200-50-50)=151-100=51.
            Then we check: 51<=maxA? maxA=50 -> false -> return -1? 

        But we know: if we set s=51, we get 150 which is <151. 

        What if we set s=100? 
            total_sum = 200+100=300
            min_total=50, max_total=100
            final_grade = 300-50-100=150.

        So we cannot achieve 151.

        Therefore, the algorithm returns -1 for (5,151,[50,50,50,50])? 

        That is correct.

        But then why did the linear part formula yield 151? 

        The linear part formula is only valid when s is between minA and maxA (inclusive). In our example, the minA and maxA of the existing array are both 50. So the linear part is only for s in [50,50] -> i.e., s=50. 
        Then f(50)=200+50-50-50=150.

        So the linear part formula is not valid for s=51? 

        Therefore, the algorithm is correct.

  One more test: 
        N=3, X=100, A=[100,50] 
          total = 150
          minA = 50, maxA=100
          Step1: total - maxA = 150-100=50>=100? no.
          Step2: needed_s = 100 - (150-50-100)=100-0=100.
                 100<=maxA? maxA=100 -> yes -> candidate = max(50,100)=100.

          Then the scores: [100,50,100] -> sorted: [50,100,100] -> remove min=50 and max=100 -> leaves [100] -> sum=100 -> correct.

  Therefore, we write the code accordingly.

  Code:

      total = sum(A)
      minA = min(A)
      maxA = max(A)

      if total - maxA >= X:
          return 0

      # Compute the expression for the linear part: we require s in [minA, maxA] and s>= (X - (total - minA - maxA))
      needed_s = X - (total - minA - maxA)

      # But note: we must check that needed_s is not greater than maxA
      if needed_s <= maxA:
          # We can choose s in [minA, maxA]. The minimal s that satisfies the condition is max(minA, needed_s)
          return max(minA, needed_s)

      else:
          return -1

  However, wait: what if the constant in the first interval (total - maxA) is not enough, but the linear part fails (needed_s>maxA) and the third interval constant (total-minA) is >=X? 
        But we argued that if needed_s>maxA, then total-minA < X. 
        Why? 
            needed_s = X - (total - minA - maxA) > maxA
            => X > (total - minA - maxA) + maxA = total - minA.

        So total-minA < X -> so the third interval doesn't work.

  Therefore, the above code is complete.

  Let me test with a hypothetical: 
        Suppose: 
            A = [0, 100] (so N=3, two rounds done)
            X = 100
            total = 100
            minA = 0
            maxA = 100

            Step1: total - maxA = 100-100=0>=100? false.
            Step2: needed_s = 100 - (100-0-100) = 100 - (0) = 100.
                    100<=100 -> true -> candidate = max(0,100)=100.

            Then the scores: [0,100,100] -> final_grade = 100 -> correct.

        But note: we can also get 100 by setting s=100? 

        However, what if we set s=50? 
            Step1: 0>=100? false.
            needed_s = 100 - (100-0-100)=100-0=100.
            candidate = 100 -> so we return 100.

        But what if we set s=0? 
            Then the scores: [0,100,0] -> sorted: [0,0,100] -> remove one 0 and 100 -> leaves [0] -> sum=0 -> not 100.

        s=50: 
            scores: [0,100,50] -> sorted: [0,50,100] -> remove 0 and 100 -> leaves [50] -> sum=50.

        s=100: 
            scores: [0,100,100] -> sorted: [0,100,100] -> remove 0 and one 100 -> leaves [100] -> sum=100.

        So the minimal s that works is 100.

  Therefore, the code is correct.

  But note: what if the minimal s that we return is greater than 100? 
        We have: 
            candidate = max(minA, needed_s)
            and we only enter the if block if needed_s<=maxA. 
            But maxA is the maximum of the given A, which is at most 100. 
            Therefore, candidate is at most 100.

  Also, if the candidate is set, it is at least minA (which is at least 0) and at most 100.

  So we are safe.

  Therefore, we write the function accordingly.

  Code:

      def solve(self, N: int, X: int, A: List[int]) -> int:
          total = sum(A)
          minA = min(A)
          maxA = max(A)

          # Check if we can use the first interval (s in [0, minA)): the constant value is total - maxA
          if total - maxA >= X:
              return 0

          # Calculate the requirement for the linear part (s in [minA, maxA])
          # Formula: f(s) = total + s - minA - maxA >= X  => s >= X - (total - minA - maxA)
          needed_s = X - (total - minA - maxA)

          # If needed_s is within [minA, maxA] (but note: we are going to cap it to minA at least) and also needed_s<=maxA, then we can use that candidate.
          if needed_s <= maxA:
              # We return the smallest s in [minA, maxA] that is at least needed_s: that is max(minA, needed_s)
              return max(minA, needed_s)

          else:
              return -1

  Let's test with the provided examples.

  Example 1: 
        N=5, X=180, A=[40,60,80,50]
        total = 230
        minA = 40, maxA=80
        Step1: 230-80=150>=180? no.
        needed_s = 180 - (230-40-80) = 180 - (110)=70
        70<=80 -> true -> return max(40,70)=70.

  Example 2: 
        N=3, X=100, A=[100,100]
        total=200
        minA=100, maxA=100
        Step1: 200-100=100>=100 -> true -> return 0.

  Example 3: 
        N=5, X=200, A=[0,0,99,99]
        total=198
        minA=0, maxA=99
        Step1: 198-99=99>=200? no.
        needed_s=200 - (198-0-99)=200-99=101
        101<=99? false -> return -1.

  Example 4: 
        N=10, X=480, A=[59,98,88,54,70,24,8,94,46]
        total=59+98+88+54+70+24+8+94+46 = 541
        minA=8, maxA=98
        Step1: 541-98=443>=480? no.
        needed_s=480 - (541-8-98)=480-435=45
        45<=98 -> true -> return max(8,45)=45.

  All match.

  However, note: the problem says "If your final grade will never be X or higher no matter what score you earn in round N, print -1 instead."

  And we return -1 only in the else branch.

  Therefore, we have the solution.

  But wait: what if the linear part condition is satisfied by a value that is an integer, but we are returning a non-integer? 
        Actually, needed_s is computed as: 
            needed_s = X - (total - minA - maxA)
        and X, total, minA, maxA are integers -> so needed_s is an integer.

  And we return an integer.

  So we are good.

  Code:

      from typing import List

      class Solution:
          def solve(self, N: int, X: int, A: List[int]) -> int:
              total = sum(A)
              minA = min(A)
              maxA = max(A)
              
              # Check the first segment: [0, minA)
              if total - maxA >= X:
                  return 0
                  
              # Calculate the needed score for the linear segment [minA, maxA]
              needed_s = X - (total - minA - maxA)
              if needed_s <= maxA:
                  return max(minA, needed_s)
              else:
                  return -1

  We'll run the provided examples to be sure.

  Example 2: 
      Input: "3 100\n100 100" -> returns 0 -> correct.

  Example 1: returns 70 -> correct.

  Example 3: returns -1 -> correct.

  Example 4: returns 45 -> correct.

  This matches.

  However, note: what if the current total - maxA is already >=X, but the minimal s we can choose is 0? 
        The problem says: "minimum score you must earn in round N", and 0 is the minimum possible.

  Therefore, we return 0 in that case.

  We are done.

## Final Solution

### Step 1: Problem Understanding and Modeling

The problem involves calculating the minimum score needed in the final round (round N) to achieve a target final grade (X) or higher. The final grade is computed by summing all scores from N rounds, excluding the highest and lowest scores. 

**Key Insights:**
1. **Final Grade Calculation:** After sorting all scores (including the final round), the final grade is the sum of scores from index 1 to N-2 (0-indexed). This is equivalent to the total sum of all scores minus the minimum and maximum scores.
2. **Variable Final Round Score:** The score in round N (denoted as `s`) can be any integer between 0 and 100.
3. **Segmented Function:** The final grade `f(s)` behaves differently based on `s` relative to the current minimum (`minA`) and maximum (`maxA`) scores from the first N-1 rounds:
   - **Case 1 (s < minA):** The final grade is `total_current - maxA`.
   - **Case 2 (minA ≤ s ≤ maxA):** The final grade is `total_current + s - minA - maxA`.
   - **Case 3 (s > maxA):** The final grade is `total_current - minA`.
4. **Objective:** Find the smallest `s` in [0, 100] such that `f(s) >= X`. If impossible, return -1.

**Constraints:**
- `3 <= N <= 100`
- `0 <= X <= 100*(N-2)`
- Scores are integers between 0 and 100.

### Step 2: Multiple Solution Exploration

1. **Brute Force Solution:**
   - **Idea:** Iterate all possible `s` (0 to 100), compute `f(s)` for each, and find the smallest `s` where `f(s) >= X`.
   - **Time Complexity:** O(100) = O(1) per test case, feasible since 100 is small.
   - **Space Complexity:** O(1), no extra space beyond input.
   - **Limitations:** While efficient due to small constraint (s in [0,100]), it's not optimized for larger ranges (though not needed here).

2. **Optimized Solution:**
   - **Idea:** Leverage the segmented nature of `f(s)` to compute the answer without iterating all possible `s`:
     - **Case 1:** If `total_current - maxA >= X`, `s=0` suffices.
     - **Case 2:** For `s` in [minA, maxA], solve `total_current + s - minA - maxA >= X` to find minimal `s`.
     - **Case 3:** If `total_current - minA >= X`, `s = maxA` works (since `s > maxA` yields the same grade).
     - If none of the cases yield a solution, return -1.
   - **Time Complexity:** O(N) for summing and finding min/max of the list.
   - **Space Complexity:** O(1), no extra space.
   - **Optimization:** Direct computation avoids unnecessary iterations.

3. **Space-Optimized Solution:**
   - **Idea:** Same as optimized solution, as it already uses minimal space (only a few variables).
   - **Time Complexity:** O(N) for processing the list.
   - **Space Complexity:** O(1).

4. **Special Technique Solution:**
   - **Idea:** Use mathematical properties to derive `s` directly from the equations for each segment.
   - **Time Complexity:** O(N) for summing and min/max.
   - **Space Complexity:** O(1).
   - **Applicability:** Efficient and direct, suitable given constraints.

### Step 3: Optimal Solution Selection and Implementation

**Selection Rationale:**
- The optimized solution efficiently computes the result in O(N) time with O(1) space, which is optimal for the constraints (N ≤ 100). It avoids brute-force iteration by leveraging mathematical insights about `f(s)`'s segments.

**Implementation:**
```python
from typing import List

class Solution:
    def solve(self, N: int, X: int, A: List[int]) -> int:
        total = sum(A)
        minA = min(A)
        maxA = max(A)
        
        # Case 1: s in [0, minA) -> constant: total - maxA
        if total - maxA >= X:
            return 0
        
        # Case 2: s in [minA, maxA] -> linear: total + s - minA - maxA
        # Solve: total + s - minA - maxA >= X -> s >= X - (total - minA - maxA)
        needed_s = X - (total - minA - maxA)
        if needed_s <= maxA:
            # s must be at least minA and at least needed_s
            return max(minA, needed_s)
        
        # Case 3: Not feasible since needed_s > maxA implies total - minA < X
        return -1
```

**Key Optimization Points:**
1. **Direct Calculation:** Uses arithmetic to compute `needed_s` for the linear segment instead of iterating.
2. **Early Termination:** Checks the simplest case (s=0) first.
3. **Mathematical Insight:** Uses derived expressions for each segment to avoid unnecessary computations.

### Step 4: Verification and Summary

**Verification with Examples:**
1. **Example 1:** 
   - Input: `N=5, X=180, A=[40,60,80,50]`
   - `total = 230, minA=40, maxA=80`
   - Case 1: `230-80=150 < 180` → skip.
   - `needed_s = 180 - (230-40-80)=70`, `70<=80` → return `max(40,70)=70`. Correct.

2. **Example 2:**
   - Input: `N=3, X=100, A=[100,100]`
   - `total=200, minA=100, maxA=100`
   - Case 1: `200-100=100>=100` → return `0`. Correct.

3. **Example 3:**
   - Input: `N=5, X=200, A=[0,0,99,99]`
   - `total=198, minA=0, maxA=99`
   - Case 1: `198-99=99<200` → skip.
   - `needed_s=200-(198-0-99)=101>99` → return `-1`. Correct.

4. **Example 4:**
   - Input: `N=10, X=480, A=[59,98,88,54,70,24,8,94,46]`
   - `total=541, minA=8, maxA=98`
   - Case 1: `541-98=443<480` → skip.
   - `needed_s=480-(541-8-98)=45<=98` → return `max(8,45)=45`. Correct.

**Summary:**
- The problem is solved by analyzing the segmented behavior of the final grade function.
- The optimal solution efficiently computes the minimal `s` in O(N) time by leveraging arithmetic on the current scores.
- Edge cases (like `s=0` being sufficient or no solution possible) are handled directly through the derived conditions.

**Further Optimization:**
- Given the constraints (N ≤ 100), further optimization isn't necessary. The solution is already optimal in time and space.