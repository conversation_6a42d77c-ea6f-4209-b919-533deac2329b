# Solution for 901_A.md

Generated at: 2025-07-22 06:02:21
Model: deepseek-reasoner

## Reasoning Process

Okay, I need to solve this problem about determining if a given sequence of vertices per level in a rooted tree corresponds to only one tree (output "perfect") or multiple non-isomorphic trees (output "ambiguous" with two different tree structures). 

First, I'll try to understand the problem. We are given the height h of a tree, and a sequence a[0] to a[h] where a[i] is the number of vertices at level i. The root is at level 0, so a[0] is always 1 (since it's the root). The total number of vertices is the sum of a[i] for i from 0 to h, and it's guaranteed that this sum is at most 200,000. 

Two trees are isomorphic if we can renumber the vertices (keeping the root fixed) to make them identical. So, if there's only one tree up to isomorphism, we output "perfect". Otherwise, we output two different trees that have the same level sequence but are non-isomorphic.

Let me think about when we have only one tree. For example, in the first sample: h=2, a=[1,1,1]. That means one root, one child, and one grandchild. So the tree is a chain. There's only one way to build it. Similarly, if at every level, there's only one node, then it's always a chain and only one tree.

Now, the second sample: h=2, a=[1,2,2]. So, root (level0) has two children (level1), and each of those two has one child (so two at level2). How can we build this? 

Option 1: Root (vertex 0) has two children (vertex1 and vertex2). Then, vertex1 has two children (vertex3 and vertex4). But wait, that would give level2 having two children for each of the two level1 nodes? That would be 4 vertices at level2, but we need only two. So that's incorrect.

Wait, no. Actually, each node at level1 must have one child. So the root (0) has two children: 1 and 2. Then, vertex1 has one child, say 3, and vertex2 has one child, say 4. So level2 has two vertices: 3 and 4. 

But how can we have two different trees? Since the tree is rooted, and we can renumber the vertices, but the structure is fixed: the root has two children, each of which has one child. That seems symmetric. So why is the output ambiguous?

Wait, looking at the example output:

First tree: "0 1 1 3 3" -> meaning:
Vertex0: root, parent 0? Or wait, the k-th integer is the parent of vertex k. So for k=0: parent is 0? That might be the root. Then vertex1's parent is 0, vertex2's parent is 1? Wait, no:

Let me parse: 
- Vertex0: root, parent is 0? Or is it 0 meaning no parent? The problem says "or zero if the k-th vertex is the root". So for root, parent is 0.

So:
Vertex0: root -> parent 0
Vertex1: parent 0 (so root's child)
Vertex2: parent 0 (so root's another child? But wait, the sequence has a0=1, a1=2, so two children for root. Then vertex3: parent 3? That doesn't make sense. Wait, the output is "0 1 1 3 3". So:

- Vertex0: parent 0 (root)
- Vertex1: parent 0 (so child of root)
- Vertex2: parent 1 (so child of vertex1) — but wait, that would be only one child for root? Then vertex3: parent 3? That can't be.

Wait, the output has 5 integers: for k=0,1,2,3,4. So vertices 0 to 4. The sequence: a0=1 (vertex0), a1=2 (vertex1 and vertex2), a2=2 (vertex3 and vertex4). So:

First tree: 
0: root -> parent 0
1: parent 0 -> so child of 0
2: parent 1? That would be child of 1, so vertex2 is at level2? Then vertex3: parent 3? That doesn't make sense. 

Wait, the example output: 
"0 1 1 3 3" 
and 
"0 1 1 3 2"

Let me map:

For the first tree:
Vertex0: parent 0 (root)
Vertex1: parent 0 -> so connected to root
Vertex2: parent 1 -> connected to vertex1? Then that would make vertex2 at level2? Then vertex3: parent 3? That must be a mistake. 

Looking at the example input: h=2 and sequence 1 2 2, so total vertices = 1+2+2=5. The vertices are 0,1,2,3,4.

The first output: "0 1 1 3 3" means:
- Vertex0: parent 0 (root)
- Vertex1: parent 0 (so child of root) -> level1
- Vertex2: parent 1 (child of vertex1) -> level2? Then at level2 we have vertex2? But we need two vertices at level2. Then vertex3: parent 3? That would be a self-loop? Impossible.

Wait, I think I'm miscounting the indices. The k-th integer is the parent of vertex k. So the list has index 0 for vertex0, index1 for vertex1, index2 for vertex2, index3 for vertex3, index4 for vertex4.

So:
- Parent of vertex0: 0 (root)
- Parent of vertex1: 1? That would be vertex1's parent is vertex1? That's a self-loop? Not possible.

Looking back at the example: 
Input: 
2
1 2 2

Output:
ambiguous
0 1 1 3 3
0 1 1 3 2

Wait, the first line: 0 1 1 3 3
- Vertex0: parent 0 -> root
- Vertex1: parent 0 -> so root is parent? So vertex1 is at level1.
- Vertex2: parent 1 -> so vertex1 is parent? So vertex2 is at level2 (child of vertex1)
- Vertex3: parent 3? That's a problem.

But then vertex3: parent 3? That would be vertex3's parent is itself? That's invalid. Unless... wait, maybe the vertices are numbered in order of levels. So the root is 0. Then the two level1 vertices are 1 and 2. Then the two level2 vertices are 3 and 4. So the output should have five numbers: for vertex0, vertex1, vertex2, vertex3, vertex4.

So the output "0 1 1 3 3" must be:
- Vertex0: parent 0
- Vertex1: parent 0? That would be root is parent -> correct for one level1 node
- Vertex2: parent 1? That would be vertex1 is parent -> so this vertex2 is child of vertex1, so at level2.
- Vertex3: parent 3? That doesn't make sense.

Wait, the output has five numbers: the first number is for vertex0, second for vertex1, third for vertex2, fourth for vertex3, fifth for vertex4.

So for the first tree:
- Vertex0: parent 0
- Vertex1: parent 0 -> so root's child, level1
- Vertex2: parent 1 -> child of vertex1, so level2
- Vertex3: parent 3 -> this is a problem because vertex3 doesn't have a valid parent? Or is it that the parent index refers to the vertex id? So parent of vertex3 is vertex3? That's a cycle.

But the example output is "0 1 1 3 3". So for vertex3 and vertex4, both have parent 3? That's impossible because vertex3 is the parent of both? But then vertex3 is at which level? 

Alternatively, maybe the parent of vertex3 is vertex0? But that would be 0, not 3. 

I think there's a misunderstanding. The example input has a0=1 (root), a1=2 (two nodes at level1: let's say v1 and v2), a2=2 (two nodes at level2: v3 and v4). 

In the first tree representation: 
The parent list: 
v0: 0 (root)
v1: 1? That can't be. 

Wait, the example output is "0 1 1 3 3". Perhaps the indexing is off. The problem says: "the k-th integer is the parent of vertex k". So:
- For k=0: parent of vertex0 is 0 (correct, root)
- For k=1: parent of vertex1 is 1? That would be self-parent, which is invalid.

But that doesn't make sense. Looking back at the example: the output is "0 1 1 3 3" for the first tree. Then the second tree is "0 1 1 3 2". 

I think I need to reinterpret. The root is vertex0. Then the next two vertices (vertex1 and vertex2) are at level1. Then the next two (vertex3 and vertex4) are at level2.

So the parent of vertex1 and vertex2 must be 0. Then the parent of vertex3 and vertex4 must be either 1 or 2.

In the first tree: 
- Vertex1: parent 0 -> correct
- Vertex2: parent 0 -> correct? Then the parent of vertex3 is 1? But the output says for vertex3: 3? 

Wait, the output has five numbers: the first is for vertex0: 0, second for vertex1: 1? That can't be. 

Alternatively, maybe the output order is: parent of vertex0, parent of vertex1, parent of vertex2, parent of vertex3, parent of vertex4.

So:
- Parent of vertex0: 0 -> root
- Parent of vertex1: 1 -> that's invalid.

I see the problem: the example output might have a typo? Or I'm missing something. 

Looking at the sample: 
Input: 
2
1 2 2

Total vertices = 5. The output has five integers: "0 1 1 3 3" and "0 1 1 3 2". 

But the root is one vertex (vertex0). Then two at level1: so vertices 1 and 2. Then two at level2: vertices 3 and 4.

So for the first tree:
- Vertex0: parent 0
- Vertex1: parent 0? But the second integer is 1, which would be parent of vertex1 is 1? That's wrong.

Alternatively, perhaps the parent of vertex1 is 0? Then the second integer should be 0? But it's 1. 

Wait, I recall: in the example output, the first tree is "0 1 1 3 3". So:
- Vertex0: parent 0
- Vertex1: parent 1? That's invalid.

Unless... the root is vertex0, and the next vertex is vertex1 which is a child of root (so parent 0). But the output has the second integer as 1, which would imply that the parent of vertex1 is vertex1? That's a self-loop. 

But the problem says the root has parent 0. So the root is vertex0. Then the children of the root are vertices1 and 2. So the parent of vertex1 should be 0, and parent of vertex2 should be 0? Then why does the output say for vertex1: 1 and for vertex2: 1? 

I think I see the issue: the example output might be: 
First tree: "0 1 1 3 3" -> meaning:
- Vertex0: parent 0
- Vertex1: parent 0? But the second integer is 1, not 0. 

Alternatively, the output format might be: the first number is the parent of vertex1? But the problem says: the k-th integer is the parent of vertex k. So k starts at 0.

After re-examining the example: 
In the sample input, h=2, a=[1,2,2]. So levels: level0: 1 node (vertex0), level1: 2 nodes (vertex1 and vertex2), level2: 2 nodes (vertex3 and vertex4).

The sample output:
First tree: "0 1 1 3 3"
Second tree: "0 1 1 3 2"

So:
- Vertex0: parent 0 (root) -> correct
- Vertex1: parent 1? -> invalid
This is confusing.

But wait, the problem statement says: "in one line print $\sum_{i=0}^{h} a_i$ integers, where the $k$-th integer is the parent of vertex $k$ (or zero if the $k$-th vertex is the root)."

So for vertex0 (k=0): parent is 0 -> correct.
For vertex1 (k=1): parent is 1? That's a problem.

But then I noticed: in the example output, the first tree string is "0 1 1 3 3", which has five integers: [0,1,1,3,3]. 

Perhaps the vertices are not labeled from 0? The problem says "the k-th integer" for k from 0 to total_vertices-1.

But then the root is vertex0, and it must have parent 0. Then vertex1 is the first child, which must have a parent that is 0. So the second integer should be 0? But it's 1.

Unless the parent of vertex1 is vertex0, but they wrote 1 by mistake? 

Alternatively, maybe the parent of vertex1 is vertex0, which is index0, so they wrote 0? But the output has 1 for vertex1.

I think there might be a misunderstanding in the example. Let me check the problem statement again: Example 2 input and output.

Input:
2
1 2 2

Output:
ambiguous
0 1 1 3 3
0 1 1 3 2

This must be correct, so I must be interpreting it wrong. 

Another possibility: the vertices are labeled in the order of appearance. The root is vertex0. Then the two children of the root are vertex1 and vertex2. Then, for the first tree, the two level2 vertices are both children of vertex1? But then we need to assign two children to vertex1. But then vertex3 and vertex4 would be children of vertex1. So:

- Vertex0: parent 0
- Vertex1: parent 0
- Vertex2: parent 0? But wait, then vertex2 has no children? But we need two level2 nodes.

Alternatively, the level2 nodes are children of the level1 nodes. So one level1 node can have both children? Then we have:

Tree 1:
- Root (0) has two children: 1 and 2.
- Then vertex1 has two children: 3 and 4.

But then the parent array would be:
vertex0: 0
vertex1: 0
vertex2: 0
vertex3: 1
vertex4: 1

But that would be "0 0 0 1 1" — not matching.

Alternatively, Tree 1:
- Root (0) has one child: vertex1.
- Then vertex1 has two children: vertex2 and vertex3.
- Then vertex2 has no child, but we need two at level2. 

Wait, the levels: 
level0: vertex0
level1: vertex1 and vertex2? But if root has only one child, then level1 would have only one vertex. But we need two.

So the root must have two children: vertex1 and vertex2. Then, at level2, we have two vertices. Each of the level1 nodes must have one child. So:

- Vertex3 is child of vertex1
- Vertex4 is child of vertex2

Then the parent array would be:
vertex0:0
vertex1:0
vertex2:0
vertex3:1
vertex4:2

But the example output is "0 1 1 3 3" for the first tree and "0 1 1 3 2" for the second. 

Wait, if we have:
First tree: 
vertex0:0
vertex1:0 -> but the second integer is for vertex1, which should be 0? But it's 1.

Unless the vertices are assigned differently. The problem doesn't specify the order of vertices, but typically we assign by levels: first level0, then level1, then level2.

So the first vertex is root (0). Then the next a1=2 vertices are at level1: so vertex1 and vertex2. Then the next a2=2 vertices are at level2: vertex3 and vertex4.

So the parent of vertex3 must be either 1 or 2, and parent of vertex4 must be either 1 or 2.

The first tree in example: parent of vertex3 is 3? That doesn't make sense. 

Looking at the example output: "0 1 1 3 3" — perhaps the parents are not by vertex index but by the order of the vertices in the level? But no, the problem says the k-th integer corresponds to vertex k.

Another idea: maybe the example output has a typo and it's "0 1 1 2 2" for one tree and "0 1 2 2 2" or something. But it's written as 3.

Wait, the parent indices: the root is 0, then vertices 1 and 2 are at level1. Then vertices 3 and 4 are at level2. The only valid parent indices for vertices 3 and 4 are 1 or 2. So the output should have only 0,1,2 as parent indices. But the example output has 3, which is greater than 2. That is impossible.

Unless the vertices are not numbered in level order? But the problem doesn't specify. However, the output format doesn't specify the labeling order. But typically, we label the root as 0, then the next vertices in increasing order of levels and arbitrary within a level.

But the problem says: "print descriptions of two trees" — so we can choose any labeling as long as the level counts match and the tree is valid. However, the parent of a vertex must be a vertex from the previous level. 

But in the example output, for vertex3, parent is 3, which is at the same level? That's not possible.

I think there might be a mistake in the example. Let me look for the problem online or re-read.

Alternatively, I found a known problem: "Perfect Tree" or "Ambiguous Trees". After a quick search in my mind, I recall a similar problem in CodeForces: "Perfect Tree" (but I'm not sure). 

But let me think logically. 

For a given level sequence, when is the tree unique? 

The tree is unique if at every level, every node (except possibly the last level) has exactly one child. But that's not sufficient. 

Actually, the tree is unique if and only if at every level i (from 0 to h-1), every node at level i has exactly the same number of children, and that number is fixed by the sequence. 

More precisely, if for every level i (i from 0 to h-1), the number of nodes at level i+1 is exactly a[i] * k, then we can distribute equally. But even then, if the distribution is forced, then only one tree.

But consider: if at level i, we have a[i] nodes, and at level i+1, we have a[i+1] nodes. Then, each node at level i must have at least one child (since the tree goes to height h, so no leaf at level i for i<h). But the number of children per node can vary.

The tree is unique if there is only one way to assign children to parents such that the level sequence is a0, a1, ... ah, and the tree is isomorphic only if no matter how we assign, the structure is the same up to reordering.

But actually, isomorphism for rooted trees: the tree is completely determined if the sequence is such that at each level, the children counts for each node are fixed and the tree is balanced. But that's not the case.

Actually, two trees are isomorphic if we can renumber the nodes to make them identical. So the structure matters. For example, if at some level, two nodes have different numbers of children, then they are not isomorphic. But if we can swap two subtrees and get the same tree, then they are isomorphic.

Wait, for rooted trees, the order of children doesn't matter. So the tree is determined by the number of children at each node and the structure of the subtrees. 

But in this problem, we are only given the number of nodes at each level. So we don't know the exact degree of each node.

So when can we have two non-isomorphic trees with the same level sequence? 

Consider level i and i+1: we have p = a[i] parents and c = a[i+1] children. We need to assign children to parents. Each parent must have at least one child (because if a parent has no child, then the height would be less than h). But since the tree has height exactly h, then every node at level i (for i<h) must have at least one child.

Now, the assignment of children to parents: if all parents have exactly one child, then the tree is a chain and unique. 

If at some level i, we have multiple children per parent, then we have a choice: we can assign the children to parents in different ways. But does that lead to non-isomorphic trees? 

For example, in the sample: a=[1,2,2]. We have root (1 node) at level0, 2 nodes at level1, 2 nodes at level2. The root has two children. Then each of the two level1 nodes must have one child (because 2 children at level2, and 2 parents at level1). So each level1 node has exactly one child. 

How can we have two different trees? 

Unless... the two trees are the same up to reordering? But if we swap the two level1 nodes and then swap their children, we get the same tree. 

So then why is it ambiguous?

Unless the problem allows different parent assignments that lead to the same tree structure? But then they are isomorphic and we should output "perfect". 

But the example outputs "ambiguous". 

After re-examining the example output: 
Tree1: "0 1 1 3 3"
Tree2: "0 1 1 3 2"

If we have:
Vertices: 
0: root
1: parent 0
2: parent 1? But then vertex2 is at level2? Then vertex3: parent 3? That's invalid.

Alternatively, the vertices are not in level order? But the problem doesn't specify the order of the vertices in the output. We can output any valid parent array. 

But the parent array must be such that the level of each vertex is correct. 

In the first tree of the example: 
- Vertex0: parent0 -> level0
- Vertex1: parent1 -> but parent1 is vertex1, which is at the same level? Or if vertex1 is at level1, then its parent must be at level0, which is vertex0. So parent must be 0.

This is very confusing. 

Perhaps the example output has a mistake. But let me try to build two different trees for [1,2,2].

Tree1: 
- Root 0 has two children: 1 and 2 (level1).
- Then, both children of the root have one child: vertex1 has child 3, vertex2 has child 4. 
So parent array: 
vertex0:0
vertex1:0
vertex2:0
vertex3:1
vertex4:2
-> "0 0 0 1 2" or in order: [0,0,0,1,2] for vertices0 to4.

Tree2:
- Root 0 has two children: 1 and 2.
- Then, vertex1 has two children: 3 and 4. But then level2 would have two children from one node and none from the other? But we need two nodes at level2, so that would work: vertex3 and vertex4 are both children of vertex1. 
Then parent array:
vertex0:0
vertex1:0
vertex2:0  -> so far same
vertex3:1
vertex4:1
-> [0,0,0,1,1]

But the example output is "0 1 1 3 3" and "0 1 1 3 2", which is different.

Wait, in Tree2, vertex2 has no child? But then the tree has height 2? Yes, because the deepest node is at level2. But the constraint is that every node at level1 must have at least one child? No, only that the tree has height exactly h=2. So the deepest node must be at level2. But a node at level1 (vertex2) has no child, so it is a leaf at level1, but we have leaves at level2 as well. That is allowed? The problem says the height is the maximum number of edges from root to any leaf. So if we have a leaf at level1 and another at level2, then the height is 2, which is correct.

But the sequence a=[1,2,2] means: 
level0:1 node (0)
level1:2 nodes (1 and 2)
level2:2 nodes (3 and 4)

In Tree2: 
- level0: 0
- level1: 1 and 2
- level2: 3 and 4 (both children of 1)
So yes, that matches the sequence.

But then we have two different trees:

Tree A: 
0
| \
1  2
|  |
3  4

Tree B:
0
| \
1  2
|\
3 4

These two trees are not isomorphic. Why? In Tree A, the root has two children: one (1) has a subtree of height 1 (with one leaf), and the other (2) has a subtree of height 1 (with one leaf). In Tree B, the root has two children: one (1) has a subtree of height 1 with two leaves, and the other (2) has a subtree of height 0 (no children). 

So in Tree A, both children of the root have exactly one child each. In Tree B, one child of the root has two children and the other has none. 

Now, can we renumber the nodes to make Tree A look like Tree B? In Tree A, we have two nodes at level2 (3 and 4) that are leaves and each has a parent at level1. In Tree B, we have two leaves at level2 (3 and 4) that have the same parent (1), and one leaf at level1 (vertex2). 

The degrees are different: in Tree A, at level1, both nodes have degree 1 (one child each). In Tree B, one node at level1 has degree 2 (two children) and the other has degree 0. So they are not isomorphic.

So for the sequence [1,2,2], we have at least two non-isomorphic trees.

Now, how to represent them in the parent array?

For Tree A:
- Vertex0: parent 0
- Vertex1: parent 0
- Vertex2: parent 0
- Vertex3: parent 1
- Vertex4: parent 2
So the array: [0,0,0,1,2] for vertices0 to4.

For Tree B:
- Vertex0:0
- Vertex1:0
- Vertex2:0
- Vertex3:1
- Vertex4:1
So array: [0,0,0,1,1]

But the example output is:
0 1 1 3 3
0 1 1 3 2

This doesn't match. 

Unless the vertices are labeled differently. In Tree B, if we label:
- Vertex0: root
- Vertex1: child of root
- Vertex2: child of root
- Vertex3: child of vertex1
- Vertex4: child of vertex1

Then parent array: 
0:0, 1:0, 2:0, 3:1, 4:1 -> [0,0,0,1,1]

But the example has "0 1 1 3 3" which is different.

What if we change the labeling? Suppose we label the level1 nodes as 1 and 2, and then for level2, we assign vertex3 and vertex4 to different parents. But in Tree A, we have to assign one to 1 and one to 2.

Now, in the example output: 
First tree: "0 1 1 3 3" 
Second tree: "0 1 1 3 2"

If we interpret:
First tree:
- Vertex0: parent0
- Vertex1: parent1 -> but parent1 is not defined yet? 

This suggests that the labeling is not by levels but by the order of appearance in the parent array. But that doesn't make sense.

Another possibility: the example output has the parent array for vertices numbered in the order of the array index, but the parent indices refer to the array index of the parent.

But in Tree B, if we have:
- Let vertex0 be root.
- Then, we have vertex1 as the first level1 node, and vertex2 as the second level1 node.
- Then, the first level2 node (vertex3) is child of vertex1, and the second level2 node (vertex4) is child of vertex1.

So parent of vertex3 is 1 (the index of vertex1) and parent of vertex4 is 1.

Array: [0,0,0,1,1]

But the example says for the first tree: "0 1 1 3 3"

Unless they are using 1-indexed for parents? But the root has parent 0, which is not 1-indexed.

Perhaps the parent array is 1-indexed? But the root is 1? Then parent of root is 0. Then the array for Tree B would be: 
Vertex1:0
Vertex2:1  [meaning parent is 1? Then vertex2 is child of vertex1?]

But that doesn't match.

After rethinking, I recall that in some problems, the parent array uses the actual vertex id. In our case, we use the index of the vertex. So vertex0's parent is 0, vertex1's parent is the index of its parent, which is 0.

The example output has for the first tree: "0 1 1 3 3", which uses numbers 0,1,3. 3 is greater than the last level1 index (which is 2). That is only possible if the parent is a vertex from the same level or a later level, which is not allowed.

I think there is a mistake in the example output. But the problem says the example, so I must match it.

Looking at the example output: 
For the first tree: "0 1 1 3 3"
For the second tree: "0 1 1 3 2"

What if the vertices are not in level order? For example, the root is 0. Then the next vertex is 1, which is a child of 0. Then vertex2 is a child of 1. Then vertex3 is a child of ... but then we have only one level1 node so far.

Perhaps the labeling is different. Let me try to build the tree from the parent array.

For the first tree: "0 1 1 3 3"
- Vertex0: parent0 -> root.
- Vertex1: parent1 -> parent is vertex1? But vertex1 is itself -> invalid.

Unless parent1 means vertex1's parent is vertex0? But then it should be 0.

I think the example output might be using a different labeling. Or perhaps there's a offset.

Another idea: the parent array might be 1-indexed, but then the root has parent 0, which is not 1-indexed. 

Let me try 1-indexed interpretation:
- Vertex1: parent0 (root)
- Vertex2: parent1 -> so parent is vertex1
- Vertex3: parent1 -> parent is vertex1
- Vertex4: parent3 -> parent is vertex3
- Vertex5: parent3 -> parent is vertex3

Then the tree has:
- Level0: vertex1? But the root should be one node. 
- Level1: vertices2 and 3 (children of vertex1)
- Level2: vertices4 and 5 (children of vertex3) -> so level2 has two nodes.

But then the sequence is a0=1 (vertex1), a1=2 (vertices2,3), a2=2 (vertices4,5). So it matches.

And the height is 2.

In this tree, the structure is: 
  1
 / \
2   3
     \
      4
       \
        5? 

No, vertex4 and 5 are both children of vertex3, so:
     1
    / \
   2   3
       / \
      4  5

So level0:1, level1:2, level2:2. 

For the second tree: "0 1 1 3 2" (in 1-indexed, the array is for vertices1 to5: 
- Vertex1:0
- Vertex2:1
- Vertex3:1
- Vertex4:3
- Vertex5:2

So the tree:
     1
    / \
   2   3
  /   / \
 5   4   ? 

Vertex5: parent2 -> so child of vertex2.
Vertex4: parent3 -> child of vertex3.
So the tree:
       1
      / \
     2   3
    /     \
   5       4

Then level0:1 (vertex1)
level1:2 (vertices2,3)
level2:2 (vertices5,4)

This is different from the first tree. In the first tree, the node3 (at level1) has two children. In the second tree, node2 has one child and node3 has one child. 

But wait, in the second tree, node2 has one child (5), node3 has one child (4). So it's the same as the first tree? No, in the first tree, node3 has two children, and node2 has no child.

In the 1-indexed first tree: 
- Vertex2: child of1, no children (since no one has parent2? In the parent array, no vertex has parent2, so vertex2 has no children.
- Vertex3: has two children:4 and5.

In the second tree: 
- Vertex2: has one child:5
- Vertex3: has one child:4

So these are non-isomorphic.

So the example output is in 1-indexed? But the problem says: the k-th integer is the parent of vertex k, and the root has parent 0. 

But in 1-indexed, the vertices are numbered 1 to n, and the root is 1, with parent 0. 

But the problem does not specify that the vertices must be numbered in a particular order. It only requires that we print the parent of vertex k for k from 0 to n-1? Or from 1 to n?

The problem says: "print $\sum_{i=0}^{h} a_i$ integers, where the $k$-th integer is the parent of vertex $k$"

And the note: "or zero if the $k$-th vertex is the root".

So the first integer is for k=0, which is the root. 

But in the example, the first integer is 0 (for the root), then the next integers are for k=1,2,3,4. 

In the first tree, the parent of vertex1 is 1? That is invalid.

Unless they are using a different vertex numbering. The problem does not specify how to number the vertices, only that the parent array should be for vertex0 to vertex_{n-1} and the parent is the vertex number.

So we can choose any numbering. 

In the example output, for the first tree: 
- Vertex0: parent0
- Vertex1: parent1 -> which is itself? 
- Vertex2: parent1
- Vertex3: parent3
- Vertex4: parent3

This is only valid if:
- Vertex0 is root.
- Vertex1: parent is vertex1 -> self-loop, invalid.

So it must be that the numbering is 0 for the root, then the level1 vertices are 1 and 2, and level2 vertices are 3 and 4. 

Then the only valid parents are 0,1,2. 

So the example output must be wrong. 

I found a known that in CodeForces problem "Ambiguous Trees", the sample output for "1 2 2" is indeed:

ambiguous
0 1 1 3 3
0 1 1 3 2

But this is for vertex0 to4, and it's valid? How? 

After research, I recall that in that problem, the parent array is 1-indexed in the output but the problem statement uses 0 for root in 0-indexed system. But the sample input and output are as given.

Alternatively, the example might be using a compressed representation. 

But to resolve, let's assume the example is correct and try to build the tree from the parent array.

For the first tree: parent array = [0,1,1,3,3] for vertices0..4.
- Vertex0: parent0 -> root.
- Vertex1: parent1. Who is parent1? Vertex1. So it's parent is itself. This is invalid in a tree.

Unless the parent of vertex1 is vertex0? But they wrote 1, not 0.

The only way this works is if the parent indices are not the vertex id, but the index in the array of the parent's parent? That doesn't make sense.

Another idea: the parents are given as the index of the parent in the array of that level. For example, the root is at level0. The level1 vertices: two of them. The parent of the first level1 vertex is the first vertex in level0, which is index0. But how to reference it? 

The problem says the parent is the vertex number, not an index in the level.

Given the confusion, and since the constraints state that the total number of vertices is at most 200,000, and the sum of a_i is at most 200,000, I need to focus on the algorithm.

The key is: when is the tree unique? 

The tree is unique if and only if for every level i from 1 to h, the assignment of children to parents is forced to be uniform, and any non-uniform assignment would break the level sequence or create isomorphism? 

But isomorphism is about the structure, not the level sequence alone.

Insight: two trees are non-isomorphic if at some level, the degrees of the nodes are different. 

But we only have the level counts, not the degrees.

So, when can we have two different assignments that yield non-isomorphic trees? 

This happens if at some level i (>=1), there is a node at level i that has more than one child, and another node at level i that has exactly one child, and we can swap the assignment to create a different degree distribution.

But note: the level sequence is fixed. So at level i, we have a[i] nodes, and at level i+1, we have a[i+1] nodes. We must assign the level i+1 nodes to the level i nodes as children.

Each node at level i must have at least one child (because the tree must extend to height h).

The assignment is a partition of a[i+1] into a[i] parts, each part at least 1. 

The tree is unique if and only if for every level i, the only possible partition is the one where every node at level i has exactly floor(a[i+1]/a[i]) or ceil(a[i+1]/a[i]) children, and it is not possible to have a node with a different number of children.

But even then, if the partition is not uniform, but we can reorder the children, then the trees might be isomorphic.

For example, in the sequence [1,2,2]:
- At level0: 1 node.
- Level1: 2 nodes.
- Level2: 2 nodes.
At level0 to level1: the root has 2 children.
At level1 to level2: we need to assign 2 children to 2 parents. Each parent must have at least one child. The only possibility is one parent has 1 child and the other has 1 child. So the trees are isomorphic? 

But wait, we can have one parent have 2 children and the other have 0? But then the other parent would be a leaf at level1, and we have only one node at level2? But we need two nodes at level2. So the only possibility is that each level1 node has exactly one child. 

Then why is the example output "ambiguous"? 

Unless my Tree B is valid: in Tree B, one level1 node has 2 children and the other has 0. Then level2 has two nodes, both children of the same level1 node. And the other level1 node has no children. 

But is that allowed? The tree has height 2: the path from root to a leaf in level2 is 2 edges. And there is a leaf at level1 (the level1 node with no children) and two leaves at level2. The maximum is 2, so height is 2. The level counts: level0:1, level1:2, level2:2. 

So it is allowed. 

And in this case, we have two different trees: one in which the two level1 nodes each have one child, and another in which one level1 node has two children and the other has none.

And these are not isomorphic, as explained.

So the tree is not unique if at some level i, we can have a parent at level i that has more than one child while another parent at level i has exactly one child (or zero, but zero only if it is at the last level? not here because i<h) — but in this case, we are at level1 (i=1) and we are going to level2 (i+1=2). The node at level1 that has no child becomes a leaf at level1, which is allowed as long as there is at least one path to level2.

The condition for ambiguity is: if there is a level i (1<=i<h) such that a[i] >= 2 and a[i+1] >= 2, then we can have at least two different distributions: 

Option 1: all level i nodes have exactly one child (so a[i+1] = a[i] * 1) -> then it's a must, but if a[i+1] > a[i], then we cannot have all with one child. 

In our case, a[1]=2, a[2]=2, so a[1+1]=a[2]=2, and a[1]=2, so 2 = 2 * 1, so each must have one child. But then how can we have a node with two children? 

If we try: one node has two children, then the other node must have 0 children, because 2+0=2. But 0 is allowed only for leaves, and we are at level1, and the tree must extend to level2? It does because the node with two children has children at level2. 

So the condition is: if at some level i (from 0 to h-1), we have a[i] >= 2 and a[i+1] > a[i] (wait, no: in our case a[i+1] = a[i] =2, and we can have one node take all the children of the level (2) and the others take 0.

But only if a[i+1] >= 2, and a[i] >= 2, and a[i+1] > a[i] is not necessary. 

In fact, we can have different distributions as long as a[i+1] - a[i] >= 1? But in our case, a[i+1] - a[i] =0.

The only requirement is that we can have at least one node with at least 2 children, and the rest with at least 0, but also we need to have exactly a[i+1] children in total.

So, if a[i+1] > a[i], then we must have at least one node with at least 2 children. And then we can have different distributions. 

But in our example, a[i+1] = a[i] =2, and we can have either:
- Each of the a[i] nodes has exactly 1 child. (then a[i+1] = a[i] * 1 =2)
- Or one node has 2 children and the other has 0.

So both are valid.

And these lead to non-isomorphic trees.

Therefore, the tree is not unique if there exists a level i (0-indexed, for i from 0 to h-1) such that:
- a[i] >= 2
- a[i+1] >= 2
- and also, we have the possibility to assign the children such that not all parents have the same number of children.

But note: if a[i+1] = a[i], then we can have all parents have 1 child, or one parent have 2 and another 0, etc. 

In fact, we can always have the uniform distribution (each parent has exactly the same number of children) if a[i+1] is divisible by a[i]. But we can also have non-uniform distributions as long as the sum is a[i+1] and each has at least one child? 

But wait, in the case a[i+1] = a[i], the uniform distribution is one child per parent. The non-uniform is one parent takes two, another takes 0, and the rest take 1? But we have a[i] parents. If one takes 2 and one takes 0, then the others (a[i]-2) would have to take the remaining a[i+1]-2 = a[i]-2. So if a[i] >=2, then we can have this non-uniform assignment.

But if a[i] =2, then a[i]-2=0, so the non-uniform assignment is one parent takes 2, the other takes 0. And the sum is 2.

So the condition for ambiguity is: there exists a level i such that a[i] >= 2 and a[i+1] >= 2 and a[i] >= 2 (which is redundant) and the level i is not the last level (i < h).

But also, we must be able to form two different degree distributions. 

In particular, if at any level i (0<=i<h), we have a[i]>=2 and a[i+1]>=2, then we can form two trees:
Tree1: 
  - At level i, give to the first node at level i as many children as possible (>=2) and the second node at level i one child, and adjust so that the total is a[i+1]. 
  - But wait, in our example, we gave one node 2 children and the other 0. 

But is that always possible? 
We require: let the minimum number of children per parent is 1. So if we are to have one parent have k children (k>=2) and another have 0, then we must have k + 0 + ... (for the other a[i]-2 parents, we would need to assign the remaining a[i+1]-k children) but also the other parents must have at least 1 child? 

No, we can have multiple parents with 0 children, as long as we have at least one parent with enough children to reach the next levels? But the tree height is defined by the maximum path. 

Actually, the tree must have at least one path from the root to a leaf of length h (edges). So from level0 to level h.

For level i, as long as at least one node at level i has at least one child that is not a leaf at level i+1? But we can have leaves at different levels.

The only constraint is that there is at least one node at level i that has a child (so that the tree can go to level i+1 and eventually to level h). 

So for level i, we can have some nodes with 0 children (they are leaves at level i), and the others with at least 1 child, as long as the total children is a[i+1] and at least one node has a child (so that the height can be achieved).

But to achieve height h, we only require that for levels i=0 to h-1, there is at least one node at level i that has a child that leads to a path of length h-i. However, we are given the level counts for all levels, so we know that level h exists and has a[h] nodes. So we must have, for each level i from 0 to h-1, at least one child that eventually leads to level h. 

But the problem guarantees that there is at least one tree matching the sequence. So we don't need to worry about that.

Therefore, for a level i, if a[i] >=2 and a[i+1] >=2, then we can do:

Option A: 
  - Let the first node at level i have 1 child.
  - Let the second node at level i have 1 child.
  - ... and so on for all a[i] nodes, but then total children is a[i], but we need a[i+1] children. So if a[i+1] > a[i], we can put the extra children arbitrarily.

But in our example, a[i+1] = a[i] =2, so Option A would be each has 1 child.

Option B: 
  - Let the first node at level i have a[i+1] - (a[i]-1) children (which is a[i+1]-a[i]+1), and the remaining a[i]-1 nodes have 1 child each. 
  - This works if a[i+1] - a[i] + 1 >= 2, i.e., a[i+1] >= a[i]+1.

But in our example, a[i+1] = a[i] =2, then a[i+1]-a[i]+1 = 1, which is not>=2.

Alternatively, we can let one node have 2 children and the others have 0 or 1. 

Specifically, for our example, we let one node have 2 children and the other have 0.

This works because 2+0=2.

In general, if a[i] >=2 and a[i+1] >=2 and a[i+1] - (a[i] - 1) >= 2? 
- We can have one node take 2 children, and the other a[i]-1 nodes take at least 0. But we need total a[i+1] = 2 + (a[i+1]-2). And we have a[i]-1 nodes to assign a[i+1]-2 children. But we can assign 0 to them? Then we require a[i+1]-2 =0? So a[i+1]=2.

So the non-uniform assignment (one node with 2, others with 0) is only possible if a[i+1] = 2 and a[i] >=2.

But also, we can have other non-uniform assignments. For example, if a[i+1] = 3 and a[i]=2, then we can have (3,0) or (2,1). The uniform assignment would be (1.5,1.5) -> not integer, so we must have non-uniform. But then (2,1) is the only non-uniform? Then we have only one tree? 

No, (2,1) and (1,2) would be isomorphic by swapping the two parents.

So to have non-isomorphic trees, we need two different degree distributions that are not permutations of each other. 

For example:
- In the case a[i]=2, a[i+1]=2: we can have (1,1) and (2,0). (0,2) is the same as (2,0) by swapping. 
- (1,1) is uniform, (2,0) is non-uniform.

And (1,1) and (2,0) are not permutations: one has both degrees 1, the other has degrees 2 and 0.

So the trees are non-isomorphic.

Now, can we do this for any a[i]>=2 and a[i+1]>=2? 

Consider a[i]=3, a[i+1]=3:
- Uniform: (1,1,1)
- Non-uniform: (3,0,0) -> then the parent with 3 children, and the other two with 0.
  But then the total is 3. 
- Also, (2,1,0) is non-uniform.

But (2,1,0) and (3,0,0) are different from (1,1,1) and among themselves.

However, (2,1,0) and (1,2,0) are isomorphic by swapping the first and second parent. Similarly, (3,0,0) is unique.

So we can have at least two non-isomorphic trees: 
Tree1: all parents have 1 child.
Tree2: one parent has 3 children, the others have 0.
Tree3: one parent has 2 children, one has 1, one has 0.

But Tree2 and Tree3 are non-isomorphic to Tree1 and to each other.

So we only need to output two trees.

Therefore, the condition for ambiguity is: there exists at least one level i (0<=i<h) such that a[i]>=2 and a[i+1]>=2.

Because if we have that, we can build:
- TreeA: a uniform tree where every parent at level i has exactly floor(a[i+1]/a[i]) or ceil, but then we can also build
- TreeB: a tree where one parent at level i takes 2 children (if a[i+1]>=2) and another parent takes 0 (if a[i]>=2), and the remaining a[i]-2 parents take 1 child? But wait, then the total children would be 2 + 0 + 1*(a[i]-2) = a[i]. But we need a[i+1] children. 

This works only if a[i+1] = a[i]? 

Actually, we can do this: 
  - Let one parent take 2 children.
  - Let one parent take 0 children.
  - The remaining a[i]-2 parents take 1 child each.
  - Total = 2 + 0 + 1*(a[i]-2) = a[i] 

So this works only if a[i+1] = a[i] and a[i]>=2.

But what if a[i+1] > a[i]? 
For example, a[i]=3, a[i+1]=4.
- Uniform: not possible since 4/3 is not integer.
- We can have distributions: (2,1,1), (2,2,0), (3,1,0), etc.

And (2,1,1) is different from (2,2,0) or (3,1,0). But also, (2,1,1) might be the only one up to isomorphism? 

No, (2,1,1) and (1,2,1) are isomorphic.

But (3,1,0) is different from (2,2,0): 
- (3,1,0): degrees 3,1,0
- (2,2,0): degrees 2,2,0

These are not the same up to permutation.

So we can have:
- TreeA: (2,2,0)
- TreeB: (3,1,0)

Or even TreeC: (4,0,0) if a[i+1]=4.

But the condition is: if there exists a level i such that a[i]>=2 and a[i+1]>=2, then we can find two non-isomorphic trees.

In fact, we can always do:
- Tree1: make a star at level i: one parent takes almost all children, and the others take 1 or 0.

Specifically, we can do:
  Tree1: the first parent at level i has a[i+1] - (a[i] - 1) children, and the other a[i]-1 parents have 1 child each. (This is the greedy assignment: minimize the max children per parent? or the typical assignment)
  Tree2: the first parent at level i has 2 children, the second parent has 0 children, and then the remaining parents have 1 child each. But then the total is 2 + 0 + 1*(a[i]-2) = a[i], so this only works if a[i+1] = a[i]. 

For the case a[i+1] > a[i], we can do:
  Tree1: the first parent has a[i+1] - (a[i]-1) children (which is at least 2 because a[i+1] > a[i] and a[i]>=2, so a[i+1] - (a[i]-1) >= a[i]+1 - a[i] +1 =2? not exactly: a[i+1] - (a[i]-1) = a[i+1] - a[i] +1. Since a[i+1] > a[i], then a[i+1] - a[i] >=1, so this value is at least 2.
  Tree2: the first parent has 2 children, and then the remaining a[i]-1 parents have (a[i+1]-2) children to share, and since a[i+1]-2 >=0, and we can assign each of the remaining a[i]-1 parents at least 0 children, but we must have at least one parent that has a child that eventually goes to the last level. But we can assign arbitrarily as long as the total is a[i+1]-2.

But for Tree2, if we assign the remaining a[i+1]-2 children to the other parents arbitrarily, it might be that the distribution is different from Tree1.

However, to ensure non-isomorphism, we want the two trees to have different degree distributions at level i.

In Tree1, the first parent has a[i+1]-a[i]+1 children (which is >=2) and the others have 1.
In Tree2, the first parent has 2 children, the second parent has 0 children (if a[i]>=2), and then the remaining parents have ...? But the total children from the other parents must be a[i+1]-2. But we have a[i]-2 parents left. So if we assign each of them exactly one child, then we need a[i+1]-2 = a[i]-2, i.e., a[i+1] = a[i]. 

Therefore, the construction of Tree2 (with a 2 and a 0) only works when a[i+1] = a[i] and a[i]>=2.

But if a[i+1] > a[i], we can do a different non-uniform assignment that is not a permutation of Tree1's assignment.

For example, a[i]=3, a[i+1]=4:
  Tree1: first parent: 4 - (3-1) = 4-2 =2 children; the other two parents: 1 each. -> [2,1,1]
  Tree2: first parent: 3 children; second parent: 1 child; third parent:0 children. -> [3,1,0] -> this is different from [2,1,1] because one has a 3 and a 0.

And [3,1,0] and [2,1,1] are not permutations of each other.

So we can always do:
  Tree1: [ a[i+1]-a[i]+1, 1, 1, ..., 1 ]   (first parent has a[i+1]-a[i]+1, the rest have 1)
  Tree2: [ a[i+1]-a[i]+2, 0, 1, 1, ..., 1] ? But then the first parent might have more than available? 

Alternatively, Tree2: [2, 0, and then the remaining a[i]-2 parents have their children such that the total is a[i+1]-2. But then we need to assign a[i+1]-2 children to a[i]-2 parents, and each must be at least 0. And we can assign the minimum to the next parent? 

But to have a different degree distribution, we can do:
  Tree2: first parent: 2 children
          second parent: a[i+1]-2 children (if a[i+1]-2>=0) 
          and the rest: 0
But then the degree list would be [2, a[i+1]-2, 0,0,...,0]. 

This is different from Tree1's [a[i+1]-a[i]+1, 1,1,...,1] as long as a[i+1]-a[i]+1 !=2 or a[i+1]-2 !=1, etc. But not necessarily.

Alternatively, we can do:
  Tree2: first parent: a[i+1]-a[i]+2 - ? 
But perhaps a simpler way: 

Since the problem only requires two different trees, and the constraints are that the total vertices is at most 200,000, we can do:

- If there exists any level i (0<=i<h) such that a[i]>=2 and a[i+1]>=2, then we can output two trees that differ in the assignment at level i.

- In the first tree, we make the assignment as uniform as possible. 
  Actually, for the first tree, we can just do a left-heavy assignment: assign children to parents in order, each parent gets at least one child, and the extra children are assigned to the first parent.

- In the second tree, we make the assignment such that one parent (say the first) has two children and another parent (the last) has 0, and the rest have 1. But only if a[i+1] = a[i]. 
  If a[i+1] > a[i], then in the second tree, we can make the first parent have one more child and the last parent have one less? But then it might be isomorphic.

But we can do for the second tree: 
  - We make the first parent at level i have 2 children, and then the next parent have the remaining a[i+1]-2 children? But that parent might have a very high degree.

However, to ensure non-isomorphism, we want the two trees to have different multisets of degrees at level i.

So for a level i with a[i] parents and a[i+1] children (with a[i]>=2, a[i+1]>=2), we can do:

Tree1 (typical assignment):
  - The first parent (at the level) gets the first few children, the next parent gets the next, etc.
  - Specifically, we can assign the children consecutively: 
        parent0: children from start0 to start0 + d0 - 1
        parent1: children from start1 to start1 + d1 - 1
        ...
  - We can set the degrees as: 
        d0 = a[i+1] - (a[i] - 1)   [so that the first parent takes the extra children]
        d1 = 1, d2=1, ... 

Tree2 (different assignment):
  - We make parent0 have 2 children (if a[i+1] >=2), and parent1 have 0 children (if a[i]>=2), and then parent2 to parent_{a[i]-1} have 1 child each? But then the total is 2 + 0 + 1*(a[i]-2) = a[i]. 
  - But this only works if a[i+1] = a[i]. 

What if a[i+1] > a[i]? 
  - Then we can make parent0 have a[i+1]-a[i]+2 children? but that might be arbitrary.
  - Alternatively, we can make parent0 have 2 children, parent1 have a[i+1]-2 children, and the rest have 0. 
  - This works if a[i] >=2 (we have at least two parents), and then parent1 has a[i+1]-2 children, which is at least 0 (since a[i+1]>=2). 

But is this non-isomorphic from Tree1? 

Example: a[i]=3, a[i+1]=4.
  Tree1: degrees: [2,1,1]
  Tree2: degrees: [2,2,0]  (if we do parent0:2, parent1:2, parent2:0) -> but then the total is 4, and the multiset is {2,2,0} vs {2,1,1} -> different.

Or we can do: [2, a[i+1]-2, 0,0,...] -> in this example [2,2,0].

And [2,2,0] is different from [2,1,1].

So algorithm:

1. If for every level i from 0 to h-1, we do not have (a[i]>=2 and a[i+1]>=2), then the tree is unique: output "perfect".

2. Otherwise, there is at least one level i with a[i]>=2 and a[i+1]>=2. Then output "ambiguous", and two trees.

To build the trees:

- We will build two parent arrays.
- We know the total number of vertices n = sum(a[0..h]).
- We will generate the parent array for the entire tree.

For both trees, the structure will be the same for levels before i, and after i, and also at level i except for the assignment of children.

Let me define:

- We will create an array for the first tree (tree1) and second tree (tree2).
- We traverse the levels and assign parents.

But we only need to differ at level i. For levels before i, the parent assignment is the same. For level i, we assign differently. For levels after i, we can assign in the same way as tree1, but it might be affected by the different assignment at level i.

How to assign parents:

We can assign the vertices in level order.

Let’s define:
  - Let the root be vertex0.
  - Let the next a[1] vertices be the level1 vertices, and so on.

Specifically, we can assign an array "parent" for each vertex.

We'll have a running counter for the next vertex id.

For tree1 and tree2, the only difference is at level i.

Steps for tree1 (typical tree):
  - parent[0] = 0.
  - Let last = 0 (last vertex assigned)
  - Let next = 1 (next vertex id to assign)
  - We'll create an array for the parent of each vertex.
  - For each level l from 1 to h:
        For each of the a[l] vertices at this level:
             assign parent as the next available parent from level l-1.
        But we need to assign the parents from level l-1 in some order.

But how to assign? 

In tree1, we do a left-heavy assignment: 
  - The first parent at level l-1 gets as many children as possible? 
  - But we want a typical assignment: we can assign the children to the parents in a round-robin or in consecutive blocks.

Actually, for tree1, we can assign:
  - For level l, the parents are the vertices of level l-1, in the order they are labeled.
  - We assign the children consecutively: 
        Let the first parent in level l-1 (vertex p_start) have children: next, next+1, ... next + d0 - 1
        Then the next parent has children: next + d0, ... 
  - But we don't know how many children each parent has. 

Alternatively, we can do:
  - In tree1, we assign the children in a contiguous block to each parent. 
  - Specifically, for level l-1, the parents are in the range [p_start, p_start + a[l-1] - 1].
  - For the first parent at level l-1, we give it floor(a[l] / a[l-1]) or ceil children? 

But we can do a greedy assignment for tree1: 
  - Let the first parent at level l-1 get as many children as needed to make sure that the remaining parents can each get at least one child.
  - That is, the first parent gets a[l] - (a[l-1] - 1) children, and then each of the remaining a[l-1]-1 parents gets one child.

This is what we described earlier.

But note: this greedy assignment might be used only for the ambiguous level in tree1.

For the entire tree1, we can do a typical assignment: the first parent in the level gets the extra children.

Similarly for tree2, at the ambiguous level i, we assign differently.

Algorithm for building the parent array for the whole tree:

  n = sum(a)
  Let parent = [0] * n   # for tree1 and tree2, we might have two arrays.

  # The first tree: 
  parent1[0] = 0   # root
  # We will assign vertices level by level.
  # The vertices are assigned in increasing order: 
  #   level0: [0:0]
  #   level1: [1:1+a[1]-1]
  #   level2: [1+a[1]: 1+a[1]+a[2]-1] and so on.

  Let current_vertex = 1   # the next vertex to assign (after root)
  Let ambiguous_level = -1  # we will find one level i where a[i]>=2 and a[i+1]>=2.

  # First, check if there's any ambiguous level: 
  found = False
  for l in range(0, h):
      if a[l] >=2 and a[l+1]>=2:
          ambiguous_level = l
          found = True
          break

  If not found:
      return ["perfect"]

  Else:
      # Build tree1 and tree2 normally for levels up to ambiguous_level-1, then at level ambiguous_level do differently, and then after that, do the same.

  But note: for levels before the ambiguous level, the parent assignment is the same for tree1 and tree2.

  So:

  Let parent1 = [0]*n
  Let parent2 = [0]*n   # we'll build two arrays.

  # The root is vertex0, parent1[0]=0, parent2[0]=0.

  # We'll simulate level by level.
  # We need to know the start index of each level.

  Let levels = []
  start = 0
  for i in range(0, h+1):
      levels.append( (start, start+a[i]-1) )   # the vertex indices for level i are [start, start+a[i]-1]
      start += a[i]

  # Now, for level0, we have one vertex:0.

  # For level1: the vertices are [levels[1][0], levels[1][1]]
  #   their parents must be from level0: only vertex0.

  # In general, for level l (>=1), the parent of a vertex at level l is a vertex at level l-1.

  # For tree1 and tree2, for levels l = 1 to ambiguous_level, we assign the parents in the same way: 
  #   We assign the parents in a contiguous manner: the first a[l] vertices in level l-1 get assigned as parents, in order.

  # Specifically, for a vertex at level l with index v, we can assign its parent as the vertex at level l-1 with index: 
  #   base = levels[l-1][0]   # the first vertex in level l-1
  #   parent1[v] = base + (v - levels[l][0]) // (number of children per parent?)  -> not exactly.

  Alternatively, we can do:

  For level l from 1 to h:
      Let base_prev = levels[l-1][0]   # the first vertex of the previous level
      Let base_cur = levels[l][0]       # the first vertex of the current level
      Let count_cur = a[l]              # number of vertices in current level
      Let count_prev = a[l-1]           # number of vertices in the previous level

      # In tree1, we assign:
      #   We'll assign the first (count_cur - (count_prev - 1)) children to the first parent.
      #   Then the next count_prev-1 parents each get one child.

      # But wait, we don't need that for all levels, only for the ambiguous level we want two different assignments.

      # For levels before the ambiguous level, we can do a simple assignment: 
      #   parent1 for the first count_prev vertices in the current level: assign parent = base_prev + 0, then base_prev+1, ... 
      #   but then if count_cur > count_prev, we need to assign the extra children to the last parent? 

      # Actually, for the sake of simplicity, for levels that are not the ambiguous level, we can assign the parents in a left-heavy way: 
      #   the first vertex in the current level gets the first parent, the second the first parent? No.

      # How about: 
      #   We assign the parents for the current level in the order of the parents in the previous level, and we assign the children consecutively to each parent.

      # Specifically, we can do:
      #   Let p = base_prev   # the first parent
      #   Let children_assigned = 0
      #   For each vertex in the current level, in order:


## Final Solution

