# Solution for abc320_d.md

Generated at: 2025-07-22 06:31:35
Model: deepseek-reasoner

## Reasoning Process

Alright, so I'm trying to solve this problem. Let me read it again to make sure I understand.

We have N people on a coordinate plane, with Person 1 at the origin (0,0). We are given M pieces of information. Each piece of info says: from person A's perspective, person B is X units in the positive x-direction and Y units in the positive y-direction. This means that if person A is at (a_x, a_y), then person B is at (a_x + X, a_y + Y). But note, the problem says "from person A's perspective", so it's relative to A.

Wait, actually, that makes sense. So if A is at (x_a, y_a), then B is at (x_a + X_i, y_a + Y_i). Alternatively, we can write:

x_b = x_a + X_i
y_b = y_a + Y_i

But note that the problem also allows negative values for X_i and Y_i, so it's possible for a person to be in the negative direction relative to another.

The goal is to determine the coordinates of each person. If we can't uniquely determine a person's coordinates, we output "undecidable" for that person.

Constraints: N and M up to 200,000. So we need an efficient solution.

Looking at the examples:

Example 1:
Input: 
3 2
1 2 2 1
1 3 -1 -2

Output:
0 0   (Person 1)
2 1   (Person 2)
-1 -2 (Person 3)

Explanation: 
From the first relation: from person1's perspective, person2 is at (2,1). Since person1 is at (0,0), person2 is at (2,1).
From the second relation: from person1's perspective, person3 is at (-1,-2). So person3 is at (-1,-2).

Example 2:
Input:
3 2
2 1 -2 -1
2 3 -3 -3

Output:
0 0
2 1
-1 -2

Wait, this is the same output as Example 1. Let me see.

First relation: from person2's perspective, person1 is at (-2,-1). So if person2 is at (x2, y2), then person1 is at (x2 -2, y2 -1). But we know person1 is at (0,0), so:

0 = x2 - 2  => x2 = 2
0 = y2 - 1  => y2 = 1

So person2 is at (2,1).

Second relation: from person2's perspective, person3 is at (-3,-3). So:

x3 = x2 - 3 = 2 - 3 = -1
y3 = y2 - 3 = 1 - 3 = -2

Same as Example 1.

Example 3:
Input:
5 7
1 2 0 0
1 2 0 0
2 3 0 0
3 1 0 0
2 1 0 0
3 2 0 0
4 5 0 0

Output:
0 0
0 0
0 0
undecidable
undecidable

So, multiple relations that are consistent but also redundant. The key here is that persons 4 and 5 have no connection to person1, so we can't determine their absolute coordinates. They are only connected to each other: from person4's perspective, person5 is at (0,0). But without knowing where person4 is, we can't fix person5 either. So both are undecidable.

Now, persons 1, 2, and 3: all relations say that each person is at the same location as the others? Let me see:

First relation: from person1, person2 is at (0,0). So if person1 is at (0,0), then person2 is at (0,0). Similarly, the second relation is the same. Then, from person2, person3 is at (0,0): so person3 is at (0,0). Then, from person3, person1 is at (0,0): which is consistent. Similarly, the other relations are the same. So they all end up at (0,0). 

But wait, actually, the relations are saying that each pair is at the same location. So all three are at (0,0). Hence, the output for persons 1,2,3 is (0,0) and persons 4,5 are undecidable.

So the problem reduces to: we have a graph of N nodes (people) and M edges (relations). Each edge from A to B has a vector (X, Y). Then, the absolute coordinate of a node i can be determined if and only if it is connected to the origin (person1) and we can compute the coordinate by traversing the path from person1 to i.

But note: the graph might have cycles or multiple paths. We have to check for consistency. The problem states that the given information is consistent, so we don't have to worry about contradictions? Actually, it says "The given information is consistent." So we don't need to check for contradictions? But we still need to handle multiple paths and see if they lead to the same coordinate.

However, the problem also says that if the coordinate cannot be uniquely determined, output "undecidable". This happens when the person is not connected to person1? Or when there are multiple possible coordinates? But the information is consistent, so multiple paths would yield the same coordinate. But if the person is not connected to person1, then we cannot determine the absolute coordinate. For example, in Example 3, persons 4 and 5 form a separate component. So they are undecidable.

So the problem is: we have a graph (possibly disconnected). We start from person1 at (0,0). Then, for each connected component that includes person1, we can compute the coordinates of all nodes in that component. For nodes not in that component, we output "undecidable".

But wait: what if a component is connected to person1 via multiple paths? Since the information is consistent, all paths should yield the same coordinate. So we can just do a BFS or DFS from person1 and assign coordinates.

But what about cycles? The problem says the information is consistent, so the cycle should not break the consistency. For example, if we have a cycle: 1->2: (2,1), 2->3: (3,4), and 3->1: (-5,-5). Then we have:

Let person1: (0,0)
Person2: (2,1)
Person3: (2+3,1+4) = (5,5)
Then from 3 to 1: (5 -5, 5-5) = (0,0) which is consistent.

But in the input, we might not get the relations in the direction we want. For example, we might have an edge from A to B and also from B to A. Or we might have an edge in the reverse direction.

So we can model the graph as undirected? Actually, each relation is directed: from A to B. But we can also derive the reverse relation: if from A, B is at (X, Y), then from B, A is at (-X, -Y). So we can create an undirected graph? Actually, we can build a directed graph and also include the reverse edges with negative weights? But that would double the edges? Alternatively, we can build an undirected graph where each edge (A, B) has two directions: from A to B: (X, Y) and from B to A: (-X, -Y).

So the plan is:

1. Build a graph where each edge is stored as an undirected edge. For a given relation (A, B, X, Y), we add:
   - Edge from A to B: (X, Y)
   - Edge from B to A: (-X, -Y)

But actually, we can just store one edge and then when traversing, we know that if we go from A to B, we add (X,Y), but if we go from B to A, we subtract (X,Y) (i.e., add (-X, -Y)).

Alternatively, we can build the graph as:

graph[A].append( (B, X, Y) )
graph[B].append( (A, -X, -Y) )

This way, we have bidirectional edges.

2. Then, we start BFS or DFS from node1 (person1) at coordinate (0,0). We assign coordinates to each node we visit.

3. Since the graph may be disconnected, we mark the nodes we can reach. For those we can't, we output "undecidable".

But wait, what about multiple paths? Since the information is consistent, we don't need to worry about conflicting coordinates. We can just assign the coordinate the first time we visit a node, and because of consistency, if we visit the same node again via a different path, we should get the same coordinate. However, we might not want to traverse the same node again? Actually, we can use a visited array to avoid processing a node more than once.

But how do we handle the coordinate computation? 

Let me define an array `coords` of size (N+1), where `coords[i]` is a tuple (x_i, y_i) for person i. Initialize all to None. Then set coords[1] = (0,0).

Then, we use a queue for BFS starting from node1. For each neighbor (B, X, Y) of node A, we have:

   coords[B] = (coords[A][0] + X, coords[A][1] + Y)

But wait: the edge from A to B is (X,Y). So if we are at A and we have an edge to B, then B's coordinate is A's coordinate plus (X,Y). Similarly, if we go from B to A, we have an edge (A, -X, -Y). So if we start from A, we set B as (A_x + X, A_y + Y). If we start from B, then we set A as (B_x - X, B_y - Y). But in our graph, we have stored both directions.

In BFS, we traverse each edge only once per direction? Actually, we traverse each node only once. So if we visit a node, we set its coordinate and then traverse all its neighbors. Since the graph is undirected (because we added both directions), we must avoid processing an edge twice. But with a visited array, we avoid processing the same node again.

But what if the graph has cycles? We don't traverse a node twice. So we don't check the consistency of the cycle? But the problem says the information is consistent, so we don't need to validate? However, it's better to validate? But the problem says "The given information is consistent", so we can assume that if we ever get a node with two different paths, they should give the same coordinate. But since we are setting the coordinate the first time we visit, we don't check later. So we don't validate? 

But the problem doesn't require us to check for consistency (it says the input is consistent). So we can just do a BFS without checking.

But to be safe, if we ever come across a node that already has a coordinate and we are going to assign a different one, we could have a problem. But since the input is consistent, that should not happen. So we can skip the check.

However, for debugging, we might want to check. But for performance, we can skip.

But wait: what if the same node is reached by two different paths? We don't process it again because we mark it visited. So we never recalculate. That means we rely on the first assignment. But if the graph is consistent, the first assignment is the only one and the other path would have given the same. So we don't need to check.

So algorithm:

- Create an array `res` (or `coords`) of size (N+1) for the coordinates, initialized as None. Also, a list for the graph: `graph = [[] for _ in range(N+1)]`

- For each relation (A, B, X, Y):
    - Append to graph[A]: (B, X, Y)
    - Append to graph[B]: (A, -X, -Y)

- Create a queue (collections.deque). Start from node 1: set coords[1] = (0,0), and enqueue node 1.

- While queue is not empty:
    - Pop a node u.
    - For each neighbor (v, x, y) in graph[u]:
        - If coords[v] is None: 
            - Set coords[v] = (coords[u][0] + x, coords[u][1] + y)
            - Enqueue v.

- Then, for i from 1 to N:
    - If coords[i] is not None: output the coordinate as string: f"{x} {y}"
    - Else: output "undecidable"

But wait: what if there are multiple edges between the same pair? For example, Example 3: multiple relations between the same pair. How does that affect?

In the input of Example 3, we have:

1 2 0 0
1 2 0 0   -> duplicate? 
Then 2 3 0 0
3 1 0 0
2 1 0 0   -> which is the same as 1 2 0 0? Actually, 2 1 0 0: from person2, person1 is at (0,0). But that is the same as from person1, person2 is at (0,0): which implies they are at the same location. So the reverse edge is (0,0) -> but we already have that.

When building the graph, we add:

For (1,2,0,0): 
   graph[1].append( (2,0,0) )
   graph[2].append( (1,0,0) )   # because we add the reverse: -0,-0 is (0,0)

Similarly, the next line is the same: we add the same edges again.

But then we have (2,1,0,0): which is the same as the reverse edge we already added? So we add again: 
   graph[2].append( (1,0,0) )   # but we already have one? 
   graph[1].append( (2,0,0) )

So we have multiple edges. But when we traverse, we will set the coordinate of node2 the first time we process node1 and the edge (1,2). Then, when we process node2, we will see the edge (2,1) to node1, but node1 is already visited, so we skip. Similarly, when we process node1, we have multiple edges to node2? We will try to set node2 multiple times? But the first time we set it, then the next time we see the same edge, we skip because node2 is visited.

So duplicate edges don't cause a problem.

But what if we have two different edges between the same two nodes? For example, two different relations for the same pair (A, B)? 

But the problem states the input is consistent. So if we have two different relations for (A, B), they must be the same? Or they must be consistent? Actually, the problem says "The given information is consistent", meaning that there are no contradictions. So we can assume that for the same pair (A, B), if we have two relations, they are identical? Or if they are different, the input is not provided that way? 

Looking at the constraints: "All input values are integers" and "The given information is consistent". So we can assume that for any pair, the relative positions are consistent? Actually, the problem doesn't say that the same pair might appear multiple times, but in Example 3, it does. And in that case, the relations are the same: (0,0). So we can assume that if the same pair appears multiple times, the (X,Y) are the same? Or if they are different, then it's inconsistent? But the problem states the information is consistent, so we can assume that duplicate edges are either identical or not present? 

But the problem says: "The given information is consistent." So we don't have to worry about conflicting edges.

Therefore, the above algorithm should work.

But wait: what about the direction? When we add an edge (A, B, X, Y), we add an edge from A to B: (X,Y) and from B to A: (-X,-Y). Then, when we traverse from A to B, we set B's coordinate as A's plus (X,Y). When we traverse from B to A, we set A's coordinate as B's plus (-X,-Y). But if we set B from A, then when we go from B to A, we see A is already set, so we don't set again. And the value for A would be (B_x - X, B_y - Y) = ( (A_x+X) - X, (A_y+Y)-Y) = (A_x, A_y), which is consistent. So no problem.

Now, what about the performance? We have N and M up to 200,000. BFS is O(N+M). So that's acceptable.

But what about memory? We are storing the graph: each edge is stored twice (once in each direction). So total edges in the graph: 2*M. So memory is O(N+M), which is acceptable.

So the plan:

1. Initialize:
   - coords = [None] * (N+1)   # 1-indexed, so index0 unused.
   - graph = [[] for _ in range(N+1)]

2. For i in range(M):
      A, B, X, Y = relations[i]
      graph[A].append( (B, X, Y) )
      graph[B].append( (A, -X, -Y) )

3. Use BFS (or DFS) starting from node1.
   - queue = deque([1])
   - coords[1] = (0,0)
   - while queue:
        u = queue.popleft()
        for each neighbor in graph[u]:
            v, dx, dy = neighbor
            if coords[v] is None:
                coords[v] = (coords[u][0] + dx, coords[u][1] + dy)
                queue.append(v)

4. For i from 1 to N:
      if coords[i] is not None:
          output f"{coords[i][0]} {coords[i][1]}"
      else:
          output "undecidable"

Let me test with Example 1:

Relations: 
(1,2,2,1) -> 
   graph[1]: append (2,2,1)
   graph[2]: append (1,-2,-1)

(1,3,-1,-2) ->
   graph[1]: [(2,2,1), (3,-1,-2)]
   graph[3]: [(1,1,2)]   # because -(-1)=1, -(-2)=2? Wait: no.

Wait, for (1,3,-1,-2):
   from 1 to 3: (-1,-2)
   from 3 to 1: (1,2)   [because we add (3,1, -(-1), -(-2)) = (1,2)? Actually: 
        We have (A=1, B=3, X=-1, Y=-2)
        So for A=1: (B=3, X=-1, Y=-2) -> add to graph[1]: (3,-1,-2)
        For B=3: (A=1, -X = -(-1)=1, -Y=-(-2)=2) -> so graph[3].append( (1,1,2) )

BFS:

Start: node1 -> (0,0)

From node1: 
   neighbors: (2,2,1) and (3,-1,-2)
   Set node2: (0+2,0+1) = (2,1)
   Set node3: (0-1,0-2) = (-1,-2)

Then from node2: 
   neighbors: (1,-2,-1) -> but node1 is visited, skip.
   Any other? None.

Then from node3:
   neighbors: (1,1,2) -> node1 visited, skip.

So we get:
1: (0,0)
2: (2,1)
3: (-1,-2)

Matches.

Example 2:

Relations:
(2,1,-2,-1) -> 
   graph[2]: (1,-2,-1) [direct]
   graph[1]: (2,2,1)   [reverse: -(-2)=2, -(-1)=1]

(2,3,-3,-3) ->
   graph[2]: [(1,-2,-1), (3,-3,-3)]
   graph[3]: [(2,3,3)]   [because -(-3)=3, -(-3)=3]

BFS:

Start node1: (0,0)

From node1: neighbors: (2,2,1) -> set node2: (0+2,0+1) = (2,1)

Then from node2: 
   neighbors: (1,-2,-1): visited, skip.
             (3,-3,-3): set node3: (2-3, 1-3) = (-1,-2)

Then from node3: 
   neighbors: (2,3,3): then set? but node2 is already set. So skip.

Output: same as Example1.

Example 3:

Relations: 7 edges, but many duplicates.

We have:

1 2 0 0 (twice) -> 
   graph[1]: [ (2,0,0), (2,0,0) ]   # two identical edges? 
   graph[2]: [ (1,0,0), (1,0,0) ]

2 3 0 0 -> 
   graph[2]: [ (1,0,0), (1,0,0), (3,0,0) ]
   graph[3]: [ (2,0,0) ]

3 1 0 0 -> 
   graph[3]: [ (2,0,0), (1,0,0) ]   # because from 3 to 1: (0,0) -> so reverse: from1 to3: (0,0)? Actually, no: 
        For (3,1,0,0): 
            graph[3].append( (1,0,0) )
            graph[1].append( (3,0,0) )   # because reverse: -0, -0 = (0,0)

Similarly, 2 1 0 0: 
   graph[2].append( (1,0,0) )   [already there]
   graph[1].append( (2,0,0) )   [already there]

3 2 0 0: 
   graph[3].append( (2,0,0) )   [already there? because we have (3,2,0,0) -> but we already have (2,3,0,0) which added (3,0,0) to graph[2] and (2,0,0) to graph[3]? Actually, no: 
        The edge (3,2,0,0) is a new edge? But it's the same as (2,3,0,0) in reverse? Actually, (2,3,0,0) was already processed and we added the reverse? So this new edge (3,2,0,0) is the same as the reverse of (2,3,0,0) which we already added? 

But in the input, we have:

4 5 0 0: 
   graph[4]: [ (5,0,0) ]
   graph[5]: [ (4,0,0) ]

Now, BFS:

Start at node1: (0,0)

From node1: 
   neighbors: (2,0,0) and (2,0,0) [duplicate] and (3,0,0) [from the edge (3,1,0,0) we added (1,3,0,0) in graph[1]? Actually, the edge (3,1,0,0) was processed and we added to graph[1] an edge (3,0,0). Also the edge (1,2,0,0) added (2,0,0) twice? 

So we have multiple edges from node1: two to node2 and one to node3? 

But when we set node2: 
   the first time: (0+0,0+0) = (0,0) -> so set and enqueue.
   Then the second edge to node2: we see node2 is already set, so skip.

Similarly, for node3: set to (0+0,0+0) = (0,0) and enqueue.

Then from node2: 
   neighbors: (1,0,0) [visited], (1,0,0) [visited], (3,0,0) -> set node3? but node3 is already set to (0,0). So we skip.

Then from node3: 
   neighbors: (2,0,0) [visited], (1,0,0) [visited]

So we have set nodes 1,2,3 to (0,0). Nodes 4 and 5 are unvisited, so we output "undecidable" for them.

Perfect.

So the code should work.

But what about the case where there is a long chain? For example, 1 connected to 2, 2 connected to 3, 3 connected to 4, etc. BFS will traverse the entire chain.

Also, what about a tree? Same.

And if there are cycles? We skip already visited nodes, so it's safe.

So we'll implement using BFS.

But note: we might have multiple edges? But the algorithm handles them: we only set the coordinate the first time we visit a node.

One more test: if we have a relation that gives a new path to an already visited node? 

For example, after we set node3 via 1->3, then we have a path 1->2->3. When we go from node2 to node3, we check: 
   coords[3] is already set to (0,0) (from the direct edge). Then we skip. We don't check if (2+0, 2+0) equals (0,0). But we should. But the problem says the input is consistent, so we don't have to. However, if we want to be safe, we could check and if it's different, we have a contradiction. But the problem says it's consistent, so we skip.

But to be safe for debugging, we can add an assert? But the constraints say it's consistent, so we don't need in production. We can skip.

But the problem says "The given information is consistent", so we don't have to check.

So we'll not include the check.

Now, code:

We are given:

N, M and a list of relations: each is (A, B, X, Y)

We'll use collections.deque for BFS.

Steps:

1. Initialize:
   from collections import deque
   coords = [None] * (N+1)   # 0-indexed, index0 unused, index1 to N
   graph = [[] for _ in range(N+1)]

2. For each relation in relations:
      A, B, X, Y = relation
      graph[A].append( (B, X, Y) )
      graph[B].append( (A, -X, -Y) )

3. q = deque()
   coords[1] = (0, 0)
   q.append(1)

4. while q:
        u = q.popleft()
        for (v, dx, dy) in graph[u]:
            if coords[v] is None:
                coords[v] = (coords[u][0] + dx, coords[u][1] + dy)
                q.append(v)

5. Prepare the output: for i from 1 to N:
        if coords[i] is not None:
            output.append(f"{coords[i][0]} {coords[i][1]}")
        else:
            output.append("undecidable")

Return the output list.

But note: the coordinates can be as low as -10^9 and as high as 10^9? So we are just adding integers. Python integers are fine.

Let me test with a larger example: a chain of 4 nodes.

Input: 
4 3
1 2 10 20
2 3 5 10
3 4 1 1

Then:
Person1: (0,0)
Person2: (10,20)
Person3: (10+5, 20+10) = (15,30)
Person4: (15+1, 30+1) = (16,31)

But the relations:

1->2: (10,20)
2->3: (5,10) -> so from 2, 3 is at (5,10): so 3 = (10+5,20+10)=(15,30)
3->4: (1,1) -> 4 = (15+1,30+1)=(16,31)

Now, the graph:

For (1,2,10,20):
   graph[1]: (2,10,20)
   graph[2]: (1,-10,-20)

For (2,3,5,10):
   graph[2]: (1,-10,-20), (3,5,10)
   graph[3]: (2,-5,-10)

For (3,4,1,1):
   graph[3]: (2,-5,-10), (4,1,1)
   graph[4]: (3,-1,-1)

BFS:

Start: 1 -> (0,0)
From 1: to 2: set (0+10,0+20) = (10,20) -> enqueue 2.

Then from 2: 
   to 1: visited, skip.
   to 3: set (10+5,20+10)=(15,30) -> enqueue 3.

Then from 3:
   to 2: visited -> skip.
   to 4: set (15+1,30+1)=(16,31) -> enqueue 4.

Then from 4: 
   to 3: visited -> skip.

Output: 
1: 0 0
2: 10 20
3: 15 30
4: 16 31

Correct.

Now, what if we have a different path to a node? But the problem says consistent, so we don't have to worry. But just for illustration, suppose we have:

3 3
1 2 10 20
1 3 15 30
2 3 5 10

This should be consistent? Let's see:

From 1:2 is (10,20), 3 is (15,30)
From 2:3 is (5,10): so 3 should be at (10+5,20+10)=(15,30) -> matches.

So when we traverse:

Start at 1: set 1=(0,0)
Then from 1: set 2=(10,20) and 3=(15,30)

Then from 2: we see 3, but 3 is already set, so skip.

Then from 3: we see 1 and 2, both visited.

So no problem.

Therefore, we can implement the BFS.

But what if the graph is disconnected? Then the nodes not connected to 1 remain as None, and we output "undecidable".

That matches the requirement.

So I think we are ready.

Let me write the code accordingly.

But note: we are using BFS, but DFS would work too. Since the graph is unweighted, either is fine. But BFS is better to avoid recursion depth? With 200,000 nodes, DFS recursion might hit recursion limit. So BFS with iterative queue is better.

We'll use collections.deque for BFS.

Now, code implementation:

We'll use:

import collections

class Solution:
    def solve(self, N: int, M: int, relations: List[Tuple[int, int, int, int]]) -> List[str]:
        # Initialize graph and coords
        graph = [[] for _ in range(N+1)]
        coords = [None] * (N+1)   # 1-indexed: index 0 unused, 1..N
        
        # Build graph: for each relation, add two directed edges.
        for a, b, x, y in relations:
            graph[a].append((b, x, y))
            graph[b].append((a, -x, -y))
        
        # BFS starting from node 1
        q = collections.deque()
        coords[1] = (0, 0)
        q.append(1)
        
        while q:
            u = q.popleft()
            for v, dx, dy in graph[u]:
                if coords[v] is None:
                    coords[v] = (coords[u][0] + dx, coords[u][1] + dy)
                    q.append(v)
        
        # Prepare output for each person from 1 to N
        res = []
        for i in range(1, N+1):
            if coords[i] is not None:
                res.append(f"{coords[i][0]} {coords[i][1]}")
            else:
                res.append("undecidable")
        return res

Let me run the examples.

Example 1: 
N=3, M=2, relations: [(1,2,2,1), (1,3,-1,-2)]

graph[1]: [(2,2,1), (3,-1,-2)]
graph[2]: [(1,-2,-1)]
graph[3]: [(1,1,2)]

coords[1] = (0,0)
Then from 1: 
   v=2: set coords[2] = (0+2,0+1)=(2,1) -> enqueue 2
   v=3: set coords[3] = (0-1,0-2)=(-1,-2) -> enqueue 3
Then from 2: 
   neighbors: (1,-2,-1): coords[1] is set -> skip.
Then from 3:
   neighbors: (1,1,2): skip.

Output: 
   i=1: "0 0"
   i=2: "2 1"
   i=3: "-1 -2"

Example 2: 
Relations: [(2,1,-2,-1), (2,3,-3,-3)]

graph[2]: [(1,-2,-1), (3,-3,-3)]
graph[1]: [(2,2,1)]
graph[3]: [(2,3,3)]

BFS:
Start: coords[1] = (0,0) -> enqueue 1
From 1: 
   graph[1]: (2,2,1) -> set coords[2] = (0+2,0+1)=(2,1) -> enqueue 2
From 2: 
   (1,-2,-1): skip
   (3,-3,-3): set coords[3] = (2-3,1-3)=(-1,-2) -> enqueue 3
From 3: 
   (2,3,3): skip because coords[2] is set.

Output: same.

Example 3: 
N=5, M=7, relations: 
(1,2,0,0) twice, (2,3,0,0), (3,1,0,0), (2,1,0,0), (3,2,0,0), (4,5,0,0)

We'll build graph:

For each relation, we add two edges.

After processing:

graph[1]: 
   from (1,2,0,0): (2,0,0) [twice? yes, two times]
   from (3,1,0,0): (3,0,0) [because for (3,1,0,0): graph[1].append( (3,0,0) )? 
        Actually: for (3,1,0,0): 
            graph[3].append( (1,0,0) )
            graph[1].append( (3, -0, -0) ) -> (3,0,0)   [since -0 is 0]
   from (2,1,0,0): (2,0,0) [again] -> so graph[1] has [ (2,0,0), (2,0,0), (3,0,0), (2,0,0) ]? 
        Actually: 
            (1,2,0,0): added (2,0,0) to graph[1] and (1,0,0) to graph[2]? 
            then (1,2,0,0) again: same.
            then (2,1,0,0): added (1,0,0) to graph[2] and (2,0,0) to graph[1]? -> so that's the third (2,0,0) for graph[1].
            then (3,1,0,0): added (1,0,0) to graph[3] and (3,0,0) to graph[1].
            then (3,2,0,0): added (2,0,0) to graph[3] and (3,0,0) to graph[2]? 

But we don't care about duplicates.

BFS:

Start: coords[1] = (0,0) -> enqueue 1.

Then from 1: 
   For each edge: 
        to 2: set (0+0,0+0) = (0,0) -> enqueue 2 (if not set). Then next edge to 2: skip. Then edge to 3: set (0+0,0+0)=(0,0) -> enqueue 3. Then next edge to 2: skip.

Then from 2: 
   graph[2] has: 
        from (1,2,0,0): (1, -0, -0) = (1,0,0) -> skip because visited.
        from (1,2,0,0) again: same.
        from (2,3,0,0): (3,0,0) -> but 3 is set? skip.
        from (2,1,0,0): (1,0,0) -> skip.
        from (3,2,0,0): (3,0,0) -> skip? because we added an edge from 2 to 3: (3,0,0) and also the reverse? Actually, the edge (3,2,0,0) was processed: 
             graph[2].append( (3,0,0) )? and also graph[3].append( (2,0,0) )? 
        But we already set 3 from the edge from 1 to 3.

Then from 3: 
   edges: 
        from (3,1,0,0): (1,0,0) -> skip.
        from (2,3,0,0): (2,0,0) -> skip? because we have an edge from 3 to 2: (2,0,0) -> but 2 is visited.
        from (3,2,0,0): (2,0,0) -> skip.

Then we have nodes 4 and 5: unvisited.

So output: 
1: "0 0"
2: "0 0"
3: "0 0"
4: "undecidable"
5: "undecidable"

But wait, the output for person2 and person3 is "0 0", which is correct.

However, the sample output says:

0 0
0 0
0 0
undecidable
undecidable

So we output for person1:0 0, person2:0 0, person3:0 0, person4:undecidable, person5:undecidable. 

But our code for person2 and person3: we output "0 0", which is correct.

But the sample output has three lines: 
0 0
0 0
0 0
undecidable
undecidable

So we are good.

Now, one more edge case: when M=0. Then we have no relations.

Then, only person1 is at (0,0), and all others are undecidable.

Example: N=3, M=0.

Output:
0 0
undecidable
undecidable

Which is correct.

So the code should be correct.

Let me code accordingly.

Note: we are using a list for graph and coords of size N+1. Index 0 is unused. This is acceptable.

Time complexity: O(N+M) for BFS and building the graph.

Space: O(N+M) for the graph and the queue.

We'll implement.

But note: the coordinates can be negative, so we are adding integers. That's fine.

We'll write the code as described.

I think that's the solution.

## Final Solution

### Approach
The problem involves determining the coordinates of N people on a plane based on M relative position statements. Person 1 is fixed at the origin (0, 0). Each statement provides that, from person A's perspective, person B is at (X, Y). This implies that if A is at (x_a, y_a), then B is at (x_a + X, y_a + Y). The goal is to compute the absolute coordinates for each person if uniquely determinable; otherwise, report "undecidable".

#### Key Insight
The problem can be modeled as a graph where each person is a node, and each relative position statement is a directed edge from A to B with a vector (X, Y). The reverse edge from B to A will naturally have the vector (-X, -Y). The absolute coordinate of any person can be determined if and only if they are in the same connected component as person 1. For nodes in this component, we can propagate coordinates starting from person 1 using BFS or DFS. Nodes not reachable from person 1 remain undecidable.

#### Algorithm Selection
1. **Graph Construction**: For each relation (A, B, X, Y), add two directed edges:
   - From A to B: (X, Y)
   - From B to A: (-X, -Y)
2. **BFS Traversal**: Start from person 1 at (0, 0). For each neighbor of the current node, compute the neighbor's coordinates as the current node's coordinates plus the edge vector. If the neighbor hasn't been visited, enqueue it.
3. **Result Compilation**: After BFS, for each person, if their coordinate was computed, output it; otherwise, output "undecidable".

#### Complexity Analysis
- **Time Complexity**: O(N + M) for graph construction and BFS traversal, which is optimal given the constraints (N, M ≤ 2 × 10^5).
- **Space Complexity**: O(N + M) to store the graph and coordinates.

### Solution Code
```python
from collections import deque
from typing import List, Tuple

class Solution:
    def solve(self, N: int, M: int, relations: List[Tuple[int, int, int, int]]) -> List[str]:
        graph = [[] for _ in range(N+1)]
        coords = [None] * (N+1)
        
        for a, b, x, y in relations:
            graph[a].append((b, x, y))
            graph[b].append((a, -x, -y))
        
        q = deque()
        coords[1] = (0, 0)
        q.append(1)
        
        while q:
            u = q.popleft()
            for v, dx, dy in graph[u]:
                if coords[v] is None:
                    coords[v] = (coords[u][0] + dx, coords[u][1] + dy)
                    q.append(v)
        
        res = []
        for i in range(1, N+1):
            if coords[i] is not None:
                res.append(f"{coords[i][0]} {coords[i][1]}")
            else:
                res.append("undecidable")
                
        return res
```

### Explanation
1. **Graph Construction**: The graph is built using adjacency lists. For each relation (A, B, X, Y), an edge from A to B with vector (X, Y) and a reverse edge from B to A with vector (-X, -Y) are added.
2. **BFS Initialization**: Start BFS from person 1, initialized at (0, 0). The BFS queue processes each node, propagating coordinates to neighbors using the edge vectors.
3. **Coordinate Propagation**: For each neighbor, if its coordinates haven't been set, they are computed as the current node's coordinates plus the edge vector. The neighbor is then enqueued for further processing.
4. **Result Generation**: After BFS, each person's coordinate is checked. If set, the coordinates are formatted into a string; otherwise, "undecidable" is appended to the result list. The result list is returned, providing the solution for all N people.

This approach efficiently propagates known coordinates through the graph, leveraging BFS to handle large input sizes within feasible time and space complexity.