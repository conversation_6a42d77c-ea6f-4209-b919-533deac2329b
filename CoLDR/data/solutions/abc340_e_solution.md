# Solution for abc340_e.md

Generated at: 2025-07-22 06:55:51
Model: deepseek-reasoner

## Reasoning Process

Alright, so I'm trying to solve this problem about distributing balls in boxes. Let me read the problem again and understand what it's asking.

We have N boxes numbered from 0 to N-1. Each box i starts with A_i balls. Then, we perform M operations. In each operation i, we do the following:

1. Set a variable C to 0.
2. Take out all the balls from box B_i (so that box becomes empty, and we're holding all the balls that were in it).
3. While we have at least one ball in hand, repeat:
   - Increase C by 1.
   - Put one ball from hand into box (B_i + C) mod N.

So, for each ball taken from box B_i, we put it into a sequence of boxes starting from (B_i + 1) mod N, then (B_i + 2) mod N, and so on, until we've distributed all the balls.

The problem constraints are that N and M can be up to 200,000, and the initial A_i can be as large as 10^9. So, we can't simulate each operation step-by-step because that would be too slow. For example, if one operation takes out 10^9 balls, we can't loop 10^9 times.

I need to find an efficient way to compute the final state of the boxes.

Let me try to understand with the example:

Example 1: N=5, M=3, A = [1,2,3,4,5], B = [2,4,0]

Operation 1: B1 = 2
- Take all balls from box 2, which has 3 balls. Now we hold 3 balls, and box 2 becomes 0.
- Then, we distribute:
  - C=1: put one ball in (2+1)%5 = box 3
  - C=2: put one ball in (2+2)%5 = box 4
  - C=3: put one ball in (2+3)%5 = box 0
- After op1: boxes become: [1+1=2, 2, 0, 4+1=5, 5+1=6]? Wait, no: initial was [1,2,3,4,5]. After removing box2 (which is index 2, so the third box) we have:
  Box0:1, Box1:2, Box2:0, Box3:4, Box4:5.
  Then we add:
    First ball: box (2+1)%5=3 → Box3 becomes 5
    Second ball: box (2+2)%5=4 → Box4 becomes 6
    Third ball: box (2+3)%5=0 → Box0 becomes 2
  So after op1: [2,2,0,5,6]

But wait, the output after all operations is [0,4,2,7,2]. So I must be missing the subsequent operations.

Operation 2: B2=4
- Take all balls from box4, which has 6 balls. So we hold 6 balls, set box4 to 0.
- Then distribute:
  C=1: (4+1)%5=0 → box0 gets one
  C=2: (4+2)%5=1 → box1 gets one
  C=3: (4+3)%5=2 → box2 gets one
  C=4: (4+4)%5=3 → box3 gets one
  C=5: (4+5)%5=4 → box4 gets one? But wait, we are in the loop until we have no balls left. So after C=5, we have distributed 5 balls, but we have 6. Then:
  C=6: (4+6)%5= (10 mod5)=0 → box0 gets another
  So we put two in box0, one each in boxes1,2,3, and one in box4? But we set box4 to 0 at the beginning, so after putting one in box4, it becomes 1? Then after op2:

  Current state: [2,2,0,5,0] → then take 6 from box4? Actually, after op1 we had [2,2,0,5,6]. So taking from box4: 6 balls.

  After distribution:
    Box0: 2 (from before) + 2 (from C=1 and C=6) = 4
    Box1: 2 + 1 = 3
    Box2: 0 + 1 = 1
    Box3: 5 + 1 = 6
    Box4: 0 + 1 (from C=5) = 1? But wait, the last one was put at C=6 which is box0, so after C=5 we put one in box4 (so box4 becomes 1), then C=6: box0 gets another.

  So after op2: [4,3,1,6,1]

But that doesn't match the example. The example output after all operations is [0,4,2,7,2]. So I must be messing up.

Wait, let me redo the operations step by step with the example.

Initial: [1,2,3,4,5]

Operation 1: i=1, B1=2
- Take all from box2: so we take 3 balls. Box2 becomes 0.
- Then distribute:
  C=1: (2+1)%5=3 → box3: 4+1=5
  C=2: (2+2)%5=4 → box4: 5+1=6
  C=3: (2+3)%5=0 → box0: 1+1=2
- So after op1: [2,2,0,5,6]

Operation 2: i=2, B2=4
- Take all from box4: 6 balls. Box4 becomes 0.
- Distribute 6 balls:
  C=1: (4+1)%5=0 → box0: 2+1=3
  C=2: (4+2)%5=1 → box1: 2+1=3
  C=3: (4+3)%5=2 → box2: 0+1=1
  C=4: (4+4)%5=3 → box3: 5+1=6
  C=5: (4+5)%5=4 → box4: 0+1=1
  C=6: (4+6)%5=0 → box0: 3+1=4
- So after op2: [4,3,1,6,1]

But the example output is [0,4,2,7,2] after three operations. So we have one more operation.

Operation 3: i=3, B3=0
- Take all from box0: 4 balls. Box0 becomes 0.
- Distribute 4 balls:
  C=1: (0+1)%5=1 → box1: 3+1=4
  C=2: (0+2)%5=2 → box2: 1+1=2
  C=3: (0+3)%5=3 → box3: 6+1=7
  C=4: (0+4)%5=4 → box4: 1+1=2
- So after op3: [0,4,2,7,2] which matches the example.

So the operations are clear. But how to do this efficiently? Because M and N are up to 200,000 and the number of balls in a box can be huge (up to 10^9), we cannot simulate each distribution step by step.

I need to find a way to update the state of the boxes without iterating each ball.

Let me think about what happens in one operation:

Suppose we have a box `b` (from B_i) that has `k` balls. We remove all `k` balls and then distribute them one by one to boxes:

box (b+1) mod N, (b+2) mod N, ... until we've placed `k` balls.

So the starting box is `b+1` (mod N) and we put one ball in each consecutive box until we've done `k` boxes. But if `k` is larger than N, we wrap around multiple times.

Actually, the distribution is:

- The first ball goes to (b+1) mod N
- The second to (b+2) mod N
- ...
- The k-th ball goes to (b+k) mod N.

So we are effectively adding 1 to a contiguous set of boxes in a circular array? But not exactly contiguous because of modulus. Also, note that we might wrap around the array.

So, if we let `start = (b+1) % N`, then we are adding 1 to the boxes from `start` to `min(N-1, start + k - 1)`, and then if `start + k - 1 >= N`, we wrap and add from 0 to `(start + k - 1) % N`.

But wait, it's more like:

We are adding 1 to a contiguous segment (in circular fashion) of `k` boxes starting at `(b+1) % N`.

But actually, the boxes we add to are:

For j=1 to k: box (b + j) mod N.

So if we define:

start = (b+1) % N

Then we are adding 1 to the next k boxes starting from `start`, wrapping around.

However, we can break this into one or two segments:

- If the segment does not wrap around: from `start` to `start + k - 1` (if `start + k - 1 < N`)
- If it wraps: from `start` to `N-1`, and then from `0` to `(start + k - 1) % N`.

But note: (start + k - 1) % N is the last box, but if we wrap, then the last box is (start + k - 1) - N? Actually, modulo arithmetic: the last index is (b + k) mod N? But we are going from j=1 to k: (b+j) mod N.

So the last box is (b+k) mod N? Actually, no: the last box is (b + k) mod N, but the boxes we update are from (b+1) mod N to (b+k) mod N? Not exactly: for j=1: (b+1), j=2: (b+2), ... j=k: (b+k). Then we mod N.

So the set of boxes is the contiguous segment from (b+1) mod N to (b+k) mod N? But in a circular array, contiguous? Actually, yes, because we are increasing by 1 each time. So it's a contiguous circular segment.

But note: if k >= N, then we wrap around multiple times? Actually, no: because modulo N, the boxes are repeated. For example, if k = 2*N, then we add 2 to every box? Let me check:

Suppose N=3, b=0, k=6: then we distribute 6 balls:

j=1: (0+1)%3=1
j=2: (0+2)%3=2
j=3: (0+3)%3=0
j=4: (0+4)%3=1
j=5: (0+5)%3=2
j=6: (0+6)%3=0

So we add 2 to each box? Because each box gets two balls.

In general, if we have k balls, then:

- Each box gets floor(k / N) balls as a base, because we go around full cycles.
- Then, we have r = k % N remaining balls to distribute: which will be the next r boxes starting from (b+1) mod N.

So, the operation can be decomposed as:

1. Remove all balls from box b: set it to 0? Actually, no: because we are taking all the balls, so the box becomes 0. But note: during the distribution, we might put balls back into box b? Only if in the distribution, we hit box b? For example, if we have to put a ball in (b + j) mod N = b when j is a multiple of N? But in the operation, we start j from 1. So we don't put a ball back in box b until we've distributed at least N balls? Because (b + N) mod N = b.

So, in the operation:

- We set the current value of box b to 0 (because we take all balls out).
- Then we add k (the number of balls taken) as follows:

  We add floor(k / N) to every box. Because every full cycle adds 1 to each box.

  Then, we have r = k % N balls to distribute. These r balls are distributed to r consecutive boxes starting from (b+1) mod N.

But note: if r=0, then we just add floor(k/N) to every box and that's it.

However, what if r>0? We add 1 to each of the r consecutive boxes starting at (b+1) mod N, and then we also have the base floor(k/N) for every box.

But wait: the base floor(k/N) is added to every box, including box b? But we set box b to 0? Actually, no: because when we remove the k balls from box b, we set it to 0, but then we add floor(k/N) to every box, including box b. So after the operation, box b will have floor(k/N) (unless we also add the remainder?).

But in the remainder distribution, we start from (b+1) mod N, so we don't add to box b in the remainder part. So the net effect for box b is: we set it to 0, then add floor(k/N). So it ends up with floor(k/N).

But let me test with a small example: N=3, b=0, k=3.

Initial: box0 has 3, others 0.

Operation: take box0: set to 0. Then k=3, so floor(3/3)=1, so we add 1 to every box. Then r=0. So after: [1,1,1]. But wait, according to the process:

We take 3 balls from box0 -> box0=0.
Then distribute:
  j=1: (0+1)%3=1 -> add1 to box1 -> 1
  j=2: (0+2)%3=2 -> add1 to box2 -> 1
  j=3: (0+3)%3=0 -> add1 to box0 -> 1
So [1,1,1]. And with the method: base = 1 for all, and r=0 -> same.

Now, if k=4: 
We take 4 balls from box0 -> set to 0.
Then base = floor(4/3)=1, so we add 1 to every box. Then r=1: we add 1 to the next r=1 boxes starting from (0+1)%3=1. So box1 gets an extra 1 -> total for box1: 1 (base) +1 =2. Others: box0:1, box2:1? But let's simulate:

j=1: box1:1
j=2: box2:1
j=3: box0:1
j=4: box1:2 (because (0+4)%3=1)
So [1,2,1]. Which matches: base 1 for all, then extra 1 to box1.

So the operation can be broken down as:

1. Let k = current value of box b (we are going to remove this).
2. Set box b to 0? But then we add base (floor(k/N)) to every box, including b, so effectively we add base to box b? But wait, we set it to 0 and then add base: so it becomes base. But in the distribution, we also add base to every other box.

But in the remainder, we add 1 to r consecutive boxes starting from (b+1) mod N.

However, note: when we set box b to 0, and then we add base to every box, including b, so box b becomes base. Then, the remainder distribution does not touch box b? Because we start at (b+1) mod N and go for r boxes, which doesn't include b? Only if r < N - (b+1) mod N? Actually, no: if r is large, it might wrap and then we might add to box b? But the remainder distribution starts at (b+1) mod N and goes for r boxes. The set of boxes is the next r boxes after b (including (b+1) mod N, but excluding b).

But note: the remainder distribution does not include box b because we start at (b+1) mod N and then the next r boxes. Since we are going in order, we don't hit b until we've done at least (N-1) steps? For example, if we start at (b+1) mod N and then we do r boxes, we go from (b+1) to (b+r) mod N. Now, if r >= N, then we wrap, but we already handled the full cycles. So r is less than N. Therefore, the segment of r boxes does not include b? Because the segment is from (b+1) to (b+r) mod N, which is a contiguous set of r boxes that does not include b (since b is at the starting point? Actually, if we start at b+1 and go r steps, the last box is (b+r) mod N. Since r < N, then (b+r) mod N is not b? Because to get to b, we would need to go from b+1 to b, which requires going around the entire array, but r < N, so we never wrap back to b.

Wait: if b is 0, then we start at 1, and then we go to 1,2,...,r. If r= N-1, then we go to boxes 1,2,...,N-1. Then if r = N, we would have a full cycle, but r is modulo N, so r=0. So with r in [0, N-1], the segment does not include b.

Therefore, the effect of an operation on the array is:

- base = k // N
- r = k % N
- We add base to every box.
- Then, we add 1 to a contiguous segment of r boxes starting from (b+1) mod N.

But note: we also set box b to 0? Actually, we set it to 0 when we take the balls, but then we add base to every box, including b. So the net effect for box b is base (because 0 + base). But what if we had to add to box b in the remainder? We don't, as we established. So box b becomes base? However, in the example operation above, when we took the balls, we set box b to 0, and then we added base to every box, so box b becomes base. Then the remainder adds 1 to a segment that doesn't include b. So yes.

But wait: what about the balls that were in the other boxes? We are updating the entire array by adding base, and then a segment of r ones. But we also set the box b to base? Actually, we set it to 0 and then add base: that's base. But the other boxes: they were not set to 0? So we are adding base to every box, including the ones that have their own values.

But in the example, we didn't set any other box to 0. Only box b is set to 0 at the beginning of the operation.

So the operation:

- We have an array A[0..N-1]
- For an operation on box b:
   k = A[b]   # the current value at box b
   A[b] = 0   # we take the balls out
   base = k // N
   r = k % N

   Then, we do:
      For every box i: A[i] += base
      Then, we do a range update: from (b+1) mod N to (b+r) mod N, add 1 to each.

But note: the segment might wrap around. For example, if b = N-1, then (b+1) mod N = 0. And if r is 2, then we add 1 to boxes 0 and 1.

So we have two cases:

Case 1: The segment does not wrap: if (b+1) + r - 1 < N, i.e., if b + r < N? Then the segment is [b+1, b+r] (both inclusive).

Case 2: It wraps: then the segment is [b+1, N-1] and [0, (b+r) mod N - 1]? Actually, (b+r) mod N = (b+1 + r - 1) mod N? Actually, the segment is of length r, starting at (b+1) mod N. So if (b+1) + r <= N, then the segment is [b+1, b+r] (inclusive). Otherwise, it wraps: [b+1, N-1] and [0, (b+1 + r - 1) % N] = [0, (b+r) % N]? But note: (b+1 + r - 1) = b+r. Then mod N: but if we wrap, then the segment from b+1 to N-1 has length (N - (b+1)), and the rest (r - (N - (b+1))) is from 0 to (r - (N - (b+1)) - 1? Actually, the total length is r.

Alternatively, we can do:

   start = (b+1) % N
   end = (b + r) % N   # but note: if we start at s and go r steps, the last index is (s + r - 1) mod N? Actually, no: the boxes are s, s+1, ..., s+r-1 mod N.

Wait, in the distribution, the boxes are:

   s0 = (b+1) % N
   s1 = (b+2) % N
   ...
   s_{r-1} = (b+r) % N   ??? 

But: for j=1: (b+1) mod N
      j=2: (b+2) mod N
      ...
      j=r: (b+r) mod N

So we are adding 1 to the boxes: (b+1) mod N, (b+2) mod N, ..., (b+r) mod N.

But note: these are r consecutive boxes. However, (b+r) mod N might be less than (b+1) mod N if we wrap. So we cannot represent it as a single contiguous interval in the array without wrap.

But we can break it into two intervals:

- From (b+1) to (b+r) if (b+r) < N: then one segment [b+1, b+r]
- Else: 
   first part: [b+1, N-1]
   second part: [0, (b+r) % N]   but note: (b+r) mod N = (b+r) - N? Because b+r >= N.

But wait: if b+r >= N, then the segment is [b+1, N-1] and [0, b+r - N]. And the length is (N - (b+1)) + (b+r - N + 1) = (N - b - 1) + (r - (N - b - 1))? Actually, the total length should be r.

Alternatively, we can note that the set of indices is:

   [b+1, b+2, ..., N-1, 0, 1, ..., (b+r) mod N]

But (b+r) mod N = b+r - N, because b+r >= N and < 2N? (since r < N, so b+r < N + N = 2N, and b < N, so b+r < 2N).

So the second segment is from 0 to (b+r) - N - 1? Or to (b+r) - N? Actually, the last box is (b+r) mod N = b+r - N. But we start at 0 and go to b+r - N? Actually, the boxes we update are:

   From b+1 to N-1: that's (N - (b+1)) boxes.
   Then from 0 to (b+r) mod N: that is, 0 to (b+r - N). But how many? The number of boxes from 0 to (b+r - N) inclusive is (b+r - N) + 1.

Total boxes: (N - (b+1)) + (b+r - N + 1) = (N - b - 1) + (b + r - N + 1) = r.

Yes.

But note: if r=0, then no boxes are updated in the remainder.

So the operation becomes:

   k = A[b]
   base = k // N
   r = k % N

   # Step 1: set A[b] to 0? But then we add base to all, so effectively we will add base to A[b] and every other box.
   # Instead, we can do:
   #   We are going to add base to every box. But then we also need to add the remainder to specific boxes.

   However, note: after setting A[b] to 0, we add base to every box, including b. Then we add the remainder. But the remainder does not include b. So for box b: becomes base.

   For the rest, we do:

   if r > 0:
        start = (b+1) % N
        if start + r <= N:
            then update [start, start + r - 1] by 1? But note: we are adding to each box in that interval 1.
        else:
            update [start, N-1] by 1
            update [0, start + r - N - 1] by 1? 

Wait, no: the boxes are (b+1), (b+2), ..., (b+r) mod N. But if we break:

        The first segment: from start to N-1: that's length = N - start
        Then the rest: r - (N - start) = r - N + start
        Then the second segment is from 0 to (r - N + start - 1)? Because we start at 0 and go for (r - N + start) boxes? 

But the last box we update is (b+r) mod N = (start + r - 1) mod N? Actually, no: the last box is (b+r) mod N. But note:

   The first box: (b+1) mod N = start
   The last box: (b+r) mod N = (start + r - 1) mod N? 

Wait, let me check:

   b, then start = b+1 (if b+1 < N) or 0 if b+1==N? 
   Then the next boxes: start+1, ... until we have r boxes.

But the j-th box (1-indexed) is (b+j) mod N.

So for j=r: (b+r) mod N.

But the sequence of indices is:

   start, start+1, ..., N-1, 0, 1, ... until we have r boxes.

So the last index is (start + r - 1) % N? Actually, no: the last index is (b+r) mod N = (start + r - 1) mod N? 

Wait: j=1: index = start = (b+1) mod N
j=2: start+1
...
j = (N - start): index = N-1
j = (N - start + 1): index = 0
j = (N - start + 2): index = 1
...
So the last index = (start + r - 1) mod N? 

But (start + r - 1) mod N = (b+1 + r - 1) mod N = (b+r) mod N.

Yes, so the last box is (start + r - 1) mod N? Actually, no: because we are adding 1 to each step, the last box is (start + r - 1) mod N? But wait: if we start at start and take r steps, the last index is start + r - 1 (mod N). 

But in the example: b=0, r=2: then start = 1, then last index = 1 + 2 - 1 = 2? But we have two boxes: j=1:1, j=2:2? Then 2 mod N=2. But if N=3, then 2 is valid.

But in the example above with b=4, N=5, r=6%5=1? Actually, in the first example operation on box2: k=3, so r=3. Then start = (2+1)=3. Then we update from 3 to 3+3-1=5? 5 mod5=0? But we updated boxes 3,4,0. But 3,4,0: that's three boxes. The last box is (2+3)=5 mod5=0. But if we compute (start + r - 1) mod N = (3+3-1)=5 mod5=0, which is correct.

So the segment is from start to (start + r - 1) mod N. But to avoid mod, we can break the segment:

   If start + r - 1 < N:
        segment: [start, start+r-1]
   Else:
        segment1: [start, N-1]
        segment2: [0, (start+r-1) % N]   which is [0, start+r-1 - N]

But note: (start + r - 1) % N = start + r - 1 - N, because start + r - 1 >= N and < 2N? (since r < N, so start + r - 1 < N + N - 1 = 2N-1).

So the two segments:

   [start, N-1] and [0, start + r - 1 - N]

But the second segment goes to start+r-1-N, inclusive? Because we break at the wrap.

So the operation:

   base = k // N
   r = k % N

   Add base to all boxes.

   Then, if r > 0:
        s = (b+1) % N   # start
        e = (s + r - 1) % N   # but we don't need e for the break, we need to check if s + r - 1 >= N.

        Actually, we can compute:

        if s + r <= N:
            update segment [s, s+r-1] by 1
        else:
            update [s, N-1] by 1
            update [0, s + r - 1 - N] by 1   # because from s to N-1: that's (N - s) boxes, then we need to update the next (r - (N - s)) boxes starting at 0.

        But note: the last index in the segment is s + r - 1. If s + r - 1 >= N, then the second part: from 0 to (s + r - 1 - N) is (s + r - 1 - N + 1) = (s + r - N) boxes? Actually, the number of boxes in the second segment is r - (N - s). And the last index in the second segment is (r - (N - s)) - 1? But we are updating from 0 to (s + r - 1 - N) inclusive? That's (s + r - N) boxes? Because 0 to x (inclusive) has x+1 boxes. But we need to update (r - (N - s)) boxes? 

        Actually, the first segment: from s to N-1: that's (N - s) boxes.
        The second segment: from 0 to (s + r - 1 - N) inclusive? That's (s + r - 1 - N) - 0 + 1 = s + r - N.

        And (N - s) + (s + r - N) = r, which is correct.

        So we update [0, s + r - N - 1]? No: because the last index is s + r - 1 - N, and we update from 0 to that index. But if we update from 0 to (s + r - 1 - N) inclusive, that is (s + r - N) boxes.

        Alternatively, we can note: the second segment starts at 0 and ends at (s + r - 1) mod N, which is s + r - 1 - N. So the segment is [0, s + r - 1 - N]. 

        But we can also represent it as [0, (s + r - 1) % N] which is the same.

        However, to avoid confusion, we can just use two segments:

            Segment 1: [s, min(N-1, s+r-1)]   and then if we didn't cover all, segment2: [0, (s + r - 1) % N]? Actually, that's not correct because the second segment should start at 0 and go for (r - (N - s)) boxes? So the last index is (r - (N - s)) - 1? 

        Actually, the second segment has length = r - (N - s), so the indices are from 0 to (r - (N - s)) - 1? 

        But we already know that the last box is (s + r - 1) mod N = s + r - 1 - N. So the segment is [0, s + r - 1 - N]? 

        Example: N=5, s=3, r=3: then the segment is [3,4] (because 3 to 4) and then the next one is [0, 3+3-1-5 = 0]? So [0,0]. Then total boxes: 2 (from [3,4]) and 1 (from [0,0]) -> 3, which is correct.

        So the update is:

            [3,4] and [0,0]? 

        But in the example, we updated boxes 3,4,0. So yes.

        Therefore, the algorithm for one operation:

            k = A[b]   # current value at box b
            base = k // N
            r = k % N

            # We set A[b] = base? But wait: we are going to add base to every box, including b. But we just took the balls from b, so we set it to 0? Then we add base to every box: so A[b] becomes base. Then we add the remainder to the other boxes? But note: the remainder update does not include b.

            So:

            # Step 1: set A[b] to base? Actually, no: because we are going to add base to every box. So we can do:

            #   We add base to every box. Then, if r>0, we add 1 to the two segments.

            But note: we must remember that we are going to update the entire array by adding base. So we can maintain:

                Let's have an array D of length N, initially zeros, that represents the current state.

                But we are going to do M operations, each of which does:

                   base = k // N
                   r = k % N

                   # Then we add base to every element. Then we add 1 to a contiguous segment (possibly two segments) for the remainder.

            However, if we do that naively, adding base to every box in each operation would be O(N) per operation, leading to O(M*N) which is 200000*200000 = 40e9, too slow.

So we need to optimize.

We can use a Fenwick tree or segment tree for range updates? But we have M operations, each updating the entire array by adding base (which is an O(1) global add?) and then updating two segments for the remainder (each update is O(1) with lazy propagation?).

But note: the base is added to every box. So we can maintain a global variable `total_base` that accumulates the base from all operations? Then at the end, each box i will have A_initial[i] + total_base + the remainder updates that affected it.

But wait: the base in each operation is different and depends on the current value at the box we are processing. So we cannot precompute a global base because the base for each operation is computed at the time of the operation and depends on the current state.

So we need to simulate the operations in order, and the base for each operation is the value we just took from the box divided by N.

But we cannot update every box for every operation because that would be O(N*M).

Alternative idea:

We can note that the entire operation only sets one box to zero (and then we add base to all, which we can handle by a global variable) and then we update two segments for the remainder.

But the catch: the value we take from a box is the current value at that box at the time of the operation. And the current value is the initial value plus all the base adds from previous operations and the remainder updates from previous operations.

So we need to know, at the time of operation j, the current value at box b_j.

This suggests that we need to maintain an array that we update as we go.

We can use a Fenwick tree or segment tree that supports point queries and range updates. Then:

   We maintain an array `ans` (or use a Fenwick tree) that holds the current number of balls in each box.

   For each operation i:
        b = B_i   (the box index for this operation)
        k = query(b)   # get the current value at box b
        base = k // N
        r = k % N

        Then, we need to:
            # Set box b to base? Actually, we are going to:
            #   Subtract k from box b? Because we take the balls. Then add base to every box? But then add 1 to the remainder segment.

        Instead, we can break it:

            We are going to set box b to base? But then we add base to every box? Actually, the base add is to every box, so we can do:

                Update the entire array by adding base? That's a global update. But then we set box b to base? But if we add base to every box, then box b becomes (current_value + base) and then we set it to base? So we need to subtract the base that was added to box b? That's messy.

        Alternatively, we can avoid the global update by updating the remainder segments and the box b update separately.

        Here's a better idea:

            The effect of the operation is:

                A[b] becomes base   (because we set it to 0 then add base to all, but we can do: after taking the balls, we set it to base? Then we don't do a global add? But no: we are adding base to every box, not just b.

            Actually, we must add base to every box. So we can do:

                We maintain a global variable `global_add` that accumulates the base from all operations? Then, at the end, we add `global_add` to every box. But the base from each operation is different and we need to update the current state for the operations.

        But note: the base in an operation is added to every box at the time of the operation. So if we do:

            global_base = 0
            Then for each operation:
                current_value = A_initial[b] + global_base + (sum of remainder updates that hit box b from previous operations)   ??? 

        But we are updating the array as we go, so the current_value at box b includes the global_base and the previous remainder updates.

        And we do:

            k = current_value
            base = k // N
            r = k % N

            Then we set box b to base? But then we add base to global_base? Because every box will get base? But then the other boxes will get base from this operation via global_base at the end? 

        However, for the current operation, we set box b to base. But the global_base will be added to every box at the end? So box b at the end will be base + global_base? That's not right.

        Alternatively, we can maintain the array without the global_base, and then at the end add the accumulated global_base.

        But during the operations, the value we read at box b must include the global_base? 

        Let me define:

            Let `base_total` be the sum of all base values from previous operations.

            Then the current value at box i is: 
                V[i] = A_initial[i] + base_total + F[i] 
            where F[i] is the sum of the remainder updates (the ones that add 1) that affected box i.

        Now, at an operation:

            b = B_i
            k = V[b] = A_initial[b] + base_total + F[b]

            Then we compute base = k // N, r = k % N.

            Then we add this base to base_total? So base_total_new = base_total + base.

            But then, we need to set V[b] to base? But after the operation, the box b becomes base? However, note that in the operation we set box b to 0 and then add base to every box, including b. So it becomes base. But also, the base we just added to base_total will be added to every box at the end? But wait, we haven't finished processing, so we are going to use the new base_total in the next operations.

            However, for the current state, we set V[b] to base. But base is the new value without the base_total? 

        Actually, after the operation, the value at box b should be base (because we set it to 0 and then added base to every box). But base is a value that does not include the base_total? 

        Let me write the current value at box b at the beginning of the operation: 
            V = A_initial[b] + base_total_old + F[b]

        Then we do:

            base = V // N
            r = V % N

            Then we set box b to 0 + base? And then we are going to add base to every box? But that base will be added to every box, including b. So the new value for box b becomes base (from the set) + base (from the global add)? = 2*base? That's not right.

        I think I'm confusing the order.

        Actually, the operation:

            Step 1: set box b to 0. Then the value at box b is 0.
            Step 2: add base to every box. Then box b becomes base.

            Then we add the remainder: which doesn't affect box b.

        So the final value at box b is base.

        But in our formulation, we have:

            V[b] = A_initial[b] + base_total_old + F[b]   (at the start of the operation)

        Then we set it to base. And then we add base to every box? But then we are going to add base to base_total for future operations, so at the end, we will add base_total (which includes this base) to every box.

        That means that at the end, box b will be base (from the set) + base_total (which includes this base) = base + (base_total_old + base) = base_total_old + 2*base.

        But it should be base.

        How to resolve?

        We need to separate the current state from the future global adds.

        Idea:

            We maintain:

                base_total: the sum of all base values from previous operations.

                F: an array for the remainder updates (only the ones that add 1) from previous operations.

            Then, at the time of an operation, the current value at box i is: 
                V[i] = A_initial[i] + base_total + F[i]

            But after the operation, we are going to add base to base_total, so base_total_new = base_total + base.

            And then we update F for the remainder: we add 1 to the remainder segments.

            But for box b, we need to set it to base. But after the operation, the value at box b should be base. However, in our expression, after the operation, the value at box b should be:

                V[b] = A_initial[b] + base_total_new + F_new[b]

            But we have:

                base_total_new = base_total + base
                F_new[b] = F_old[b]   # because the remainder update of this operation doesn't touch b

            So we require:

                base = A_initial[b] + (base_total + base) + F_old[b]

            => 0 = A_initial[b] + base_total + F_old[b] 

            But initially, V[b] = A_initial[b] + base_total + F_old[b] = k, and we set it to base. This equation is not holding.

        This indicates that we cannot easily separate the current value into initial, base_total, and F.

        Alternative approach:

            We use a Fenwick tree or segment tree that supports:
                - Point query: to get the current value at a box at the time of the operation.
                - Range update: to add a value to a contiguous segment.

            And we also need to set a point to a specific value? But note: during the operation, we set box b to 0 and then later we add base to every box and the remainder to specific boxes. But instead of doing a global add, we can update the remainder segments and the box b individually.

            Actually, we can simulate the operation with:

                # Step 1: get the current value at b: k = tree.get(b)
                base = k // N
                r = k % N

                # Step 2: set tree[b] = base   [because after we take the balls, we will add base to every box, but then we also need to add the remainder to other boxes; but note the remainder does not include b, so tree[b] should be base]

                But then we need to add base to every other box? That's O(N).

            Or, we can do:

                tree.set(b, base)   # this sets box b to base.

                Then, we need to add base to every box? But that's O(N). 

                And then add 1 to the remainder segments? 

                But wait, we already set box b to base, which is (0 + base). Then if we add base to every box, we will be adding base again to every box, including b? Then box b becomes base + base, and others become current_value + base + (remainder updates).

            That's not right.

        After thinking, I found a better approach:

            We can delay the global add. Instead, we maintain a separate global variable `g` that is the amount to be added to every box from the base of previous operations. But then the current value at a box i is: tree.get(i) + g.

            For an operation:

                b = current box
                k = tree.get(b) + g   # current value at b
                base = k // N
                r = k % N

                # Now, we set tree[b] to -g + base? 
                # Because: we want after the operation, the value at b to be base. 
                # But after the operation, when we add g (from previous global adds) and then we will add base (this operation's base) to every box at the end of the operation? 

                Actually, after this operation, we will add this base to the global variable, so the new global variable will be g + base.

                And we set tree[b] to a value such that when we add g (previous global) and then add base (this base) to every box, tree[b] + g + base = base? 
                    => tree[b] + g + base = base  => tree[b] = -g.

                But then, after the operation, the value at b is tree[b] + (g+base) = -g + g + base = base. That's correct.

                Then, we need to add 1 to the remainder segments. And we also need to add base to the global variable for future operations.

                So:

                    tree.update(b, -g - tree.get(b))   # set tree[b] to -g? But we can do tree.set(b, -g)

                    Then, we add 1 to the two segments for the remainder.

                    Then, we update the global variable: g = g + base.

            Then, after all operations, we do:

                For each box i: 
                    result[i] = tree.get(i) + g

            But we also need to account for the remainder updates during the operations.

            However, the remainder updates are range updates on the tree. And we do them immediately.

            Also, the initial tree should be initialized with the initial A: 
                tree.set(i, A[i])   for each i.

            And g starts at 0.

            Let me test with the small example: N=3, b=0, k=3.

            Initial: A = [3,0,0], g=0.

            Operation:
                b=0
                k = tree.get(0)+g = 3+0 = 3
                base = 3//3 = 1
                r = 0
                Then, set tree[0] = -g = 0? But we want after adding g (which will become 1) we have 0+1=1? But then after the operation, the value should be 1.

                But then we do g = g + base = 0+1=1.

                Then, we update the remainder: r=0, so nothing.

                So after op: tree = [0,0,0], g=1.

                Then for box0: 0+1=1, box1:0+1=1, box2:0+1=1. Correct.

            Another example: b=0, k=4, N=3.

                Initial: tree=[4,0,0], g=0.
                k=4
                base=4//3=1, r=1.
                Set tree[0] = -g = 0.
                Then update the remainder: r=1, start=(0+1)%3=1. 
                    Update: add 1 to [1,1] (since 1+1-1=1 <3)
                So tree becomes: [0,1,0]
                Then g = 0+1=1.

                Then after op: 
                    box0:0+1=1
                    box1:1+1=2
                    box2:0+1=1
                But earlier we wanted [1,2,1]? But we have [1,2,1] -> correct.

            But wait, the initial tree was set to the initial A, and then we did the set and the range update.

            Now, let's do the first operation of the example: 

                N=5, M=3, A = [1,2,3,4,5], B = [2,4,0]

                Initial: tree = [1,2,3,4,5], g=0.

                Operation1: b=2
                    k = tree[2] + g = 3+0=3
                    base=3//5=0, r=3
                    Set tree[2] = -g = 0.
                    Then, remainder: start = (2+1)%5=3, r=3.
                        Since 3+3-1=5 >=5? 3+3=6>5 -> so we break:
                            segment1: [3,4] -> add1 to indices 3 and 4.
                            segment2: [0, 3+3-1-5 = 0]? so [0,0] -> add1 to index0.
                    So tree becomes: 
                        0: 1 (initial) +1 (update) = 2? But we set tree[0] = 1 initially, then update: we add1 to index0 -> becomes 1+1=2? 
                        But also, we set tree[2] to 0.
                        So after update: 
                            tree[0]=1+1=2? 
                            tree[1]=2 (unchanged)
                            tree[2]=0 (set)
                            tree[3]=4+1=5
                            tree[4]=5+1=6
                    Then g = 0+0=0.

                    State after op1: tree = [2,2,0,5,6], g=0.

                Operation2: b=4
                    k = tree[4] + g = 6+0=6
                    base=6//5=1, r=1
                    Set tree[4] = -g = 0.
                    Then, remainder: start=(4+1)%5=0, r=1.
                        So update [0,0] by 1.
                    Then tree[0] becomes 2+1=3.
                    Then g = 0+1=1.

                    State: tree = [3,2,0,5,0], g=1.

                Operation3: b=0
                    k = tree[0]+g = 3+1=4
                    base=4//5=0, r=4
                    Set tree[0]= -g = -1.
                    Then, remainder: start=(0+1)%5=1, r=4.
                        1+4-1=4 <5? -> so update [1,4] (inclusive) by 1.
                    So tree[1]=2+1=3, tree[2]=0+1=1, tree[3]=5+1=6, tree[4]=0+1=1.
                    Then g = 1+0=1.

                After all operations, for each box i: tree[i] + g:
                    box0: -1+1=0
                    box1:3+1=4
                    box2:1+1=2
                    box3:6+1=7
                    box4:1+1=2
                Which is [0,4,2,7,2] -> matches.

            So the plan:

                We'll use a Fenwick tree or a segment tree that supports:
                    - point update (set a point to a value) -> but we can also do: get the current value and then update by (new_value - old_value), but that's two operations.
                    - range update (add a value to a range) for two segments possibly.

                However, Fenwick tree and segment tree support range updates and point queries.

                We also need point updates (setting a point to a value) and range updates (adding a value to a range).

                We can do:

                    For setting a point i to a value v:
                        current = tree.get(i)
                        tree.update(i, v - current)

                But we can also support point updates directly.

                Alternatively, we can use a Fenwick tree that supports point updates and range updates? Actually, a Fenwick tree typically supports point updates and range queries. But here we need range updates and point queries? Because at the time of the operation, we need the value at a point (box b), and we do range updates for the remainder.

                Fenwick tree for range updates and point queries:

                    We can use a Fenwick tree with difference array.

                We can maintain an array `arr` and a Fenwick tree `fenw` that represents the difference array, so that:

                    point query: at index i: arr[i] + fenw.prefix_sum(i)

                    range update [l, r] by x: 
                         fenw.update(l, x)
                         fenw.update(r+1, -x)   if r+1 < N, but if it wraps, we need to handle it.

                But in our case, we also have a global variable g that is added at the end.

                And we do point updates (like setting a point to -g). That can be done by:

                    current_val = arr[i] + fenw.prefix_sum(i)
                    desired_val = -g
                    diff = desired_val - current_val
                    then update the point i by diff: 
                         we can do fenw.update(i, diff) and then fenw.update(i+1, -diff) ??? 
                    Or, we can update the point i to the desired value by:

                         We know the current value from the tree is stored in `arr`? Or we can avoid `arr` by using the Fenwick tree for all.

                Actually, we can maintain the current array without the global add. And then at the end, we add g to every box.

                But during the operations, we need to know the current value at a box (without g) to compute the next operation.

                How about we store the current state in a data structure that supports:

                  - point query: get(i) -> returns the current value at i (without g)
                  - point update: set(i, v) -> sets the current value at i to v (without g)
                  - range update: add(l, r, x) -> adds x to all elements from l to r (inclusive)

                We can use a Fenwick tree that supports range updates and point queries? 

                Actually, a Fenwick tree for range updates and point queries is not standard. Standard Fenwick tree does point updates and range queries.

                Alternatively, we can use a Fenwick tree that is set up for point queries by storing the array as the difference array. But then range updates are efficient, and point queries are O(1) if we maintain the array separately? 

                Or we can use a Fenwick tree that supports point queries by doing a prefix sum? 

                Let me recall:

                    We maintain an array `bit` for the Fenwick tree.

                    To do a range update [l, r] by x:
                         update(l, x)
                         update(r+1, -x)

                    Then the point query at i is the prefix sum up to i.

                But that is for a Fenwick tree that is used for range queries and point updates? 

                Actually, if we want to support:

                    range update: add x to [l, r]
                    point query: get(i)

                We can do:

                    We maintain a Fenwick tree `fenw` (initialized to zeros) and an array `base` (the initial array).

                    Then the value at i is: base[i] + fenw.prefix_sum(i)

                So we can:

                    base = initial A.
                    fenw = FenwTree(N)

                    Then:
                        point query i: base[i] + fenw.prefix_sum(i)
                        range update [l, r] by x: 
                             fenw.update(l, x)
                             if r+1 < N: fenw.update(r+1, -x)

                But what if the range wraps? We break the range update into two segments.

                For the remainder update:

                    If we have a contiguous segment [l, r] that does not wrap: 
                         update [l, r] by 1: 
                             fenw.update(l, 1)
                             if r+1 < N: fenw.update(r+1, -1)

                    If it wraps: [l, N-1] and [0, r']:
                         segment1: [l, N-1]:
                             fenw.update(l, 1)
                             # no need to update at N because index out of bounds? So if we have an array of size N, indices 0..N-1, then for the first segment, we do:
                             fenw.update(l, 1)
                             fenw.update(N, -1)   but we don't have index N? So we can avoid if r+1 is out of bounds.

                         segment2: [0, r']:
                             fenw.update(0, 1)
                             if r'+1 < N: fenw.update(r'+1, -1)

                Alternatively, we can note that the Fenwick tree is defined for indices 0..N-1, so we don't store beyond that. So for the first segment [l, N-1]: we update l by 1 and that's it? Because the Fenwick tree prefix_sum(i) for i>=l will get the 1, and we don't need to subtract at N because it's beyond.

                But that's not correct: because if we do:

                    fenw.update(l, 1)

                Then the prefix_sum(i) for i>=l will be increased by 1. But we only want to increase from l to N-1. So without subtracting at N, the prefix_sum for i>=l will all be increased by 1, which is what we want for the segment [l, N-1]? 

                Similarly, for [0, r']: we update 0 by 1 and then update r'+1 by -1 (if r'+1 < N).

                But then the entire array from l to N-1 gets 1 added? And then from 0 to r' also gets 1 added? 

                So the value at an index i is base[i] + fenw.prefix_sum(i).

                For i in [0, r']: 
                    base[i] + (prefix_sum(i)) 
                    prefix_sum(i) includes the 1 we added at index0 and then subtracted at r'+1? So for i<=r': prefix_sum(i) = 1 (if no other updates) because we added at 0 and nothing subtracted until r'+1.

                For i in [l, N-1]: 
                    prefix_sum(i) includes the 1 at l, so it's 1.

                For i in [r'+1, l-1] (if any) in the middle: 
                    prefix_sum(i) = 0? 
                But we didn't update there, so that's correct.

                So the updates for the remainder:

                  Case1: no wrap: l = start, r = start+r-1 (if start+r-1 < N)
                         fenw.update(l, 1)
                         if r+1 < N: fenw.update(r+1, -1)

                  Case2: wrap: 
                         segment1: [l, N-1]  (l = start)
                             fenw.update(l, 1)
                             # no update at N because it's beyond, so we don't subtract.

                         segment2: [0, r'] where r' = (start + r - 1) % N = start + r - 1 - N
                             fenw.update(0, 1)
                             if r'+1 < N: fenw.update(r'+1, -1)

                But note: the second segment we update [0, r']: we do:
                    fenw.update(0,1)
                    if r'+1 < N: fenw.update(r'+1, -1)

                However, if r' = N-1, then r'+1 = N, which is out of bounds, so we skip.

            Steps for one operation:

                b = current box index
                k = base[b] + fenw.prefix_sum(b)   # current value at b (without g)
                base_val = k // N
                r = k % N

                # Set box b to -g (for the tree) so that at the end when we add g, it becomes base_val? 
                # But we have: 
                #   We want after the operation, the value at b to be base_val.
                #   And at the end of all operations, the final value is (tree_value) + g.
                #   But at the time of setting, we set the tree value at b to x such that at the end: x + g = base_val? 
                #   But during the operation, we are going to add the current base_val to the global g at the end of the operation.

                # Actually, after we set, we will then update the global g: g = g + base_val.
                #   So at the end, the value at b is: x + (g + base_val) = base_val.
                #   => x + g + base_val = base_val  => x = -g.

                # So we set the tree at b to -g.

                How to set the tree at b to -g?

                    We can do:

                        current_val_tree = base[b] + fenw.prefix_sum(b)   # but base[b] is the initial value? and we have been doing range updates? 

                    Actually, in our Fenwick tree setup, we have:

                        value(i) = base_arr[i] + fenw.prefix_sum(i)

                    So to set the value at b to -g, we need:

                        base_arr[b] + fenw.prefix_sum(b) = -g

                    But we cannot change base_arr arbitrarily? 

                Alternatively, we can update the tree to adjust the value at b to -g.

                We can do a point update at b by:

                    current = base_arr[b] + fenw.prefix_sum(b)
                    diff = -g - current
                    Then, we can update the Fenwick tree at b by adding diff? But that would affect the prefix sums for indices >=b.

                But we can use a Fenwick tree that supports point updates? 

                Actually, we can avoid the base_arr by storing the current array in a separate array `arr` that we update explicitly. Then we don't use base_arr? 

                Let me change:

                    We maintain:
                       `arr` for the current state without g.
                       a Fenwick tree for range updates? Actually, if we do range updates by updating the Fenwick tree, then the point query would be arr[i] + fenw.prefix_sum(i) -> but that's not standard.

                Alternatively, we can use a Fenwick tree that supports point updates and point queries, and then we do the range update by iterating? But that's O(N).

            Given the complexity, maybe a Fenwick tree for range updates and point queries is not the best. We can use a lazy segment tree that supports range updates and point queries and point updates.

            But M and N are 200000, and we have M operations, each requiring:

                - 1 point query
                - 1 point update (set)
                - 2 range updates (each O(log N))

            So total O(M * log N) which is acceptable.

            However, we can also use a Fenwick tree for range updates and point queries? 

            Standard technique:

                We maintain a Fenwick tree `fenw` for the difference array for range updates and point queries.

                But we also need to support point updates (setting to a specific value). 

            Actually, we can do without a separate base_arr if we incorporate the point updates into the Fenwick tree. But we already have the Fenwick tree for the range updates. How to set a point to a value?

                Let `arr` be the current array (without g). We maintain `arr` separately and update it when we do a point set. But then the range updates are stored in the Fenwick tree? 

                Or, we can maintain the entire current array in a Fenwick tree that supports both point updates and range updates? 

            I think using a segment tree with lazy propagation is overkill. 

            Alternatively, we can use two Fenwick trees: one for the range updates and one for the point updates? 

            But simpler: we can use an array `val` for the current value without g, and then we do the range updates by updating a difference array and then reconstructing the point? But that would be O(N) per operation.

            Another idea: use a Fenwick tree that supports point updates and range queries, and then for range updates, we do a loop? That's O(N).

            Since each operation might update up to two segments of total length r (which can be large, up to N-1), and M is 200000, worst-case total update length is O(M*N) = 40e9, too slow.

            So we need a data structure that can do range updates in O(log N) time.

            Therefore, we use a Fenwick tree or segment tree that supports:

                - range add update (add a value to a contiguous segment) in O(log N)
                - point update (set a value) in O(log N)
                - point query in O(log N)

            We can use a Fenwick tree for the range add and point query? 

            Yes, using the standard technique:

                We maintain a Fenwick tree `fenw` for the difference array.

                Let `diff` be the difference array: 
                    diff[0] = arr[0]
                    diff[i] = arr[i] - arr[i-1] for i>=1

                Then, to update [l, r] by x:
                    diff[l] += x
                    if r+1 < N: diff[r+1] -= x

                Then the point query at i: 
                    prefix_sum(i) of the diff array.

                But we need to do point updates: set index i to v.

                How?

                    current = prefix_sum(i)
                    We want to set it to v, so we need to change the diff array such that the prefix_sum(i) becomes v.

                    But note: prefix_sum(i) = diff[0] + diff[1] + ... + diff[i]

                    We can update diff[0] to change the entire prefix? That would be messy.

                Alternatively, we can have a separate Fenwick tree for the initial array? 

            After research, I found that we can do:

                We maintain a Fenwick tree `fenw` that supports range add and point query.

                For point set at i to v:

                    current = point_query(i)   # current value
                    add = v - current
                    Then we do a point update at i: 
                         range_update [i, i] by add   -> which is: 
                             update(i, add)
                             if i+1 < N: update(i+1, -add)

                That works.

            So we can use a Fenwick tree that supports:

                __init__(n)
                update(i, delta): add delta to index i in the diff array -> then update the Fenwick tree accordingly.
                range_update(l, r, delta): 
                    update(l, delta)
                    if r+1 < N: update(r+1, -delta)

                point_query(i): return the prefix sum up to i.

            Actually, the Fenwick tree for the diff array is built for point queries: we store the diff array in a Fenwick tree, then the point query is the prefix sum of the diff array.

            So we can implement a Fenwick tree that stores the diff array and supports:

                add(i, delta): add delta to the diff array at i -> then update the Fenwick tree at i by delta.

                prefix_sum(i): returns the prefix sum of the diff array up to i.

                Then, to do a range update [l, r] by delta:
                    add(l, delta)
                    if r+1 < N: add(r+1, -delta)

                Then the point query at i is prefix_sum(i).

            And for point set: 
                current = prefix_sum(i)
                add = v - current
                Then we do add(i, add)   -> which affects the point query at i and beyond? But we want only at i? 

                Actually, if we do:

                    add(i, add)
                    # then the value at i becomes current + add = v.
                    # but the values at indices > i also get increased by add? 

                That is not what we want.

            To set a point i to v without affecting others, we need to:

                current = prefix_sum(i)
                add = v - current
                Then we do:
                    add(i, add)
                    add(i+1, -add)   # to cancel the effect for indices > i

            But this is a range update [i, i] by add? 

            So we can define:

                def point_set(self, i, v):
                    current = self.point_query(i)
                    diff = v - current
                    self.range_update(i, i, diff)

            Then, we can implement range_update as above.

            So the Fenwick tree class:

                class Fenw:
                    def __init__(self, n):
                        self.n = n
                        self.tree = [0]*(n+1)

                    def update(self, index, delta):
                        # index from 0 to n-1
                        i = index + 1
                        while i <= self.n:
                            self.tree[i] += delta
                            i += i & -i

                    def prefix_sum(self, index):
                        # prefix sum [0, index]
                        s = 0
                        i = index+1
                        while i:
                            s += self.tree[i]
                            i -= i & -i
                        return s

                    def range_update(self, l, r, delta):
                        self.update(l, delta)
                        if r+1 < self.n:
                            self.update(r+1, -delta)

                    def point_query(self, index):
                        return self.prefix_sum(index)

                    def point_set(self, index, v):
                        current = self.point_query(index)
                        diff = v - current
                        self.range_update(index, index, diff)

            But note: the point_query is the prefix_sum of the diff array, which is the value at the index? 

            Actually, in the standard difference array, the array A is:

                A[0] = diff[0]
                A[i] = diff[0]+diff[1]+...+diff[i]

            So yes, the prefix_sum(i) is the value at i.

            Therefore, we can use this Fenw tree.

            Steps for one operation:

                b = B_i

                # Query the

## Final Solution

