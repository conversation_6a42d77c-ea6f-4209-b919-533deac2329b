# Solution for abc307_e.md

Generated at: 2025-07-22 06:20:23
Model: deepseek-reasoner

## Reasoning Process

Alright, I'm trying to solve this problem where we have N people arranged in a circle, and we need to assign each person an integer from 0 to M-1 such that no two adjacent people have the same integer. The challenge is to count the number of valid assignments modulo 998244353, with N and M as large as 10^6.

First, I recall that if the circle were a line (i.e., not circular), the problem would be straightforward. For a line of N people, the number of ways would be:
- First person: M choices
- Each subsequent person: M-1 choices (since they can't have the same as the previous one)
So total for a line: M * (M-1)^(N-1)

But because it's a circle, the first and last persons are also adjacent. That complicates things. So, I need a different approach for the circular arrangement.

I remember that one common method for circular arrangements is to use inclusion-exclusion or to fix one person's value and then solve for the rest. But with constraints up to 10^6, I need an efficient solution.

Let me think about using dynamic programming (DP). However, a naive DP would be O(N*M), which is O(10^12) for the worst case, so that won't work. I need a more mathematical approach.

Another idea is to use the inclusion-exclusion principle. The total number of assignments without restrictions is M^N. Then, subtract the assignments where at least one adjacent pair (including the last and first) are the same. But because of overlapping constraints (adjacent pairs share neighbors), inclusion-exclusion might get messy.

Alternatively, I recall that for circular arrangements with two constraints (first and last), we can use a recurrence or generating functions. But I need a formula that can be computed quickly.

I remember that the number of ways to color a circle of N nodes with M colors such that no two adjacent are the same is:
- (M-1)^N + (-1)^N * (M-1)

Wait, let me verify that with small examples.

For N=3, M=3:
Using the formula: (3-1)^3 + (-1)^3*(3-1) = 8 - 2 = 6. That matches the example.

For N=4, M=2:
(2-1)^4 + (-1)^4*(2-1) = 1^4 + 1*1 = 1+1=2. Also matches.

So, is the general formula: (M-1)^N + (-1)^N * (M-1) ?

But wait, let me check another case. Suppose N=2, M=2. Then, the valid assignments are: (0,1) and (1,0). So total 2.

Using the formula: (2-1)^2 + (-1)^2*(2-1) = 1 + 1 = 2. Correct.

But what if M=1? Then, for any N>=2, it should be 0 because the only color is the same for everyone, so adjacent will be same. But the formula: (1-1)^N + (-1)^N*(1-1) = 0 + 0 = 0. That works.

So the formula seems to be: 
- If we fix the first person, then the next ones have constraints, and the last one has constraints relative to both the first and the previous.

But actually, the formula I recalled is for the circular arrangement. But let me confirm the derivation.

I think the standard derivation is:

Let a_n be the number of valid colorings for a circle of n nodes. Then, we can relate it to the line coloring.

For a line of n nodes, the number is M*(M-1)^(n-1). For the circle, we can fix the first node, then the second, third, ..., and the last node must be different from both the first and the (n-1)th.

Alternatively, we can use recurrence:

Consider two cases for the circle:
1. If the first and the (n-1)th nodes have different colors, then the last node has (M-2) choices.
2. If they have the same color, then the last node has (M-1) choices? But wait, if the first and the (n-1)th are the same, then the last node must be different from that color, so (M-1) choices. But how to count the arrangements where the first and (n-1)th are same?

Actually, we can define:
- Let a_n be the number of valid colorings for a circle of n nodes.
- Let b_n be the number of valid colorings for a line of n nodes where the first and last have different colors? Or maybe:

I remember a recurrence:

a_n = (M-1)^n + (-1)^n * (M-1)

But how to derive it?

Alternatively, we can use the formula from combinatorics: 

a_n = (M-1)^n + (-1)^n (M-1)

But wait, the problem is that the formula I have is actually for the case where the circle condition is the only constraint. And it matches the examples.

So, for the circle, the number of ways is: (M-1)^N + (-1)^N * (M-1)

But wait, no: let me double-check the formula. I think the correct formula for coloring a circle of N nodes with M colors, no two adjacent same, is:

a_N = (M-1)^N + (-1)^N * (M-1)

But when I plug in N=3, M=3: 2^3 + (-1)^3 * 2 = 8 - 2 = 6. Correct.

But actually, I recall that the standard formula is:

a_N = (M-1)^N + (-1)^N * (M-1)

But wait, I think I have seen:

a_N = (M-1)^N + (-1)^N (M-1)

Yes, that seems consistent.

But wait, another common derivation is:

The number of ways = (M-1)^N + (-1)^N * (M-1)

But actually, I think I need to adjust: the total for the circle is:

We can also express it as: (M-1) * (-1)^(N-1) + (M-1)^N? 

No, the formula I have is the same as above.

But let me derive it.

We can use the principle of inclusion–exclusion or solve the recurrence.

Set up a recurrence:

Let a_n be the number for a circle of n nodes.

Consider the first node. It has a color, say color A. Then, we can think of the remaining n-1 nodes as a line where the first node of the line is adjacent to the fixed color A, and the last node of the line is also adjacent to A.

Alternatively, define two sequences:

Let f(n) be the number of ways to color n nodes in a line such that the first and last are different? Or:

Standard recurrence for circular coloring:

We can define:
- Let a_n: number of valid colorings for a circle of n nodes.
- Let b_n: number of valid colorings for a line of n nodes such that the two ends have different colors? Actually, no.

Another way:

Define:
- Let f(n): number of ways to color n nodes in a line such that the first and last are not necessarily constrained (but adjacent in the line are constrained). Then f(n)=M*(M-1)^(n-1).

But for the circle, we have an additional constraint that the first and last must be different.

So, the number for the circle is the same as the number of ways to color a line of n nodes such that the first and last have different colors.

Is that correct?

Actually, yes: because if we break the circle at the first node, then the constraint between the last and first becomes a constraint on the last node (must be different from the first). So, the circle condition is equivalent to a line where the first and last are different.

So, we have:
Total for line without circular constraint: M*(M-1)^(n-1)

Now, among these, the cases where the first and last are the same: how many?

If the first and last are the same, then we can think of the first and last as being fixed to a color, say color c. Then, the last node must be the same as the first. Then, the second node must be different from the first, the third different from the second, and the last must equal the first.

This is similar to a circular arrangement of n-1 nodes? Actually, we can merge the first and last into one node? Then the problem reduces to coloring a circle of n-1 nodes? But wait, no: because the first and last are fixed to the same color, then the second node must be different from the first (which is same as the last), and the second last must be different from the last (which is same as the first). So, the second last must be different from the first and from the (n-2)th. 

Alternatively, we can define:

Let g(n) be the number of line colorings of n nodes where the first and last are the same.

Then, we can form a recurrence.

But actually, we can compute the number of line colorings of n nodes with the first and last same as:

- Choose the color for the first (and last): M choices.
- Then, the middle n-2 nodes form a line such that adjacent are different and the second is different from the first (fixed) and the (n-1)th is different from the last (which is same as first).

But note: the condition for the middle n-2 is a standard line coloring with the constraints that the ends are adjacent to the fixed color (the first/last color). Actually, it's equivalent to a line of n-2 with the additional constraint that the first of the middle (which is the second of the original) is different from the fixed color, and the last of the middle (the n-1th) is different from the fixed color. But the fixed color is the same for both ends.

Alternatively, we can use DP:

Let d[i][0] be the number of ways for the first i nodes in the line such that the i-th node is not the same as the first node? That might get complicated.

But note: the number of line colorings where the first and last are the same can be found by recurrence.

Set:
- Let A(n) = number of valid line colorings of n nodes such that the first and last are the same.
- Let B(n) = number of valid line colorings of n nodes such that the first and last are different.

Then:
Total for line: A(n) + B(n) = M*(M-1)^(n-1)

Now, how to relate A(n) and B(n) to A(n-1) and B(n-1)?

For A(n) (first and last same):
- The last node must be the same as the first. So, the last node is fixed to the color of the first. Then, the (n-1)th node must be different from the last (which is the first) and from the (n-2)th? Actually, the (n-1)th must be different from the n-th (which is same as first) and from the (n-2)th. But also, the (n-1)th must be different from the first? Only if n-1 is adjacent to the first? Not necessarily.

Actually, the recurrence:

To form a coloring of length n where the first and last are same:

- The first node has color c (M choices). The last node must be c.
- The (n-1)th node must be different from c (because adjacent to the last which is c) and also different from the (n-2)th.

But we can also think backwards:

Consider the n-th node: it is same as the first. Then, the (n-1)th node must be different from the n-th (so different from c) and also different from the (n-2)th.

But we can relate to the coloring of the first n-1 nodes? Actually, the coloring of the first n-1 nodes must form a valid line coloring and the (n-1)th node must be different from the first (because the last is same as first and adjacent to n-1). So, the coloring of the first n-1 nodes must end with a color different from the first. That is exactly B(n-1).

Wait: because in the first n-1 nodes, the last (the (n-1)th) must be different from the first? Then, when we set the n-th to be the same as the first, we have the constraint that the (n-1)th is different from the n-th (which is same as first). So, for each valid coloring of the first n-1 nodes that ends with a color different from the first, we can set the n-th to the first color. Therefore:

A(n) = B(n-1)

Because the coloring of the first n-1 nodes must be a coloring that ends with a color different from the first (so that the n-th, set to the first, is different from the n-1th).

Now, for B(n): colorings where the first and last are different.

Similarly, we can append a last node to a coloring of n-1 nodes. The last node must be different from the (n-1)th and also different from the first.

But we have two cases for the coloring of the first n-1 nodes:

1. If the coloring of the first n-1 nodes ends with a color that is the same as the first? Then we cannot choose that color or the color of the (n-1)th for the last node? Actually, no: the last node must be different from the (n-1)th and different from the first. If the (n-1)th is same as the first, then we have to choose a color different from the first (which automatically satisfies both). So, that gives (M-1) choices? But wait, the last node must be different from the (n-1)th (which is same as first) and the first? Then, any color not the first is acceptable. So, M-1 choices.

2. If the coloring of the first n-1 nodes ends with a color different from the first, then the last node must be different from the (n-1)th and different from the first. So, we have M-2 choices.

Therefore:

B(n) = [A(n-1) * (M-1)] + [B(n-1) * (M-2)]

But we also have A(n-1) + B(n-1) = total for n-1 = M*(M-1)^(n-2)

And we know A(n) = B(n-1)

So, we can express everything in terms of B(n-1) and B(n-2)? Actually, we can derive a recurrence for B(n):

B(n) = A(n-1)*(M-1) + B(n-1)*(M-2)
      = B(n-2)*(M-1) + B(n-1)*(M-2)

But we don't need to go that far.

Actually, the problem is that we are trying to compute the circle condition: which is the same as the number of line colorings of n nodes with the first and last different. That is exactly B(n).

But we can express B(n) in a closed form.

We have:

Total line colorings: T(n) = A(n) + B(n) = M * (M-1)^(n-1)

And we have A(n) = B(n-1)   [for n>=2]

But also, note that T(n) = A(n) + B(n) = B(n-1) + B(n)

But T(n) = M*(M-1)^(n-1)

So, we have:

B(n) = T(n) - A(n) = T(n) - B(n-1)

But T(n) = M*(M-1)^(n-1)

So, B(n) = M*(M-1)^(n-1) - B(n-1)

This is a recurrence: B(n) + B(n-1) = M*(M-1)^(n-1)

We can solve this recurrence.

This is a linear recurrence. Let me write:

B(n) = M*(M-1)^(n-1) - B(n-1)

We can expand:

B(n) = M*(M-1)^(n-1) - [M*(M-1)^(n-2) - B(n-2)]
      = M*(M-1)^(n-1) - M*(M-1)^(n-2) + B(n-2)

But we can also write:

B(n) + B(n-1) = M*(M-1)^(n-1)

This recurrence can be solved by iterating, but we need a closed form.

Alternatively, we can use generating functions, but let me try to unroll:

B(1) is not defined? Because for a line of 1 node, we don't have a last? So, we start from n=2.

For n=2: 
Line of 2 nodes: first and last (which are the two adjacent nodes) must be different: M choices for first, M-1 for the second. So B(2) = number of colorings where first and last (which is the second) are different? That is M*(M-1). Also, A(2)=M? Actually, no: if the two are same, then M choices (both same). But wait, adjacent must be different? So for n=2, the valid colorings are only those with two different colors. So A(2)=0, B(2)=M*(M-1).

But according to the recurrence: T(2)=M*(M-1), and A(2)=B(1). But what is B(1)? For a line of 1 node, the last is the same as the first? But adjacent? Only one node, so no adjacent constraint. But we don't have a constraint for the last and first? Actually, we didn't define for n=1.

So, let me define for n>=2.

We can also note that the recurrence:

B(n) = M*(M-1)^(n-1) - B(n-1)

With base: for n=2, B(2)=M*(M-1)

Now, we can compute:

B(3) = M*(M-1)^2 - B(2) = M*(M-1)^2 - M*(M-1) = M*(M-1)[ (M-1) - 1] = M*(M-1)(M-2)

Which is correct: for 3 nodes in a line, the first and last must be different: first has M, second has M-1, third must be different from second and also different from first? No, the constraint for the third is only adjacent to the second. But we are counting all valid line colorings? Then the first and last being different: total line colorings for 3: M*(M-1)^2. Then subtract the ones where first and last are same: which is A(3). For A(3): first and last same. Then, first has M, second must be different from first, and third must be same as first and different from second. So, M*(M-1)*1 = M*(M-1). Then B(3)= total - A(3) = M*(M-1)^2 - M*(M-1) = M*(M-1)(M-2). That is also the count of colorings where the first and last are different.

But in the circle, we have 3 nodes and we require the first and last to be different? Actually, for the circle, we require all adjacent to be different, including the last and first. So the circle condition for 3 is equivalent to the line condition for 3 with the additional constraint that the first and last are different. Therefore, the circle count is exactly B(3)=M*(M-1)(M-2). But wait, for M=3, that is 3*2*1=6, which matches.

So, the recurrence is:

B(n) = M*(M-1)^(n-1) - B(n-1)

We can solve this recurrence by unfolding:

B(n) = M*(M-1)^(n-1) - M*(M-1)^(n-2) + M*(M-1)^(n-3) - ... 

It alternates.

In fact:

B(n) = sum_{k=1}^{n} (-1)^{n-k} * M * (M-1)^(k-1)

Wait, let me write:

B(n) = M*(M-1)^(n-1) - B(n-1)
       = M*(M-1)^(n-1) - [ M*(M-1)^(n-2) - B(n-2) ]
       = M*(M-1)^(n-1) - M*(M-1)^(n-2) + B(n-2)
       = M*(M-1)^(n-1) - M*(M-1)^(n-2) + [ M*(M-1)^(n-3) - B(n-3) ]

So, we see that:

B(n) = M * [ (M-1)^(n-1) - (M-1)^(n-2) + (M-1)^(n-3) - ... + (-1)^{n-2}(M-1)^1 ] + (-1)^{n-1} B(1)

But what is B(1)? For n=1: the line has one node. Then, the first and last are the same. But we don't have a constraint? Actually, the problem for n=1: the circle? Then adjacent? The only node is adjacent to itself? So we must have no adjacent same? Then for one node, any color is valid? But the constraint is "no two adjacent" — but there is only one, so it's valid. Then the circle condition: for n=1, we don't have two distinct adjacent, so it's valid. But we are solving for a line of 1 node for the purpose of breaking the circle? Actually, in our recurrence for the circle, we broke it to a line. But for the circle, when n=1, the condition is trivially satisfied? But in our recurrence for B(n) (line colorings where first and last different) for n=1: the first and last is the same node, so we would require that the color is different from itself? That's impossible. So B(1)=0? And A(1)=M? Because the first and last are same, and there's no constraint? Actually, for a line of 1 node, the adjacent constraint doesn't exist (only one node). So total colorings: M. And the condition for A(1) is that the first and last are same? Yes, so A(1)=M, and B(1)=0.

But in our recurrence, we start from n=2. So base: n=2: B(2)=M*(M-1)

Then, the recurrence:

B(n) = M * (M-1)^(n-1) - B(n-1)

The solution is:

B(n) = M * sum_{i=0}^{n-2} (-1)^i * (M-1)^(n-1-i)   [but I'm not sure]

Alternatively, we can write:

B(n) = (M-1)^n + (-1)^n * (M-1)

Wait, let me check for n=2: (M-1)^2 + (1)*(M-1) = (M-1)(M-1+1)= (M-1)*M. Correct.

For n=3: (M-1)^3 + (-1)^3*(M-1)= (M-1)^3 - (M-1) = (M-1)[(M-1)^2 - 1] = (M-1)(M^2-2M+1-1)= (M-1)(M^2-2M)= M(M-1)(M-2). Correct.

So, indeed, the closed form for the number of valid circular colorings (which is the same as the line colorings where the first and last are different) is:

B(n) = (M-1)^n + (-1)^n * (M-1)

But wait, is that the same as the formula I recalled earlier? Yes.

Therefore, the answer for the circle is:

ans = (M-1)^N + (-1)^N * (M-1)

But wait, let me test with the example N=4, M=2: 
(2-1)^4 + (-1)^4*(2-1) = 1^4 + 1*1 = 2. Correct.

But wait, what if M=1? Then: 
(1-1)^N + (-1)^N*(1-1) = 0 + 0 = 0. Correct for N>=2.

But what if N=1? The problem states N>=2, so we don't have to worry.

So, the solution is:

ans = (M-1)^N + (-1)^N * (M-1)

But wait, is that the entire story? Actually, no: because when we broke the circle to a line, we fixed the first node? But in the circular arrangement, the entire coloring is symmetric. Actually, in the derivation above, the formula for the circle is exactly that.

But wait, in the formula, we didn't multiply by M? 

No: because in the derivation of the recurrence, the total line colorings is M*(M-1)^(n-1). But in the closed form for B(n), we have:

B(n) = (M-1)^n + (-1)^n * (M-1)

But for n=2: (M-1)^2 + (M-1) = (M-1)(M-1+1)=M(M-1). Correct.

But actually, in the circle, we don't fix the first node? The formula we derived for the circle is the same as the number of line colorings where the first and last are different. And that was the circle condition.

But in the circle, we have rotational symmetry? Actually, no: because the circle is fixed (people are numbered). So we are counting assignments to specific positions.

Therefore, the formula is:

ans = (M-1)^N + (-1)^N * (M-1)

But wait, that doesn't match the example: for N=3, M=3: 2^3 + (-1)^3*2 = 8 - 2 =6. Correct.

But actually, I think I see an issue: in the line coloring, we have M choices for the first node. But in the formula for B(n), we did not fix the first node? Actually, the formula we derived for B(n) (the number of line colorings of n nodes with the first and last different) is:

B(n) = (M-1)^n + (-1)^n * (M-1)

But for n=2: M*(M-1) = (M-1)^2 + (M-1) = (M-1)(M-1+1)=M(M-1). So that includes the choice for the first node.

Similarly, for the circle, the formula we have is the same as the line coloring with the first and last different? And that is the circle condition. So the formula is correct.

But wait, actually, the standard formula for the circular coloring is:

The number of ways = (M-1)^n + (-1)^n (M-1)

But that is the entire count? Yes, as we verified with examples.

Therefore, the solution is to compute:

ans = pow(M-1, N, 998244353) 
if N % 2 == 0:
    ans = (ans + (M-1)) % mod
else:
    ans = (ans - (M-1)) % mod

But wait: (-1)^N: if N is even, then we add (M-1); if odd, subtract.

But note: if (M-1) is negative? We are modulo arithmetic.

But modulo: we can do:

mod = 998244353
ans = pow(M-1, N, mod)
if N % 2 == 0:
    ans = (ans + (M-1)) % mod
else:
    ans = (ans - (M-1)) % mod

But we have to do modulo arithmetic: subtracting (M-1) might be negative, so:

ans = (ans + (mod if N%2==1 and (M-1) > ans else 0)) 

Better to do:

ans = (pow(M-1, N, mod) + ( (1 if N%2==0 else -1) * (M-1) )) % mod

But modulo: we can adjust negative:

ans = (pow(M-1, N, mod) + ( (1 if N%2==0 else mod-1) * (M-1) )) % mod

Wait, no: the term is (-1)^N * (M-1). So:

term = (M-1) if N % 2 == 0 else -(M-1)

Then ans = (pow(M-1, N, mod) + term) % mod

But negative modulo: we can do:

ans = ( pow(M-1, N, mod) + (term % mod) ) % mod

But if term is negative, we can do:

term = (M-1) if N % 2 == 0 else - (M-1)
ans = (pow(M-1, N, mod) + term) % mod

But if term is negative, then (ans might be negative) so:

ans = (pow(M-1, N, mod) + term) % mod
if ans < 0: ans += mod

But modulo operation in Python handles negative by doing % mod, but it's better to adjust.

Alternatively, we can compute:

if N % 2 == 0:
    ans = (pow(M-1, N, mod) + (M-1)) % mod
else:
    ans = (pow(M-1, N, mod) - (M-1)) % mod

But if (M-1) is large, subtracting might give negative? Then we can do:

ans = (pow(M-1, N, mod) - (M-1)) % mod
if ans < 0: ans += mod

But in Python, the modulo operator % will return a nonnegative integer. However, if the result is negative, adding mod once is enough because the absolute value of (pow(...) - (M-1)) is at most mod? Actually, we are doing modulo mod, but the subtraction: we are subtracting (M-1) which is up to 10^6, and the power is mod mod, so the power result is in [0, mod-1]. Then subtracting (M-1) might be negative? Then we do:

ans = (pow(M-1, N, mod) - (M-1)) % mod

This might be negative? Then we can adjust by:

ans = (pow(M-1, N, mod) - (M-1)) 
if ans < 0:
    ans += mod
else:
    ans %= mod

But the modulo operation in Python: 

For example: (a - b) % mod, if a < b, then it becomes a - b + mod.

So we can simply do:

ans = (pow(M-1, N, mod) - (M-1)) % mod
if N % 2 == 1:
    # then we subtracted, so we do the above
    # but we can do the same for both?
Actually, for the even case we add, so no problem. For odd, we subtract, so we do:

ans = (pow(M-1, N, mod) - (M-1)) % mod

But that is the same as:

ans = (pow(M-1, N, mod) + ( (mod - (M-1 % mod)) if N%2==1 else (M-1) )) % mod

But simpler: we can compute term = (M-1) if N%2==0 else mod - (M-1) % mod
Then ans = (pow(M-1, N, mod) + term) % mod

But wait: for the odd case, we want to subtract (M-1), which is equivalent to adding (mod - (M-1)) modulo mod.

So:

term = (M-1) if N % 2 == 0 else (mod - (M-1) % mod)
ans = (pow(M-1, N, mod) + term) % mod

But note: if M-1 is negative? But M>=2, so M-1>=1.

So we can write:

mod = 998244353
if N % 2 == 0:
    ans = (pow(M-1, N, mod) + (M-1)) % mod
else:
    ans = (pow(M-1, N, mod) + (mod - (M-1) % mod)) % mod

But actually, we can also do:

ans = pow(M-1, N, mod)
if N % 2 == 0:
    ans = (ans + (M-1)) % mod
else:
    ans = (ans - (M-1)) % mod
    # if ans is negative, we can add mod? But % mod will make it positive?
    # Actually, in Python, (ans - (M-1)) % mod will be nonnegative.

But let me test: 
In Python: (-5) % 4 -> 3? So yes, it will be nonnegative.

So we can simply do:

ans = pow(M-1, N, mod)
if N % 2 == 0:
    ans = (ans + (M-1)) % mod
else:
    ans = (ans - (M-1)) % mod

But the subtraction: if (M-1) is large, and the power is less than (M-1), then (ans - (M-1)) will be negative, and then % mod will add mod until positive. So it's safe.

But note: the power is taken modulo mod, so it's in the range [0, mod-1]. Then subtracting (M-1) (which is at most 10^6) from a number in [0, mod-1] might give negative? Then modulo mod will adjust to positive.

So the code is:

mod = 998244353
if M == 1:
    # if M==1, then we have only one color. Then for N>=2, we must have 0.
    # but the problem says M>=2? Actually constraints say M>=2? 
    # Constraints: N, M >=2? Actually, constraints: 2<=N,M<=10^6, so M>=2. So we don't need to handle M=1 separately.
    # But if M==1, then the formula: (0)^N + ... ->0, so it's safe.

But what if M==1 and N==1? Then we return 1? But the problem says N>=2. So we don't have to worry.

Therefore, code:

class Solution:
    def solve(self, N: int, M: int) -> int:
        mod = 998244353
        # If M==1: then only possible if N==1, but N>=2 -> 0. But M>=2 per constraints.
        base = M - 1
        # Compute base^N mod mod
        p = pow(base, N, mod)
        if N % 2 == 0:
            ans = (p + base) % mod
        else:
            ans = (p - base) % mod
        return ans

But wait, let me test with examples.

Example 1: N=3, M=3: base=2, p = 2^3 mod mod = 8. Then N%2==1 -> ans = (8-2) % mod = 6. Correct.

Example 2: N=4, M=2: base=1, p=1^4=1. Then N%2==0 -> (1+1) % mod =2. Correct.

Another test: N=2, M=2: base=1, p=1^2=1. Then N%2==0 -> 1+1=2. Correct: two colorings: (0,1) and (1,0).

But wait, what about (0,0) and (1,1)? They are invalid because adjacent same. So only two valid.

But the formula: for N=2, M=2: (M-1)^2 + (-1)^2*(M-1)=1+1=2. Correct.

So the code should work.

But wait, the constraints: N, M up to 10^6. The pow(base, N, mod) uses fast exponentiation, which is O(log(N)), so it's efficient.

Therefore, the solution is:

class Solution:
    def solve(self, N: int, M: int) -> int:
        mod = 998244353
        base = M - 1
        p = pow(base, N, mod)
        if N % 2 == 0:
            return (p + base) % mod
        else:
            return (p - base) % mod

But wait, what if base is 0? Then when M=1, base=0. But M>=2, so base>=1.

So it's safe.

Let me run the example 3: Input: 987654 456789 -> output 778634319

But I don't have the exact value to check, but the code is efficient.

However, I should test with a small example: N=3, M=3 ->6. Done.

But wait, what about the circle: in the formula, we have (M-1)^N + (-1)^N*(M-1). But is this the standard formula? I recall that the standard formula for the number of ways to color a circle of n beads with m colors, no two adjacent same, is:

(m-1)^n + (-1)^n * (m-1)

Yes, that matches.

So I think this is correct.

But wait, I found a reference: 
The number of ways to color a circle of n nodes with m colors such that adjacent are different is:
  (m-1)^n + (-1)^n (m-1)

Yes.

So the solution is as above.

But note: in the problem, the circle: person 1 is adjacent to person 2 and person N. So the condition is symmetric.

Therefore, the code:

class Solution:
    def solve(self, N: int, M: int) -> int:
        MOD = 998244353
        base = M - 1
        res = pow(base, N, MOD)
        # Add or subtract (M-1) based on parity of N
        if N % 2 == 0:
            res = (res + base) % MOD
        else:
            res = (res - base) % MOD
        return res

But wait, what if (res - base) is negative? Then in Python, (res - base) % MOD will be nonnegative. For example: (5 - 10) % 7 = (-5) % 7 = 2. So it's safe.

I'll run with the example 3: 987654, 456789. The expected output is 778634319.

But I can compute that locally? Or trust the formula.

But let me test with a small value: N=2, M=10: base=9, p=9^2=81, then even: 81+9=90. The valid: for two adjacent: each can be 10 choices for first, 9 for second: 90. Correct.

So, I think we can submit.

But wait: the problem says "no two adjacent have the same integer". And our formula is for circular arrangement. So it's correct.

Therefore, the code is:

class Solution:
    def solve(self, N: int, M: int) -> int:
        mod = 998244353
        base = M - 1
        p = pow(base, N, mod)
        if N % 2 == 0:
            ans = (p + base) % mod
        else:
            ans = (p - base) % mod
        return ans

I'll write the code accordingly.

But note: the modulus for pow is mod, and then we do the addition/subtraction modulo mod.

This should handle the constraints.

Let me run the example: N=4, M=2: base=1, p=1, then 1+1=2. Correct.

Another: N=3, M=3: 2^3=8, then 8-2=6. Correct.

So, I'm confident.

## Final Solution

### Step 1: Problem Understanding and Modeling
The problem involves counting the number of ways to assign integers from 0 to \(M-1\) to \(N\) people arranged in a circle such that no two adjacent people have the same integer. The solution must be computed modulo 998244353. Key constraints include \(2 \leq N, M \leq 10^6\), and the circular arrangement means person \(N\) is adjacent to person 1.

**Key Insights:**
- For a linear arrangement (non-circular), the count is \(M \times (M-1)^{N-1}\).
- The circular arrangement introduces an additional constraint that person \(N\) and person 1 must have different integers, making the problem more complex.
- A known combinatorial formula for circular arrangements where no two adjacent are the same is \((M-1)^N + (-1)^N \times (M-1)\). This formula accounts for the circular constraint efficiently.

**Edge Cases:**
- If \(M = 1\) and \(N \geq 2\), no valid assignments exist (output 0). However, constraints ensure \(M \geq 2\).
- For \(N = 2\), the formula correctly yields \(M \times (M-1)\) valid assignments.

### Step 2: Solution Exploration
1. **Brute Force Solution:**
   - Enumerate all \(M^N\) assignments and check adjacency conditions.
   - **Time Complexity:** \(O(M^N)\), infeasible for \(N, M \leq 10^6\).
   - **Space Complexity:** \(O(N)\) for storing assignments, but time is prohibitive.

2. **Dynamic Programming (DP):**
   - Use DP states to track valid assignments for the circle. However, transitions are complex due to the circular constraint.
   - **Time Complexity:** \(O(N \times M)\), which is \(O(10^{12})\) in the worst case, too slow.
   - **Space Complexity:** \(O(N \times M)\), also infeasible.

3. **Mathematical Formula:**
   - Leverage the derived formula \((M-1)^N + (-1)^N \times (M-1)\).
   - **Time Complexity:** \(O(\log N)\) using fast exponentiation.
   - **Space Complexity:** \(O(1)\), highly efficient.
   - **Applicability:** Directly solves the problem within constraints.

### Step 3: Optimal Solution Selection
**Selection Rationale:**
- The mathematical formula approach reduces the problem to a constant-time computation after exponentiation.
- Fast exponentiation handles large \(N\) and \(M\) efficiently (\(O(\log N)\) time).
- The solution is optimal due to its logarithmic time complexity and constant space usage.

**Implementation:**
```python
MOD = 998244353

class Solution:
    def solve(self, N: int, M: int) -> int:
        base = M - 1
        p = pow(base, N, MOD)
        if N % 2 == 0:
            return (p + base) % MOD
        else:
            return (p - base) % MOD
```

**Key Optimization Points:**
- **Fast Exponentiation:** Computes \((M-1)^N \mod 998244353\) in \(O(\log N)\) time.
- **Modulo Handling:** Efficiently adjusts for negative values during subtraction using modulo arithmetic.
- **Direct Formula Application:** Uses the combinatorial formula to avoid complex DP or inclusion-exclusion.

### Step 4: Verification and Summary
**Verification with Examples:**
- **Example 1 (N=3, M=3):** 
  - \((3-1)^3 + (-1)^3 \times (3-1) = 8 - 2 = 6\), matches.
- **Example 2 (N=4, M=2):** 
  - \((2-1)^4 + (-1)^4 \times (2-1) = 1 + 1 = 2\), matches.
- **Edge Case (N=2, M=10):** 
  - \(9^2 + 9 = 81 + 9 = 90\), correct since first has 10 choices, second has 9.

**Further Optimization Directions:**
- The current solution is already optimal. For larger constraints (beyond \(10^6\)), ensure efficient handling of big integers via modular arithmetic.

**Summary:**
The problem is efficiently solved using a combinatorial formula tailored for circular arrangements. The solution leverages fast exponentiation and modular arithmetic to handle large inputs, ensuring optimal performance and correctness. The key insight is recognizing and applying the formula \((M-1)^N + (-1)^N \times (M-1)\), which simplifies the problem to logarithmic time complexity.