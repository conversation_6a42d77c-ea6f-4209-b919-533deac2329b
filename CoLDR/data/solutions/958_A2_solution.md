# Solution for 958_A2.md

Generated at: 2025-07-22 06:10:48
Model: deepseek-reasoner

## Reasoning Process

Alright, I'm going to tackle this problem step by step. Let me make sure I understand it correctly.

So, <PERSON> has two maps. The first map is an N x M grid, and the second map is an M x N grid. <PERSON> needs to find an M x M section in the first map that matches exactly with an M x M section in the second map. But here's the twist: the second map's M x M section is actually taken by columns? Wait, let me read the output format again.

The output should be two integers i and j, meaning that the M x M section in the first map starting at row i (and covering all M columns) is equal to a section in the second map starting at column j. But how is the section in the second map defined?

Looking back at the example: 
Input has N=10, M=5. The first map is 10 rows of 5 characters. The second map is 5 rows of 10 characters. The output is "4 6". 

In the example, the first map's section starts at row 4 (so rows 4 to 8, since M=5). The second map's section starts at column 6 (so columns 6 to 10, since M=5). But how do we extract an M x M section from the second map? 

Ah, I see. The second map has M rows and N columns. For a fixed j, we take the columns j to j+M-1 from each row of the second map. But wait, that would form a grid of M rows and M columns? Exactly. So the section we take from the second map is a contiguous block of M consecutive columns in each of the M rows.

Now, the problem is: we need to find an i (from 1 to N-M+1) and a j (from 1 to N-M+1, because the second map has N columns) such that the M x M block from the first map starting at row i (rows i to i+M-1) and the M x M block from the second map starting at column j (columns j to j+M-1 in each row) are identical.

But wait, how is the second map stored? It's given as M lines, each of N characters. So each line is a string of length N. So for the second map, if we take the k-th row (0-indexed) and then the substring from j to j+M-1, that gives us one row of the M x M block. Similarly, in the first map, the block starting at row i: we take M consecutive rows starting at i, and each row is taken entirely (all M columns).

So, the problem reduces to: 
Find i in [0, N-M] (0-indexed) and j in [0, N-M] (0-indexed) such that for every r in [0, M-1] and every c in [0, M-1], 
   map1[i + r][c] == map2[r][j + c]

But note: the example output is 4 and 6. Since indexing starts at 1, that would be i=3 (0-indexed row 3) and j=5 (0-indexed column 5). 

Let me check with the example:

First map rows 4 to 8 (0-indexed rows 3 to 7) are:
Row3: mayth
Row4: eforc
Row5: ebewi
Row6: thyou
Row7: hctwo

Second map: 
The second map is given as 5 rows, each 10 characters. We start at column 6 (0-indexed 5) and take 5 characters. So for each row in the second map, we take substring [5:10] (0-indexed, from index 5 to 9).

The second map rows:
0: "somermayth" -> substring [5:10] is "mayth"
1: "andomeforc" -> "eforc"
2: "noiseebewi" -> "ebewi"
3: "againthyou" -> "thyou"
4: "noisehctwo" -> "hctwo"

Exactly matches the first map's block. Perfect.

So the task is clear. Now, how to solve it.

Constraints: N up to 2000, M up to 200. So the total number of possible i is about 2000, and j about 2000. That gives 4e6 possible (i,j) pairs. For each pair, we need to check an M x M block, which is 200x200=40000 comparisons. So worst-case 4e6 * 40000 = 160e9 comparisons. That's 160 billion, which is too slow in Python. So we cannot brute force.

We need a more efficient way.

Approach 1: Brute Force (but too slow as above)

Approach 2: Precomputation to reduce the comparison cost.

Idea: Instead of comparing the entire M x M block for each (i,j), can we use hashing? We can precompute a hash for the entire block from the first map and the entire block from the second map for each possible starting position. But note, we have two different grids.

But actually, for the first map, for a fixed i, the block is map1[i:i+M] (a list of M strings, each of length M). For the second map, for a fixed j, the block is each row of map2, but taking the substring [j:j+M]. 

We can precompute hashes for each row in both maps. Then, for each candidate i, we can form a fingerprint for the block in the first map. Similarly, for each candidate j, form a fingerprint for the block in the second map. Then, we look for a pair (i,j) where the fingerprints are equal.

But how to compute the fingerprint for a block? We can use a 2D rolling hash. Alternatively, we can compute row hashes and then combine them.

But note: M is only 200, but N is 2000. So the total number of rows in the first map is 2000, and for the second map, we have 200 rows. The blocks we are comparing are both M x M.

We can do:

For the first map, for each starting row i, we have M rows (from i to i+M-1), each of length M. We can compute a hash for each row in the first map? But we don't need to; we can precompute a hash for each row of the first map for the entire row? But the rows are only M characters long. So each row is already of length M. So for the first map, the block starting at row i is exactly the rows i to i+M-1. So we can form a hash for the entire block by combining the hashes of the M rows.

Similarly, for the second map, for each j, we need to extract from each of the M rows a substring of length M starting at j. Then combine them.

So if we precompute a hash for each row in the first map (but since the row is only M characters, we don't need to do anything). However, for the second map, we have rows of length N, and we need to extract substrings of length M. We can precompute a 1D rolling hash for each row of the second map so that we can quickly compute the hash for any substring.

But then, how to combine the row hashes for the entire block? We can use a polynomial hash for 2D.

Proposed plan:

1. Precompute a base hash for the first map's rows. Since each row is fixed, we can compute a hash for each row. For the block in the first map starting at i, the block is rows [i, i+M-1]. We can form a combined hash for the block by hashing the hashes of the rows? Or we can compute a single hash for the entire block.

Alternatively, we can compute a 2D hash for the entire first map? But the block is contiguous rows, so we can do:

Let H1(i) = hash for the block starting at row i. We can compute H1(i) by combining the hashes of the M rows. But if we use a polynomial hash, we can do:

Let's define a hash function for a string s: 
   hash(s) = (s[0]*p^0 + s[1]*p^1 + ... ) mod q

But for 2D, we can do:

   H = 0
   for r in range(M):
        row_hash = 0
        for c in range(M):
            row_hash = (row_hash * base1 + char_value(map1[i+r][c])) mod mod
        H = (H * base2 + row_hash) mod mod

Similarly for the second map block at j:

   H' = 0
   for r in range(M):
        row_hash = 0
        for c in range(M):
            # the character is map2[r][j+c]
            row_hash = (row_hash * base1 + char_value(map2[r][j+c])) mod mod
        H' = (H' * base2 + row_hash) mod mod

But if we do this from scratch for each (i,j), it's still O(M*M) per pair, which is 4e6 * 40000 = 160e9, too slow.

We need to precompute row hashes for the second map so that we can get the hash for a substring quickly.

Alternatively, we can precompute for the second map: for each row, we compute a rolling hash for the entire row. Then, for a fixed j, we can get the hash for the substring j to j+M-1 in O(1) per row. Then, we can compute the entire block hash for the second map for a fixed j by combining the M row hashes (each of which is the hash for the substring at that row) in O(M). Similarly, for the first map, for a fixed i, we can compute the block hash by combining the M consecutive rows (each row is already of length M, so we have a precomputed hash for each row) in O(M).

But note: the first map's rows are fixed. So we can precompute an array H1 for each row in the first map: H1[r] = hash of map1[r] (the entire row). Then, for a block starting at i, the block is the rows i to i+M-1. The combined hash for the block can be:

   H = 0
   for r in range(i, i+M):
        H = (H * base2 + H1[r]) % mod

But wait, that doesn't account for the 2D structure? Actually, if we use a polynomial hash for the vertical direction, that could work. We are treating each row as a single entity.

Similarly, for the second map: precompute for each row r (0 to M-1) and for each starting column j, the hash of the substring map2[r][j:j+M]. But we don't want to precompute for every j (which is N-M+1 per row, about 2000) for M rows (200). So total 200*2000=400000, which is acceptable.

But then, for each j, we can compute the block hash for the second map as:

   H' = 0
   for r in range(M):
        h = precomputed_second_hash[r][j]   # which is the hash for row r, substring starting at j of length M
        H' = (H' * base2 + h) % mod

Then, we want to find i and j such that H1_block[i] == H2_block[j]. But note: the base2 and mod must be the same.

But wait, we can do better: we don't need to iterate over all i and j. We can precompute all H1_block for i in [0, N-M] and store in a set or a dictionary. Then for each j, compute H2_block[j] and check if it's in the set. Then we can break when we find one.

But note: the problem says "if multiple solutions exist, any is acceptable".

So steps:

Precomputation:
1. Precompute a row hash for each row in the first map. Since each row is of fixed length M, we can compute H1[r] for each row r in [0, N-1] as the hash of the entire row.

2. Precompute the block hashes for the first map for every starting row i (from 0 to N-M). We can compute H1_block[i] as:

   H1_block[i] = 0
   for r in range(i, i+M):
        H1_block[i] = (H1_block[i] * base2 + H1[r]) % mod

But note: this is O(M) per i, and there are about N-M+1 ≈ 2000 i's, so total 2000*200=400000, acceptable.

3. For the second map: we want to precompute for each row r (0 to M-1) a rolling hash for the entire row (which is of length N). Then we can get the hash for any substring of length M in that row in O(1) time.

   We can precompute for each row r:
        prefix hash and power array for rolling hash.

   Steps for one row:
        Let s = map2[r]
        Precompute an array P of powers: P[0]=1, P[i]=P[i-1]*base1 % mod
        Precompute an array H of length N+1: H[0]=0, H[i] = (H[i-1]*base1 + idx(s[i-1])) % mod

        Then the hash for substring from j to j+M-1 is:
            h = (H[j+M] - H[j]*P[M]) % mod

   We do this for each row r (0 to M-1). Precomputation per row is O(N), which is 2000, so total 200*2000=400000, acceptable.

4. Then, for each j in [0, N-M] (0-indexed), we compute the block hash for the second map:

        H2_block[j] = 0
        for r in range(M):
            h_r = hash_second_row(r, j, j+M-1)   # using the rolling hash we precomputed for row r
            H2_block[j] = (H2_block[j] * base2 + h_r) % mod

   This is O(M) per j, and there are about N-M+1 ≈ 2000 j's, so total 2000*200=400000, acceptable.

5. Now, we have a list of H1_block for i in [0, N-M] and H2_block for j in [0, N-M]. We can store the H1_block values in a set or a dictionary mapping hash to i. Then, for each j, we check if H2_block[j] is in the set. If yes, we have a solution: any i that produced that hash and the current j.

But wait: we must be cautious of hash collisions. Since the constraints are not huge, we can use a double hash (two mods) to reduce collisions. Or one mod with a large prime might suffice? But to be safe, we can use two mods.

Alternatively, we can avoid double hashing and hope that one mod is enough? But worst-case N=2000, M=200, so total blocks in first map is 2000, and in second map 2000. So 4000 blocks. The chance of collision might be low, but to be safe, we can use two mods.

But the problem says "a solution is guaranteed to exist", so we must output one. But if we have a hash collision, we might output a wrong pair. So we should verify? But M is 200, and we have to compare 40000 characters, which is acceptable if we do it once? But worst-case if we get multiple collisions? We can do:

   Precompute H1_block and store in a dictionary: mapping hash -> list of i's? But then when we get a matching hash from H2_block, we can verify with one of the i's.

But since we only need one solution, we can break at the first j that matches, and then for the matching i, we do a full comparison? But the hash we use is for the entire block, so if we use two mods, we can have two hashes and then the chance of collision is very low.

Alternatively, we can do without full 2D hashing: we note that M is small (only 200). So we can avoid storing the entire H1_block for every i? Instead, we can iterate over j and for each j, we iterate over i? But that would be 4e6 iterations, and then for each (i,j) we compare the blocks. But comparing an M x M block is 40000 per pair, so 4e6 * 40000 = 160e9, which is too slow in Python.

So we stick with hashing and use two mods to reduce collision probability.

Plan with double hashing:

   Use two mods: mod1 and mod2, and two bases: base1 and base2 (for rows and for combining rows).

   Precompute for the first map:
        For each row, compute two hash values (for mod1 and mod2) for the entire row.

        H1_row1[r] = hash of map1[r] for mod1
        H1_row2[r] = hash of map1[r] for mod2

   Then for each i in [0, N-M]:
        H1_block1[i] = 0
        H1_block2[i] = 0
        for r in range(i, i+M):
            H1_block1[i] = (H1_block1[i] * base2_1 + H1_row1[r]) % mod1
            H1_block2[i] = (H1_block2[i] * base2_2 + H1_row2[r]) % mod2

        Store in a dictionary: key = (H1_block1[i], H1_block2[i]), value = i

   For the second map:
        Precompute for each row r (0 to M-1) and for mod1 and mod2 the rolling hash arrays.

        For each row r:
            Precomputation for mod1 and mod2 separately.

        Then for each j in [0, N-M]:
            H2_block1 = 0
            H2_block2 = 0
            for r in range(M):
                # get the substring hash for row r, from j to j+M-1 for mod1 and mod2
                h1 = (H_second1[r][j+M] - H_second1[r][j] * pow1[r][M]) % mod1
                h1 %= mod1
                # Similarly for mod2
                h2 = (H_second2[r][j+M] - H_second2[r][j] * pow2[r][M]) % mod2
                h2 %= mod2

                # Then combine these row hashes vertically
                H2_block1 = (H2_block1 * base3_1 + h1) % mod1
                H2_block2 = (H2_block2 * base3_2 + h2) % mod2

            Check if (H2_block1, H2_block2) is in the dictionary. If yes, then we have i = dictionary[(H2_block1, H2_block2)] and j (current j). Then output (i+1, j+1) because the problem uses 1-indexing.

But wait: the base for combining rows (base2 and base3) should be the same? Actually, the hash computation for the block should be the same for both maps. So we should use the same base for the vertical direction in both maps? Yes.

So we can use the same base2 for both. And same mods. So base2_1 and base2_2 are the same for both maps? Actually, the hashing procedure must be identical for the two blocks.

But note: the first map's block is taken from consecutive rows, each row of length M. The second map's block is taken from consecutive rows (the rows are fixed: 0 to M-1) and a contiguous substring of length M. So the structure is the same: M rows, each of M characters. So we use the same hash function.

Therefore, we can define:

   For a block of M rows, each row has a hash (computed as a 1D hash for the row of M characters). Then the entire block is hashed by:

        block_hash = 0
        for each row in the block (from top to bottom):
            block_hash = (block_hash * base_vertical + row_hash) % mod

   And we do this for both mods.

Now, for the first map, the row_hash for a row r is the hash of map1[r] (which is a string of M characters).

For the second map, the row_hash for row r is the hash of the substring map2[r][j:j+M] (a string of M characters). We compute the row_hash for each row r in the second map using the same 1D hash function as for the first map? But wait, we can compute the row_hash for the second map by the same polynomial formula? Actually, we have two ways:

   Option A: For the second map, we precomputed a rolling hash for the entire row (using base1 and mod1 for the horizontal). Then we use the same base1 to compute the row_hash for a substring? But the row_hash for the first map was computed with base1? So we have to use the same base1 for the second map's row substring.

   But note: the rolling hash we use for the second map is computed with base1. Then the value h1 we get for a substring is the same as if we computed the row_hash for that substring from scratch? 

   Yes, because the polynomial hash is:

        For a string s[0..M-1], the hash is: s[0]*B^(M-1) + s[1]*B^(M-2) + ... + s[M-1]*B^0? 

   Or more commonly: 

        h = 0
        for c in s:
            h = (h * B + val(c)) % mod

        This gives: h = val(s[0])*B^(M-1) + ... + val(s[M-1])*B^0.

   For the rolling hash, if we precomputed:

        H[i] = (H[i-1]*B + s[i-1]) % mod   (for i from 1 to N)
        Then the hash for s[j:j+M] = (H[j+M] - H[j]*B^M) % mod.

   This is the same as the above if we define H[0]=0.

   So yes, the value we get from rolling hash is the same as if we computed the row_hash from scratch.

Therefore, we can use the same base1 and base2 for both maps.

Now, the base_vertical (for combining rows) is base2. We can choose base2 to be a different base? Actually, to avoid collisions, we can choose different bases for horizontal and vertical.

So overall:

   Choose:
        base_h = random or fixed (like 131, 1313) for horizontal
        base_v = another base for vertical (like 13331, 10007)

   And two mods: mod1 and mod2 (large primes, e.g., 10**9+7 and 10**9+9).

Steps in code:

   Precomputation for the first map (map1):
        For each row in map1 (N rows), compute:
            row_hash1 = 0
            for char in row:
                row_hash1 = (row_hash1 * base_h1 + ord(char)) % mod1
            Similarly for mod2: 
                row_hash2 = 0
                for char in row:
                    row_hash2 = (row_hash2 * base_h2 + ord(char)) % mod2

        Actually, we can use the same base_h for both mods? Or different? To avoid correlation, we can use different base_h for mod1 and mod2? Actually, we can, but it's not necessary. Alternatively, we can use the same base_h for both mods? The base_h can be the same, but the mods are different. That is acceptable.

        However, for simplicity, we can use the same base_h for both mods? Yes, because the mods are different.

        So:

            row_hashes1[r] = (h1, h2) for row r? Or store two arrays: H1_row1 and H1_row2.

        We'll store two arrays: H1_row1 and H1_row2 for the first map's rows.

   Precomputation for the second map (map2) for rolling hashes:

        We need for each row r (0 to M-1) in map2, precompute:

            For mod1:
                H_sec1[r][0] = 0
                for idx in range(1, N+1):
                    H_sec1[r][idx] = (H_sec1[r][idx-1]*base_h1 + ord(map2[r][idx-1])) % mod1

                Precompute power array for base_h1: pow_base1[0] = 1, then pow_base1[i] = pow_base1[i-1]*base_h1 % mod1, for exponents up to M (actually we need up to N, but for the substring, we need base_h1^M).

            Similarly for mod2: 
                H_sec2[r][0] = 0
                for idx in range(1, N+1):
                    H_sec2[r][idx] = (H_sec2[r][idx-1]*base_h2 + ord(map2[r][idx-1])) % mod2
                and pow_base2[0] = 1, ... 

        But note: we can precompute the power for base_h1^M once for each mod? Since M is fixed.

        Actually, for each row, the base_h1^M is the same. So we can precompute:

            power1 = pow(base_h1, M, mod1)
            power2 = pow(base_h2, M, mod2)

        Then for a row r, the hash for the substring from j to j+M-1 for mod1 is:

            h1 = (H_sec1[r][j+M] - H_sec1[r][j] * power1) % mod1
            Similarly for mod2: h2 = (H_sec2[r][j+M] - H_sec2[r][j] * power2) % mod2

        We do this for each row r and for each j? Actually, we don't precompute for every j at this stage. We precomputed the prefix arrays, then we can compute the substring hash in O(1) per j per row.

   Then, for the first map, we precompute H1_block for each i:

        For i from 0 to N-M:
            block1 = 0
            block2 = 0
            for r in range(i, i+M):   # M rows
                block1 = (block1 * base_v1 + H1_row1[r]) % mod1
                block2 = (block2 * base_v2 + H1_row2[r]) % mod2
            Store block = (block1, block2) in a dictionary: dict[block] = i

        Note: we use base_v for the vertical direction. We can choose base_v1 and base_v2 as two different bases? Actually, we use the same base_v for both mods? Or different? Since mods are different, we can use the same base_v. But to avoid any potential correlation, we can use two different bases. But it's not necessary. We'll use the same base_v value for both mods? Actually, no: the base_v1 is for mod1 and base_v2 for mod2. They can be the same integer? Yes, but modulo different mods.

   Similarly, for the second map, for each j from 0 to N-M:

            block1_sec = 0
            block2_sec = 0
            for r in range(0, M):   # for each row in the second map (0 to M-1)
                # compute the row hash for row r, substring j to j+M-1 for mod1 and mod2
                h1 = (H_sec1[r][j+M] - H_sec1[r][j] * power1) % mod1
                h1 %= mod1
                h2 = (H_sec2[r][j+M] - H_sec2[r][j] * power2) % mod2
                h2 %= mod2

                # Then combine with vertical base
                block1_sec = (block1_sec * base_v1 + h1) % mod1
                block2_sec = (block2_sec * base_v2 + h2) % mod2

            Then check if (block1_sec, block2_sec) is in the dictionary. If yes, then we found i = dict[(block1_sec, block2_sec)] and j.

   Then output (i+1, j+1) because the problem indexes from 1.

But note: we must handle negative mod results? We do:

        h1 = (H_sec1[r][j+M] - H_sec1[r][j] * power1) % mod1
        if h1 < 0: h1 += mod1

   Similarly for h2.

However, in Python, % gives non-negative, but subtraction might be negative? Actually, yes. So we can do:

        h1 = (H_sec1[r][j+M] - H_sec1[r][j] * power1) % mod1
        if h1 < 0: h1 += mod1   # but Python's % already gives in [0, mod1-1]? 

But the expression might be negative? Then we can do:

        h1 = (H_sec1[r][j+M] - H_sec1[r][j] * power1) % mod1
        # But if negative, we can add mod1? Actually, we can do:
        # h1 = ( ... ) % mod1
        # This will be in [0, mod1-1] because modulo operation in Python returns non-negative.

   However, the subtraction might be negative? Then modulo will make it positive. Example: 

        (a - b) % mod = (a - b + k*mod) % mod, and it will be in [0, mod-1]. So no need to adjust.

   But we should ensure that we are doing modulo arithmetic correctly. The formula for rolling hash is:

        h = (H[j+M] - H[j] * base^M) % mod

   This is standard.

Now, what about the vertical base? We need base_v1 and base_v2? Actually, we can choose two different bases for the vertical direction? But we can use the same base for both mods? Actually, we can set:

        base_v1 = 131   (for mod1)
        base_v2 = 131   (for mod2)

   But different mods, so same base integer is fine.

However, to avoid any systematic issues, we can choose different bases. But it's not necessary. We'll choose two random bases? Or we can use the same base for simplicity.

But note: the base_v for the vertical direction must be the same for both maps? Yes, because the hashing procedure must be the same.

So we choose:

        base_h = 131   for both mods? Actually, we can use the same base_h for both mods? Yes.

        base_v = 13331   for both mods? Yes.

   But we can also choose base_h and base_v as distinct? That would be better.

   Actually, we'll choose:

        base_h1 = 131
        base_h2 = 131   # same? Actually, we can use different for mod1 and mod2? 

   But the horizontal base for mod1 and mod2 can be the same? It doesn't matter because mods are different. Similarly, the vertical base can be the same.

   However, to be safe from collisions, we can choose different bases for mod1 and mod2? Actually, we can choose the same base for both mods? The mods are different, so the same base is acceptable.

   Alternatively, we can choose:

        base_h1 = 131
        base_h2 = 13331
        base_v1 = 10007
        base_v2 = 10009

   But that's arbitrary.

   Actually, we can set:

        base_h = random.randint(100, 10000)   for both mods? But we use the same base_h for both mods? 

   Or we can set two independent bases for mod1 and mod2? 

   Since mod1 and mod2 are independent, we can use the same base_h for both. But to avoid any potential structure, we can choose two different bases. Let me do:

        base_h1 = 131
        base_h2 = 1313
        base_v1 = 13331
        base_v2 = 10007

   Then, for the vertical direction, we use base_v1 for mod1 and base_v2 for mod2? 

   Actually, the base_v1 is used only in mod1, and base_v2 only in mod2, so we can choose arbitrarily.

   We'll do:

        base_h1 = 131
        base_h2 = 1313
        base_v1 = 13331
        base_v2 = 10007

   And mod1 = 10**9+7, mod2 = 10**9+9.

Now, code structure:

   Steps:

        Read N, M
        Read first map: next N lines
        Read second map: next M lines

        Precompute for first map:
            H1_row1 = [0]*N   # for mod1
            H1_row2 = [0]*N   # for mod2

            for i in range(N):
                s = map1[i]
                h1 = 0
                h2 = 0
                for char in s:
                    h1 = (h1 * base_h1 + ord(char)) % mod1
                    h2 = (h2 * base_h2 + ord(char)) % mod2
                H1_row1[i] = h1
                H1_row2[i] = h2

        Precompute vertical hashes for the first map for each block starting at i:

            blocks1 = {}   # mapping (block_hash1, block_hash2) -> i
            # Precompute base_v1^M? Actually, we are doing a loop of M for each i, so we don't need power for base_v.

            for i in range(0, N-M+1):
                bh1 = 0
                bh2 = 0
                for r in range(i, i+M):
                    bh1 = (bh1 * base_v1 + H1_row1[r]) % mod1
                    bh2 = (bh2 * base_v2 + H1_row2[r]) % mod2
                blocks1[(bh1, bh2)] = i   # store the starting row i

        Precompute for second map:

            # Precompute prefix hashes for each row of the second map for both mods.

            # We have M rows in map2.
            H_sec1 = [[0]*(N+1) for _ in range(M)]   # for mod1
            H_sec2 = [[0]*(N+1) for _ in range(M)]   # for mod2

            # Precompute powers for base_h1^M and base_h2^M for the horizontal rolling?
            power1 = pow(base_h1, M, mod1)
            power2 = pow(base_h2, M, mod2)

            for r in range(M):
                s = map2[r]
                # for mod1
                for j in range(1, N+1):
                    H_sec1[r][j] = (H_sec1[r][j-1] * base_h1 + ord(s[j-1])) % mod1
                # for mod2
                for j in range(1, N+1):
                    H_sec2[r][j] = (H_sec2[r][j-1] * base_h2 + ord(s[j-1])) % mod2

            # Now, for each j in range(0, N-M+1):
            for j in range(0, N-M+1):
                bh1_sec = 0
                bh2_sec = 0
                for r in range(M):
                    # for row r, substring j to j+M-1: [j, j+M) -> indices j to j+M-1
                    # H_sec1[r][j+M] - H_sec1[r][j] * power1
                    h1 = (H_sec1[r][j+M] - H_sec1[r][j] * power1) % mod1
                    h2 = (H_sec2[r][j+M] - H_sec2[r][j] * power2) % mod2
                    # Ensure non-negative? The modulo operation in Python gives non-negative? 
                    # But if negative, we adjust.
                    if h1 < 0:
                        h1 += mod1
                    if h2 < 0:
                        h2 += mod2
                    # Now, combine vertically: use base_v1 and base_v2
                    bh1_sec = (bh1_sec * base_v1 + h1) % mod1
                    bh2_sec = (bh2_sec * base_v2 + h2) % mod2
                key = (bh1_sec, bh2_sec)
                if key in blocks1:
                    i_val = blocks1[key]
                    # Then we have solution: (i_val, j) -> output (i_val+1, j+1)
                    return (i_val+1, j+1)

        If not found, but problem says solution exists, so we should find one.

But wait: what if two different blocks have the same double hash? Then we store only one i? But we break at the first j that matches. But if there's a collision, we might get a wrong i? 

We can avoid by verifying? But the problem constraints: M=200, so comparing 40000 characters for one pair is acceptable? Worst-case if we get a collision, we do a full check. But we have at most 2000 j's, and the dictionary for the first map has 2000 i's. The chance of collision is low, but to be safe, we can verify the block when we get a match.

But the problem says solution exists, so at least one (i,j) is correct. But we might get a false positive? 

So we can:

        if key in blocks1:
            i_val = blocks1[key]
            # Check the block: 
            # Block in first map: rows i_val to i_val+M-1, each entire row (M chars)
            # Block in second map: for each row r in [0, M-1], substring [j:j+M] of map2[r]
            match = True
            for r in range(M):
                row1 = map1[i_val + r]
                row2 = map2[r][j:j+M]
                if row1 != row2:
                    match = False
                    break
            if match:
                return (i_val+1, j+1)

        But if not match, we continue to next j? 

But what if the same key appears for multiple i? Then we stored only one i? So we should store all i's? Or we can store the first i we computed for that key? And if it fails, we remove that key? But then we cannot use the dictionary for other j's? 

Alternatively, we can store a list of i's for each key? But then if we get a collision, we iterate over all i's that produced that key until we find the correct one? 

But worst-case, if the hash function is bad, we might get many i's with the same key? Then we have to check many i's for each j? 

However, the total number of i's is 2000, and the dictionary keys are double hashes, so the chance of collision is very low. But to be safe, we can store the first i for each key and if the block doesn't match, we skip and continue. But then we might miss a solution? Because the same key might be produced by two different blocks? 

But the problem says solution exists. So if we skip, we might not find the solution? 

So we should store all i's that have the same key? 

Alternatively, we can avoid storing the blocks in a dictionary and instead precompute all H1_block and then for each j, we check if there's any i such that the double hash matches? But we don't store i's? Actually, we stored in a dictionary. 

We can do:

        blocks1 = {}   # key: (bh1, bh2) -> list of i's

        Then when we get a key for j, we get the list of i's and check each one until we find a match.

        But worst-case, if we have 2000 i's and 2000 j's, and the hash function is bad (all blocks have the same hash), then we do 2000*2000*M*M = 4e6 * 40000 = 160e9, which is too slow.

Therefore, we must rely on the double hash to be collision resistant. Given the constraints (total blocks about 4000), we can use a double hash and skip the verification. The chance of a false positive is about 1/(mod1 * mod2) which is 1e-18, so negligible.

So we can skip the verification.

But to be safe, we can choose large mods and random bases? 

Alternatively, we can do without double hashing: use a single mod but with a very large modulus? But 10**18? But then we have to use Python ints and mod operations might be slow? 

But the constraints are small: 400000 operations per precomputation, so 400000 * 4 = 1.6e6 mod operations? That's acceptable. But we use two mods? Same.

So we'll do double hashing without verification.

Edge: when M=0? But M>=1.

Let me test with the example.

Example: 
N=10, M=5

First map rows 3 to 7 (0-indexed) are:
3: "mayth"
4: "eforc"
5: "ebewi"
6: "thyou"
7: "hctwo"

For the first map, we compute row hashes for each row.

Then for the block starting at i=3 (0-indexed), we combine:

   bh1 = 0
   for r in [3,4,5,6,7]:
        bh1 = (bh1 * base_v1 + H1_row1[r]) % mod1
        similarly for bh2.

For the second map, for j=5 (0-indexed), we compute for each row r in [0,4] the substring from j=5 to j+5=10? Actually, j=5, then substring [5:10] (0-indexed).

Row0: "somermayth" -> "mayth"
Row1: "andomeforc" -> "eforc"
etc.

Then we compute the row_hash for each of these substrings (which should be the same as the row_hash in the first map for the corresponding rows? Actually, the row_hash for "mayth" in the first map (row3) should be the same as the row_hash for "mayth" in the second map (row0 substring)? Because we use the same base_h and mod.

Then we combine the same way: same base_v and mod, so the block_hash should be the same.

Therefore, it should match.

Now, complexity:

   Precomputation for first map rows: O(N*M) = 2000*200 = 400000, which is acceptable.

   Precomputation for first map blocks: O((N-M)*M) = 2000*200 = 400000.

   Precomputation for second map prefix arrays: O(M*N) = 200*2000=400000.

   Precomputation for second map blocks: O((N-M)*M) = 2000*200=400000.

   Total operations: 1.6e6, which is acceptable in Python.

Now, space: 
   We store: 
        H1_row1, H1_row2: 2*N = 4000 integers.
        blocks1 dictionary: at most N-M+1 = 2000 entries.

        For second map: we store two 2D arrays: H_sec1 and H_sec2, each of size M*(N+1) = 200*2001=400200 integers per array, so about 800400 integers.

        Also, we store the two maps: N*M + M*N = 2*N*M = 2*2000*200=800000 characters? But that's given.

        Total integers: about 4000 + 2000 + 800400*2? Actually, the arrays: H_sec1 and H_sec2: each is M*(N+1) = 200*2001=400200, so two arrays: 800400 integers. Then the dictionary: 2000 entries, each with a tuple of two integers and one integer value: so 2000*3 = 6000 integers? 

        Total: 4000 (for H1_row) + 800400*2 (for H_sec) + 2000*3 (for dictionary) + 2000 (for H1_block precomputation? We don't store separately) -> about 1.6e6 integers? And the maps: 2000*5 + 5*10 = 10000+50=10050 characters? Actually, the first map: 2000 rows of 200 characters: 400000 characters. The second map: 200 rows of 2000 characters: 400000 characters. So total 800000 characters.

        This is acceptable.

Now, code implementation:

   Steps:

        mod1 = 10**9+7
        mod2 = 10**9+9
        base_h1 = 131   # for horizontal in mod1
        base_h2 = 1313  # for horizontal in mod2
        base_v1 = 13331 # for vertical in mod1
        base_v2 = 10007 # for vertical in mod2

        Read input.

        # First map: N rows, each of M characters
        map1 = [input().strip() for _ in range(N)]

        # Second map: M rows, each of N characters
        map2 = [input().strip() for _ in range(M)]

        # Precompute row hashes for first map
        H1_row1 = [0] * N
        H1_row2 = [0] * N
        for i in range(N):
            s = map1[i]
            h1 = 0
            h2 = 0
            for c in s:
                h1 = (h1 * base_h1 + ord(c)) % mod1
                h2 = (h2 * base_h2 + ord(c)) % mod2
            H1_row1[i] = h1
            H1_row2[i] = h2

        # Precompute blocks for first map: for i from 0 to N-M
        blocks_dict = {}   # (bh1, bh2) -> i
        for i in range(0, N - M + 1):
            bh1 = 0
            bh2 = 0
            for r in range(i, i+M):
                bh1 = (bh1 * base_v1 + H1_row1[r]) % mod1
                bh2 = (bh2 * base_v2 + H1_row2[r]) % mod2
            # Store in dictionary: if the same block hash appears twice, we store the first i? Or any? Since any solution is acceptable, we can store the first i.
            # But if we get the same block twice? Then we overwrite? It doesn't matter because we only need one solution.
            blocks_dict[(bh1, bh2)] = i

        # Precompute for second map: prefix arrays for each row for both mods.
        # We need to precompute power1 = base_h1^M % mod1, power2 = base_h2^M % mod2
        power1 = pow(base_h1, M, mod1)
        power2 = pow(base_h2, M, mod2)

        # Initialize H_sec1 and H_sec2: 2D arrays of size M x (N+1)
        H_sec1 = [[0]*(N+1) for _ in range(M)]
        H_sec2 = [[0]*(N+1) for _ in range(M)]

        for r in range(M):
            s = map2[r]
            # For mod1
            for j in range(1, N+1):
                H_sec1[r][j] = (H_sec1[r][j-1] * base_h1 + ord(s[j-1])) % mod1
            for j in range(1, N+1):
                H_sec2[r][j] = (H_sec2[r][j-1] * base_h2 + ord(s[j-1])) % mod2

        # Now, for each j in range(0, N - M + 1):
        for j in range(0, N - M + 1):
            bh1_sec = 0
            bh2_sec = 0
            for r in range(M):
                # Compute the substring hash for row r, from j to j+M-1 for mod1 and mod2
                # For mod1: 
                h1 = (H_sec1[r][j+M] - H_sec1[r][j] * power1) % mod1
                if h1 < 0:
                    h1 += mod1
                h2 = (H_sec2[r][j+M] - H_sec2[r][j] * power2) % mod2
                if h2 < 0:
                    h2 += mod2
                # Now combine vertically
                bh1_sec = (bh1_sec * base_v1 + h1) % mod1
                bh2_sec = (bh2_sec * base_v2 + h2) % mod2
            key = (bh1_sec, bh2_sec)
            if key in blocks_dict:
                i_val = blocks_dict[key]
                # We have a candidate: i_val and j
                # We'll break and return
                return (i_val+1, j+1)

        # If we get here, no solution? But problem says guaranteed. So we can return (0,0) or something? 
        return (0,0)   # but should not happen.

But wait: the problem says solution guaranteed to exist.

Let me test with a small example.

Example: 
N=2, M=1
map1:
  a
  b
map2:
  ab

We should find: 
  In first map: the 1x1 block can be at row0: ['a'] or row1: ['b'].
  In second map: we have 1 row of 2 characters: "ab". We can start at column0: substring "a", or column1: substring "b".

  So two solutions: (1,1) and (2,2). 

  Our method:

      First map row hashes:
          row0: 'a' -> h1 = ord('a') = 97 for mod1? But modulo mod1? Actually, 97.
          row1: 'b' -> 98.

      Then blocks_dict:
          i=0: block_hash = (97, 97)   # actually for mod2: same? 
          i=1: block_hash = (98, 98)

      Second map: 
          M=1, so one row: "ab"
          Precompute prefix for row0: 
             H_sec1[0][0]=0
             j=1: H_sec1[0][1] = 0*base_h1 + 'a' -> 97
             j=2: H_sec1[0][2] = 97 * base_h1 + 98

          power1 = base_h1^1 = base_h1.

          For j=0: 
                h1 = (H_sec1[0][1] - H_sec1[0][0]*base_h1) % mod1 = (97 - 0) % mod1 = 97
                Then bh1_sec = (0 * base_v1 + 97) % mod1 = 97
          For j=1:
                h1 = (H_sec1[0][2] - H_sec1[0][1]*base_h1) % mod1 = ( (97*base_h1+98) - 97*base_h1 ) % mod1 = 98

          So keys: (97, ...) and (98, ...) -> will match i=0 and i=1 respectively.

      Then we return (0+1,0+1) for j=0 -> (1,1) and for j=1 we return (2,2). Actually, we break at the first j that matches. So if we iterate j from 0 to N-M (which is 1) then we first j=0: key=97 -> matches i=0 -> output (1,1). 

      But if we want (2,2) we would have to continue? The problem says any solution is acceptable.

So it's correct.

What if the same block appears multiple times? Then we return the first j that matches the first i? That's acceptable.

Now, let me consider a case with M=2, N=3.

Example:
  map1 = 
    ab
    cd
    ef

  map2 = 
    abx
    cdy

  We need a 2x2 block in the first map: 
      i=0: [ab, cd]
      i=1: [cd, ef]

  In the second map: 
      We have 2 rows of 3 characters.
      j=0: [ab, cd] -> same as first map's i=0.
      j=1: [bx, dy] -> doesn't match.

  So solution: (1,1) for the first block.

  Our method:

      First map row hashes (M=2, so each row has 2 characters):

          row0: "ab": 
               h1 = (0 * base_h1 + 'a') * base_h1 + 'b' = (97 * base_h1 + 98) % mod1
          Similarly, row1: (99 * base_h1 + 100) 
          row2: (101 * base_h1 + 102)

      Then for i=0 (block of 2 rows):
          bh1 = (0 * base_v1 + h0) * base_v1 + h1 = h0 * base_v1 + h1
      i=1: bh1 = h1 * base_v1 + h2

      Second map:
          For row0: prefix: [0, a, ab, abx] -> for j=0, M=2: substring "ab": 
                h0_sec = (prefix[2] - prefix[0]*base_h1^2) % mod1? 
                But our method: 
                    H_sec1[0][0]=0, H_sec1[0][1]=97, H_sec1[0][2]=97*base_h1+98, H_sec1[0][3]=... 
                Our substring from j=0 to j+2=2: 
                    h1 = (H_sec1[0][2] - H_sec1[0][0] * (base_h1^2)) % mod1 = (97*base_h1+98) - 0 = 97*base_h1+98 -> same as first map row0.

          Similarly for row1: j=0: "cd" -> same as first map row1.

          Then for j=0: 
                bh1_sec = (0 * base_v1 + h0_sec) * base_v1 + h1_sec = same as first map i=0.

          So we return (0+1, 0+1) = (1,1)

      Correct.

Now, I think it's solid.

But note: the power for base_h1^M: we computed as pow(base_h1, M, mod1). Then in the substring hash, we do:

        h1 = (H_sec1[r][j+M] - H_sec1[r][j] * power1) % mod1

This is the standard rolling hash for a substring? 

Yes: 
   Let H[0..n] be the prefix hash, then for substring [j, j+L-1], the hash is:

        H[j+L] - H[j] * (base^L) mod mod

   This is correct.

Now, code:

   We'll implement as described.

   One more thing: the bases and mods. We can choose:

        base_h1 = 131
        base_h2 = 1313
        base_v1 = 13331
        base_v2 = 10007
        mod1 = 10**9+7
        mod2 = 10**9+9

   But we can also use random bases? But fixed is fine.

   However, to avoid anti-hash test cases? The constraints are small so no need.

Let me code accordingly.

But note: the base_v1 and base_v2: we use them for the vertical direction. The vertical direction has M rows, so we need to combine M row hashes. We don't precompute the power for base_v? Because we are doing a loop.

We can optimize the vertical hash by precomputation? But M is small (200), so we don't need.

Now, let me write the code.

Note: We must use the same hashing formula for the rows in the first map and the substrings in the second map. For the first map, we computed the row hash by:

        h = 0
        for c in s:
            h = (h * base + ord(c)) % mod

   For the second map, we compute the substring hash by the rolling method, which yields the same value as if we did:

        h = 0
        for k in range(M):
            h = (h * base + ord(s[j+k])) % mod

   And that equals: 
        h = ord(s[j])*base^(M-1) + ord(s[j+1])*base^(M-2) + ... + ord(s[j+M-1])*base^0

   But for the first map, we did:

        for the entire row s (of length M): 
            h = 0
            for c in s:   # which is s[0] to s[M-1]
                h = (h * base + ord(c)) % mod

        which is: 
            h = ord(s[0])*base^(M-1) + ... + ord(s[M-1])*base^0

   So the same as the substring? 

   But note: in the first map, we are taking the entire row (which is of length M) and in the second map we are taking a substring of length M. So the polynomial representation is the same.

   Therefore, if the strings are the same, the row_hash is the same.

But what if the row in the first map is "ab" and the substring in the second map is "ab", then they have the same row_hash? Yes.

Therefore, the method is consistent.

Now, we write the code.

One more edge: when M=0? But M>=1.

We'll assume M>=1.

Let me run the example from the problem:

   Input: 
        10 5
        somer
        andom
        noise
        mayth
        eforc
        ebewi
        thyou
        hctwo
        again
        noise
        somermayth
        andomeforc
        noiseebewi
        againthyou
        noisehctwo

   We expect output: 4 6 -> meaning i=3 (0-indexed) and j=5 (0-indexed).

   We'll compute the block_hash for the first map starting at i=3:

        Rows: 3 to 7: 
            row3: "mayth" -> compute its row_hash (for mod1 and mod2) -> call it h3
            row4: "eforc" -> h4
            row5: "ebewi" -> h5
            row6: "thyou" -> h6
            row7: "hctwo" -> h7

        Then block_hash1 = ( ( ( ( (0 * base_v1 + h3) * base_v1 + h4 ) * base_v1 + h5 ) * base_v1 + h6 ) * base_v1 + h7 ) % mod1

   For the second map, at j=5:

        For row0: "somermayth" -> substring from 5 to 10: "mayth" -> same as h3
        row1: "andomeforc" -> "eforc" -> h4
        row2: "noiseebewi" -> "ebewi" -> h5
        row3: "agaithyou" -> substring "thyou"? Actually, the input says "againthyou", so substring from 5: "thyou" -> but the row in the first map row6 is "thyou", so same? Actually, no: row3 in the second map is "againthyou", so substring from 5: "thyou", which is the same as row6? 

        But wait: the block in the first map: row3 is "mayth", row4 "eforc", row5 "ebewi", row6 "thyou", row7 "hctwo".

        In the second map: 
            row0: substring "mayth" -> matches row3
            row1: "eforc" -> matches row4
            row2: "ebewi" -> matches row5
            row3: "thyou" -> matches row6
            row4: "hctwo" -> matches row7

        So the row_hashes are the same.

        Then the block_hash for the second map at j=5 is:

            ( ( ( ( (0 * base_v1 + h3) * base_v1 + h4 ) * base_v1 + h5 ) * base_v1 + h6 ) * base_v1 + h7 ) % mod1 -> same as the first map.

        So it matches.

   Therefore, we output (3+1, 5+1) = (4,6) -> correct.

Now, code:

   We'll code accordingly.

   One optimization: for the second map, we don't need to store the entire 2D arrays for H_sec1 and H_sec2? Actually, we can compute the substring hash on the fly without storing the entire prefix arrays? But then for each j and each row, we would need to compute the substring hash by iterating M characters? That would be O(M) per row per j, and total O(M * (N-M) * M) = 200 * 2000 * 200 = 80e6, which is acceptable? But 80e6 in Python might be borderline? 80e6 operations is acceptable in Pyton in C++ but in Python it might be a few seconds? But we have two mods and two bases? 

   Alternatively, we precomputed the prefix arrays to get O(1) per substring.

   We did precomputation.

   But if we are concerned about space, note that M is only 200, and N is 2000, so the total space for the two 2D arrays is 2 * 200 * 2001 * size of integer? Each integer is 8 bytes? Then 2 * 200 * 2001 * 8 = 6.4e6 bytes? 6.4 MB? Acceptable.

   So we'll do as above.

Now, I'll write the code accordingly.

Let me test with a small example in code? We did.

But we can run the provided example.

Alternatively, we can write and run on the example.

But for now, we'll write the code as described.

One more note: the problem says the characters are lower-case Latin. So ord(c) from 97 to 122. No issue.

Code:

   We'll use:

        mod1 = 10**9+7
        mod2 = 10**9+9
        base_h1 = 131
        base_h2 = 1313
        base_v1 = 13331
        base_v2 = 10007

   But we can choose different base_v? Actually, base_v1 and base_v2 can be any distinct? But we use them for the vertical direction. We'll keep as above.

   Now, implement.

   We note: the second map has M rows, each of length N.

   Precomputation for the second map: we do two separate loops for mod1 and mod2? Or we can do in one loop? Actually, we can do:

        for r in range(M):
            s = map2[r]
            # for mod1
            for j in range(1, N+1):
                H_sec1[r][j] = (H_sec1[r][j-1] * base_h1 + ord(s[j-1])) % mod1
            # for mod2
            for j in range(1, N+1):
                H_sec2[r][j] = (H_sec2[r][j-1] * base_h2 + ord(s[j-1])) % mod2

   We do two separate loops for each row? That's acceptable.

   Then, we compute the powers:

        power1 = pow(base_h1, M, mod1)
        power2 = pow(base_h2, M, mod2)

   Then, we iterate j from 0 to N-M (inclusive).

   For each j, we iterate r from 0 to M-1, compute the substring hash for that row, then combine vertically.

   Then check.

Now, we write the function accordingly.

I think we are done.

Let me write the code in the solve function.

Note: the problem says M<=N, so we don't worry about M>N.

We'll code accordingly.

We'll use:

        import sys? Or we are in a class.

        The starter code:

            class Solution:
                def solve(self, N: int, M: int, map1: List[str], map2: List[str]) -> Tuple[int, int]:

        So we are given N, M, map1 (list of N strings, each of length M), map2 (list of M strings, each of length N).

        We'll implement the above.

But note: the second map has M rows, each of length N.

Now, code:

        mod1 = 10**9+7
        mod2 = 10**9+9
        base_h1 = 131
        base_h2 = 1313
        base_v1 = 13331
        base_v2 = 10007

        # Step 1: Precompute row hashes for map1 for both mods
        H1_row1 = [0] * N
        H1_row2 = [0] * N
        for i in range(N):
            s = map1[i]
            h1 = 0
            h2 = 0
            for c in s:
                h1 = (h1 * base_h1 + ord(c)) % mod1
                h2 = (h2 * base_h2 + ord(c)) % mod2
            H1_row1[i] = h1
            H1_row2[i] = h2

        # Step 2: Precompute block hashes for the first map for each starting row i
        blocks_dict = {}
        # If M==0? but M>=1, so we do:
        for i in range(0, N - M + 1):
            bh1 = 0
            bh2 = 0
            for r in range(i, i+M):
                bh1 = (bh1 * base_v1 + H1_row1[r]) % mod1
                bh2 = (bh2 * base_v2 + H1_row2[r]) % mod2
            # Use tuple (bh1, bh2) as key, store i
            blocks_dict[(bh1, bh2)] = i

        # Step 3: Precompute prefix hashes for the second map for both mods
        # Precompute power for horizontal: base_h1^M mod mod1, base_h2^M mod mod2
        power1 = pow(base_h1, M, mod1)
        power2 = pow(base_h2, M, mod2)

        # Create 2D arrays: M rows, each with N+1
        # We'll use lists of lists
        H_sec1 = [[0]*(N+1) for _ in range(M)]
        H_sec2 = [[0]*(N+1) for _ in range(M)]

        for r in range(M):
            s = map2[r]
            # For mod1: H_sec1[r][0..N]
            for j in range(1, N+1):
                H_sec1[r][j] = (H_sec1[r][j-1] * base_h1 + ord(s[j-1])) % mod1
            for j in range(1, N+1):
                H_sec2[r][j] = (H_sec2[r][j-1] * base_h2 + ord(s[j-1])) % mod2

        # Step 4: Iterate j from 0 to N-M
        for j in range(0, N - M + 1):
            bh1_sec = 0
            bh2_sec = 0
            for r in range(M):
                # Compute the substring hash for row r, from j to j+M-1 for mod1 and mod2
                # For mod1:
                #   H_sec1[r][j+M] - H_sec1[r][j] * power1
                h1 = (H_sec1[r][j+M] - H_sec1[r][j] * power1) % mod1
                h2 = (H_sec2[r][j+M] - H_sec2[r][j] * power2) % mod2
                # Adjust negative modulo
                if h1 < 0:
                    h1 += mod1
                if h2 < 0:
                    h2 += mod2
                # Now combine vertically for the block
                bh1_sec = (bh1_sec * base_v1 + h1) % mod1
                bh2_sec = (bh2_sec * base_v2 + h2) % mod2
            key = (bh1_sec, bh2_sec)
            if key in blocks_dict:
                i_val = blocks_dict[key]
                return (i_val+1, j+1)

        # According to the problem, a solution exists, so we should have returned
        # If not, return (0,0) as fallback
        return (0,0)

But wait: what if the substring hash h1 or h2 is negative? We adjust. But in Python, the modulo operation already returns non-negative? 

For example: 

        a = -5
        a % mod1 = mod1 - 5, which is positive.

        So we don't need the adjustment? 

        But the formula: 

            h1 = (H_sec1[r][j+M] - H_sec1[r][j] * power1) % mod1

        This will be non-negative? Yes, because modulo operation.

        So we can remove the adjustment.

        But let me test: 

            In Python: (-5) % 10**9+7 = 10000000002? 

        So it's non-negative.

        Therefore, we can remove the if h1<0: h1+=mod1.

        But to be safe, we can leave it? Or remove.

        I'll remove.

        So:

            h1 = (H_sec1[r][j+M] - H_sec1[r][j] * power1) % mod1
            h2 = (H_sec2[r][j+M] - H_sec2[r][j] * power2) % mod2

        Then use.

Now, we can run the example.

But we don't have the actual input. However, we can test with the provided example.

Alternatively, we can run locally.

But for the purpose, we'll submit.

One more note: the base_v1 and base_v2: we use them for the vertical combination. The same as in the first map.

Now, I think it's done.

We'll write the code accordingly.

Let me test with the small example: N=2, M=1.

But we did the math.

I think it's correct.

We'll output the code.

Note: the problem constraints: N=2000, M=200, so the loops are 2000*200 = 400000 per precomputation, which is acceptable in Pyton? In Pyton, 400000 iterations is fast.

Total steps: about 4 * 400000 = 1.6e6 iterations, which is acceptable.

So we are good.

Now, the complete code:

## Final Solution

### Approach
The problem requires finding an M x M section in an N x M grid (first map) that matches an M x M section in an M x N grid (second map). The solution involves efficiently comparing all possible M x M blocks from both grids without resorting to a brute-force approach due to high computational complexity.

1. **Problem Analysis**: The task is to locate a starting position (i, j) such that the M x M block starting at row i in the first map matches the M x M block starting at column j in each row of the second