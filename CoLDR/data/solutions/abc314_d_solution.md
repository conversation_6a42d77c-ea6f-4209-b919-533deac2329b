# Solution for abc314_d.md

Generated at: 2025-07-22 06:33:17
Model: deepseek-reasoner

## Reasoning Process

Alright, I'm trying to solve this problem. Let me start by understanding what it requires. 

We have a string S of length N, which contains a mix of uppercase and lowercase English letters. Then, we have Q operations to perform on this string. Each operation is one of three types:

1. **Type 1 (t_i = 1)**: Change the x_i-th character of S to c_i. This is a direct character replacement at a specific position.
2. **Type 2 (t_i = 2)**: Convert all uppercase letters in S to lowercase. Here, x_i and c_i are not used (they are given as 0 and 'a' respectively).
3. **Type 3 (t_i = 3)**: Convert all lowercase letters in S to uppercase. Similarly, x_i and c_i are not used.

The goal is to perform all Q operations in order and then output the final string.

Constraints are significant: N and Q can each be up to 500,000. So, any brute-force solution that processes the entire string for each operation (especially for types 2 and 3) would be too slow. For example, if we do a full scan for every type 2 or 3 operation, that would be O(N) per operation, leading to O(Q * N) worst-case time, which is 500,000 * 500,000 = 250 billion operations. That's too slow in Python.

So, I need a smarter approach.

Let me think about how to handle the operations efficiently.

First, note that the operations are sequential. We must process them in the given order. 

The main challenge is the type 2 and 3 operations, which affect the entire string. Instead of applying them immediately to the entire string, which is costly, we can try to track the "current case state" and defer the actual case conversion until necessary.

But wait, there's a problem: if we have a type 2 operation, it sets the entire string to lowercase. Then, if we have a type 1 operation that sets a specific character to an uppercase letter, and then a type 3 operation, that uppercase letter would become uppercase again? Hmm, let me see.

Actually, if we do:

1. Type 2: set all to lowercase.
2. Type 1: change one character to 'Y' (uppercase).
3. Type 3: set all to uppercase.

Then the character set in step 2 becomes 'Y' (remains uppercase) in step 3? Actually, no. In step 2, the entire string becomes lowercase. Then step 1 changes the 4th character to 'Y' (uppercase). Then step 3 converts all lowercase to uppercase. So, the 'Y' is already uppercase, so it remains, but the rest become uppercase. So the final string has the 4th character as 'Y' and the rest uppercase.

But if we just track the last case-conversion operation, then when we do a type 1 operation, we need to know if the current global case setting is lowercase or uppercase to adjust the character we set? 

Alternatively, we can think: the type 2 and 3 operations set a global flag. Let's say we maintain a variable `current_case` that could be 0, 2, or 3? Or perhaps `last_global_op` which records the last global operation (type 2 or 3) that occurred after any type 1 operations.

But then, when we set a character with type 1, we need to record the character and also the state of the global flag at that time. Because if later a global operation (type 2 or 3) comes, it will override the case of that character, unless there's a subsequent type 1 operation that sets it again.

Wait, that might be a key insight. The effect of a global operation (type 2 or 3) applies to all characters that have not been set by a more recent type 1 operation. 

So, we can do the following:

1. We'll maintain a variable `global_flag` which can be:
   - 0: no global operation has been applied (or we can use 2 and 3 for the last global op)
   - 2: last global op was to set to lowercase
   - 3: last global op was to set to uppercase

But actually, we might have multiple global operations. Each global operation overrides the previous ones. So, only the last global operation matters? Not exactly, because if we have type 1 operations after a global operation, they set individual characters which are not affected by the next global operation until overridden.

So, the idea is:

- Instead of applying global operations immediately, we can remember the last global operation (if any) that occurred after the last type 1 operation. But actually, we can think in reverse.

Alternatively, we can process the operations from the last one backwards? But the problem requires processing in order.

Another approach is to use lazy propagation. We can maintain an array `last_modified` which records the index of the last operation that modified each character. Then, for each character, we can check if there was any global operation after the last modification. 

But how would we do that? We can store:

- `last_global_op_index`: the index of the last global operation (if any) that happened after the last type 1 operation for that character.

But that seems complex because we need to know for each character what the last operation that affected it was.

Alternatively, we can note that the effect of any global operation applies to all characters that were not updated after that global operation. So, if we have a global operation at operation i, and then a type 1 operation at operation j (j > i) on a character, then the global operation i does not affect that character because the type 1 operation j sets it explicitly.

So, we can do:

1. We'll keep an array `last_op` of length N, which records the operation index (or time) at which the character was last updated by a type 1 operation. Initially, we can set `last_op[i] = 0` for all i.

2. We'll also record the global operations: we can keep an array `global_ops` that records the type (2 or 3) and the operation index (time) when they occurred.

3. Then, after processing all operations, for each character i:
   - If there was a type 1 operation after the last global operation, then we use the character set by that type 1 operation.
   - Otherwise, the character's case is determined by the last global operation that occurred after the last type 1 operation for that character.

But we need to know the last global operation that happened after the last type 1 operation for each character? Actually, no. We can do:

   - For each character, we have `last_op[i]` which is the time of the last type 1 operation for that character.
   - Then, we need to know the last global operation that happened after `last_op[i]`. 

So, we can precompute an array `global_after` that, for each operation index, gives the last global operation that occurred after that index? 

Alternatively, we can process the operations and at the end, for each character, look at the last time it was set (by type 1). Then, we need to know if after that time, there was any global operation, and if so, which type was the last one.

But then, we have to consider that multiple global operations might have occurred. We only care about the last global operation that happened after the last type 1 on that character.

We can precompute an array `last_global_type` (or `last_global_after`) for each operation index? Actually, we can create an array `global_timeline` that stores for each operation index, the cumulative effect? But the global operations are not cumulative; they set the state. So, the last global operation in the entire sequence sets the state for all characters not updated later.

Wait, actually, the entire sequence of operations is fixed. So, we can do:

   Step 1: Precompute an array `global_after` of length Q+1 (or Q+2) that stores the last global operation type (if any) that occurs after a given time. 

But we can do it backwards: 

   - Let `global_ops` be an array of length Q+1, where `global_ops[i]` is the global operation at time i (if operation i is type 2 or 3). Otherwise, we can set it to 0 or None.

   - Then, create an array `last_global` of length Q+2 (to avoid index out-of-bounds) such that `last_global[i]` is the last global operation that occurred from operation i to the end. We can fill this backwards.

   For example, we can do:

      last_global[Q] = 0   # meaning no global op after the last operation?
      Actually, we can set an array `suffix_global` where `suffix_global[i]` = the last global operation type from operation i to the end.

   How to fill:

      We'll create an array `suffix_global` of length Q+1 (indexed from 0 to Q). We process from the last operation to the first.

      Let `suffix_global[Q] = 0` (meaning after the last operation, no additional global op? Actually, we want for each operation index, the last global op that happened at or after that operation).

      Alternatively, we can define an array `global_history` of length Q+1, where index 0 to Q-1 are the operations. Then, we can create an array `last_global_after` such that for each time t (from 0 to Q), `last_global_after[t]` is the last global operation type (2 or 3) that occurs at or after time t. If there are multiple, the last one (closest to the end) is the one that matters.

      We can compute it backwards:

          last_global_after[Q] = 0   # no global op after the last operation? But the last operation might be global.

          Actually, we can do:

          Let `global_after` be an array of length Q+2, all initialized to 0.
          Then, for i from Q-1 down to 0:
              if operation i is type 2 or 3:
                 global_after[i] = operation_i_type
              else:
                 global_after[i] = global_after[i+1]   # because the next operation's global setting is the same as the current if we don't have a global at i?

          But wait, no: the global_after array should store the last global operation that occurred from i to the end. So, if we have a global operation at i, then from i onward, until the next global operation, this one is the last? Actually, the last global operation in the entire sequence from i to the end is the one that will set the case for characters not updated after i.

          Actually, we want the very last global operation in the entire operation list from i to the end. So, we can do:

             global_after[i] = global_after[i+1]   # by default
             if operation i is type 2 or 3:
                 global_after[i] = operation_i_type   # because if a global op happens at i, then that overrides any previous? But we are going backwards, so we want the last one? Actually, no: we want the last global op that appears from i onward. Since we are traversing backwards, when we hit a global op, we set global_after[i] to that type, and then for the next (earlier) operation, we use the same until we hit another global op.

          But that's incorrect. For example, if we have:

          Operation 0: type 2
          Operation 1: type 3
          Operation 2: type 1

          Then, for i=0: we have a global op (type2) and then at i=1, we have type3. The last global op from i=0 onward is type3 (because it's the last one). Similarly, from i=1 onward, the last global op is type3, and from i=2 onward, there's no global op? Actually, operation 2 is type1, so no global.

          So, we should set:

             global_after[0] = 3
             global_after[1] = 3
             global_after[2] = 0  (or none)

          How to compute:

             Let global_after[Q] = 0 (meaning after the last operation, no global op has occurred? Actually, we can index operations from 0 to Q-1).

          Instead, we can create an array `last_global` of length Q (for each operation index) and then set:

             last_global = [0] * (Q+1)   # index 0 to Q
             last_global[Q] = 0   # beyond the last operation, no global

             for i in range(Q-1, -1, -1):
                 if operations[i][0] == 2 or operations[i][0] == 3:
                     last_global[i] = operations[i][0]
                 else:
                     last_global[i] = last_global[i+1]

          Then, for each character, we know:

             - The last time it was updated by a type 1 operation, say at time t (operation index t).
             - Then, the last global operation after t is last_global[t+1]? Because the type1 op at time t sets the character, and then any global op after t (from t+1 to the end) can override the case? Actually, no: the type1 op sets the character to a specific letter. Then, if after that, a global op occurs, it will change the case of that character.

          So, for a character that was last updated at time t (by a type1 op), we need to apply the last global operation that occurred after time t (i.e., from time t+1 to the end). That is stored in last_global[t+1].

          Then, if last_global[t+1] is:
             - 0: then the character remains as set at time t.
             - 2: then we convert that character to lowercase.
             - 3: then we convert that character to uppercase.

          But note: when we set the character at time t, we set it to a specific case (the c_i given). Then, if after that, a global op occurs, it will change the case of that character. So, we have to apply that conversion.

          Example: 
             Operation t: type1, sets character to 'Y' (uppercase). 
             Then, a global op type2 (to lowercase) occurs. Then, the character becomes 'y'.

          So, for each character i:

             - Let t = last_op[i]  (the last operation index that set this character by type1). If the character was never set by type1, then t = -1 (or 0, but we need to represent initial state).

          How to handle the initial string? We can consider that the initial string is set at time 0 (before any operations). Then, the last_global[0] will be the last global operation from the beginning to the end.

          So, we can do:

             last_op = an array of length N, initialized to -1 (meaning no type1 op has set this character yet).

          Then, we process each operation:

             For each operation index i (0-indexed) in 0 to Q-1:
                 if operation is type1 at position x_i (convert to 0-indexed) to character c_i:
                     set the character at x_i to c_i (but we don't store the entire string, we store the last op index and the character set at that time)
                     and set last_op[x_i] = i   (the operation index)

          Then, after processing all operations, for each character j:

             - If last_op[j] == -1 (never set by type1), then we take the initial character S[j], and then we need to apply the last global operation that occurred from the start (i.e., last_global[0]? But wait, we have to consider that the initial character is set at time -1 (before any operation). Then, the global op that applies is the last_global[0] (the first element in the last_global array we built).

             - Else, if last_op[j] = k (an index), then we have the character set at operation k (c_k). Then, we look at last_global[k+1] (the next operation after k to the end). 

          Then, we convert that character:

             - If last_global_after (which we call `last_global[k+1]`) is 0, then the character is as set at k (c_k).
             - If 2, then we convert c_k to lowercase.
             - If 3, then we convert c_k to uppercase.

          But note: the character set at operation k could be uppercase or lowercase. Then, when we apply the global op, we change the case accordingly.

          However, if we set a character to 'Y' (uppercase) and then a global op type2 (to lowercase) occurs, we convert that 'Y' to 'y'. Similarly, if we set to 'y' and then type3, we get 'Y'.

          So, we have to store the character as set (in its original case) at the type1 op, and then after, apply the case conversion if a global op occurred.

          But what if multiple global ops occur after a type1? Only the last one matters. And we have that in `last_global[k+1]`.

          So, the steps are:

            Step 1: Precompute the `last_global` array for operations from the end backwards.

            Step 2: Initialize an array `last_op` for each character index to -1 (meaning not updated by type1).

            Step 3: Also, we need an array `char_set` of length N to store the character that was set at the last type1 operation for each position. Initially, we can set `char_set` to the initial S? But we don't need to store the initial S for positions that are updated by type1. Alternatively, we can store:

               - For each position i, we store the last character set by a type1 operation. If none, then we'll use the initial S[i] and then apply the global conversion from the beginning (last_global[0]).

            Actually, we can do:

               Let `result_chars` = [None] * N   # we will build the final character for each position.

               Also, we create an array `last_time` of length N, initially -1, and an array `last_char` of length N, initially the initial characters? Or we can store the initial character as the base.

            Alternatively, we can:

               - Create an array `current_char` of the initial S? But we don't want to update the entire array for each operation.

            Instead, we can do:

               We are going to process each operation sequentially, but we only record the last type1 operation for each position and the character set. Then, after all operations, we decide the final character by applying the global conversion if needed.

            So:

               last_time = [-1] * N   # for each index in the string, the last operation index that updated it (if any)
               last_char = [''] * N   # we can initialize with the initial S? Or we can have:

               Actually, we can do:

                  last_char = list(S)   # the initial character for each position.

               Then, for each operation of type1 at index i (0-indexed operation index):

                  pos = x_i - 1   # because the input is 1-indexed
                  last_char[pos] = c_i   # the character set at that operation
                  last_time[pos] = i      # the operation index

            Then, after processing all operations, for each position pos:

                  if last_time[pos] == -1:   # never updated by type1
                      # then we use the initial character: last_char[pos] is the initial character
                      # and we apply the global conversion from the very beginning? 
                      # Actually, the initial character was set at time -1, so the global op that applies is the one from operation0 onward: last_global[0] (if defined)

                  else:
                      t = last_time[pos]
                      # the character is last_char[pos]
                      # then we look at the last_global array at index t+1: which gives the last global op after operation t.

            How do we define last_global? We defined an array `last_global` of length Q+1? Actually, we defined it for indices 0 to Q, where `last_global[i]` is the last global op from operation i to the end.

            So, for a character set at operation t, we need last_global[t+1]? 

            But note: the global op that occurs at operation t (the same time) would not affect this character because the type1 op sets it, and then if a global op occurs at the same operation, it would run after? The operations are sequential. So, if at operation t we set the character, and then at the same operation index, we don't have a global op? Because the operation is either type1 or global. So, at a given operation, it's one type.

            Therefore, for a type1 op at index t, the global ops that affect it are those that occur at operation indices > t (i.e., after).

            So, we need last_global[t+1] (since operations are 0-indexed, and we have from t+1 to the end).

            For a character that was never updated, we can consider its last update time as -1, and then we use last_global[0] (because we want global ops from the start, i.e., operation index 0).

            Then, for each position:

                if last_global_after is 0: then leave the character as is.
                if 2: convert to lowercase.
                if 3: convert to uppercase.

            But note: the character stored in `last_char` might be either case. We apply the conversion only if the global_after flag is set and not 0.

            So, for a character `ch` and global flag `flag`:

                if flag == 0: then output ch
                if flag == 2: output ch.lower()
                if flag == 3: output ch.upper()

            However, wait: what if the global_after flag is set, but the character is a digit? The problem states only letters. So, we can safely convert.

            But the problem says the string consists of uppercase and lowercase English letters. So, no non-letters.

            So, we can do:

               if flag == 0: 
                   output = ch
               elif flag == 2:
                   output = ch.lower()
               elif flag == 3:
                   output = ch.upper()

            But note: if the character is set by a type1 op to 'Y' (uppercase) and then a flag 2 (to lowercase) is applied, we get 'y'. Similarly, if set to 'y' and flag 3, we get 'Y'.

            This matches the requirement.

            Example 1: 

                S = "AtCoder", operations:

                Operation0: type1, x4 -> change 4th char (0-indexed index3) to 'i' (lowercase). 
                   So, last_char[3] = 'i', last_time[3]=0.

                Operation1: type3 (to uppercase) -> global op. 
                   We record in last_global: 
                      We build last_global array backwards:

                         Let last_global = [0]*(Q+1)  # Q=5, so indices 0 to 5.
                         last_global[5] = 0 (beyond the last op)
                         Operation4: type1 -> not global, so last_global[4] = last_global[5] = 0.
                         Operation3: type2 -> global, so last_global[3] = 2
                         Operation2: type1 -> last_global[2] = last_global[3] = 2
                         Operation1: type3 -> last_global[1] = 3
                         Operation0: type1 -> last_global[0] = last_global[1] = 3

                Then, for each character:

                   For the character at index3 (4th char): last_time[3]=0. Then we look at last_global[1] (since t=0, then t+1=1). last_global[1] is 3? But wait, the last_global array we built: 

                   We built for each starting index i, the last global op from i to the end.

                   So, for the character set at op0, we need the last global op from op1 to the end. That is stored in last_global[1]. And last_global[1] is 3? 

                   But in the example, after op0, we have op1 (type3) -> then op2 (type1: set 5th char to 'b'), then op3 (type2: to lowercase), then op4 (type1: set 4th char to 'Y').

                   Actually, the character at index3 was set at op0 and then reset at op4. So, for the character at index3, last_time is 4 (because op4 sets it again). 

                   Let me recount the operations for the example:

                      Operation0: (1,4,i) -> set index3 to 'i'
                      Operation1: (3,0,a) -> convert all to uppercase: so the entire string becomes uppercase -> then the 'i' becomes 'I'
                      Operation2: (1,5,b) -> set index4 (5th char) to 'b' -> so index4 becomes 'b' (but then the global op1 set everything to uppercase, so now we set index4 to 'b'? So becomes 'b' (lowercase? but then the string is still in uppercase? Actually, no: after op1, the string is uppercase. Then op2 sets the 5th char to 'b' (which is lowercase). Then the string becomes: "I" at index3, and at index4: 'b' (so the string is ... at index3: 'I', index4: 'b').

                   But then operation3: (2,0,a) -> set all to lowercase: so the 'I' becomes 'i', and the 'b' was lowercase, so remains 'b'? Actually, no: the operation3 sets all to lowercase: so 'I' becomes 'i', and the 'b' is already lowercase so remains. Then operation4: (1,4,Y) -> set index3 to 'Y' (uppercase). 

                   So, the final character at index3 is 'Y'. 

                   Now, in our method:

                      For index3: last_time[3] = 4 (because op4 set it). Then, we look at last_global[5] (because t=4, then t+1=5). last_global[5]=0. So we leave the character as 'Y'. Correct.

                   For index4: set at op2 (operation index2). Then, we look at last_global[3] (t=2, so t+1=3). last_global[3]=2 (from our array: at index3 we set to 2). So we convert the character set at op2: 'b' to lowercase -> 'b'. Correct.

                   For other characters: they were never updated. So, last_time[i] = -1. Then, we use the initial character and apply last_global[0]? 

                   In the example, the initial string was "AtCoder". 

                   After all operations, the final string is "atcYber". 

                   The other characters: 
                      index0: 'A' initially. Then, we apply last_global[0]? We computed last_global[0]=3? But that would convert to uppercase? But the final string has 'a' at index0.

                   Why? 

                   Actually, the last global op that occurs from the beginning (operation0) onward: 
                      Operation0: type1 (not global) -> skip
                      Operation1: type3 -> set to uppercase -> then the initial 'A' remains 'A'? 
                      Then, operation3: type2 -> set to lowercase -> so after op3, the entire string becomes lowercase. Then op4 sets index3 to 'Y'. 

                   So, for the characters that were never updated (like index0), the last global op that occurred from the start (operation0) to the end is the last one: which is op3 (type2). 

                   How do we capture that? 

                   We built last_global[0] = 3? But that's from our backward pass: 

                      last_global[5]=0
                      op4: type1 -> last_global[4]=last_global[5]=0
                      op3: type2 -> last_global[3]=2
                      op2: type1 -> last_global[2]=last_global[3]=2
                      op1: type3 -> last_global[1]=3
                      op0: type1 -> last_global[0]=last_global[1]=3

                   So last_global[0] = 3. But that is incorrect for the initial characters? 

                   Actually, we want for the initial characters (last_time=-1) to be affected by the global op that occurs from the very first operation onward. So, we use last_global[0]? 

                   But in the example, the initial characters are affected by all global operations. The last one was type2 at op3. So we should get last_global[0] as the last global op from op0 onward? But our backward array for index0 is 3 (because the first global op from op0 onward is op1 (type3) and then op3 (type2) so the last one is op3 (type2). But our backward pass sets last_global[0]=3 because it picks the first global op encountered when going backwards? But we want the last global op in the entire sequence from op0 to the end.

                   How did we build last_global? 

                      We did:

                         last_global[5]=0
                         for i=4 down to 0:
                            if op[i] is global: last_global[i] = op_type
                            else: last_global[i] = last_global[i+1]

                   This sets last_global[i] to the last global op that occurs from i to the end? Actually, no: it sets it to the first global op encountered when scanning from i to the end? 

                   For i=0: 
                      op0: not global -> so last_global[0]=last_global[1] -> 3
                   But the last global op from op0 onward is actually op3 (type2), not op1 (type3). 

                   So, our method is incorrect. We are storing the nearest global op at or after i, but we need the very last one (the most recent one in the sequence). 

                   How to get the last one? 

                   We can modify the backward pass: instead of setting last_global[i] to the next one if not global, we can set it to the next one always? But then if we have multiple global ops, we only get the first one we encounter backwards, which is the last one in the sequence? 

                   Actually, when we traverse backwards, we are going from the last operation to the first. The first global op we meet going backwards is the last global op in the sequence. So, that is the most recent one. Then, for a starting index i, we want the most recent global op from i to the end. 

                   But then, if we have:

                      i=0: operations [0: type1, 1: type3, 2: type1, 3: type2, 4: type1]

                   Then, when we traverse backwards:

                      i=4: not global -> last_global[4]=last_global[5]=0
                      i=3: global (type2) -> last_global[3]=2
                      i=2: not global -> last_global[2]=last_global[3]=2
                      i=1: global (type3) -> last_global[1]=3
                      i=0: not global -> last_global[0]=last_global[1]=3

                   But the last global op from i=0 to the end is type2 (at op3), not type3. 

                   So, we must store the last global op that occurs at or after i, meaning the very last one in the entire operation list. 

                   How can we do that? 

                   We can do:

                      last_global[Q] = 0   # beyond the last op
                      for i from Q-1 down to 0:
                         last_global[i] = last_global[i+1]   # initially set to the next
                         if operation i is type2 or 3:
                             last_global[i] = operation_type   # but this overwrites the value from i+1? 

                   Then, for the example:

                      i=4: op4 is type1 -> so last_global[4]=last_global[5]=0
                      i=3: op3 is type2 -> so last_global[3]=2 (overwriting the previous last_global[4] which was 0)
                      i=2: op2 is type1 -> last_global[2]=last_global[3]=2
                      i=1: op1 is type3 -> last_global[1]=3
                      i=0: op0 is type1 -> last_global[0]=last_global[1]=3

                   This gives last_global[0]=3, but we want it to be 2 for the initial characters? 

                   Actually, no: for the initial characters, we are using last_global[0]. But the last global op from op0 to the end is the last one that occurs, which is type2 at op3. But our array stores for i=0: 3, which is the first global op encountered when going forward? 

                   We need the last global op in the entire sequence from i to the end. 

                   To get that, we can do:

                      Let last_global[i] = the last global op in the entire operation sequence from i to the end. 

                   How to compute? 

                      We traverse backwards and remember the last global op we encountered? But then, if we set:

                         last_global[i] = (if we encounter a global op, then that becomes the value until we meet an earlier one? but that's the opposite of what we want).

                   Alternatively, we can do a forward pass to record the last global op? But that would require storing for each operation index the last global op after it? 

                   Another idea: we can record the last global op that occurred at any time. Then, we can create an array `global_occurrence` that records the type of the last global op that occurred at or after a given time. 

                   Actually, we can do:

                      Let `global_after` = [0] * (Q+1)
                      Let last_global = 0
                      Then, for i from Q-1 down to 0:
                         if operation i is type2 or 3:
                             last_global = operation_type   # because when going backwards, we set the last_global to the most recent (in the forward sense) global op we've seen.
                         global_after[i] = last_global

                   Then, for the example:

                      Start: last_global=0
                      i=4: op4 is type1 -> so we set global_after[4]=0 (and last_global remains 0? but we didn't reset it). Actually, we do:

                         for i in range(Q-1, -1, -1):
                             if op[i] is type2 or 3:
                                 last_global = op_type
                                 global_after[i] = last_global
                             else:
                                 global_after[i] = last_global   # because last_global holds the last global op we encountered going backwards (which is the most recent in the forward sequence)

                   Then:

                         i=4: op4 is type1 -> global_after[4]=last_global -> which is currently 0? (but we haven't seen any global op yet). 

                   We start with last_global=0, then:

                         i=4: not global -> global_after[4]=0
                         i=3: type2 -> last_global=2, global_after[3]=2
                         i=2: not global -> global_after[2]=2
                         i=1: type3 -> last_global=3, global_after[1]=3
                         i=0: not global -> global_after[0]=3

                   So, global_after[0]=3, which is not the last global op (which is type2 at op3) but the last but one? 

                   Actually, we want for a starting index i, the last global op that occurs from i to the end. That is the last one in the sequence. 

                   How about:

                      global_after[0] should be the last global op in the entire sequence? 
                      In the example, the last global op is op3 (type2). 

                   But our method sets global_after[0]=3? 

                   Actually, we did:

                      We traverse backwards: 
                         i=4: set to 0
                         i=3: set to 2 and set last_global=2
                         i=2: set to 2
                         i=1: set to 3 and set last_global=3
                         i=0: set to 3

                   But the last global op in the entire sequence is type2 (at op3), not type3 (at op1). 

                   So, we must not overwrite last_global with an earlier global op? 

                   We want the last global op in the entire sequence from i to the end, so we should remember the last one (in the forward order) we encounter when going backwards. 

                   Actually, when going backwards, the first global op we meet is the last one in the forward order. Then, we want that to be the value for all i from that point back? 

                   We can do:

                      last_global = 0
                      for i from Q-1 down to 0:
                         if operation i is type2 or 3:
                             last_global = operation_type   # because we are seeing a global op that occurs later in the sequence (but in the backward scan, we see it first)
                         global_after[i] = last_global

                   Then, in the example:

                      i=4: not global -> global_after[4]=0 (last_global is 0)
                      i=3: type2 -> set last_global=2, then global_after[3]=2
                      i=2: not global -> global_after[2]=2
                      i=1: type3 -> set last_global=3, then global_after[1]=3
                      i=0: not global -> global_after[0]=3

                   But this gives global_after[0]=3, but the last global op in the entire sequence is type2 (at index3). 

                   Why is that? 

                   The issue is that when we set last_global=3 at i=1, we are forgetting the global op at i=3, which is after i=1 in the forward order? 

                   Actually, no: in the forward order, the operations are:

                     0, 1, 2, 3, 4

                   When we go backward: 4,3,2,1,0.

                   At i=3 (backward), we set last_global=2. Then at i=1 (which is before i=3 in the forward order), we set last_global=3. But the global op at i=1 is before i=3. The last global op in the entire sequence is at i=3 (type2). 

                   So, we should not reset last_global when we see an earlier global op? 

                   We want the very last global op that occurs in the sequence from i to the end. So, for any i, it is the same: the last global op in the entire operation list. But that is not true. 

                   For i=0, the last global op from op0 to the end is op3 (type2).
                   For i=4, there is no global op after op4, so 0.

                   Actually, we want for each i, the last global op that occurs in the operations i, i+1, ... Q-1. 

                   We can do a backward pass that propagates the last global op type from the end, but we must only take the most recent one (in the forward order). 

                   How about we do:

                      global_after = [0]*(Q+1)
                      last_type = 0
                      last_index = -1   # not needed
                      Instead, we can do:

                         We want to know the type of the last global op in the suffix [i:]. 

                         We can precompute an array `suffix_last_global_type` and `suffix_last_global_index`? But we only care about the type.

                         Actually, we can do:

                            suffix_last_global_type = [0]*(Q+1)
                            # set the last one (beyond) to 0
                            suffix_last_global_type[Q] = 0
                            for i in range(Q-1, -1, -1):
                                if operations[i][0] in (2,3):
                                    suffix_last_global_type[i] = operations[i][0]
                                else:
                                    suffix_last_global_type[i] = suffix_last_global_type[i+1]

                         But that gives for i=3: type2, for i=2: type2, for i=1: type3, for i=0: type3. But that is the first global op encountered in the suffix, not the last.

                   What we need is not the first global op in the suffix, but the last one (in the entire suffix). 

                   To get the last global op in the suffix [i:], we can do a backward pass that carries forward the last seen global op type, and only updates it when we see a global op? 

                   Actually, we can do:

                      last_global = 0
                      for i in range(Q-1, -1, -1):
                          if operations[i][0] in (2,3):
                              last_global = operations[i][0]   # because when we see a global op, it becomes the last one for the current suffix starting at i
                          global_after[i] = last_global

                   Then, in the example:

                      i=4: not global -> last_global=0 (from initialization? but we haven't seen any? We start last_global=0 for beyond Q, then at i=4, if op4 is type1, we set global_after[4]=last_global (0), and last_global remains 0.
                      i=3: type2 -> last_global=2, global_after[3]=2
                      i=2: type1 -> global_after[2]= last_global (2)
                      i=1: type3 -> last_global=3, global_after[1]=3
                      i=0: type1 -> global_after[0]= last_global (3)

                   This gives for i=0:3, i=1:3, i=2:2, i=3:2, i=4:0.

                   But for a character set at operation0 (i=0), we use global_after[1] (because t=0, then t+1=1) which is 3.
                   For a character set at operation1 (i=1): we use global_after[2]=2.
                   For a character set at operation2 (i=2): we use global_after[3]=2.
                   For a character set at operation3 (i=3): we use global_after[4]=0.
                   For a character set at operation4 (i=4): we use global_after[5] (which we didn't compute) -> we have global_after for indices0 to Q-1. We need up to Q.

                   We can make global_after of length Q+1, with global_after[Q]=0.

                   So for a character set at operation i, we use global_after[i+1].

                   Now, for the initial characters (last_time=-1), we use global_after[0]? which is 3. But in the example, the initial characters become lowercase because of the type2 at op3. 

                   Why? 

                   Because the initial characters are set initially, and then the first operation is op0 (type1) which doesn't change them. Then, the global op at op1 (type3) sets them to uppercase, and then at op3 (type2) sets them to lowercase. So the last global op they are subjected to is type2.

                   But our global_after[0] is 3, which is not correct.

                   How can we capture that? 

                   We want for the initial characters: the last global op in the entire operation sequence. That is the last global op from operation0 to the end, which is type2 (op3). But our global_after[0] is 3, because our backward pass sets it to 3 (from the global op at op1) and then overwrites to 2 at op3? But in our code above, we do:

                      last_global=0
                      i=4: last_global=0, global_after[4]=0
                      i=3: last_global=2, global_after[3]=2
                      i=2: last_global=2, global_after[2]=2
                      i=1: last_global=3, global_after[1]=3
                      i=0: last_global=3, global_after[0]=3

                   So, we are not carrying the last_global from the end correctly. 

                   The error is that at i=3, we set last_global=2, but then at i=1, we see a global op and set last_global=3, overwriting the 2. But the global op at i=1 is before i=3 in the forward order, so it should not override the later global op at i=3.

                   We only want to remember the very last global op in the suffix [i:], so once we set last_global=2 at i=3, then for i=2 and i=1 and i=0, the last global op in the suffix is type2? No, because at i=0, the suffix [0:] includes op0,1,2,3,4. The last global op is at i=3, type2.

                   So, the algorithm for the backward pass should be: 

                      last_global = 0
                      for i in range(Q-1, -1, -1):
                          if operations[i][0] in (2,3):
                              last_global = operations[i][0]   # because this is the last global op seen when scanning backwards, and therefore the last in the forward order in the suffix [i:]
                          global_after[i] = last_global

                   But then, for i=1, we set last_global=3, and for i=0, we then set global_after[0]=3, but the last global op in the entire sequence is at i=3, type2. 

                   This is because the backward pass sees the operations in reverse order. When at i=3, we see a global op and set last_global=2. Then, when we move to i=2, we don't see a global op, so global_after[2]=2. Then at i=1, we see a global op and set last_global=3, so global_after[1]=3. But that is not what we want: for the suffix starting at i=1, the operations are 1,2,3,4. The last global op in that suffix is at i=3, type2. 

                   We should not reset last_global to 3 at i=1, because there is a later global op at i=3. 

                   Therefore, the above method is incorrect. 

                   We must design the backward pass so that it only sets last_global to a global op if there is no global op after it? That doesn't work.

                   Another idea: we can store the very last global op in the entire operation sequence, and then for each i, if there is any global op in [i:], it is that last one. 

                   In other words, the last global op in the entire sequence is the same for all i: it is the last global op in [0:Q-1]. 

                   But that is not true. For example, if we have:

                     0: type2
                     1: type1
                     2: type3
                     3: type1
                     4: type2

                   Then, the last global op overall is type2 at op4.
                   For a character set at op0: it is affected by type2 at op4.
                   For a character set at op3: the last global op after op3 is op4, type2.
                   For a character set at op4: then no global op after, so none.
                   For a character set at op1: then the last global op after op1 is op4, type2.

                   But also, for a character never set, the last global op is op4.

                   However, consider a character set at op0, and then we have op1 (type1 on a different character), then op2 (type3), and then no more global ops. Then the last global op for a character set at op0 is type3 at op2, not the earlier type2 at op0.

                   So, the last global op in the entire operation sequence is not sufficient. We need for each i, the last global op in the suffix [i:], which is the last one in the entire sequence that occurs at or after i.

                   How to compute that in one backward pass? 

                   We can do:

                      last_global = 0
                      for i in range(Q-1, -1, -1):
                          if operations[i][0] in (2,3):
                              last_global = operations[i][0]   # because when we are at i, if there is a global op, it becomes the last one for the suffix starting at i, and we don't care about any after because we are traversing backwards and we will see them first.
                          But wait, no: the last_global should be the last global op in the forward order in the suffix [i:], which is the first global op we meet when scanning from i to the end? 

                   No, it is the last one. 

                   We can do a backward pass that does not update last_global to an earlier global op. Instead, we should. because the last one in the forward order is the first one we meet in the backward pass. 

                   So, in the backward pass, the first global op we meet is the last one in the forward order. Then, for all i from that index to the beginning, the last global op in the suffix is that one. 

                   Then, we can do:

                      last_global = 0
                      for i in range(Q-1, -1, -1):
                          if operations[i][0] in (2,3):
                              last_global = operations[i][0]   // this is the last global op in the suffix [i:]
                          global_after[i] = last_global

                   แต่มันไม่ถูกต้องในตัวอย่างก่อนหน้าเพราะว่าเราเจอ type3 at i=1 before type2 at i=3 in the backward pass, and we overwrite last_global to 3, but the type2 at i=3 is later in the forward order.

                   The mistake is that we should not overwrite last_global when we see a global op at an earlier index in the forward order. We should only set it if we haven't seen a global op that is later in the forward order? 

                   Since we are going backward, the first global op we meet is the last in the forward order. Then, any global op we meet after that (in the backward pass) is earlier in the forward order, so we should not use it. 

                   Therefore, we only want the very last global op in the entire sequence for the suffix [i:], and that is the first one we meet in the backward pass. Then, for all i from 0 to that index, the last global op is that one. 

                   But then, for the example, the last global op in the entire sequence is type2 at i=3. When we meet it in the backward pass at i=3, we set last_global=2, and then for i=2,1,0, we set global_after[i]=2. 

                   Then, for a character set at operation0, we use global_after[0]=2, which is correct.
                   For a character set at operation0, we then apply type2 (lowercase) to the initial character.
                   For a character set at operation1, we use global_after[2] = ? 

                   We have an array global_after of length Q, where we set for i=0,1,2,3,4: 

                      i=4: last_global=0 ( initially, then at i=4, we don't see a global op? so global_after[4]=0 (correct, because for a character set at op4, there's no global op after)
                      i=3: see global op type2, set last_global=2, so global_after[3]=2.
                      i=2: not global, so global_after[2]=2 ( because last_global is 2)
                      i=1: see global op type3, set last_global=3, so global_after[1]=3.
                      i=0: not global, so global_after[0]=3.

                   Then, for a character set at operation1 (i=1), we use global_after[2]=2, which is type2, so convert to lowercase. 
                   For a character set at operation0, we use global_after[1]=3, which is type3, so convert to uppercase. 

                   But in the example, the last global op for the initial characters is type2 (at op3), not type3 ( at op1). 

                   So, for the initial characters, we should have global_after[0]=2, not 3.

                   How can we achieve that? 

                   We must not reset last_global when we see an earlier global op in the forward order. In the backward pass, once we set last_global to a value, we should only reset it if we see a global op that occurs later in the forward order. But in the backward pass, we are seeing from the end backward, so the first global op we see is the last in forward order. Then, any global op we see after that ( in the backward pass) occurs earlier in the forward order, so should not affect the last_global for indices before it.

                   Therefore, we should not do: 

                      if operations[i][0] in (2,3):
                          last_global = operations[i][0]

                   because that will reset last_global to an earlier global op in the forward order.

                   Instead, we should only set last_global to a global op if it is the first one encountered in the backward pass, and then for the rest ( earlier in the backward pass) we leave last_global unchanged. 

                   But that means last_global will only be set once, to the last global op in the entire sequence. Then, for all i, global_after[i] = that value, which is not correct for suffixes that start after that global op.

                   For example, for a character set at operation4 ( after the last global op), we should have no global op, so 0.

                   Therefore, we must have a different last_global for each i. 

                   The correct method is to do a backward pass that for each i, global_after[i] = the last global op in the forward order in the suffix [i:], which can be done by:

                      last_global = 0
                      for i in range(Q-1, -1, -1):
                          # if there is a global op at i, then for this i and for all j<i, this global op is the last? -> no, because there might be a later global op in the forward order between i+1 and end.
                          # but we are traversing from the end, so the first global op we meet is the last in the forward order.
                          # then, for global_after[i], we should set it to that last_global only if we have not seen a global op after i in the forward order? 
                          # actually, last_global should be the last global op in the suffix [i:], and we are to because we are traversing from the end.

                          # But the catch is: if at i we have a global op, then for the suffix [i:], the last global op is either the last_global we have from i+1 or the op at i, whichever is later in the forward order.

                          # but since we are coming from the end, the last_global we have is the last global op in [i+1: end]. So, to get the last in [i: end], we should take max between ( if current i is global, then it is the last in [i:], because i is before any in [i+1:] in the forward order, so the last in [i+1:] is later than i? -> no, in the forward order, i is now, then i+1 is next.

                   I am considering: the last global op in the suffix [i:] is: 
                      if there is any global op in [i:], it is the one with the highest index in the forward order. 

                   Since we are at i in the backward pass, we have already computed global_after[i+1] = the last global op in [i+1:].

                   Then, for [i:], the last global op is:
                      if the operation at i is global, then it is this op, because i is before i+1 in the forward order, but the last global op is the one with the largest index, so if there is a global op at i, and one at j>i, then the one at j is later. 
                      wait, no: in the forward order, the operation at i happens before i+1. So the last (most recent) global op is the one with the largest index (i as in later in the sequence). 

                   Therefore, the last global op in [i:] is the last global op in [i+1:], unless there is none, in which case if i is global, then it is the op at i.

                   But if there is a global op in [i+1:], then it has a larger index than i, so it is later. Therefore, the last global op in [i:] is the same as in [i+1:], regardless of what is at i.

                   then, we can do:

                      global_after[i] = global_after[i+1]

                   and that's it? 

                   then, for the example, we would have:

                      global_after[4] = 0
                      global_after[3] = global_after[4]=0
                      global_after[2] =0
                      global_after[1]=0
                      global_after[0]=0

                   which is not correct.

                   unless we do:

                      if the operation at i is global, then the last global op in [i:] is the operation at i, because it is the last one ( since it is at i, and then the only one or later ones are after but we haven't seen them? 

                   Let me think: in the forward order, the operations are: i, i+1, ... Q-1.
                   The last global op in [i:] is the highest-indexed global op in [i, i+1, ..., Q-1].

                   to get that, we can do a backward pass that for each i, if there is a global op at i, then we, but we also need to compare with the future.

                   actually, the highest-indexed global op in [i:] is max of { i if it is global, otherwise 0, and the last_global for [i+1:] }

                   but we want the type of the last global op ( the one with the highest index), so if there is a global op in [i+1:], it has index > i, so it is later, and we use its type. Only if there is none, and if i is global, then we use i's type.

                   Therefore, the correct is:

                      global_after[i] = global_after[i+1]   # which is the last global op in [i+1:], the one with the highest index in [i+1:]

                   and that is also the last global op in [i:], because any global op at i is before the ones in [i+1:], so the last is the one in [i+1:] if it exists.

                   unless there is no global op in [i+1:], then we use the op at i if it is global.

                   So:

                      if global_after[i+1] != 0:
                         global_after[i] = global_after[i+1]
                      else:
                         if operation[i] is global:
                            global_after[i] = operation[i][0]
                         else:
                            global_after[i] = 0

                   But then, in the example:

                      i=4: global_after[5]=0 ( assume)
                         then, because op4 is not global, global_after[4]=0.
                      i=3: global_after[4]=0, and op3 is global (type2), so global_after[3]=2.
                      i=2: global_after[3]=2 (!=0), so global_after[2]=2.
                      i=1: global_after[2]=2 (!=0), so global_after[1]=2.
                      i=0: global_after[1]=2, so global_after[0]=2.

                   Then, for initial characters ( last_time=-1) -> use global_after[0]=2, which is correct.
                   For a character set at operation0: last_time[ pos ]=0, then use global_after[1]=2.
                   For a character set at operation1: last_time[pos]=1, use global_after[2]=2.
                   for a character set at operation2: last_time[pos]=2, use global_after[3]=2.
                   for a character set at operation3: last_time[pos]=3, use global_after[4]=0.
                   for a character set at operation4: last_time[pos]=4, use global_after[5]=0.

                   This matches the example.

                   Let me test with the provided example 1:

                      S = "AtCoder"
                      Q=5

                      operations = [
                         [1,4,'i'],   # index0
                         [3,0,'a'],   # index1
                         [1,5,'b'],   # index2
                         [2,0,'a'],   # index3
                         [1,4,'Y']    # index4
                      ]

                   Build global_after ( size 6: indices 0 to 5, with global_after[5]=0)

                      i=4: ( operation4 is type1, so not global -> global_after[4] = global_after[5]=0.
                      i=3: type2 -> since global_after[4]=0, and current is global, so global_after[3]=2.
                      i=2: type1 -> global_after[2] = global_after[3]=2.
                      i=1: type3 -> since global_after[2]=2 (!=0), so global_after[1]=2.
                      i=0: type1 -> global_after[0]=global_after[1]=2.

                   For the initial characters ( never type1) -> use global_after[0]=2, so convert to lowercase.
                   For the character at index3 (set at op0 and op4) -> last_time[3]=4, so use global_after[5]=0 -> no conversion, so 'Y' remains.
                   For the character at index4 ( set at op2) -> last_time[4]=2, use global_after[3]=2 -> convert to lowercase: 'b' -> 'b' ( remains).
                   For other characters: 
                         index0: 'A' in initial -> convert to 'a'
                         index1: 't' -> 't' ( already lowercase) -> 't' ( after lowercase) -> 't'
                         index2: 'C' -> 'c'
                         index5: 'r' -> 'r'
                         index6: not in? the string length is 7, so index0 to 6.

                   So the final string is: a,t,c,Y,b,e,r -> "atcYber", which matches.

                   Therefore, the plan is:

                      Step 1: Precompute global_after[0..Q] ( length Q+1) with:
                         global_after[Q] = 0
                         for i from Q-1 down to 0:
                             if global_after[i+1] != 0:
                                 global_after[i] = global_after[i+1]
                             else:
                                 if operations[i][0] in (2,3):
                                     global_after[i] = operations[i][0]
                                 else:
                                     global_after[i] = 0

                      Step 2: Initialize last_time = [-1] * N, and last_char = list(S)

                      Step 3: For each operation index i in 0 to Q-1:
                                 if it's type1:
                                     pos = x_i - 1
                                     last_char[pos] = c_i
                                     last_time[pos] = i

                      Step 4: For each position j in 0 to N-1:
                                 if last_time[j] == -1:
                                     # use initial character, and apply global_after[0]
                                     flag = global_after[0]
                                 else:
                                     flag = global_after[ last_time[j] + 1 ]   # because the type1 op happened at operation last_time[j], and then we look at the suffix after it: from last_time[j]+1 to end.

                                 then, 
                                    if flag == 0: 
                                        output = last_char[j]
                                    elif flag == 2: 
                                        output = last_char[j].lower()
                                    elif flag == 3: 
                                        output = last_char[j].upper()

                      Step 5: Combine the output list to string and return.

                   Time complexity: O(N + Q), which is acceptable.

                   Let me test with Example 2:

                      Input: 
                         35
                         TheQuickBrownFoxJumpsOverTheLazyDog
                         10
                         2 0 a
                         1 19 G
                         1 13 m
                         1 2 E
                         1 21 F
                         2 0 a
                         1 27 b
                         3 0 a
                         3 0 a
                         1 15 i

                      We are not going to do the entire string, but let's do the global_after array.

                      operations = [
                         [2,0,'a'],   #0
                         [1,19,'G'],   #1 -> sets index18 to 'G'
                         [1,13,'m'],   #2 -> sets index12 to 'm'
                         [1,2,'E'],    #3 -> sets index1 to 'E'
                         [1,21,'F'],   #4 -> sets index20 to 'F'
                         [2,0,'a'],    #5
                         [1,27,'b'],   #6 -> sets index26 to 'b'
                         [3,0,'a'],    #7
                         [3,0,'a'],    #8
                         [1,15,'i']    #9 -> sets index14 to 'i'
                      ]

                      global_after size = 11, global_after[10]=0.
                      i=9: type1 -> global_after[9] = global_after[10]=0 ( because global_after[10]!=0? no, it is 0, and op9 is not global, so 0.
                      i=8: type3 -> global_after[8] = global_after[9]=0, then because it's global, set to 3.
                      i=7: type3 -> global_after[7] = global_after[8]=3 (!=0), so set to 3.
                      i=6: type1 -> global_after[6]=global_after[7]=3.
                      i=5: type2 -> global_after[5]=global_after[6]=3 (!=0), so set to 3.
                      i=4: type1 -> global_after[4]=3.
                      i=3: type1 -> global_after[3]=3.
                      i=2: type1 -> global_after[2]=3.
                      i=1: type1 -> global_after[1]=3.
                      i=0: type2 -> global_after[0]=global_after[1]=3.

                      For each character:
                         For the ones set by type1, we use global_after[ last_time[j]+1 ]:

                         last_time for the set characters:
                            index18 (op1): last_time=1, then flag = global_after[2]=3 -> convert 'G' to 'G' ( uppercase) -> 'G' -> 'G' is already uppercase, so 'G'
                            index12 (op2): last_time=2, flag=global_after[3]=3 -> 'm' -> 'M'
                            index1 (op3): last_time=3, flag=global_after[4]=3 -> 'E' -> 'E'
                            index20 (op4): last_time=4, flag=global_after[5]=3 -> 'F' -> 'F'
                            index26 (op6): last_time=6, flag=global_after[7]=3 -> 'b' -> 'B'
                            index14 (op9): last_time=9, flag=global_after[10]=0 -> 'i' remains 'i'? 
                                 but the expected output is: 
                                    "TEEQUICKBROWMFiXJUGPFOVERTBELAZYDOG"
                                 so at index14, it is 'i' (lowercase) -> then in the final string, it is 'i' -> becomes 'i' because no conversion? 
                                 but after op9, there is no global op, so remains 'i'. 
                                 In the expected string, the 'i' is at position 15 (0-indexed 14) is 'i' in the output? -> the output has 'Fi' in the string: "BROWMFiX", so at index14 is 'i', which is lowercase. matches.

                         For the initial characters, we use global_after[0]=3 ( convert to uppercase).

                         The initial string is "TheQuickBrownFoxJumpsOverTheLazyDog"

                         Convert to uppercase: 
                            becomes "THEQUICKBROWNFOXJUMPSOVERTHELAZYDOG"

                         Then, we overwrite some positions:

                            index1: 'E' ( replaces 'H') -> so becomes "T E E ..."
                            index12: 'M' ( replaces 'N') -> "BROWM"
                            index18: 'G' ( replaces ' jumpsO: index18 is which character? 

                         Let me count: 
                            "TheQuickBrownFoxJumpsOverTheLazyDog" -> 35 characters.
                            T (0), h(1), e(2), Q(3), u(4), i(5), c(6), k(7), B(8), r(9), o(10), w(11), n(12) -> at index12, 'n' becomes 'M' ( uppercase) -> 'M'
                            then: F(13), o(14), x(15) -> but we set index14 to 'i' ( at operation9) -> 'i' ( not converted because no global after) and index15 is 'x' -> becomes 'X' uppercase? 
                                   -> in the output: "BROWMFiX", so 
                                      index8: 'B', 9:'R',10:'O',11:'W',12:'M',13:'F',14:'i',15:'X'

                         Also, index20: 'F' -> the initial at index20 is 'u' in "Jumps", -> then becomes 'U' by global, then set to 'F' ( at op4) -> and then global_after[5]=3 ( convert to uppercase) -> 'F' is uppercase, remains.

                         index26: initially 'a' in "Lazy", then set to 'b' at op6, then convert to uppercase by global_after[7]=3 -> 'B'. 
                         in the expected string: "LAZYDOG" becomes "LAZYDOG", but at index26 is the space? Let me count carefully.

                         The string: "TheQuickBrownFoxJumpsOverTheLazyDog"
                         Let's index: 
                            T(0), h(1), e(2), 
                            Q(3), u(4), i(5), c(6), k(7), 
                            B(8), r(9), o(10), w(11), n(12), 
                            F(13), o(14), x(15), 
                            J(16), u(17), m(18), p(19), s(20), 
                            O(21), v(22), e(23), r(24), 
                            T(25), h(26), e(27), 
                            L(28), a(29), z(30), y(31), 
                            D(32), o(33), g(34)

                         So, 
                            op1: 19th character -> index18: which is 'm' in "Jumps" -> becomes 'G'
                            op2: 13th -> index12: 'n' in "Brown" -> 'm' ( then will be uppercase to 'M')
                            op3: 2nd -> index1: 'h' ->

## Final Solution

