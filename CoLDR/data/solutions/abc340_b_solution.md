# Solution for abc340_b.md

Generated at: 2025-07-22 06:41:42
Model: deepseek-reasoner

## Reasoning Process

Alright, I'm trying to solve this problem. Let me first make sure I understand it correctly. 

So, we start with an empty sequence A. Then, we have Q queries. Each query can be one of two types:
1. `1 x`: This means we append the integer x to the end of A.
2. `2 k`: This means we need to find the k-th value from the end of A. And it's guaranteed that when this query is given, the sequence has at least k elements.

The output should be the answers for each type 2 query in order, each on a new line.

Constraints: Q is at most 100, so it's a small input size. x is between 1 and 10^9, and k is a positive integer not greater than the current length of A.

Looking at the example:
Input:
5
1 20
1 30
2 1
1 40
2 3

After the first two queries, A is [20, 30]. Then, the third query is `2 1`, which should return the 1st from the end, which is 30. Then append 40, so A becomes [20,30,40]. The next query is `2 3`, which is the 3rd from the end, meaning the first element, 20. So output is 30 and 20.

Okay, so the problem is straightforward: we need to process these queries and output the answers for the type 2 queries.

Now, let me think about different approaches.

**Approach 1: <PERSON>rute Force**
The most intuitive way is to maintain a list (array) and for each query:
- For type 1, append x to the list.
- For type 2, we need to get the k-th from the end. That would be the element at position `len(A) - k`. Because the last element is at index -1, which is `len(A)-1`. So the first from the end is at `len(A)-1`, the second from the end at `len(A)-2`, and so on. Therefore, the k-th from the end is at `len(A) - k`.

Since Q is only up to 100, and each operation (appending or accessing) is O(1), this should be efficient enough.

But just to be thorough, let me consider other approaches. Is there a more optimized solution? Well, given that Q is small, any approach would work. But let's think about space. We are storing all elements, which is necessary. So space is O(Q), which is acceptable.

Alternatively, we could avoid storing the entire list? But no, because we need to answer queries about k-th from the end, which could be any element. So we need to store all appended elements.

Another idea: if we were concerned about time for very large Q, but Q is only 100, so it's negligible.

So the brute force approach is actually optimal for this constraint.

But let me think about how to implement it.

Steps:
1. Initialize an empty list A.
2. Also, initialize an empty list for the answers.
3. Iterate over each query in the list of queries.
   - If the query is type 1: append x to A.
   - If the query is type 2: calculate the index as len(A) - k, then the element at that index is A[len(A)-k]. Append this to the answers list.
4. After processing all queries, return the answers list.

Wait, but the problem says "the k-th value from the end". So if the list is [20,30,40], then:
- 1st from end: 40? But in the example, after appending 40, the list is [20,30,40]. Then query `2 3` should return 20, which is the 3rd from the end.

But according to my index calculation: index = len(A) - k = 3 - 3 = 0, which is 20. That's correct.

But in the example, the first type 2 query was after two elements: `2 1` -> index=2-1=1? Wait, no. The list at that point is [20,30]. The last element is at index 1 (0-indexed). So the 1st from the end is at index 1? Then A[1] is 30. Correct.

So the index calculation is: we want the k-th from the end. The last element is the 1st from the end. The one before last is 2nd, etc.

So the index is `len(A) - k`. For example, if k=1, index = len(A)-1. But wait, if the list has n elements, the last element is at index n-1, which is the 1st from the end. The first element is at index 0, which is the n-th from the end.

So for a query `2 k`, we return the element at index `n - k`.

Yes, that matches.

But in code, if we have a list `A`, then we can do `A[-k]`? Because in Python, negative indices count from the end. So `A[-1]` is the last element, `A[-2]` is the second last, and `A[-k]` is the k-th from the end. But wait, the problem says "k-th from the end", meaning the first from the end is the last element, which is `A[-1]`. So for k=1, we want `A[-1]`, for k=2, `A[-2]`, and for k=3, `A[-3]`. So yes, we can directly use negative indexing.

But we have to be cautious: if k is 1, then A[-1] is the last. But if k is the length of the list, then A[-k] is the first element. That matches the requirement.

So in code, for a type 2 query with k, we can simply do `A[-k]`.

But we have to note: if we are using an array and we know the size, we can also do `A[len(A)-k]`. Both are O(1) operations in Python.

So implementation-wise, both are acceptable.

But let me test with the example:

After first two queries: A = [20,30]
Query `2 1`: A[-1] is 30 -> correct.

Then after appending 40: A = [20,30,40]
Query `2 3`: A[-3] is 20 -> correct.

Perfect.

Now, is there any other approach? For example, storing the list in reverse? Then appending would be at the beginning? But that would make appending O(n) because we have to shift all elements. Not efficient. So that's worse.

Alternatively, we could maintain a separate array for the answers? But no, we need to answer the queries in order.

Another idea: we could precompute the answers? But the queries are sequential and depend on the previous appends, so we have to process in order.

So the straightforward approach is best.

What about space? We are storing the entire list. Since Q is 100, the maximum size of A is 100, which is very small.

So no issues.

Now, edge cases: 
- When the list is empty? But the problem says for type 2 queries, it's guaranteed that the length is at least k. So we don't have to handle when the list is too short.
- k is positive and at least 1, and not more than the current length. So safe.

Also, x is positive, so no negative numbers to worry about.

So the code should be simple.

Let me write the steps in code:

Initialize:
  A = []
  results = []

For each query in queries:
  if query[0] == 1:
      A.append(query[1])
  else: # it's type 2
      k = query[1]
      # we need the k-th from the end: which is A[-k]
      results.append(A[-k])

Then return results.

But wait, in the example, the output is two lines: 30 and 20. So the results list should be [30,20].

But in the code, when we get the first type 2, A is [20,30], so A[-1] is 30. Then after appending 40, A is [20,30,40], so A[-3] is 20. So results becomes [30,20]. Correct.

What about if k=2 in a list of 3? Then A[-2] is 30. For example, if after the last query, we had another query `2 2`, then we should get 30.

But the example doesn't have that.

But the code would handle it.

Another test: only one element. Query: `1 10` then `2 1` -> returns 10. Correct.

Two elements: `1 10`, `1 20`, then `2 2` -> returns 10. Because A[-2] is 10.

Yes.

So the code should be correct.

Now, is there any other approach that is more efficient? Given the constraints, no. But just for the sake of exploration, what if Q was very large? Then the list approach is still O(1) per append and O(1) per access. So it's optimal.

But if we had to also support removals? Then the problem doesn't have that. So no need.

Therefore, the solution is straightforward.

I think we can implement it as described.

Now, the starter code is in a class method. So we'll write:

class Solution:
    def solve(self, queries: List[List[int]]) -> List[int]:
        A = []
        res = []
        for query in queries:
            if query[0] == 1:
                A.append(query[1])
            else:  # query[0] == 2
                k = query[1]
                res.append(A[-k])
        return res

But let me test with the example:

queries = [[1,20], [1,30], [2,1], [1,40], [2,3]]

Processing:
1. [1,20]: A = [20]
2. [1,30]: A = [20,30]
3. [2,1]: A[-1] = 30 -> res = [30]
4. [1,40]: A = [20,30,40]
5. [2,3]: A[-3] = 20 -> res = [30,20]

Return [30,20] -> matches.

So it's correct.

But what if we use an index: len(A)-k? That would also work. But negative indexing is more direct.

But in some languages, negative indexing might not be available. But since the problem is for Python, we can use it.

Alternatively, we could do:

index = len(A) - k
res.append(A[index])

Same thing.

But negative indexing is concise.

So I'll go with that.

Now, what about performance? Appending to a list in Python is amortized O(1). Accessing by index is O(1). So overall time is O(Q), and space is O(Q) for storing A and the result.

Given Q <= 100, it's efficient.

So I think this is the optimal solution.

But just to consider another approach: we could store the list in reverse. Then appending would be at the end, but then the k-th from the end would be the k-th element from the beginning? But then when we append, we have to insert at the beginning? Because we want the last element to be at the beginning? Then appending would be O(n) because we have to shift. So worse. Not necessary.

Alternatively, we could store the elements as they come, and for query type 2, we traverse from the end. But that would be O(k) per query. In worst case, k could be large, but Q is 100, so worst-case total operations would be about 100*100=10000, which is acceptable. But why do that when we have O(1) access? So the list with direct indexing is better.

So I think the solution is solid.

Now, let me write the code accordingly.

But in the problem, the input is from standard input, but the function is given as a method that takes a list of queries. So we just implement as described.

I think that's it.

## Final Solution

### Approach
The problem involves processing a sequence of queries on an initially empty list. The queries can be of two types:
1. **Append Query (`1 x`)**: Appends the integer `x` to the end of the list.
2. **Retrieve Query (`2 k`)**: Retrieves the `k`-th element from the end of the current list. 

Given the constraints (with `Q` up to 100), the optimal approach is straightforward:
- **Maintain a Dynamic List**: Use a list to dynamically append elements as they come in.
- **Direct Indexing for Retrieval**: For each retrieve query, directly access the `k`-th element from the end using negative indexing (i.e., `list[-k]`), which is an O(1) operation in Python.

### Solution Code
```python
class Solution:
    def solve(self, queries):
        A = []
        results = []
        for query in queries:
            if query[0] == 1:
                A.append(query[1])
            elif query[0] == 2:
                k = query[1]
                results.append(A[-k])
        return results
```

### Explanation
1. **Initialization**: Start with an empty list `A` to store the sequence and an empty list `results` to store answers for retrieve queries.
2. **Processing Queries**:
   - **Append Queries (`1 x`)**: Directly append `x` to `A`.
   - **Retrieve Queries (`2 k`)**: Use negative indexing (`A[-k]`) to access the `k`-th element from the end of `A` and append it to `results`.
3. **Output**: After processing all queries, return `results`, which contains answers for each retrieve query in the order they were processed.

This approach efficiently handles both types of queries in O(1) time per operation (amortized for appends) and O(Q) space, making it optimal for the given constraints. The solution leverages Python's list operations for simplicity and clarity.