# Solution for abc318_a.md

Generated at: 2025-07-22 06:23:36
Model: deepseek-reasoner

## Reasoning Process

Alright, so I'm trying to solve this problem about <PERSON><PERSON><PERSON> and full moons. Let me make sure I understand what's being asked.

The problem says that today is day 1. The first full moon he can see is on day M (which is on or after day 1). After that, every P days he sees another full moon. So the full moons occur on days: M, M+P, M+2P, M+3P, and so on. We're given an integer N, and we need to count how many full moons occur between day 1 and day N (inclusive).

The input is three integers: N, M, P. Constraints are: 1 ≤ N ≤ 200,000, and 1 ≤ M ≤ P ≤ 200,000. All integers.

For example, if N=13, M=3, P=5, then the full moons are on days 3, 8, 13. So the answer is 3. Another example: N=5, M=6, P=6. Since M is 6, which is beyond 5, there are no full moons in the range, so output 0. A third example: N=200000, M=314, P=318, output 628.

I need to count the number of terms in the arithmetic sequence: M, M+P, M+2P, ..., up to the largest term ≤ N.

So, the k-th full moon day is: day_k = M + (k-1)*P, and we need day_k ≤ N.

So, mathematically, we're solving for k: M + (k-1)*P ≤ N.

We can rearrange this inequality:

(k-1)*P ≤ N - M

k-1 ≤ (N - M) / P

k ≤ (N - M) / P + 1

Since k must be an integer (number of days), we can express k as the largest integer such that M + (k-1)*P ≤ N.

Alternatively, k = floor((N - M) / P) + 1, but only if N >= M. If N < M, then there are 0 full moons.

Wait, let me test with the examples.

Example 1: N=13, M=3, P=5.

k = (13-3)/5 + 1 = 10/5 + 1 = 2 + 1 = 3. Correct.

Example 2: N=5, M=6, P=6. Since 5 < 6, k = 0. So we don't calculate; just return 0.

Example 3: N=200000, M=314, P=318.

k = (200000 - 314) / 318 + 1 = 199686 / 318 + 1.

199686 ÷ 318. Let me calculate: 318 * 628 = ? 

Actually, 318 * 600 = 190,800; 318 * 28 = 318*20 + 318*8 = 6360 + 2544 = 8904. Total 190800+8904=199,704. But 199,704 is more than 199,686? Wait, that can't be because 199,686 < 199,704. So maybe it's 627?

Alternatively, 199686 // 318. In integer division: 199686 ÷ 318.

Let me compute: 318 * 627 = 318 * 600 = 190,800; 318 * 27 = 318*20=6360, 318*7=2226, so 6360+2226=8586. Total 190800+8586=199,386. Then 199,686 - 199,386 = 300. 300 < 318, so quotient is 627. Then k = 627 + 1 = 628. Correct.

So the formula seems to be:

if N < M:
    count = 0
else:
    count = ((N - M) // P) + 1

But wait, (N - M) // P might be integer division, which truncates towards zero? In Python, for non-negative numbers, it's floor division. Since N >= M in this case (because if N < M, we skip), so (N-M) is non-negative. So (N-M)//P is the floor of (N-M)/P. Then adding 1 gives the correct k.

But is that always correct? Consider when (N-M) is exactly divisible by P. For example, N=8, M=3, P=5. Then full moons: 3, 8. So k=2.

Calculation: (8-3)=5, 5//5=1, then 1+1=2. Correct.

Another case: N=7, M=3, P=5. Then full moons: 3 and 8 (but 8>7, so only day3). So k=1.

Calculation: (7-3)=4, 4//5=0, then 0+1=1. Correct.

But what if M is 0? The constraints say M>=1, so we don't have to worry.

So the algorithm is straightforward:

if N < M:
    return 0
else:
    return ((N - M) // P) + 1

But wait, let me test with the example 2: N=5, M=6, P=6. Since 5<6, returns 0. Correct.

But what if M is greater than N? Then we return 0. That's handled.

But what if M is equal to N? Then we have one day. Example: N=3, M=3, P=5. Then (3-3)=0, 0//5=0, 0+1=1. Correct.

What if M is greater than N? Then we return 0. Correct.

But what about the case when P is 0? The constraints say P>=M and M>=1, and P>=M, so P>=1. So no division by zero.

But the constraints say M≤P, so if M is 1 and P is 1, then every day from day1 to dayN? 

Example: N=5, M=1, P=1. Then full moons on days 1,2,3,4,5. So 5 moons.

Using the formula: (5-1)//1 +1 = 4//1 +1 = 4+1=5. Correct.

Another case: N=10, M=5, P=5. Then days:5,10. So 2 moons. Formula: (10-5)//5 +1 = 5//5+1=1+1=2. Correct.

So the formula seems solid.

But wait, what if M is 10, P=5, and N=20? Then days:10,15,20. So 3. Formula: (20-10)//5 +1 = 10//5+1=2+1=3. Correct.

But what about when (N-M) is negative? We already check N<M so that returns 0, so we don't do the division.

So the code can be:

def solve(self, N: int, M: int, P: int) -> int:
    if N < M:
        return 0
    else:
        return (N - M) // P + 1

But wait, in the example: N=13, M=3, P=5: (13-3)=10, 10//5=2, 2+1=3. Correct.

But wait, is there a possibility of off-by-one error? Let me think again.

The sequence: first term a1 = M, common difference P. The n-th term: a_k = M + (k-1)*P.

We need a_k <= N.

So M + (k-1)*P <= N

=> (k-1)*P <= N - M

=> k-1 <= (N-M)/P

Since k-1 must be an integer, the maximum k-1 is floor((N-M)/P). Then k = floor((N-M)/P) + 1.

Yes, that's standard for arithmetic sequences.

But note: if (N-M) is negative, we return 0. Otherwise, we compute as above.

But in the formula, if (N-M) is negative, we skip. So the code is:

if N < M:
    return 0
k = (N - M) // P + 1

But wait, what if (N-M) is not divisible by P? For example, N=12, M=3, P=5. Then full moons: 3,8,13. But 13>12, so only two: 3 and 8.

Formula: (12-3)=9, 9//5=1 (since 9 divided by 5 is 1.8, floor is 1), then 1+1=2. Correct.

So the floor division handles it.

Therefore, the solution is straightforward.

But let me consider the constraints: N up to 200,000. The calculation is O(1), so it's efficient.

But what if N is 200,000, M is 1, P is 1? Then (200000-1)//1 +1 = 199999+1=200000. Correct.

What if M is 200,000, P=200,000, N=200,000? Then (200000-200000)=0, 0//200000=0, 0+1=1. Correct.

Another edge: M=200000, P=200000, N=199999? Then N<M, so return 0. Correct.

So I think this handles all cases.

But wait, the problem says: "The first day on or after today on which he can see a full moon is day M". So M must be at least 1, which is given. And then every P days. So the sequence is as we described.

I think the solution is correct.

But let me check the example 3: N=200000, M=314, P=318. Output 628.

We computed: (200000-314)=199686, 199686 // 318.

How to compute 199686 // 318? We can do: 318 * 628 = 199704? Which is more than 199686? Then it should be 627? Then 627+1=628? But 318*627=199386, and 199386+318=199704. So 199686 - 199386 = 300, which is less than 318, so the quotient is 627. Then 627+1=628. So the formula holds.

In Python, integer division for non-negative numbers is floor division, which truncates to the next lower integer. So 199686 // 318 = 627. Then +1 is 628. Correct.

So I can implement it.

But is there any case where this might break? For example, if M is 0? But constraints say M>=1. So we are safe.

Also, if P is 0? Constraints say P>=M>=1, so P>=1. So no.

Therefore, the code is:

class Solution:
    def solve(self, N: int, M: int, P: int) -> int:
        if N < M:
            return 0
        return (N - M) // P + 1

But wait, what if (N-M) is negative? We have the condition at the beginning, so (N-M) is non-negative in the else branch. So it's safe.

But what if M is greater than N? Then we return 0. Correct.

But what if M is less than or equal to N? Then we compute.

I think that's it.

Let me run through the examples in code:

Example1: N=13, M=3, P=5 -> (13-3)=10, 10//5=2, 2+1=3 -> output 3.

Example2: N=5, M=6, P=6 -> 5<6 -> 0.

Example3: N=200000, M=314, P=318 -> (200000-314)=199686, 199686//318=627, 627+1=628.

So the code should be correct.

But why is the example 3 output 628? Because 314, 314+318=632, then 632+318=950, ... until 314 + 627*318 = 314 + 199386 = 199700? Wait, 314 + 627*318.

But 627*318 = 199386, then 199386+314=199700. But 199700 is less than 200000? Then next would be 199700+318=200018, which is over 200000. So the last term is 199700? But wait, the first term is M=314. Then the terms are:

a1 = 314
a2 = 314 + 318 = 632
a3 = 314 + 2*318 = 314+636=950
...
a_k = 314 + (k-1)*318

Set 314 + (k-1)*318 ≤ 200000

=> (k-1)*318 ≤ 200000 - 314 = 199686

=> k-1 ≤ 199686 / 318 ≈ 627.628...

So k-1 = 627, k=628.

But the last term is 314 + 627*318 = 314 + 199386 = 199700. Is 199700 ≤ 200000? Yes. Then the next one would be 199700+318=200018, which is greater than 200000. So there are 628 terms.

Yes, so the formula is correct.

Therefore, the solution is simple.

But just to be thorough, let me consider if there's any other approach.

Alternative approach 1: Brute force.

We could iterate from day M to day N, stepping by P, and count. But if N is 200,000 and M is 1 and P is 1, then we iterate 200,000 times. But the constraints say N up to 200,000, which might be acceptable? But worst-case, if P=1, then we iterate 200,000 times, which in Python might be acceptable for 200,000? But 200,000 is 2e5, which is acceptable in C++/Java, but in Python, it's acceptable too for simple loops. However, the problem says constraints up to 200,000, and the O(1) solution is trivial, so we don't need brute force.

But for completeness, brute force would be:

count = 0
day = M
while day <= N:
    count += 1
    day += P

But worst-case, when M=1 and P=1 and N=200000, it loops 200,000 times. But 200,000 is acceptable in Pyton? Actually, in many online judges, 200,000 iterations in Python might be acceptable because it's about 0.2 seconds. But the problem says constraints up to 200,000, and the arithmetic solution is O(1), so why use O(N) when we can do O(1)? Also, worst-case P=200,000, then the loop runs only once? Because M+ P is M+200000, which is > N (if N is 200000). So the number of iterations is roughly (N-M)//P + 1. But in the worst-case (P=1) it's 200,000, which is acceptable. But the problem constraints are that N, M, P up to 200,000, so the worst-case for the loop is when P=1 and M=1, which is 200,000 iterations, which is acceptable in Python. But the arithmetic solution is better.

But the problem might have multiple test cases? Actually, the input is one line, so one test case. But even so, O(1) is better than O(N). So I think the direct formula is best.

Alternative approach 2: using math.

We can think of it as the number of multiples of P in the range [M, N]. That is, we want to count integers k>=0 such that M + k*P <= N.

Which is the same as k <= (N-M)/P.

So k from 0 to floor((N-M)/P). The count is floor((N-M)/P) + 1? Actually, k=0 gives M, which is the first term. Then k=1 gives M+P, etc. So the number of k is floor((N-M)/P) + 1.

Same as before.

So no difference.

Therefore, the optimal solution is the direct formula.

So I'll implement that.

But one more edge: if M is greater than N, we return 0. Otherwise, we compute.

But what if M is exactly N? Then (N-M)=0, 0//P=0, 0+1=1. Correct.

What if M is 1, P is 1, N is 1? Then (1-1)=0, 0//1=0, 0+1=1. Correct.

What if M is 2, P is 3, N is 3? Then (3-2)=1, 1//3=0, 0+1=1. Correct: only day2.

What if M is 2, P=3, N=4? Then (4-2)=2, 2//3=0, 0+1=1. Correct: only day2.

What if M is 2, P=3, N=5? Then (5-2)=3, 3//3=1, 1+1=2. Days:2,5. Correct.

So all cases covered.

Therefore, the code:

class Solution:
    def solve(self, N: int, M: int, P: int) -> int:
        if N < M:
            return 0
        return (N - M) // P + 1

I think that's it.

## Final Solution

### Solution Approach
The problem involves counting the number of days within a given range [1, N] where Takahashi can see a full moon. The first full moon occurs on day M (where M ≥ 1), and subsequent full moons occur every P days. The solution requires efficiently determining the count of these days without iterating through each day, especially given the constraints where N can be as large as 200,000.

#### Key Insight
The days when full moons occur form an arithmetic sequence starting at M with a common difference P. The sequence is: M, M + P, M + 2P, M + 3P, ... up to the largest term ≤ N. The task reduces to finding the number of terms (k) in this sequence such that:
\[
M + (k-1)P \leq N
\]
Rearranging this inequality gives:
\[
k \leq \frac{N - M}{P} + 1
\]
Since k must be a non-negative integer, the solution involves computing the floor division of (N - M) by P and adding 1. However, if N is less than M, there are no full moons in the range, and the answer is 0.

#### Algorithm Selection
The optimal solution leverages mathematical properties of arithmetic sequences to compute the result in constant time O(1). The steps are:
1. If N < M, return 0 since no full moons occur in the range.
2. Otherwise, compute the result as: \(((N - M) // P) + 1\), where // denotes integer division (floor division for non-negative numbers).

This approach efficiently calculates the count without iteration, making it suitable for large values of N, M, and P (up to 200,000).

#### Complexity Analysis
- **Time Complexity**: O(1) due to the direct computation using arithmetic operations.
- **Space Complexity**: O(1) as no additional space is used beyond the input variables.

### Solution Code
```python
class Solution:
    def solve(self, N: int, M: int, P: int) -> int:
        if N < M:
            return 0
        return (N - M) // P + 1
```

### Explanation
1. **Initial Check (N < M)**: If the upper bound N is less than the first full moon day M, there are no full moons in the range, so return 0.
2. **Arithmetic Sequence Calculation**: For cases where N ≥ M, the number of full moons is derived from the formula \((N - M) // P + 1\). Here:
   - \((N - M)\) gives the span from the first full moon to the upper bound.
   - Floor division by P computes how many intervals fit into this span.
   - Adding 1 accounts for the first full moon day (M).

This approach efficiently counts the days without iterating, leveraging mathematical properties for optimal performance. The solution handles all edge cases, including when no full moons occur (N < M) and when the full moons exactly align with the bounds (e.g., N = M). 

### Verification
- **Example 1**: Input `13 3 5` → \((13 - 3) // 5 + 1 = 10 // 5 + 1 = 3\) → Output `3`.
- **Example 2**: Input `5 6 6` → Since \(5 < 6\), output `0`.
- **Example 3**: Input `200000 314 318` → \((200000 - 314) // 318 + 1 = 199686 // 318 + 1 = 627 + 1 = 628\) → Output `628`.

The solution is verified against the provided examples and handles constraints efficiently.