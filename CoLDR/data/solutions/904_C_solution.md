# Solution for 904_C.md

Generated at: 2025-07-22 06:03:25
Model: deepseek-reasoner

## Reasoning Process

Alright, I'm trying to solve this "Shockers" problem. Let me first understand what exactly is being asked.

So, <PERSON><PERSON> is on a show where there's a secret letter selected that he doesn't know. He performs a series of actions: 
1. Saying a word without getting shocked (so the word doesn't contain the secret letter).
2. Saying a word and getting shocked (so the word does contain the secret letter).
3. Guessing a letter, and if wrong, he gets shocked; if correct, the show ends.

The goal is to find the number of "excessive" electric shocks. These are shocks that occur after the point when the secret letter could have been uniquely determined. 

Important constraints:
- n (number of actions) up to 100,000.
- Total length of all words is up to 100,000.
- The last action is always a correct guess, and it's the first correct one.

Let me think through the examples to model the problem.

Example 1:
Input:
5
! abc   -> shocked, so secret letter is in 'abc' (possible: a,b,c)
. ad    -> not shocked, so secret letter not in 'ad'. Since we already thought a was possible, now a is eliminated? Also, d isn't in the previous set? Actually, the word is 'ad', so the secret letter is not in this word. So we can eliminate any letters in 'ad' from the current possible set. So after this, possible letters: from {a,b,c} minus {a,d} -> {b,c}.
. b     -> not shocked, so secret letter not in 'b'. So we eliminate b. Now possible only {c}.
! cd    -> shocked, so secret letter is in 'cd'. But we already know it's c (from previous elimination). So this shock is excessive because at this point we already knew it was c. Then he guesses ? c, which is correct and ends the show.
Excessive shocks: 1 (the last ! action).

Example 2:
8 actions:
! hello -> shocked, so secret in {'h','e','l','o'}? Actually, the word is "hello", so letters: h,e,l,o. So possible set: {h,e,l,o}.
! codeforces -> shocked, so secret in {'c','o','d','e','f','r','s'}? Now, the secret must be in the intersection of the two sets? Because it must be in both words? Wait, no. Actually, each time he gets shocked, the word contains the secret letter. But the secret letter is fixed. So after the first action, the secret is one of the letters in "hello". After the second action, the secret must be in both "hello" and "codeforces". So the possible set becomes the intersection of the two sets? But wait, no. Actually, the secret letter is in the first word and in the second word. So the set of possible letters is the set of letters that have appeared in all the shocked words and not in any non-shocked words? 

But also, the non-shocked words: if he says a word and doesn't get shocked, then the secret letter is not in that word. So we can remove those letters from the possible set.

So after:
1. ! hello -> possible: {h,e,l,o} (assuming all lowercase, and distinct? The word has two 'l's, but we consider unique letters).
2. ! codeforces -> possible: intersection of {h,e,l,o} and {c,o,d,e,f,r,s} = {e,o}
Then:
3. ? c -> he guesses 'c'. Since the secret is either e or o, this is wrong. So he gets a shock. This is a shock, but is it excessive? Not yet, because we don't know uniquely. At this point, we still have two possibilities. So this shock is necessary? Or is it excessive? Wait, the problem says excessive shocks are those after the moment the letter could have been uniquely determined. So at this point, we still have two possibilities, so it's not unique. So this shock is necessary? Then we eliminate 'c'? Wait, no: the guess is incorrect, so we know that 'c' is not the secret. But since we already knew the secret was in {e,o}, and c isn't in that set, we already knew that? Actually, no: the set of possible letters is only {e,o} at this point, so when he guesses 'c', which is not in the possible set, we already know it's wrong? But the problem says he gets a shock for wrong guess. But in terms of the possible set, we don't change because we already don't have 'c'. 

Wait, actually, the possible set is maintained by the words, not by the guesses? The guesses only give information if they are incorrect? Because if he guesses a letter that's not in the current possible set, we already know it's wrong, but the problem doesn't say that he knows the possible set. He doesn't. He only gets the shocks. So we, as observers, can compute the possible set. But for Valentin, he might not know? But the problem is from our perspective: we have to compute the minimal possible set and then see when it becomes unique.

So after action 2, the possible set is {e,o}. Then he guesses 'c'. Since 'c' is not in the possible set, it's incorrect, and he gets a shock. This shock is necessary because the possible set still has two letters. Then:

4. . o -> he says "o" and doesn't get shocked. But if he says "o" and the secret letter is 'o', he should get shocked. So if he doesn't get shocked, then the secret letter is not in the word. The word is "o", so the secret letter is not 'o'. So we can remove 'o' from the possible set. Now possible set: {e}. So at this point, we know uniquely that the secret is 'e'. 

Then next actions:
5. ? d -> he guesses 'd'. But we know the secret is 'e', so this is wrong. Shock. This shock is excessive because we already know the secret is 'e'. 
6. ? h -> same, wrong guess, shock. Excessive.
7. . l -> not shocked, so secret not in 'l'. But we already know it's 'e', so this doesn't change anything. But the action doesn't cause a shock? So not counted as excessive? The excessive shocks are only the ones that are shocks (either from ! actions or from ? wrong guesses) that happen after the unique determination.
8. ? e -> correct, ends.

So the excessive shocks: actions 5 and 6 (both ? actions) which are two shocks. But the output is 2. However, note that the problem says: "excessive electric shocks are those received after the moment the selected letter could have been uniquely determined". So after action 4, we know the secret is 'e'. Then any shock after that is excessive. So the shocks in actions 5 and 6 are excessive. Also, note that action 7 is a '.' so no shock. So total excessive shocks: 2.

But wait, the example output is 2. So that matches.

Example 3:
Input:
7
! ababahalamaha   -> secret in {'a','b','h','l','m'}? The word: unique letters: a,b,h,l,m.
Then he makes a series of guesses: 
? a -> wrong (and gets shock). But wait, the last action is a correct guess, and it's ? h. So before that, he guessed a, b, a, b, a, and then h. But the problem says: "The selected letter could only be determined when Valentin guessed it". So initially, after the first word, we know the secret is one of the letters in that word. Then when he guesses, if he guesses a letter that is in the set, but not the secret, he gets shocked. But until the last guess, we don't know which one? Actually, no: if we have no other information? The only information is the first action: the secret is in {a,b,h,l,m}. Without any other words, we don't have more information. So the set remains {a,b,h,l,m}. Then when he starts guessing, each wrong guess tells us that that letter is not the secret. So after guessing 'a' (wrong), we remove 'a'. Then guess 'b' (wrong), remove 'b'. Then he guesses 'a' again? But we already removed 'a', so that's redundant. Then 'b' again? Then 'a' again? Then finally 'h' (correct). 

But note: the problem states: "if he didn't get a shock, the word does not contain the selected letter". But in this example, there are no '.' actions, only one '!' and then all '?'. So we don't get any additional information from words. The only information we have is from the first word (secret is in {a,b,h,l,m}) and then each wrong guess removes one letter. So after the first wrong guess (a), set becomes {b,h,l,m}. After second guess (b), set becomes {h,l,m}. Then he guesses a again: but a is not in the set? So that guess is redundant? And similarly for the next two. Then when he guesses h, which is in the set, and it's correct? But the problem says the last action is a correct guess and the first correct one. 

But when does the secret become uniquely determined? Only when the set of possibilities has one letter. Initially, we have 5 letters. After first guess, 4. After second guess, 3. Then he guesses a (which isn't in the set) -> shock, but the set is still 3. Then he guesses b (already removed) -> shock, set still 3. Then a again -> same. Then h: correct. So the set becomes size 1 only when he guesses h? Because after the second guess, we have 3 letters. Then the next three guesses are letters not in the set? Or are they? Actually, after the second guess, the set is {h,l,m}. Then he guesses a: a is not in the set, so the set remains {h,l,m}. Similarly for the next two. Then when he guesses h, which is in the set, and it's correct. So the set becomes uniquely determined only when he makes the correct guess? Because until that moment, we still have three possibilities. So no shock is excessive because the unique determination happens at the last action. So excessive shocks = 0.

But wait, the last action is the correct guess, which is not a shock? Or is it? The problem says: "each incorrect guess also results in an electric shock". So correct guess doesn't shock? Then the shocks occur only on incorrect guesses and on '!' actions. In this example, the shocks occur on the first '!' (which is necessary, because we don't know the secret then), and then on the 5 incorrect guesses. But none of these are after the unique determination because the unique determination happens at the last guess. So no excessive shocks.

So the key is to simulate the set of possible secret letters and track when the set becomes of size 1. Then, any shock (from a '!' action or a '?' action that is wrong) that occurs after that moment is excessive.

But note: the moment the set becomes size 1, we know the letter. So any shock from that point until the last action (which is the correct guess) is excessive.

But what if the set becomes size 1 at some action, but then later we get more information that contradicts? Actually, the problem states that the information is consistent: if he doesn't get shocked, the word doesn't contain the secret, and if he does, it does. And the last guess is correct. So the set should only shrink and eventually become the correct letter.

So the plan:
1. Start with the set of all 26 letters? Or start with the set of letters that have appeared in any shocked word? Actually, we can start with all 26 letters. Because if a letter has never been mentioned, it could still be the secret? But wait: the problem says that if he pronounces a word without shock, the secret is not in that word. But if a letter has never been in any word, then we don't have information about it. However, the secret must be a letter that caused a shock? Actually, no: the secret is fixed, and we get information only from the actions.

But note: the problem states that if he gets shocked, the word contains the secret. If he doesn't, the word does not. So if a letter has never been in any word, then we have no information. However, the problem also says that the last action is a correct guess. So the secret is that letter.

But consider: what if the secret is a letter that never appeared in any word? Then we would never get a shock from a word? But the problem says: every time he pronounces a word containing the selected letter, he gets shocked. So if the secret is 'x', and he never says a word with 'x', then he never gets shocked from a word. Then the only shocks are from incorrect guesses. Then when he guesses 'x', it's correct and the show ends.

But in this case, the set of possible letters: initially all 26. Then, each time he says a word and doesn't get shocked, we remove all the letters in that word. Also, each time he gets shocked from a word, we know the secret is in that word? But actually, we know that the secret is in the word only if he got shocked. So we can also use that information: the secret must be in the union of all words from which he got shocked? Actually, no: the secret must be in every word that caused a shock? Because if a word caused a shock, the secret is in that word. So the secret must be in the intersection of all words that caused a shock. 

But also, from non-shocked words, the secret is not in that word, so we can remove those letters.

So the algorithm:
- Initialize the set of possible letters to be the set of all 26 lowercase letters.
- Alternatively, we can start with the set of all letters that appear in the entire input? But that might be complicated. Starting with 26 is safe.

- Also, we can maintain a set of letters that are definitely not the secret: from non-shocked words, we can remove all the letters in that word from the possible set.

But for shocked words: the secret must be in that word. So we can take the intersection of the current possible set and the set of letters in the word. 

So:
possible_letters = set of 26 letters.

For each action:
  if action is '. w': 
      remove all letters in w from possible_letters? But wait: if the word has a letter that is not in the current set, we don't care. Actually, we can do: possible_letters = possible_letters - set(w)
  if action is '! w':
      possible_letters = possible_letters ∩ set(w)   -> because the secret must be in w.
  if action is '? s': 
      if it's a wrong guess (which we know because the last one is the only correct one), then we remove s from possible_letters? Because the secret is not s. But note: we remove it only if it's wrong. But we know it's wrong because the last one is the correct one. So for all '?' actions except the last one, we remove the letter.

But wait: what if the '?' action is for a letter that has already been removed? Then we don't need to remove again. But it's harmless.

Also, note: after the set becomes size 1, we know the secret. Then any subsequent shock (either from '!' or from '?' that isn't the correct one) is excessive.

But the last action is a correct guess, which doesn't cause a shock? So we don't count that.

So steps:
1. Initialize possible = set of all lowercase letters (26 letters).
2. Initialize a variable: found = False. Or rather, we can track the moment when the set size becomes 1. Let me call that moment T. Then any shock (from '!' or '?' actions) that occurs after T is excessive.
3. But note: the action that causes T might be the one that reduces the set to 1. Then any shock after that (until the last action) is excessive.

But how do we count the shocks? We note:
- For '!' actions: always causes a shock (because he pronounces a word containing the secret? Actually, no: wait, if the word doesn't contain the secret, he doesn't get shocked. But the action '!' means he did get shocked. So the problem says: every time he pronounces a word containing the selected letter, he gets shocked. And the action '!' indicates that he did get shocked. So we know the word contains the secret. Similarly, '.' means he did not get shocked, so the word does not contain the secret.

So in the input, we are told the outcome. So for each action:
  - '! w': shock happens, and we know the secret is in w.
  - '. w': no shock, so we know the secret is not in w.
  - '? s': if s is not the secret (which we know because the last one is the only correct one), then a shock happens.

So to count excessive shocks: we need to know when the set becomes size 1, and then count the number of shocks (from any '!' or '?' actions that are not the last correct guess) that occur after that moment.

But note: the last action is a correct guess and does not cause a shock? Or does it? The problem says: "each incorrect guess also results in an electric shock". So correct guess: no shock. So only incorrect guesses and the '!' actions (which always cause a shock) are shocks.

So algorithm:
possible = set of all 26 letters
excessive = 0
unique_determined = False   # or we can set a flag when |possible| == 1, and then after that, every shock is excessive.

But actually, the set might become 1 at some point, but then we get more information that doesn't change it? So we can set a flag when the set becomes size 1. Then, after that, any shock (from '!' or '?' that is incorrect) is excessive. But note: if we get more actions that remove letters, but the set was already size 1, then we are still in the state of knowing the secret.

But what if the set becomes size 1 at step i. Then at step i+1, we get a '.' action: we remove some letters that are not the secret (so the set remains the same, since the secret is already the only one). Then at step i+2, we get a '!' action: we intersect with a word that must contain the secret. Since the secret is already in the set, the set remains the same. Then a '?' action that is incorrect: we remove a letter that is not the secret, so set remains size 1.

So the set remains size 1 from the moment it first becomes 1 until the end. So we can set a flag at the first moment the set becomes 1, and then count every shock (from '!' and from incorrect '?') that occurs from the next action until the last action (excluding the last action if it's a correct guess).

But note: the last action is a correct guess and is not a shock. So we don't count it.

But what if the set becomes size 1 at the same action that we do a shock? For example, if we have an action that reduces the set to 1 and that action is a shock (either '!' or '?' incorrect), then that shock is not excessive? Because at the moment of the action, we didn't know the secret until after the action. 

The problem says: "excessive electric shocks are those received after the moment the selected letter could have been uniquely determined". So the moment is after processing the action that made the set size 1. So if the action that causes the set to become 1 is a shock, that shock is not excessive. Only the shocks that occur after that action (in subsequent actions) are excessive.

So steps:
1. Initialize possible = set of 26 letters.
2. Initialize a variable: already_determined = False. Or rather, we can note the index when the set becomes 1. Then after that index, any shock is excessive.
3. But we process actions one by one. So we can have a flag that becomes True once the set becomes 1. Then, for all subsequent actions (until the last one) that are shocks, we count.

But note: after the set becomes 1, we still have to update the set? Because we have to process the rest of the actions to know what happens? But actually, the set will remain size 1 because we only remove letters that aren't the secret or intersect with sets that contain the secret. So once the set is {x}, then:
- If we see '. w': remove all letters in w. But x is the secret and is not in w (because he didn't get shocked), so we remove other letters, but x remains? Actually, we remove all letters in w. If x is not in w, then we don't remove x. So the set remains {x}.
- If we see '! w': we do possible = possible ∩ set(w). Since x is in w (because he got shocked), so {x} ∩ set(w) = {x}.
- If we see '? s': if s != x, then we remove s. But if s is not x, and x is the only one, then we remove s and the set remains {x}. If s == x, then it's the last action? Because the last action is the correct one. So we don't remove x in that case? But we don't process the last action as a shock? Because the last action is a correct guess and doesn't cause a shock.

So the set remains {x} from the moment it becomes 1 until the end.

Therefore, we can do:

count_excessive = 0
determined = False   # becomes true when |possible| becomes 1

for i in range(n-1):   # because the last action is the correct guess, we skip it? Or process all but the last? Actually, we need to process all actions. The last action is the correct guess. We don't count it as a shock. But we have to update the set? For the last action, if it's a '?', then we remove the letter? But no: the last action is correct, so we don't remove it. Actually, we don't remove the correct letter. So for the last action, we do nothing? Or we break?

But the problem says: the show ends when he guesses correctly. So we don't process after that. But the input includes the last action. So we have to process all n actions.

But for the last action, which is a correct guess, we don't get a shock. So we don't count it as excessive. Also, for the set: when we process the last action, it is a '? s' and s is the secret. We don't remove it, and the set remains the same. But we don't need to update the set because after that the show ends.

So algorithm:

possible = set of all 26 letters
excessive = 0
determined = False   # set to True once we have |possible| == 1

for i in range(n):
    action = actions[i].split()
    type = action[0]
    data = action[1]

    # If this is the last action, it's a correct guess. We don't update the set? Actually, we don't need to update because we know it's correct. But we can skip updating? Or we can update: for the last action, if it's a guess, then we know it's correct, so we don't remove. But we don't do anything. But note: the set might be size 1 and we already know the secret. But the last action is a guess and correct, so we don't get a shock. So we don't count.

    # However, for the last action, we don't get a shock, so no excessive. But we still have to update the set for the actions before the last? Actually, the last action is the only correct guess. So for all actions except the last, we update the set.

    But wait: the problem says the last action is a correct guess. So we can process the first n-1 actions normally, and then the last action is special: we don't update the set (because it's correct and we don't remove the letter) and we don't count a shock.

    Alternatively, we can process all actions, but for the last one, if it's a guess, we do nothing to the set? Because it's correct. But actually, we don't need to update the set for the last action because after that we break. But the set remains the same.

    Actually, for the last action, if it's a guess and correct, we don't change the set. But if we try to update, for a '?' action, we would normally remove the letter? But we shouldn't because it's correct. So we have to avoid updating the set for the last action? Or we can process all actions, and for the last action, we skip the set update? 

    Alternatively, we can note: for the last action, it's a correct guess, so we don't remove the letter. But also, we don't get a shock. So we can do:

    for each action index i from 0 to n-1:
        if i == n-1: 
            # last action: skip updating the set? But we don't need to, because it's correct. But we also don't get a shock. So we break?
            # Actually, we don't do anything for the last action? But we must check: if the set was already determined, then the last action is a guess and correct, so no shock. So no excessive. But we don't count it. So we can break after processing the second to last? 

        Alternatively, we can process the last action as a no-op for the set? Because:
          - If it's a '.' or '!', that would be inconsistent? The problem says the last action is a correct guess. So it must be a '?'.

        So the last action is '? s' and s is correct. So we do nothing to the set. We only need to check: if we are in the determined state (|possible|==1) and this is not the last action, then we count the shocks. But the last action doesn't cause a shock. So we can process the last action without updating the set and without counting a shock.

    How about: for each action, if it is the last one, we break? But we have to process the set for the last action? Actually, no: because the last action is a correct guess, so it doesn't give new information? The set remains the same. So we can skip the last action? But we have to process the shocks for the actions before the last.

    Actually, the last action is the n-th action. We process from 0 to n-1. But the last action is the n-1-th? Then we process all.

    Let me reframe:

    We are going to iterate over all n actions. For each action, we update the set and then check if the set becomes size 1 (if not already). Then, after the set becomes size 1, we count the shocks (if any) in the subsequent actions (excluding the last action, which is the correct guess and not a shock).

    But note: the last action might cause the set to become size 1? For example, if the last action is the one that reduces the set to 1? Then there are no excessive shocks because the last action is the correct guess and no shock. So we only count shocks that occur after the set becomes 1 and before the last action.

    So steps:

    possible = set('abcdefghijklmnopqrstuvwxyz')
    determined = False
    count_excessive = 0

    for i in range(n):
        act = actions[i].split()
        type = act[0]
        data = act[1]

        # If this is the last action? We can check if i == n-1. But we have to update the set for the last action? Actually, for the last action, which is a correct guess, we don't update the set. Because:
        #   if we do a '? s' and it's correct, we don't remove it. So we skip updating the set for the last action? Or we can update the set for all actions except the last one? 

        But wait: the last action is a correct guess, so we don't remove the letter. But for the set, we don't need to do anything. So we can update the set for the last action only for '.' and '!'? But the problem states the last action is a guess. So it must be '?'. So we can do:

        if i == n-1:
            # This is the last action: it's a correct guess. So we break? Or we don't update the set? And we don't count a shock? But we have to note: if the set is already size 1, then the last action is not a shock. So we don't count excessive. And we break.
            # But we don't need to update the set. So we can break? Or skip.
            # Actually, we don't have to do anything for the last action? Because we already know the set, and the last action doesn't change it. But we have to count the shocks that occurred in the last action? The last action is correct, so no shock. So we skip.
            # But what if the set becomes size 1 at the last action? That can't happen because the last action is a guess. How? If we have a set of size 2, and the last action is a correct guess? But we don't update the set for the last action. So the set remains 2? Actually, no: we don't update the set for the last action. So the set remains as before.

            # Actually, the set update for the last action: we don't do anything because it's correct. So the set remains the same as after the previous action.

            # So we don't need to update the set for the last action. But we do need to check: if the set was already size 1 at the start of the last action, then the last action is a correct guess and not a shock, so no excessive.

            # Therefore, we can break out of the loop? Or just skip processing the last action? Actually, we don't process the last action for set update. But we have to process the shocks for the actions before the last.

            # Actually, we are iterating all actions. The last action doesn't cause a shock? So we don't count it. And we don't update the set? So we can just skip the set update for the last action? But we have to process the set for the last action if it's not a guess? But the problem says it's a guess. So we can skip.

            # Alternatively, we can process the set for the last action: if it's a '.' or '!', we update. But the problem states the last action is a correct guess, so it's '?'. So we don't need to update the set. So we can skip the set update for the last action? Or we can update and it doesn't matter? 

            # But if we update for the last action: if it's a '? s' and s is correct, we don't remove it. So we leave the set unchanged. So we can update for the last action: for '?', we do nothing? Actually, no: for a '?' action that is incorrect, we remove the letter. But the last one is correct, so we don't remove. So we can do:

            #   if type == '?' and i != n-1: then remove s from possible
            #   else: for '.' and '!', update as usual, and for last action '?' do nothing? Or for last action, we skip the removal.

            # But the problem says the last action is the correct guess and the first correct one. So we can do:

            #   if type == '?' and i < n-1:   # not the last action
            #       possible.discard(s)   # or remove, but discard is safe

            #   else: 
            #       for '.' and '!', update as usual.

            # But wait, the last action might be a '.' or '!'? The problem says: the last action is a correct guess. So it's of type '?'. So we don't have to worry. So we can do:

            #   if type == '.':
            #       possible -= set(data)
            #   elif type == '!':
            #       possible &= set(data)
            #   else: # type == '?'
            #       if i < n-1: # not last action
            #           possible.discard(data)

        So we can write:

        if type == '.':
            # remove all letters in data from possible
            possible -= set(data)
        elif type == '!':
            # intersect possible with set(data)
            possible = possible & set(data)
        else: # type is '?'
            # if this is not the last action, then it's an incorrect guess, so remove the letter
            if i < n-1:
                possible.discard(data)

        Then, after updating the set for this action, we check:
          if len(possible) == 1 and not determined:
              # then from now on, any shock in the subsequent actions (until the last) is excessive
              # set determined to True, and note that we start counting from the next action
              determined = True

          But note: the current action might have updated the set to 1. Then the shocks in the current action: are they excessive? No, because at the moment of the action, we haven't processed the update. The shock happens during the action, and then we update the set. So the shock is not excessive. Only the shocks that occur after the action (in the future) are excessive.

        Then, if determined is True and i < n-1: # we haven't reached the last action, and the set is already determined
            # then, if the current action is a shock, we count it as excessive? 
            # But wait: we update the set at the current action, and then check for determined. Then we mark determined. Then for the current action, if it is a shock, we haven't counted it as excessive? Because we set determined after the update.

            Actually, the current action's shock is not excessive because the set becomes 1 only after the action. So we should not count the current action's shock as excessive. We only count the shocks in the actions that come after the current action.

        Therefore, we need to mark the time when we become determined, and then from the next action onward, we count shocks.

        How? We can set a flag at the current action, and then in the next actions, if there's a shock, we count.

        Alternatively, we can do:

        After updating the set, if we are not yet in the determined state and the set becomes 1, then we set determined = True and note that from the next action, we start counting.

        Then, for the current action, we don't count any shock as excessive (because it happened at the same time as the determination).

        Then, for the subsequent actions (until the last one), if the action is a shock (i.e., it's a '!' or it's a '?' that is incorrect and not the last action), then we add to excessive.

        But note: for the current action, if it was a shock, we don't count it as excessive. But we still need to know if the current action was a shock? Actually, we don't count it because the determination happened after the shock.

        So we can do:

        excessive = 0
        determined = False   # becomes True when we have set size 1

        for i in range(n):
            # process action i
            # update the set

            if not determined and len(possible) == 1:
                determined = True
                # mark that from the next action (i+1 to n-2, because the last action is the correct guess at n-1) we count shocks
                # but we don't count the current action

            if determined and i < n-1: 
                # then if the current action is a shock, we count it? 
                # But wait: the current action we just set determined to True. Then the current action is already over. We are updating the set and then checking. So the current action's shock is not counted? Because we set determined after updating, but the shock happened during the action. So we don't count the current action.

                # Actually, we want to count shocks that occur after the determination. The determination happens at the end of this action. So the next actions are the ones that are after.

                # Therefore, we should not count the current action. So we do:

                # Check if the current action is a shock? And then count? But we are at the end of the action. We haven't counted the shock for the current action. 

                # How about: after updating the set and setting the determined flag, we then check for the current action: if we are already determined (from a previous action) and the current action is a shock, then count.

                # Actually, we set determined at the current action, but then we don't count the current action because the determination happened after the shock. So we only start counting from the next action.

            So we need to separate: the setting of determined and then for the subsequent actions (including the current only if we were already determined at the start of the action).

        Alternatively, we can do:

        Let's maintain:
          determined = False
          count = 0

          for i in range(n):
              if determined and i < n-1:   # if we are in the determined state and this is not the last action
                  # then if the current action is a shock, count it
                  if actions[i] is '!' or (actions[i] is '?' and i != n-1):
                      count += 1

              # Then update the set

              # Then, if not determined and len(possible)==1:
                  determined = True

        But wait: the update of the set happens after we check for the shock? Then the set update might make the set size 1, but we haven't set determined yet. So the next action will be the first one that is after the determination.

        But for the current action, we checked for being in the determined state at the beginning. Then we update the set and then set determined. So the current action is not counted as excessive because at the beginning of the action we were not determined.

        Then, for the next action, we will have determined set to True.

        So that works.

        Example: 
        Action 0: ! abc -> shock. At the start of the action, determined is False. Then we update the set: possible = set(abc) (assuming we started with 26, then intersected with 'abc' -> size 3). Then we check: not determined and size>1, so no set determined flag.

        Then, at action 1: . ad -> not shock. At the start, determined is False. Then update: remove 'a','d' -> possible = {b,c}. Then still not set determined.

        Action 2: . b -> not shock. At start, determined=False. Update: remove 'b' -> possible = {'c'}. Then we set determined = True.

        Action 3: ! cd -> shock. At the start of the action, determined is True and i=3 < n-1? n=5, so n-1=4. So i=3 < 4 -> true. Then check: action is '!', so shock -> count +=1. Then update the set: intersect with set('cd') -> {'c'} -> size1, but we are already determined, so no change.

        Action 4: ? c -> last action. At start, determined=True, but i=4 is n-1? So skip the shock count. Then update: since it's last action, we do nothing? Actually, for type '?', and i < n-1? i=4 and n-1=4 -> false, so we don't remove. Then after update, the set is still {'c'}.

        Then we output count=1.

        This matches.

        Example 2: 
        Input: 
          8
          ! hello
          ! codeforces
          ? c
          . o
          ? d
          ? h
          . l
          ? e   # last action

        Steps:

        possible = set(26 letters)
        determined = False
        count = 0

        Action0: '! hello'
          determined? no -> skip counting
          update: possible = possible ∩ set('hello') = set('h','e','l','o')   (size 4)
          then check: not determined and len=4 -> no set

        Action1: '! codeforces'
          determined? no -> skip
          update: possible = possible ∩ set('codeforces') = {'e','o'}   (size2)
          then check: not determined and len=2 -> no set

        Action2: '? c'
          determined? no -> skip counting
          update: since not last action (i=2, n-1=7? n=8 -> last index 7), so remove 'c'. But 'c' is already not in possible? So possible remains {'e','o'}
          then check: size=2 -> no set determined.

        Action3: '. o'
          determined? no -> skip
          update: remove set('o') from possible -> possible = {'e'} -> size 1 -> set determined = True.

        Action4: '? d'
          at start: determined=True and i=4 < 7? (last index is 7) -> yes. Then check: action is '?' -> and it's not last action, so it's a shock? Actually, a '?' action that is incorrect causes a shock. So we count: count=1.
          then update: remove 'd' (but 'd' not in possible) -> remains {'e'}.

        Action5: '? h'
          determined=True, i=5<7 -> yes. Shock? Yes. count=2.
          update: remove 'h' -> remains {'e'}.

        Action6: '. l'
          determined=True, i=6<7 -> yes. But this is a '.' action: not a shock? So we don't count. 
          update: remove 'l' -> remains {'e'}.

        Action7: '? e' -> last action. 
          determined=True, but i=7 == n-1 -> skip counting.
          update: skip removal because last action.

        Then output 2.

        This matches.

        Example 3: 
          7
          ! ababahalamaha   -> set: {'a','b','h','l','m'}? 
          then 6 guesses: 
          ? a -> incorrect, remove a -> set: {'b','h','l','m'}
          ? b -> remove b -> {'h','l','m'}
          ? a -> a not in set -> no change
          ? b -> b not in set -> no change
          ? a -> no change
          ? h -> last action: skip removal.

        When does determined become True? 
          After the first two actions, the set becomes size 3. Then the next three actions: we remove a and b again? But they are not there. So the set remains size 3 until the last action. Then we never set determined=True. So we never count any excessive shock.

        Then count=0.

        But what about the shocks? We have:
          Action0: '!' -> shock (not excessive because not determined)
          Action1: '? a' -> shock (not excessive: set size 4 -> then becomes 3? Actually, after removal, set becomes 4? Wait, initial set 26, then action0: intersect with the word -> set of 5 letters. Then action1: remove 'a' -> set of 4. Then action2: remove 'b' -> set of 3. Then actions 3,4,5: remove a,b,a -> but they are gone, so set remains 3. So determined is never set. Then we don't count any shock as excessive. But the shocks in actions1 to 5: 5 shocks? But we only count if we are in determined state? We never are. So excessive=0.

        But note: the shocks in actions1 to 5: they are not excessive because the set was never reduced to 1. So we output 0.

        But wait: the problem says "excessive electric shocks are those received after the moment the selected letter could have been uniquely determined". So if the set is never reduced to 1 until the last guess, then there is no moment of unique determination. So no excessive shocks.

        Therefore, we only set determined when the set becomes size 1.

        But what if the set becomes size 0? That shouldn't happen because the information is consistent and the last action is correct. So we don't need to handle that.

        Time complexity: 
          n up to 100,000.
          Each action: for '.' and '!', we are converting the word to a set. The total length of words is 100,000. So the total cost for set creation: O(100,000) per set? But the total length of all words is 100,000. So we are iterating each word once? Then the total time is O(100,000) for set creation? 

          But the set operations: 
            For '.' : set difference: the set of the word has size at most 26? Because only 26 distinct letters. So the set operations are O(1) because the set of the word is at most 26. Similarly for '!', the intersection is with a set of at most 26.

          Also, for the possible set: it starts at 26, and then only shrinks. So the set operations are O(1) because the size of the set for the word is small (max 26) and the possible set is at most 26.

        So overall time: O(n * 26) which is O(n) and acceptable for n=100,000.

        Space: we store the set of possible letters, which is at most 26. And for each word, we create a set of distinct letters. The total distinct letters over words is 26? But we create sets for each word. However, the total length of words is 100,000, but the distinct letters per word are at most 26. The memory for each set is at most 26, and we have n words -> n sets, each of size at most 26. So total memory O(26*n) which is 2,600,000? But n=100,000 -> 2.6e6 integers? That might be acceptable in C++ but in Python? But note: the total length of words is 100,000, so we are storing 100,000 words as strings, and then also sets for each. But the sets: for each word, we create a set of distinct letters. The total distinct letters over all words is at most 26? But we are storing 100,000 sets. Each set is small (max 26). The total memory for sets: 100,000 * 26 = 2.6e6 elements? But each element is a character, which is 1 byte? Then 2.6e6 bytes = 2.6 MB. That's acceptable.

        Alternatively, we can avoid creating a set for each word? We could use a precomputation? But not necessary.

        But we can do: for each word, we can create a set only once. Then we do set operations. So it's acceptable.

        Implementation:

        We'll read n.
        Then read n lines.

        Code:

        import sys

        def main():
            data = sys.stdin.read().splitlines()
            n = int(data[0])
            actions = data[1:1+n]

            possible = set("abcdefghijklmnopqrstuvwxyz")
            determined = False
            excessive = 0

            for i in range(n):
                parts = actions[i].split()
                if len(parts) < 2:
                    # but the problem says there is data, so skip?
                    continue
                type_char = parts[0]
                word = parts[1]

                # Check if we are in determined state and this is not the last action (i < n-1) and the action causes a shock
                if determined and i < n-1:
                    # Shock occurs for '!' and for '?' (which is incorrect, but we know it's incorrect because it's not the last action, and the last action is the only correct one). 
                    if type_char == '!' or type_char == '?':
                        excessive += 1

                # Now update the set
                if type_char == '.':
                    # Remove all letters in the word
                    # Create set from the word
                    word_set = set(word)
                    possible -= word_set
                elif type_char == '!':
                    word_set = set(word)
                    possible = possible & word_set
                else:  # type_char == '?'
                    # If it's not the last action, remove the letter
                    if i < n-1:
                        # Remove the letter in word (which is a single letter? Actually, the data is a single letter? The input: "? s", so word is a string of one letter.
                        # We can do: possible.discard(word) or remove? But if the letter is not in the set, discard is safe.
                        # But the problem: the word is a single letter? The example: "? c", so word is "c".
                        # So we can do:
                        #   possible.discard(word)
                        # But what if word has multiple letters? The problem says: "? s" where s is a lowercase English letter. So word is a single letter.
                        # So:
                        if word in possible:
                            possible.discard(word)
                        # Or we can use: 
                        #   possible = possible - {word}
                        # But that creates a new set. Since it's a single letter, we can do:
                        #   possible.discard(word)

                # After updating, check if we are not determined and the set becomes 1
                if not determined and len(possible) == 1:
                    determined = True

            print(excessive)

        But note: the set operation for '!' is O(1) because the set of the word is at most 26? Actually, the size of the word set is at most 26, and the size of possible is at most 26, so the intersection is O(26). Similarly, the set difference for '.' is O(len(word_set)) which is O(26). 

        Also, for the '?' action, we do a single discard which is O(1).

        So overall O(n) which is acceptable.

        Let me test with the examples.

        Example1: n=5
          actions:
            "! abc" -> type='!', word='abc' -> set: possible = possible & {'a','b','c'} -> from 26 to {'a','b','c'}
            then ". ad" -> type='.', word='ad' -> possible = {'a','b','c'} - {'a','d'} = {'b','c'}
            then ". b" -> possible = {'b','c'} - {'b'} = {'c'} -> then we set determined=True
            then "! cd" -> determined is True and i=3 < 4 -> True. And type is '!' -> shock, so excessive=1.
            then "? c" -> last action: skip counting (because i< n-1? i=4, n-1=4 -> false? Actually, i=4 (0-indexed) and n-1=4? Then for the condition: determined and i<n-1 -> 4<4 is false, so skip. Then update: type '?', but i==n-1, so we skip removal. Then we don't change the set.

          Output: 1.

        Example2: 
          Actions:
            0: "! hello" -> possible = set(26) ∩ set('hello') = {'h','e','l','o'} -> size4.
            1: "! codeforces" -> possible = {'h','e','l','o'} ∩ set('codeforces') = {'e','o'} -> size2.
            2: "? c" -> not last, so remove 'c'. But 'c' is not in possible? So no change. Then determined still false.
            3: ". o" -> remove set('o') -> possible = {'e'} -> set determined = True.
            4: "? d" -> determined=True, i=4<7 (last index=7? n=8, last index=7) -> yes, and type '?' -> shock, so excessive=1.
            5: "? h" -> shock, excessive=2.
            6: ". l" -> no shock (because it's '.') so we don't count? But the condition: determined and i<7? i=6<7 -> true, and the type is '.' -> not '!' or '?'. So we don't add. 
                Actually, in the condition we check for '!' or '?'. For '.', we don't get a shock. So we don't add.
            7: "? e" -> last action: skip counting.

          Output: 2.

        Example3: 
          Action0: "! ababahalamaha" -> possible = set(26) ∩ set('ababahalamaha') = set('a','b','h','l','m') -> size5.
          Then 6 guesses: 
            i=1: "? a" -> remove 'a' -> possible = {'b','h','l','m'} -> size4
            i=2: "? b" -> remove 'b' -> size3 -> set determined still false.
            i=3: "? a" -> remove 'a' again? But not in set -> no change.
            i=4: "? b" -> same.
            i=5: "? a" -> same.
            i=6: "? h" -> last action? Actually, n=7, so last action is i=6? Then for this action: 
                determined is still false? Because size=3 (after action2). Then we don't set determined at any point.
                Then for the last action: we skip removal (because i==n-1) and we don't count (because determined is false, and even if it were true, it's the last action).
          And we never set determined to true, so we never count any shock as excessive. 
          But note: the shocks in the actions1 to 5: 
            Action1: type '?' -> shock. But at that time, determined=False -> so we don't count as excessive. Similarly for the others.
          So excessive=0.

        But what about action6? It's the last action and correct, so no shock.

        So output 0.

        Another edge case: 
          When does the set become 1 at the last action? For example, the last action is a '.' or '!' that reduces the set to 1? But the last action is a guess. So it must be '?'. So the last action cannot be a '.' or '!'. 

        Therefore, the set can become 1 at an action that is not the last, and then we start counting from the next action.

        What if the set becomes 1 at the last action? That is, the last action is a guess and it's the one that reduces the set to 1? But we don't update the set for the last action (because it's a correct guess). So we don't remove the letter. So the set becomes 1 only by the previous actions? Then we set determined at the previous action? 

        Actually, the set becomes 1 at the last action only if the last action is an update that reduces the set to 1? But the last action is a correct guess and we don't update the set. So the set must have been 1 at the previous action? 

        For example: 
          n=3
          actions:
            "! a"   -> possible = set(26) ∩ {'a'} -> {'a'}? But wait, the set becomes 1 at action0? Then determined becomes True after action0.
          Then action1: "? b" -> shock? And we count it as excessive? Because determined is True and i=1 < n-1=2 -> yes. Then update: remove 'b' -> but set remains {'a'}.
          Then action2: "? a" -> last action: skip counting.

          So output: 1.

        But is that correct? 
          After action0: we know the secret is 'a'. Then action1: he guesses 'b' (wrong) -> shock. This shock is excessive because we already knew the secret. Then he guesses 'a' (correct) and the show ends.
          So excessive shocks: 1.

        But note: after action0, the set is {'a'} -> so we set determined at the end of action0. Then action1: we are in determined state and it's not the last action, and it's a shock -> count 1. Then action2: last action, skip. So output 1.

        Another edge: the set becomes 1 at an action that is the second to last? Then the last action is the correct guess. So we count the shock at the second to last? But the second to last action: if it's a shock, then we count it.

        Example: 
          n=2
          actions:
            "! a" -> set becomes {'a'} -> set determined=True after this action.
            "? a" -> last action: skip counting.
          Then excessive=0.

        But if we have:
          n=3
          actions:
            "! a" -> set becomes {'a'}, set determined=True.
            "? b" -> shock: excessive=1.
            "? a" -> last action: skip.

        Then output 1.

        What if the set becomes 1 at the first action? Then all subsequent shocks (until the last action) are excessive.

        Example: 
          n=100,000
          first action: "! a" -> set becomes {'a'}, determined=True.
          then 99,998 actions: each is either '! ...' or '? ...' (but not last) -> each of these causes a shock? Because if it's '!', shock always. If it's '?', and not last, shock if incorrect (which it is). 
          then last action: '? a' -> no shock.

          So we count 99,998 excessive shocks.

        But is that acceptable? The constraints: n up to 100,000 -> then 99,998 is acceptable.

        But we must be careful: the condition for counting: 
          if determined and i < n-1 and (type_char == '!' or type_char == '?') 
          then count++.

        This is O(n) and we do set operations that are O(1) per action. So total O(n).

        Now, code implementation:

        We'll write:

        import sys

        def main():
            data = sys.stdin.read().splitlines()
            n = int(data[0])
            actions = []
            for i in range(1, 1+n):
                # Each line: first character is the type, then space, then the word (which may be a word or a letter)
                # We can split the line
                actions.append(data[i])

            # Alternatively, we already have data[1:1+n]

            possible = set("abcdefghijklmnopqrstuvwxyz")
            determined = False
            excessive = 0

            for i in range(n):
                parts = actions[i].split()
                # There must be at least two parts? The type and the word. But what if the word has space? The problem says: for '.' and '!', w is a word (without spaces? The input format: "! w" and "? s", so the word is the rest? But the problem says: "? s" where s is a single letter. And for words, they are one token? The constraints: "All words consist of lowercase English letters". So we can split by space and take the second token as the entire word? Actually, the input is: the first character is the type, then space, then the word (which may be multiple characters). But the split() will split by space. So if the word has space? The problem says: "The next n lines describe his actions, each line containing one of the following: ..." and the word is given. The total length of all words <= 100,000, but the words are one contiguous string? 

                # Example: ". a b c" -> but the problem says: ". w", so w is one word? So no space. So we can split and then parts[0] is the type, and parts[1] is the word.

                if len(parts) < 2:
                    # According to the problem, each action has a type and a word. So this should not happen. Skip?
                    # But the last part might be missing? Then we skip update.
                    word = ""
                else:
                    word = parts[1]

                # Check if we are determined and not the last action and the action is a shock
                if determined and i < n-1:
                    if parts[0] == '!' or parts[0] == '?':
                        excessive += 1

                # Update the set
                if parts[0] == '.':
                    # Remove all letters in the word
                    word_set = set(word)
                    possible -= word_set
                elif parts[0] == '!':
                    word_set = set(word)
                    possible = possible & word_set
                elif parts[0] == '?':
                    # Only update if not the last action
                    if i < n-1:
                        # word is a single letter? The problem says so.
                        # But if word has multiple letters? Then we remove each? The problem says: "? s" where s is a letter. So we can take the first letter? Or the whole word? Actually, the word is the letter. So we can remove the entire word? 
                        # Example: "? c" -> word is "c". 
                        # But what if the input has "? abc"? The problem says: "? s" and s is a letter. So we can assume it's one letter.
                        # We'll remove the entire word as one letter? But if the word is "ab", then we remove the set of letters in "ab"? But that would be two letters. But the problem says s is a letter. So we assume it's one letter.

                        # We'll remove each letter? But that would be incorrect. So we must assume it's one letter.

                        # We can do: 
                        #   for char in word: 
                        #       possible.discard(char)
                        # But the problem says it's one letter. So we can just do:
                        #   possible.discard(word)   -> but word is a string of one letter? Actually, if the word is "a", then we remove the letter 'a'. But if the word is "abc", then we would remove the entire string? But that is not intended.

                        # How about: if the word has length>1? Then we remove each character? But the problem says it's a single letter. So we can break it into characters? 

                        # Actually, the problem says: "? s" where s is a lowercase English letter. So the word should be a single character.

                        # Therefore, we can do:
                        if word:  # non-empty
                            # We remove each character? Or we consider the entire word as one letter? 
                            # But the input format: the second token is the word. For a guess, it's a single letter. So the token is one letter. So we can do:
                            #   if the token has multiple characters, it's an error? But the problem states it's a single letter.

                            # We'll remove each letter in the word? But that would be incorrect if the word is "ab" (it should be one letter). So we'll assume that the token is one letter.

                            # We'll remove the entire word as a set? But then we do: possible -= set(word)
                            # But that would remove multiple letters. The problem says we are guessing one letter. So we should remove only the letter that is the guess. 

                            # But the input for a guess: the entire token is the letter? So if the token is "c", then set(word) is {'c'}. If the token is "ab", then set(word) is {'a','b'}.

                            # The problem says: "? s" and s is a letter. So we can assume the token is one character.

                            # However, to be safe: the problem says "the total length of all words" does not exceed 100,000. And for guesses, each is a single letter. So the total length for guesses is n_guess (each 1). So the token for a guess is one character.

                            # Therefore, we can remove each character? But that would be overkill. Actually, we can do:

                            #   possible.discard(word[0])   ? But that removes only the first character. But the token should be one character.

                            # Instead, we can remove the entire set of the word? But that would remove multiple letters if the token has multiple. But the problem says one letter.

                            # We'll do: 
                            #   for char in word:
                            #       possible.discard(char)
                            # But that would remove each distinct letter in the token. For a guess, the token is one letter, so one removal. But if the token has multiple letters (due to input error), we remove all.

                            # The problem says: "? s" and s is a letter. So we can assume the token is one letter. So we do:

                            #   possible.discard(word)
                            # But wait: word is a string. The set of possible letters contains single characters. We cannot discard a string? 

                            # Example: word = "c", then we want to remove the letter 'c'. So we do: possible.discard('c')? But the set contains characters, and 'c' is a character, but the string "c" is not a character? 

                            # How about: we do set(word) and then subtract? But that creates a set.

                            # Alternatively, we can do: 
                            #   for char in set(word):
                            #       possible.discard(char)
                            # But that's O(1) because the set of the word for a guess is at most 1? Or if the token has multiple letters, then it's the distinct letters. But the problem says one letter.

                            # We'll do: 
                            #   possible -= set(word)
                            # This is safe.

                            possible -= set(word)

                # After updating, check if we are not determined and the set becomes 1
                if not determined and len(possible) == 1:
                    determined = True

            print(excessive)

        But note: for the '?' update, we are subtracting the entire set of the word? For a guess, the word is one letter, so set(word) is a set of one element. But if the token has multiple letters, we remove each distinct letter. The problem says one letter, so it's safe.

        But what if the token is empty? Then set(word) is empty, so we do nothing.

        We can also note: for the last action (which is a guess), we skip the update. So we don't remove the letter.

        Let me test with the example: 
          n=3
          actions:
            "! a"
            "? bc"   # not last, so we remove set('bc') -> which is {'b','c'}
            "? a"   # last action: skip

          After action0: possible = set(26) ∩ {'a'} = {'a'} -> then set determined=True.
          Then action1: we are in determined state and i=1 < n-1=2 -> true. And type is '?' -> shock, so excessive=1.
          Then update: for the guess, we remove set('bc') -> possible = {'a'} - {'b','c'} = {'a'} -> remains 1.
          Then action2: last action, skip update and skip counting (because i==n-1).

          Output:1.

        But the problem says the token for a guess is a single letter. So we don't expect multiple letters. But if there is, we handle it by removing all.

        Alternatively, we can do: for the '?' action, we only remove the first character? But the problem says one letter.

        I think it's safer to remove all distinct letters in the token. But the token for a guess should be one letter. So it's acceptable.

        We can also do: 
            if i < n-1:
                # Remove each distinct letter in the word
                for char in word:
                    # But we can do: possible.discard(char) but that would remove each occurrence? But if we use set, it's the same.
                # Or: 
                #   possible = possible - set(word)
                # which is the same.

        But the set operation for a word of length L: O(L). But the total length of words is 100,000. And for the '?' actions, the words are supposed to be single letters. So the total length for the '?' actions is the number of '?' actions. And the total length of all words is 100,000, so the length of the words for '.' and '!' is the rest. 

        But we are also doing set(word) for '.' and '!' actions. The total length of all words is 100,000, so the total time for creating sets is O(100,000). So overall, it's O(100,000) which is acceptable.

        Therefore, we can use set(word) for all types.

        We can refactor:

        if type_char == '.':
            possible -= set(word)
        elif type_char == '!':
            possible &= set(word)
        else: # '?'
            if i < n-1:
                possible -= set(word)

        This is clean.

        Let me test with the examples.

        Example1: 
          action0: "! abc" -> set('abc') -> possible = possible & {'a','b','c'} -> becomes {'a','b','c'}.
          action1: ". ad" -> set('ad') -> possible = {'a','b','c'} - {'a','d'} = {'b','c'}.
          action2: ". b" -> set('b') -> possible = {'b','c'} - {'b'} = {'c'} -> set determined=True.
          action3: "! cd" -> determined and i=3<4 -> count shock: excessive=1. Then update: possible = {'c'} & set('cd') = {'c'}.
          action4: "? c" -> last action: skip update.

        Output:1.

        Example2: 
          action0: "! hello" -> set('hello') -> possible = set(26) & {'h','e','l','o'} -> {'h','e','l','o'}
          action1: "! codeforces" -> set('codeforces') -> possible = {'h','e','l','o'} & {'c','o','d','e','f','r','s'} = {'e','o'}
          action2: "? c" -> not last, so set('c') -> possible = {'e','o'} - {'c'} = {'e','o'}
          action3: ". o" -> set('o') -> possible = {'e','o'} - {'o'} = {'e'} -> set determined=True.
          action4: "? d" -> determined and i=4<7 -> count shock: excessive=1. Then update: possible = {'e'} - set('d') = {'e'}
          action5: "? h" -> count shock: excessive=2. Then update: {'e'}-{'h'} = {'e'}
          action6: ". l" -> update: {'e'}-{'l'} = {'e'} -> no change. And not a shock? So we don't count (we only count if it's '!' or '?').
          action7: last action: skip update.

          Output:2.

        Example3:
          action0: "! ababahalamaha" -> set of distinct letters: {'a','b','h','l','m'} -> possible = set(26) & that set -> that set.
          action1: "? a" -> set('a') -> possible = {'a','b','h','l','m'} - {'a'} = {'b','h','l','m'}
          action2: "? b" -> set('b') -> possible = {'b','h','l','m'} - {'b'} = {'h','l','m'}
          action3: "? a" -> set('a') -> possible = {'h','l','m'} - {'a'} = {'h','l','m'} (no change)
          ... and so on.

        So the code should be:

        possible = set("abcdefghijklmnopqrstuvwxyz")
        determined = False
        excessive = 0

        for i in range(n):
            parts = actions[i].split()
            if len(parts) < 2:
                word = ""
            else:
                word = parts[1]
            cmd = parts[0]

            # Count shock: if determined and not last action and (cmd is '!' or '?')
            if determined and i < n-1:
                if cmd == '!' or cmd == '?':
                    excessive += 1

            # Update the set
            if cmd == '.':
                possible -= set(word)
            elif cmd == '!':
                possible = possible & set(word)
            elif cmd == '?':
                if i < n-1:
                    possible -= set(word)

            if not determined and len(possible) == 1:
                determined = True

        print(excessive)

        Let me run the small edge: n=2
          actions: 
            "! a"   -> possible = set(26) & set('a') = set('a') -> then set determined=True.
            "? a"   -> last action: skip update. 
            Then at the beginning of the second action: determined is True, but i=1 (the last) -> so we skip counting. Then we output 0.

        Another: n=3
         

## Final Solution

