# Solution for 99_B.md

Generated at: 2025-07-22 06:15:49
Model: deepseek-reasoner

## Reasoning Process

We are given the number of cups `n` and a list of `volumes` (n non-negative integers)

The problem states that initially all cups had the same amount. Then, either:
1. No pouring happened: then all volumes are equal.
2. Exactly one pouring happened: this means that one cup (source) lost `v` ml and another cup (destination) gained `v` ml. All other cups remain at the initial volume.

Therefore, after the pouring, we should have:
- Two cups that are different from the initial volume: one is `initial - v` and the other is `initial + v`.
- The rest of the cups are at the initial volume.

Moreover, note that the pouring must be from one cup to another, so `v` must be positive.

Steps:

1. If all volumes are the same, then output "Exemplary pages."

2. Otherwise, we need to check for exactly one pouring.

Conditions for exactly one pouring:
- There should be exactly two cups that are not equal to the initial volume (which we don't know). But note: the initial volume must be such that the entire set of volumes can be explained.

Let's denote:
- Let `total` be the sum of the volumes. Since pouring from one cup to another doesn't change the total, the initial volume per cup must be `total / n`. But note: if pouring happened, then the initial volume must be such that the two altered cups are symmetric around it.

However, note: the problem says that initially all cups had the same amount. So the initial volume per cup is `total / n`. But this must be an integer? The problem says the volumes are integers. And pouring an integer `v` from one cup to another would mean that the total remains the same. Therefore, if the total is not divisible by `n`, then it's impossible to have the same initial volume? Actually, no: because pouring does not change the total, so the initial volume per cup must be `total / n`. If `total` is not divisible by `n`, then it's an unrecoverable configuration.

But wait: the problem says that the chef poured the same amount initially. So the initial state had `n * x = total`. Therefore, `x = total / n` must be an integer.

So first check: if `total % n != 0`, then we cannot have an initial integer volume. Therefore, output "Unrecoverable configuration."

But note: what if no pouring happened? Then all volumes are equal, and then `total = n * x` so divisible. Similarly, if one pouring happened, then the total remains the same so divisible.

Therefore, we can do:

Case 1: If all volumes are equal: then output "Exemplary pages."

Case 2: If the total is not divisible by `n`: then output "Unrecoverable configuration."

Case 3: Now, total is divisible by `n`, so initial volume `x = total // n`.

Then, we expect:
- Most cups (n-2) have volume `x`.
- One cup has volume `x - v` and another has volume `x + v` for some positive integer `v`.

But note: the pouring is from the cup with `x - v`? Actually, no: the source cup loses `v` so becomes `x - v`, and the destination gains `v` so becomes `x + v`.

But wait: in the example:
  Input: [270, 250, 250, 230, 250]
  Here, x = total / n = (270+250+250+230+250) = 1250, 1250//5=250.
  Then we have:
      cup1: 270 = 250 + 20 -> destination
      cup4: 230 = 250 - 20 -> source
  And the others are 250.

So we can scan the list and check:
- Count how many cups are above `x`, how many are below, and how many are equal.

We should have:
- Exactly one cup above `x`: say by `v` (so volume = x+v)
- Exactly one cup below `x`: by the same `v` (so volume = x-v)
- The rest equal to `x`.

Then we can output: "v ml. from cup #a to cup #b." 
  where `a` is the index of the cup with volume `x-v` and `b` is the index of the cup with volume `x+v`.

But note: the problem indexes cups from 1 to n. In the example: 
  cup1: 270 -> index 1 (but in 0-indexed list, it's at index0) -> so we output cup #1 as the first cup? 
  The example output: "20 ml. from cup #4 to cup #1." 
  Here, the source cup (which was poured from) is the one that ended at 230 (which is the 4th cup, i.e., the 4th in the input order) and the destination is the one that ended at 270 (the 1st cup).

So we need to note the positions (the input order).

Steps for Case 3 (when total divisible by n and not all equal):

1. Let `x = total // n`.
2. Create two lists (or just traverse and record):
   - List of indices where the volume is not `x`.
3. We expect exactly two indices: 
   - One with value `x - v`
   - One with value `x + v` (and `v` must be positive)

But note: we don't know `v`. Actually, the two non-x values must be symmetric: the deviation from `x` must be equal in magnitude and opposite in sign.

So we can:
  - Let `deviations = []`
  - For each volume, if volume != x, then record (index, volume - x)

Then we should have two deviations: one negative and one positive, and they should be negatives of each other.

But note: what if there are more than two non-x? Then invalid.

Algorithm:

1. If all volumes are equal: return "Exemplary pages."

2. total = sum(volumes)
   If total % n != 0: return "Unrecoverable configuration."

3. Let x = total // n.

4. Let non_x = []   # list of tuples (index, deviation) for volumes that are not x

   For i in range(n):
        if volumes[i] != x:
            non_x.append( (i, volumes[i] - x) )

5. If the number of non_x is not 2: return "Unrecoverable configuration."

6. Now, non_x has two elements: (i1, d1) and (i2, d2)
   Conditions:
      d1 must be -d2 and d1 must be negative? Actually, one positive and one negative and they are negatives.

   Check: d1 = -d2? and d1 != 0 (which we already know because they are non-x).

   But note: if one is positive and the other negative and they are negatives, then d1 = -d2.

   Also, we must have d1 positive? Actually, we don't care: we know one will be positive and the other negative? Actually, if one is positive, the other must be negative and the absolute values equal.

   So check: d1 + d2 == 0? and d1 != 0.

   However, if d1 is positive and d2 is negative and d1 = -d2, then d1+d2=0.

   But note: if d1 and d2 are both zero? we filtered non_x so they are non-zero.

7. If d1 + d2 != 0, then invalid -> "Unrecoverable configuration."

8. Now, we have two deviations: one positive and one negative. The positive deviation (gain) is the destination, the negative deviation (loss) is the source.

   Specifically:
        The cup with deviation = negative value (say -v) is the source (cup a) -> it lost v ml.
        The cup with deviation = positive value (say +v) is the destination (cup b).

   Then we output: "v ml. from cup #a to cup #b." 

   But note: the example: 
        The source cup (which lost 20 ml) ended at 230 -> so deviation = 230 - 250 = -20 -> so we take the absolute value? 
        Actually, we want v to be positive. We can take v = abs(d1) (since d1 = -d2, so |d1|=|d2|).

   And the source cup is the one with the negative deviation, and the destination is the one with the positive.

   But wait: the pouring is from the source (which originally had x, then lost v) to the destination (which originally had x, then gained v). So the source ended at x - v, and the destination ended at x + v.

   Therefore:
        source: index of the cup with volume = x - v (which is deviation = -v)
        destination: index of the cup with volume = x + v (deviation = +v)

   So v = abs(deviation) for the negative one? Actually, we can take v = positive value, which is the amount poured.

   Specifically, v = d2 (if d1 is negative and d2 is positive) or v = d1 (if d1 is positive and d2 is negative)? But we know one is positive and the other negative? Actually, we can do:

        v = abs(d1)   # since |d1| = |d2|

        Then, source_index = the index of the cup with deviation = -v
        destination_index = the index of the cup with deviation = +v

   But note: the two deviations: one is -v and the other is +v.

   How to assign? We can check the sign.

   Alternatively: 
        if d1 < 0:
            source_index = i1
            destination_index = i2   # because d2 must be positive? because d1 = -d2 -> if d1 is negative then d2 = -d1 = positive.
        else:
            source_index = i2
            destination_index = i1

   But note: if d1 is positive, then d2 must be negative? because d1 = -d2 -> if d1 is positive then d2 = -d1 (negative). So actually, we have one positive and one negative.

   Therefore, we can do:

        if d1 > 0:
            # then d2 must be negative
            source_index = i2
            destination_index = i1
            v = d1   # because d1 is positive
        else: 
            # d1 < 0
            source_index = i1
            destination_index = i2
            v = -d1   # because d1 is negative, so -d1 is positive

   Alternatively, we can do:

        v = abs(d1)   # which is the same as abs(d2)
        source_index = the index of the cup with deviation = -v
        destination_index = the index of the cup with deviation = v

   But note: we have the two deviations: one is -v and one is v.

   So we can:

        if non_x[0][1] < 0:
            source_index = non_x[0][0]
            destination_index = non_x[1][0]
            v = -non_x[0][1]   # because non_x[0][1] is negative, so we take positive
        else:
            source_index = non_x[1][0]
            destination_index = non_x[0][0]
            v = non_x[0][1]

   But wait, if non_x[0][1] is negative, then the other must be positive? So we can do:

        dev1 = non_x[0][1]
        dev2 = non_x[1][1]
        if dev1 < 0 and dev2 > 0:
            source_index = non_x[0][0]
            destination_index = non_x[1][0]
            v = dev2   # which is positive
        elif dev1 > 0 and dev2 < 0:
            source_index = non_x[1][0]
            destination_index = non_x[0][0]
            v = dev1   # which is positive
        else:
            # This should not happen because we know one positive and one negative? but what if both positive? then we already have two non_x and they are not symmetric? Actually, we checked d1+d2==0, so if both positive, then d1+d2 !=0 -> so we already caught that? 

   Actually, we have already checked that d1+d2==0, so one must be positive and the other negative? Because if both positive: then d1+d2>0, if both negative: then d1+d2<0. Only when one positive and one negative and of the same absolute value, then they add to zero.

   Therefore, after checking d1+d2==0, we know one is positive and the other negative.

   So we can simply:

        v = abs(dev1)   # since |dev1| = |dev2|
        # Now, the source is the one with the negative deviation, the destination is the positive.

        if dev1 < 0:
            source_index = non_x[0][0]
            destination_index = non_x[1][0]
        else:
            source_index = non_x[1][0]
            destination_index = non_x[0][0]

   But note: we don't know which of the two is negative? We can check.

   Alternatively, we can do:

        # We know one element in non_x has negative deviation and the other positive.
        for i, dev in non_x:
            if dev < 0:
                source_index = i
                source_dev = dev
            else:
                destination_index = i
                destination_dev = dev

        Then v = destination_dev   (which is positive)

   But note: we have only two, so we can do:

        source_index = None
        destination_index = None
        for i, dev in non_x:
            if dev < 0:
                source_index = i
            else:
                destination_index = i

        v = -non_x[0][1] if non_x[0][1] < 0 else non_x[1][1]   # but simpler: v = abs(dev) for the negative one? or just take the positive deviation.

        Actually, we know the positive deviation is the v. So we can take v = max(dev1, dev2)   # since one is negative and the other positive, the max is the positive.

        Or: v = (dev1 - dev2) // 2? Not necessary.

   However, we know that the positive one is v and the negative is -v, so we can set v = the positive deviation.

   How about: 
        v = abs(dev1)   # since both have same absolute value.

   So we can do:

        v = abs(dev1)

        Then, we need to assign:
            source_index: the index of the cup with deviation = -v
            destination_index: the index of the cup with deviation = v

        We can do:

            if non_x[0][1] == -v:
                source_index = non_x[0][0]
                destination_index = non_x[1][0]
            else:
                source_index = non_x[1][0]
                destination_index = non_x[0][0]

   But note: we know the deviations are -v and v, so one of them must be -v and the other v.

   Actually, we can avoid the if by:

        # We have two: one with dev = -v and one with dev = v.
        # Since we have v = abs(dev1), then one of the deviations is negative and the other positive? 

        # Alternatively, we can simply:
        #   if non_x[0][1] < non_x[1][1]:
        #       then non_x[0] is negative and non_x[1] is positive? 
        #   But what if non_x[0] is positive? Then non_x[0] > non_x[1] (which is negative) -> so we can't compare that way.

   Actually, we can do:

        if non_x[0][1] < 0:
            source_index = non_x[0][0]
            destination_index = non_x[1][0]
        else:
            source_index = non_x[1][0]
            destination_index = non_x[0][0]

   But note: if non_x[0][1] is negative, then non_x[1][1] must be positive? because we checked d1+d2=0. So that works.

   Therefore, we can do:

        if non_x[0][1] < 0:
            source_index = non_x[0][0] + 1   # because the problem indexes from 1
            destination_index = non_x[1][0] + 1
        else:
            source_index = non_x[1][0] + 1
            destination_index = non_x[0][0] + 1

        v = abs(non_x[0][1])   # or v = abs(non_x[0][1]) because both have the same absolute value.

   However, note: we can also compute v from the positive deviation: 
        v = non_x[1][1] if non_x[0][1] < 0 else non_x[0][1]

   But that is the same as the absolute value of the negative one? 

   Actually: 
        If non_x[0][1] is negative: then v = -non_x[0][1] = non_x[1][1] (since non_x[1][1] = -non_x[0][1])
        If non_x[0][1] is positive: then we use non_x[0][1]? But then non_x[1][1] is negative, so we don't want that? 

   Actually, we want v to be positive. So we can do:

        v = abs(non_x[0][1])   # which is the same as abs(non_x[1][1])

   So we can just use that.

Summary of the algorithm:

1. If n==0: handle? but n>=1.

2. Check if all volumes are the same: 
        if min(volumes) == max(volumes): then return "Exemplary pages."

3. total = sum(volumes)
   if total % n != 0: return "Unrecoverable configuration."

4. x = total // n

5. non_x = []
   for i in range(n):
        if volumes[i] != x:
            non_x.append( (i, volumes[i]-x) )

6. If len(non_x) != 2: return "Unrecoverable configuration."

7. Let dev1 = non_x[0][1], dev2 = non_x[1][1]
   If dev1 + dev2 != 0: return "Unrecoverable configuration."

8. Now, we know we have one negative and one positive. Let v = abs(dev1)   (which is positive)

   Then, if non_x[0][1] < 0:
        source_index = non_x[0][0] + 1
        destination_index = non_x[1][0] + 1
   else:
        source_index = non_x[1][0] + 1
        destination_index = non_x[0][0] + 1

9. Format the string: f"{v} ml. from cup #{source_index} to cup #{destination_index}."

But note: the example output: "20 ml. from cup #4 to cup #1." 
   In the example: 
        cups: [270,250,250,230,250] -> 
        indices: 
            0: 270 -> deviation = +20 -> destination -> becomes cup #1 (index0 -> cup1)
            1: 250 -> skip
            2: 250 -> skip
            3: 230 -> deviation = -20 -> source -> becomes cup #4 (index3 -> cup4)

   So our method: 
        non_x: 
            (0, 20) and (3, -20)
        Then we check: 
            non_x[0][1] = 20 -> positive -> so we take: 
                source_index = non_x[1][0] + 1 = 3+1 = 4
                destination_index = non_x[0][0] + 1 = 0+1 = 1
        Then output: "20 ml. from cup #4 to cup #1." -> matches.

Edge Cases:

- n=1: 
   Then: 
        total = that one volume.
        x = total // 1 = total -> so the deviation: if there's only one cup, then we expect no pouring? because you can't pour from a cup to itself? 
        But the problem says: poured from one cup to another. So if n=1, then no pouring is possible? 
        Also, the pouring must be from one cup to another? meaning two distinct cups? so n>=2 for pouring to occur? 

   However, the problem states: "exactly one pouring" meaning one pouring operation which involves two distinct cups.

   So for n=1: 
        The input: 
            1
            250
        Then we check: all volumes are the same -> "Exemplary pages."

   But what if n=1 and total is not divisible by 1? That never happens because 250 % 1 == 0.

- Another edge: two cups, but they are swapped? 
        Input: 
            2
            250
            250   -> then all same -> exemplary.

        Input:
            2
            230
            270
        Then total = 500, x = 250.
        non_x: 
            index0: 230 -> dev = -20
            index1: 270 -> dev = +20
        Then we have two non_x, and dev0+dev1=0 -> valid.
        Then output: 
            non_x[0] is (0, -20) -> negative -> source: index0 -> cup1, destination: index1 -> cup2 -> "20 ml. from cup #1 to cup #2."

        But what if the input order is reversed? 
            2
            270
            230
        Then non_x: 
            index0: 270 -> +20 -> positive -> then in the else branch: 
                source_index = non_x[1][0] + 1 = 1+1=2
                destination_index = non_x[0][0] + 1 = 0+1=1
            -> "20 ml. from cup #2 to cup #1."

        This makes sense: because the second cup (230) is the source and the first (270) is the destination? 

        But wait: the pouring: 
            We started with [250,250]. Then poured from cup2 to cup1: 
                cup1 becomes 250+20 = 270
                cup2 becomes 250-20 = 230.

        So the pouring was from cup2 to cup1? So the source is cup2 and destination is cup1.

        Therefore, the output for [270,230] is "20 ml. from cup #2 to cup #1." -> correct.

- What if more than two non_x? 
        Example: 
            n=3, volumes = [100, 200, 300] -> total=600, x=200 -> non_x: 
                index0: 100 -> -100
                index1: 200 -> skip
                index2: 300 -> +100? -> but then we have two non_x: [ (0,-100), (2,100) ] -> that would be valid? 
            But wait: what if the initial was 200, then poured from cup0 to cup2: 100 ml? 
            Then: 
                cup0: 200-100=100
                cup1: 200
                cup2: 200+100=300 -> matches.

            So that would be valid.

        However, what if: 
            n=3, volumes = [100, 200, 400] -> total=700 -> not divisible by 3? 700 % 3 != 0 -> unrecoverable.

        What if: 
            n=3, volumes = [100, 200, 300] -> total=600 -> divisible by 3 -> x=200.
            non_x: [0:100 -> -100, 2:300 -> +100] -> then two non_x -> and -100+100=0 -> valid.

        So output: "100 ml. from cup #1 to cup #3." -> because index0 -> cup1, index2 -> cup3.

        But note: the problem says "exactly one pouring" - this is one pouring.

        What if: 
            n=4, volumes = [100, 200, 200, 300] -> total=800, x=200.
            non_x: [0:100 -> -100, 3:300 -> +100] -> two non_x -> valid.

        What if: 
            n=4, volumes = [100, 200, 300, 400] -> total=1000, x=250.
            non_x: 
                0:100 -> -150
                1:200 -> -50
                2:300 -> +50
                3:400 -> +150
            Then we have 4 non_x -> so we return "Unrecoverable configuration." -> which is correct because one pouring cannot account for four changes.

        What if: 
            n=3, volumes = [100, 200, 301] -> total=601 -> not divisible by 3 -> unrecoverable.

        What if: 
            n=3, volumes = [100, 200, 299] -> total=599 -> not divisible by 3 -> unrecoverable.

        What if: 
            n=3, volumes = [100, 200, 300] -> valid.

        What if: 
            n=3, volumes = [100, 200, 299] -> total=599 -> 599 % 3 !=0 -> unrecoverable.

        What if: 
            n=3, volumes = [100, 200, 300] -> valid.

        What if: 
            n=3, volumes = [100, 201, 299] -> total=600 -> x=200.
            non_x: 
                0:100 -> -100
                1:201 -> +1
                2:299 -> +99
            Then non_x has 3 entries -> so unrecoverable.

        What if: 
            n=3, volumes = [100, 200, 301] -> total=601 -> 601 % 3 != 0 -> unrecoverable.

        What if: 
            n=3, volumes = [100, 200, 300] -> valid.

        What if: 
            n=3, volumes = [100, 300, 200] -> same as above: 
                total=600, x=200.
                non_x: 
                    index0:100 -> -100
                    index1:300 -> +100
                    index2:200 -> skip
                Then two non_x: [ (0,-100), (1,100) ] -> and they add to 0 -> valid.
                Then: 
                    non_x[0] is (0,-100) -> negative -> source: index0+1=1, destination: index1+1=2.
                Output: "100 ml. from cup #1 to cup #2."

        This makes sense: from cup1 to cup2.

        But note: the cup3 is 200 -> unchanged.

        What if: 
            n=4, volumes = [100, 200, 200, 300] -> valid: 100 and 300 are the non_x: 
                non_x: [0:100 -> -100, 3:300 -> +100] -> valid.

        What if: 
            n=4, volumes = [100, 200, 200, 100] -> 
                total = 600, x=150? -> 600/4=150 -> 
                non_x: 
                    index0:100 -> -50
                    index1:200 -> +50
                    index2:200 -> +50
                    index3:100 -> -50
                Then we have four non_x -> so invalid.

        Why? Because we can only have two cups altered. Here four cups are altered. So invalid.

        But note: what if two pourings? The problem states exactly one pouring. So only two cups should be altered.

        Therefore, the algorithm:

            def solve(n, volumes):
                if all(v == volumes[0] for v in volumes):
                    return "Exemplary pages."
                total = sum(volumes)
                if total % n != 0:
                    return "Unrecoverable configuration."
                x = total // n
                non_x = []
                for i, vol in enumerate(volumes):
                    if vol != x:
                        non_x.append( (i, vol - x) )
                if len(non_x) != 2:
                    return "Unrecoverable configuration."
                dev1, dev2 = non_x[0][1], non_x[1][1]
                if dev1 + dev2 != 0:
                    return "Unrecoverable configuration."
                # Now, we have two non_x: one positive and one negative.
                # We know they are symmetric.
                if non_x[0][1] < 0:
                    source_index = non_x[0][0] + 1
                    destination_index = non_x[1][0] + 1
                else:
                    source_index = non_x[1][0] + 1
                    destination_index = non_x[0][0] + 1
                v = abs(dev1)   # or abs(dev2), same.
                return f"{v} ml. from cup #{source_index} to cup #{destination_index}."

Let's test with the examples:

Example 1: [270,250,250,230,250] 
        total = 1250, n=5 -> x=250.
        non_x: 
            i0: 270 -> +20 -> (0,20)
            i3: 230 -> -20 -> (3,-20)
        len(non_x)=2 -> ok.
        dev1+dev2 = 20-20=0 -> ok.
        non_x[0][1]=20>0 -> so we take: source_index = non_x[1][0]+1 = 3+1=4, destination_index = 0+1=1.
        v = abs(20)=20 -> "20 ml. from cup #4 to cup #1." -> correct.

Example 2: [250,250,250,250,250] -> all same -> "Exemplary pages."

Example 3: [270,250,249,230,250] -> 
        total = 270+250+249+230+250 = 1249
        1249 % 5 != 0? 1249 / 5 = 249.8 -> not integer -> "Unrecoverable configuration." -> matches.

But wait: the example output is "Unrecoverable configuration." for example3.

Another test: 
        Input: [100, 200, 300] for n=3 -> 
        total=600, x=200.
        non_x: 
            index0:100 -> -100 -> (0,-100)
            index2:300 -> +100 -> (2,100)
        Then: 
            non_x[0][1] = -100 <0 -> source_index = 0+1=1, destination_index=2+1=3, v=abs(-100)=100.
        Output: "100 ml. from cup #1 to cup #3."

But what if the input: [300,200,100] -> 
        non_x: 
            index0:300 -> +100 -> (0,100)
            index2:100 -> -100 -> (2,-100)
        Then non_x[0][1]=100>0 -> so source_index = non_x[1][0]+1 = 2+1=3, destination_index=0+1=1.
        Output: "100 ml. from cup #3 to cup #1." -> which is correct.

Another test: two cups: [200,100] -> 
        total=300, n=2 -> x=150.
        non_x: 
            index0:200 -> +50
            index1:100 -> -50
        Then: non_x[0][1]=50>0 -> source_index = non_x[1][0]+1 = 1+1=2, destination_index=0+1=1.
        Output: "50 ml. from cup #2 to cup #1." -> correct.

Another test: two cups: [100,200] -> 
        non_x: 
            index0:100 -> -50
            index1:200 -> +50
        Then non_x[0][1]=-50<0 -> source_index=0+1=1, destination_index=1+1=2.
        Output: "50 ml. from cup #1 to cup #2." -> correct.

But note: what if the two non_x deviations are not symmetric? 
        Example: n=2, volumes=[100, 300] -> total=400, x=200.
        non_x: 
            (0, -100), (1, +100) -> symmetric -> valid.

        But what if: [100, 301] -> total=401, 401%2 !=0 -> unrecoverable.

        What if: [100, 300] -> valid.

        What if: [100, 201] -> total=301 -> not divisible by 2 -> unrecoverable.

        What if: [100, 200, 200, 200] -> 
            total=700, n=4 -> x=175.
            non_x: 
                index0:100 -> -75
                index1:200 -> +25
                index2:200 -> +25
                index3:200 -> +25
            Then non_x has 4 entries -> unrecoverable.

        What if: [100, 200, 200, 300] -> 
            total=800, x=200 -> 
            non_x: 
                index0:100 -> -100
                index3:300 -> +100 -> two non_x -> and -100+100=0 -> valid.
            Output: "100 ml. from cup #1 to cup #4." -> because non_x[0] is (0,-100) -> source is index0+1=1, destination is index3+1=4.

        But wait: the other cups (index1 and index2) are 200 -> unchanged. So it's valid.

        What if: [100, 200, 300, 400] -> 
            total=1000, x=250 -> non_x: 
                0:100 -> -150
                1:200 -> -50
                2:300 -> +50
                3:400 -> +150
            Then non_x has 4 entries -> unrecoverable.

        What if: [100, 200, 300, 400, 250] -> 
            total=1250, n=5 -> x=250 -> 
            non_x: 
                0:100 -> -150
                1:200 -> -50
                2:300 -> +50
                3:400 -> +150
                4:250 -> skip
            Then non_x has 4 entries -> unrecoverable.

        What if: [100, 200, 300, 400, 250, 250] -> 
            n=6, total=1500, x=250.
            non_x: 
                0:100 -> -150
                1:200 -> -50
                2:300 -> +50
                3:400 -> +150
            non_x has 4 entries -> unrecoverable.

        What if: [150, 250, 350] -> 
            n=3, total=750, x=250.
            non_x: 
                0:150 -> -100
                2:350 -> +100
            Then valid: "100 ml. from cup #1 to cup #3."

        What if: [150, 250, 350, 250] -> 
            n=4, total=1000, x=250.
            non_x: 
                0:150 -> -100
                2:350 -> +100
            Then two non_x -> and -100+100=0 -> valid.
            Output: "100 ml. from cup #1 to cup #3."

        But note: the other two cups are 250 -> unchanged.

        So the algorithm seems to cover.

However, note: what if the two non_x deviations are symmetric but the absolute value is not an integer? 
   But the volumes are integers, and the deviations are integers? 
        volumes[i] - x: since volumes[i] and x are integers, the deviation is integer.

   And we checked that the deviations are negatives of each other (so they are integers and symmetric).

Therefore, the solution is as above.

Code Implementation:

Note: We have to be cautious because the problem states non-negative integers.

Let's code accordingly.

We'll do:

    if n == 0:
        # but n>=1, so skip
    if all(v == volumes[0] for v in volumes):
        return "Exemplary pages."

    total = sum(volumes)
    if total % n != 0:
        return "Unrecoverable configuration."

    x = total // n
    non_x = []
    for i, vol in enumerate(volumes):
        diff = vol - x
        if diff != 0:
            non_x.append((i, diff))

    if len(non_x) != 2:
        return "Unrecoverable configuration."

    (idx1, diff1), (idx2, diff2) = non_x
    if diff1 + diff2 != 0:
        return "Unrecoverable configuration."

    # Now, we have two non_x: one positive and one negative.
    if diff1 < 0:
        source_index = idx1 + 1
        destination_index = idx2 + 1
        v = -diff1   # which is positive
    else:
        source_index = idx2 + 1
        destination_index = idx1 + 1
        v = diff1   # which is positive

    # But note: we could also have done v = abs(diff1) and then the if for the indices.

    return f"{v} ml. from cup #{source_index} to cup #{destination_index}."

However, note: we have two deviations: one is positive and one is negative. The above if-else uses the fact that if diff1 is negative then we use that as source, and the other as destination. Otherwise, we use the other one as source and the first as destination? Actually, no: in the else branch, diff1 is positive, then we set source_index to idx2 (which we assume is negative) and destination_index to idx1 (which is positive). But we know that one of the two is negative and the other positive? Actually, we know that because we checked the sum is zero. So if diff1 is positive then diff2 must be negative? because diff1+diff2=0 -> so diff2 = -diff1 -> negative.

Therefore, we can also do:

    if diff1 < 0:
        source_index = idx1 + 1
        destination_index = idx2 + 1
        v = -diff1
    else: 
        # then diff1>0 and diff2<0
        source_index = idx2 + 1
        destination_index = idx1 + 1
        v = diff1

But note: what if one of the deviations is zero? but we filtered non_x only for non-zero. So no.

Alternatively, we can write:

    # Since we know the two deviations are negatives of each other, we can simply:
    v = abs(diff1)
    if diff1 < 0:
        source_index = idx1 + 1
        destination_index = idx2 + 1
    else:
        source_index = idx2 + 1
        destination_index = idx1 + 1

But note: if diff1 is negative, then idx1 is the source. Otherwise, if diff1 is positive, then idx2 must be the source? Actually, no: because if diff1 is positive, then the other (diff2) is negative -> so the source is the one with negative deviation, which is idx2.

Therefore, the above if-else is correct.

Let me test with the example [100,200,300] (n=3):
    non_x: (0,-100) and (2,100)
    Then: 
        diff1 = -100 -> negative -> source_index = 0+1=1, destination_index=2+1=3 -> correct.

    Example [300,200,100]:
        non_x: (0,100) and (2,-100)
        Then: diff1=100 (positive) -> so source_index = idx2+1 = 2+1=3, destination_index=0+1=1 -> correct.

Therefore, we can write the code accordingly.

Final code:

    def solve(self, n: int, volumes: List[int]) -> str:
        # If all are the same
        if all(v == volumes[0] for v in volumes):
            return "Exemplary pages."
        
        total = sum(volumes)
        # If total is not divisible by n
        if total % n != 0:
            return "Unrecoverable configuration."
        
        x = total // n
        non_x = []
        for i, vol in enumerate(volumes):
            diff = vol - x
            if diff != 0:
                non_x.append( (i, diff) )
                
        # Check the number of non-zero deviations
        if len(non_x) != 2:
            return "Unrecoverable configuration."
        
        # Get the two deviations
        (i1, d1), (i2, d2) = non_x
        if d1 + d2 != 0:
            return "Unrecoverable configuration."
        
        # Now, we have one negative and one positive.
        if d1 < 0:
            source_index = i1 + 1
            destination_index = i2 + 1
            v = -d1   # because d1 is negative, so we take positive
        else:
            # Then d2 must be negative (because d1+d2=0) and d1>0
            source_index = i2 + 1
            destination_index = i1 + 1
            v = d1
            
        return f"{v} ml. from cup #{source_index} to cup #{destination_index}."

But note: we can also do without the explicit v assignment:

        if d1 < 0:
            source_index = i1 + 1
            destination_index = i2 + 1
        else:
            source_index = i2 + 1
            destination_index = i1 + 1
        v = abs(d1)   # because d1 and d2 are negatives, so |d1| = |d2|

So:

        if d1 < 0:
            source_index = i1 + 1
            destination_index = i2 + 1
        else:
            source_index = i2 + 1
            destination_index = i1 + 1
        v = abs(d1)

        return f"{v} ml. from cup #{source_index} to cup #{destination_index}."

This is simpler.

But note: we can also use:

        v = abs(d1)

        if d1 < 0:
            ... 
        else:
            ...

But we must be cautious: what if d1 is negative? then we use i1 as source, i2 as destination. If d1 is positive, then we use i2 as source and i1 as destination? Actually, no: because if d1 is positive, then d2 must be negative (because d1+d2=0) so the source should be i2 (which has d2, negative) and destination i1 (d1, positive).

Therefore, the above is consistent.

Therefore, we write:

    def solve(self, n: int, volumes: List[int]) -> str:
        if n == 0:
            return "Unrecoverable configuration."   # though n>=1, but for safety?
        if all(v == volumes[0] for v in volumes):
            return "Exemplary pages."
        
        total = sum(volumes)
        if total % n != 0:
            return "Unrecoverable configuration."
        
        x = total // n
        non_x = []  # store (index, diff)
        for i, vol in enumerate(volumes):
            diff = vol - x
            if diff != 0:
                non_x.append((i, diff))
                
        if len(non_x) != 2:
            return "Unrecoverable configuration."
        
        (idx0, diff0), (idx1, diff1) = non_x
        if diff0 + diff1 != 0:
            return "Unrecoverable configuration."
        
        # Now, we have two deviations: one positive, one negative.
        v = abs(diff0)
        # The source is the one with the negative deviation, the destination is the one with positive.
        if diff0 < 0:
            source_index = idx0 + 1
            destination_index = idx1 + 1
        else:
            source_index = idx1 + 1
            destination_index = idx0 + 1
            
        return f"{v} ml. from cup #{source_index} to cup #{destination_index}."

This is the final solution.

Let me run through the provided examples:

Example 1: [270,250,250,230,250] -> 
        non_x: (0,20) and (3,-20) -> 
        diff0 = 20 -> positive -> then source_index = 3+1=4, destination_index=0+1=1 -> "20 ml. from cup #4 to cup #1." -> correct.

Example 2: [250,250,250,250,250] -> all same -> "Exemplary pages." -> correct.

Example 3: [270,250,249,230,250] -> total=1249 -> 1249 %5 !=0 -> "Unrecoverable configuration." -> correct.

Another test: [100,200,300] (n=3) -> 
        total=600, x=200 -> non_x: (0,-100) and (2,100) -> 
        diff0 = -100 -> negative -> source_index=0+1=1, destination_index=2+1=3 -> "100 ml. from cup #1 to cup #3." -> correct.

Therefore, we are confident.

Note: We must use `from typing import List` if not already imported.

But the problem says we are in a class method.

We'll assume:

    from typing import List

    class Solution:
        def solve(self, n: int, volumes: List[int]) -> str:
            ... 

Let me write the complete code accordingly.

## Final Solution

### Step 1: Problem Understanding and Modeling
- **Problem Analysis**: 
  - Chef Gerasim suspects that juice was poured from one cup to another exactly once. Initially, all cups had the same volume of juice. 
  - The task is to determine if:
    1. No pouring occurred (all cups have equal volume): output "Exemplary pages."
    2. Exactly one pouring occurred (one cup lost `v` ml, another gained `v` ml, others unchanged): output "v ml. from cup #a to cup #b."
    3. The configuration is invalid (any other scenario): output "Unrecoverable configuration."
  - **Key Insight**: The total volume remains unchanged after pouring. Thus, the initial volume per cup must be `total_volume / n`, which must be an integer. If not, the configuration is invalid.
  - **Constraints**: 
    - Number of cups `n` is between 1 and 1000.
    - Juice volumes are non-negative integers up to 10,000.

- **Edge Cases**:
  - All cups have the same volume (no pouring).
  - Total volume not divisible by `n` (invalid).
  - More or fewer than two cups deviate from the initial volume (invalid).
  - The two deviating cups do not have symmetric deviations (i.e., one +v and the other -v) (invalid).

### Step 2: Multiple Solution Exploration
1. **Brute Force Solution**:
   - **Idea**: Check all conditions sequentially: all equal, total divisible by `n`, exactly two deviations, and symmetric deviations.
   - **Time Complexity**: O(n) for checking all conditions (sum, filtering deviations).
   - **Space Complexity**: O(n) to store deviations (can be optimized to O(1)).
   - **Applicability**: Straightforward and efficient for given constraints (n ≤ 1000).

2. **Optimized Solution**:
   - **Idea**: Same as brute force but with early termination and minimal space usage. Instead of storing all deviations, track only the two non-matching cups.
   - **Time Complexity**: O(n) (one pass for sum, one pass for deviations).
   - **Space Complexity**: O(1) (store only the two deviating cups' indices and deviations).
   - **Applicability**: More space-efficient, still linear time.

3. **Space-Optimized Solution**:
   - **Idea**: Compute total and initial volume. Then, in one pass, identify the two deviating cups and verify conditions.
   - **Time Complexity**: O(n) (two passes: one for sum, one for checking deviations).
   - **Space Complexity**: O(1) (only store necessary variables).
   - **Applicability**: Optimal for space, but same time complexity as brute force.

4. **Special Technique Solution**:
   - **Idea**: Use mathematical properties. The sum must be divisible by `n`. The deviations must be exactly two and symmetric.
   - **Time Complexity**: O(n) (same as above).
   - **Space Complexity**: O(1) (same as above).
   - **Applicability**: Leverages arithmetic properties for correctness.

### Step 3: Optimal Solution Selection and Implementation
- **Selection Rationale**: The problem constraints (n ≤ 1000) allow an O(n) solution. The space-optimized approach (O(1) space) is efficient and clear.
- **Implementation**:
  1. If all volumes are equal, return "Exemplary pages."
  2. If total volume is not divisible by `n`, return "Unrecoverable configuration."
  3. Compute initial volume per cup: `x = total // n`.
  4. Traverse volumes to find cups not equal to `x`. If not exactly two, return invalid.
  5. Check if the deviations of these cups sum to zero (symmetric). If not, invalid.
  6. Output the pouring details: volume poured is the absolute deviation, source is the cup with negative deviation, destination is the cup with positive deviation.

- **Key Optimizations**:
  - Early termination if all cups are equal.
  - Sum divisibility check avoids unnecessary processing.
  - Only track two deviating cups for O(1) space.

### Step 4: Verification and Summary
- **Verification**:
  - **Example 1**: Input [270, 250, 250, 230, 250] → Output "20 ml. from cup #4 to cup #1." (correct).
  - **Example 2**: Input [250, 250, 250, 250, 250] → Output "Exemplary pages." (correct).
  - **Example 3**: Input [270, 250, 249, 230, 250] → Total 1249 not divisible by 5 → Output "Unrecoverable configuration." (correct).
- **Summary**:
  - The solution efficiently checks the necessary conditions using arithmetic properties and linear scans.
  - Handles all edge cases, including single cup, symmetric deviations, and invalid configurations.
  - Further optimization is not needed given the constraints.

### Final Code
```python
from typing import List

class Solution:
    def solve(self, n: int, volumes: List[int]) -> str:
        # Check if all volumes are equal
        if all(v == volumes[0] for v in volumes):
            return "Exemplary pages."
        
        total = sum(volumes)
        # Check if total is divisible by n
        if total % n != 0:
            return "Unrecoverable configuration."
        
        x = total // n
        non_x = []
        for i, vol in enumerate(volumes):
            diff = vol - x
            if diff != 0:
                non_x.append((i, diff))
                # Early termination if more than two deviations
                if len(non_x) > 2:
                    return "Unrecoverable configuration."
        
        # Check exactly two deviations
        if len(non_x) != 2:
            return "Unrecoverable configuration."
        
        (idx0, diff0), (idx1, diff1) = non_x
        # Check deviations sum to zero (symmetric)
        if diff0 + diff1 != 0:
            return "Unrecoverable configuration."
        
        # Determine source and destination
        v = abs(diff0)
        if diff0 < 0:
            source_index = idx0 + 1
            destination_index = idx1 + 1
        else:
            source_index = idx1 + 1
            destination_index = idx0 + 1
        
        return f"{v} ml. from cup #{source_index} to cup #{destination_index}."
```