# Solution for 913_B.md

Generated at: 2025-07-22 05:54:01
Model: deepseek-reasoner

## Reasoning Process

We are given the number of vertices `n` and a list `parents` of length `n-1`.
The list `parents` has the parent of vertex 2 at index 0, vertex 3 at index 1, ... vertex n at index n-2.

We know:
- Vertex 1 is the root.
- The tree is rooted at vertex 1 and edges are directed from the root.

Definition of a spruce: every non-leaf vertex must have at least 3 leaf children.

Approach:
1. Build the tree as an adjacency list (but we only care about children). Actually, we don't need to store the entire tree structure? 
   We need to know for each node, its children. Then we can traverse the tree to check the condition.

Steps:
a) Create an array (or list) `children` of size (n+1) (index 0 unused) such that `children[i]` is a list of children of node i.
b) Note: the root is node 1. The root is non-leaf? But note: the root must have at least 2 children (given). However, the condition for spruce requires every non-leaf vertex to have at least 3 leaf children. 
   So if the root has only 2 children, then it fails (unless both children are leaves? But then the root has 2 leaf children, which is less than 3 -> fails).

c) We need to mark which nodes are leaves. A leaf is a node that has no children.

d) For each non-leaf node, we want to count how many of its children are leaves. Then check that this count is at least 3.

But note: what if a non-leaf node has more than 3 children? Then as long as at least 3 of them are leaves, it's okay? However, note the condition: "every non-leaf vertex has at least 3 leaf children". So if a node has 5 children and 3 of them are leaves, that's okay. The other two might be non-leaves? Then we have to check those non-leaves as well.

But note: the condition must hold recursively? Actually, the problem states: "every non-leaf vertex has at least 3 leaf children". So it's only about the immediate children.

So the plan:
1. Build the children list for each node (from index 1 to n).
   - Initialize `children = [[] for _ in range(n+1)]`
   - For i from 0 to n-2 (because parents list has n-1 elements), the vertex is i+2, and parent = parents[i]. So we add (i+2) to children[parents[i]].

2. Identify leaves: a node is a leaf if `len(children[node]) == 0`.

3. For every node from 1 to n:
   - If the node is not a leaf (i.e., it has children), then we count the number of leaf children.
   - How to count leaf children: for each child in children[node], if that child is a leaf (i.e., has no children) then count it.

4. If we find any non-leaf node that has less than 3 leaf children, return "No". Otherwise, return "Yes".

Important: the root is non-leaf? Yes, because the problem states that the root has at least 2 children. But note: the condition requires at least 3 leaf children for non-leaf nodes. So if the root has 2 children (both leaves) then it fails. If the root has 3 children and all are leaves, then it passes. But what if the root has 3 children and one of them is non-leaf? Then the root has 2 leaf children? Then it fails.

Actually, the root must have at least 3 leaf children? But the problem says: "the root has at least 2 children" (from constraints). So we must check the root as well.

Example 1: 
   n=4, parents = [1,1,1] -> meaning:
      vertex2: parent=1
      vertex3: parent=1
      vertex4: parent=1
   So root (1) has three children: [2,3,4]. All of them are leaves? Yes. So condition: root has 3 leaf children -> valid.

Example 2:
   n=7, parents = [1,1,1,2,2,2] -> meaning:
      vertex2: parent=1
      vertex3: parent=1
      vertex4: parent=1
      vertex5: parent=2
      vertex6: parent=2
      vertex7: parent=2
   So:
     root (1) has children [2,3,4]. Now, what about node2? It has children [5,6,7] -> so node2 is non-leaf. 
     Now, for the root: its children are [2,3,4]. How many of these are leaves? None of 2,3,4 are leaves because each has 3 children? Actually, wait: 
        The leaves are 5,6,7 (and also 3 and 4? Let's see: 
          Node 3: has no children? Actually, from the input: 
            vertex5: parent=2 -> so node2 has 3 children: 5,6,7.
            vertex3: no child? So node3 is a leaf? Similarly, node4 is a leaf? 
        So the root has children: [2,3,4]. 
          child 2: non-leaf (has children 5,6,7)
          child 3: leaf
          child 4: leaf
        Therefore, the root has only 2 leaf children (3 and 4). But the root is non-leaf and requires at least 3 leaf children -> fails.

   However, the example output is "No", so this matches.

Example 3:
   n=8, parents = [1,1,1,1,3,3,3] -> meaning:
        vertex2: parent=1
        vertex3: parent=1
        vertex4: parent=1
        vertex5: parent=1
        vertex6: parent=3
        vertex7: parent=3
        vertex8: parent=3
   So:
        root (1): children [2,3,4,5]
        node2: no children? -> leaf
        node3: children [6,7,8] -> non-leaf
        node4: leaf
        node5: leaf
        node6,7,8: leaves

   Now, check the root: 
        leaf children: 2,4,5 -> 3 leaf children (and one non-leaf: node3) -> so root has 3 leaf children -> valid.

   Check node3: non-leaf, and has 3 children (6,7,8) which are all leaves -> valid.

   So output "Yes".

Implementation:

Steps:
1. Create an array `children` of size n+1 (index 0 unused, we use 1..n).
2. For i in range(0, n-1):
        parent = parents[i]   # which is the parent of vertex (i+2)
        children[parent].append(i+2)

   But note: the vertices are 1-indexed. The root is 1. The next vertex is 2, then 3, ... n.

   Actually, the input gives the parent of vertex (i+1) at index i? 
        The first integer (after n) is the parent of vertex 2 -> index0: p0 = parent of vertex2.
        Then the next is parent of vertex3 -> index1: p1 = parent of vertex3.
        ... until index n-2: parent of vertex n.

   So we have:
        for i in range(0, n-1):
            parent = parents[i]
            child_vertex = i+2
            children[parent].append(child_vertex)

3. Mark leaves: We can create an array `is_leaf` of size (n+1) such that:
        is_leaf[i] = (len(children[i]) == 0)

4. Traverse all nodes from 1 to n:
        if the node is not a leaf (so it has children), then we want to count the number of leaf children.
        count = 0
        for each child in children[node]:
            if is_leaf[child]:
                count += 1
        if count < 3:
            return "No"

5. If we check all non-leaf nodes and they all have at least 3 leaf children, return "Yes".

But note: the root has at least 2 children (given) but we still check the root: if the root has less than 3 leaf children, we return "No".

Complexity: 
   Building the children list: O(n)
   Marking leaves: O(n)
   Then we traverse each node, and for each node we traverse its children. The total number of children is n-1 (since there are n-1 edges). So overall O(n).

This is efficient for n up to 1000.

Edge Cases:
   - n=3: the root must have at least 2 children (so two children). Then the root has two children. Both must be leaves? Then the root has 2 leaf children -> condition fails -> output "No". 
        Example: 
            Input: 3
                    1
                    1
            This means: 
                vertex2: parent=1
                vertex3: parent=1
            Root has two children (2 and 3). Both are leaves. Then the root (non-leaf) has 2 leaf children -> less than 3 -> "No".

   - But wait, the problem says: "the root has at least 2 children" (constraint). So n=3 is the minimum, and we have two children. Then we output "No".

   However, the problem states: "Vertex 1 is the root. It's guaranteed that the root has at least 2 children." and the condition requires at least 3 leaf children for non-leaf nodes. So the root fails.

   So the algorithm is straightforward.

Let's code accordingly.

Note: the problem says "non-leaf vertex" -> meaning if a vertex is non-leaf (i.e., has at least one child) then we require at least 3 leaf children.

But note: what if a non-leaf node has non-leaf children? Then we count only the immediate children that are leaves.

We don't care about grandchildren for the condition of the current node.

Implementation:

We'll do:
    children = [[] for _ in range(n+1)]
    for i in range(n-1):
        p = parents[i]   # parent of vertex i+2
        children[p].append(i+2)

    is_leaf = [False] * (n+1)
    for i in range(1, n+1):
        if len(children[i]) == 0:
            is_leaf[i] = True

    for i in range(1, n+1):
        if not is_leaf[i]:   # non-leaf node
            count = 0
            for child in children[i]:
                if is_leaf[child]:
                    count += 1
            if count < 3:
                return "No"

    return "Yes"

But note: the vertices we have are from 1 to n. We skip index0.

Let's test with n=3: 
    children: 
        children[1] = [2, 3]   -> because for i=0: parents[0] = 1 -> child=2; i=1: parents[1]=1 -> child=3.
    Then:
        is_leaf[1] = False (because children[1] has 2 elements)
        is_leaf[2] = True (because children[2] is empty)
        is_leaf[3] = True
    Now, check node1: non-leaf -> count leaf children: 
        children[1]: [2,3] -> both are leaves -> count=2 -> which is <3 -> return "No".

Example 1: n=4, parents=[1,1,1] -> 
    children[1] = [2,3,4]; children[2]=[], children[3]=[], children[4]=[]
    is_leaf: [False, True, True, True] for indices 1..4? Actually:
        is_leaf[1] = False, is_leaf[2]=True, is_leaf[3]=True, is_leaf[4]=True.
    Check node1: count leaf children: 3 (all are leaves) -> passes.

Example 2: n=7, parents=[1,1,1,2,2,2] -> 
    children[1] = [2,3,4]
    children[2] = [5,6,7]   # because: 
        i=0: vertex2 -> parent=1 -> so children[1].append(2)
        i=1: vertex3 -> parent=1 -> children[1].append(3)
        i=2: vertex4 -> parent=1 -> children[1].append(4)
        i=3: vertex5 -> parent=2 -> children[2].append(5)
        i=4: vertex6 -> parent=2 -> children[2].append(6)
        i=5: vertex7 -> parent=2 -> children[2].append(7)
    Then:
        is_leaf[1]: False
        is_leaf[2]: False (because children[2] has 3 elements)
        is_leaf[3]: True (because children[3] is empty)
        is_leaf[4]: True
        is_leaf[5]: True, [6]:True, [7]:True.

    Check node1: non-leaf -> count leaf children: 
        children[1]: [2,3,4] -> 
            2: not leaf -> skip
            3: leaf -> count=1
            4: leaf -> count=2 -> total=2 -> less than 3 -> return "No".

Example 3: n=8, parents=[1,1,1,1,3,3,3] -> 
    children[1] = [2,3,4,5]
    children[2] = [] -> leaf
    children[3] = [6,7,8] -> non-leaf
    children[4] = [] -> leaf
    children[5] = [] -> leaf
    children[6..8] = [] -> leaves.

    Check root (1): 
        children[1]: [2,3,4,5]
        leaf children: 2 (leaf), 4 (leaf), 5 (leaf) -> count=3 -> okay.
    Check node3: 
        children[3]: [6,7,8] -> all leaves -> count=3 -> okay.

    Return "Yes".

So the algorithm is correct.

We'll implement accordingly.

Note: we don't need to build an entire tree, just the children list and then the leaf array.

Let's code.

Time Complexity: O(n) because we traverse each node and each edge once.

Space Complexity: O(n) for storing children and is_leaf.

This meets the constraints (n<=1000).

We'll write the code accordingly.

## Final Solution

### Step 1: Problem Understanding and Modeling
- **Problem Analysis**: The task is to determine if a given rooted tree is a "spruce". A spruce tree requires every non-leaf vertex to have at least 3 leaf children. The tree is rooted at vertex 1, and edges are directed away from the root.
- **Key Constraints**:
  - The number of vertices `n` ranges from 3 to 1000.
  - The root (vertex 1) has at least 2 children.
  - Each non-leaf vertex must have at least 3 leaf children.
- **Input Structure**: 
  - First line: integer `n` (number of vertices).
  - Next `n-1` lines: integers representing the parent of vertices 2 to `n`.
- **Output**: "Yes" if the tree is a spruce; "No" otherwise.
- **Edge Cases**:
  - Root with only 2 children (fails as it needs at least 3 leaf children).
  - Non-leaf nodes with fewer than 3 leaf children (fails).
  - Nodes with non-leaf children still need to meet the leaf child requirement.

### Step 2: Multiple Solution Exploration
1. **Brute Force Solution**:
   - **Idea**: Build the tree structure, identify leaves, and check each non-leaf node for at least 3 leaf children.
   - **Complexity**: O(n) time (each node and edge processed once), O(n) space (storing children and leaf status).
   - **Applicability**: Straightforward and efficient for given constraints (n ≤ 1000).

2. **Optimized Solution**:
   - **Idea**: Same as brute force, but optimized by counting leaf children during tree traversal without extra leaf markers.
   - **Complexity**: Similar to brute force; minor optimization by combining steps.

3. **Space-Optimized Solution**:
   - **Idea**: Use a single array to track children counts and leaf status implicitly.
   - **Complexity**: Same O(n) time and space; minimal space savings.

4. **Special Technique Solution**:
   - **Idea**: Process parent list to directly compute leaf counts without building the full tree structure.
   - **Complexity**: O(n) time, but less intuitive; no significant advantage.

### Step 3: Optimal Solution Selection and Implementation
- **Selection Rationale**: The brute force approach is clear, efficient (O(n) time), and meets constraints. It directly checks the spruce condition by examining each node's children.
- **Implementation**:
  1. **Tree Construction**: Create an adjacency list for children of each node.
  2. **Leaf Identification**: Mark nodes with no children as leaves.
  3. **Validation**: For each non-leaf node, count its leaf children. If any node has fewer than 3, return "No"; else "Yes".
- **Key Optimizations**:
  - Single-pass leaf identification after building the tree.
  - Early termination if any node fails the condition.

### Solution Code
```python
from typing import List

class Solution:
    def solve(self, n: int, parents: List[int]) -> str:
        children = [[] for _ in range(n+1)]
        for i in range(n-1):
            parent = parents[i]
            child_vertex = i + 2
            children[parent].append(child_vertex)
        
        is_leaf = [False] * (n+1)
        for i in range(1, n+1):
            if len(children[i]) == 0:
                is_leaf[i] = True
        
        for i in range(1, n+1):
            if not is_leaf[i]:
                count = 0
                for child in children[i]:
                    if is_leaf[child]:
                        count += 1
                if count < 3:
                    return "No"
        
        return "Yes"
```

### Step 4: Verification and Summary
- **Verification**:
  - **Example 1 (n=4)**: Root has 3 leaf children → "Yes".
  - **Example 2 (n=7)**: Root has only 2 leaf children → "No".
  - **Example 3 (n=8)**: Root has 3 leaf children; node 3 has 3 leaf children → "Yes".
- **Edge Case (n=3)**: Root has 2 leaf children → "No".
- **Summary**:
  - The solution efficiently checks the spruce condition by leveraging tree traversal and leaf counting.
  - Optimal for constraints with O(n) time and space complexity.
  - Further optimizations are unnecessary given problem constraints.