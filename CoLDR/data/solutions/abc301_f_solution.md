# Solution for abc301_f.md

Generated at: 2025-07-22 06:21:01
Model: deepseek-reasoner

## Reasoning Process

Alright, I'm going to tackle this problem step by step. Let me first make sure I understand what is being asked.

### Problem Understanding
The problem is about counting the number of strings that can be formed by replacing each '?' in the given string S with any uppercase or lowercase English letter, such that the resulting string does not contain a DDoS-type string as a subsequence. 

A DDoS-type string is defined as a string of length 4 that satisfies two conditions:
1. The first, second, and fourth characters are uppercase letters, and the third is a lowercase letter.
2. The first and second characters are equal.

Examples: "DDoS", "AAaA" are valid, but "ddos" (all lowercase) and "IPoE" (first two are different) are not.

Constraints: The length of S can be up to 300,000, which means that any brute force approach (like generating all 52^q possibilities) is infeasible.

We need to count the number of valid strings modulo 998244353.

### Key Insight
The main challenge is avoiding any occurrence of a DDoS-type string as a subsequence. Since we're dealing with subsequences (not substrings), the characters don't have to be contiguous. This makes the problem more complex because we have to ensure that no four characters anywhere in the string form a DDoS-type string.

A DDoS-type string has the pattern: 
- Position 0: uppercase letter (say, A)
- Position 1: same uppercase letter (A)
- Position 2: any lowercase letter (say, b)
- Position 3: the same uppercase letter again (A)

So, the pattern is essentially: A, A, [a lowercase], A.

### Approach Brainstorming
1. **Brute Force Approach (Infeasible)**: 
   - Generate all possible replacements of the '?'s (52^q possibilities) and check each string for the presence of any DDoS-type subsequence. 
   - Time complexity: O(52^q * n), which is astronomical for q up to 300,000. Not feasible.

2. **Dynamic Programming (DP) Approach**:
   - We need an efficient way to count the strings that do not contain the forbidden subsequence. This often involves stateful DP where the state captures the progress towards forming the forbidden pattern.
   - Since the forbidden pattern is of length 4, we might design a DP that tracks the last few characters or the progress of forming a DDoS-type string.

3. **State Representation**:
   - We can define a DP state that represents the current progress in forming the forbidden pattern. For the DDoS pattern (A, A, lower, A), we can break it down into stages:
     Stage 0: no progress.
     Stage 1: found first uppercase A.
     Stage 2: found a second A (immediately after the first? Not necessarily contiguous, but in order).
     Stage 3: found a lowercase letter after the two A's.
     Stage 4: found a third A after the lowercase. This is the forbidden state.

   We want to avoid reaching stage 4.

4. **State Machine**:
   - The state can be defined as the length of the longest prefix of the DDoS pattern that we've matched as a subsequence. So the states are 0, 1, 2, 3, and 4 (which is bad). We must avoid state 4.

   However, note that the pattern requires the first two to be the same uppercase letter. So we have to account for the letter identity. This complicates the state because we might have multiple possible letters.

5. **Handling Multiple Letters**:
   - Instead of tracking the exact letter, we can note that the pattern requires the first, second, and fourth characters to be the same uppercase letter. Therefore, the state must remember which uppercase letter is being used for the A's. But tracking a state per letter would multiply the state space by 26, which might be acceptable.

6. **Proposed DP State**:
   - Let dp[i][a][b][c] be ... but that might be too complex. Alternatively, we can use a state that tracks:
        state 0: no progress.
        state 1: we have one uppercase letter (say, A) at some position.
        state 2: we have two A's (with any characters in between, but in order).
        state 3: we have two A's and then a lowercase letter (any, but note that the pattern doesn't require the lowercase to be specific).

   However, the problem is that the third character must be a lowercase, and the fourth must be the same A. Also, after state 3, if we get an A, we go to state 4 (invalid). 

   But note: the state machine must account for multiple possible A's. For example, if we have two different uppercase letters, say A and B, then having two A's and then a lowercase and then an A would form the pattern, but two A's and then a lowercase and then a B would not.

   Therefore, the state must remember the specific uppercase letter that we are collecting for the pattern.

7. **Refined State**:
   - We can define dp[i][state][letter] where:
        i: the current index in the string (from 0 to n-1)
        state: 0, 1, 2, 3 (meaning: 0: nothing, 1: one A, 2: two A's (same), 3: two A's and one lowercase)
        letter: the uppercase letter that we are forming the pattern with (only relevant for state>=1). We can represent letter as an integer from 0 to 25.

   But the state space would be n * 4 * 26, which is about 300,000 * 4 * 26 ≈ 31.2 million states. This might be acceptable in C++ but in Python it could be borderline in speed and memory.

   However, we have 300,000 * 104 states (if we consider state and letter) which is 31.2 million. But each state transition would consider 52 possibilities? Actually, we don't iterate over 52 for each state; we can compute the transitions based on the current character (if fixed) or over 52 if it's a '?'. But wait: we are iterating over the string, and for each state we consider the choices for the current character.

   Actually, the DP would be:

        dp[0][state][letter] = ... initial state

        For each position i from 0 to n-1:
            For each state in [0,1,2,3]:
                For each letter in [0,25] (if state>=1, otherwise we don't need letter? Actually, state 0 doesn't require a letter) OR we can treat state 0 as having no letter, and then when we pick a letter we move to state 1.

   But the state 0 doesn't have an associated letter. So we can split state 0 and states 1-3.

   Alternatively, we can have:

        dp[i][0]: no started pattern (or we can think of as the state without any active letter for the pattern)
        dp[i][1][a]: we have one occurrence of uppercase letter 'a' (so far)
        dp[i][2][a]: we have two occurrences of the same uppercase letter 'a'
        dp[i][3][a]: we have two 'a's and then one lowercase (any) — so we are waiting for the next 'a' to complete the pattern.

   Then, if we ever complete (i.e., when we are in state 3 and we get the same 'a' as an uppercase, then that leads to an invalid string. So we must avoid that.

   Therefore, we design the DP to avoid state 4 (which is the completion). We can simply not count any transition that would lead to state 4.

   The transitions:

   Let the current character be c (which can be a specific letter or '?'). We consider the possibilities:

   - If c is fixed (uppercase or lowercase) or '?' then we have multiple choices.

   For each state and each possible choice for the current character:

        State 0:
            If we choose an uppercase letter x:
                Then we move to state 1 with letter x.
            If we choose a lowercase letter:
                Stay in state 0.

        State 1 with letter a:
            If we choose an uppercase letter x:
                - if x == a: move to state 2 with letter a.
                - if x != a: then we have a new active letter? Actually, we can consider that now we have a new single occurrence of x. But what about the old a? We might have two active patterns? 

        This is a problem: the pattern can be non-overlapping? Actually, the subsequence can be non contiguous. So we can have multiple patterns starting at different times.

   However, the problem is that we must avoid any occurrence of the pattern. The key is: we want to avoid the pattern as a subsequence. We can use the idea of the "longest subsequence" that is a prefix of the pattern. 

   Alternatively, we can use a DP that tracks the maximum progress of any pattern that has been formed so far. But note: if we have multiple patterns, we only care that none of them completes. However, the state must remember the progress for each possible letter? 

   Actually, we can note that the pattern is defined for a specific letter. Therefore, we can have independent states for each letter? But that would multiply the state by 26. 

   Proposed state:

        dp[i][state] for each letter? Actually, we can have:

        Let dp[i][a][b] be too high. Instead, we can use:

        We'll have a state vector of size 4 (for stages 0 to 3) for each letter? Then the state would be 4 * 26. Then the entire state is 104 integers per index.

        Alternatively, we can have:

            dp[i][0]: the count of strings for the prefix S[0:i] that have not started any pattern (or have started but we only track one pattern at a time?).

        But the challenge is that we can have multiple patterns for different letters. We must avoid any pattern completing.

   Actually, the patterns for different letters are independent? However, when we add a character, it might advance the pattern for one letter and not affect others. 

   Therefore, we can maintain for each letter a state (0 to 3). But then the state space would be 26*4 = 104 per index. And then we have 300,000 indices, so total states 300,000 * 104 ≈ 31.2 million. That is acceptable in C++ but in Python we must optimize.

   However, note: the transitions: at each step, we update the state for each letter? But 26*4 per state, and for each state we have to consider the current character? Actually, we don't need to iterate over all 26 letters for each character. We can update the state for the current letter if the current character is uppercase and matches the letter, etc.

   Alternatively, we can design the DP without iterating over all 26 letters at each step. We can aggregate the states per stage over all letters? 

   Insight: The stages for different letters are independent? Actually, no: because if we pick an uppercase letter, it only affects the state for that letter. For other letters, the state remains the same.

   Therefore, we can have:

        Let dp[i][s] be a vector of length 4 for each letter? Actually, we can have:

            dp[i][stage][letter] = count

        But that's a 2D array per i: 4x26. Total states: 300,000 * 4 * 26 = 31.2e6. Each state we update by the current character.

   How to update?

        We'll have an array: dp[stage][letter] for the current position. Initially, at i=0, we have dp0: stage0 for all letters.

        Actually, at the beginning, we can have:

            stage0: count 1 (for no started pattern) but we haven't started any letter. However, we can also consider that for each letter we are in stage0.

        Actually, we can have:

            dp[0][0] = 1 for the entire state? But how do we represent the state per letter? 

        Alternatively, we can avoid storing per letter by noting that the state for a letter is independent. We can store:

            f0: the count of strings that have not started any pattern? Actually, no: because we might have started a pattern for a specific letter.

        Instead, we can store:

            state0: the total count (for all letters, we are at stage0). But wait: actually, the stages are per letter. However, we don't care about the identity of the letter until we start a pattern. 

        How about we store:

            dp[i][0]: the count of strings that have not started any pattern (for any letter) — but actually, we might have started a pattern for a letter and then abandoned it? 

        This is getting messy.

   Another approach: we can use a state that represents the maximum progress of any pattern so far. However, the progress for different letters can be different, and we must avoid any pattern reaching stage 4.

   But note: we can have multiple patterns in progress? For example, we might have two A's and then a lowercase, and also two B's. Then if we add an uppercase A, we complete the pattern for A. So we must avoid that.

   Therefore, the state must remember the progress for each letter? But that is 26 patterns, each having 4 states -> 4^26 states, which is infeasible.

   So we must aggregate. How?

   Insight: The pattern for each letter is independent. The transitions for a given letter only depend on the current character and the state for that letter. And the current character might affect multiple letters? Actually, no: because if the current character is an uppercase letter, say 'A', then it will only update the state for the letter 'A'. If it's a lowercase, it will update the state for every letter that is in state 2 (because the pattern requires a lowercase after two same uppercase). But wait: a lowercase after two same uppercase for any letter? Actually, the pattern for a letter 'A' requires that after the two A's we get a lowercase (any) and then an A. So if we have a lowercase, it can advance any pattern that is at state 2 (for any letter) to state 3.

   Therefore, the state for different letters are not independent: a lowercase letter can advance all active patterns (that are in state 2) to state 3.

   This is a problem: we cannot store per-letter states independently because one character (a lowercase) can update all states at stage2. 

   Then we must store the counts for each state per letter? That would be 26*4 states per index, and we update:

        For a lowercase:
            For each letter, if the state for that letter is 2, then we move it to 3.

        For an uppercase letter 'A':
            For the letter 'A':
                state0 -> state1
                state1 -> state2
                state2: remains state2? Actually, no: because we are getting an extra 'A', so if we are at state2, we have two A's already, and then we get a third: that doesn't form the pattern because the pattern requires the third character to be a lowercase. But if we are at state3 (meaning we have two A's and then a lowercase) and then we get an A, then we complete the pattern -> invalid.

            But also, if we are in state3 for 'A' and we get an uppercase 'A', then we complete the pattern. So we must avoid that.

        However, note: the pattern is: A, A, (any lowercase), A. So the third character must be a lowercase. Therefore, if we are at state2 (two A's) and then we get an uppercase A, we stay at state2? Actually, we can have a new pattern: the two A's we just got? But the pattern requires the first two to be the same, then a lowercase, then the same. So the two A's we have might be the first two of a new pattern? 

        Actually, we can have overlapping patterns. For example, the string "AAA" might form the start of multiple patterns: 
            Pattern 1: positions 0,1,?,3? But we don't have a lowercase in between. 

        However, the pattern requires a lowercase after the two A's. So if we are at state2 (meaning we have two A's) and we get another uppercase A, we can consider that we now have two A's again (the last two). So we can update the state for 'A':

            state0: if we get an A -> state1
            state1: if we get an A -> state2
            state2: if we get an A -> state2? Because we can start a new pattern? Actually, we can reset: we can use the last two A's as the first two of a new pattern? 

        But note: the pattern does not require contiguous characters. However, the state we are tracking is the longest prefix of the pattern that we have matched. 

        We can use a state machine that allows:

            state0: no progress for letter A.
            state1: one A (which could be the first of the pattern)
            state2: two A's (the first two) — we are waiting for a lowercase.
            state3: two A's and then a lowercase — waiting for the final A.

        Then when we see an uppercase A at state3, we would complete the pattern (so invalid). At state2, if we see an A, we don't advance to state3 because state3 requires a lowercase. But we can update the state for the two A's: we now have two A's (the last two) so we can go to state2 from state1 (if we were in state1, then adding an A goes to state2). However, if we are already in state2, then adding an A: we can still consider that we are in state2? Actually, we can also remain in state2 because the two most recent A's can be used as the first two of a pattern. But we don't want to reset the count of A's? 

        Actually, the state machine should be:

            state0: no A in the current pattern.
            state1: one A (and we are waiting for a second A or a reset by a lowercase? Actually, no: we can have multiple A's and then a lowercase and then an A. The pattern is defined by four characters. The state machine should remember the most recent progress.

        However, the pattern requires the first two to be the same letter, then a lowercase, then the same letter. So if we have three A's in a row, we can use the first two and then later a lowercase and then the third A? Or we can use the last two? 

        Actually, the subsequence does not have to be contiguous. For example, in "AAAb", we can form the pattern if we have a lowercase after the two A's and then an A. But if we have "AAAA", then we can form the pattern in multiple ways: 
            positions 0,1, then we need a lowercase and then an A. But if there's no lowercase in between, then we cannot form the pattern? Actually, we can: 
                positions 0,1, (then we skip the third A because we need a lowercase) and then the fourth A? But we don't have a lowercase between the second and fourth A.

        However, the pattern requires a lowercase in the third position of the pattern. So in "AAAA", we cannot form the pattern because there is no lowercase between the two A's and the final A. 

        But wait: the pattern is defined as four characters: the first two are the same uppercase, the third is any lowercase, and the fourth is the same uppercase. The characters must appear in order. So in "AAAA", we can choose:
            first A: at index0
            second A: at index1
            then we need a lowercase: we don't have one, so skip. Then we see the A at index2: but we cannot use it as the third because the pattern requires the third to be lowercase. Then the A at index3: same. So we cannot form the pattern from "AAAA".

        However, what about using non contiguous A's? For example, in "A...A...a...A", we can form the pattern. But in "AAAA", we don't have a lowercase. So we cannot form the pattern.

        Therefore, the state machine per letter can be:

            state0: no A that can start the pattern for letter A.
            state1: we have one A that can be the first of the pattern.
            state2: we have two A's (so we have the first two) — then we need a lowercase.
            state3: we have two A's and then one lowercase — then we need the final A.

        Then, when we are in state3 and we add an uppercase A, we complete the pattern -> invalid.

        How do we update:

            When we add an uppercase letter 'A':

                state0 -> state1   (because now we have one A)
                state1 -> state2   (because now we have two A's)
                state2 -> state2   (because we already have two A's, and adding another A doesn't change that we have two A's; but also, we can consider that the last A can be used as the start of a new pattern? Actually, we can also update state2 to state1? No, because we have at least two A's. But the state2 means we are waiting for a lowercase to come. We don't reset the state2 on seeing an A? 

                However, we can also start a new pattern from the last A? But the state machine is tracking the longest prefix matched. We want to avoid completing the pattern. We are in state2: we have two A's and then we see a third A: that doesn't help form the pattern because the pattern requires a lowercase after the two A's. But the state2 doesn't require that the two A's are contiguous; it only requires that we have two A's in order. Then we are waiting for a lowercase. The third A doesn't change that. 

                But what if we want to start a new pattern? The pattern requires two A's. The state2 already has two A's. If we see another A, we still have two A's (and we can use the last two as the two A's for a new pattern). However, the state2 already captures that we have two A's. So we don't need to reset. 

                However, consider: we are in state2 (from two A's at positions i and j). Then we see a lowercase: we move to state3. Then we see an A: we complete. 

                But what if after state2 we see an A and then a lowercase? Then we can form the pattern: use the last two A's as the first two, then the lowercase, and then we need an A later. But wait: we don't have an A after the lowercase yet. 

                Actually, the state machine should be updated as follows for an uppercase A:

                    state0: becomes state1.
                    state1: becomes state2.
                    state2: remains state2. (because we have two A's, and now we have a third, so we still have two A's? Actually, no: we have more than two, but the pattern only requires two. So state2 is satisfied: we have at least two A's. So we remain in state2.
                    state3: if we see an A, then we complete the pattern -> invalid. So we avoid this.

                However, what if we are in state3 and we see an A? That would be bad. So we disallow that.

            When we add a lowercase letter:

                state0: remains state0.
                state1: remains state1? Because we haven't completed the second A. But we can use the lowercase as part of the pattern? No, the pattern requires the first two to be A's. A lowercase breaks nothing? But if we are in state2 (two A's) and then we see a lowercase, we move to state3.
                state2: moves to state3.
                state3: remains state3? Because we are already waiting for the final A, and a lowercase doesn't help.

        But wait: after state3, if we get a lowercase, we remain in state3. Then if later we get an A, we complete the pattern? 

        Also, after state3, if we get a lowercase, we are still in state3, and then we can get an A to complete.

        Therefore, the state machine for each letter is:

            state0: no A collected for the pattern for this letter.
            state1: one A collected.
            state2: two A's collected (so we have the first two of the pattern).
            state3: two A's and one lowercase collected (so we are waiting for the final A).

        Transitions for a given letter when we add a character c:

            If c is an uppercase letter, say 'B' (not the current letter, say 'A'):

                Then for letter 'A', the state remains unchanged? Actually, no: because the pattern for 'A' is unaffected by 'B'. So we leave the state for 'A' as is.

            But our state is per letter. So when we add a letter, it only affects the state for that specific letter. For other letters, the state remains the same.

            Therefore, when we add an uppercase letter 'A':

                For letter 'A':
                    state0 -> state1
                    state1 -> state2
                    state2 -> state2   (because we have more than two, but we only need two)
                    state3 -> invalid (so we avoid this)

                For any other letter 'B':
                    the state for 'B' remains unchanged.

            When we add a lowercase letter:

                For every letter:
                    state0 -> state0
                    state1 -> state1
                    state2 -> state3
                    state3 -> state3   (because we are still waiting for the final A, and we've seen a lowercase)

            When we add a '?', then it can be either uppercase or lowercase. So we have to consider both.

        Also, when we add an uppercase letter that is not 'A', say 'B', then for 'B' we update as above for 'A', and for other letters we do nothing? Actually, for the letter 'B', we update:

            state0->state1, state1->state2, state2->state2, and state3->invalid.

        Therefore, the state for each letter is updated independently, except for lowercase which updates all letters.

        But note: when we add a lowercase, it updates the state for every letter that is in state2 to state3. So we need to know, for each letter, what state it is in.

        Therefore, the entire state is a vector of size 4*26: for each letter, we have 4 states. We can represent it as a 2D array: state[26][4]. The total state size is 104 integers per index. Then we iterate over the string (300,000 steps). At each step, we update the state based on the current character.

        How to update efficiently?

            If the current character is a fixed uppercase letter, say 'A' (or 'B', etc.):

                For the specific letter (say index a), we update:
                    new_state[a][0] = 0   # because we are going to update state0 to state1, so we don't keep state0 for this letter.
                    new_state[a][1] = current_state[a][0]   # state0 moves to state1
                    new_state[a][2] = current_state[a][1] + current_state[a][2]   # state1 moves to state2, state2 stays state2? Actually, from state1: we move to state2, and state2 remains state2? 

                    But wait: we are adding one uppercase. For the specific letter, the transitions:

                        state0: goes to state1
                        state1: goes to state2
                        state2: goes to state2
                        state3: goes to invalid -> so we set that to 0 and skip.

                But note: we can also consider that the new character might be used to start a new pattern? Actually, we are updating per letter. The above transitions are for the letter a.

                For other letters, the state remains unchanged? Actually, no: because we added an uppercase letter that is not their letter, so they remain unchanged.

                Therefore, the update for other letters is just copying.

            If the current character is a fixed lowercase letter:

                For every letter:
                    state0: remains state0
                    state1: remains state1
                    state2: moves to state3
                    state3: remains state3

            If the current character is '?':

                Then we consider both uppercase and lowercase, and take the sum.

                But note: '?' can be any of 52 letters. However, we can split:

                    It can be one of 26 uppercase letters or 26 lowercase letters.

                For the uppercase part: we would update as if it were each uppercase letter. But we have 26 possibilities. How to aggregate?

                    We can do:

                        For each letter a in 0..25:
                            We update the state for letter a as if we added an uppercase a, and for the other letters, we leave unchanged? 

                    But then the total would be the sum over the 26 possibilities? 

                    Alternatively, we can note:

                        The uppercase choice: 26 possibilities. Each uppercase letter a will update only the state for letter a (and leave other letters unchanged). And the lowercase choice: 26 possibilities? Actually, no: a lowercase letter doesn't have a letter identity. So one choice for lowercase: it will update all letters in the same way.

                Actually, the lowercase letter is the same for all letters. So we have:

                    Option 1: lowercase: one choice (but there are 26 lowercase letters, but they all act the same: they don't have a letter identity for the pattern, because the pattern only cares about the uppercase letter. So for a lowercase, it advances any letter that is in state2 to state3, regardless of which lowercase it is.

                Therefore, when we have a '?', we have:

                    26 uppercase choices: each uppercase letter a will update only the state for a, and leave others unchanged.

                    26 lowercase choices: which update all letters in the same way.

                So the total for '?' is:

                    new_state = (sum over each uppercase letter a: the state updated as if a was chosen) * 1   [but wait, we have 26 choices, so we must add 26 times the lowercase update?]

                Actually, the count multiplies: 

                    We can do:

                    new_state = 0
                    For each uppercase letter a (26 possibilities):
                        temp_state = current_state
                        update temp_state for letter a as uppercase
                        new_state += temp_state
                    Then add 26 times the update for lowercase.

                But this would be 26*104 operations per step? Then total operations 300,000 * 26 * 104 ≈ 826 million, which is too heavy in Python.

        We need a more efficient update.

        Efficient update for '?':

            Let the current state be a vector for 26 letters, each with 4 states. We'll denote F[a][s] = count for letter a in state s.

            When we add a lowercase (26 possibilities): 
                For each letter a: 
                    new_F[a][0] = F[a][0]
                    new_F[a][1] = F[a][1]
                    new_F[a][2] = 0   ? because we move to state3
                    new_F[a][3] = F[a][2] + F[a][3]

                Actually: 
                    state0: remains -> new_F[a][0] = F[a][0]
                    state1: remains -> new_F[a][1] = F[a][1]
                    state2: moves to state3 -> so we add F[a][2] to state3
                    state3: remains -> so we add F[a][3] to state3? 

                So: 
                    new_F[a][0] = F[a][0]
                    new_F[a][1] = F[a][1]
                    new_F[a][2] = 0
                    new_F[a][3] = F[a][2] + F[a][3]

                And this is the same for all 26 lowercase letters? But we have 26 choices for lowercase. However, the update is independent of the specific lowercase. So if we choose one lowercase, we get the above. Then if we choose any of the 26, we get the same update? Actually, yes. So the total for the lowercase part is 26 * (the above).

            When we add an uppercase letter a0 (one specific letter):

                For letter a0:
                    new_F[a0][0] = 0
                    new_F[a0][1] = F[a0][0]   # state0->state1
                    new_F[a0][2] = F[a0][1] + F[a0][2]   # state1->state2, state2->state2
                    new_F[a0][3] = F[a0][3]   # state3 remains? But if we are in state3 and we add an uppercase a0, that would complete the pattern -> invalid. So we must avoid that. Therefore, we set state3 to 0? Actually, we are not counting invalid strings. So we should not add any possibility that leads to state3 then an uppercase a0.

                Therefore, for state3: we do not allow: so we set new_F[a0][3] = 0? Actually, we are in state3 and then we add an a0: that would form the pattern. So we must not count that.

                How about we do:

                    new_F[a0][1] = F[a0][0] 
                    new_F[a0][2] = F[a0][1] + F[a0][2] 
                    new_F[a0][3] = 0   # because we cannot come from state3: it becomes invalid.

                But wait: what about state3: we are in state3 and we add an uppercase a0: that is invalid, so we set the transition from state3 to 0.

                And state0: becomes state1, so we set new_F[a0][0]=0 and add to state1.

                For other letters a != a0:
                    new_F[a] = F[a]   // unchanged

            Then, for the '?' case, we have:

                total = (sum for each uppercase letter a0 of the state updated for a0) + 26 * (the state updated for lowercase)

            But note: the lowercase update is the same for all 26 choices? And we have 26 choices, so we multiply by 26.

            However, we have to do:

                new_state_upper = 0
                For each a0 in 0..25:
                    temp = current_state  # but we don't want to modify, so we need to compute per a0
                    # For a0: update only the row for a0
                    # For the others, they remain.

                    Actually, we can compute:

                        For the specific letter a0, we update F[a0] as described for uppercase.
                        For other letters, we leave F[a] unchanged.

                    Then the state for uppercase choice a0 is the entire state vector with only a0 updated.

                Then we sum over a0 in 0..25.

                Then add 26 times the state updated for lowercase.

            But note: the lowercase update is applied to the entire state vector at once.

        The state vector has 26*4 = 104 numbers. Then for the uppercase part, we have 26 possibilities, each requiring updating one row (4 numbers). So 26*4 = 104 updates per '?' for the uppercase part? Then total operations per '?' is 104 (for uppercase) + 104 (for lowercase) = 208. Then for the entire string: 300,000 * 208 = 62.4e6, which is acceptable in Python? 

        However, we also have to do the lowercase update separately: the lowercase update requires updating all 26*4 states? Actually, the lowercase update is a fixed transformation: we can do:

            for each letter a:
                new_F[a][0] = F[a][0]
                new_F[a][1] = F[a][1]
                new_F[a][2] = 0
                new_F[a][3] = F[a][2] + F[a][3]

        This is 4*26 = 104 operations.

        Then for the '?':

            Option 1: 
                lowercase_part = 26 * (the state after lowercase update)   # because 26 lowercase choices
                uppercase_part = 0
                For each uppercase letter a0 (26 letters):
                    # make a copy of the current state? But we don't want to copy the entire state (104 numbers) 26 times -> 26*104 = 2704 per step, which for 300,000 steps is 811e6, too heavy.

            How to avoid copying?

        We need to compute:

            uppercase_part = 0
            For each a0, we update only the row for a0 and leave others as the current state. But then we are essentially doing:

                base = current_state   # we don't modify it
                total_upper = 0
                For a0 in 0..25:
                    # We create a new state that is the same as base except for a0 which is updated as:
                    new_state = base   # but we don't want to copy the whole state each time.

            Alternatively, we can compute the uppercase_part without iterating per a0 by aggregating the changes:

            Let F be the current state (26x4 array).

            For the uppercase choices: we have 26 choices. For each choice a0, the new state is:
                For letter a0: 
                    new_F[a0][0] = 0
                    new_F[a0][1] = F[a0][0]
                    new_F[a0][2] = F[a0][1] + F[a0][2]
                    new_F[a0][3] = 0   # because state3 becomes invalid? Actually, no: we set state3 to 0? But what about state3: we cannot have state3 then an uppercase a0? So we set it to 0.

                But wait: what if we are in state3 for letter a0? Then we cannot use that. So we set new_F[a0][3]=0.

                For other letters a != a0: 
                    new_F[a] = F[a]

            Then the entire state for choice a0 is: 
                It's F for all letters except a0, and for a0 we have the updated row.

            Then the sum over a0 is:

                For a given letter a, how many times does it appear as updated? 

                For a fixed letter a, in the sum over a0:
                    - If a0 is not a, then the state for a is taken as F[a] (unchanged) in the state for that a0.
                    - If a0 is a, then the state for a is updated to (0, F[a][0], F[a][1]+F[a][2], 0).

                Therefore, the total for letter a is:
                    unchanged in 25 cases (when a0 != a) and updated in 1 case (when a0 = a).

                    So: 
                    new_F_total[a][0] = 25 * F[a][0] + 0   # from the 25 unchanged and the one updated sets it to 0? But wait: in the unchanged cases, it's F[a][0] and in the updated case, it's 0 for state0.
                    new_F_total[a][1] = 25 * F[a][1] + F[a][0]   # from unchanged: F[a][1] for 25 times, and from the updated: F[a][0] goes to state1.
                    new_F_total[a][2] = 25 * F[a][2] + (F[a][1] + F[a][2])   # unchanged: F[a][2] for 25 times, updated: state2 becomes F[a][1]+F[a][2]
                    new_F_total[a][3] = 25 * F[a][3] + 0   # unchanged: F[a][3] for 25 times, updated: state3 becomes 0.

                But wait: in the updated case, state3 is set to 0. And also, in the updated case, state0 and state3 become 0 and state1 and state2 are updated.

            Actually, the entire state for a specific a0 is a full vector. When we sum over a0, we are adding 26 different state vectors. The state vector for a0 has:
                For a0: updated row: (0, F[a0][0], F[a0][1]+F[a0][2], 0)
                For a != a0: the row F[a]

            Then the total state (sum over a0) is:

                For a fixed letter a:
                    state0: sum_{a0 != a} F[a][0]   (because for a0 != a, we have F[a][0], and for a0=a, we have 0) -> (25 * F[a][0])
                    state1: sum_{a0 != a} F[a][1] + [for a0=a: F[a][0]] -> 25 * F[a][1] + F[a][0]
                    state2: sum_{a0 != a} F[a][2] + [for a0=a: F[a][1]+F[a][2]] -> 25 * F[a][2] + (F[a][1]+F[a][2])
                    state3: sum_{a0 != a} F[a][3] + [for a0=a: 0] -> 25 * F[a][3]

            Then we also have the lowercase part: 26 times the state updated for lowercase.

            Therefore, for '?' we do:

                total_state = uppercase_part + 26 * lowercase_part

            Where:
                uppercase_part[a][0] = 25 * F[a][0]
                uppercase_part[a][1] = 25 * F[a][1] + F[a][0]
                uppercase_part[a][2] = 25 * F[a][2] + (F[a][1] + F[a][2])
                uppercase_part[a][3] = 25 * F[a][3]

                lowercase_part: as above for each letter a:
                    lowercase_part[a][0] = F[a][0]
                    lowercase_part[a][1] = F[a][1]
                    lowercase_part[a][2] = 0
                    lowercase_part[a][3] = F[a][2] + F[a][3]

                Then total_state = uppercase_part + 26 * lowercase_part

            So for each letter a and each state s, we compute:

                new_F[a][0] = uppercase_part[a][0] + 26 * lowercase_part[a][0]
                ... similarly for other states.

            This is 4*26 operations per '?'.

        Therefore, the update per character:

            If fixed uppercase 'A' (which corresponds to a specific letter a0):

                For each letter a:
                    if a == a0:
                        new_F[a][0] = 0
                        new_F[a][1] = F[a][0]
                        new_F[a][2] = F[a][1] + F[a][2]
                        new_F[a][3] = 0   # because state3 then adding a0 is invalid
                    else:
                        new_F[a] = F[a]   // unchanged

                But wait: what about state3? For a0, we set state3 to 0. For other letters, we leave state3 as is. And state0, state1, state2 for a0 are updated.

            If fixed lowercase:
                For each letter a:
                    new_F[a][0] = F[a][0]
                    new_F[a][1] = F[a][1]
                    new_F[a][2] = 0
                    new_F[a][3] = F[a][2] + F[a][3]

            If '?':
                For each letter a, we compute:

                    new0 = 25 * F[a][0] + 26 * F[a][0]   # from uppercase_part and lowercase_part? 
                    But wait, we have:

                        new0 = uppercase_part[a][0] + 26 * lowercase_part[a][0] 
                             = 25*F[a][0] + 26 * F[a][0] 
                             = 51*F[a][0]

                Actually, no: we computed:

                    uppercase_part[a][0] = 25 * F[a][0]
                    lowercase_part[a][0] = F[a][0]   -> then 26 * lowercase_part[a][0] = 26 * F[a][0]

                    so new0 = 25*F[a][0] + 26*F[a][0] = 51*F[a][0]

                Similarly:

                    new1 = (25 * F[a][1] + F[a][0]) + 26 * F[a][1] 
                          = 25*F[a][1] + F[a][0] + 26*F[a][1] 
                          = 51*F[a][1] + F[a][0]

                    new2 = (25 * F[a][2] + F[a][1] + F[a][2]) + 26 * 0 
                          = 26*F[a][2] + F[a][1]

                    new3 = (25 * F[a][3]) + 26 * (F[a][2] + F[a][3])
                          = 25*F[a][3] + 26*F[a][3] + 26*F[a][2]
                          = 51*F[a][3] + 26*F[a][2]

                But wait, is that correct? 

                Alternatively, we can derive without splitting:

                    The '?' has two options: uppercase or lowercase. The uppercase option: 26 possibilities, each updating one specific letter. The lowercase option: 26 possibilities (any lowercase letter) but all have the same effect.

                    Then the total is:

                        new_state = 0
                        new_state += 26 * (lowercase_updated_state)   # because 26 lowercase choices
                        new_state += (sum over a0 in 0..25 of the state updated for uppercase a0)   # 26 uppercase choices, each one specific letter.

                    Then for a fixed letter a:

                        new_F[a][0] = 26 * (F[a][0])   [from lowercase] + (25 * F[a][0] + 0) [from uppercase: when the uppercase choice is not a] + (0) [when the uppercase choice is a: because then we set state0 to 0] 
                                    = 26 * F[a][0] + 25 * F[a][0] 
                                    = 51 * F[a][0]

                        new_F[a][1] = 26 * (F[a][1]) + (25 * F[a][1] + F[a][0]) 
                                    = 26*F[a][1] + 25*F[a][1] + F[a][0] 
                                    = 51*F[a][1] + F[a][0]

                        new_F[a][2] = 26 * 0 + (25 * F[a][2] + (F[a][1] + F[a][2])) 
                                    = 0 + 25*F[a][2] + F[a][1] + F[a][2] 
                                    = 26*F[a][2] + F[a][1]

                        new_F[a][3] = 26 * (F[a][2]+F[a][3]) + (25 * F[a][3] + 0) 
                                    = 26*F[a][2] + 26*F[a][3] + 25*F[a][3] 
                                    = 26*F[a][2] + 51*F[a][3]

                This matches.

        Therefore, we can handle:

            fixed uppercase: update the specific letter a0: 
                new0 = 0
                new1 = F0
                new2 = F1 + F2
                new3 = 0

            fixed lowercase: update all letters:
                new0 = F0
                new1 = F1
                new2 = 0
                new3 = F2 + F3

            for '?': update all letters:
                new0 = 51 * F0
                new1 = 51 * F1 + F0
                new2 = 26 * F2 + F1   # note: 26*F2 + F1? But from above: 26*F[a][2] + F[a][1] -> yes.
                new3 = 51 * F3 + 26 * F2

        But wait: for the uppercase part of '?', we did not set state3 to 0 for the updated letter? Actually, in the aggregated update we did: for the specific letter a0, we set state3 to 0. And in the sum over a0, we ended up with 25 * F[a][3] for letter a? Then we added the lowercase part. So the above formulas are correct.

        Initial state:

            Before processing any characters, we start at state0 for all letters. So we have a state vector F of size 26x4, with F[a][0] = 1 for all a? 

            But wait: we haven't seen any character. How many patterns have we matched? None. So we are at state0 for every letter. But the state0 for a letter means we haven't started the pattern for that letter. 

            However, the entire state should be one. How do we represent the state?

            Actually, the state of the entire string is described by a vector of counts: for each letter and each stage, the number of ways to be in that stage for that letter.

            But initially, we have one empty string. Then:

                For every letter, we are at stage0. 

            However, we have 26 letters. How do we represent the initial state? 

            We can have a 2D array F[26][4] initialized to 0. Then for each letter, F[a][0] = 1? 

            But then the total state would have 26 * 1 = 26 at stage0? That doesn't sound right because we have one string.

            Actually, the state is over the entire string. The condition for a letter is independent. We are counting the number of ways. The initial state: there is one way to have not started any pattern for any letter. But we have 26 letters, and for each letter we are at state0. So we can store one value per state per letter.

            However, the entire state is the product over letters? Actually, no: the state for different letters are not independent. But we are storing a vector of 26*4. The total count is the entire vector? How do we combine?

            Actually, the state is: the string so far has not completed any pattern (i.e., no letter has reached state4). And the state for each letter is the progress of the pattern for that letter. 

            The entire state is the product of the states for the letters? No, because the same character may be part of patterns for multiple letters. Actually, we are storing the entire vector: F[a][s] means that for the letter a, we are at stage s. But the state of the entire system is the vector for all a. 

            However, we are not storing a separate state per letter as independent; we are storing the entire state as a 26x4 array. The count is the number of ways to have that particular assignment of states to letters. 

            But initially, we have one string. And for every letter, we are at state0. So we set F[a][0] = 1 for every a? But then the entire state would have 26 ones? That doesn't add up to one.

            This indicates a flaw: we are tracking the state per letter independently, but the initial state should be one. 

            How about we store the state as a vector of length 4 for the entire system? But then we lose the letter identity.

        Alternative: we don't need per-letter state. We can aggregate by stage per letter? But the stages for different letters are not additive.

        Actually, we can note: the pattern for a specific letter is independent. The only interaction is that a lowercase letter advances all letters that are in state2. 

        How to represent the state without 26x4? 

            Let F0 = the number of letters that are in state0? No, because the states for different letters are not additive.

            We need to know, for each letter, the stage it is in? 

        But note: the initial state: we haven't seen any character. Then for every letter, the state is 0. So we can represent the state as:

            count0 = 1   # meaning: we have one global state where every letter is at state0.

        But then when we add a character, we update the state per letter. The state space is 4^26? which is too big.

        Therefore, we must store per-letter states. But the initial state: we have one string. How?

            We can store an array dp[0..3] for each letter, but the entire state is a product state. However, the total state is the combination of the states for each letter. But we don't need the correlation between letters? 

            Actually, the update for a character only affects one letter (if uppercase) or all letters (if lowercase). So the state is the product over letters? 

            Then we can store: 

                F = [ f0, f1, f2, f3 ] for each letter? 

            But the state for the entire system is: for each letter, we have a distribution over states. And the total count is the product over letters? 

            However, the states for different letters are not independent: when we add a lowercase, it updates all letters simultaneously. 

            Therefore, we must store the entire joint state? 

        Actually, we can factorize? 

            The state for letter a is independent of the state for letter b? 

            But they are updated simultaneously by a lowercase. So they are not independent. 

            However, the update for a lowercase is applied uniformly: if we know the number of ways to have a particular state for letter a, and similarly for letter b, and the updates are independent, then we can store the product.

            Specifically, the entire system state is the product over letters of the state for that letter. And the initial state is: for each letter, the state is (1,0,0,0). Then the entire state is the product, so the total count is 1.

            Then when we add a lowercase, we update each letter independently: 

                For letter a: 
                    (f0, f1, f2, f3) -> (f0, f1, 0, f2+f3)

            Then the entire state remains the product over letters. 

            Similarly, when we add an uppercase letter a0, we update only letter a0: 

                (f0, f1, f2, f3) for a0 becomes (0, f0, f1+f2, 0)   # and we set state3 to 0 to avoid completion.

            And for other letters, we do nothing.

            Then the entire state is the product of the states for each letter.

            Therefore, we can store for each letter a state vector of length 4: (f0, f1, f2, f3). And the total state is the product of the per-letter vectors? 

            But the entire state is the product over letters of (f0[a] + f1[a] + f2[a] + f3[a])? Actually, no: the entire state is the product of the per-letter state vectors. The count for the entire system is the product of the counts for each letter? 

            Actually, no: the state is the combination of independent per-letter states. The total number of ways is the product of the per-letter state vectors? 

            But then the initial state: for each letter, we have (1,0,0,0). Then the entire state is the product over a of 1 = 1. 

            Then when we add a character, we update one letter (for uppercase) or all letters (for lowercase) independently.

            However, for a fixed string, the state for each letter is determined. The entire state is the product of the per-letter states. 

            Therefore, we can store:

                total_state = 1   # which is the product over letters of (f0[a]+f1[a]+f2[a]+f3[a])? 

            But we need to update per-letter states.

            Actually, we store for each letter a vector (f0, f1, f2, f3) and the entire system's count is the product over a of (f0[a]+f1[a]+f2[a]+f3[a])? But that is not true: the states for different letters are not independent in the product. 

            Let me clarify: 

                We are counting the number of strings. The state for letter a is the progress of the pattern for a. The entire system's state is described by the progress for every letter. 

                We want to count the number of ways to have a global state. And the global state is the combination of the per-letter states. 

                The transitions: 

                  - For a lowercase: we update every letter independently: so we apply the same transformation to each letter.

                  - For an uppercase a0: we update only letter a0.

                And the updates are linear.

            Therefore, the entire state is the product over letters of the per-letter state vector. But the per-letter state vector is a vector of four numbers (which are counts). The entire state is then a 26-dimensional tensor? 

            And the total count is the sum over the tensor? 

            But we don't want to store a 26-dimensional tensor.

        Alternatively, because the letters are independent, we can store the counts for each letter independently. But then the state is a vector of length 4 for each letter, and the entire system is the product of the per-letter states. 

        However, the initial state: we have one state. And the per-letter states are (1,0,0,0) for every letter. So we store:

            For each letter a: 
                state_a = [1, 0, 0, 0]

            Then the entire count is the product over a of (state_a[0]+state_a[1]+state_a[2]+state_a[3])? 
            But wait: no. The entire state is not the product of the sums; it is the product of the state vectors. But actually, the state for each letter is a vector, and the combined state is the product. The total count is the product over a of (1) = 1.

        When we add a character, we update one letter or all letters. After the update, the entire state is the product of the updated per-letter states.

        But how do we update efficiently without storing 26 vectors? 

            We can store an array of size 4 for each letter. That's 26*4=104 numbers. And then the total count is not stored; we only care about the state for the next step. 

            And at the end, the total number of valid strings is the sum over all the per-letter state vectors? 

            Actually, no: the entire state is the product of the per-letter states. The total count is the product over letters of (f0[a]+f1[a]+f2[a]+f3[a]). 

            But that is not correct: because the state is the combined state of all letters, and the count is the product of the per-letter state vectors. However, the state for a letter is a vector of four nonnegative integers. How do we combine?

            Let me consider two letters: A and B.

            State for A: [A0, A1, A2, A3]
            State for B: [B0, B1, B2, B3]

            The combined state: we are in state i for A and state j for B, and the count for that combined state is A_i * B_j.

            The total count is then (A0+A1+A2+A3) * (B0+B1+B2+B3). 

            Therefore, the entire system's total count is the product over letters of (sum of the state vector for that letter).

            But then we lose the information per state. 

            However, we don't care about the combined state per se. We care about the update: 

                When we add a lowercase, we update each letter independently. So for each letter, we transform its state vector:

                    new_state = M_lower * old_state

                where M_lower is the 4x4 matrix:

                    [1, 0, 0, 0]
                    [0, 1, 0, 0]
                    [0, 0, 0, 1]   # wait, state2 becomes state3? Actually, state2 moves to state3, and state3 remains state3? 
                    [0, 0, 1, 1]   -> no, we did:

                    state0: remains
                    state1: remains
                    state2: becomes state3
                    state3: remains and also receives state2.

                So the matrix is:

                    s0: [1,0,0,0]   -> s0
                    s1: [0,1,0,0]   -> s1
                    s2: [0,0,0,1]   -> s3? 
                    s3: [0,0,0,1]   -> s3, but also s2 moves to s3: so for state3, we add the count from state2.

                Actually, for state3 in the new state, we have the sum of the old state2 and state3.

                So the matrix for a letter is:

                    new0 = old0
                    new1 = old1
                    new2 = 0
                    new3 = old2 + old3

                So matrix M_lower:

                    [1,0,0,0]
                    [0,1,0,0]
                    [0,0,0,0]   # for state2 becomes 0? 
                    [0,0,1,1]

                But then the new state3 = 0*old0 + 0*old1 + 1*old2 + 1*old3.

            For an uppercase letter a0, we update only letter a0 with matrix M_upper:

                    new0 = 0
                    new1 = old0
                    new2 = old1 + old2
                    new3 = 0

                So matrix M_upper for letter a0:

                    [0,0,0,0]
                    [1,0,0,0]   # new1 = old0
                    [0,1,1,0]   # new2 = old1+old2
                    [0,0,0,0]

            For other letters, we use the identity matrix.

            Then for a '?' for a given letter a, we have 26 choices for lowercase and 26 choices for uppercase. But the uppercase choice is for a specific letter a0, and only affects letter a0. The lowercase choice is for any lowercase letter, and affects all letters. 

            However, the problem: the uppercase choice is not for a specific letter a0 and then update that letter, but the character is one specific letter a0, and then it updates only that letter. And there are 26 choices.

            So for the entire system, when we add a lowercase, we do:

                new_state = (M_lower applied to every letter) * 26   # because there are 26 choices

            When we add an uppercase, we do for each choice a0:

                new_state = (apply M_upper to letter a0 and identity to others) 

            Then the total for '?' is:

                26 * (apply M_lower to every letter) + sum_{a0} (apply M_upper to letter a0 and identity to others)

            Because the entire state is the product over letters, the application of M_lower to every letter is simply: for each letter, we multiply its state vector by M_lower, and then the total state is the product. 

            But wait, how do we compute "apply M_lower to every letter"? 

                Since the state is the product over letters, applying M_lower to every letter means:

                    new_total = product_{a} (M_lower * v_a)

                where v_a is the state vector for letter a.

                And similarly, for the uppercase part, for a fixed a0:

                    new_total = [ (M_upper * v_{a0}) ] * [ product_{a != a0} v_a ]

            But then the entire state is a scalar: the total count is the product over a of (sum of the components of the state vector for a). 

            However, we also want to avoid state4 (which we do by not allowing the transition from state3 to state4). But we already set state3 to 0 in the uppercase update for the specific letter.

            And at the end, we want the total count.

            Therefore, we can store: 

                total_count = 1   # initially for the empty string? 

            But we also need to store the state per letter? 

            Alternatively, because the letters are independent, we can store for each letter a state vector v_a of length 4, and the entire system's count is the product over a of (v_a[0]+v_a[1]+v_a[2]+v_a[3]). 

            However, the updates are not on the sum, but on the vector. 

            And the catch: when we update one letter, the entire system's count is not simply the product of the sums, because the state for the updated letter is a vector, and the others are vectors. 

            Specifically, after a letter a0 is updated by M_upper, its new state vector is w = M_upper * v_{a0}. 
            Then the entire system's count is:

                (w[0]+w[1]+w[2]+w[3]) * product_{a!=a0} (v_a[0]+v_a[1]+v_a[2]+v_a[3])

            But wait, this is not the same as the product of the sums. This is because the state for the entire system is the product of the per-letter state vectors, but then we aggregate over the states. And the count is the sum_{i0,i1,...,i25} product_{a} (v_a[i_a]) = product_{a} (sum_i v_a[i]). 

            So the entire system's count is the product over letters a of (sum_{s=0..3} v_a[s]). 

            Therefore, we only need to store for each letter a the sum s_a = v_a[0]+v_a[1]+v_a[2]+v_a[3]. 

            But then we lose the vector and we cannot update. 

        This indicates that the state per letter is not independent in the sum, but in the vector. And the updates are linear transformations on the vectors. 

        Therefore, we must store for each letter a the vector v_a = [c0, c1, c2, c3]. And the entire system's count is the product over a of (c0+c1+c2+c3). 

        But then we don't need the vector, only the sum? 

        However, when we update, we need the vector for the letter to compute the new vector. 

        So we must store the vector for each letter. The state is 26 vectors of length 4. Total 104 numbers.

        Initialization:

            v_a = [1,0,0,0] for every letter a.

        Then the initial count = product_{a} (1+0+0+0) = 1.

        Update for a fixed uppercase letter a0:

            v_{a0} = [0, v_{a0}[0], v_{a0}[1]+v_{a0}[2], 0]
            other letters unchanged.

            Then the entire system's count = (0 + v_{a0}[0] + (v_{a0}[1]+v_{a0}[2]) + 0) * (product_{a!=a0} (sum of v_a)) 

            But wait, we are not using the product of the sums. We are storing the vectors to use in future updates.

        We will update the vector for a0 and leave others unchanged.

        Then for the next step, we have the new vectors.

        Similarly, for a fixed lowercase letter:

            for every letter a:
                v_a = [v_a[0], v_a[1], 0, v_a[2]+v_a[3]]

        Then for '?':

            new_vectors = 26 * (vectors after lowercase update) + (sum_{a0} (vectors after uppercase update for a0))

            How to compute this without iterating over a0 explicitly for the entire vector of 104 numbers? 

            We already did: per-letter updates. For each letter a, we can compute the new vector for a from its own vector.

            For a fixed letter a, its new vector in the '?' case is:

                lowercase_part = [v_a[0], v_a[1], 0, v_a[2]+v_a[3]]
                uppercase_part = for the specific choice a0 = a: [0, v_a[0], v_a[1]+v_a[2], 0]
                and for choices a0 != a: unchanged = v_a

                Then the total for letter a is:
                   = 26 * lowercase_part + ( unchanged * 25 + uppercase_part ) 
                   = 26*[v0, v1, 0, v2+v3] + 25*[v0, v1, v2, v3] + [0, v0, v1+v2, 0]

                = [26*v0 + 25*v0, 26*v1 + 25*v1 + v0, 26*0 + 25*v2 + (v1+v2), 26*(v2+v3) + 25*v3] 
                = [ (26+25)*v0, (26+25)*v1 + v0, 25*v2 + v1+v2, 26*v2+26*v3+25*v3]
                = [51*v0, 51*v1+v0, 26*v2+v1, 26*v2+51*v3]   [wait: 25*v2 + v1+v2 = 26*v2+v1]

            Therefore, for each letter a, the new vector is:

                w0 = 51 * v0
                w1 = 51 * v1 + v0
                w2 = 26 * v2 + v1
                w3 = 51 * v3 + 26 * v2

        So we can update each letter independently.

        Therefore, the algorithm:

            Let v = a list of 26 vectors, each vector is [1,0,0,0]

            MOD = 998244353

            For each char c in S:

                if c is uppercase letter: 
                    a0 = index of the letter (0..25)
                    for each letter a in range(26):
                        if a == a0:
                            # update: new_v = [0, v0, v1+v2, 0]
                            v0, v1, v2, v3 = v[a0]
                            v[a0] = [0, v0, (v1+v2) % MOD, 0]
                        else:
                            # unchanged
                            pass

                if c is lowercase letter:
                    for each letter a in range(26):
                        v0, v1, v2, v3 = v[a]
                        v[a] = [v0, v1, 0, (v2+v3) % MOD]

                if c == '?':
                    for each letter a in range(26):
                        v0, v1, v2, v3 = v[a]
                        w0 = 51 * v0 % MOD
                        w1 = (51 * v1 + v0) % MOD
                        w2 = (26 * v2 + v1) % MOD
                        w3 = (51 * v3 + 26 * v2) % MOD
                        v[a] = [w0, w1, w2, w3]

            After processing all characters, the total count = product_{a} (v0[a]+v1[a]+v2[a]+v3[a]) % MOD

        But is that correct?

        Let me test on a small example.

        Example: "D??S" is not provided, but the example "DD??S" output is 676.

        We are given: "DD??S"

        Alternatively, let's try a very short string: "A" of length 1.

            Initially: for each letter, [1,0,0,0]

            'A' is uppercase, letter 'A'=0.

            Update for 'A': 
                For letter0 (='A'): becomes [0, 1, 0, 0]  (since v0=1, so new1=1; v1=0, v2=0, so new2=0)
                For other letters: unchanged: [1,0,0,0]

            Then the total count = (0+1+0+0) * product_{a!=0} (1+0+0+0) = 1 * 1 = 1? 

            But there is one string.

        Now, "A?":

            First char 'A': then state for 'A' = [0,1,0,0], others=[1,0,0,0]

            Then '?':

                For letter0 (='A'): 
                    v = [0,1,0,0]
                    w0 = 51*0 = 0
                    w1 = 51*1 + 0 = 51
                    w2 = 26*0 + 1 = 1
                    w3 = 51*0 + 26*0 = 0
                    new_v = [0,51,1,0]

                For other letters a (25 letters), each has [1,0,0,0]:
                    w0 =

## Final Solution

