# Solution for abc304_e.md

Generated at: 2025-07-22 06:22:10
Model: deepseek-reasoner

## Reasoning Process

Alright, I'm trying to solve this problem. Let me start by understanding what it's asking.

We have an undirected graph G with N vertices and M edges. The graph is considered "good" if for a given set of K pairs (x_i, y_i), there is no path between x_i and y_i. The given graph is already good. Then, we have Q queries. In each query, we add an edge between p_i and q_i, and we need to check if the resulting graph is still good.

Constraints are high: N, M, K, Q up to 200,000. So, a brute-force approach for each query (like running a BFS/DFS for each pair) would be too slow. I need an efficient solution.

First, I note that the graph G might have multiple edges and self-loops. The example input has a self-loop (5 5) and a duplicate edge (2 3 twice). So, I should handle that.

The condition for being good is that none of the K pairs (x_i, y_i) are connected. After adding an edge (p, q), the graph might become "bad" if at least one of these pairs becomes connected. However, it's possible that adding an edge connects two components that were previously disconnected, which might cause one or more of the forbidden pairs to become connected.

But note: the initial graph is good, meaning all these forbidden pairs are disconnected. Adding an edge can only connect some vertices that were in the same component or merge two components. Therefore, after adding an edge (p, q), the only pairs that might become connected are those that were in the same connected component as p and q? Actually, no: if p and q are in different components, adding the edge merges those two components. Then, any forbidden pair (x_i, y_i) that has x_i in the first component and y_i in the second component would become connected.

So, the idea is: for each forbidden pair (x_i, y_i), they must remain in different connected components even after adding the new edge. Therefore, if adding the edge (p_i, q_i) would merge two components, we must check that for every forbidden pair that has one vertex in the first component and the other in the second, there isn't any such pair. But wait, the problem states that initially, all forbidden pairs are disconnected. After adding the edge, the only pairs that become connected are those that were split by the same component boundary that the edge is bridging.

But how do we know which forbidden pairs are between two specific components?

I recall that in graph theory, when we have a graph and we want to check connectivity, we can use union-find or BFS to get connected components. But with 200,000 queries, we need a faster method.

Alternative approach: we can precompute the connected components of the initial graph. Let each component have a representative (like a root) or a unique label.

Then, for each forbidden pair (x_i, y_i), they are in different components. We can represent this pair as (c_x, c_y) where c_x is the component of x_i and c_y is the component of y_i. Since the graph is good initially, we know that for each such pair, the two components are distinct.

Now, when we add an edge between p and q, if p and q are already in the same component, then adding the edge doesn't change the connectivity (since it's an undirected graph, and even if it's a multi-edge, it doesn't merge components). So, the graph remains as it was, and since initially, no forbidden pair was connected, they still aren't. Therefore, the graph remains good. So, the answer is "Yes" in this case.

But if p and q are in different components, say component A and B, then adding the edge merges A and B into a single component. Then, any forbidden pair (x_i, y_i) that has one in A and the other in B would now become connected. So, if there exists at least one forbidden pair that has one vertex in A and the other in B, then after adding the edge, that pair becomes connected, and the graph is no longer good. Thus, the answer is "No". Otherwise, if no such forbidden pair exists between A and B, then even after merging, none of the forbidden pairs become connected, so the graph remains good, and we output "Yes".

So, the problem reduces to:

1. Preprocess the connected components of the initial graph (using union-find or DFS/BFS, but with N and M up to 200,000, DFS/BFS is O(N+M) which is acceptable, but union-find might be more efficient for dynamic merging? However, we are not going to change the graph; we are just building the initial graph. So both are fine. But since we are going to use the components for many queries, we can use union-find to build the components and then label each node.

2. Label each node with its component id (root in union-find or a unique id for the component).

3. For each forbidden pair (x_i, y_i), we note that they are in different components. We can represent this pair as an unordered pair (min(c_x, c_y), max(c_y, c_x))? Actually, we want to know: are there any forbidden pairs between two components? So, we can create a set (or a hash table) of pairs of components that are "forbidden". That is, for a pair (x, y), we have (c_x, c_y) (with c_x != c_y). But to avoid duplicates, we can store the pair in a canonical order (smaller component id first, then larger).

But note: the problem states that for i≠j, the pairs {x_i, y_i} are distinct. However, multiple forbidden pairs might be between the same two components. For example, two different pairs (x1, y1) and (x2, y2) might both have x1 in component A and y1 in component B, and similarly for x2, y2. So, we don't need to store each pair individually; we just need to know that there is at least one forbidden pair between A and B? Actually, no: we need to know that there is at least one forbidden pair between the two components that are being merged. So, if we store for each unordered pair of components (A, B) whether there is a forbidden pair between them, that would work.

But the number of distinct component pairs from the forbidden pairs is at most K, which is 200,000. So, we can create a set (or a mapping) of component pairs.

However, when we add an edge between p and q, if they are in components A and B (A != B), then we check if the pair (min(A, B), max(A, B)) exists in the set of forbidden component pairs. If it does, then after merging, at least one forbidden pair becomes connected, so the graph becomes bad. Otherwise, it remains good.

But wait: what if the forbidden pair is between components A and C, and we are merging A and B? Then, the pair (A, C) is still separate. Only pairs that are exactly between A and B would be affected.

Therefore, the algorithm:

1. Preprocess the connected components:

   - Use union-find (disjoint set union, DSU) to compute the connected components of the initial graph. We can ignore duplicate edges and self-loops? Actually, self-loops don't connect anything new, and duplicate edges are redundant for connectivity. So, we can skip duplicate edges and self-loops when building the graph for DSU. But the input has them. We can just add an edge only if it connects two distinct nodes and we haven't merged them yet? Actually, DSU can handle duplicate edges by ignoring them (if we do union-find with union by rank, adding the same edge twice is harmless because the second time, the two nodes are already in the same component, so we skip).

   Steps for DSU:

   - Initialize parent[i] = i, rank[i]=0 for each node.
   - For each edge (u, v), if u != v, do union(u, v) (if they are different, merge; if same, skip).

   Then, for each node, we can get its component root (by find). But note: the root might not be a consistent label? Because after path compression, the root is consistent per component, but the root value might be arbitrary. We can relabel the components so that each component has a unique id. Alternatively, we can use the root of the component as the representative.

   However, after union-find, we can traverse all nodes and assign a component id: for each node, we find its root, and then we can map that root to a new id (like a consecutive integer). But actually, for our purpose, we don't need consecutive integers. We can use the root itself. But the root is an integer between 1 and N. However, multiple roots might be present? Actually, the root of a component is a node id. But if we do path compression, then the root for all nodes in the same component is the same.

   So, step 1: run DSU to get the root for each node.

2. Preprocess the forbidden component pairs:

   - Create an empty set `forbidden_set`.
   - For each forbidden pair (x, y) in the list `pairs`:

        root_x = find(x)
        root_y = find(y)

        Since the graph is good, root_x != root_y. Then, we create a canonical representation for the pair of components: (min(root_x, root_y), max(root_x, root_y)). Then, we add this tuple to `forbidden_set`.

   But note: we don't need to store every pair? Actually, we just need to know for a given pair of components (A, B) whether there is at least one forbidden pair between them. So, storing each distinct pair (A, B) is enough? But wait: what if multiple pairs between the same two components? We don't need to store duplicates. So, we can store each distinct (min, max) pair of roots. But actually, we are storing each distinct pair of components that has at least one forbidden edge. However, the problem says that the forbidden pairs are distinct by vertex pairs, but multiple vertex pairs can be between the same two components. So, we don't want to store the same component pair multiple times. But if we use a set, then duplicate component pairs are stored only once.

   However, the problem doesn't require knowing how many, just whether there is at least one. So, we can use a set of tuples (min_id, max_id).

   But note: the set size is at most K, which is 200,000. So, it's acceptable.

3. Process each query:

   For each query (p, q):

      root_p = find(p)
      root_q = find(q)

      If root_p == root_q: then adding the edge doesn't change the components. So, the graph remains good -> "Yes".

      Else:

          Let A = min(root_p, root_q), B = max(root_p, root_q). Then, check if (A, B) is in the forbidden_set.

          If it is, then adding the edge would merge A and B, causing at least one forbidden pair to become connected -> "No".

          Else, "Yes".

But wait: what if the forbidden_set has multiple pairs between A and B? We don't care; we only care if there is at least one.

This seems to work.

But let me test with the example.

Example input:

N=6, M=6
Edges:
1-2, 2-3, 2-3 (duplicate), 3-1 (cycle), 5-4, 5-5 (self-loop)

K=3
Pairs: (1,5), (2,6), (4,3)

Q=4
Queries: (2,5), (2,6), (5,6), (5,4)

First, build the initial graph. Ignore the duplicate edge (2,3) and self-loop (5,5). So edges: (1,2), (2,3), (3,1) -> forms a triangle (component A: {1,2,3}). Then (5,4) -> component B: {4,5}. And vertex 6? It's not connected to any edge? So it's a singleton: component C: {6}.

Wait, the edges: 
1-2, 2-3, 2-3 (duplicate), 3-1, 5-4, 5-5.

So, the graph has:
- Component 1: nodes 1,2,3 (because 1-2, 2-3, 3-1: all connected)
- Component 2: nodes 4,5 (edge 5-4; note: 5-5 is a self-loop and doesn't connect anything else)
- Component 3: node 6 (no edges)

Now, the forbidden pairs:

(1,5): between comp1 and comp2 -> store (min(comp1_id, comp2_id), max(...)) -> (id1, id2) where id1 and id2 are the roots? But what are the roots? Let me assign:

Assume we run DSU:

Initialize: each node is its own parent.

Then process edges:

1-2: union(1,2): let's set parent[2]=1 (or 1's parent becomes 2? Doesn't matter as long as consistent). Assume we use union by rank and path compression, but for simplicity, let's assign:

After processing all edges:

- Nodes 1,2,3: all have the same root, say 1 (if we union 1-2: root1 becomes parent of 2; then 2-3: when processing 2-3, find(2)=1, find(3)=3, so union(1,3) -> set parent[3]=1.

- Nodes 4,5: union(4,5): say set parent[5]=4 or vice versa. Let's say root is 4.

- Node 6: remains 6.

So, component roots: 
1: for nodes 1,2,3
4: for nodes 4,5
6: for node 6.

Forbidden pairs:

(1,5): node1 is in comp1 (root=1), node5 is in comp2 (root=4). So, store (min(1,4), max(1,4)) = (1,4).

(2,6): node2 in comp1 (root=1), node6 in comp3 (root=6). Store (1,6).

(4,3): node4 in comp2 (root=4), node3 in comp1 (root=1). Store (1,4) again? But that's the same as the first one.

But the problem says: for i≠j, the vertex pairs are distinct. So (1,5) and (4,3) are different vertex pairs, but they are between the same two components (1 and 4). So in our set, we only store (1,4) once? Actually, we are adding the tuple (1,4) twice to the set. But a set in Python will only store one instance. So the set will have {(1,4), (1,6)}.

Now, process queries:

1. (2,5): 
   - Find(2): root=1
   - Find(5): root=4
   - They are different. Then, check if (min(1,4)=1,4) is in forbidden_set? Yes, (1,4) is there. So output "No". Matches example.

2. (2,6):
   - Find(2)=1, Find(6)=6 -> different. Check (1,6) in forbidden_set? Yes. So "No".

3. (5,6):
   - Find(5)=4, Find(6)=6 -> different. Check (min(4,6)=4, max=6) -> (4,6). Is (4,6) in forbidden_set? Currently, we have (1,4) and (1,6). So (4,6) is not present. Therefore, output "Yes". Matches.

4. (5,4):
   - Find(5)=4, Find(4)=4 -> same. So output "Yes". Matches.

But in the example, the fourth query is (5,4). In the input, there is already an edge (5,4). But the graph might have multi-edges? The problem says: the graph G^{(i)} is obtained by adding an edge (p_i, q_i). So if we add (5,4) to the graph that already has (5,4), then we are adding a duplicate. But as per the algorithm, we check the roots: 5 and 4 are already in the same component (root=4 for both). So we output "Yes".

But what if we add an edge that is a self-loop? For example, (5,5). Then, we check: p=q? But the problem states p_i != q_i, so no self-loops in queries.

So the algorithm seems correct.

But what if the DSU uses union by rank and path compression? Then, the root might change over time? Actually, no: we are not going to change the graph after building the initial components. The queries are independent, and we are not actually adding the edges. We are only checking what would happen if we add the edge. So we don't update the DSU. Therefore, we run DSU once at the beginning to get the initial components, and then we don't change it. Then, for each query, we just do two find operations (which are O(α(N)) and a set lookup.

But note: the set `forbidden_set` is built from the initial components. We store the component pairs for the forbidden pairs in their initial state.

Now, time complexity:

- DSU: O(M * α(N)) which is acceptable for M, N up to 200,000.
- Building forbidden_set: O(K * α(N)) (because for each forbidden pair, we do two finds) and then insert into a set (O(1) per insertion, so O(K)).
- Then, each query: two finds and a set lookup: O(α(N)) per query, so total O(Q * α(N)).

Overall: O((M + K + Q) * α(N)), which is acceptable.

Space: O(N) for DSU, O(K) for the set.

But note: the set stores at most K entries? Actually, at most min(K, N*(N-1)/2) but in worst-case, K=200,000, so it's acceptable.

Edge cases:

- What if a forbidden pair (x_i, y_i) has x_i and y_i in the same component initially? But the problem states that the graph is good initially, so there is no path. So they must be in different components. So we don't have to worry about that.

- What if the graph is empty? Then all nodes are singletons.

- What if there are no forbidden pairs? Then K=0, so forbidden_set is empty. Then, for any query that merges two components, since the set is empty, (A,B) won't be in the set, so we output "Yes". That makes sense: if there are no restrictions, then adding any edge doesn't break the "good" property.

- What if we add an edge that connects more than two components? Actually, no: each edge only connects two components. So we are only merging two components per edge.

But note: if there are multiple edges in the queries that are independent, we are only considering one edge at a time. The queries are independent: we start from the initial graph and add one edge.

So the code:

Steps:

1. Initialize DSU (size N+1, since nodes are 1-indexed).

2. Process M edges: for each edge (u, v), if u != v, then union(u, v). But even if u==v (self-loop), skip.

3. Build an array `root` for each node: for i from 1 to N, root[i] = find(i). But actually, we don't need to store them? We can compute on the fly.

   Alternatively, we can store the root for each node in an array to avoid calling find repeatedly? But in the DSU, after building, we can compress paths so that each find is O(1) amortized. So we can just call find when needed.

4. Build the forbidden_set:

   ```
   forbidden_set = set()
   for each pair (x, y) in pairs:
        rx = find(x)
        ry = find(y)
        # since they must be different, but if by mistake same? The problem says no path, so they are in different components. But in code, we can skip if same? Actually, the problem states they are disconnected, so rx != ry. But if rx==ry, that would be an error? Or we can still add? But then we would have a pair (rx, rx) which we don't store? Actually, we store (min, max). If rx==ry, then min==max. But the problem says they are disconnected, so rx != ry. So we don't have to worry.

        if rx != ry:
            key = (min(rx, ry), max(rx, ry))
            forbidden_set.add(key)
   ```

5. For each query (p, q):

        rp = find(p)
        rq = find(q)
        if rp == rq:
            ans.append("Yes")
        else:
            key = (min(rp, rq), max(rp, rq))
            if key in forbidden_set:
                ans.append("No")
            else:
                ans.append("Yes")

But note: the DSU structure must not be modified after the initial building. So we are doing only find operations in the queries, which don't modify the DSU.

Implementation of DSU:

We'll implement DSU with path compression and union by rank for efficiency.

Steps for DSU:

   parent = list(range(N+1))
   rank = [0]*(N+1)

   def find(x):
        if parent[x] != x:
            parent[x] = find(parent[x])
        return parent[x]

   def union(x, y):
        rx = find(x)
        ry = find(y)
        if rx == ry:
            return
        if rank[rx] < rank[ry]:
            parent[rx] = ry
        elif rank[rx] > rank[ry]:
            parent[ry] = rx
        else:
            parent[ry] = rx
            rank[rx] += 1

But note: we are not going to do union in the queries. Only in the initial graph building.

However, we have M edges, but there might be duplicate edges and self-loops. So in the initial graph, we should avoid adding an edge that is a self-loop? And skip duplicates? Actually, in the union function, if rx==ry, we skip. So we can just process all edges: for each edge (u, v), if u != v, then call union(u, v). But if u==v, skip. But the input says u and v are between 1 and N, and p_i != q_i in queries, but edges can have u_i = v_i? Yes, as in the example. So we skip self-loops.

But duplicate edges: we can process them, but the union will skip them because rx==ry. So it's harmless.

So, code structure:

   parent = list(range(N+1))
   rank = [0]*(N+1)

   for i in range(M):
        u, v = edges[i]
        if u != v:
            union(u, v)

   # Then, for each node, we can optionally do a find to compress paths? Actually, when we call find later, it will compress.

   # Build forbidden_set
   forbidden_set = set()
   for i in range(K):
        x, y = pairs[i]
        rx = find(x)
        ry = find(y)
        # Since the graph is good, rx != ry. But in code, we can check?
        if rx == ry:
            # According to problem, this shouldn't happen. But for safety, skip? Or the problem guarantees no path, so rx != ry. So we can skip if they are the same? Actually, if they are the same, then the condition is violated initially? But the problem states the graph is good. So we assume rx != ry.
            # But to be safe, we can skip adding to the set if rx==ry. Because then the pair is already connected, but the problem says there is no path? Contradiction. So we assume rx != ry.
            pass
        else:
            key = (min(rx, ry), max(rx, ry))
            forbidden_set.add(key)

   # Then process queries
   ans = []
   for i in range(Q):
        p, q = queries[i]
        rp = find(p)
        rq = find(q)
        if rp == rq:
            ans.append("Yes")
        else:
            key = (min(rp, rq), max(rp, rq))
            if key in forbidden_set:
                ans.append("No")
            else:
                ans.append("Yes")

But wait: what if a forbidden pair is stored and then we get a query that merges two components that are not the same as the stored pair? We are storing the root pair. So it's correct.

But note: the roots are arbitrary numbers (node indices). And the same component always has the same root. So the key is unique per pair of components.

However, when we do union by rank, the root might be the same for the same component? Yes.

So, I think this is correct.

But let me test with a small example that has multiple components and forbidden pairs.

Example: 3 nodes, no edges initially. Then components: 1,2,3.

Forbidden pairs: (1,2), (1,3).

Then forbidden_set: 
   (1,2): key=(1,2)
   (1,3): key=(1,3)

Queries:

1. Add edge (2,3): 
   Components: after adding, 2 and 3 become one component? Say root becomes 2 (if we union: set parent[3]=2). Then component of 1 is 1, component of 2 is 2, component of 3 is 2? Then the pair (1,2) is still in component (1) and (2) -> but the component of 2 is now 2, and 3 is also 2? Then the forbidden pair (1,3) is now (1,2) because the root of 3 is 2. So the key for (1,3) becomes (1,2). But we have stored (1,2) for (1,2) and (1,3) for (1,3). But after the merge, the forbidden pair (1,3) becomes between component 1 and component 2 (because node3 is now in component 2). So the key for (1,3) is (1,2). And we have that key in the set? Actually, we stored (1,3) initially, but after the merge, we don't have (1,3) in the set? 

Wait, no. We built the set initially. The set has two keys: (1,2) and (1,3). But when we process the query (2,3), the two components are 2 and 3. Then we form the key (min(2,3), max(2,3)) = (2,3). Is (2,3) in the set? No. So we would output "Yes". But that's wrong because after merging 2 and 3, the pair (1,3) becomes connected? Actually, node1 (component1) and node3 (which is now in component2) become connected? But the initial set stored (1,3) as (1,3). However, after merging 2 and 3, the root of 3 becomes the same as 2. So the root of 3 is now 2? Then the pair (1,3) becomes (1,2). But we don't have (1,2) in the set? We stored (1,3) and (1,2) separately. But we did not store (1,2) for (1,3) because initially, the root of 3 was 3, so the key was (1,3). Then, after the merge, we are about to merge components 2 and 3. The key for the merge is (2,3). Then we check: (2,3) is not in the set. So we allow the merge. But then the forbidden pair (1,3) becomes connected? 

But wait: after the merge, the component of 3 is now 2 (if we set parent[3]=2). Then the pair (1,3) is between component1 (root1=1) and component2 (root2=2). And we have stored (1,2) in the set? Actually, we stored (1,2) for the pair (1,2) and (1,3) for the pair (1,3). But (1,3) was stored as (1,3) initially. Now, after the merge, the root of 3 is 2, so the pair (1,3) is now (1,2). But we do not have (1,2) stored for the pair (1,3)? We have (1,2) only for the pair (1,2). So the set does not contain (1,2) for the pair (1,3). Therefore, we didn't mark (1,2) as forbidden for the pair (1,3). But the problem: the forbidden pairs are defined by the vertices, not by the components. 

This is a problem. The algorithm fails here.

What went wrong? The issue: after merging two components (say B and C), the forbidden pairs that were between A and B, and between A and C, are now both between A and the merged component (call it B'). But our forbidden_set was built with the initial components. We stored (A,B) and (A,C). But after merging, we have a new component B'. Then, the forbidden pair (1,2) is (A,B) and (1,3) is (A,C). When we merge B and C, the pair (1,2) becomes (A, B') and (1,3) becomes (A, B'). But we have not stored any forbidden pair for (A, B')? We only stored (A,B) and (A,C). So when we merge B and C, the key we check is (B,C). We don't check the pairs (A,B) and (A,C). 

But the condition for the graph to remain good is that none of the forbidden pairs become connected. After merging B and C, the pair (1,3) becomes connected? Because 1 is in A and 3 is in B' (which is connected to 2, which is also in B'). So 1 and 3 are now in different components? Actually, no: A and B' are separate. But 1 is in A, and 3 is in B', which is the merged component of B and C. So they are still separate? Then why is (1,3) connected? They are not? Then the graph remains good.

Wait, in the example: initially, no edges. Then we add (2,3). Then the graph has one edge (2,3). Now, is there a path from 1 to 3? No, because 1 is still isolated. So the pair (1,3) remains disconnected. Similarly, (1,2) remains disconnected. Therefore, the graph remains good. So the algorithm correctly output "Yes" for the query (2,3). 

But then why was I confused? Because I thought the pair (1,3) becomes connected? But it doesn't. Only the components of 2 and 3 are merged. The component of 1 is still separate. So (1,2) and (1,3) are still disconnected.

So the algorithm is correct.

But consider a different scenario: 

Three components: A (nodes 1,2), B (nodes 3,4), C (node 5).

Forbidden pairs: (1,3) and (1,5).

Now, we want to add an edge between 3 and 5. This will merge B and C. Then, the forbidden pair (1,5) becomes (1, BC) which is still disconnected? Because 1 is in A and 5 is in BC. But the pair (1,5) is still disconnected. However, the pair (1,3) is between A and BC (because 3 is in BC). So now, the pair (1,3) is still disconnected? Yes. So the graph remains good.

But what if we have:

Forbidden pairs: (2,5) and (4,5). Then, if we merge B and C, we get a component BC (3,4,5). Then, the pair (2,5) becomes (A, BC) and (4,5) becomes (BC, BC) -> but wait, (4,5) are both in BC? Then they become connected? But the problem states that initially, there is no path. After adding the edge (3,5) (which connects B and C), then 4 and 5 are in the same component? But initially, 4 and 5 were in the same component? No, initially, 4 is in B and 5 is in C. Then after merging B and C, they become the same component. So if there was a forbidden pair (4,5), then after merging, they become connected. 

But in our algorithm:

Initially, for the forbidden pair (4,5): 
   root4 = (say) 3 (if B has root 3) and root5=5. So store (3,5).

Then, when we add edge (3,5): 
   root3 = 3, root5=5 -> different. Then we form key (3,5). Check if (3,5) is in the set? Yes, because we stored (3,5) for (4,5). So we output "No", which is correct.

So the algorithm handles that.

But what if the forbidden pair is (2,4) and (2,5)? Then:

For (2,4): root2 (in A)=1, root4 (in B)=3 -> key (1,3)
For (2,5): root2=1, root5=5 -> key (1,5)

Now, add edge (4,5): 
   root4=3, root5=5 -> key (3,5). 
   Check: (3,5) is not in the set. So output "Yes". 

But after adding (4,5), the components B and C merge. Then, the pair (2,5) is between A and BC -> which is disconnected. The pair (2,4) is between A and BC -> disconnected. And the pair (4,5) becomes connected? But (4,5) is not a forbidden pair. So the graph remains good.

So the algorithm is correct.

Therefore, the solution is:

Implement DSU with path compression and union by rank. Process the M edges (skipping self-loops). Then, for each forbidden pair, compute the roots and store the canonical pair in a set. Then for each query, if the two nodes are in the same component, output "Yes". Otherwise, check if the canonical pair of their roots is in the forbidden set: if yes, "No"; else, "Yes".

Code:

Note: We assume nodes are 1-indexed.

Let's code accordingly.

But note: the DSU find and union functions. We'll write the DSU class or just functions? Since we are in a class, we can do:

In the solve method:

    parent = list(range(N+1))
    rank = [0] * (N+1)

    # DSU functions
    def find(x: int) -> int:
        if parent[x] != x:
            parent[x] = find(parent[x])
        return parent[x]

    def union(x: int, y: int):
        rx = find(x)
        ry = find(y)
        if rx == ry:
            return
        if rank[rx] < rank[ry]:
            parent[rx] = ry
        elif rank[rx] > rank[ry]:
            parent[ry] = rx
        else:
            parent[ry] = rx
            rank[rx] += 1

    # Process edges
    for u, v in edges:
        if u == v:
            continue
        ru = find(u)
        rv = find(v)
        if ru != rv:
            union(ru, rv)   # Actually, we can call union(u, v) because find inside union will get roots. But our union function above uses find, so we can call union(u, v). But we already computed ru and rv? Then we can do:
            # Actually, we can do: 
            #   if ru != rv: then merge ru and rv (using the same union by rank logic). 
            # But the function above does that. Alternatively, we can write without the inner find? But we can just call union(u, v) and the function will do find.

    But we have:

        def union(x, y):
            rx = find(x)
            ry = find(y)
            ...

    So we can call union(u, v) for each edge? Then we don't need to call find outside.

    Alternatively, we can do:

        if u != v:
            union(u, v)

    But in the union function, we do find, which will get the roots. Then if same, return. So it's safe.

    However, to avoid redundant finds, we can check ru and rv outside? But the union function does the same. So it's fine.

    But the problem: the DSU functions are defined inside solve, and we are modifying parent and rank. So it's okay.

    However, with large recursion depth? Python recursion limit might be hit. But N up to 200,000, so recursion depth might be too high? We should avoid recursion for DSU? We can use iterative find.

    Alternatively, we can increase recursion limit? But that might be unsafe.

    Better: iterative path compression.

    But for simplicity, we'll do recursion and hope that the depth is not too high? But worst-case chain is 200,000, which is too deep for recursion.

    So we should write iterative find.

    Let me write iterative find:

        def find(x: int) -> int:
            stack = []
            while parent[x] != x:
                stack.append(x)
                x = parent[x]
            root = x
            for i in stack:
                parent[i] = root
            return root

    But this is iterative and does path compression.

    Alternatively, we can do:

        def find(x: int) -> int:
            r = x
            while parent[r] != r:
                r = parent[r]
            # now r is root
            while x != r:
                p = parent[x]
                parent[x] = r
                x = p
            return r

    But the common iterative method:

        def find(x: int) -> int:
            root = x
            while root != parent[root]:
                root = parent[root]
            while x != root:
                nxt = parent[x]
                parent[x] = root
                x = nxt
            return root

    This is efficient.

    But we can also use the iterative method that compresses partially? But we want full compression.

    So we'll do:

        parent = list(range(N+1))
        rank = [0]*(N+1)

        def find(x: int) -> int:
            root = x
            while root != parent[root]:
                root = parent[root]
            # Path compression
            while x != root:
                nxt = parent[x]
                parent[x] = root
                x = nxt
            return root

        def union(x: int, y: int):
            rx = find(x)
            ry = find(y)
            if rx == ry:
                return
            if rank[rx] < rank[ry]:
                parent[rx] = ry
            elif rank[rx] > rank[ry]:
                parent[ry] = rx
            else:
                parent[ry] = rx
                rank[rx] += 1

    Then process edges.

But note: we are building the graph only once.

Now, after building the graph, we need to compress all paths? Actually, each find during the building already compresses the path. But when we call find later (for the forbidden pairs and queries), we might have paths that are not compressed? But the initial unions might leave some long chains. So we can do: after building the graph, for each node, call find to compress? Or we can rely on the find calls during the forbidden_set building to compress? That might be acceptable.

But to be safe, we can compress all paths by doing:

    for i in range(1, N+1):
        find(i)

But that is O(N) and each find is O(α(N))? Actually, after the initial unions, the find with iterative compression will be efficient.

Alternatively, we can not do that and let the subsequent finds compress as needed.

Given the constraints, it should be acceptable.

But worst-case, the union by rank ensures that the tree height is O(log N), so the iterative find won't be too deep.

So we'll proceed.

Now, code:

    parent = list(range(N+1))
    rank = [0]*(N+1)

    # iterative find
    def find(x):
        root = x
        while root != parent[root]:
            root = parent[root]
        while x != root:
            next_node = parent[x]
            parent[x] = root
            x = next_node
        return root

    def union(x, y):
        rx = find(x)
        ry = find(y)
        if rx == ry:
            return
        if rank[rx] < rank[ry]:
            parent[rx] = ry
        elif rank[rx] > rank[ry]:
            parent[ry] = rx
        else:
            parent[ry] = rx
            rank[rx] += 1

    # Build the graph
    for u, v in edges:
        if u != v:
            union(u, v)

    # Then build forbidden_set
    forbidden_set = set()
    for x, y in pairs:
        rx = find(x)
        ry = find(y)
        if rx == ry:
            # According to the problem, this should not happen. But skip.
            # But the problem guarantees no path, so we can ignore.
            continue
        key = (min(rx, ry), max(rx, ry))
        forbidden_set.add(key)

    # Process queries
    ans = []
    for p, q in queries:
        rp = find(p)
        rq = find(q)
        if rp == rq:
            ans.append("Yes")
        else:
            key = (min(rp, rq), max(rp, rq))
            if key in forbidden_set:
                ans.append("No")
            else:
                ans.append("Yes")

    return ans

But wait: what if in the forbidden_set, we have a pair (a,b) and the query has the same pair (a,b)? Then we output "No". But if the same pair (a,b) is not forbidden, we output "Yes". So that's correct.

But note: the roots might change during the building of the graph, but after that, they are fixed. And we are using the same DSU without modification.

I think it's correct.

Let me run the provided example through the code:

N=6, M=6
edges = [[1,2],[2,3],[2,3],[3,1],[5,4],[5,5]]

After building the graph:

Components: 
  1,2,3 -> root? Let's say we assign: 
      Initially: parent=[0,1,2,3,4,5,6]
      Then, process (1,2): union(1,2): set parent[2]=1? Then parent[2]=1, and parent[1]=1.
      Then (2,3): find(2)=1, find(3)=3 -> set parent[3]=1.
      Then (2,3) again: same, skip.
      Then (3,1): same, skip.
      Then (5,4): union(5,4): set parent[4]=5? Or by rank: initially, rank all 0. So when union(5,4): set parent[4]=5? Then parent[4]=5, parent[5]=5.
      Then (5,5): skip.

      So parent: 
          1:1, 2:1, 3:1, 4:5, 5:5, 6:6.

      Then, when we call find for nodes, we compress the path? For node2: 
          find(2): 
            root: start at 2, then parent[2]=1, then parent[1]=1 -> root=1.
            then set parent[2]=1, so no change.
          Similarly, node3: 
            start at 3, parent[3]=1 -> then set parent[3]=1? 
          So after compression: 
            parent[1]=1, parent[2]=1, parent[3]=1, parent[4]=5, parent[5]=5, parent[6]=6.

Forbidden pairs: 
  (1,5): find(1)=1, find(5)=5 -> key=(1,5)
  (2,6): find(2)=1, find(6)=6 -> key=(1,6)
  (4,3): find(4)=5, find(3)=1 -> key=(1,5)  -> same as first.

So forbidden_set = {(1,5), (1,6)}

Queries:

  (2,5): find(2)=1, find(5)=5 -> key=(1,5) in set -> "No"
  (2,6): (1,6) in set -> "No"
  (5,6): (5,6) -> (min(5,6)=5, max=6) -> (5,6) not in set -> "Yes"
  (5,4): same component (5) -> "Yes"

Matches.

But wait, the example output for the third query is "Yes", and the fourth is "Yes". So we return ["No","No","Yes","Yes"].

Perfect.

Now, edge: if a node is isolated, its parent is itself.

One more edge: if we have a chain. But DSU with union by rank and path compression will handle.

I think we are done.

But note: the DSU union by rank and path compression is efficient.

We'll code accordingly.

But note: the DSU we implemented uses iterative find. That should avoid recursion depth issues.

Let me write the complete code.

We'll write the solve function accordingly.

But note: the problem has multiple edges and self-loops. We skip self-loops (u==v) and duplicate edges are handled by DSU (if same component, skip).

So we are good.

Now, time to code.

But note: the DSU arrays are defined in the solve function. With N up to 200,000, it's acceptable.

Let me test with a small example.

Test with 2 nodes, no edges, K=1: forbidden pair (1,2), Q=1: add edge (1,2). Then:

  Initially: parent[1]=1, parent[2]=2.
  Forbidden: (1,2) -> key=(1,2) in set.
  Query: (1,2): key=(1,2) in set -> "No". Correct.

Another test: 2 nodes, no edges, K=0, Q=1: add edge (1,2). Then:
  Forbidden_set is empty.
  Query: (1,2): key=(1,2) not in set -> "Yes". Correct.

Another test: 3 nodes, edges: (1,2). Then components: {1,2}, {3}. Forbidden pairs: (1,3). Then queries: add (2,3). 
  Forbidden_set: (1,3) -> key: (1,3) (because find(1)=1, find(3)=3).
  Query: (2,3): find(2)=1 (because 2 is in the same component as 1: after union(1,2), parent[2]=1? Then find(2) returns 1. So key = (1,3) -> which is in the set? Then output "No". But after adding (2,3), the components {1,2} and {3} become one component? Then the pair (1,3) becomes connected. So "No" is correct.

But what if the forbidden pair was (2,3) initially? Then we store (1,3) because find(2)=1, find(3)=3. Then the query (2,3) becomes (1,3) in set? Then "No". Correct.

But if the forbidden pair was (1,2)? Then initially, they are connected? But the problem states that the graph is good, so no path. So we don't have such a pair. So we skip.

So the code should be:

But note: the DSU implementation: when we do union(1,2) for two nodes, the parent of 2 becomes 1. Then when we call find(2), it returns 1.

So the key for the forbidden pair (2,3) is (1,3).

Therefore, the algorithm is consistent.

Now, we code.

One more thing: we must not use recursion for find to avoid recursion depth. We have implemented iterative find.

We'll write accordingly.

Code:

Note: the DSU arrays are 0-indexed? But the nodes are 1-indexed. So we create arrays of size N+1, and use indices 1 to N.

We'll write:

    parent = [i for i in range(N+1)]
    rank = [0] * (N+1)

    # DSU functions
    def find(x):
        root = x
        while root != parent[root]:
            root = parent[root]
        # Path compression
        temp = x
        while temp != root:
            nxt = parent[temp]
            parent[temp] = root
            temp = nxt
        return root

    def union(x, y):
        rx = find(x)
        ry = find(y)
        if rx == ry:
            return
        if rank[rx] < rank[ry]:
            parent[rx] = ry
        elif rank[rx] > rank[ry]:
            parent[ry] = rx
        else:
            parent[ry] = rx
            rank[rx] += 1

Then process edges.

But note: in the union function, we are using the same DSU techniques.

We'll run the example above.

Now, let's write the entire solve function.

We'll return the list of answers.

Edge: if there are no edges and no unions, then each node is its own component.

I think we are ready.

We'll write the code accordingly.

But note: the problem has constraints that might push performance. We are doing:

- M unions: O(M * α(N))
- K finds: O(K * α(N))
- Q finds: O(Q * α(N))

And set lookups: O(1) per query.

Total: O((M+K+Q) * α(N)) which is about 600,000 operations, which is acceptable in Pyton? Probably.

But we must write efficient DSU.

I'll write the iterative path compression.

One more thing: in the DSU, the parent array is updated during the building and then during the queries we do finds? But we don't update the graph after the initial building. So the parent array is fixed after the initial unions? But during the forbidden_set building and the queries, we do find which compresses the path. So the first time we find a node that was not compressed, we compress it. Subsequent finds are O(1).

So it's efficient.

Now, code:

Note: We must not do union in the queries. Only in the initial graph building.

We'll code accordingly.

Let me write the entire solve function.

We'll use:

    import sys
    but the function is given, so we just fill in.

    class Solution:
        def solve(self, N: int, M: int, edges: List[List[int]], K: int, pairs: List[List[int]], Q: int, queries: List[List[int]]) -> List[str]:
            parent = [i for i in range(N+1)]
            rank = [0] * (N+1)
            
            # DSU functions
            def find(x: int) -> int:
                root = x
                while root != parent[root]:
                    root = parent[root]
                # Compress path
                curr = x
                while curr != root:
                    nxt = parent[curr]
                    parent[curr] = root
                    curr = nxt
                return root
            
            def union(x: int, y: int):
                rx = find(x)
                ry = find(y)
                if rx == ry:
                    return
                if rank[rx] < rank[ry]:
                    parent[rx] = ry
                elif rank[rx] > rank[ry]:
                    parent[ry] = rx
                else:
                    parent[ry] = rx
                    rank[rx] += 1
                    
            # Build initial graph
            for u, v in edges:
                if u == v:
                    continue
                union(u, v)
            
            # Build forbidden_set: set of tuples (min_root, max_root) for each forbidden pair
            forbidden_set = set()
            for x, y in pairs:
                rx = find(x)
                ry = find(y)
                # Since the graph is good, rx != ry, but if they are same, skip (shouldn't happen)
                if rx == ry:
                    continue
                # Represent the pair in canonical order
                if rx > ry:
                    rx, ry = ry, rx
                forbidden_set.add((rx, ry))
            
            ans = []
            for p, q in queries:
                rp = find(p)
                rq = find(q)
                if rp == rq:
                    ans.append("Yes")
                else:
                    if rp > rq:
                        rp, rq = rq, rp
                    # Now rp < rq
                    if (rp, rq) in forbidden_set:
                        ans.append("No")
                    else:
                        ans.append("Yes")
            return ans

But note: in the forbidden_set, we stored (min, max). Similarly, in the query, we convert to (min, max). So we can do:

            for p, q in queries:
                rp = find(p)
                rq = find(q)
                if rp == rq:
                    ans.append("Yes")
                else:
                    if rp > rq:
                        key = (rq, rp)
                    else:
                        key = (rp, rq)
                    if key in forbidden_set:
                        ans.append("No")
                    else:
                        ans.append("Yes")

But I did a swap. Alternatively, we can do as above.

But in the code above, I swapped in the query processing. But we can also do:

            key = (min(rp, rq), max(rp, rq))

But that would be two min and max per query. Instead, we can do:

            if rp > rq:
                key = (rq, rp)
            else:
                key = (rp, rq)

But that is the same as (min, max).

But for clarity, we can do:

            key = (min(rp, rq), max(rp, rq))

But that is O(1) per query.

But to avoid function calls, we can do:

            if rp < rq:
                key = (rp, rq)
            else:
                key = (rq, rp)

But the set lookup is the same.

I'll use:

            if rp == rq:
                ans.append("Yes")
            else:
                key = (rp, rq) if rp < rq else (rq, rp)
                ans.append("No" if key in forbidden_set else "Yes")

But to be clear, we can write:

            if rp == rq:
                ans.append("Yes")
            else:
                if rp > rq:
                    rp, rq = rq, rp
                if (rp, rq) in forbidden_set:
                    ans.append("No")
                else:
                    ans.append("Yes")

This is efficient.

We'll run the example and hope it passes.

But note: the example input has duplicate edges and self-loop. We skip the self-loop and the duplicate edge is handled by union (which skips).

So it's correct.

I think we are done.

But note: the problem says the graph G is good initially. So for each forbidden pair (x_i,y_i), there is no path. Therefore, we don't expect to have rx==ry in the forbidden_set building. But if we do, we skip. So it's safe.

Now, we output the list of answers.

Let me test with a small example: 2 nodes, one edge (1,2), K=0, Q=1: add edge (1,2). Then the graph is already connected, so adding the edge doesn't change. Then output "Yes".

But the initial graph has an edge (1,2). Then the query is to add (1,2). Then the graph has two edges? Then the components are still the same. So "Yes".

But what if the initial graph has no edge, and we have a forbidden pair (1,2), and then a query to add (1,2)? Then we output "No", as above.

I think we are set.

We'll submit the code.

But note: the constraints say that Q, K, M, N can be up to 200,000. So we must test with worst-case? But the code should be O(α(N)) per operation. And the DSU with union by rank and path compression is efficient.

But we use iterative find to avoid recursion depth.

We'll hope.

But one more thing: the DSU union: we are doing:

        if rank[rx] < rank[ry]:
            parent[rx] = ry
        elif rank[rx] > rank[ry]:
            parent[ry] = rx
        else:
            parent[ry] = rx
            rank[rx] += 1

This is standard.

I think it's correct.

Now, we write the final code.

But note: in the forbidden_set building, we stored the component pair in canonical order. And in the query, we do the same. So it's consistent.

Final code:

Note: We use 0-indexed or 1-indexed? The nodes are 1-indexed, and our parent array is 0-indexed? Actually, we have parent[0] but we don't use it. We use indices 1 to N.

So the code is:

    parent = [i for i in range(N+1)]   # parent[0]=0, parent[1]=1, ..., parent[N]=N.

Then, we only use 1..N.

So it's correct.

Let me run the provided example.

We'll do the example step by step.

Given:

N=6, M=6
edges: [1,2],[2,3],[2,3],[3,1],[5,4],[5,5]

After building the graph:

  parent: 
      Initially: [0,1,2,3,4,5,6]
      Process [1,2]: union(1,2): 
          find(1)=1, find(2)=2 -> different, same rank -> set parent[2]=1, rank[1] becomes 1.
          parent: [0,1,1,3,4,5,6], rank[1]=1
      [2,3]: find(2)=1 (because parent[2]=1, then parent[1]=1 -> root=1), find(3)=3 -> set parent[3]=1, rank[1] is 1, so set parent[3]=1, and rank[1] remains 1? Because 1>0, so no rank update? Actually, in union, we compare the ranks: rank[1]=1, rank[3]=0 -> set parent[3]=1.
          parent: [0,1,1,1,4,5,6]
      [2,3] again: same, skip.
      [3,1]: same, skip.
      [5,4]: union(5,4): 
          find(5)=5, find(4)=4 -> different, same rank. Then set parent[4]=5? Or parent[5]=4? Since same rank, we set parent[4]=5? Then parent: [0,1,1,1,5,5,6], and rank[5] becomes 1.
      [5,5]: skip.

  Then, after building, we have:
        parent: [0,1,1,1,5,5,6]

  But then we do find for the forbidden pairs? And during find, we do path compression? For example, for node4: 
        find(4): 
            root = 4 -> parent[4]=5, then go to 5 -> parent[5]=5 -> root=5.
            then, from 4: set parent[4]=5? Then set parent[4]=5. Then 5 is already root.
        So parent[4]=5.

  Similarly, node3: 
        start at 3, parent[3]=1 -> then parent[1]=1 -> root=1. Then set parent[3]=1.

  So after compression, parent: [0,1,1,1,5,5,6]

  Then, for forbidden pairs:

        (1,5): find(1)=1, find(5)=5 -> key=(1,5) -> but 1<5, so (1,5)
        (2,6): find(2)=1 (because parent[2]=1, then parent[1]=1), find(6)=6 -> (1,6)
        (4,3): find(4)=5, find(3)=1 -> (1,5)

  So forbidden_set = {(1,5), (1,6)}

  Then queries:

        (2,5): find(2)=1, find(5)=5 -> key=(1,5) in set -> "No"
        (2,6): (1,6) in set -> "No"
        (5,6): (5,6) -> min=5, max=6 -> (5,6) not in set -> "Yes"
        (5,4): same component? find(5)=5, find(4)=5 -> same -> "Yes"

  So the output is ["No","No","Yes","Yes"] -> matches.

Therefore, we are confident.

Now, we write the code accordingly.

Note: We must be cautious: the DSU functions are nested. But it's acceptable.

We'll submit.

But note: the problem says that the graph G is good, so we assume that the forbidden pairs are disconnected. But if a forbidden pair is connected initially, we skip adding to the set. So if the graph is not good initially (which shouldn't happen), we skip. But the problem states it is good.

So we are safe.

Final code:

## Final Solution

To solve this problem, we need to determine if adding a new edge to an undirected graph preserves the "good" property of the graph. A graph is considered "good" if there is no path between any of the given pairs of vertices. The solution involves efficiently checking whether adding each query edge connects any of the forbidden pairs.

### Approach
1. **Problem Analysis**: The problem requires processing multiple queries where each query adds an edge to the graph and checks if the resulting graph remains "good". The key insight is that adding an edge between two vertices in different components merges those components. The graph remains "good" only if none of the forbidden pairs have one vertex in each of these components.

2. **Union-Find Data Structure (Disjoint Set Union - DSU)**: 
   - **Initial Setup**: We use the DSU to manage connected components of the graph. The DSU helps in efficiently finding and merging components.
   - **Path Compression and Union by Rank**: These optimizations ensure that the DSU operations (find and union) run in near-constant time per operation, making the solution scalable for large inputs.

3. **Preprocessing**:
   - **Building Components**: Process all initial edges to build the connected components of the graph.
   - **Forbidden Component Pairs**: For each forbidden vertex pair (x_i, y_i), store the pair of their component roots in a canonical form (min-root, max-root) in a set. This set helps in quickly checking if merging two components would connect any forbidden pair.

4. **Query Processing**:
   - For each query (p_i, q_i), check if the vertices are already in the same component. If so, adding the edge doesn't change the graph, so the answer is "Yes".
   - If they are in different components, form the canonical pair of their roots. If this pair exists in the forbidden set, adding the edge connects a forbidden pair, so the answer is "No". Otherwise, the answer is "Yes".

### Solution Code
```python
from typing import List

class Solution:
    def solve(self, N: int, M: int, edges: List[List[int]], K: int, pairs: List[List[int]], Q: int, queries: List[List[int]]) -> List[str]:
        parent = [i for i in range(N+1)]
        rank = [0] * (N+1)
        
        def find(x: int) -> int:
            root = x
            while root != parent[root]:
                root = parent[root]
            curr = x
            while curr != root:
                next_node = parent[curr]
                parent[curr] = root
                curr = next_node
            return root
        
        def union(x: int, y: int):
            rx = find(x)
            ry = find(y)
            if rx == ry:
                return
            if rank[rx] < rank[ry]:
                parent[rx] = ry
            elif rank[rx] > rank[ry]:
                parent[ry] = rx
            else:
                parent[ry] = rx
                rank[rx] += 1
                
        for u, v in edges:
            if u != v:
                union(u, v)
                
        forbidden_set = set()
        for x, y in pairs:
            rx = find(x)
            ry = find(y)
            if rx != ry:
                if rx > ry:
                    rx, ry = ry, rx
                forbidden_set.add((rx, ry))
        
        ans = []
        for p, q in queries:
            rp = find(p)
            rq = find(q)
            if rp == rq:
                ans.append("Yes")
            else:
                if rp > rq:
                    rp, rq = rq, rp
                ans.append("No" if (rp, rq) in forbidden_set else "Yes")
        return ans
```

### Explanation
1. **Initialization**: The DSU is initialized with each vertex as its own parent and rank zero.
2. **Union-Find Operations**: The `find` function uses path compression to flatten the structure, and `union` uses union by rank to merge trees efficiently.
3. **Graph Construction**: The initial edges are processed to build connected components. Self-loops are skipped, and duplicate edges are handled naturally by the DSU.
4. **Forbidden Pairs Handling**: For each forbidden vertex pair, their component roots are found. The pair of roots is stored in canonical form (smaller root first) in a set to indicate that merging these components would violate the "good" property.
5. **Query Processing**: For each query:
   - If the vertices are in the same component, adding the edge doesn't change connectivity, so the answer is "Yes".
   - If they are in different components, the canonical root pair is checked against the forbidden set. If present, the answer is "No"; otherwise, "Yes".

This approach efficiently checks each query in near-constant time after an initial setup phase, making it suitable for large inputs as specified in the problem constraints.