# Solution for abc324_f.md

Generated at: 2025-07-22 06:40:48
Model: deepseek-reasoner

## Reasoning Process

Alright, I'm going to tackle this problem step by step. Let me start by understanding the problem.

We have a directed graph with N vertices and M edges. Each edge has two attributes: beauty and cost. The goal is to find a path from vertex 1 to vertex N that maximizes the ratio of total beauty to total cost. That is, for a path P, we want to maximize (sum of beauties on P) / (sum of costs on P).

Constraints are quite large: N and M up to 200,000. So, we need an efficient solution.

First, I recall that problems involving maximizing a ratio often use binary search. Specifically, for this type of problem, we can set up a binary search on the possible ratio values. The idea is to transform the problem into checking whether there exists a path such that (sum of beauties) / (sum of costs) >= x, which can be rewritten as sum of (beauty - x * cost) >= 0.

So, if we let f(x) = max{ sum(beauty_i) - x * sum(cost_i) } over all paths from 1 to N, then our goal is to find x such that f(x) = 0. Actually, we want the maximum x for which f(x) >= 0.

But wait, actually, we can set up a binary search for the ratio. For a given x, we can create a new graph where each edge has weight = beauty - x * cost. Then, if there is a path from 1 to N with total weight >= 0, then the ratio x is achievable. We can then search for the maximum such x.

However, note that the graph is directed and the constraints say that u_i < v_i. That means the graph is a directed acyclic graph (DAG)? Because every edge goes from a lower vertex to a higher vertex. So, there can't be cycles. This is important because without cycles, we can compute the maximum (or minimum) path in a DAG efficiently.

Yes, since u_i < v_i for every edge, the graph is a DAG. Therefore, we can use topological sorting to process vertices in order from 1 to N.

So, the plan is:

1. Use binary search over the possible ratio values. What should the range be? Since beauty and cost are positive and at least 1, the ratio can be as low as 0 (if we have a path with positive cost but zero beauty, but that's not possible because beauty is at least 1). Actually, the minimum ratio could be min(beauty_i / cost_i) and maximum could be max(beauty_i / cost_i) over all edges? But note that a path might combine edges with different ratios. However, the maximum ratio for a path cannot exceed the maximum ratio of an edge? Actually, no, because adding edges might change the ratio. For example, if we have two edges: one with 2/1=2 and another with 1/2=0.5, then the total ratio is (2+1)/(1+2)=1, which is in between. Actually, the maximum ratio for a path must be between min(beauty_i/cost_i) and max(beauty_i/cost_i). But to be safe, we can set the low to 0 and high to max(beauty_i) * (max path length) / min(cost_i) ... but that might be too high. Alternatively, we can set high to max(beauty_i) * M / (min(cost_i))? But M is 200,000 and beauty and cost up to 10,000, so max ratio could be (10,000 * 200,000) / 1 = 2e9. But that's too big. Alternatively, we can set high to the maximum edge ratio multiplied by the maximum possible path length? Actually, the maximum ratio for a path is at most the maximum ratio of any edge? Or not? Actually, no. Consider two edges: the first has beauty=10000, cost=1 (ratio 10000), and the second has beauty=1, cost=10000 (ratio 0.0001). If we take both, the ratio is (10000+1)/(1+10000)=1, which is less than 10000. But is there a way to get a ratio higher than the maximum edge? Suppose we have two edges: one with 10/1=10 and another with 1000/100=10. Then the total is 1010/101 ≈10, which is the same. Actually, by the mediant inequality, the ratio of the sum is between the min and max of the individual ratios. Therefore, the maximum ratio for a path is the maximum ratio of any single edge? But wait, that's not true. Consider edges: (beauty=2, cost=1) and (beauty=1, cost=1). The individual ratios are 2 and 1. If we take both, the total ratio is (2+1)/(1+1)=1.5, which is between 1 and 2. So the maximum ratio of a path must be between the min and max of the individual edge ratios. Actually, the maximum ratio of any path cannot exceed the maximum ratio of an edge? Because if we add an edge with ratio r to a path, then the new ratio is a weighted average of the existing path's ratio and r. Therefore, it will be between the two. Thus, the maximum ratio for the entire path is at most the maximum edge ratio. Similarly, at least the minimum edge ratio.

So, we can set:
   low = min(beauty_i / cost_i for all i)  # actually, since all are positive, but to be safe, set low=0? Or maybe min_ratio = min(beauty_i/cost_i) and high = max(beauty_i/cost_i). But note: the path might consist of multiple edges. However, by the above reasoning, the overall ratio is between min_ratio and max_ratio. Therefore, we can set low = 0 and high = max(beauty_i) (since cost_i>=1, max_ratio <= max(beauty_i)). But actually, max_ratio = max(beauty_i) / min(cost_i) = 10000 / 1 = 10000. So we can set low=0, high=10000.

But to be precise, we can set:
   low = 0
   high = max(beauty_i) * 1.0  # because min cost is 1, so max ratio is at most 10000.

But actually, we can set high = max(beauty_i / cost_i) over edges. That would be safer. Since M is 200,000, we can precompute that.

So, binary search will iterate until (high - low) is small enough. We want absolute error <= 1e-9. But note that the problem says relative or absolute error. So we can iterate for about 100 iterations? Because 2^(-100) is about 1e-30, which is way below 1e-9.

But we have to consider the time per iteration. For each iteration, we need to:

1. Build the graph with weights: for each edge, weight = beauty - x * cost.
2. Then, we need to find the longest path from 1 to N in a DAG. Since it's a DAG (because u_i < v_i), we can do a topological sort and then use dynamic programming.

But note: the graph is already in topological order? The vertices are numbered 1 to N, and every edge goes from a lower vertex to a higher vertex. So we can process vertices in increasing order.

So the algorithm for each mid (candidate ratio x) is:

   Let dist[1] = 0, and for other vertices, we can initialize to -infinity (since we want maximum path).
   Then, for vertex i from 1 to N:
        for each edge from i to j (where j>i), update:
            dist[j] = max(dist[j], dist[i] + (beauty - x*cost))

   Then, if dist[N] >= 0, then the ratio x is achievable, so we can set low = mid. Otherwise, high = mid.

But wait: we want to check if there exists a path such that the total weight (beauty - x*cost) >= 0. So we can compute the maximum total weight from 1 to N. If it is >=0, then we can achieve ratio at least x.

However, note: there might be multiple paths. We are taking the maximum path in terms of (beauty - x*cost). If that maximum is >=0, then yes.

But what if we have negative weights? Then, we cannot use Dijkstra, but we can use DP because it's a DAG.

So, for each vertex, we want the maximum value of the path from 1 to that vertex. Since we process vertices in increasing order (from 1 to N), we can update each vertex by checking all incoming edges? Actually, in our graph, edges go from u to v with u < v. So for a vertex j, all incoming edges come from vertices i < j. Therefore, when processing j, we can have looked at all i < j already. So we can do:

   dp[1] = 0
   For i from 2 to N:
        dp[i] = -infinity
        for each edge (u, v, b, c) that ends at i (so v==i):
            candidate = dp[u] + (b - x * c)
            if candidate > dp[i]:
                dp[i] = candidate

   Then check dp[N] >= 0.

But note: there might be multiple edges to the same vertex. So we need to consider all incoming edges to i.

But the problem: how to handle multiple edges? We take the maximum candidate from all incoming edges.

But the graph has M edges, and we are iterating over each vertex and then each incoming edge. The total is M, so O(M) per iteration.

Since M is 200,000 and we do about 100 iterations, total operations 200,000 * 100 = 20e6, which is acceptable in Python? Actually, 20e6 operations in Python might be borderline in PyPy/C++ but in Python we have to be cautious. However, 20e6 is acceptable in Pyton if we optimize the inner loop. But we have to make sure that we are not doing anything heavy.

Alternatively, we can precompute the graph. We can store for each vertex, a list of tuples (u, b, c) for the incoming edges? Actually, in the input, we have edges as (u, v, b, c). So for each vertex, we can store the edges that end at that vertex.

So steps:

   Precomputation:
        graph = [[] for _ in range(N+1)]
        For each edge (u, v, b, c):
            # we store at graph[v] the tuple (u, b, c)
            graph[v].append((u, b, c))

   Then, in each binary search iteration:

        dp = [-10**18] * (N+1)
        dp[1] = 0
        for v in range(2, N+1):
            for (u, b, c) in graph[v]:
                if dp[u] != -10**18:  # meaning we can reach u
                    candidate = dp[u] + b - mid * c
                    if candidate > dp[v]:
                        dp[v] = candidate
            # if no incoming edge, then dp[v] remains -inf, but since there's a path from 1 to N, we don't care about unreachable?
        Then check dp[N] >= 0.

But note: the graph is directed and u < v, so we can process vertices from 1 to N. And we know that vertex 1 has no incoming edge? Because u < v, so no edge can end at 1. Similarly, for vertex 2, only edges from 1 can come in. So we are safe.

But what if a vertex has no incoming edge? Then it remains -inf, and then when processing outgoing edges from that vertex, we use -inf. But that's okay because we won't update any further. However, the problem states that there's at least one path from 1 to N, so we don't have to worry about vertex N being unreachable.

But in the DP, we only update a vertex if we have an incoming edge from a reachable vertex. Since we start at 1, which is set to 0, and we process in increasing order, the vertices that are unreachable from 1 will remain at -inf. But that's acceptable.

Now, time complexity: each edge is processed once per binary search iteration. So total operations: M * (number of iterations). With 100 iterations, that's 200,000 * 100 = 20,000,000. In Python, 20e6 iterations might run in a few seconds? But worst-case in Pyton, 20e6 iterations might be acceptable. However, we have to test with the constraints.

But note: worst-case M is 200,000 and iterations 100, so 20e6. In C++ that's fast, in Python we might be borderline in Pyton (if each iteration is heavy). But each iteration is just a simple arithmetic and comparison. So it should be acceptable.

But let me check with the examples.

Example 1: 
   Input: 
        5 7
        1 2 3 6
        1 3 9 5
        2 3 1 5
        2 4 5 3
        2 5 1 9
        3 4 4 8
        4 5 2 7

   We know the answer is 0.75.

   Let me test with mid=0.75: then weight for each edge:
        edge1: 3 - 0.75*6 = 3 - 4.5 = -1.5
        edge2: 9 - 0.75*5 = 9-3.75=5.25
        edge3: 1 - 0.75*5 = 1-3.75=-2.75
        edge4: 5 - 0.75*3 = 5-2.25=2.75
        edge5: 1 - 0.75*9 = 1-6.75=-5.75
        edge6: 4 - 0.75*8 = 4-6=-2
        edge7: 2 - 0.75*7 = 2-5.25=-3.25

   Then the path 1->3->4->5: 
        edge2: 5.25
        edge6: -2
        edge7: -3.25
        total = 5.25-2-3.25 = 0, which is >=0. So 0.75 is achievable.

   Now, if we set mid=0.76, then:
        edge2: 9 - 0.76*5 = 9-3.8=5.2
        edge6: 4 - 0.76*8 = 4-6.08=-2.08
        edge7: 2 - 0.76*7 = 2-5.32=-3.32
        total = 5.2 -2.08 -3.32 = -0.2 <0 -> so 0.76 not achievable.

   Therefore, the maximum x is 0.75.

Example 2: 
        3 3
        1 3 1 1 -> weight = 1 - x*1
        1 3 2 1 -> weight = 2 - x
        1 3 3 1 -> weight = 3 - x

   We take the maximum weight for vertex 3: max(1-x, 2-x, 3-x). For x=3, weight=0; for x>3, negative. So the maximum x that has at least one non-negative weight is 3.

But note: we are taking the maximum weight path. For x=3, the path with the last edge (beauty=3, cost=1) has weight 0. So we get 0, which is >=0.

So the algorithm should work.

But what about multiple edges to the same vertex? We are storing for each vertex, all incoming edges. Then, when processing vertex v, we iterate over all incoming edges and take the maximum candidate. So that's correct.

Now, implementation details:

   Precompute the graph: for each vertex, store a list of tuples (u, b, c) for each incoming edge.

   Then, set low = 0.0, high = max_ratio (which is max(beauty_i / cost_i for each edge) + 1? Or just set high to 10000.0? Since max_ratio is at most 10000, we can set high = 10000.0.

   Then, for a fixed number of iterations (say, 100), do:

        mid = (low + high) / 2.0
        dp[1] = 0.0
        for i from 2 to N:
            dp[i] = -10**18   # a very small number
        for v in range(2, N+1):
            for (u, b, c) in graph[v]:
                # if dp[u] is not -inf, then update
                candidate = dp[u] + b - mid * c
                if candidate > dp[v]:
                    dp[v] = candidate
        if dp[N] >= 0:
            low = mid
        else:
            high = mid

   Then, after the iterations, print low.

But wait: we are updating dp[v] only from one incoming edge at a time. We are taking the maximum candidate from any incoming edge. That is correct.

However, note: a vertex might have multiple incoming edges, and we want the maximum path to that vertex. So we initialize dp[v] to -inf, then for each incoming edge, we compute candidate and take the max. That is correct.

But what if we have multiple paths to a vertex? We are considering each incoming edge and the path that ends at that edge. Since the graph is a DAG, and we process in increasing order, we have computed the maximum dp[u] for each u already. Then for v, we look at all edges (u->v) and update dp[v] = max(dp[v], dp[u] + weight). That's standard.

But note: the weights can be negative? Yes, so we need to consider the maximum even if negative. But we initialize to -inf and update with max.

But one issue: we are using a very small number for -inf. Since the maximum absolute value: the weight per edge can be as low as (1 - 10000*1) = -9999, and the path can have up to N-1 edges (200,000). So the minimum total weight could be about -9999 * 200000 = -2e9. So we set initial dp to -10**18? That is safe.

Alternatively, we can set to -10**18 for all except dp[1]=0.

But in the update, we only update if we have an incoming edge. For a vertex that has no incoming edge, it remains -10**18. Then, if we have an edge from that vertex to another, we would be adding to -10**18, which is still very negative. But that's okay because we are taking max.

Now, testing with the examples.

Example 3: 
        Input: 
        10 20
        ... (20 edges)

        Expected output: 1.8333333333333333

        Let me test with mid=1.8333333333333333:

        For each edge, compute weight = b - mid * c.

        Then, we need to compute the maximum path from 1 to 10. The path that gives the ratio 1.833333... should have total weight 0.

        How to know? The problem states that the answer is 1.833333... so the total weight for the optimal path should be 0.

        But without knowing the exact path, we can only trust the algorithm.

        However, we can run the algorithm and check.

But we'll code and test with the examples.

But note: the graph is a DAG, so the algorithm should be correct.

Now, let me write the code accordingly.

But one more thing: the problem constraints say that the graph has at least one path from 1 to N, so we don't worry about no path.

However, in the DP, we set dp[1]=0, and then we process from 2 to N. For each vertex, we update from incoming edges. Then, if there's no path to N, dp[N] would be -inf. But the problem states that there is at least one path, so we are safe.

But in the binary search, we check if dp[N] >=0. So if we set the initial high to max_ratio, then the first iteration (mid = max_ratio) would set each edge weight as (b - max_ratio*c) which is <=0. The best path would be 0 (if we take an edge with ratio exactly max_ratio) or negative. So if we set high to max_ratio, then the first mid might have dp[N] = 0? Only if the path that uses the edge with max_ratio alone? But if the path is not a single edge, then it might be negative. So we have to set high to the maximum possible ratio, which is the max_ratio? Actually, the maximum ratio of a path is at most the maximum edge ratio. So if we set high = max_ratio, then the condition for mid = max_ratio: the weight for the edge with max_ratio is 0, and if we have a path that only uses that edge (if it goes from 1 to N) then dp[N]=0. Otherwise, if the max_ratio edge is not on a path from 1 to N, then we might get a negative value. But then we set high = mid, and we'll get the next lower. But we know the maximum ratio for a path is at most the maximum edge ratio. So setting high to max_ratio is safe. Alternatively, we can set high = max_ratio + 1? Then we can avoid the case where the max_ratio is not achieved by any path. But actually, the maximum ratio for a path is at most max_ratio, so if we set high = max_ratio + 1, then the condition for mid = max_ratio + 1: all edges are negative, so dp[N] is negative, and we set high = mid, then eventually we get the correct ratio. But to be safe, we can set:

   low = 0.0
   high = max_ratio + 1.0   # where max_ratio = max(beauty_i / cost_i for each edge)

But then the number of iterations: 100 is enough.

So steps:

   1. Precompute:
        graph = [[] for _ in range(N+1)]
        max_ratio = 0.0
        for each edge (u, v, b, c) in edges:
            graph[v].append( (u, b, c) )
            ratio = b / c
            if ratio > max_ratio:
                max_ratio = ratio

        high0 = max_ratio + 1.0

   2. Set low = 0.0, high = high0
   3. For _ in range(100):   # 100 iterations
            mid = (low + high) / 2.0
            dp = [-10**18] * (N+1)
            dp[1] = 0.0
            for v in range(2, N+1):
                for edge in graph[v]:
                    u, b, c = edge
                    # if dp[u] is not -10**18, then we can update
                    # but even if it is, we can update? It's -inf, then adding will be -inf, which is less than any candidate we have? So we can update without checking.
                    candidate = dp[u] + b - mid * c
                    if candidate > dp[v]:
                        dp[v] = candidate
            if dp[N] >= 0:
                low = mid
            else:
                high = mid

   4. Print low

But wait: what if the path has multiple edges? We are updating for each vertex in increasing order, and each edge is processed once. This should be correct.

Now, let me test with example 2: 
        N=3, M=3, edges: [1,3,1,1], [1,3,2,1], [1,3,3,1]

        Precompute: max_ratio = max(1/1, 2/1, 3/1)=3, so high0=4.

        Then, we do binary search.

        Iterations: 100, so we'll get low=3.0 at the end? Actually, at the last step, we set low=mid when dp[N]>=0.

        For mid=3.0: 
            dp[1]=0
            for v=3: 
                edge1: candidate = 0 + 1 - 3*1 = -2
                edge2: candidate = 0 + 2 - 3*1 = -1
                edge3: candidate = 0 + 3 - 3*1 = 0
            so dp[3]=0 -> set low=3.0

        Then we continue: since we do 100 iterations, the value of low will converge to 3.0.

        Then we output 3.0.

But what about the next iteration? After setting low=3.0, then mid becomes (3.0+4.0)/2=3.5. Then for mid=3.5, candidate for the third edge is 3-3.5=-0.5, so dp[3]=max of negatives = -0.5? Then set high=3.5. Then low remains 3.0. Then next mid = (3.0+3.5)/2=3.25, then check: for the third edge: 3-3.25*1=-0.25 -> negative, so set high=3.25. Then mid=3.125, then negative again. But after 100 iterations, the low and high will be very close, but the answer we output is low? Actually, we output low at the end. But the condition for the last iteration: we set low to the last value that had dp[N]>=0. However, in our loop, we set low=mid when the condition holds. Then at the end, low is the maximum ratio we found. But we want the maximum ratio that is achievable. And we are storing that in low.

But actually, we are doing:

   if dp[N] >=0: 
        low = mid   # meaning we can try a higher ratio
   else:
        high = mid

So we are maintaining that low is always achievable. Then at the end, we output low.

But after 100 iterations, the value of low is the one we want.

Alternatively, we can output (low+high)/2 at the end? But in our code, we set low to mid when condition holds. Then at the end, we output low. But actually, the last update might have set low to the last mid that was achievable, and then we break. But we are not breaking, we are doing 100 iterations. Then the low and high will be very close, and we can output either? Actually, we output low, which is the last value that we know is achievable. But after 100 iterations, the difference is negligible.

But to be safe, we can output low. Or we can output the last mid that was achievable? Actually, we are storing the last mid that was achievable in low. So output low.

But let me test with a simple case: 
        Suppose we have one edge: 1->2 with b=1, c=1. Then the ratio is 1.0.

        max_ratio=1.0, so high0=2.0.

        Iteration1: mid = (0+2)/2=1.0 -> check: dp[2] = 0 + 1 - 1.0*1 = 0 -> set low=1.0.
        Then next iterations: high becomes 2.0, then mid becomes (1+2)/2=1.5 -> then weight = 1-1.5=-0.5 -> set high=1.5.
        Then mid = (1+1.5)/2=1.25 -> weight=-0.25 -> set high=1.25.
        ... after 100 iterations, low is still 1.0. So we output 1.0.

        Correct.

Therefore, the algorithm is:

   Precompute the graph and max_ratio.
   low, high = 0.0, max_ratio + 1.0
   for _ in range(100):
        mid = (low+high)/2.0
        dp = array of -10**18 for indices 1..N
        dp[1]=0
        for v from 2 to N:
            for each incoming edge (u, b, c) to v:
                candidate = dp[u] + b - mid*c
                if candidate > dp[v]:
                    dp[v] = candidate
        if dp[N] >= 0:
            low = mid
        else:
            high = mid

   print(low)

But we have to be cautious: the graph might have multiple edges, and we are storing for each vertex the list of incoming edges.

Now, let me test with example 1 to see if the code runs.

But I'll write the code accordingly.

One more thing: we are using a list for dp. The indices are from 1 to N.

But in Python, we have to initialize dp for 1-indexed. So dp[0] is unused? We have vertices 1 to N.

So:

   dp = [-10**18] * (N+1)   # 0-indexed: index0 unused, we use 1..N

   Then dp[1]=0.0

   Then for v from 2 to N+1? Actually, for v in range(2, N+1).

Now, time: 20e6 iterations in Python: let me see in practice. But the constraints are 200,000 * 100 = 20e6, which is acceptable in Pyton if we write in Pyton and run in Pyton with PyPy or in C++ but in Pyton it might be 1-2 seconds? In Pyton, 10e6 operations per second? So 20e6 might be 2 seconds. But worst-case 100 iterations, so 200 seconds? That's too slow.

Wait, no: 200,000 edges per iteration, 100 iterations: 20,000,000 edges processed. Each edge: a few operations (fetch u, b, c; compute candidate; update dp[v]). So about 20e6 operations. In Pyton, if each operation is 1e-7 seconds, then 2 seconds. But worst-case in Pyton, it might be 10e6 per second? Then 20e6 is 2 seconds. So acceptable.

But we have to optimize the inner loop. We can avoid using a list of lists for graph? We have precomputed graph, which is a list of lists. Then, in the inner loop, we iterate over graph[v] for each v from 2 to N.

But the total edges is M, so the inner loop over v from 2 to N: for each v, we iterate over graph[v]. So the total is M. So overall per iteration, O(M).

Now, let me write the code accordingly.

But note: the problem constraints: N, M up to 200,000. So 100 * 200,000 = 20,000,000 iterations. In Python, we can do that.

But to make it faster, we can use local variables and avoid global lookups. We can also precompute the list for vertices 2 to N that have at least one incoming edge? But the graph already stores only the vertices that have incoming edges. So we can iterate over v from 2 to N, and if graph[v] is not empty, then we iterate. But we are already storing the graph.

Alternatively, we can precompute the order of vertices? But we are processing in increasing order.

Now, code:

   import sys

   def main():
        data = sys.stdin.read().split()
        if not data: 
            return
        it = iter(data)
        n = int(next(it)); m = int(next(it))
        edges = []
        graph = [[] for _ in range(n+1)]
        max_ratio = 0.0
        for i in range(m):
            u = int(next(it)); v = int(next(it)); b = int(next(it)); c = int(next(it))
            edges.append((u, v, b, c))
            graph[v].append((u, b, c))
            ratio = b / c
            if ratio > max_ratio:
                max_ratio = ratio

        low = 0.0
        high = max_ratio + 1.0   # to cover the max_ratio itself

        # We'll do 100 iterations
        # Use dp array for each vertex
        # Preallocate dp as a list of size (n+1)
        # We'll reuse the dp array? Actually, we can create a new one for each iteration? But 100 * (n+1) = 100*200001 ~20e6 integers? But that's 20e6*8 bytes? 160e6 bytes? 160 MB? That might be acceptable.

        # Alternatively, we can create a list and then reset each element to -10**18 for each iteration? But we are creating a new list each time? 
        # We'll create a new dp for each iteration.

        for iteration in range(100):
            mid = (low + high) / 2.0
            dp = [-10**18] * (n+1)   # 0-indexed, index0 unused
            dp[1] = 0.0
            # process vertices from 2 to n
            for v in range(2, n+1):
                for edge in graph[v]:
                    u, b_val, c_val = edge
                    # u is the source, which is less than v
                    candidate = dp[u] + b_val - mid * c_val
                    if candidate > dp[v]:
                        dp[v] = candidate

            if dp[n] >= 0:
                low = mid
            else:
                high = mid

        print("{:.15f}".format(low))

But wait: what if there are multiple edges to the same vertex? The inner loop for a vertex v: we iterate over all edges to v and update dp[v] to the maximum candidate. That is correct.

But let me test with the example 1: 
        We know the answer is 0.75.

        After 100 iterations, we output 0.75.

But in the binary search, we set low to 0.75 at the first time we check mid=0.75? Then we set low=0.75 and then we set mid= (0.75 + 1.0)/2? Then we check mid=0.875, which fails, then set high=0.875, and then next mid= (0.75+0.875)/2, etc. But after 100 iterations, the value of low will be very close to 0.75? Actually, the algorithm will converge to 0.75? Because 0.75 is the exact answer.

But when we check mid=0.75, we get dp[5]=0, which is >=0, so we set low=0.75. Then, even if we do more iterations, low remains 0.75? Because when we set mid between 0.75 and high, and then if that mid fails, we set high to that mid, but we never set low to a value above 0.75? Then the last low is 0.75.

But then we output 0.75.

So it's correct.

But what if the answer is not exactly representable? Then we have to output with 15 decimal places.

Now, let me test with the example 3. The expected output is 1.8333333333333333.

        How much is 11/6? 1.8333333333333333 -> 11/6 = 1.8333333333333333...

        So the algorithm should converge to 11/6.

        How? The path that achieves 11/6? The problem says the answer is 1.8333333333333333.

        We don't know the exact path, but we can trust the algorithm.

Potential issues: 
        - The graph might have multiple paths, and we are taking the maximum weight path for a given mid. This is correct.

        - The binary search: we are doing 100 iterations, which is sufficient for 1e-9 precision.

        - The DAG: we process in increasing order of vertex indices, which is topological order.

But note: the graph is given with u_i < v_i, so the topological order is 1,2,...,N.

But what if there are edges that skip vertices? For example, an edge from 1 to 3, then from 3 to 5. Then when processing vertex 3, we use the edge from 1 to 3. Then when processing vertex 5, we use the edge from 3 to 5. So it's correct.

Now, one more thing: the graph might have multiple edges from the same u to the same v? Yes, and we are storing all of them. Then for each vertex v, we check all edges to v. So we take the maximum candidate from any incoming edge. That is correct.

Therefore, I'll implement the above.

But let me check the performance: 100 * M = 100 * 200000 = 20e6, which is acceptable in Pyton? In Pyton, 20e6 iterations should run in about 1-2 seconds. But we are doing floating point operations. And we are storing the graph as a list of lists. 

But the numbers: M=200,000, so the graph has 200,000 edges. The total memory is about 200,000 * (tuple of (int, int, int))? Each tuple is 3 integers -> 24 bytes? Then 200,000 * 24 = 4.8e6 bytes. Then the dp array is 200,001 * 8 bytes (for double) = 1.6e6 bytes. So total memory is acceptable.

Now, code:

   We'll read the input, build the graph, then run the binary search.

   But note: the graph building: we store for each vertex v, the list of incoming edges. So for each edge (u, v, b, c), we append (u, b, c) to graph[v].

   Then run the binary search.

Let me run the example 3: 
        Input: 10 20, then 20 lines.

        We have to compute the max_ratio: which is the max of b_i/c_i over the 20 edges.

        Then run the binary search.

        The answer should be 1.8333333333333333.

But we don't know which path? But we can assume the algorithm will find the path that gives the maximum ratio.

But we'll code and test with the provided examples.

But since we cannot run now, we trust the logic.

Now, edge case: if there's only one edge from 1 to N. Then the ratio is b/c. Then the algorithm should output b/c.

Another edge case: two edges from 1 to 2 and 2 to N, and one direct from 1 to N. Then we have to compare the ratios.

But the algorithm will consider all paths: the direct path and the two-edge path.

Now, I think the algorithm is correct.

But one more thing: the initialization of dp[1]=0. Then for vertex 1, we don't have any incoming edge? So that's correct.

But what about edges that start at 1? They are stored in graph[v] for v>1. Then when processing v (which is >=2), we use dp[1] which is 0.

So it's correct.

Now, let me write the code accordingly.

But note: we are using floating point for mid and the weights. The problem says absolute or relative error <= 1e-9. With 100 iterations, the binary search will have error (high0 - 0) / 2^100, which is about 1e-30, so more than enough.

But the floating point precision? The weights are computed as b - mid * c. Since b and c are integers up to 10000, and mid is a double, we might have precision issues? But the range of mid is [0, 10001], and the weights can be as large as 10000 and as low as -10000*10000? But we are storing in doubles, which have about 15-17 decimal digits. So we are safe.

But to be cautious, we can use the absolute value for the condition? But we are checking dp[N] >=0, which is exact? Actually, the computation might have floating point errors. For example, if the true value is 0, but we compute a very small negative due to rounding, then we set high=mid, and then the next iteration we set low=mid? Then we might lose the exact value.

But we are storing the last mid that had a non-negative path in 'low'. So if due to rounding we get a negative for a mid that should be non-negative, we set high=mid, and then in the next iterations, we might set low to a value slightly below the true ratio. But then we output low, which is the last value that we know is achievable. However, the problem says that the output must be within 1e-9. So if we output a value that is slightly below the true maximum, but within 1e-9, that's acceptable.

But to avoid that, we can use a tolerance? Actually, we are doing 100 iterations, which is enough to get 1e-9 precision. And the problem requires 1e-9 error. So we don't need to change.

Alternatively, we can check: if dp[N] >= -1e-9, then set low=mid? But that might set low to a ratio that is slightly above the true maximum? Because we want the maximum ratio such that there exists a path with (sum b) / (sum c) >= x. But if we set low=mid when dp[N] >= -1e-9, then we might set low to a value that is above the true maximum? Then the output would be above the true maximum, which might break the requirement.

So we stick to the exact comparison: if dp[N] >=0, set low=mid.

But the problem: when the true total weight is 0, but due to floating point, we get a negative, then we set high=mid, and then the next iterations we'll set low to a value below the true maximum? Then the output low would be below the true maximum, but within the required precision? Because we do 100 iterations, the error from binary search is negligible, but the floating point error might be the issue.

To mitigate, we can use a tolerance? Actually, we can check if dp[N] is close to zero? But the problem says the output must be within 1e-9 of the true answer. So if the true ratio is x0, and we output x, then |x - x0| <= 1e-9. 

But the algorithm: the true maximum ratio is x0. Then for any x < x0, we have a path with positive total weight (for the weight function with x). For x > x0, the maximum total weight is negative. But at x=x0, the total weight is 0. So if we set low to the last x that had non-negative total weight, then we output low = x0? But due to floating point, we might not get exactly 0 at x0? 

But in the example 1, we got exactly 0.0 at mid=0.75. But what if the true total weight is 0, but we get -0.0000000000000001? Then we set high=mid, and then the next iterations will set low to a value below x0? Then the final low might be the last mid that was below x0? Then the output would be that low, which is below x0. But then the error is (x0 - low) which is the step size of the last iteration? 

But we do 100 iterations, so the step size is (max_ratio+1) / 2^100, which is about 10000 / 2^100, which is negligible (much less than 1e-9). So even if we output low that is the last mid that passed the test, which is x0 - delta, the delta is negligible. Therefore, we are safe.

So I'll stick to the exact condition.

Now, let me write the code.

But note: the problem constraints: N up to 200000, M up to 200000. So we have to be efficient in memory and time.

We'll build the graph as a list of lists. The memory: each list element for an edge is about 24 bytes? (three integers: 12 bytes? But in Pyton, integers are 24 bytes? Actually, in Pyton, integers are objects, so they are larger. But for 200000 edges, each edge stored once, so 200000 * (one list element: which is a tuple of three integers) -> each tuple: 3 * 28 bytes? (for 64-bit, each int is 24 bytes? Actually, in Pyton, an integer is 24 bytes, and a tuple of three integers: 3*24 + overhead? Let me not worry: 200000 * 100 bytes = 20e6 bytes = 20 MB? Acceptable.

So the code:

   import sys

   class Solution:
        def solve(self, N: int, M: int, edges: List[List[int]]) -> float:
            # Build graph: for each vertex, store incoming edges as (u, b, c)
            graph = [[] for _ in range(N+1)]
            max_ratio = 0.0
            for edge in edges:
                u, v, b, c = edge
                # u->v
                graph[v].append((u, b, c))
                ratio = b / c
                if ratio > max_ratio:
                    max_ratio = ratio

            low = 0.0
            high = max_ratio + 1.0   # so that we cover max_ratio

            # Do 100 iterations of binary search
            # We'll use a dp array for each iteration
            for it in range(100):
                mid = (low + high) / 2.0
                # Initialize dp: for vertices 1..N, dp[1]=0, others -10**18
                dp = [-10**18] * (N+1)   # 0-indexed, index0 unused
                dp[1] = 0.0
                # Process vertices from 2 to N
                for v in range(2, N+1):
                    # For each incoming edge to v
                    for u, b_val, c_val in graph[v]:
                        # u is less than v
                        candidate = dp[u] + b_val - mid * c_val
                        if candidate > dp[v]:
                            dp[v] = candidate

                if dp[N] >= 0:
                    low = mid
                else:
                    high = mid

            return low

But we have to format the output to 15 decimal places. But the solve function returns the float, and the caller prints with formatting.

But the starter code has a function solve, so we return low.

But the problem: the problem constraints say that the graph is directed and u_i < v_i. So we are safe.

Now, test with the examples.

Example 1: 
        We know the answer is 0.75.

        The code should return 0.75.

Example 2: returns 3.0.

Example 3: returns 1.8333333333333333.

But to be sure, we can run the code with the examples.

But I'll write a test function.

Alternatively, we can submit and hope.

But let me test with example 1 in code.

But since we are in an interview setting, we'll assume correctness.

One more potential issue: the graph might have a vertex that has no incoming edge? Then we leave it as -10**18. Then if there's an edge from that vertex, then we update the next vertex using candidate = -10**18 + ... which is still -10**18. So that's acceptable.

Now, I think the solution is complete.

But what about multiple edges to the same vertex? We are updating correctly: we take the maximum candidate from any incoming edge.

So I'll write the code accordingly.

But note: the graph is built for each vertex v, storing incoming edges. Then we process v from 2 to N, and for each incoming edge, we update dp[v]. This is correct.

Let me run a small example with two edges to the same vertex:

        Example: 
          3 2
          1 3 10 1   -> edge1: beauty=10, cost=1
          2 3 1 1    -> edge2: beauty=1, cost=1
          And an edge from 1 to 2: 1 2 1 1

        Then the paths to 3: 
           1->3: ratio=10
           1->2->3: ratio=(1+1)/(1+1)=1

        So the maximum ratio is 10.

        In the algorithm: 
          max_ratio = max(10, 1, 1) = 10
          Then we set high=11.

          Then we check mid= (0+11)/2=5.5 -> 
                dp[1]=0
                for v=2: 
                    edge: 1->2: candidate = 0+1-5.5*1 = -4.5 -> dp[2]=-4.5
                for v=3:
                    edge1: 1->3: candidate=0+10-5.5=4.5 -> dp[3]=4.5
                    edge2: 2->3: candidate=dp[2]+1-5.5 = -4.5+1-5.5=-9 -> so dp[3] remains 4.5
                dp[3]>=0 -> set low=5.5

          Then we do next iteration: mid = (5.5+11)/2=8.25
                for v=2: candidate=0+1-8.25*1=-7.25
                for v=3: from 1: 10-8.25=1.75 -> dp[3]=1.75 -> set low=8.25

          Then mid=(8.25+11)/2=9.625
                for v=3: from 1: 10-9.625=0.375 -> set low=9.625

          Then mid=(9.625+11)/2=10.3125
                for v=3: from 1: 10-10.3125 = -0.3125 -> set high=10.3125

          Then mid=(9.625+10.3125)/2=9.96875
                for v=3: from 1: 10-9.96875=0.03125 -> set low=9.96875

          ... and after 100 iterations, it converges to 10.0.

        But the true maximum is 10.0. So the algorithm should return 10.0.

        However, we set low to 9.96875 and then continue, but eventually we will get low=10.0? Actually, we do 100 iterations, so the value will be very close to 10.0.

        But note: when mid=10.0, candidate for edge1: 10-10.0=0, so we set low=10.0.

        How? 
          At the iteration when mid=10.0, which we set as (9.96875+10.3125)/2? Actually, no: we set low=9.96875 and high=10.3125. Then mid = (9.96875+10.3125)/2=10.140625? 

        Actually, we set low=9.96875 and then next mid = (9.96875+10.3125)/2=10.140625, which fails, then set high=10.140625. Then mid = (9.96875+10.140625)/2=10.0546875, then fails, then set high=10.0546875. Then mid = (9.96875+10.0546875)/2=10.01171875, then fails? 

        How do we get to 10.0? 

        Actually, we don't set mid to 10.0 exactly. But we set low=9.96875, which is less than 10.0. Then the next mid is 10.140625, which is above 10.0. Then we set high=10.140625. Then next mid is 10.0546875, which is above 10.0. Then we set high=10.0546875. Then next mid = (9.96875+10.0546875)/2=10.01171875, which is above 10.0. Then we set high=10.01171875. Then next mid = (9.96875+10.01171875)/2=9.990234375, which is below 10.0. Then we set low=9.990234375? 

        But then the value of low is 9.990234375, and high=10.01171875. Then we continue.

        But after 100 iterations, the low and high will be very close to 10.0? 

        However, we require absolute error 1e-9. After 100 iterations, the error is (11)/2^100, which is about 1e-30, so the value of low will be within 1e-9 of 10.0.

        So we output low, which is about 10.0 - a tiny bit? But the problem allows absolute or relative error <=1e-9. So it's acceptable.

        But note: the condition for setting low is that the path exists with total weight>=0. For mid=10.0, we have the edge1: weight=0, so we set low=10.0. Why didn't we set low to 10.0 in the iteration that sets mid=10.0?

        How do we get mid=10.0? 
          We set mid = (low+high)/2.0. 
          Initially, low=0, high=11 -> mid=5.5 -> set low=5.5
          Then low=5.5, high=11 -> mid=8.25 -> set low=8.25
          Then low=8.25, high=11 -> mid=9.625 -> set low=9.625
          Then low=9.625, high=11 -> mid=10.3125 -> set high=10.3125 (because at mid=10.3125, the weight for edge1: 10-10.3125<0, so set high)
          Then low=9.625, high=10.3125 -> mid=9.96875 -> set low=9.96875 (because at mid=9.96875, the weight for edge1: 10-9.96875>0)
          Then low=9.96875, high=10.3125 -> mid=10.140625 -> set high=10.140625 (because 10-10.140625<0)
          Then low=9.96875, high=10.140625 -> mid=10.0546875 -> set high=10.0546875
          Then low=9.96875, high=10.0546875 -> mid=10.01171875 -> set high=10.01171875
          Then low=9.96875, high=10.01171875 -> mid=9.990234375 -> set low=9.990234375 (because 10-9.990234375>0)
          Then low=9.990234375, high=10.01171875 -> mid=10.0009765625 -> set high=10.0009765625 (because 10-10.0009765625<0)
          Then low=9.990234375, high=10.0009765625 -> mid=9.99560546875 -> set low=9.99560546875
          ... and so on.

        But we never set mid=10.0. However, the true maximum ratio is 10.0. But the algorithm will set low to the maximum mid that is achievable. The last mid that was achievable is 9.99560546875? Then we output 9.99560546875? That is 0.00439453125 away from 10.0, which is not within 1e-9.

        This is a problem.

        Why didn't we set low=10.0? Because we never checked mid=10.0. 

        How can we check mid=10.0? We would have to have low=9.999999 and high=10.000001, then mid=10.0. Then we check and set low=10.0.

        But in the above sequence, we never hit 10.0 exactly.

        However, we can change the binary search to include the exact value? Actually, the condition for low is set only when we have a path that is non-negative. So we need to check mid=10.0 to set low=10.0.

        But with floating point, we might never hit 10.0 exactly.

        Solution: we can set the initial high to max_ratio + 1.0, and then the true maximum ratio is in [0, max_ratio]. But we are doing binary search on a continuous value. And the condition is set by a continuous function. So we can set the number of iterations to 100, and then output low. But the true maximum ratio is x0, and we are setting low to the last mid that was achievable. The last mid that was achievable is at least x0? Actually, no: we set low=mid only when the path has weight>=0 for that mid. And for any mid <= x0, the path has weight>=0? 

        Actually, the function f(x) = max_path(beauty - x*cost) is monotonic: as x increases, the weight decreases. And at x=x0, f(x0)=0. For x < x0, f(x)>=0? Actually, if x < x0, then the ratio x is less than the maximum ratio, so there exists a path that has ratio at least x0, so the total weight for x would be positive? 

        Actually, no: the function f(x) is the maximum over paths of (sum b - x * sum c). And for x < x0, we know that the path that achieves x0 has (sum b - x * sum c) = (sum b - x0 * sum c) + (x0 - x)*sum c = 0 + (x0-x)*sum c >0. So yes, f(x) >0 for x < x0. And f(x0)=0. And for x>x0, f(x)<0.

        Therefore, the last mid for which f(mid)>=0 is the maximum x such that f(x)>=0, which is x0. But we set low to mid only when f(mid)>=0. Then at the end, low is the last mid that had f(mid)>=0. But in the iterations, we set low=mid when f(mid)>=0, and then we set high=mid when f(mid)<0. Then the value of low is always the last mid that passed the test. And when we break, low is the maximum ratio we can achieve.

        But in the example above, we set low to 9.99560546875, which is less than x0=10.0. Why? Because we never tested any mid between 9.99560546875 and 10.0 that passes? But we did: the next mid is 10.0009765625, which fails, so we set high=10.0009765625. Then the next mid is (9.99560546875+10.0009765625)/2=9.998291015625, which is less than 10.0, and f(9.998291015625) = 10 - 9.998291015625 * 1 = 0.001708984375 >0, so we set low=9.998291015625.

        Then the next mid = (9.998291015625+10.0009765625)/2=9.9996337890625 -> set low=9.9996337890625
        Then mid= (9.9996337890625+10.0009765625)/2=10.00030517578125 -> fails, set high=10.00030517578125
        Then mid= (9.9996337890625+10.00030517578125)/2=9.999969482421875 -> set low=9.999969482421875
        Then mid= (9.999969482421875+10.00030517578125)/2=10.000137329101562 -> fails, set high=10.000137329101562
        Then mid= (9.999969482421875+10.000137329101562)/2=10.000053405761719 -> fails, set high=10.000053405761719
        Then mid= (9.999969482421875+10.000053405761719)/2=10.000011444091797 -> fails, set high=10.000011444091797
        Then mid= (9.999969482421875+10.000011444091797)/2=9.999990463256836 -> set low=9.999990463256836

        Then we are getting closer to 10.0. And after 100 iterations, we'll be very close.

        But we output low, which is 9.999990463256836, which is about 9.5e-6 away from 10.0. But we require 1e-9. How many iterations do we need to get 1e-9?

        The initial range is 0 to 11 (or max_ratio+1). The number of iterations needed: log2((11)/1e-9) = log2(11e9) ≈ log2(10^10) = about 33.2, so 34 iterations? But we do 100, which is more than enough.

        Actually, the binary search on a continuous value: the error after k iterations is (high0 - low0)/2^k. Here, high0 - low0 = max_ratio+1, which is at most 10001. Then 10001/2^k <= 1e-9 -> 2^k >= 10001e9 -> k >= log2(10001e9) ≈ log2(10^13) = about 43. So 50 iterations should be enough. We do 100, so it's safe.

        Therefore, the final output will be within 1e-9.

        So I'll keep 100 iterations.

        Now, the code is ready.

        Let me write the final code.

        Note: we are using a list for dp, and we are processing vertices in increasing order.

        One optimization: we can break early if we are processing a vertex that has no incoming edge? But we already stored graph[v] for each v. If graph[v] is empty, then we skip. But in the for loop for v, we iterate over graph[v]. So if graph[v] is empty, we do nothing. That is efficient.

        Now, we'll write the code accordingly.

        But note: the graph might have vertices that are not connected to 1? But the problem states there is at least one path from 1 to N. But there might be other vertices that are not on any path from 1 to N? Then we don't care. But in the dp, they remain -inf, and then when we update their outgoing edges, we use -inf, which is okay.

        So the code:

        import sys
        from typing import List

        class Solution:
            def solve(self, N: int, M: int, edges: List[List[int]]) -> float:
                graph = [[] for _ in range(N+1)]
                max_ratio = 0.0
                for e in edges:
                    u, v, b, c = e
                    graph[v].append((u, b, c))
                    ratio = b / c
                    if ratio > max_ratio:
                        max_ratio = ratio
                
                low = 0.0
                high = max_ratio + 1.0
                # Do 100 iterations
                for _ in range(100):
                    mid = (low + high) / 2.0
                    dp = [-10**18] * (N+1)  # 0-indexed, index0 unused.
                    dp[1] = 0.0
                    for v in range(2, N+1):
                        for u, b_val, c_val in graph[v]:
                            candidate = dp[u] + b_val - mid * c_val
                            if candidate > dp[v]:
                                dp[v] = candidate
                    if dp[N] >= 0:
                        low = mid
                    else:
                        high = mid
                return low

        Then the caller will format to 15 decimal places.

        But the problem: the problem constraints say that the graph is directed and u_i < v_i, so we are safe.

        Now, I'll test with the provided examples.

        Example 1: 
            Input: 
                5 7
                1 2 3 6
                1 3 9 5
                2 3 1 5
                2 4 5 3
                2 5 1 9
                3 4 4 8
                4 5 2 7

            Output: 0.75

        Example 2: 
            Input: 
                3 3
                1 3 1 1
                1 3 2 1
                1 3 3 1
            Output: 3.0

        Example 3: 
            Input: 
                10 20
                ... (given)
            Output: 1.8333333333333333

        I think it should work.

        But we have to run example 3 to be sure? But we can rely on the logic.

        Therefore, we return the code.

        However, note: the initial dp for vertex 1 is 0.0, and then we process from 2 to N. And for vertex 1, there are no incoming edges? So it's correct.

        Let me test with example 2: 
            graph for vertex 3: three edges: (1,1,1), (1,2,1), (1,3,1)
            Then for v=3, we consider all three edges. For mid=3.0, the candidate for the third edge: 0+3-3.0=0.0 -> dp[3]=0.0, then set low=3.0.

        So the algorithm will work.

        Now, we output the code.

        Note: we are using a large negative for initialization: -10**18. This is to avoid underflow? But the minimum value we can get is about -10000 * 200000 = -2e9, so -10**18 is safe.

        Therefore, final code.

## Final Solution

To solve this problem, we need to find the maximum ratio of total beauty to total cost for any path from vertex 1 to vertex N in a directed graph where each edge has both beauty and cost attributes. The solution leverages binary search combined with dynamic programming on a directed acyclic graph (DAG) to efficiently compute the desired ratio.

### Approach
1. **Problem Analysis**: The problem requires finding a path from vertex 1 to vertex N such that the ratio of the sum of beauties to the sum of costs along the path is maximized. Given the constraints (N and M up to 200,000), a brute-force approach is infeasible. Instead, we use binary search to determine the maximum possible ratio.
2. **Binary Search Setup**: The binary search is performed over possible ratios. The initial search range is set from 0 to the maximum ratio of any single edge (plus a small buffer). For each candidate ratio `mid`, we check if there exists a path where the total beauty minus `mid` times the total cost is non-negative.
3. **Dynamic Programming on DAG**: The graph is a DAG because each edge goes from a lower to a higher vertex (u_i < v_i). We process vertices in topological order (from 1 to N). For each vertex, we compute the maximum value of `(beauty - mid * cost)` for paths ending at that vertex using dynamic programming.
4. **Checking Feasibility**: For each candidate ratio `mid`, we compute the maximum path value from vertex 1 to vertex N. If this value is non-negative, it means the ratio `mid` is achievable, and we adjust the binary search range to explore higher ratios. Otherwise, we explore lower ratios.
5. **Convergence**: After sufficient iterations (100 in this case), the binary search converges to the maximum ratio within the required precision (1e-9).

### Solution Code
```python
from typing import List

class Solution:
    def solve(self, N: int, M: int, edges: List[List[int]]) -> float:
        graph = [[] for _ in range(N+1)]
        max_ratio = 0.0
        for e in edges:
            u, v, b, c = e
            graph[v].append((u, b, c))
            ratio_val = b / c
            if ratio_val > max_ratio:
                max_ratio = ratio_val
        
        low = 0.0
        high = max_ratio + 1.0
        
        for _ in range(100):
            mid = (low + high) / 2.0
            dp = [-10**18] * (N+1)
            dp[1] = 0.0
            for v in range(2, N+1):
                for (u, b_val, c_val) in graph[v]:
                    candidate = dp[u] + b_val - mid * c_val
                    if candidate > dp[v]:
                        dp[v] = candidate
            if dp[N] >= 0:
                low = mid
            else:
                high = mid
        
        return low
```

### Explanation
1. **Graph Construction**: The graph is built such that each vertex stores its incoming edges along with their beauty and cost values. The maximum ratio of any single edge is also computed during this step.
2. **Binary Search**: The binary search is initialized with `low` as 0.0 and `high` as the maximum edge ratio plus 1.0. This ensures the search space covers all possible ratios.
3. **Feasibility Check**: For each midpoint ratio `mid` in the binary search:
   - **Dynamic Programming Setup**: Initialize a DP array where `dp[i]` represents the maximum value of `(sum of beauties) - mid * (sum of costs)` for paths ending at vertex `i`. The initial value for vertex 1 is 0.0, and others are set to a very low value (-10^18).
   - **Processing Vertices**: Vertices are processed in increasing order (from 2 to N). For each vertex, all incoming edges are considered to update the DP value by computing the candidate value from the source vertex of the edge.
4. **Adjusting Search Range**: If the DP value for vertex N is non-negative, it means the ratio `mid` is achievable, so the search continues for higher ratios (`low = mid`). Otherwise, the search continues for lower ratios (`high = mid`).
5. **Result**: After 100 iterations, the binary search converges to the maximum ratio, which is returned as the solution. The precision of 100 iterations ensures the result meets the required accuracy (1e-9).

This approach efficiently narrows down the possible ratios using binary search and leverages dynamic programming on a DAG to check feasibility, making it suitable for large input sizes.