# Solution for 903_D.md

Generated at: 2025-07-22 06:02:18
Model: deepseek-reasoner

## Reasoning Process

Alright, I'm going to tackle this programming problem step by step. Let me make sure I understand it completely.

The problem defines a function d(x, y) which is:
- y - x, if the absolute difference between x and y is greater than 1
- 0, if the absolute difference is 1 or less

I have to compute the sum of d(a_i, a_j) for every pair (i, j) such that i ≤ j. The array can have up to 200,000 elements, and each element can be as large as 10^9. 

First, I note that a brute-force approach where I check every pair would be O(n^2), which is too slow for n=200,000. So I need a smarter way.

Let me break down the function d(x,y). It's non-zero only when |x-y|>1. Also, note that d(x,y) = y - x when |x-y|>1, and 0 otherwise. Importantly, for each pair (i,j) with i≤j, we consider d(a_i, a_j). Since i≤j, we are considering pairs in order, but note the function is not symmetric: d(x,y) = -d(y,x). But since we only consider i≤j, we don't have to worry about both orders.

However, in the problem, the pairs are (i,j) with i≤j, so for each pair, we have the first element at index i and the second at j. So for each pair, we compute d(a_i, a_j). 

But note: when a_i > a_j, d(a_i, a_j) = a_j - a_i, which is negative. So the function can return negative values.

Looking at the examples:

Example 1: [1,2,3,1,3] -> output 4.

The pairs that contribute non-zero are:
- (1,3): d(1,3)=2 (since |1-3|=2>1 -> 3-1=2)
- (1,5): d(1,3)=2 (a1=1, a5=3 -> same as above)
- (3,4): d(3,1)=1-3 = -2 (since |3-1|=2>1 -> 1-3=-2)
- (4,5): d(1,3)=2 (a4=1, a5=3 -> 3-1=2)

So 2+2-2+2=4. Correct.

Example 2: [6,6,5,5] -> output 0.

Let me compute:
Pairs:
(1,2): d(6,6)=0
(1,3): d(6,5)=5-6=-1? But |6-5|=1 which is not >1, so 0? Actually, |6-5|=1, so 0.
Similarly, all pairs: any two same numbers: |x-y|=0 -> 0. Adjacent different: |6-5|=1 -> 0. So total 0.

Example 3: [6,6,4,4] -> output -8.

Pairs:
(1,2): d(6,6)=0
(1,3): d(6,4)= |6-4|=2>1 -> 4-6 = -2
(1,4): similarly, d(6,4)=-2
(2,3): d(6,4)=-2
(2,4): d(6,4)=-2
(3,4): d(4,4)=0

So total: -2 -2 -2 -2 = -8.

So the problem reduces to: for each pair (i,j) with i≤j, if |a_i - a_j| > 1, then add (a_j - a_i) to the sum.

But note: a_j - a_i can be positive or negative. Actually, since i≤j, the indices don't imply any order of values. So a_i can be larger or smaller than a_j.

But the condition |a_i - a_j|>1 must be satisfied, and then we add a_j - a_i.

So the expression is: 
Sum_{i<j} [ (a_j - a_i) * I(|a_i - a_j|>1) ]

But note: the problem says i≤j, so including i=j. However, when i=j, |a_i - a_j|=0, so d=0. So we can consider only i<j.

So we can rewrite the required sum as:
Sum_{i<j} { (a_j - a_i) if |a_i - a_j|>1, else 0 }

Now, how to compute this efficiently for large n?

One idea: split the sum into two parts:
Total = Sum_{i<j} [a_j * I(|a_i - a_j|>1)] - Sum_{i<j} [a_i * I(|a_i - a_j|>1)]

So if I let:
S1 = sum over all pairs (i<j) such that |a_i - a_j|>1 of a_j
S2 = sum over all pairs (i<j) such that |a_i - a_j|>1 of a_i

Then total = S1 - S2.

So I can compute S1 and S2 separately.

But how to compute these without iterating over all pairs?

I recall that in problems involving pairs and conditions on differences, sorting the array can help. Because after sorting, we can use binary search or two pointers to count pairs satisfying certain conditions.

Let me sort the array. But note: the original indices don't matter because we are summing over all pairs. So sorting is acceptable.

However, the condition |a_i - a_j|>1 is symmetric, but the contributions a_j and a_i are not. But after sorting, we can traverse the array in a way that for each element, we can quickly find the elements that are too close (within 1) and exclude them.

But note: after sorting, the array is in increasing order. So for a given element a_j (at position j), the condition |a_i - a_j|>1 becomes: 
a_i < a_j - 1 OR a_i > a_j + 1.

But since the array is sorted in increasing order, a_i > a_j + 1 would only happen for i>j? Actually, no: because we are considering pairs (i,j) with i<j (in the original array) but after sorting, the indices are rearranged. However, when we sort, we lose the original indices. But that's okay because the condition only depends on the values, and the pair (i,j) is considered regardless of original indices. 

But wait: the pair (i,j) in the original array: after sorting, we have to consider the value of a_i and a_j, regardless of their original positions. So we can sort the array and then for each element, consider it as the j-th element in the sorted array? But then we have to consider the pairs of indices in the sorted array? Actually, the problem doesn't care about the original indices, so we can consider the sorted array and then consider every pair (i,j) in the sorted array? But note: we are only considering each distinct pair of elements once? Actually, no: because the same value might appear multiple times, and each occurrence is distinct.

So we have to account for duplicates.

Therefore, after sorting, we have a sorted array b[0..n-1]. Then we can compute:

S1 = sum_{j=0}^{n-1} [ a_j * (number of i < j such that |b_i - b_j|>1) ]

But wait, no: because in the sorted array, for a fixed j (as the second element in the pair), the condition |b_i - b_j|>1 for i<j is actually: b_i <= b_j - 2? Because if b_i is less than b_j, then |b_i - b_j| = b_j - b_i. So we require b_j - b_i > 1, i.e., b_i <= b_j - 2.

Similarly, for i>j? But in the pair (i,j) we have i<j in the original array, but after sorting, we have to consider both: when the element with the smaller index in the original array has a larger value? Actually, we lost the original indices. 

But note: the problem doesn't require the original indices to be in order? The pair (i,j) in the original array: i and j are indices. However, the condition and the value of d(a_i, a_j) depend only on the values, not on the indices. So we can consider the multiset of values. But the contribution of each pair (x, y) (with x = a_i, y = a_j) is d(x,y) and each pair of indices is unique. However, the same value might appear multiple times, so we have to consider each occurrence.

Therefore, after sorting, we can traverse the array and for each element (as the j-th element in the sorted array), we want to count how many i (with i being an index in the sorted array, but i < j in the sorted array) such that |b_i - b_j|>1. But note: in the sorted array, for i<j, we have b_i <= b_j. Therefore, |b_i - b_j| = b_j - b_i. So the condition becomes b_j - b_i > 1, i.e., b_i <= b_j - 2.

Similarly, for pairs where the element at i (in the original array) is greater than the element at j? But in the sorted array, when we traverse j from 0 to n-1, we are only considering i<j (in the sorted array) meaning b_i <= b_j. So we miss the pairs where b_i > b_j? But that would be when i>j in the sorted array. So we have to consider both cases? 

Wait, no. Because the pair (i,j) in the original array: if the element at i is greater than the element at j, then in the sorted array, the element a_i would come after a_j? But then when we traverse the sorted array, we haven't considered that when we are at a_j (which is the smaller one) and then later we see a_i (the larger one), then we would have i>j in the sorted array. So in that case, when we are at a_i (the larger one) and look at the elements to the left (which are smaller), we have already considered the pair? Actually, no: because the pair (i,j) in the original array: we don't know which one is smaller. But the condition |x-y|>1 must be considered regardless.

But in the sorted array, for a given element b_j, the pairs that involve it and a smaller element are when i<j (in the sorted array) and the condition is b_j - b_i > 1. The pairs that involve it and a larger element? Actually, for the element b_j, if there is a larger element b_k (k>j) such that |b_j - b_k|>1, then when we are at b_k (in the sorted array) and we look at the elements to the left, we would consider b_j? Then we require b_k - b_j > 1. So actually, we can break the condition into two parts:

1. For each element b_j, consider the elements to the left (i<j) that are <= b_j-2. Then the contribution from these pairs (with b_j as the second element) is: for each such element, d(b_i, b_j) = b_j - b_i (because b_j > b_i and |b_j-b_i|>1). So that's positive.

2. For each element b_j, consider the elements to the left? Wait, no: if we are only going to traverse the array once, then for the larger elements, we haven't encountered them when we are at b_j. So we need to account for the pairs where b_j is the first element and there is a larger element? Actually, no: because in the pair (i,j) in the original array, the function is d(a_i, a_j). When a_i is larger than a_j, then we have d(a_i, a_j) = a_j - a_i = -(a_i - a_j). So if we consider the pair (i,j) and a_i>a_j, then it would be negative. But in the sorted array, when we are at the larger element (say b_k = a_i), and we look at the smaller elements (which are to the left) that are at least b_k+2? Wait, that doesn't make sense because the smaller elements are smaller.

Alternatively, we can note that the entire sum can be rewritten as:

Total = sum_{i<j} [ (a_j - a_i) * I(|a_i - a_j|>1) ]

But by symmetry, we can also write:

Total = - sum_{i<j} [ (a_i - a_j) * I(|a_i - a_j|>1) ]

But that doesn't help.

Alternatively, we can consider the entire set of pairs and note that the function is antisymmetric? Actually, d(x,y) = -d(y,x). But we are only considering i≤j, so we are covering each unordered pair once? Actually, no: because (i,j) with i≤j is the same as (j,i) with j≥i? But we are only considering one order.

So the above idea of splitting into S1 and S2 is good. And we can compute:

Total = S1 - S2

where 
S1 = sum_{i<j and |a_i - a_j|>1} a_j
S2 = sum_{i<j and |a_i - a_j|>1} a_i

Now, we can compute S1 as: for each j, a_j multiplied by the number of i<j (in the entire array? but we need the condition) such that |a_i - a_j|>1. But again, without the original indices, we have to be cautious.

But if we sort the array, then for each j (in the sorted array), we can define:

For S1: 
When we are at position j (in the sorted array), we want to add a_j * (number of indices i<j in the sorted array such that |b_i - b_j|>1). But note: the condition |b_i - b_j|>1 for i<j (in the sorted array) becomes b_j - b_i > 1. So we need the count of i<j with b_i <= b_j-2.

Similarly, for the pairs where i>j (in the original array) and a_i is actually less than a_j? Actually, we haven't considered those. Because in the sorted array, we are traversing from smallest to largest. For each element b_j, we only consider the pairs (i,j) where i<j in the sorted array? But that corresponds to the pairs where the element is less than or equal to b_j. But what about the pairs where the element is greater? 

In the original array, a pair (i,j) with i<j (original indices) can have a_i > a_j. Then in the sorted array, the element a_i (which is larger) would come after a_j (which is smaller). So when we are at the larger element (a_i) in the sorted array, we look at the elements to the left (which are smaller) and we see a_j. Then the condition is |a_i - a_j|>1, which is a_i - a_j>1. Then the contribution for that pair would be: in the sorted array, when we are at a_i (the larger one), we have a_j (the smaller one) to the left. Then the contribution for the pair (i,j) in the original array: d(a_i, a_j) = a_j - a_i = negative. But in the sorted array, when we are at a_i (the larger one) and we consider the element a_j (the smaller one) to the left, we are effectively considering the pair (a_j, a_i) in the sorted array? But in the original array, the pair is (i,j) with i and j being the original indices. But the function d(a_i, a_j) = a_j - a_i. Alternatively, if we consider the pair in the sorted array as (j_sorted, i_sorted) then we have the smaller element first and then the larger. Then the function d(b_j_sorted, b_i_sorted) = b_i_sorted - b_j_sorted? But in the original array, the pair was (i,j) with a_i = b_i_sorted (the larger) and a_j = b_j_sorted (the smaller), so we need d(b_i_sorted, b_j_sorted) = b_j_sorted - b_i_sorted? 

This is getting messy.

But note: the expression for the entire sum is:

Total = sum_{all pairs (i,j) with i<j} [ (a_j - a_i) * I(|a_j - a_i|>1) ]

But we can split the pairs into two sets:
1. Pairs where a_i <= a_j: then |a_j - a_i| = a_j - a_i, so condition becomes a_j - a_i>1, i.e., a_j - a_i >= 2 (since integers). Then the contribution is (a_j - a_i) * I(a_j - a_i>=2)
2. Pairs where a_i > a_j: then |a_i - a_j| = a_i - a_j>1, i.e., a_i - a_j>=2. Then the contribution is (a_j - a_i) = - (a_i - a_j). So we can write: for such pairs, the contribution is - (a_i - a_j) * I(a_i - a_j>=2)

So overall:

Total = [sum_{i<j, a_j>=a_i} (a_j - a_i) * I(a_j - a_i>=2)] + [sum_{i<j, a_j < a_i} (a_j - a_i) * I(a_i - a_j>=2)]

But note: the second term is negative. And we can combine:

Total = [sum_{i<j, a_j>=a_i+2} (a_j - a_i)] - [sum_{i<j, a_i>=a_j+2} (a_i - a_j)]

But note: in the second term, because i<j and a_i>=a_j+2, we can also write: for each pair (i,j) with i<j and a_i>=a_j+2, we subtract (a_i - a_j).

Alternatively, we can consider the entire set of pairs and note that the condition is |a_i - a_j|>=2, and the sign is determined by the relative order: if a_j>a_i, then positive; if a_j<a_i, then negative.

But after sorting the array, we can handle both cases by traversing from left to right and then from right to left? Or we can do it in one pass.

Idea: sort the array. Then, for each element, we want to compute:

- The sum of (b_j - b_i) for all i<j with b_j - b_i>=2. This is the positive part.
- The sum of (b_j - b_i) for all i<j with b_i - b_j>=2? Actually, no: because if b_i > b_j, then in the sorted array we have i>j. So we need to consider the pairs in the sorted array? Actually, in the sorted array, for a pair (i,j) with i<j, we have b_i <= b_j. So the case where a_i > a_j (in the original array) is not captured by i<j in the sorted array? 

Wait, no: in the sorted array, we have b_0 <= b_1 <= ... <= b_{n-1}. Then for any pair (i,j) in the sorted array with i<j, we have b_i <= b_j. Therefore, the case where the original array has a_i > a_j and i<j is not captured by the sorted array? 

But that's a problem. Because when we sort, we break the original indices. So we cannot use the sorted array to directly represent the original pairs? 

But note: the problem does not require the pairs to be in any order of indices. The condition and the value of d(x,y) depend solely on the values. So the entire sum is a function of the multiset of values. Therefore, the order of the original indices does not matter. 

So we can reassign the values arbitrarily? Then we can sort the array and then consider every pair of the sorted array? But note: the pair (i,j) in the original array: after sorting, we have to account for every unordered pair of elements? Actually, the problem counts every pair of indices (i,j) with i<=j. So if we have duplicate values, each occurrence is distinct.

Therefore, we can consider the sorted array and then for every pair of indices in the sorted array? But that would be O(n^2). 

Alternatively, we can use the fact that the array is sorted and use prefix sums to quickly compute the sums for the condition.

But we have to account for two things: the count of elements that are at least 2 less than the current, and the sum of those elements.

So for the positive part: for each j, we want to consider the elements i (in the sorted array) with i<j and b_i <= b_j - 2. Then the positive contribution for b_j is: 
b_j * (count of such i) - (sum of such b_i)

Similarly, what about the negative part? The negative part would come from pairs where in the original array, the element at i is greater than the element at j and |a_i - a_j|>1. But in the sorted array, when we have b_i <= b_j for i<j, we don't have any element to the left that is greater. So we haven't considered those. 

But wait: the negative part is when a_i > a_j and i<j (in the original array). In the sorted array, this corresponds to an element that is greater (a_i) being at a lower index than a_j? But that can't happen because we sorted. 

Therefore, the negative part is not captured by the sorted array? 

Alternatively, we can note that the entire expression is antisymmetric. Actually, the function d(x,y) is antisymmetric: d(x,y) = -d(y,x). And we are summing over all pairs (i,j) with i<=j. But note that i<=j in the original array, but the pair (i,j) is not symmetric: we are only considering one order.

But the condition |x-y|>1 is symmetric. So the set of pairs that contribute is symmetric, but the sign of the contribution is antisymmetric.

But since we are only considering i<=j, we are not including the pair (j,i) when j>i? Actually, we are including each unordered pair of distinct indices exactly once: the one with the smaller index first. So we are not including both orders.

Therefore, the negative part is the same as the positive part but with the roles of x and y reversed? 

But let me write the entire sum:

Total = sum_{i<j} d(a_i, a_j)

= sum_{i<j} { (a_j - a_i) if |a_j - a_i|>1, else 0 }

But we can also write:

Total = sum_{i<j} (a_j - a_i) * I(|a_j - a_i|>1)

But note: if we swap i and j, we get:

For a fixed pair of values (x,y), the contribution is d(x,y) for the pair (i,j) if i<j and a_i=x, a_j=y. But if i>j, then we wouldn't consider it. But we are only considering i<=j. 

Alternatively, we can consider the entire set of unordered pairs? But the problem is defined for ordered pairs with i<=j.

But the array is not sorted by value. So the negative part is not captured by the sorted array in the left-to-right pass.

But note: in the sorted array, we have the values in increasing order. Then, for a pair of values (x,y) with x>y, in the sorted array, the larger value x will appear after y. So when we consider the element x (the larger one) at position j in the sorted array, and we look at the elements to the left, we see y (the smaller one). Then the condition is |x-y|>1 -> x-y>1. And the contribution for the pair (i,j) in the original array: if in the original array, the index of x is i and the index of y is j, and i<j? Then d(a_i, a_j) = d(x,y) = y-x? But that would be negative. But in the sorted array, we are at x (which is at position j_sorted) and we look at y at i_sorted (which is less than j_sorted). Then the contribution is d(y,x)? But we are considering the pair (i_sorted, j_sorted) in the sorted array? That would be the pair (y,x) in the sorted array, and then d(y,x) = x - y? Because y<x, and |x-y|>1, so it should be x-y? But that's positive. But in the original array, the pair is (i,j) with a_i=x and a_j=y, so we should have d(x,y) = y-x (since |x-y|>1, and it's y-x? Actually, no: the function d is defined as d(x,y) = y-x if |x-y|>1. 

Wait, the function is: 
d(x,y) = y - x if |x-y|>1, regardless of which is larger? 

But that doesn't make sense. For example, if x=4 and y=2, then |x-y|=2>1, so d(4,2)=2-4=-2? Or is it d(2,4)=4-2=2? 

Looking back at the definition: 
d(x,y) = 
  y - x, if |x-y|>1

So for d(4,2): |4-2|=2>1, so d(4,2)=2-4=-2.

For d(2,4)=4-2=2.

So the function is not symmetric in x and y; it depends on the order of arguments.

But in the problem, for a pair (i,j) with i<=j, we have d(a_i, a_j). So if a_i=4 and a_j=2, then d(4,2)=2-4=-2.

Now, in the sorted array, we have the values sorted: [2,4]. Then for the pair in the sorted array: (2,4) at positions (0,1). Then if we consider the pair (0,1) in the sorted array, we would compute d(2,4)=4-2=2, which is positive. But in the original array, the pair (i,j) with i<j might have been (index of 4, index of 2), which would give d(4,2)=-2.

So we cannot simply ignore the original indices. The function depends on the values and the order of the indices in the original array? But the problem says: for all pairs (i,j) with 1<=i<=j<=n. So the first argument is a_i and the second is a_j.

Therefore, the value depends on the values and the order of the indices. But when we sort the array, we lose the original indices.

So we have to account for the original indices. This is a problem.

Alternative approach: we can use the fact that the entire sum can be written as:

Total = sum_{j=1}^{n} a_j * (number of i<j such that |a_i - a_j|>1 and in the original array i<j) 
        - sum_{j=1}^{n} a_i * (number of i<j such that |a_i - a_j|>1 and in the original array i<j)

But now the conditions involve the original indices. This seems difficult.

Another idea: we can separate the positive and negative contributions.

Note that d(a_i, a_j) = 
  a_j - a_i, if a_j - a_i > 1
  0, if |a_i - a_j|<=1
  a_j - a_i, if a_i - a_j > 1 -> which is negative since a_j < a_i

But we can write:

Total = sum_{i<j} [ a_j * I(|a_i - a_j|>1) ] - sum_{i<j} [ a_i * I(|a_i - a_j|>1) ]

Let A = sum_{j} a_j * c_j, where c_j = number of i<j (original indices) such that |a_i - a_j|>1.
Let B = sum_{i} a_i * d_i, where d_i = number of j>i (original indices) such that |a_i - a_j|>1.

But note: in the second sum, we have sum_{i<j} a_i * I(|a_i - a_j|>1) = sum_{i} a_i * (number of j>i such that |a_i - a_j|>1).

So Total = A - B.

Now, we need to compute for each element, based on its original index, the number of elements to the left that are not within 1 of it, and the number of elements to the right that are not within 1 of it.

But the conditions are on the values, not on the indices. So we need for each j, to count the number of i<j (original indices) such that a_i is not in [a_j-1, a_j+1].

Similarly for B.

This sounds like a job for a Fenwick tree or segment tree, but the values can be up to 10^9.

We can coordinate compress the values.

So steps:

1. Coordinate compress the array a.

2. We want to do two passes: 
   - One left-to-right: for each element a_j (in order of increasing j), we want to count the number of i<j such that a_i < a_j-1 or a_i > a_j+1. And also the sum of a_i for those i.
   - Then similarly, right-to-left: for each element a_i, count the number of j>i such that a_j < a_i-1 or a_j > a_i+1, and the sum of a_j for those j.

But then A = sum_j a_j * (count_left_j)
   B = sum_i a_i * (count_right_i)

Then Total = A - B.

But note: for the left pass, for each j, we want:
   count_left_j = number of i<j with a_i <= a_j-2 or a_i >= a_j+2.

Similarly, for the right pass, for each i, we want:
   count_right_i = number of j>i with a_j <= a_i-2 or a_j >= a_i+2.

And also, we need the sum of a_i for the left ones? Actually, for A and B, we only need the counts, not the sums? 

Let me check:

A = sum_j [ a_j * (number of i<j (original) such that |a_i - a_j|>1) ]

B = sum_i [ a_i * (number of j>i (original) such that |a_i - a_j|>1) ]

So we only need the counts per element, not the sums of the values of the other elements.

But is that sufficient? Yes, because A is the sum over j of a_j times the count of qualifying i's to the left. Similarly for B.

So we can do:

For A:
  We want for each j (in order of increasing index), to compute:
      c_j = number of i in [0, j-1] such that a_i <= a_j-2 or a_i>=a_j+2.

Then A = sum_j a_j * c_j.

For B:
  For each i (in order of decreasing index), to compute:
      d_i = number of j in [i+1, n-1] such that a_j <= a_i-2 or a_j>=a_i+2.

Then B = sum_i a_i * d_i.

Then Total = A - B.

Now, how to compute c_j and d_j efficiently? Since n can be 200,000, we need O(n log n) for each pass.

We can use a Fenwick tree or a segment tree for range sum queries, but we need to do range queries by value.

Steps for the left pass (computing c_j):

  - We will traverse j from 0 to n-1.
  - For each j, we query the Fenwick tree for the count of elements in the ranges [min_value, a_j-2] and [a_j+2, max_value], and then add them to get c_j.
  - Then we update the tree at position a_j with +1 (to include this element for future queries).

Similarly for the right pass (computing d_i):

  - Traverse i from n-1 down to 0.
  - For each i, query the count in the ranges [min_value, a_i-2] and [a_i+2, max_value].
  - Then update the tree at a_i with +1.

But note: the array a has duplicates, so we need to handle that. Fenwick tree can handle duplicates by storing frequencies.

Also, we need to compress the values.

So overall complexity: O(n log n), which is acceptable for n=200,000.

Let me test this approach with the examples.

Example 1: [1,2,3,1,3] (n=5)

For A (left to right):

j=0: a_j=1. No i<j, so c0=0. Then update freq[1] = 1.
j=1: a_j=2. Query for values <= (2-2)=0 and >=4. Both 0. So c1=0. Update freq[2]=1.
j=2: a_j=3. Query for values <=1 and >=5. 
   In the tree: we have [1,2]. So <=1: count=1 (the 1). >=5:0. So c2=1.
   Then update: freq[3]=1.
j=3: a_j=1. Query for values <= -1 (0) and >=3. 
   Values in the tree: [1,2,3]. 
   <= -1:0. >=3:1 (the 3). So c3=1.
j=4: a_j=3. Query for values<=1 and >=5. 
   Current tree: [1,2,3,1] -> frequencies: 1:2, 2:1, 3:1.
   <=1: count=2 (the two 1's). >=5:0. So c4=2.

Then A = 1*0 + 2*0 + 3*1 + 1*1 + 3*2 = 0+0+3+1+6 = 10.

For B (right to left):

i=4: a_i=3. No j>i, so d4=0. Then update: set at 3: +1.
i=3: a_i=1. Query for j>i: which are at i=4: value 3. 
   Query: values <= 1-2 = -1:0, or >=1+2=3: count=1 (the 3). So d3=1. Update: at 1: +1 -> now freq1=1, freq3=1.
i=2: a_i=3. Query: values <=1 and >=5. 
   Currently in tree: a3=1, a4=3. 
   <=1:1, >=5:0 -> d2=1. Update: at 3: +1 -> freq3=2.
i=1: a_i=2. Query: values<=0 and>=4:0. So d1=0. Update: at2: +1.
i=0: a_i=1. Query: values<=-1 or>=3. 
   In tree: a1=2, a2=3, a3=1, a4=3: 
   values>=3: we have two 3's? So count=2. 
   Also, values<=-1:0. So d0=2.

Then B = 1*2 + 2*0 + 3*1 + 1*1 + 3*0 = 2 + 0 + 3 + 1 + 0 = 6.

Then Total = A - B = 10 - 6 = 4. Which matches.

Example 2: [6,6,5,5]

A (left to right):
j0: a0=6 -> c0=0. Update: f6=1.
j1: a1=6 -> query: values<=4 and values>=8: 0. c1=0. Update: f6=2.
j2: a2=5 -> query: values<=3 and values>=7:0. c2=0. Update: f5=1.
j3: a3=5 -> query: values<=3 and values>=7:0. c3=0. Update: f5=2.
A=0.

B (right to left):
i3: a3=5 -> d3=0. Update: f5=1.
i2: a2=5 -> query: values<=3 and values>=7:0. d2=0. Update: f5=2.
i1: a1=6 -> query: values<=4 and values>=8:0. d1=0. Update: f6=1.
i0: a0=6 -> query: values<=4 and values>=8:0. d0=0. Update: f6=2.
B=0.
Total=0.

Example 3: [6,6,4,4]

A (left to right):
j0: a0=6 -> c0=0. Update: f6=1.
j1: a1=6 -> c1=0. Update: f6=2.
j2: a2=4 -> query: values<=2 and values>=6. 
   values>=6: we have two 6's -> count=2. So c2=2. Update: f4=1.
j3: a3=4 -> query: values<=2 and values>=6: values>=6:2. So c3=2. Update: f4=2.
A = 6*0 + 6*0 + 4*2 + 4*2 = 0+0+8+8=16.

B (right to left):
i3: a3=4 -> d3=0. Update: f4=1.
i2: a2=4 -> d2=0. Update: f4=2.
i1: a1=6 -> query: values<=4 and values>=8. 
   Currently: at i2 and i3: two 4's. 
   values<=6-2=4: we have two 4's. So d1=2. Update: f6=1.
i0: a0=6 -> query: values<=4 and values>=8. 
   values<=4: two 4's -> count=2. So d0=2. Update: f6=2.
B = 6*2 + 6*2 + 4*0 + 4*0 = 12+12=24.

Total = 16 - 24 = -8. Correct.

So the approach works.

Now, implementation:

1. We will compress the array a. The values are in [1,10^9], and n up to 200000, so we can compress to size 200000.

But note: we need to query ranges [min, a_j-2] and [a_j+2, max]. So we need to know the compressed indices for a_j-2 and a_j+2.

But after compression, we have the sorted unique values. Then for a value x, we can find the largest value <= x and the smallest value >= x. But for the ranges, we need:

- For [min, a_j-2]: we need the position of the largest value that is <= a_j-2.
- For [a_j+2, max]: we need the position of the smallest value that is >= a_j+2.

We can do this with binary search.

Alternatively, we can include all candidate values: but we don't have to. We can use the compressed array and then use the Fenwick tree on the compressed indices.

Steps for the left pass:

  - Compress the entire array a. Let comp be the sorted unique values.
  - Create a Fenwick tree (or segment tree) for range sum (frequencies) over the size of comp.
  - Initialize the tree to zeros.
  - For j from 0 to n-1:
        Let x = a[j]
        Find the index for x in the compressed array: idx_x.
        Find the index for x-2: we need the largest value <= x-2. Similarly for x+2: the smallest value >= x+2.

        Query the tree for the range [0, idx_low] where idx_low is the last index with value <= x-2.
        Query the tree for the range [idx_high, len(comp)-1] where idx_high is the first index with value >= x+2.

        c_j = query1 + query2

        Then update the tree at idx_x by adding 1.

  - Then A = sum_j (a_j * c_j)

Similarly for the right pass, but traverse from n-1 to 0.

But note: in the right pass, we are counting j>i such that a_j is outside [a_i-1, a_i+1]. So for a given i, we query the tree (which has the frequencies for j>i) for the ranges [min, a_i-2] and [a_i+2, max].

We'll need two Fenwick trees? Or we can use the same one and reset? We'll do two separate passes.

We can use the same Fenwick tree class and just reinitialize.

Now, for the Fenwick tree, we only need the sum of frequencies (counts), not the values. So it's a Fenwick tree for frequency counts.

We'll implement a Fenwick tree for frequencies.

But note: the range queries we need are prefix sums and suffix sums. Fenwick tree can do prefix sums efficiently.

So for the left query: 
   count1 = tree.range_query(0, pos_low)   # values <= x-2
   count2 = tree.range_query(pos_high, size-1)   # values >= x+2

But Fenwick tree is usually for prefix sums. To do a suffix sum, we can do total - prefix to the one before. Or we can store the total and subtract. Alternatively, we can use a segment tree. But Fenwick tree is simpler.

Alternatively, we can note that the entire tree is a frequency array, and we can do:

   total = tree.range_query(0, size-1)
   count2 = total - tree.range_query(0, pos_high-1)   # values >= pos_high: but we need values >= x+2, and we have the index for the smallest value >= x+2, so from that index to the end.

So we can do:

   count1 = tree.range_query(0, idx_low)
   count2 = tree.range_query(idx_high, size-1)   # if we support any range, but Fenwick tree is for prefix. 

We can implement a Fenwick tree that supports point updates and prefix queries. Then to get the sum in [l, r], we do query(r) - query(l-1). But then for [idx_high, size-1], we can do:

   count2 = tree.range_query(idx_high, size-1) = query(size-1) - query(idx_high-1)

But we have to be cautious with indices.

Alternatively, we can use a segment tree or a Fenwick tree that supports arbitrary ranges, but the Fenwick tree naturally supports prefix sums. So we can compute:

   total = tree.query(size-1)   # prefix sum up to last
   count1 = tree.query(idx_low)   # prefix sum up to idx_low: which is the count of values <= comp[idx_low]
   count2 = total - tree.query(idx_high-1)   # because we want values from idx_high to end: so subtract prefix up to idx_high-1.

But note: if idx_high is 0, then query(-1) is undefined. So we have to be careful.

Alternatively, we can design the Fenwick tree to have a function that returns the sum in an arbitrary range [l, r] (0-indexed) as:

   def query_range(l, r):
        if l>r: return 0
        return query(r) - (query(l-1) if l>0 else 0)

But actually, we can do:

   if l==0: 
        s1 = 0
   else:
        s1 = query(l-1)
   s2 = query(r)
   return s2 - s1

But our Fenwick tree usually uses 1-indexed? 

I think it's common to implement Fenwick tree 1-indexed. But we can also do 0-indexed. To avoid confusion, we can use 1-indexed.

Steps for the Fenwick tree:

  - We'll create an array bit of size = size_comp+1 (1-indexed).

  - update(index, delta): update the frequency at index (0-indexed index) by delta. Then we update the Fenwick tree at index+1.

  - query(index): returns the prefix sum from 0 to index (0-indexed). So we compute the prefix sum for [0, index] (0-indexed) by calling the Fenwick tree's prefix function for index+1.

  - Then to get the sum in [l, r] (0-indexed inclusive), we do:
        if l>r: return 0
        res = query(r) - (query(l-1) if l>0 else 0)

But we can also write:

        return query(r) - (query(l-1))

But if l==0, then query(-1) is 0.

So:

        if l>0:
            left = query(l-1)
        else:
            left = 0
        right = query(r)
        return right - left

But we can define a helper function:

        def range_query(l, r):
            if r < l:
                return 0
            return query(r) - (query(l-1) if l-1>=0 else 0)

But actually, we can write:

        return query(r) - query(l-1)

and when l=0, query(-1) should return 0.

So we can design the query function to return 0 for index<0.

In the Fenwick tree, we can have:

        def query(self, index):
            # index is 0-indexed. We want prefix [0, index]
            i = index+1
            s = 0
            while i>0:
                s += self.bit[i]
                i -= i & -i
            return s

        Then for range [l, r] (0-indexed, inclusive):

            if l<=r:
                return self.query(r) - (self.query(l-1) if l-1>=0 else 0)
            else:
                return 0

But we can also do:

            if l>r: return 0
            return self.query(r) - self.query(l-1)   # and let query(-1)=0.

        We can define a method that handles l-1:

        total = self.query(r)
        if l>0:
            total -= self.query(l-1)
        return total

But we can also compute without a helper: we can use the prefix query.

For the left pass:

   For each j:
        x = a[j]
        Find:
          low_val = x-2
          high_val = x+2

        Find the largest value in the compressed array that is <= low_val. Let that value be v1, and its index in the compressed array is idx1. Then the count for [min, low_val] is the prefix query at idx1.

        Find the smallest value in the compressed array that is >= high_val. Let that value be v2, and its index is idx2. Then the count for [v2, max] = total_count - prefix_query(idx2-1)   [because prefix_query(idx2-1) gives the count of numbers < v2].

        But note: the compressed array has unique values. So:

          count_low = (if there is any value <= low_val) then the count of numbers in the tree that are in the compressed array from 0 to idx1. 

          count_high = (if there is any value >= high_val) then the count of numbers in the tree that are in the compressed array from idx2 to end.

        Then c_j = count_low + count_high.

        Then update the tree at the position of x.

Similarly for the right pass.

We can use the same Fenwick tree class for both passes.

Implementation details:

  - We need to compress the array: 
        sorted_vals = sorted(set(a))
        comp_map = {}
        for idx, val in enumerate(sorted_vals):
            comp_map[val] = idx

        Then for a value x, we can get its index: comp_map[x]. But we also need to find the index for x-2 and x+2.

  - For x-2: we can use bisect_right and bisect_left.

        import bisect

        idx_low = bisect.bisect_right(sorted_vals, x-2) - 1
        # This gives the last index where the value is <= x-2.

        If no value is <= x-2, then idx_low = -1? But then we can set:

        pos1 = bisect.bisect_right(sorted_vals, x-2)   # returns the first index where the value > x-2, so the number of elements <= x-2 is pos1.
        So the count of numbers <= x-2 is the prefix sum at index = pos1-1 (if pos1>0) or 0 if pos1==0.

        Actually, we can get the count for [min, x-2] by:

        pos1 = bisect.bisect_right(sorted_vals, x-2)   # returns the insertion position, which is the first index where the value > x-2. So the last index that is <= x-2 is pos1-1.

        So the index for the last value <= x-2 is pos1-1.

        Then the count is the prefix sum at index = pos1-1? But if pos1==0, then no value.

        Alternatively, we can do:

        count_low = fenw.query(pos1-1)   if pos1>0, else 0.

        Similarly, for x+2:

        pos2 = bisect.bisect_left(sorted_vals, x+2)   # returns the first index where the value >= x+2.
        Then the count of numbers >= x+2 is: total_count_in_tree - fenw.query(pos2-1)   [because fenw.query(pos2-1) gives the count of numbers < x+2].

        But wait: fenw.query(pos2-1) is the count of numbers that are <= sorted_vals[pos2-1] (which is the last value that is < x+2). So the count of numbers >= x+2 is total - fenw.query(pos2-1).

        But note: if pos2 is beyond the last index, then we set pos2 = len(sorted_vals), then fenw.query(pos2-1) = fenw.query(len-1) = total, so count_high=0.

        If pos2=0, then all values are >= x+2, so count_high = total.

  - But note: we are updating the tree as we go, so the total_count_in_tree is j (for the left pass) because we are at j and have updated j-1 times? Actually, we are updating after we compute c_j. So at the time of query for j, the tree has indices for i=0 to j-1. So the total_count_in_tree is j.

        So we can do:

        count_low = fenw.query(pos1-1)   # if pos1>0, else 0
        count_high = j - fenw.query(pos2-1)   # because j is the total number of elements in the tree so far (since we haven't updated j yet)

        But wait, we haven't updated a_j yet, so the tree has j elements? Actually, we are at j, and we are about to update, so the tree has j elements? But we are updating after the query, so the tree has j elements? Actually, no: we update after the query. So the tree has elements for i=0 to j-1, so total count is j.

        Therefore, count_high = j - (fenw.query(pos2-1) if pos2>0 else 0) 

        But if pos2==0, then fenw.query(-1) is 0? Then count_high = j - 0 = j? But that's correct because all elements are >= x+2.

        But if pos2>0, then we subtract the count of numbers that are < x+2, which is fenw.query(pos2-1). 

        However, note: the tree has j elements. So we can compute:

        count_high = j - fenw.query(pos2-1)   # if we define fenw.query(-1)=0.

        But we can design the query function to return 0 for index<0.

        So:

        count_low = fenw.query(pos1-1)   # which is the count of numbers <= x-2.

        count_high = j - fenw.query(pos2-1)   # count of numbers >= x+2.

        Then c_j = count_low + count_high.

        Then update the tree at the position of x (which is comp_map[x]) by 1.

But note: the Fenwick tree we have is for the compressed array. When we update, we update the frequency at the compressed index of x.

Similarly, for the right pass:

   We traverse j from n-1 downto 0.
   The tree will be updated for indices j+1 to n-1. So total count in tree = n-1 - j.

   For each i, we want:
        d_i = count of j>i such that a_j <= a_i-2 or a_j>=a_i+2.

   For a_i = x:
        Find:
          low_val = x-2
          high_val = x+2

        pos1 = bisect.bisect_right(sorted_vals, x-2)   # first index > x-2, so the count of numbers <= x-2 is the prefix sum at pos1-1.
        count_low = fenw.query(pos1-1)   # if pos1>0, else 0.

        For count_high: 
          pos2 = bisect.bisect_left(sorted_vals, x+2)   # first index >= x+2.
          Then the count of numbers >= x+2 = total_in_tree - fenw.query(pos2-1)

          total_in_tree = n-1 - i   (because we are at i, and the tree has indices from i+1 to n-1)

        So count_high = (n-1-i) - fenw.query(pos2-1)   # if we define query(-1)=0.

        Then d_i = count_low + count_high.

        Then update the tree at the position of x by 1.

But note: we must update the tree at the end of each iteration.

So the plan is:

  Step 1: Coordinate compress the array.

  Step 2: Initialize Fenwick tree for frequencies (size = len(comp)).

  Step 3: Traverse j from 0 to n-1 for A:
        x = a[j]
        Find pos1 and pos2 for x-2 and x+2 in the compressed array.
        count_low = fenw.query(pos1-1)   # returns 0 if pos1-1 <0
        count_high = j - fenw.query(pos2-1)   # because so far we have j elements in the tree (from 0 to j-1)
        c_j = count_low + count_high
        A += x * c_j
        Update fenw at the index of x by 1.

  Step 4: Similarly, for B, initialize another Fenwick tree (or reuse and clear) and traverse i from n-1 downto 0:
        x = a[i]
        Find pos1 and pos2 for x-2 and x+2.
        count_low = fenw.query(pos1-1)
        total_in_tree = n-1 - i   # because we have processed indices from i+1 to n-1
        count_high = total_in_tree - fenw.query(pos2-1)
        d_i = count_low + count_high
        B += x * d_i
        Update fenw at the index of x by 1.

  Step 5: Total = A - B.

Now, let's code the Fenwick tree.

We'll create a class for Fenwick tree:

class Fenw:
    def __init__(self, size):
        self.n = size
        self.bit = [0]*(self.n+1)   # 1-indexed

    def update(self, index, delta):
        # index is 0-indexed
        i = index+1
        while i<=self.n:
            self.bit[i] += delta
            i += i & -i

    def query(self, index):
        # returns prefix sum [0, index] for 0-indexed index; if index<0, return 0
        if index < 0:
            return 0
        i = index+1
        s = 0
        while i>0:
            s += self.bit[i]
            i -= i & -i
        return s

Then we can use:

    fenw = Fenw(len(comp))

But note: when we do fenw.query(pos1-1), if pos1-1 is negative, we return 0.

We'll write the code accordingly.

Edge: if x is 1, then x-2 = -1. Then we want values <= -1: none. So pos1 = bisect_right(sorted_vals, -1) -> which is 0? Then fenw.query(-1)=0 -> count_low=0.

Similarly, if x is 10^9, then x+2 = 10^9+2, which is beyond the max. Then pos2 = bisect_left(sorted_vals, 10^9+2) -> returns len(sorted_vals). Then fenw.query(pos2-1) = fenw.query(len-1) = total count. Then count_high = j - total_count = j - j =0? But total_count in the tree is j, so that's 0.

So it's correct.

Now, we implement.

Complexity: O(n log n), which is acceptable.

Let me code accordingly.

But note: we need to do two passes, so we can create two Fenwick trees or reuse one by resetting. We'll create two: one for the left pass and one for the right pass.

Alternatively, we can write a function that does the left pass and then reset the tree for the right pass.

But we'll create two for clarity.

Actually, the right pass is in reverse, so we can use one Fenwick tree for the left and then create a new one for the right.

Code structure:

    n = int(input().strip())
    a = list(map(int, input().split()))

    # Step 1: compress
    sorted_vals = sorted(set(a))
    comp_map = {val: idx for idx, val in enumerate(sorted_vals)}
    size_comp = len(sorted_vals)

    # Step 2: Fenwick tree for left pass
    fenw_left = Fenw(size_comp)
    A = 0
    for j in range(n):
        x = a[j]
        # Find positions for x-2 and x+2
        # For x-2: we want the count of numbers <= x-2
        pos1 = bisect.bisect_right(sorted_vals, x-2)   # gives the first index where value> x-2, so the count of numbers <= x-2 is pos1 (because the array is sorted and unique) but actually, the number of distinct values <= x-2 is pos1, but the frequency count is stored in the tree. But we don't have the distinct values, we have the entire array. So we use the Fenwick tree to get the actual count.

        # But we don't use pos1 as the count. We use the Fenwick tree to query the prefix up to the last value <= x-2. The last value <= x-2 is at index = pos1-1 (if pos1>0). 
        # So:
        idx_low = pos1 - 1   # the index in the compressed array for the last value <= x-2, or if none, then we set to -1 and then query(-1)=0.
        count_low = fenw_left.query(idx_low)   # because we want [0, idx_low] inclusive

        # For x+2: we want the count of numbers >= x+2.
        pos2 = bisect.bisect_left(sorted_vals, x+2)   # first index with value>=x+2
        # Then the count of numbers < x+2 is the prefix sum up to index = pos2-1. Then the count of numbers >= x+2 is total_in_tree - (prefix_sum at pos2-1)
        total_in_tree = j   # because we have processed j elements (from 0 to j-1) and we haven't updated j yet.
        count_high = total_in_tree - fenw_left.query(pos2-1)   # because query(pos2-1) is the count of numbers in [0, pos2-1] (which are < x+2)

        c_j = count_low + count_high
        A += x * c_j

        # Update Fenwick tree for the current x
        idx_x = comp_map[x]
        fenw_left.update(idx_x, 1)

    # Step 3: Fenwick tree for right pass
    fenw_right = Fenw(size_comp)
    B = 0
    for i in range(n-1, -1, -1):
        x = a[i]
        # same: find for x-2 and x+2
        pos1 = bisect.bisect_right(sorted_vals, x-2)
        idx_low = pos1 - 1
        count_low = fenw_right.query(idx_low)

        pos2 = bisect.bisect_left(sorted_vals, x+2)
        total_in_tree = n-1 - i   # because we have processed indices from i+1 to n-1 (so n-1-i elements)
        count_high = total_in_tree - fenw_right.query(pos2-1)

        d_i = count_low + count_high
        B += x * d_i

        idx_x = comp_map[x]
        fenw_right.update(idx_x, 1)

    # Step 4: result
    result = A - B
    print(result)

But wait: in the right pass, the Fenwick tree is being updated for the current i? And we are processing from the end. So when we are at i, the tree contains the frequencies for the indices from i+1 to n-1. So the total_in_tree is n-1-i.

But we update after we compute d_i, so the tree has not been updated for i yet. So the total_in_tree is the number of elements we have processed so far, which is n-1-i? Actually, we have processed i+1 to n-1, which is n-1-i elements. Then we update at the end.

But note: the update is for the current element? Actually, the current element is a[i], and we are going to add it to the tree after we compute d_i. So the tree has the elements from i+1 to n-1. So total_in_tree = n-1-i is correct.

But then count_high = total_in_tree - fenw_right.query(pos2-1) is the count of numbers >= x+2 in the tree (which is the part of the array after i).

Similarly, count_low is the count of numbers <= x-2 in the tree.

So it's correct.

Now, test with a small example: [1,3] (n=2)

Left pass for A:
  j=0: x=1
      x-2 = -1: pos1 = bisect_right([1,3], -1) -> 0, so idx_low = -1 -> count_low = fenw_left.query(-1)=0.
      x+2=3: pos2 = bisect_left([1,3], 3) -> index=1? because sorted_vals[1]=3.
      total_in_tree = 0 (because j=0: no elements processed yet)
      count_high = 0 - fenw_left.query(1-1) = 0 - fenw_left.query(0) = 0 - 0 =0.
      c0=0, A=0.
      update: at comp_map[1]=0 -> update index0 by 1.

  j=1: x=3
      x-2=1: pos1 = bisect_right([1,3],1) -> returns 1 (because the first index where value>1 is at index1, which is 3). So idx_low = 1-1=0.
      count_low = fenw_left.query(0) = 1? (because we updated index0 for 1) -> so 1.
      x+2=5: pos2 = bisect_left([1,3],5) -> returns 2 (since 5>3, so it would be inserted at the end, which is index2, but the array has size2, so index2 is beyond). Then count_high = total_in_tree (which is j=1) - fenw_left.query(2-1) = 1 - fenw_left.query(1). 
          We have updated only index0: so fenw_left.query(1) = the prefix sum for index0 and index1: but we only have index0. 
          How do we query index1? 
          Our Fenwick tree: 
              size_comp=2.
              We have updated index0: so the Fenwick tree: 
                  bit[1] = value for index0:1, bit[2] = also 1? Because the tree: 
                  index0+1=1: update: 
                      bit[1] +=1
                      then 1+ (1 & -1)=1+1=2: so bit[2] +=1.
              Then query(1): 
                  for index=1 (0-indexed), we do:
                      i = 1+1=2 -> then s += bit[2] ->1
                      then 2 - lowbit(2)=0? -> stop? 
                  so returns 1? 
          Then count_high = 1 - 1 =0.
      So c1 = 1 + 0 =1.
      Then A += 3*1 = 3.

  Then A=3.

Right pass for B:
  Start from i=1 (x=3) and then i=0 (x=1)

  i=1: x=3
        total_in_tree = n-1-1 = 2-1-1=0? Actually, i=1: n-1-i = 2-1-1=0. So no elements to the right.
        So count_low and count_high=0 -> d1=0, B=0.
        Then update: add 3 at index1.

  i=0: x=1
        x-2=-1: count_low=0.
        x+2=3: 
            pos2 = bisect_left([1,3],3)=1.
            total_in_tree = n-1-0 = 1 (because we have processed i=1, so one element to the right).
            count_high = 1 - fenw_right.query(1-1) = 1 - fenw_right.query(0)
            The tree has only the element 3 at index1. So for index0: we haven't updated it, so fenw_right.query(0) = frequency of index0? which is 0.
            So count_high=1.
        d0=0+1=1, then B += 1*1 = 1.

  Then B=1.

  Total = 3-1=2.

But the actual for [1,3]: 
  Pairs: (1,1):0, (1,2): d(1,3)=2 (since |1-3|>1 -> 3-1=2), (2,2):0 -> but only one pair (i=0,j=1) -> 2.

But we have two pairs? Actually, the pairs are (i,j) with i<=j: 
   (0,0): d(1,1)=0
   (0,1): d(1,3)=2
   (1,1): d(3,3)=0
   So total=2.

But our result is 2. Correct.

So the code is correct.

But note: in the right pass, when we update the Fenwick tree, we update the index for the current x. For i=0, we update the index for 1 (which is index0) by 1. But after we compute d0, we update. So the tree for the next (if any) would have this. But since we are going from right to left, after i=0, we are done.

So we are good.

Now, we write the code accordingly.

But note: the Fenwick tree for the right pass: we update the current element after we use it. So for the next (if we go to a lower i) the tree includes the current. But we are going from high i to low i, and the tree is for the elements to the right (which are the ones we've already processed, i.e., i+1 to n-1). So when we update at i, we are adding the current element to the tree so that for the next (i-1) the tree includes i.

But we do the update after we compute d_i, so for the current i, the tree has the elements from i+1 to n-1. Then we update to add i, so then for i-1, the tree has i to n-1.

So it's correct.

Let me test with [1,3] again in the right pass:

   i=1: x=3 -> we compute d_i=0, then update index1 (for 3) by 1.
   i=0: x=1 -> then the tree has one element (at index1). Then we compute for x=1: 
        count_high: for x+2=3, we get count_high = total_in_tree (which is 1) - fenw_right.query(1-1)=query(0)=0 -> so 1. Then update index0 by 1.

So correct.

Now, we code.

But note: the array a can have duplicates, so the comp_map must map each distinct value to an index. Then when we update, we update that index.

But duplicates: for example, two 1's: then they both map to index0. Then updating index0 twice: the Fenwick tree will store the frequency at index0 as 2.

The Fenwick tree for frequencies: we are updating by 1 each time.

So it's correct.

Now, we write the complete code.

We'll implement the Fenw class as described.

Let me code accordingly.

Note: we use bisect module.

We'll write:

import bisect

class Fenw:
    def __init__(self, size):
        self.n = size
        self.bit = [0] * (self.n+1)   # 1-indexed, so index0 unused? no: we use 1..n for 0-indexed indices.
    
    def update(self, index, delta):
        # index: 0-indexed
        i = index + 1
        while i <= self.n:
            self.bit[i] += delta
            i += i & -i

    def query(self, index):
        # returns the prefix sum [0, index] (0-indexed). If index<0, return 0.
        if index < 0:
            return 0
        i = index + 1
        s = 0
        while i > 0:
            s += self.bit[i]
            i -= i & -i
        return s

Then the main solution:

    n = int(input().strip())
    a = list(map(int, input().split()))
    if n == 0:
        print(0)
        exit(0)
    sorted_vals = sorted(set(a))
    comp_map = {}
    for idx, val in enumerate(sorted_vals):
        comp_map[val] = idx
    size_comp = len(sorted_vals)

    fenw_left = Fenw(size_comp)
    A = 0
    for j in range(n):
        x = a[j]
        # For x-2: find the count of numbers in the tree that are <= x-2.
        # Find the position: 
        pos1 = bisect.bisect_right(sorted_vals, x-2)   # first index > x-2 -> then the last index <= x-2 is pos1-1.
        idx_low = pos1 - 1
        count_low = fenw_left.query(idx_low)   # which is the sum of frequencies for indices

## Final Solution

