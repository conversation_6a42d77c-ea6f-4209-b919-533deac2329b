# Solution for 87_C.md

Generated at: 2025-07-22 05:59:11
Model: deepseek-reasoner

## Reasoning Process

Alright, I'm trying to solve this problem about <PERSON><PERSON><PERSON> and <PERSON><PERSON> playing a stone game. Let me first make sure I understand the problem correctly.

So, we start with one pile of n stones. On a player's turn, they take one pile and split it into k piles (k>=2) that form a strictly decreasing sequence where the difference between consecutive piles is exactly 1. That means the stones in the piles should be like a, a-1, a-2, ... down to some positive number, and the sequence has to be strictly decreasing by 1 each time. Also, the sum of the stones in these k piles must equal the original pile's stone count.

The players alternate turns, with <PERSON><PERSON><PERSON> going first. The player who can't make a move loses. Both play optimally. The task is: if <PERSON><PERSON><PERSON> can win, we need to output the minimal number of piles k (at least 2) into which he can split the initial pile to guarantee a win (assuming both play optimally after that). If he can't win no matter what, output -1.

Examples: 
For n=3, output is 2. That means <PERSON><PERSON><PERSON> wins by splitting into 2 piles.
How? 3 can be split into 2 and 1. Then <PERSON><PERSON> is faced with two piles: 2 and 1. But can <PERSON><PERSON> move? The moves require splitting one pile into at least two piles. The pile of 1 cannot be split (since k>=2 and we need at least two piles, but 1 can't be split into two positive stones). The pile of 2: can it be split? The only possible split would be into two piles with a difference of 1, so 1 and ... 1? But 1 and 1 isn't decreasing? Actually, it has to be strictly decreasing and consecutive integers. For 2, the only possibility is splitting into two piles: 1 and 1? But that's not strictly decreasing (1 and 1 are equal) and the difference is 0, not 1. So actually, 2 cannot be split. So after Ser<PERSON>ha splits 3 into [2,1], Gena has no moves and loses. Thus, Ser<PERSON>ha wins by splitting into 2 piles. So output is 2.

For n=6, output is -1. So Serozha loses no matter what he does. Let me see possible moves for n=6.

How can 6 be split?
The problem requires splitting into k piles (k>=2) such that the stones form consecutive decreasing integers. So the sequence is an arithmetic progression with common difference 1, but strictly decreasing.

So, for k piles, the sequence is: a, a-1, a-2, ..., a-(k-1). The sum of these is k*a - (0+1+...+(k-1)) = k*a - k*(k-1)/2. And this must equal n (which is 6).

So, k*a - k*(k-1)/2 = 6.

We need integer a, and a must be at least k (since the largest pile is a and the sequence has k terms, the smallest is a - (k-1) which must be positive).

So, for k=2: 2a - 1 = 6 => 2a=7 => a=3.5 -> not integer. So k=2 is not possible.

k=3: 3a - 3 = 6 => 3a = 9 => a=3. So the piles are 3,2,1. Sum=6. So that's valid.

k=4: 4a - 6 = 6 => 4a=12 => a=3. Then piles: 3,2,1,0? But 0 not allowed. Alternatively, the sequence would be 3,2,1,0? But the smallest must be positive. So no. Alternatively, the formula: the smallest term is a - (k-1) = 3 - 3 =0 -> invalid. So k=4 is invalid.

k=5: 5a - 10 =6 => 5a=16 => a=3.2 -> not integer.

k=6: 6a - 15=6 => 6a=21 => a=3.5 -> not integer.

So the only valid split for 6 is k=3: piles [3,2,1].

After Serozha splits 6 into [3,2,1], what happens? Now Gena has three piles. She must choose one to split. But which ones can she split?

Pile of 3: can it be split? To split 3, as we saw earlier, into two piles: 2 and 1. That's valid. So Gena can take the pile of 3 and split it into 2 and 1. Then the piles become [2,1,2,1]. But wait, the piles are unordered? Actually, the game doesn't specify order, so we can consider the state as a multiset. So after splitting the 3, we have two 2's and two 1's.

Now, the state: two piles of 2 and two piles of 1. Now it's Serozha's turn. What moves can he make?

He can choose a pile of 2. But can he split a pile of 2? As before, no. Because the only possible split would be 1 and 1, but that's not strictly decreasing by 1? Actually, 1 and 1: the sequence would be 1,1? That's not strictly decreasing. Alternatively, the problem states the piles must satisfy a1 > a2 > ... > ak and consecutive differences are 1. For two piles, that would require two stones: the larger one minus the smaller one is 1. So for a pile of 2: we need two piles that are consecutive and strictly decreasing. The only possibility is 1 and 1? But 1 and 1 are not strictly decreasing (they are equal). So 2 cannot be split. Similarly, 1 cannot be split. Therefore, after Gena's move, all piles are either 1 or 2, which are unsplittable. So Serozha has no moves and loses.

But wait, in the above scenario, after Gena splits the 3, we have two 2's and two 1's. Then Serozha has no moves and loses. So if Serozha splits 6 into [3,2,1], then Gena can make a move and then Serozha loses. So that leads to Serozha losing.

But are there other moves for Serozha? We saw that k=3 is the only move. So Serozha must make that move, and then loses. So output is -1.

Now, example 3: n=100, output 8. So Serozha wins by splitting into 8 piles. We need to find the minimal k? The problem says: the minimal number of piles k into which he can split to win. So we have to find the smallest k (starting from 2 upwards) such that splitting n into k piles (with the consecutive decreasing condition) leads to a winning position (i.e., the next player loses, meaning the resulting state is losing for Gena).

But we also have to consider that after Serozha's move, Gena will play optimally and then Serozha will also play optimally. So we need to use game theory, probably with states and winning/losing positions.

So, we can model the game as an impartial game? Each state is a multiset of piles? But the state space could be huge because n is up to 10^5. However, note that the moves are constrained: when you split a pile, you break it into consecutive decreasing integers. Moreover, the splits are independent of other piles. So the entire game state is the set of piles, and the game is the disjunctive sum of these independent games. Therefore, we can use the Sprague-Grundy theorem.

Each pile is an impartial game, and the Grundy number (or mex value) for each pile size can be computed. Then the overall game state is the XOR of the Grundy numbers of each pile. The starting state is a single pile of n. Serozha must split it into k piles (with the consecutive decreasing condition) such that the new state has a Grundy number of 0 (a losing position for the next player, Gena). Then, we want the minimal k for which such a split exists.

But note: after Serozha's move, the state becomes k piles. The Grundy number of the new state is the XOR of the Grundy numbers of each of these k piles. We need this XOR to be 0 (so that Gena faces a losing position). Also, we need to check if there's any split (for the initial n) that leads to a state with XOR 0. If none, then output -1. Otherwise, find the minimal k (number of piles) for which such a split exists.

So the plan:

1. Precompute the Grundy numbers for all pile sizes from 1 up to n (n up to 10^5).

But how do we compute the Grundy number for a pile of size i?

The Grundy number for a state is the mex (minimum excludant) of the Grundy numbers of all reachable states from that state.

From a pile of size i, we can split it into k piles (k>=2) such that the split forms an arithmetic progression of consecutive integers: a, a-1, a-2, ..., a-(k-1) and the sum is i. The split must satisfy:

- k>=2
- a must be at least k (so that the smallest term a - (k-1) >=1)
- The sum: k*a - k*(k-1)//2 = i

So, for a given i, we can iterate over possible k (from 2 to a maximum). What is the maximum k? Since the smallest term must be at least 1, we have a - (k-1) >=1 => a>=k. Then the sum is k*a - k*(k-1)//2 = i. Also, note that the minimal sum for k piles is when a is as small as possible: a = k, then the sum is k*k - k*(k-1)//2 = k(k+1)/2. So for a fixed k, the minimal i is k(k+1)/2. Therefore, for a given i, k must be such that k(k+1)/2 <= i.

But k can go up to about sqrt(2i). Since n is 10^5, the maximum k for a pile of size n is around sqrt(2*10^5) ≈ 447. So for each i, we can iterate over k from 2 to floor((sqrt(8*i+1)-1)/2). Actually, the maximal k is the solution to k(k+1)/2 <= i, which is about sqrt(2i).

But note: we have to compute the Grundy numbers for all i from 1 to n. If for each i we iterate over about O(sqrt(i)) possible k, then overall time complexity is O(n * sqrt(n)) which is about 10^5 * 447 ≈ 44.7e6, which might be acceptable in Pyton? But 44.7 million iterations in Python might be borderline in speed. However, we have to consider that for each i, we are iterating k from 2 to max_k, and for each k, we compute the set of moves: that is, the split is valid only if k*a - k*(k-1)//2 == i. So we can solve for a: a = (i + k*(k-1)//2) / k. Then a must be an integer and a must be >= k (so that the smallest pile is at least 1). Also, note that a must be an integer, so (i + k*(k-1)//2) must be divisible by k.

So for each i and each k (from 2 to max_k), we can compute:

s = k*(k-1)//2
if (i - s) % k != 0: skip
else:
   a = (i + s) // k   [but actually: (i - s) % k ==0 => a = (i + s) // k? Let me check: the equation is k*a - s = i => k*a = i + s => a = (i + s) / k.

But s = k*(k-1)//2.

So then the split is into k piles: a, a-1, ..., a-(k-1). 

But note: the new state is a set of k piles. However, the piles are distinct? Not necessarily: but the split produces distinct piles? Actually, no: the piles are distinct because they form a strictly decreasing sequence. So the piles are all distinct. But the game state is a multiset. So the next state from pile i is a set of k piles: the values a, a-1, ..., a-k+1.

Then the Grundy number for state i is computed as:

G(i) = mex { G(a) XOR G(a-1) XOR ... XOR G(a-k+1) } for all valid splits.

But wait: the entire state after the split is a set of k piles. Since the game is the disjunctive sum of these piles, the Grundy number for the new state is the XOR of the Grundy numbers of each pile. So for a split into k piles: g = G(a) XOR G(a-1) XOR ... XOR G(a-k+1).

Then G(i) = mex { all such g's for every valid split (every valid k) }.

But note: the mex is taken over the set of all these g values.

However, the problem: we are computing G(i) for i from 1 to n. But to compute G(i), we need to know G(j) for j < i? Actually, the piles after splitting are of sizes a, a-1, ... which are all less than i? Since a is (i + k*(k-1)//2)/k, and because k>=2, a must be less than i. For example, when k=2: a = (i + 1) / 2. For i=3: a= (3+1)/2=2, which is less than 3. For k=3: a=(i+3)/3. For i=6: a=(6+3)/3=3. So the new piles are smaller than i. Therefore, we can compute G(i) for i from 1 upwards.

But the problem: for each i, we iterate k from 2 to max_k (about sqrt(i)). For each k, we compute a = (i + k*(k-1)//2) / k. If this is an integer and a>=k, then we form the set of k piles: from a down to a-k+1. Then we compute g = G(a) XOR G(a-1) XOR ... XOR G(a-k+1).

But note: the piles are consecutive integers? Yes. So we have to compute the XOR of Grundy numbers over a consecutive segment: from a-k+1 to a. We can precompute a prefix XOR array for the Grundy numbers? But note: the Grundy numbers are not linear, so we cannot use a simple prefix XOR. Alternatively, we can compute the XOR for the consecutive segment by having an array of Grundy numbers and then using a Fenwick tree? But that might be overkill and we are already doing an O(n*sqrt(n)) algorithm.

Alternatively, for each split, we can iterate over the k consecutive numbers and compute the XOR. The work per split is O(k). Since k is about sqrt(i), and for each i we consider O(sqrt(i)) splits, the total work per i is O(sqrt(i)) * O(sqrt(i)) = O(i). Then the overall complexity would be O(n^2), which is 10^10 for n=10^5, too slow.

We need a more efficient way. How to compute the XOR over a consecutive segment quickly? Actually, we can precompute an array of Grundy numbers and then use a prefix XOR array? But the problem: we are computing G(i) in increasing order, so we have the Grundy numbers for all j < i. But we don't have G(i) yet. However, for the splits of i, the piles are all less than i, so we have computed their Grundy numbers. So we can store an array `g` where g[i] = Grundy number for i.

But then, for a consecutive segment [L, R] (L to R inclusive), we want the XOR of g[L] to g[R]. We can precompute a prefix-xor array `pref` such that pref[i] = g[0] XOR g[1] XOR ... XOR g[i]. Then the XOR from L to R is pref[R] XOR pref[L-1]. But note: we are computing the Grundy numbers in increasing order. So when we are at i, we have computed g[0] to g[i-1]. We can maintain a prefix-xor array as we compute g[i]. But for the split of i, we need to compute the XOR for a consecutive segment that might end at some value < i. So we can do that with a prefix-xor array that is updated as we go.

But wait: the consecutive segment is from a-k+1 to a. Since a < i? Actually, a = (i + k*(k-1)//2)/k. Since k>=2, we have a <= i/2 + (k-1)/2. For k>=2, a is less than i? Actually, for k=2: a = (i+1)/2. For i>=3, (i+1)/2 < i. Similarly, for k=3: a=(i+3)/3, which for i>=6, (i+3)/3 <= i/3+1, which is less than i for i>=3. So yes, the pile sizes in the split are all less than i. Therefore, we can compute the XOR of g over [a-k+1, a] using a prefix-xor array that we update as we compute g[0] to g[i-1]. But we are computing g[i] at step i, and we have computed g[0] to g[i-1]. So the prefix-xor array is available for indices < i.

So we can do:

Let `grundy` be an array of size n+1, with grundy[0]=0 (if we need it, but pile size 0 is not used? Actually, piles are positive, but we might use index 0 for prefix). Let `pref` be an array of length n+1, where pref[i] = grundy[0] ^ grundy[1] ^ ... ^ grundy[i].

But actually, to compute the XOR over [L, R] we need pref[R] ^ pref[L-1]. But if L=0, then we use pref[R] (if pref[-1]=0). So we can maintain a global prefix-xor array that we update as we compute each grundy[i].

But note: we are computing grundy[i] at step i, and we need to compute it using splits that involve consecutive segments of grundy numbers. For each split k, we compute a, then the segment is from L = a-k+1 to R = a. Then the XOR is pref[R] ^ pref[L-1] (if L>0, then pref[L-1] is defined).

However, we have to note: the segment [L,R] is consecutive, but the grundy numbers are stored, and we have computed up to i-1. Since a < i and L = a-k+1 >= 1 (because the smallest pile must be positive), we have L>=1 and R = a < i. So we can compute the XOR for the segment.

But the problem: we need to compute the XOR of grundy[L] to grundy[R] for L and R < i. We can do that if we have a Fenwick tree? Actually, we can maintain an array `grundy` and a Fenwick tree for XOR, but since we are only doing sequential computation (from 1 to n), we can simply maintain a global array `g` and a prefix-xor array `pref` that we update after we compute each grundy[i]. But for computing grundy[i], we don't have grundy[i] computed yet, so the prefix-xor array is only updated to index i-1. But that's okay because the splits for i only involve indices < i.

So the algorithm for precomputation:

grundy[0] = 0  (for convenience, but pile size 0 is not used in the game, but for prefix array we need index 0)
pref[0] = 0
Let g = [0]*(n+1)   # g[0]=0, g[i] for i>=1 will be computed
pref = [0]*(n+1)   # we can build as we go

for i from 1 to n:
    moves = set()   # to collect the grundy numbers of the next states
    k = 2
    # while the minimal sum for k piles is <= i: i.e., k*(k+1)//2 <= i
    while k*(k+1)//2 <= i:
        # s = k*(k-1)//2
        s = k*(k-1)//2
        total = i + s
        if total % k == 0:
            a = total // k   # a is the largest pile
            # the smallest pile is a - (k-1) = a - k + 1
            # Check: smallest pile must be >=1 -> a>=k, which we have because k*(k+1)//2 <= i => a = (i+s)/k = (i + k*(k-1)//2)/k >= (k*(k+1)//2 + k*(k-1)//2 - something? Actually, minimal i is k*(k+1)//2, then a = (k*(k+1)//2 + k*(k-1)//2)/k = ( (k^2 + k + k^2 - k) /2 ) /k = (2*k^2/2)/k = k. So a>=k holds.

            L = a - k + 1   # smallest pile
            R = a           # largest pile

            # Now, we want the XOR of g[L] to g[R]
            # Using prefix-xor: we have a prefix array up to i-1, and L,R < i? 
            # Since a = (i + s)/k, and i>=k*(k+1)//2, then a = i/k + (k-1)/2. 
            # For k>=2, a < i. Also, L = a-k+1 >= 1 (as established). And R = a < i.
            # So we can compute:
            xor_val = pref[R] ^ pref[L-1]   # because pref[R] = g[0]^...^g[R], pref[L-1]=g[0]^...^g[L-1], so XOR is g[L]^...^g[R]

            moves.add(xor_val)

        k += 1

    # Now, compute mex of moves
    mex = 0
    while mex in moves:
        mex += 1
    g[i] = mex
    pref[i] = pref[i-1] ^ g[i]   # update prefix XOR

But note: the moves set is built from the splits. However, we must note that the same xor_val might be generated by different splits, but that's okay because set will remove duplicates.

After precomputation, we have the grundy numbers for all i from 1 to n.

Then, the initial state is a pile of size n. Serozha must split it (if he can) into k piles (with the consecutive condition) such that the resulting state has a Grundy number of 0. The resulting state is the set of k piles, and its Grundy number is the XOR of the grundy numbers of the k piles. We want that XOR to be 0.

But also, we want the minimal k (number of piles) for which such a split exists.

So, after precomputation, we need to:

For the initial n, we want to find the minimal k (starting from 2 upwards) such that:
- The split into k piles is valid: that is, k*(k+1)//2 <= n and (n + k*(k-1)//2) is divisible by k, and the smallest pile is positive.
- And the XOR of the grundy numbers over the consecutive segment [a-k+1, a] is 0.

But note: we already computed the grundy numbers. So we can do:

ans = a big number (like infinity)
found = False
k = 2
while k*(k+1)//2 <= n:
    s = k*(k-1)//2
    total = n + s
    if total % k == 0:
        a = total // k
        L = a - k + 1
        R = a
        # Now, compute the XOR of grundy[L] to grundy[R] using our prefix-xor array (which we built for indices up to n)
        xor_val = pref[R] ^ pref[L-1]
        if xor_val == 0:   # then this split leads to a losing position for Gena
            found = True
            if k < ans:
                ans = k
    k += 1

if found:
    return ans
else:
    return -1

But wait: we have the entire prefix-xor array already computed? Yes, because we precomputed up to n.

However, note: in the precomputation loop, we computed grundy numbers and prefix-xor for all i from 1 to n. So at the end, pref[i] is available for i from 0 to n.

But the problem: our prefix-xor array `pref` is the cumulative XOR from 0 to i. So to get the XOR from L to R, we do pref[R] ^ pref[L-1]. And we have that.

But note: we must be cautious about the indices. If L=0, then we use pref[R] ^ pref[-1] but we defined pref[0]=0 and for L=0, we should use L-1 = -1? We defined our arrays 0-indexed. So we should handle L=0 separately? But L = a-k+1, and a>=k, so L>=1. So we are safe.

But what about the time? The precomputation: for each i, we iterate k from 2 to about sqrt(2*i). The total number of iterations is about sum_{i=1}^{n} sqrt(i) ~ O(n^(3/2)) = O(n * sqrt(n)) ≈ 10^5 * 447 ≈ 44.7e6, which is acceptable in C++ but in Python might be borderline? However, we have to note that for small i, the max_k is small, and for i=1, there are no splits (k>=2 requires at least 3 stones? For i=1: minimal k is 2, but k(k+1)/2=3>1, so no k. Similarly, i=2: minimal k=2, 3>2? k(k+1)/2 for k=2 is 3>2, so no splits. So for i=1,2, we skip the inner loop. The heavy part is for large i. But 44.7e6 iterations might be acceptable in Pyton if optimized in PyPy or Pyton with pypy, but in CP Python we might need to be careful.

Alternatively, we can optimize the inner loop by breaking early? Or note that k must be such that k*(k+1)//2 <= i. The inner loop for i from 1 to n, and k from 2 to floor((sqrt(8*i+1)-1)/2). The total number of iterations is:

sum_{i=1}^{n} (sqrt(2*i)) ~ sqrt(2) * integral_{0}^{n} sqrt(x) dx = sqrt(2) * (2/3) * n^(3/2) = (2*sqrt(2)/3) * n^(3/2)

For n=10^5, n^(3/2) = 10^5 * sqrt(10^5) ≈ 10^5 * 316.2 = 31.6e6. Then multiplied by (2*sqrt(2)/3) ≈ (2*1.414)/3 ≈ 0.94, so about 30e6. That's acceptable in Pyton? In Pyton, 30e6 iterations might run in a few seconds.

But note: we are also doing a mex computation per i. The mex computation: we are building a set of moves, then we do:

mex=0
while mex in moves: mex++

The size of the set `moves`? The number of moves per i is the number of valid k, which is about O(sqrt(i)). So the total work for mex is the size of the set (which is O(sqrt(i))) and the while loop may iterate at most O(sqrt(i)) times? Actually, the mex value is at most the size of the set? Not exactly: mex is the smallest non-negative integer not in the set. The set can have integers that are not consecutive. But the size of the set is O(sqrt(i)), and the mex value is at most the size of the set? Actually, no: the mex can be as large as the size of the set. The while loop will iterate mex+1 times? So worst-case, mex is the size of the set, and we iterate size+1 times. The total work over all i: the sum of sqrt(i) is O(n^(3/2)), which we already have. So the overall complexity is O(n^(3/2)).

But we have two nested loops: the outer i from 1 to n, and inner k from 2 to sqrt(i). Then for each valid k, we compute the XOR over a segment of length k. How? We are doing a constant-time operation using the prefix-xor array. So the inner body per k is O(1). Then the inner loop per i is O(sqrt(i)). So the total is O(n^(3/2)).

Now, after precomputation, we have to find the minimal k for the initial n. That loop: k from 2 to about sqrt(n) (since k(k+1)/2 <= n). The number of k is O(sqrt(n)), which is about 447, so negligible.

So the overall complexity is O(n^(3/2)), which is acceptable for n=10^5.

But let me test with small values.

Test n=1: 
g[1]: no splits (k>=2: minimal k=2, but 2*3/2=3>1). So moves set is empty. mex=0. So g[1]=0.

Then, for the initial n=1: Serozha cannot move? So he loses. But the problem says: output the minimal k if Serozha wins, else -1. So for n=1, output should be -1.

But according to our final step: we look for a split of n=1: k from 2 to ... but k*(k+1)/2<=1 -> no k. So we return -1. Correct.

n=2: similarly, no valid k (k=2: 3>2). So g[2]=0? Then for n=2, Serozha cannot move -> loses -> output -1.

n=3: 
For i=3:
k=2: s=1, total=3+1=4, 4%2==0 -> a=2. Then piles: 2,1. 
Then XOR = g[1] XOR g[2] = 0 XOR 0 = 0.
So moves set = {0}. Then mex = 1? 
So g[3]=1.

Then, for the initial n=3: we look for splits: k=2: valid, and the XOR of the segment [2,1] is g[1]^g[2]=0. So we have a split (k=2) that leads to XOR=0. So we return k=2.

n=4:
What are the splits?
k=2: s=1, total=4+1=5, not divisible by 2 -> skip.
k=3: s=3, total=4+3=7, 7%3 !=0 -> skip.
k=4: minimal sum = 4*5/2=10>4 -> break.
So no valid split? Then g[4]=0? (mex of empty set is 0).

But wait: actually, the mex for n=4: the moves set is empty -> mex=0. So g[4]=0.

Then for the initial n=4: we look for a split that leads to XOR=0. 
k=2: invalid? k=3: invalid. k=4: invalid. So no split -> output -1? But is that correct?

After n=4, Serozha cannot move? So he loses -> output -1. Correct.

n=5:
k=2: s=1, total=6 -> 6%2=0 -> a=3. Then piles: 3,2. 
We need g[2]^g[3] = 0^1=1.
k=3: s=3, total=5+3=8 -> 8%3 !=0 -> skip.
k=4: minimal sum=10>5 -> break.
So moves set = {1}. Then mex=0. So g[5]=0.

Then for initial n=5: we look for splits:
k=2: valid? a= (5+1)/2=3 -> piles: 3,2. Then XOR = g[3]^g[2]=1^0=1 !=0 -> skip.
k=3: invalid.
k=4: invalid.
So no split leads to XOR=0 -> output -1.

n=6: 
We know the only split is k=3: piles 3,2,1. 
So for i=6: 
k=2: (6+1)=7 -> not divisible by 2 -> skip.
k=3: s=3, total=6+3=9, 9%3=0 -> a=3. Then piles: 3,2,1. 
XOR = g[3]^g[2]^g[1] = 1 ^ 0 ^ 0 = 1.
k=4: minimal sum=10>6 -> break.
So moves set = {1}. Then mex=0. So g[6]=0? But wait: mex=0? Because 0 is not in the set? The set is {1}, so mex=0.

But then for initial n=6: we look for splits: k=3: valid, and XOR=1 !=0 -> skip. So we don't have a split that leads to XOR=0. Therefore, output -1. Which matches the example.

But wait, in the example, n=6 output is -1. So correct.

Now, n=100: output 8.

We need to precompute up to n=100. Then, for n=100, we try k from 2 to about 14 (since 14*15/2=105>100, so k max=13? 13*14/2=91<=100, 14*15/2=105>100).

We need to check for each k from 2 to 13: 
- Check if (100 + k*(k-1)//2) is divisible by k.
- Then compute the piles: a = (100 + k*(k-1)//2) / k, and then the consecutive segment from L=a-k+1 to R=a, and compute the XOR of g[L] to g[R] and see if it's 0.

We are looking for the minimal k for which the XOR is 0.

We know from the problem that k=8 should be valid and lead to XOR=0.

But we have to compute the grundy numbers for all i up to 100. So we'll simulate.

But 100 is small, so we can do it by hand? Or write a small program? Alternatively, we can trust the algorithm.

But to verify, let me compute the grundy numbers for small n:

g[0]=0
g[1]=0 (no moves)
g[2]=0 (no moves)
g[3]= mex from split k=2: piles 2,1 -> g[2]^g[1]=0^0=0 -> mex=1? So g[3]=1
g[4]=0 (no moves) -> mex=0
g[5]=0? (only move: k=2: piles 3,2 -> g[3]^g[2]=1^0=1 -> moves={1} -> mex=0 -> g[5]=0
g[6]=0? moves: k=3: piles 3,2,1 -> g[3]^g[2]^g[1]=1^0^0=1 -> moves={1} -> mex=0 -> g[6]=0
g[7]: 
  k=2: a=(7+1)/2=4 -> piles 4,3 -> g[4]^g[3]=0^1=1
  k=3: s=3, total=10, 10%3 !=0 -> skip
  k=4: minimal sum=10>7? 4*5/2=10>7 -> break
  so moves={1} -> mex=0 -> g[7]=0
g[8]:
  k=2: a=(8+1)/2=4.5 -> not integer -> skip
  k=3: s=3, total=11 -> 11%3 !=0 -> skip
  k=4: minimal sum=10<=8? 10<=8? no -> skip? Actually, 10>8 -> no k. So g[8]=0? 
  But wait: k=2: invalid; k=3: invalid; k=4: minimal sum=10>8 -> no moves. So g[8]=0.
g[9]:
  k=2: (9+1)/2=5 -> integer? 10/2=5 -> piles 5,4 -> g[5]^g[4]=0^0=0
  k=3: s=3, total=12, 12%3=0 -> a=4. Then piles: 4,3,2 -> g[4]^g[3]^g[2]=0^1^0=1
  k=4: minimal sum=10<=9? 10<=9 -> no? 10>9 -> skip
  moves={0,1} -> mex=2? 
  So g[9]=2.

But we don't need all. For n=100, we need to check the split for k=8.

For k=8: 
s = 8*7//2 = 28
total = 100+28=128
128 % 8 == 0 -> a=16.
Then the piles: 16,15,14,13,12,11,10,9. 
We need the XOR of g[9] to g[16]. 

But we only computed up to 9: g[9]=2. We need g[10] to g[16]. 

We'll have to compute them. But it's tedious. Alternatively, we can note that the problem states that k=8 is the answer for n=100, so the XOR of g[9] to g[16] must be 0.

But how can we verify? We can write a small program for n=100. But for the purpose of design, we assume the algorithm is correct.

So the steps:

1. Precompute grundy[1..n] and a prefix-xor array for them.
2. For the initial n, iterate k from 2 to max_k (about sqrt(2*n)) to find the minimal k such that:
   - The split is valid: k*(k+1)//2 <= n, and (n + k*(k-1)//2) % k == 0
   - And the XOR of grundy[L..R] (where L = a-k+1, R=a, and a=(n + k*(k-1)//2)//k) is 0.
3. If found, output k; else, output -1.

Now, let's code accordingly.

But note: the problem constraints say n up to 10^5. Our algorithm is O(n^(3/2)), which for n=10^5 is about 10^5 * 316 = 31.6e6, which in Python might be borderline in speed. But we can try to optimize by breaking the inner loop as soon as k*(k+1)//2 > i.

We can also precompute the prefix-xor array as we go, and store the grundy array.

Potential issue: the set `moves` for each i: the number of moves is about sqrt(i), and we do a mex by iterating from 0 until we find a gap. The worst-case mex value might be large? But note: the mex value is at most the number of moves? Actually, the mex is the smallest non-negative integer not in the set. The set can contain integers that are large, but the mex is at most the size of the set? Actually, no: the mex can be 0 even if the set is non-empty. But worst-case, the set might contain 0,1,2,...,m-1, then mex=m, which is the size of the set. The size of the set is O(sqrt(i)). So the while loop runs at most O(sqrt(i)) per i. The total over all i is O(n^(3/2)), which is acceptable.

But we must be careful: the set `moves` is built by iterating over k, and each k gives one value. The number of k is O(sqrt(i)), so the set has at most O(sqrt(i)) elements.

We can use a boolean array for the mex? But the mex values might be large? Actually, the grundy numbers are known to be bounded by the number of moves? The mex value for state i is at most the outdegree (which is O(sqrt(i))). So we can use an array of flags of size (max_mex_expected) per i? But we don't know. Alternatively, we can use a set and then iterate from 0 until we find a gap. Since the set size is about sqrt(i), and we do that for each i, the total cost is O(n^(3/2)) which is acceptable.

Now, we implement accordingly.

But note: the inner loop for k: we can compute k_min=2, k_max = floor((sqrt(8*i+1)-1)/2). So we can compute:

k_min = 2
k_max = int((math.isqrt(8*i+1)-1)//2   # the solution to k(k+1)/2 <= i

But we can iterate k from 2 to k_max.

We'll use math.isqrt.

But note: k_max = (-1 + math.isqrt(1+8*i)) // 2

Because the equation: k(k+1)<=2*i -> k^2+k-2*i<=0 -> k = floor( (-1+sqrt(1+8*i))/2 )

So:

k_max = (math.isqrt(1+8*i) - 1) // 2

But we have to make sure: if 1+8*i is a perfect square? Then it's exact. Otherwise, floor.

Alternatively, we can do:

k = 2
while k*(k+1) <= 2*i:   # because k(k+1)/2 <= i -> k(k+1) <= 2*i
    # do stuff
    k += 1

But the condition: k*(k+1) <= 2*i. The number of iterations is about sqrt(2*i). So same.

But in Python, the multiplication per k might be more expensive than a square root? But k is about 400 at worst, so it's acceptable.

We'll use the while condition.

Now, we code accordingly.

Let me write the code structure:

import math

def main():
    n = int(input().strip())
    if n == 0:
        # but n>=1, so skip
        print(-1)
        return
    # Precompute grundy numbers for i in range(1, n+1)
    grundy = [0] * (n+1)  # 0-indexed: grundy[0] is for 0 stones, not used in game, but for prefix we use.
    # But for pile size 0, we define grundy[0]=0.
    # Precompute prefix-xor array: pref[i] = grundy[0]^grundy[1]^...^grundy[i]
    pref = [0] * (n+1)

    # For i from 1 to n:
    #   We'll compute grundy[i] and then update pref[i] = pref[i-1] ^ grundy[i]

    # Note: for i=0, we set grundy[0]=0, pref[0]=0.
    # For i>=1:
    grundy[0] = 0
    pref[0] = 0
    # If n>=1, we start from i=1.

    for i in range(1, n+1):
        moves = set()
        k = 2
        # Condition: k*(k+1)//2 <= i -> k*(k+1) <= 2*i
        while k*(k+1) <= 2*i:
            s = k*(k-1)//2
            total = i + s
            if total % k == 0:
                a = total // k
                # smallest pile: a - k + 1
                L = a - k + 1
                R = a
                # If R > n? But R = a = (i+s)/k, and i<=n, so R<= (n + k*(k-1)//2)/k <= n/k + (k-1)/2. 
                # Since k>=2, and i<=n, R <= n. But we are computing for i, and we only need indices up to i-1? Actually, R=a and a<=i? 
                # We have: a = (i + k*(k-1)//2) / k <= i/k + (k-1)/2. 
                # Since k>=2, and i>=k*(k+1)//2, then a>=k. 
                # But also, a = (i + s)/k <= (n + k*(k-1)//2)/k. 
                # Since we are iterating k such that k*(k+1)<=2*i, then i>=k(k+1)/2, so a = (i+s)/k = i/k + (k-1)/2 <= (i/k) + k/2. 
                # But i<=n, so a <= n/k + k/2. For k>= sqrt(2n), this might be large? But note: k is bounded by about sqrt(2i) <= sqrt(2n). 
                # So a <= n/(sqrt(2n)) + sqrt(2n)/2 = sqrt(n/2) + sqrt(n/2) = 2 * sqrt(n/2) = sqrt(2n). 
                # For n=10^5, sqrt(2*10^5)≈447, so R=a<=447? Actually, no: for i=n=10^5, k can be up to about 447, then a = (10^5 + 447*446//2)/447 ≈ (100000 + 100000) / 447 ≈ 200000/447≈447. So R is about 447, which is less than n. 
                # But in our precomputation, we are at step i (which is up to n) and we are using indices L and R which are <= a <= 447? Actually, for i=10^5, a is about 447. But what about k=2: a = (10^5+1)/2=50000.5 -> not integer. But for k=447, a=(100000 + 447*446//2)/447 = (100000 + 100000 - a bit) /447 ≈200000/447≈447. So R=447, which is less than i (100000). 
                # However, when i is large (like 10^5) and k is 2, then a = (i+1)/2, which for i=100000 is 50000.5 -> not integer. But for k=2, we require i+1 divisible by 2. 
                # The important point: R = a, which can be as large as about (n + k*(k-1)//2)/k. For k=2, if i is odd, then skip. But for k=2 and i=100000, which is even? Then a=(100000+1)/2=50000.5 -> not integer. Actually, s for k=2 is 1, so total=100000+1=100001, which is odd -> not divisible by 2. 
                # So only even i might have k=2? 
                # But the point: R can be as large as about n/2. For i=10^5, k=2: if i is even? Then a = (i+1)/2? Wait: k=2: s=1, total=i+1, then a=(i+1)/2. So if i is even, then i+1 is odd -> not divisible. So for k=2, only when i is odd? 
                # Actually, no: if i=3: 3+1=4, divisible by 2 -> a=2. 
                # So R can be as large as n? For example, k=2 and i=2*m-1 (odd) then a=m. For i=99999 (if n=10^5), then a= (99999+1)/2=50000. 
                # So R=50000, which is less than i (99999) and we have computed grundy[50000] already? Yes, because we are iterating i from 1 to n. So when i=99999, we have already computed grundy[1..99998] and we are computing grundy[99999]. But we need grundy[50000] which was computed at i=50000. 
                # But note: in our prefix-xor array, we have computed up to i=99998. But when we compute grundy[99999], we need the XOR from L to R (50000 to 50000? for k=2: the piles are a and a-1 -> so two piles: 50000 and 49999). 
                # But 49999 and 50000 are both less than 99999, so we have their grundy numbers and the prefix array is available up to 99998. 
                # However, to compute the XOR from L to R, we use pref[R] ^ pref[L-1]. We have pref for indices up to 99998, which includes 50000. 
                # So we are safe.

                # Check: L = a-k+1 = a-1 (for k=2). Then we need the XOR of grundy[L] and grundy[R] = grundy[a-1] ^ grundy[a]. 
                # But we can compute it as: 
                #   xor_val = (pref[a] ^ pref[a-2]) 
                # because: 
                #   pref[a] = grundy[0]^...^grundy[a]
                #   pref[a-1] = grundy[0]^...^grundy[a-1]
                #   pref[a-2] = grundy[0]^...^grundy[a-2]
                # Then grundy[a-1]^grundy[a] = (pref[a] ^ pref[a-2])? 
                # Actually, we want grundy[a-1]^grundy[a] = (pref[a] ^ pref[a-1]) ^ (pref[a-1] ^ pref[a-2])? 
                # That doesn't work. 
                # Instead, we have:
                #   XOR from L to R = pref[R] ^ pref[L-1]
                # Here, L = a-1, R = a. 
                # So xor_val = pref[a] ^ pref[a-2]? 
                # Actually: 
                #   pref[a] = ... ^ grundy[a-2] ^ grundy[a-1] ^ grundy[a]
                #   pref[a-2] = ... ^ grundy[a-2]
                # Then pref[a] ^ pref[a-2] = grundy[a-1] ^ grundy[a]
                # So yes: 
                #   grundy[L]^grundy[R] = grundy[a-1]^grundy[a] = pref[a]^pref[a-2]
                # But we have pref array for indices up to i-1 (current i=99999, and a=50000, so a-2=49998, which is <=99998). 
                # But note: we have pref array only for indices we've computed. Since a<=i-1, we have computed grundy[a] and grundy[a-1] and ... and pref[a] is available? 
                # Actually, we are storing the entire prefix-xor array as we go. So for any index j <= i-1, we have pref[j] available. 
                # Therefore, we can compute:
                #   xor_val = pref[R] ^ pref[L-1]
                #   where L-1 = a-k (because L=a-k+1, so L-1 = a-k). 
                #   But we have to be cautious: if L-1 <0? Then we use 0? But L=a-k+1, and a>=k, so L>=1 -> L-1>=0. 
                # So:
                xor_val = pref[R] ^ pref[L-1]
                moves.add(xor_val)
            k += 1

        # Now, compute mex
        mex_val = 0
        while mex_val in moves:
            mex_val += 1
        grundy[i] = mex_val
        pref[i] = pref[i-1] ^ grundy[i]

    # Now, after precomputation, find the minimal k for initial n
    ans = 10**9
    found = False
    k = 2
    while k*(k+1) <= 2*n:
        s = k*(k-1)//2
        total = n + s
        if total % k == 0:
            a = total // k
            L = a - k + 1
            R = a
            # If R>n? But a = (n+s)/k, and since n>=k*(k+1)//2, then a>= (k*(k+1)//2 + k*(k-1)//2)/k = (k^2)/k = k. 
            # And a = (n+s)/k <= (n + k*(k-1)//2)/k <= n/k + (k-1)/2. 
            # Since k is at most about sqrt(2*n), then a is at most about n/sqrt(2*n) + sqrt(2*n)/2 = sqrt(n/2) + sqrt(n/2) = sqrt(2*n). 
            # So R = a <= about sqrt(2*n) <= 447 for n=10^5. 
            # But wait: for k=2 and n even? Then a = (n+1)/2, which can be 50000 for n=100000? But we just argued that k=2 and n=100000: n+1=100001 is not divisible by 2. 
            # Actually, for k=2: we require n+1 divisible by 2? So only when n is odd? 
            # But for n=10^5, even. 
            # So for k=2, we skip. 
            # But for k=1? not allowed. 
            # So the maximum a we might get is when k is small? 
            # For k=2, if n is odd, a = (n+1)/2, which can be large (like 50000 for n=100000? but n=100000 is even, so skip). 
            # For k=3: a=(n+3)/3. 
            # The largest a is when k=2 and n is large and odd: then a = (n+1)/2, which is about n/2. 
            # So R = a, which can be about n/2. 
            # But we have computed the grundy numbers and the prefix-xor array for all indices up to n. So we can compute the XOR from L to R. 
            xor_val = pref[R] ^ pref[L-1]
            if xor_val == 0:
                found = True
                if k < ans:
                    ans = k
        k += 1

    if found:
        print(ans)
    else:
        print(-1)

But note: when we compute the XOR for the segment [L, R] in the final step, we use the entire prefix-xor array (which we built for all indices from 0 to n). So we can do.

However, note: in the final step, we are considering splits of n, and the piles are of sizes in [L, R] (which are consecutive). The piles are all <= n? Yes, and we have computed grundy numbers for all indices from 0 to n. So we can use the final prefix-xor array (which includes index n).

But we built the prefix-xor array up to n during the precomputation. So we can use it.

Now, test with n=3: 
In precomputation, for i=3: 
   k=2: s=1, total=4, divisible by 2 -> a=2. 
   Then L=2-2+1=1, R=2.
   xor_val = pref[2] ^ pref[0]   # because L-1=0
   What is pref[2]? 
        grundy[0]=0, grundy[1]=0, grundy[2]=0 -> pref[2]=0
        pref[0]=0
        xor_val = 0^0=0 -> moves.add(0)
   Then mex: smallest non-negative not in {0} is 1 -> grundy[3]=1
   pref[3]=pref[2]^1=0^1=1

Then in the final step for n=3:
   k=2: valid, a= (3+1)/2=2. 
   Then L=1, R=2. 
   xor_val = pref[2]^pref[0]=0^0=0 -> we found a split with k=2 and XOR=0 -> output 2.

Correct.

Now, run with n=6: 
Precomputation:
i=1: moves=empty -> g[1]=0, pref[1]=0
i=2: moves=empty -> g[2]=0, pref[2]=0
i=3: k=2: valid, a=2, L=1, R=2 -> xor_val=pref[2]^pref[0]=0^0=0 -> moves={0} -> mex=1 -> g[3]=1, pref[3]=1
i=4: no moves -> g[4]=0, pref[4]=1
i=5: k=2: a=(5+1)/2=3 -> integer? 6/2=3 -> valid. Then L=3-1=2, R=3. 
        xor_val=pref[3]^pref[1]=1^0=1 -> moves={1} -> mex=0 -> g[5]=0, pref[5]=1
i=6: k=2: (6+1)=7 -> not divisible by 2 -> skip.
        k=3: s=3, total=9, divisible by 3 -> a=3. Then L=3-3+1=1, R=3.
        xor_val=pref[3]^pref[0]=1^0=1 -> moves={1} -> mex=0 -> g[6]=0, pref[6]=1

Then for n=6: 
   k=2: skip (not divisible)
   k=3: valid. a= (6+3)/3=3. Then L=1, R=3. 
        xor_val=pref[3]^pref[0]=1^0=1 -> not 0 -> skip.
   k=4: minimal sum=10>6 -> break.
   So no valid k -> output -1.

Correct.

Now, for n=100, we expect k=8 to be valid and the XOR to be 0.

But we have to compute grundy numbers up to 100? and then check k=8.

But computing 100 is manageable. However, we cannot do by hand. We trust the algorithm.

But we can run a small program for n=100 and see the value for k=8.

Alternatively, we can note that the problem states the answer is 8.

So we'll code accordingly.

But note: the complexity: for n=10^5, the precomputation loop: the inner loop runs about O(sqrt(i)) per i. The total number of iterations is about the sum of sqrt(i) for i from 1 to 10^5, which is about 632456 (using integral of sqrt(x) from 0 to 10^5: (2/3)*(10^5)^(3/2) ≈ 2/3 * 1e7.5 = 2/3 * 31622776.6 ≈ 21e6). Actually, 10^5^(3/2) = 10^5 * sqrt(10^5) = 10^5 * 316.227 ≈ 31.6e6. Then multiplied by 2/3 is about 21e6. So 21e6 iterations, which is acceptable in Pyton if we optimize the inner loop.

But in Pyton, 21e6 iterations might take a few seconds (in Pyton, 1e6 per second? So 21 seconds? That might be too slow for online judges? 

We must optimize.

How to optimize? 

We note that the condition for k: 
   k*(k+1) <= 2*i

We can iterate k from 2 to K_max, and for each k, determine the i for which the split exists? 

Alternatively, we can iterate k first and then for i that are of the form k*a - s, and then update the moves set for i. 

But we need to compute the grundy numbers in increasing order. 

So we can precompute for each k, the range of i: i_min = k*(k+1)//2, i_max = n, and then i = k*a - s, where a is integer and a>=k. 

But then we can iterate a from k to ... such that i = k*a - s <= n. 

Then for each i, we have a split: the piles are a, a-1, ..., a-k+1. 

Then we can compute the XOR for that segment (using the prefix-xor array that we build as we go) and then add that value to the moves set for i. 

But we are building the grundy numbers in increasing order. So when we process i, we have grundy numbers for all j < i. But the piles in the split are a, a-1, ... which are all less than i? Actually, i = k*a - s. Since s = k*(k-1)//2, then i = k*a - k*(k-1)//2. 
And a>=k, so i>= k*k - k*(k-1)//2 = k(k+1)/2. 
But also, a = (i + s)/k. 
Now, a must be less than i? 
We have: a = (i + k*(k-1)//2) / k = i/k + (k-1)/2. 
For k>=2, this is at most i/2 + (k-1)/2. 
For i>= k(k+1)/2, we have a>=k, and then a <= i/2 + (k-1)/2. 
For k>=2, and i>=k(k+1)/2, we have a < i? 
We need: i/k + (k-1)/2 < i. 
Multiply by k: i + k(k-1)/2 < k*i
-> k*i - i > k(k-1)/2
-> i*(k-1) > k(k-1)/2
If k>1, then i > k/2. 
But i>= k(k+1)/2, and for k>=2, k(k+1)/2 >= 3> k/2 (which for k=2 is 1, k=3 is 1.5, etc.). So yes, a < i. 

Therefore, when we process i, the piles are all less than i, so we have their grundy numbers. 

So we can precompute for each k, then iterate a from k to ... until i = k*a - s <= n. 

Then for each i, we add the move: the XOR of the segment [a-k+1, a]. 

But we have to be able to quickly compute the XOR of a consecutive segment. We have a prefix-xor array that is built as we go (for indices up to the current i). But if we iterate by i, we can update the moves set for i as we encounter it. 

But we want to avoid iterating over k and a for each i. 

We can do:

Create an array `moves_for_i` of size (n+1) that is a set or list for moves for each i.

Then for k from 2 to K_max (where K_max is the maximum k such that k*(k+1)//2 <= n; K_max ~ sqrt(2n)):

   s = k*(k-1)//2
   a_min = k
   a_max = ...? From i = k*a - s <= n, so a <= (n + s) // k. 
   Then iterate a from a_min to a_max:
        i = k*a - s
        if i > n: break
        L = a - k + 1
        R = a
        # Now, we want the XOR of grundy[L] to grundy[R] 
        # But we are building grundy in increasing order. When we process i, we have grundy for j < i. 
        # But L and R are <= a < i? (as argued) -> so we have grundy[L..R] computed. 
        # But we also have the prefix-xor array up to i-1. 
        xor_val = pref[R] ^ pref[L-1]
        moves_for_i[i].add(xor_val)

But then, when we iterate i from 1 to n, we compute grundy[i] = mex(moves_for_i[i]), and then update pref[i] = pref[i-1] ^ grundy[i].

But note: when we compute the moves_for_i[i] for a given i, we have to compute it before we compute grundy[i]. 

So we can do:

Initialize:
   moves_for_i = [[] for _ in range(n+1)]   # or sets, but we'll use a list or a set.

Precomputation for k and a:

   for k in range(2, K_max+1):
        s = k*(k-1)//2
        # a_min = k
        # a_max = (n + s) // k   (since i = k*a - s <= n -> a <= (n+s)//k)
        a = k
        while True:
            i_val = k*a - s
            if i_val > n:
                break
            # Only consider i_val in [1, n]
            L = a - k + 1
            R = a
            # We will compute the XOR later? But we need the prefix array for R and L-1. 
            # However, we cannot compute the XOR now because we haven't computed grundy for R? 
            # But at this point, we are in a precomputation phase and we haven't computed grundy for any index? 
            # We are going to compute grundy in increasing order. 
            # So we cannot compute the XOR at this point.

Therefore, we must compute the XOR when we reach i_val in the main loop. 

Alternative: we store for each i_val, the segments that start at i_val. For each i, we have a list of (L, R) pairs for every split that yields i. Then when we reach i in the main loop (from 1 to n), for each (L, R) associated with i, we compute the XOR using the current prefix-xor array (which has been built for indices 0 to i-1) and add that value to the moves set for i.

But note: the segments (L, R) for a given i_val: we generate them in the precomputation. 

Steps:

Precomputation:
   moves_set = [set() for _ in range(n+1)]   # moves_set[i] will hold the XOR values of the next states from i.

   for k in range(2, int(math.isqrt(2*n))+10):   # k from 2 to about sqrt(2*n)
        s = k*(k-1)//2
        # a_min = k
        a = k
        while k*a - s <= n:
            i_val = k*a - s
            if i_val > n: 
                break
            L = a - k + 1
            R = a
            # We store the segment [L, R] for i_val
            # But we cannot compute the XOR now, so we store the segment (L, R) for i_val.
            # moves_set[i_val].add( (L, R) )   # but then we have to store segments, and later iterate over them? 
            # Alternatively, we can store a list of segments per i_val.
            a += 1

But then in the main loop for i from 1 to n:
   for each segment (L, R) in moves_set[i]:
        xor_val = pref[R] ^ pref[L-1]   # we can compute now because R and L-1 are < i (since a < i, as argued) and we have computed grundy and pref for all indices < i.
        moves_temp.add(xor_val)
   then compute mex of moves_temp.

But the problem: we stored the segments. How many segments per i? 

The total number of segments: for each k, a goes from k to a_max = floor((n+s)/k). The total number is about sum_{k=2}^{K} (n / k) ~ n * log(n). For n=10^5, that is about 10^5 * 17 (since harmonic number up to 447 is about 6.5) -> 10^5 * 6.5 = 650000. Then the main loop: for each i, we iterate over the segments stored for i. The total work is 650000, which is acceptable. 

But the issue: in the main loop, for a given i, the number of segments stored for i might be large? But the total over all i is 650000. 

So we can do:

Precomputation:
   segs = [[] for _ in range(n+1)]   # segs[i] is a list of (L, R) for splits that yield state i.
   for k in range(2, K_max+1):
        s = k*(k-1)//2
        # a starts from k, and i_val = k*a - s
        a = k
        while True:
            i_val = k * a - s
            if i_val > n:
                break
            L = a - k + 1
            R = a
            segs[i_val].append( (L, R) )
            a += 1

Then in the main loop for i from 1 to n:
   moves = set()
   for (L, R) in segs[i]:
        xor_val = pref[R] ^ pref[L-1]
        moves.add(xor_val)
   # then compute mex and update grundy and pref

This will be O(total_segments) which is about 650000, and then the mex per i: the size of moves is at most the number of segments for i, which is bounded by the number of representations? But the total work in mex: the sum over i of the size of moves is about 650000, and then the mex for each i: we do a while loop that runs at most the size of moves. The total work for mex is also O(total_segments) because the sum of the sizes of the moves sets is the total number of segments.

But wait: the same xor_val might appear multiple times? Then we use a set. 

But the total work is O(total_segments) for the entire loop.

And total_segments ~ n * H(K_max) ~ n * log(sqrt(n)) = n * (1/2)*log(n) ~ 10^5 * (1/2)*log2(10^5) ≈ 10^5 * 8.3 = 830000. 

This is acceptable.

So we'll implement this optimized version.

Steps:

1. Precompute K_max = floor((math.isqrt(1+8*n)-1)//2)  # actually, we can let k go up to  while k*(k+1) <= 2*n: ... but in the outer loop, we can let k from 2 to int(math.isqrt(2*n)) + 1, and then a from k to

## Final Solution

