# Solution for abc328_c.md

Generated at: 2025-07-22 06:41:30
Model: deepseek-reasoner

## Reasoning Process

We are given a string S of length N and Q queries. Each query (l, r) asks for the number of indices p in [l, r-1] such that S[p] == S[p+1].

The constraints (N, Q up to 300,000) rule out a brute-force solution that checks each query by iterating over the substring.

Idea:
We note that the condition S[p] == S[p+1] is a property of consecutive pairs. We can precompute an array A of length N-1 (or of length N with the last element 0) such that:
  A[i] = 1 if S[i] == S[i+1] (for i from 0 to N-2), and 0 otherwise.

Then, the problem reduces to: for each query (l, r), we want to compute the sum of A in the segment [l-1, r-2] (if we use 0-indexing for the array A).

Why?
- The substring from index l to r (1-indexed) has consecutive pairs starting at positions: l, l+1, ..., r-1. In 0-indexed, the string indices are from l-1 to r-1. The consecutive pairs are at indices: l-1, l, ..., r-2 (because the last pair is between the second last and last character of the substring).

So we can define:
  Let A[0] = 1 if S[0]==S[1] else 0, ..., A[i] = 1 if S[i]==S[i+1] else 0, for i from 0 to N-2.

Then the answer for a query (l, r) is the sum of A from index (l-1) to index (r-2). However, note that if the substring length is 1 (i.e., r-l+1 == 1), then there are no pairs, so the sum over an empty interval is 0.

But note: if the substring has length L, then the number of consecutive pairs is L-1. However, we are only counting the pairs that are adjacent and equal.

Therefore, we can precompute a prefix sum array P for A, such that:
  P[0] = A[0]
  P[i] = P[i-1] + A[i] for i>=1

Then for a query (l, r):
  If r - l < 1 (i.e., the substring length is 1 or less), the answer is 0.
  Otherwise, we want to compute the sum of A from index (l-1) to (r-2). That is:
      ans = P[r-2] - P[l-2]   [if we define P for indices 0..N-2]

But note: if l-1 is 0, then we cannot do P[l-2] because that would be negative. So we adjust:

Let's define:
  Let P[0] = 0, and for i from 1 to N-1 (for the array A which has length N-1), we can let:
      P[i] = P[i-1] + A[i-1]   [so that P[1] = A[0], P[2] = A[0]+A[1], ...]

Alternatively, we can define:
  Let P[i] = A[0] + A[1] + ... + A[i-1] for i>=1, and P[0]=0.

Then the sum from index a to index b (inclusive) in A is P[b+1] - P[a].

So for a query (l, r) (1-indexed):
  The consecutive pairs we care about are at indices from (l-1) to (r-2) (0-indexed in the array A). 
  We can compute:
      left_index = l-1   (first index in A we need)
      right_index = r-2  (last index in A we need)

  But note: if l-1 > r-2, then the interval is empty -> return 0.

So:
  if r - l < 1: answer = 0
  else: answer = P[right_index+1] - P[left_index]

But with our prefix sum defined for indices 0..(N-1) (for P, which has length N) we have:
  P[i] for i from 0 to N: 
      P[0] = 0
      P[1] = A[0]
      P[2] = A[0] + A[1]
      ...
      P[k] = A[0] + ... + A[k-1]

Then the sum of A from a to b inclusive is P[b+1] - P[a].

In our case:
  a = l-1, b = r-2.

So the answer is: 
   if r - l < 1: 0
   else: P[r-1] - P[l-1]   because:
        We need P[b+1] = P[r-2+1] = P[r-1] 
        and P[a] = P[l-1]

But note: our A array has indices 0 to N-2. The prefix P is defined for indices 0 to N (with P[0]=0, P[1]=A[0], ..., P[N-1] = A[0]+...+A[N-2], P[N] is not used).

So we can build an array A of length N-1, and then a prefix sum array P of length N (indexed 0..N-1) where:
   P[0] = 0
   for i from 1 to N-1: P[i] = P[i-1] + A[i-1]

But then note: the last index we need for P is r-1, and r-1 can be up to N (because r <= N). However, note that r-1 is at most N-1 because r<=N. So P[r-1] is defined.

Alternatively, we can avoid the if condition for the length by defining the prefix sum array to have N elements (0-indexed from 0 to N) and then if the interval is empty, the subtraction will be 0? Actually, if the interval is empty (l-1 > r-2) then we return 0. But we can write:

   if l-1 <= r-2:
        ans = P[r-1] - P[l-1]
   else:
        ans = 0

But note: when l-1 == r-1? Actually, if the substring has length 2, then l-1 = a, and r-2 = a, so we have one element.

But we can also note that the condition l-1 <= r-2 is equivalent to r-l>=2? Actually, if the substring length is L, then we have L-1 consecutive pairs. We are only counting the pairs that are adjacent and equal. And we are summing over the indices of the consecutive pairs that fall in the substring.

But the condition for having at least one pair is that the substring length is at least 2.

So we can write:

   if r - l >= 1:  # actually, we need at least two adjacent characters? Actually, we need at least two characters to form a pair. So if the substring length is 1 (r-l+1=1) then no pairs. So condition: if the substring length is 1, then no pairs. But note: the pairs are at positions from l to r-1 (1-indexed). So if r-l+1 >=2, then we have at least one pair? Actually, the number of pairs is (r-l) in terms of consecutive pairs? But we are counting only the ones that are equal.

But the condition for the indices in A: we have an array A of length N-1. The valid indices for A are 0 to N-2. The query [l, r] (1-indexed) corresponds to the segment in A from index (l-1) to (r-2). This segment is valid only if l-1 <= r-2 and l-1>=0 and r-2<=N-2. Since l>=1, so l-1>=0. And r<=N, so r-2<=N-2 only if r>=2? Actually, if r==1, then r-2=-1 -> invalid. But the condition r>=l and l>=1, so if r==1 then l must be 1. Then we have a substring of length 1 -> no pairs. So we can avoid the explicit bounds by:

   if r - l < 1: # meaning r-l+1 < 2 -> no pairs
        ans = 0
   else:
        ans = P[r-1] - P[l-1]

But note: our prefix P is defined such that P[i] = sum(A[0:i]) = A[0] + ... + A[i-1]. Then the sum from a to b inclusive (a and b being indices in A) is P[b+1] - P[a]. In our case, we want to sum from a = l-1 to b = r-2. So:
   ans = P[r-1] - P[l-1]   [because b+1 = (r-2)+1 = r-1, and a = l-1]

But wait: 
   P[r-1] = A[0] + ... + A[r-2]
   P[l-1] = A[0] + ... + A[l-2]
   So subtracting: A[l-1] + ... + A[r-2] -> which is exactly the segment we want.

So we can do:

   Precomputation:
      A = [1 if S[i] == S[i+1] else 0 for i in range(N-1)]
      P = [0]*(N)   # we only need indices 0 to N-1, but we can make it of length N (for indices 0..N-1) but we will use indices 0..N? Actually, we can make P of length N+1 to avoid index issues? 

Alternative common prefix-sum practice: we make P of length N (for the A array which has N-1 elements) but we want to cover indices 0 to N? Actually, we can do:

   P[0] = 0
   for i in range(1, N):   # because we have N-1 elements in A, and we want P[i] = A[0]+...+A[i-1]
        P[i] = P[i-1] + A[i-1]

But then for a segment [a, b] in A, we use P[b+1]-P[a]. However, note that in our case we have defined A for indices 0 to N-2. Then the segment [a, b] in A (0-indexed) is from a to b inclusive, and the sum is P[b+1]-P[a]. 

But if we use the above definition of P (which has length N) then:
   P[0] = 0
   P[1] = A[0]
   P[2] = A[0]+A[1]
   ...
   P[N-1] = A[0]+...+A[N-2]

Then for a query (l, r):
   a = l-1 (first index in A we need)
   b = r-2 (last index in A we need)

   Then the sum is P[b+1] - P[a] = P[r-1] - P[l-1]? 
        But note: b+1 = (r-2)+1 = r-1, and a = l-1.
        So we require P[r-1] and P[l-1].

But what if l=1? Then we need P[0] which is 0 -> that's fine.
What if r = N? Then we need P[N-1] which is defined.

But note: if r = N, then the last pair we look at is at index r-2 = N-2, which is the last element of A. Then we use P[N-1] - P[l-1]. 

However, what if the substring length is 1? Then l-1 = some index and r-2 = l-1 - 1? Actually, if the substring has length 1, then r = l, so r-2 = l-2. Then we have l-1 and l-2: but note we require the condition that l-1 <= l-2? That is only if l-1 <= l-2, which implies -1<=0? Actually, we must avoid negative indices. But we have an if condition: if r-l < 1, then we skip. Alternatively, we can do:

   We can avoid the if condition by defining the prefix array for the entire range and then if the segment [l-1, r-2] is empty, then the formula P[r-1]-P[l-1] would be subtracting the same value? Actually, if r-1 < l-1, then we should return 0. But note: l-1 <= r-2  <=> l-1 <= r-2  <=> r-l>=1? Actually, if the substring has length L, then L = r-l+1, and we require at least two characters: L>=2 -> r-l>=1. So if r-l>=1, then l-1 <= r-2? 
        l-1 <= r-2  =>  l-1 <= r-2  => r-l>=1. 
        But note: if r-l=0, then r-2 = l-2, and l-1 = l-1, so l-1 > l-2? Then the segment [l-1, l-2] is invalid. So we must condition on r-l>=1.

So we can write:

   for each query (l, r):
        if r - l < 1:
            ans = 0
        else:
            ans = P[r-1] - P[l-1]

But we can avoid the condition by setting:
        ans = 0
        if r - 1 >= l-1:   # which is equivalent to r-l>=0, but actually we need r-l>=1 to have at least one element? 
            But note: the segment [l-1, r-2] is non-empty only if l-1 <= r-2, i.e., r-l>=1.

Alternatively, we can use:
        ans = P[r-1] - P[l-1]   if r-1 >= l-1, else 0.

But note: if r-l==0, then r-1 = l-1, so P[r-1]-P[l-1]=0. So actually, we don't need the condition? 

Let me check:
   For a substring of length 1: l, r such that r = l.
        Then we want the segment [l-1, r-2] = [l-1, l-2] -> which is empty. 
        We would compute: P[r-1] - P[l-1] = P[l-1] - P[l-1] = 0.

So it's safe to compute without the condition.

Therefore, we can simply do:
   ans = P[r-1] - P[l-1]

But wait: what if l=1 and r=1? Then we compute P[0]-P[0]=0 -> correct.

What if l=1 and r=2? Then we compute P[1] - P[0] = A[0] - 0 = A[0] -> which is 1 if S[0]==S[1], else 0 -> correct.

What if l=1 and r=3? Then we compute P[2] - P[0] = (A[0]+A[1]) - 0 = A[0]+A[1] -> which is the count for positions 0 and 1 -> which are the pairs (0,1) and (1,2) -> correct.

So we don't need the condition.

Therefore, the plan is:

1. Precomputation:
   - Read N, Q and the string S.
   - Precompute an array A of length N-1: 
          A[i] = 1 if S[i] == S[i+1] else 0, for i in range(0, N-1).
   - Precompute a prefix sum array P of length N (so that we can index from 0 to N-1) as:
          P[0] = 0
          for i in range(1, N):   # i from 1 to N-1
              P[i] = P[i-1] + A[i-1]

   But note: the last element we compute is P[N-1] = A[0] + A[1] + ... + A[N-2]. We don't use P[N] (if we had defined it) but we don't need it.

2. For each query (l, r):
        ans = P[r-1] - P[l-1]

But note: the indices l and r are 1-indexed. We are using 0-indexed arrays.

Example: 
   "mississippi", N=11.
   A: 
        S: m i s s i s s i p p i
        Check consecutive:
          m-i: 0
          i-s: 0
          s-s: 1 -> index2 (0-indexed in S: index2 is the second 's')? Actually, we are at the first 's' (index2) and the next is the second 's' (index3). So A[2]=1.
          s-i: 0
          i-s: 0
          s-s: 1 -> index5: because at S[5] and S[6] are both 's'
          s-i: 0
          i-p: 0
          p-p: 1 -> index8: because S[8] and S[9] are 'p'
          p-i: 0
        So A = [0,0,1,0,0,1,0,0,1,0] (length 10)

   Then P: 
        P[0]=0
        P[1]=A[0]=0
        P[2]=0+0=0
        P[3]=0+0+1=1
        P[4]=1+0=1
        P[5]=1+0=1
        P[6]=1+1=2
        P[7]=2+0=2
        P[8]=2+0=2
        P[9]=2+1=3
        P[10]=3+0=3   (we only need up to P[10] because N=11, and we have 10 elements in A)

   Query1: "3 9" -> l=3, r=9 -> ans = P[9-1] - P[3-1] = P[8]-P[2] = 2 - 0 = 2 -> correct.
   Query2: "4 10" -> l=4, r=10 -> ans = P[10-1] - P[4-1] = P[9]-P[3] = 3 - 1 = 2 -> correct.
   Query3: "4 6" -> l=4, r=6 -> ans = P[6-1] - P[4-1] = P[5]-P[3] = 1 - 1 = 0 -> correct.
   Query4: "7 7" -> l=7, r=7 -> ans = P[7-1] - P[7-1] = P[6]-P[6] = 2-2=0 -> correct.

But wait: for the second query: 
   "4 10": 
        In 0-indexed: substring from index3 to index9: "sissipp"
        pairs: 
            at index5: S[5] and S[6] -> 's' and 's' -> 1 (this corresponds to A[5]=1)
            at index8: S[8] and S[9] -> 'p' and 'p' -> 1 (A[8]=1)
        So total 2.

   We computed: P[9]=3? But we defined P[9] = A[0]+...+A[8] = 0+0+1+0+0+1+0+0+1 = 4? Actually, wait: 
        A = [0,0,1,0,0,1,0,0,1,0] -> indices 0..9 (but we only have 10 elements). 
        P[0]=0
        P[1]=A[0]=0
        P[2]=A[0]+A[1]=0
        P[3]=A[0]+A[1]+A[2]=1
        P[4]=1+A[3]=1
        P[5]=1+A[4]=1
        P[6]=1+A[5]=1+1=2
        P[7]=2+A[6]=2
        P[8]=2+A[7]=2
        P[9]=2+A[8]=3
        P[10]=3+A[9]=3

   So P[9]=3? Then P[9]-P[3] = 3-1=2 -> correct.

But wait, the pairs in the substring "sissipp" (index3 to index9) are at:
   In the entire string, the pairs are at indices 2,5,8 (0-indexed in the A array? Actually, A[2] is the pair (S[2],S[3]) -> which is the first "ss", but that is at index2 and 3. The substring "sissipp" starts at index3 (so we skip the first 's' that formed a pair with the previous? Actually, the substring "sissipp" is from index3 to index9: 
        S[3] = 's', S[4]='i' -> not a pair -> A[3]=0
        S[4]='i', S[5]='s' -> not a pair -> A[4]=0
        S[5]='s', S[6]='s' -> pair -> A[5]=1 -> index5 in A
        S[6]='s', S[7]='i' -> not -> A[6]=0
        S[7]='i', S[8]='p' -> not -> A[7]=0
        S[8]='p', S[9]='p' -> pair -> A[8]=1 -> index8 in A
   So the pairs we count are at A[5] and A[8]. The prefix P at index9 (which is the sum of A[0..8]) is 3, and at index3 (which is the sum of A[0..2]) is 1. Then 3-1=2.

However, note: the pair at A[2] (which is the first "ss") is at indices (2,3) in S. But our substring starts at index3 (so we don't include the pair at (2,3) because the first character of the substring is at index3, so the pair at (2,3) is not in the substring). 

Therefore, the method is correct.

But note: the prefix P we built is for the entire array A. The prefix P[i] is the sum of A[0] to A[i-1]. Therefore, the sum of A from a to b is P[b+1] - P[a]. However, in our computation for the query, we are using:
        ans = P[r-1] - P[l-1]

Why is that the same as the sum from a=l-1 to b=r-2?
        We want: sum_{i=l-1}^{r-2} A[i] = P[r-1] - P[l-1] 
        because: 
            P[r-1] = A[0]+...+A[r-2]   [since we have to take the first r-1 elements of A: indices 0..r-2]
            P[l-1] = A[0]+...+A[l-2] 
            then subtract: we get A[l-1] + ... + A[r-2] 

So it is correct.

Implementation:

  Steps:
      N, Q = map(int, input().split())
      S = input().strip()
      # Precompute A: length N-1
      A = [0]*(N-1)
      for i in range(N-1):
          if S[i] == S[i+1]:
              A[i] = 1

      # Precompute prefix P: we want an array of length N (for indices 0 to N-1) such that P[0]=0 and P[i]=P[i-1]+A[i-1] for i in range(1, N)
      P = [0]*N
      # If N==1, then A is empty, then we skip the loop for P? But then we don't have any A. So we need to handle N==1 separately? Actually, if N==1, then we have no consecutive pairs. And for each query: if the substring has length 1, then we return 0. But we can build P as [0] for the entire array? Then for any query: ans = P[r-1]-P[l-1]. But if N==1, then P has length 1: only P[0]=0. Then if we get a query (1,1): then we compute P[0]-P[0]=0. Correct.

      So:
          P[0] = 0
          for i in range(1, N):
              P[i] = P[i-1] + A[i-1]

      But note: if N==1, the for loop for P runs from 1 to N-1 -> 1 to 0 (if N==1, then range(1,1) is empty). So we get P = [0].

      Then process queries:
          for each query (l, r):
              # we compute ans = P[r-1] - P[l-1] 
              # But note: if l=1, then we use P[0] (which exists). If r=N, then we use P[N-1] (which exists because we built P of length N, indices 0..N-1).

      Print the answer.

Complexity:
   Precomputation: O(N) for A and O(N) for P -> total O(N)
   Each query: O(1)
   Total: O(N+Q)

This meets the constraints (N, Q up to 300,000).

Edge: 
   N=1: 
        A is not built? Actually, we build A for length N-1 -> 0, so the loop for A doesn't run? 
        Then P is built as [0] (length 1). 
        Then for any query: (l, r) with l=r=1 -> ans = P[0]-P[0]=0.

   Example: 
        Input: "5 1", "aaaaa", query: (1,5)
        A = [1,1,1,1] (for N=5, so A length 4)
        P = [0, 1, 2, 3, 4] (length 5: indices0 to 4)
        Query: l=1, r=5 -> ans = P[4]-P[0]=4-0=4.

Therefore, we can code accordingly.

Let me write the code accordingly.

Note: We must be cautious about the indices.

Alternative: we can use a prefix array of length N+1 to avoid confusion? 

Common practice: we make P of length N+1 (index 0 to N) such that:
      P[0]=0
      for i in range(1, N):   # but we have A of length N-1, so we can do:
          P[i] = P[i-1] + (1 if i-1 < len(A) and S[i-1]==S[i] else ...) 
But we already built A.

Alternatively, we can build the prefix without building A explicitly? We can build P directly:

      P[0] = 0
      for i in range(1, N):
          # P[i] = P[i-1] + (1 if S[i-1]==S[i] else 0)
      Then for i from N-1 to ...? Actually, we don't need the entire array? We can do:

      P = [0]*(N+1)   # indices 0 to N
      for i in range(1, N):
          P[i] = P[i-1] + (1 if S[i-1]==S[i] else 0)
      # But then note: we are not including the last consecutive pair? Actually, the consecutive pairs are from index0 to index N-2. So we can do:

      for i in range(0, N-1):
          if S[i]==S[i+1]:
              P[i+1] = P[i] + 1
          else:
              P[i+1] = P[i]

      Then we extend P to the last index? Actually, we want to cover indices 0..N. Then for a query (l, r):
          We want the sum of the consecutive pairs from index (l-1) to (r-2) in the array of consecutive pairs? But note: the consecutive pair at position i (0-indexed) is stored in P at index i+1? 

      Actually, with P defined as a prefix sum of the consecutive pairs array (which we call A) but without storing A, we can do:

          Let P[0]=0
          for i in range(0, N-1):
              add = 1 if S[i]==S[i+1] else 0
              P[i+1] = P[i] + add
          Then for indices from N-1 to N: we set P[i] = P[i-1]? 

      But we want a prefix that goes up to N. Then we can do:

          P = [0]*(N+1)
          for i in range(0, N-1):
              P[i+1] = P[i] + (1 if S[i]==S[i+1] else 0)
          for i in range(N-1, N+1):
              P[i] = P[i-1]   # but we don't use beyond N? Actually, we only need up to N.

      Then the consecutive pairs from index a to index b (in the consecutive pairs array) is the same as the consecutive pairs that start at position a (in S) to position b (in S). And the consecutive pairs array has the pair at index i (0-indexed) as the pair (S[i], S[i+1]). 

      Then for a query (l, r): we want the consecutive pairs that start at positions from l-1 to r-2 (in the consecutive pairs array). How to express that in the prefix P?

        The prefix P we built: 
            P[i] = number of consecutive pairs from the beginning to the pair at index i-1? 
            Actually, P[0] = 0 -> no pairs considered.
            P[1] = consecutive pair at index0? 
            P[2] = consecutive pairs at index0 and index1?

        So the consecutive pair at index i is stored in P[i+1]? 
            For the consecutive pair at index0: we have P[1] = 1 if it exists, else 0.

        Then the total consecutive pairs from index a to b inclusive is P[b+1] - P[a].

        In our case: 
            a = l-1, b = r-2.
            So the sum = P[r-1] - P[l-1]? 
                Because: we need P[b+1] = P[r-2+1] = P[r-1]
                and P[a] = P[l-1]

        This is the same as before.

      But note: we built P for indices 0 to N. Then we can use:

          ans = P[r-1] - P[l-1]

      However, if we built P for indices 0..N, then we can do this without an explicit A.

      Steps for this method:
          P = [0]*(N+1)
          for i in range(0, N-1):
              if S[i] == S[i+1]:
                  P[i+1] = P[i] + 1
              else:
                  P[i+1] = P[i]
          # Then for i from N-1 to N: we set? Actually, we don't need to set because we already set the entire array? We set P[0]=0, then we set P[1]...P[N-1]. Then we want to set P[N] = P[N-1]? 
          # But we don't use P[N] in our queries? The maximum index we use is r-1, and r<=N, so r-1 <= N-1. So we don't need to set P[N]? But we allocated N+1 elements, and we set P[0..N-1]. The last element we set is P[N-1] (if we run the loop for i from 0 to N-2). Then for any query, we only use indices from 0 to N-1.

      Example: N=5, "aaaaa"
          P[0]=0
          i=0: 'a'=='a' -> P[1]=P[0]+1=1
          i=1: 'a'=='a' -> P[2]=P[1]+1=2
          i=2: 'a'=='a' -> P[3]=3
          i=3: 'a'=='a' -> P[4]=4
          Then we don't set P[5]? But we allocated 6 elements? Then P[5] is 0? But we don't use it. 
          Query (1,5): l=1, r=5 -> ans = P[5-1] - P[1-1] = P[4]-P[0]=4-0=4 -> correct.

      But wait: we did not set P[5]. However, we set P for indices 0 to 4. Then P[4]=4. Then we don't need P[5] for the query? 

      Actually, we are using P[r-1] and P[l-1]. For r=5, we use P[4]. So we are safe.

      So we can avoid building A explicitly.

      This method uses one loop and builds P directly. And we don't have to store an extra array A.

      Space: O(N) for P.

      Then we process Q queries, each in O(1).

Implementation:

      Read N, Q
      S = input().strip()

      # If N==0? Constraints: N>=1, so we skip.
      P = [0]*(N+1)   # indices 0 to N: we will set indices 0 to N-1, and we don't use index N? Actually, we only use indices from 0 to N-1.

      # If N==1, then we skip the loop.
      for i in range(0, N-1):
          if S[i] == S[i+1]:
              P[i+1] = P[i] + 1
          else:
              P[i+1] = P[i]

      # For the last index? We don't need to set beyond N-1? Actually, the last consecutive pair we set is at index N-2 (for the pair (S[N-2], S[N-1]) and we set P[N-1] = ... 
      # But we don't set P[N]? We don't need it.

      Then for each query (l, r):
          # Note: l and r are 1-indexed
          # We want to output: P[r-1] - P[l-1]
          # Since our P is 0-indexed and we built for indices 0 to N-1, and r-1 <= N-1 (because r<=N) and l-1<=N-1 (because l>=1), we can do:
          ans = P[r-1] - P[l-1]
          print(ans)

      But note: the problem says to output each answer on a separate line.

      Alternatively, we can store the answers and then print.

      Example: N=1 -> no loop for the consecutive pairs. Then P[0]=0. Then for a query (1,1): 
          ans = P[1-1] - P[1-1] = P[0]-P[0]=0 -> correct.

      However, what if we try to access P[?] for a query with r>1? But if N==1, then r must be 1. So we are safe.

      But what if we have a query with r=N? Then we use P[N-1]. For N==1, that is P[0] -> safe.

      Therefore, we can do.

Let me code accordingly.

But note: the problem input has Q queries after the string, and we must read them.

Code structure:

      import sys
      data = sys.stdin.read().splitlines()
      first_line = data[0].split()
      N = int(first_line[0]); Q = int(first_line[1])
      S = data[1].strip()
      queries = []
      for i in range(2, 2+Q):
          line = data[i].split()
          l = int(line[0]); r = int(line[1])
          queries.append((l, r))

      Then build the prefix array P (length N+1, but we only use indices 0 to N-1).
      Then for each query, compute ans = P[r-1]-P[l-1] and output.

But note: the problem says to output each answer on a separate line.

We'll test with the examples.

Example1: "mississippi", queries: 
      Query1: (3,9) -> l=3, r=9 -> ans = P[8]-P[2]
      How do we build P for "mississippi" (N=11, so we build P[0..11]? Actually, we build an array of 12 zeros? Then we run the loop for i from 0 to 9 (because N-1=10, so i from 0 to 9 inclusive?).

      We do:
          P[0]=0
          i=0: S[0]=='m', S[1]=='i' -> not equal -> P[1]=0
          i=1: S[1]=='i', S[2]=='s' -> not -> P[2]=0
          i=2: S[2]=='s', S[3]=='s' -> equal -> P[3]=P[2]+1=1
          i=3: S[3]=='s', S[4]=='i' -> not -> P[4]=1
          i=4: S[4]=='i', S[5]=='s' -> not -> P[5]=1
          i=5: S[5]=='s', S[6]=='s' -> equal -> P[6]=1+1=2
          i=6: S[6]=='s', S[7]=='i' -> not -> P[7]=2
          i=7: S[7]=='i', S[8]=='p' -> not -> P[8]=2
          i=8: S[8]=='p', S[9]=='p' -> equal -> P[9]=3
          i=9: S[9]=='p', S[10]=='i' -> not -> P[10]=3
          Then we don't set P[11]? But we allocated 12 elements, and we set P[0..10]. 

      Then for query (3,9): 
          l=3 -> index = l-1 = 2 -> P[2]=0
          r=9 -> index = r-1=8 -> P[8]=2
          ans = 2-0 = 2 -> correct.

      Query2: (4,10): 
          l=4 -> P[3]=1
          r=10 -> P[9]=3
          ans = 3-1=2 -> correct.

      Query3: (4,6): 
          l=4 -> P[3]=1
          r=6 -> P[5]=1
          ans = 1-1=0 -> correct.

      Query4: (7,7): 
          l=7 -> P[6]=2
          r=7 -> P[6]=2
          ans = 2-2=0 -> correct.

It matches.

Therefore, we can implement the solution as described.

Let me code accordingly.

Note: We can avoid building the entire list of queries first if we want, but since Q can be 300,000, reading first and storing is acceptable.

Code:

      import sys

      def main():
          data = sys.stdin.read().splitlines()
          if not data: 
              return
          first_line = data[0].split()
          n = int(first_line[0])
          q = int(first_line[1])
          s = data[1].strip()
          # Build prefix array P of length n+1 (index 0 to n)
          P = [0] * (n+1)
          # If n==0, skip the loop? But n>=1 by constraints.
          for i in range(0, n-1):
              if s[i] == s[i+1]:
                  P[i+1] = P[i] + 1
              else:
                  P[i+1] = P[i]
          # For the last element? We set P[n]? Actually, we don't need it. But we can set the last one to the same as the previous? But we don't use beyond n-1? 
          # We only use indices 0 to n-1 (because r-1 <= n-1). So we don't need to set P[n] explicitly.

          # Process the queries:
          out_lines = []
          for i in range(2, 2+q):
              parts = data[i].split()
              l = int(parts[0])
              r = int(parts[1])
              # We want: P[r-1] - P[l-1]
              # Note: our P is 0-indexed and we have built for indices 0 to n-1 (and we also set P[n] but we don't use it). 
              # l-1 is at least 0 and at most n-1? Yes, because l>=1 -> l-1>=0, and l<=n -> l-1<=n-1.
              # r-1: similarly, r>=l and r<=n -> r-1<=n-1.
              ans = P[r-1] - P[l-1]
              out_lines.append(str(ans))

          # Output each answer on a separate line.
          sys.stdout.write("\n".join(out_lines))

      if __name__ == '__main__':
          main()

But note: when n==1, we don't enter the loop (because n-1=0, so range(0,0) is empty). Then P[0]=0. Then for a query (1,1): 
        ans = P[0]-P[0]=0 -> correct.

This should work.

Let me test with the example "aaaaa", n=5, q=1, query (1,5):
        P = [0]*6 -> [0,0,0,0,0,0]
        Then we run the loop for i in [0,4): 
            i=0: s[0]=='a', s[1]=='a' -> P[1]=P[0]+1=1
            i=1: s[1]=='a', s[2]=='a' -> P[2]=1+1=2
            i=2: s[2]=='a', s[3]=='a' -> P[3]=2+1=3
            i=3: s[3]=='a', s[4]=='a' -> P[4]=3+1=4
        Then for the query: l=1, r=5 -> ans = P[4]-P[0] = 4-0=4 -> correct.

So it works.

Therefore, we'll implement as above.

But note: the problem has the function signature:

      class Solution:
          def solve(self, N: int, Q: int, S: str, queries: List[Tuple[int, int]]) -> List[int]:

We can adapt:

      We are given the queries as a list of tuples.

      We don't need to read from stdin.

      So:

          def solve(self, N: int, Q: int, S: str, queries: List[Tuple[int, int]]) -> List[int]:
              # Build prefix array P of length N+1 (index0 to N)
              P = [0]*(N+1)
              # If N==1, skip the loop. But we can run the loop for i in range(0, N-1)
              for i in range(0, N-1):
                  if S[i] == S[i+1]:
                      P[i+1] = P[i] + 1
                  else:
                      P[i+1] = P[i]
              # Now, we don't need to set P[N] because we won't use it? 
              # But we built P of length N+1, and the last element we set is P[N-1] (if N>=2) and if N==1, then we didn't set any beyond P[0] (which is 0) and P[1] is not set? Actually, for N==1: the loop doesn't run, so P[0] is set (to 0) and that's all we need.

              ans_list = []
              for (l, r) in queries:
                  # For the substring from l to r (1-indexed), the number of consecutive pairs is P[r-1] - P[l-1]
                  res = P[r-1] - P[l-1]
                  ans_list.append(res)

              return ans_list

But note: in the prefix array P, we set P[i] for i in [0, N-1]. For indices beyond that? We don't use them. For a query (l, r) with r<=N, then r-1<=N-1, so we are safe.

Let me test with the examples.

      Example: N=1, S="a", queries: [(1,1)] -> 
          P = [0] (if we allocated N+1, then we have P[0]=0, and P[1] is also set to 0? Actually, we allocated [0]*(N+1) -> [0,0]? Then for the query: 
          l=1, r=1: 
              res = P[0] - P[0] = 0-0=0 -> correct.

      But wait: we built P as [0]*(N+1) -> for N=1: P = [0,0]. Then we did not run the loop. Then for the query: 
          P[r-1] = P[0] = 0? 
          P[l-1] = P[0] = 0? 
          Then 0.

      However, what if we have a query (1,2) for N=1? That would be invalid because the string length is 1, so r must be 1. But the constraint says: 1<=l<=r<=N. So r cannot be 2 if N=1.

      Therefore, we are safe.

      Example: N=5, S="aaaaa", queries=[(1,5)] -> 
          We build P = [0, 1, 2, 3, 4, ?] -> we allocated 6 elements. Then we set:
              P[0]=0
              P[1]=1
              P[2]=2
              P[3]=3
              P[4]=4
              P[5]=? -> we didn't set it, but we don't use it. 
          Query: (1,5): 
              P[5-1] = P[4]=4, P[1-1]=P[0]=0 -> 4.

      But wait: we set P[5]? Actually, we allocated 6 elements and set only indices 0 to 4? Then P[5] is 0? Then in the query we are using P[4] and P[0] -> that's fine.

      However, in the loop we only set indices 1 to 4 (for i in range(0,4): because N-1=4, so i from 0 to 3). Then P[5] remains 0. But we don't use it.

      So the code is safe.

We'll return the list.

But note: what if N==0? The constraints say N>=1, so we don't worry.

Let me run the example "mississippi" with the code:

      S = "mississippi", N=11
      P[0]=0
      i=0: 'm' and 'i' -> not -> P[1]=0
      i=1: 'i' and 's' -> not -> P[2]=0
      i=2: 's' and 's' -> yes -> P[3]=0+1=1
      i=3: 's' and 'i' -> not -> P[4]=1
      i=4: 'i' and 's' -> not -> P[5]=1
      i=5: 's' and 's' -> yes -> P[6]=1+1=2
      i=6: 's' and 'i' -> not -> P[7]=2
      i=7: 'i' and 'p' -> not -> P[8]=2
      i=8: 'p' and 'p' -> yes -> P[9]=3
      i=9: 'p' and 'i' -> not -> P[10]=3
      Then we stop. We have set P[0] to P[10] (indices 0..10). 

      Query (3,9): l=3, r=9 -> ans = P[8] - P[2] = 2 - 0 = 2 -> correct.

Therefore, we implement the solution as described.

Code:

      from typing import List, Tuple

      class Solution:
          def solve(self, N: int, Q: int, S: str, queries: List[Tuple[int, int]]) -> List[int]:
              # If N is 0, then we return [0]*Q, but constraints say N>=1.
              P = [0] * (N+1)   # indices 0 to N; we will set indices 0 to N-1, and we leave P[N] as 0 (but we won't use it)
              # Build the prefix array for consecutive pairs
              # We traverse from i=0 to N-2 (inclusive) to check S[i] and S[i+1]
              for i in range(0, N-1):
                  if S[i] == S[i+1]:
                      P[i+1] = P[i] + 1
                  else:
                      P[i+1] = P[i]
              # Now, P[i] for i in [0, N-1] is the prefix sum of consecutive pairs from index0 to index i-1? 
              # Actually, P[i] = total consecutive pairs from the beginning up to the pair at index i-1 (if we consider the consecutive pairs at indices 0 to i-1) 
              # But we are going to use the formula: for query (l, r) -> P[r-1] - P[l-1]

              ans_list = []
              for (l, r) in queries:
                  # The consecutive pairs in the substring [l, r] are stored in the consecutive pairs array from index l-1 to r-2 (0-indexed in the consecutive pairs array). 
                  # The sum is P[r-1] - P[l-1]
                  ans_list.append(P[r-1] - P[l-1])
              return ans_list

This is the solution.

Let me test with the provided examples.

Example 2: "aaaaa", N=5, Q=1, query (1,5): 
        We built P for "aaaaa": 
            P[0]=0
            i=0: P[1]=1
            i=1: P[2]=2
            i=2: P[3]=3
            i=3: P[4]=4
        Then for query (1,5): P[4]-P[0]=4-0=4 -> correct.

Example 1: as above.

Edge: N=1: 
        P = [0]*(2) -> [0,0]
        Then for query (1,1): 
            ans = P[0]-P[0]=0 -> correct.

So we are done.

## Final Solution

### Step 1: Problem Understanding and Modeling
The problem involves processing a string and answering multiple queries about consecutive character pairs in substrings. For each query, we need to count how many positions in the specified substring have two identical adjacent characters. 

**Key Insights:**
- The solution must efficiently handle up to 300,000 characters and 300,000 queries, ruling out brute-force approaches for each query.
- The problem reduces to counting adjacent equal character pairs over many substrings, which can be optimized using prefix sums.
- Each query (l, r) requires counting pairs from index l-1 to r-2 in the original string (0-indexed).

**Constraints:**
- String length N and number of queries Q can be as large as 300,000.
- Each query must be answered in constant time after preprocessing.

**Edge Cases:**
- Substrings of length 1 have no pairs (output 0).
- Entire string of identical characters (e.g., "aaaaa" has 4 pairs for substring [1,5]).

### Step 2: Multiple Solution Exploration

1. **Brute Force Solution:**
   - For each query, iterate through the substring and count adjacent equal pairs.
   - **Time Complexity:** O(Q * N) which is O(300,000²) = 90 billion operations, too slow.
   - **Space Complexity:** O(1) additional space.
   - **Limitations:** Not feasible for large inputs.

2. **Prefix Sum Optimization:**
   - Precompute an array `A` where `A[i] = 1` if `S[i] == S[i+1]` else `0`.
   - Compute prefix sums `P` where `P[i] = A[0] + A[1] + ... + A[i-1]`.
   - For query (l, r), answer is `P[r-1] - P[l-1]`.
   - **Time Complexity:** O(N) preprocessing and O(1) per query.
   - **Space Complexity:** O(N) for prefix array.
   - **Advantages:** Efficient and straightforward.

3. **Space-Optimized Prefix Sum:**
   - Compute prefix sums directly without storing `A` explicitly.
   - Traverse the string once, setting `P[i+1] = P[i] + 1` if `S[i] == S[i+1]` else `P[i]`.
   - **Time/Space:** Same as above but slightly less space by avoiding `A`.

4. **Segment Tree / Fenwick Tree:**
   - Could be used for range queries but overkill since queries are prefix sums.
   - **Time Complexity:** O(N) preprocessing, O(log N) per query.
   - **Limitations:** More complex with no advantage over prefix sums for this problem.

### Step 3: Optimal Solution Selection
**Selected Approach:** Space-Optimized Prefix Sum  
**Rationale:**
- Prefix sums reduce each query to O(1) after O(N) preprocessing.
- Avoids explicit storage of the pair array `A`, saving space.
- Simple implementation with optimal performance for constraints.

**Implementation:**
```python
from typing import List, Tuple

class Solution:
    def solve(self, N: int, Q: int, S: str, queries: List[Tuple[int, int]]) -> List[int]:
        # Precompute prefix sums: P[i] = count of pairs from S[0] to S[i-1]
        P = [0] * (N + 1)  # 0-indexed prefix sums, length N+1
        
        # Build prefix array: traverse string, update counts for adjacent pairs
        for i in range(N - 1):
            if S[i] == S[i + 1]:
                P[i + 1] = P[i] + 1
            else:
                P[i + 1] = P[i]
        
        # Process each query: answer = P[r-1] - P[l-1]
        results = []
        for l, r in queries:
            results.append(P[r - 1] - P[l - 1])
        return results
```

**Key Optimization Points:**
- **Prefix Array Construction:** Efficiently computes cumulative pair counts in O(N) time.
- **Query Processing:** Each query resolved in O(1) by subtracting prefix sums.
- **Edge Handling:** Naturally handles substrings of length 1 (result 0) via prefix subtraction.

### Step 4: Verification and Summary
**Testing with Examples:**
1. **Example 1:**
   - S = "mississippi", queries = [(3,9), (4,10), (4,6), (7,7)]
   - Output: [2, 2, 0, 0] (matches example).
2. **Example 2:**
   - S = "aaaaa", query = (1,5)
   - Output: [4] (correct).

**Correctness:**
- For any substring [l, r], the pairs counted are exactly from index l-1 to r-2 in the original string. The prefix sum difference `P[r-1] - P[l-1]` correctly sums the pairs in this range.

**Complexity:**
- **Time:** O(N + Q) — preprocessing O(N), each query O(1).
- **Space:** O(N) for the prefix array.

**Further Optimizations:**
- None needed; optimal for given constraints.

**Summary:**
- The problem is efficiently solved using prefix sums to precompute cumulative pair counts.
- Each query is answered by a simple subtraction, leveraging the precomputed data.
- The solution handles all edge cases, including single-character substrings and large inputs.