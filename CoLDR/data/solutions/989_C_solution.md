# Solution for 989_C.md

Generated at: 2025-07-22 06:16:55
Model: deepseek-reasoner

## Reasoning Process

We are given four integers a, b, c, d (each between 1 and 100) representing the required number of connected components for flowers A, B, C, D.

Constraints: 
  We can choose n and m (rows and columns) between 1 and 50.

We need to output a grid (n x m) of characters 'A','B','C','D' such that:
  The number of connected components of 'A' is a, for 'B' is b, for 'C' is c, and for 'D' is d.

Note: Connected components are defined by 4-directional adjacent same letters.

Approach:

One common idea in such problems is to build a grid that has a background (for example, one letter) and then embed the other letters as isolated components. However, note that we have four letters and each must have a specific number of connected components.

Let's break down:

1. We note that the maximum requirement for any of the components is 100, and our grid can be up to 50x50=2500 cells. However, we cannot have more than 2500 isolated components (each cell a component) but that is not required. Instead, we can design the grid so that we have one letter acting as a background and then the other letters are arranged in a way that each occurrence is isolated (by surrounding with the background) to form multiple components.

2. However, the problem requires that each of the four letters has at least one connected component. So we must have at least one component for each.

3. Observing the examples:

 Example 1: Input: 5 3 2 1 -> Output grid 4x7.
   The grid is:
      DDDDDDD
      DABACAD
      DBABACD
      DDDDDDD

   Here, D forms one connected component (the entire border and the middle column? Actually, the D's are all connected so one component). 
   The A's: we have 5 A's, each isolated by D's? Actually, looking at the grid:

      Row0: DDDDDDD -> all D
      Row1: D A B A C A D -> so A appears at (1,1), (1,3), (1,5) -> three separate A's? But the requirement is 5 for A? 

   Actually, the example output says: 
        Amaranths (A) has 5 components -> but in the grid we see 3 A's? That doesn't add up.

   Let me recount the example grid:

        Row1: D A B A C A D -> A at positions (1,1), (1,3), (1,5) -> 3 A's? 
        Row2: D B A B A C D -> A at (2,2), (2,4) -> 2 more A's? So total 5 A's, but are they connected?

        Check connectivity: 
          Row1: 
            (1,1): A -> adjacent: up (0,1) is D, left (1,0) is D, right (1,2) is B, down (2,1) is B -> so isolated? 
            (1,3): A -> adjacent: up D, left B, right C, down (2,3) is B -> isolated.
            (1,5): A -> adjacent: up D, left C, right D, down (2,5) is C -> isolated.
          Row2:
            (2,2): A -> adjacent: up (1,2) is B, left (2,1) is B, right (2,3) is B, down (3,2) is D -> isolated.
            (2,4): A -> adjacent: up (1,4) is C, left (2,3) is B, right (2,5) is C, down (3,4) is D -> isolated.

        So each A is a separate component: 5 components.

        Similarly, for B: 
          Row1: (1,2) and (1,4) is not B? Actually (1,2) is B, (1,4) is C. 
          Row2: (2,1), (2,3), (2,5) is not B? (2,5) is C. So B at (1,2), (2,1), (2,3) -> three B's? 
          But check connectivity: 
            (1,2): adjacent: up D, left A, right A, down (2,2) is A -> isolated? 
            (2,1): adjacent: up A, left D, right A, down D -> isolated?
            (2,3): adjacent: up A, left A, right A, down D -> isolated?
          So three separate B components.

        For C: 
          Row1: (1,4) and (1,6) is D -> so (1,4) is C, and (2,5) is C? 
          Row2: (2,5) is C -> then (2,5): adjacent: up (1,5) is A, left (2,4) is A, right (2,6) is D, down (3,5) is D -> isolated.
          Also (1,4): adjacent: up D, left A, right A, down (2,4) is A -> isolated.
          So two C's: two components.

        D: one big connected component (the entire border and the inner D at (1,0), (1,6), (2,0), (2,6), and the bottom row) -> connected.

   So the example works.

4. How to construct?

   We can use the following idea:

   - Use one letter (say D) as the background that will form one connected component (if we design it as a connected frame and then perhaps fill the entire interior with a pattern that breaks the others?).

   - But note: in the example, D is the background and forms one component. Then we have the other letters (A, B, C) placed in the inner grid in such a way that each occurrence is isolated by D (or by other letters that break connectivity). However, note that the inner grid in row1 and row2 has non-D letters, and the D's in the inner rows (like the first and last column) are connected to the outer frame.

   How about we design a grid that has:

        n = ... and m = ... (to be chosen appropriately)

        The first and last row are entirely D's.
        The first and last column are entirely D's.

        Then, the inner grid (from row 1 to row n-2, and column 1 to column m-2) is filled with a pattern.

   Now, the D's form one connected component? Only if the entire border is connected and the inner D's (if any) are connected to the border. But in the example, the inner grid does not have D's? Actually, in the example, the inner grid (rows 1 and 2, columns 1 to 5) has no D? because the inner grid is:

        Row1: A,B,A,C,A
        Row2: B,A,B,A,C

        Then the D's are only in the border? But then the entire border is connected.

   However, the example also has D's in the first and last column of the inner rows? So the grid:

        Row1: D at positions (0,0) to (0,6), (1,0), (1,6), (2,0), (2,6), and row3: all D.

        So the D's form one connected component.

   Now, how to form multiple components for A, B, C?

   We can use the inner grid to place the other letters. But we need to break them into multiple components. Since we are surrounded by D, we can put a single letter in a cell and then separate adjacent cells by D? But note we are constrained by the grid size.

   Alternatively, we can use a checkerboard-like pattern? But we need to control the exact count.

   Another idea: we can have a base grid of D's that forms one component. Then, we can put the other three letters (A, B, C) in the inner grid. But note: we need to form a specific number of components for each.

   How to form a component? We can put a letter in a cell and if we don't connect it to an adjacent same letter, it is a separate component.

   Therefore, if we put a letter in a cell and surround it by a different letter (like D, or even by one of the others) then it will be isolated.

   But note: we are only allowed to use A, B, C, D.

   So we can design:

        We reserve the background for D (one big component) and then for the inner grid, we assign cells for A, B, C in such a way that no two same letters are adjacent.

        Then each cell of A is a separate component, each of B, and each of C.

        Then we can get up to (n-2)*(m-2) components for each letter? But we are putting only one letter per cell? Actually, we can assign each inner cell arbitrarily to A, B, or C. But then the total components for A would be the number of A's, for B the number of B's, etc.

        However, we have three letters and we need to assign counts: a, b, c.

        But note: we also have the background D. And the requirement for D is d=1 (in the example). But the input d might be more than 1? Actually, the constraints say each a, b, c, d are at least 1 and at most 100.

        How to handle when d>1? Then we need to break the D background into multiple components.

        Similarly, we might have a, b, c that are larger than the available inner cells? The inner grid has (n-2)*(m-2) cells. Since n, m at most 50, inner grid can be at most 48*48 = 2304, which is more than 100, so we have enough space for a, b, c (each up to 100, so total 300, which is less than 2304).

        But what if we have d>1? Then we need to break the D background. How?

        We can have multiple "islands" of D? But then note: if we break the D background, we must separate the D's by non-D. However, the border is all D. So if we break the border, we break the entire background. How to form multiple D components?

        Idea: we can have a base grid that is not entirely D? Or we can have holes in the D background that are filled with other letters, and then also have isolated D blocks? But then the border D is one component and the inner D blocks would be separate? But they are separated by non-D.

        However, we can also create multiple D components by having disconnected D regions in the inner grid? But the border D is connected. Then if we put a D in the inner grid and surround it by non-D, it becomes a separate component.

        Therefore, we can also use the inner grid to place D's? Then we can have:

            total D components = 1 (the border) + (number of inner D cells that are isolated) 
            but note: if we put two adjacent inner D cells, they become one component.

        So to form multiple D components, we can put isolated D cells in the inner grid (each surrounded by non-D). Then each such D cell is a separate component.

        But then we have two types of D: the border (one component) and the isolated inner D's (each one component). So total D components = 1 + (number of isolated D cells).

        Alternatively, if we break the border? We can break the border by inserting non-D at some positions. Then the border D would break into multiple segments. But that might be messy.

        Alternatively, we can have the entire grid as a base of one letter (say X, which is one of A, B, C, D) and then use the other letters to form isolated components. But we have four letters.

   Let me restructure:

        We choose a base letter for the background. We can choose D as the background. Then:
            We set the entire grid to D. Then the D background is one component.

            Then we need to form:
                a components for A -> we can put a isolated A's (each in a separate cell) in the inner grid? 
                similarly for b and c.

            But then the total inner cells we need is a+b+c. Since a+b+c can be up to 300, and we have 48*48=2304, which is enough.

            However, we also have the requirement for d: the number of D components. Currently we have one big D component. But if we put an A in the grid, then the D's around that A are still connected? Actually, when we put a non-D in the grid, we break the D into the background and then the D's adjacent to that non-D are still connected via other D's? But if we put one non-D, the D remains connected. However, if we put multiple non-D's, the D background remains one connected component? Because the non-D's are isolated and the D's are everywhere else.

            But what if we put two non-D's that are adjacent? Then the D's are still connected? Actually, no: the non-D's are not breaking the D connectivity because they are like holes. The entire D remains connected as long as the holes don't separate the grid (like a hole in the middle doesn't disconnect the D background).

            However, if we put a pattern of non-D's that forms a ring, then we can break the D background into two components: the outside and the inside of the ring. But we don't need that.

            Therefore, if we just put isolated non-D cells (each non-D cell is surrounded by D), then the D remains one connected component.

            So then we have d=1. But what if d>1?

        How to increase the number of D components?

            We can break the D background by putting a non-D in the border? But the border is D. If we put a non-D in the border, then the border D is broken? Actually, if we break the border, we break the background into two parts? Not necessarily: the border is a frame. If we break one cell in the border, the D border becomes two segments? But then the inner D's are still connected to one of the segments? 

            Alternatively, we can put a D cell in the inner grid that is isolated? But then that D cell is surrounded by non-D? Then it becomes a separate D component.

            Therefore, we can also use:

                We set the entire grid to D.

                Then we set some cells to non-D (for A, B, C) and also we set some cells to D again? But that doesn't help.

            Instead, we can:

                We set the entire grid to a base letter (say X) which we will use to form one component (if we don't break it). Then we can set some cells to D and make sure they are isolated? Then each isolated D is a component.

                Similarly for A, B, C.

            Now, we have four letters. We can assign:

                Let base letter be one of the four? But then we need to form multiple components for that base letter? 

            How about we choose the base letter to be the one that has the largest component requirement? Actually, the requirements are independent.

        Alternatively, we can use a two-layer background? 

        Another common idea is to use a grid that is divided into two parts: one part for three of the letters and the other part for the last? 

        After reading the example 2:

            Input: 50 50 1 1

            Output: 
                4 50
                CCCCC... (50 C's) -> one component for C
                ABABAB... -> alternating A and B: each A is adjacent to B and vice versa, so each A is isolated? Actually, in a row of ABAB..., each A is separated by B's and above and below? 
                BABABA... -> similarly
                DDDD... (50 D's) -> one component for D.

            How many A components? In row1: A at positions (1,0), (1,2), ... -> each A is adjacent to B (left and right) and above is C (so not A) and below is B (so not A) -> so each A is isolated? Then the number of A's is 25 (in a row of 50) and each is a component -> 25 components? But the requirement is 50.

            Then row2: B at (2,0), A at (2,1), B at (2,2), ... -> so A at (2,1), (2,3), ... -> 25 more A's. So total 50 A's, each is a component -> 50 components.

            Similarly, B: row1: B at (1,1), (1,3), ... -> 25, row2: B at (2,0), (2,2), ... -> 25, so 50 components.

            C: one component (top row) and D: one component (bottom row).

        So this meets 50,50,1,1.

        How was it constructed?

            n=4, m=50.

            Row0: all C -> one component for C.
            Row1: alternating A and B: starts with A -> then B -> then A ... (so for m=50, 25 A and 25 B, each A and each B is isolated -> 25 for A and 25 for B in row1).
            Row2: alternating B and A: starts with B -> then A -> then B ... -> 25 B and 25 A, and each is isolated? 
            Row3: all D -> one component for D.

            Then total A: 25 (row1) + 25 (row2) = 50, each isolated -> 50 components.
            Similarly B: 50 components.

        But note: in row1 and row2, are the A's connected? 

            Row1: A at (1,0) and A at (1,2) are separated by B at (1,1). Similarly, the A at (1,0) and the A at (2,1) are adjacent? (1,0) and (2,1) are diagonally adjacent? But the problem says common edge, not corner. So they are not adjacent. So each A is isolated.

        Therefore, we can form a large number of isolated components by putting each in a separate cell and separating them by a different letter.

        How about we design a grid that has:

            n = ... (we choose n, m appropriately, but we know n, m at least 1 and at most 50)

            We can have:

                - One or two rows for the background of one letter (for example, D) to form one component? 
                - Then the rest of the grid we use to form the other components.

        But note: we have four letters. And we have to form a, b, c, d.

        We can do:

            Let's denote:
                total_components = [a, b, c, d] for A, B, C, D.

            We choose one letter to be the background that we break into multiple components? Or we break each letter by isolation.

        However, we have the freedom to choose the grid and the pattern.

        Plan:

            We'll set the entire grid to a base letter, say 'X'. We choose 'X' to be one of the letters that has the smallest requirement? Actually, we can choose arbitrarily.

            But note: we have to form d components for D. We can choose D to be the base? Then we break the base into d components? How?

            How to break a base into multiple components? We can put barriers of non-D. But the barriers must be connected to form a wall? 

            Alternatively, we can have:

                Let the grid be of size n x m. We choose n and m large enough (at least 3x3, but we can choose up to 50x50).

                We decide to use a base of D. Then we need d components for D. We can have:

                    The entire grid is D. Then we have one component.

                    To break the D into d components, we need to put barriers (non-D) that separate the grid into d regions. But that is complex.

            Another idea: we can form the grid as a set of isolated D "islands" by putting each D as a single cell and surrounding it by non-D. Then we can get as many D components as we want. Similarly for the others.

            But then we have to assign the non-D cells to A, B, C? and also form their components.

        Actually, we can form each component as a single cell. Then:

            The grid must have at least (a+b+c+d) cells.

            And we can set the grid to a fixed size, say 50x50 (which is 2500) and that is enough since a+b+c+d <= 400.

            How to arrange? 

                We can assign each component (for each letter) to a distinct cell. Then we set that cell to the letter, and set all other cells to a fixed letter? But then the fixed letter will form one big component? 

                But we have four letters. We can choose one of the letters to be the background? But then the background will be one component? 

                However, we have four requirements: we need a components for A, b for B, c for C, d for D.

                We can set the entire grid to a fixed letter, say 'A'. Then we have one big component for A? But we need a components for A. Then we break the A background by putting non-A cells? Then the A background breaks into multiple components? How many? 

                Actually, if we put k non-A cells, then the A background might break into k+1 components? Not necessarily: it could break into more. It's complicated.

        Alternatively, we can use a checkerboard pattern for the entire grid? But that doesn't give control.

        After reading known solutions for similar problems (like constructing a grid with given connected components per color), a common solution is:

            Use a grid that is 50x50 (max size) and divide it into four blocks. But that might not be flexible.

        Another known solution:

            Use a grid of 50 rows and 50 columns.

            We divide the grid into two parts: top half and bottom half. Then we use:

                Top half: 
                    Fill with D and A: such that we form the required a components for A and also use D as a separator? 

            But let me look at the example 3:

                Input: 1 6 4 5

                Output: 
                    7 7
                    DDDDDDD
                    D D D B D B D -> but actually: "DDDBDBD"
                    DDCDCDD
                    DBDADBD
                    DDCDCDD
                    DBDDBDD
                    DDDDDDD

                How does it get 1 for A? -> only one A at the center? 
                B: 6 components -> we see several B's: 
                    Row1: positions 3 and 5 -> two B's (but separated by D) -> two components?
                    Row2: no B? 
                    Row3: B at (3,0), (3,2), (3,4), (3,6) -> but are they connected? 
                        (3,0): adjacent? left D, above D, below D, right (3,1) is D? -> isolated? 
                        (3,2): adjacent: left (3,1) D, above (2,2) C, right (3,3) A, below (4,2) C -> isolated? 
                        (3,4): adjacent: left A, right (3,5) D, above (2,4) C, below (4,4) C -> isolated?
                        (3,6): isolated?
                    So that's 4 B's? 
                Then row5: "DBDDBDD": B at (5,1) and (5,4) -> two more, so total 6.

                Similarly, C: 
                    Row2: "DDCDCDD": C at (2,2) and (2,4) -> two components? 
                    Row4: "DDCDCDD": same -> two more? -> total 4.
                D: the entire border? and then the inner D's? 
                    The entire border: connected. Then the inner D's: 
                         Row1: D at (1,0), (1,1), (1,2), (1,4), (1,6) -> but note (1,3) is B -> so the D at (1,1) and (1,2) are connected? and (1,4) is separated by B? 
                    Actually, the D's at the border are connected. Then the inner D's: 
                         Row1: (1,0) is connected to (0,0) and (1,1) is connected to (1,0) and (1,2) is connected to (1,1). Then (1,3) is B, so (1,4) is separated? But (1,4) is adjacent to (1,5) which is B? and (1,6) is the border. So (1,4) is not connected to the border? Actually, no: because (1,4) is adjacent to (0,4) which is D? So the entire top border is D. So (1,4) is adjacent to (0,4) (which is D) so it is connected to the border.

                    How about the center: 
                         The center has a D? at (3,3) is A? Then the D's at (3,1) and (3,5)? 
                         Row3: "DBDADBD": D at (3,0), (3,3) is A, (3,6) -> so no D at (3,1) or (3,5)? Actually, (3,1) is B, (3,5) is B.

                    Actually, the D's are only at the border and in row1, row2, row4, row5? 
                    But the requirement for D is 5. How do we get 5?

                    We see: 
                        The border forms one big D? 
                        Then the inner D's: 
                            Row1: (1,0) is border, (1,1) is D -> connected to border? (1,1) is adjacent to (0,1) and (1,0) -> so connected.
                            Similarly, (1,2) is connected to (1,1) and (0,2) -> connected to border.
                            (1,4) is connected to (0,4) -> connected to border.
                            (1,6) is border.

                    So the entire top part is connected to the border.

                    Then the bottom part similarly.

                    How do we get 5 components? 

                    The example output says 5 for D. 

                    Let me mark:

                        The border: all the outer cells. Then the inner cells that are D: 
                            Row1: (1,1), (1,2), (1,4) -> but these are connected to the border? so they are part of the border component.

                        Row2: "DDCDCDD" -> D at (2,0), (2,1), (2,3), (2,5), (2,6). 
                            (2,0) is border -> connected.
                            (2,1): adjacent to (2,0) -> connected to border.
                            (2,3): adjacent to (2,2) which is C? so not D. Then above (1,3) is B? and below (3,3) is A? so isolated? -> one component?
                            (2,5): adjacent to (2,6) (border) -> connected to border.

                        Similarly, row4: same as row2? 
                            (4,3): D? at row4: "DDCDCDD" -> (4,3) is C? no, it's the same as row2: 
                                positions: 
                                    0: D -> border? no, because row4 is not the last row? the last row is row6. So (4,0) is adjacent to (3,0) which is D (from row3: "DBDADBD" -> (3,0) is D) and (4,0) is adjacent to (5,0) which is D? and (5,0) is adjacent to the border? 
                            Actually, the entire grid:

                                Row0: DDDDDDD -> border
                                Row1: DDDBDBD -> D at (1,0), (1,1), (1,2), (1,4), (1,6) -> but (1,3) is B, (1,5) is B? 
                                Row2: DDCDCDD -> D at (2,0), (2,1), (2,3), (2,5), (2,6) -> (2,2) is C, (2,4) is C? 
                                Row3: DBDADBD -> D at (3,0), (3,3) is A? (3,6) -> so (3,0) and (3,6) are D, and (3,1)=B, (3,2)=D? -> no, the string is "DBDADBD": 
                                    index0: D, index1: B, index2: D, index3: A, index4: D, index5: B, index6: D -> so D at (3,0), (3,2), (3,4), (3,6)
                                Row4: DDCDCDD -> same as row2: D at (4,0), (4,1), (4,3), (4,5), (4,6)
                                Row5: DBDDBDD -> "DBDDBDD": 
                                    D at (5,0), (5,2), (5,3), (5,4), (5,6) -> because: 
                                        index0: D, index1: B, index2: D, index3: D, index4: B? -> wait the string is "DBDDBDD": 
                                        positions: 
                                            0: D, 
                                            1: B, 
                                            2: D, 
                                            3: D, 
                                            4: B? -> actually: 
                                            "D", "B", "D", "D", "B", "D", "D" -> so at index5: D and index6: D? 
                                        Actually, the last letter is D. So: 
                                            (5,0)=D, (5,2)=D, (5,3)=D, (5,5)=D, (5,6)=D? 
                                Row6: DDDDDDD -> border.

                        Now, the D components:

                            The border: row0 and row6, and the first and last column of rows 1 to 5? 

                            How many D components? 

                                We have:
                                    The border is one component.

                                    Then we have inner D's:

                                        Row1: (1,1), (1,2), (1,4): 
                                            (1,1) is adjacent to (1,0) (border) -> so connected to border.
                                            (1,2) is adjacent to (1,1) -> connected.
                                            (1,4) is adjacent to (1,5) is B? so not D. Then above? (0,4) is D (border) -> so connected to border.

                                        Row2: (2,1): adjacent to (2,0) (border) -> connected to border.
                                              (2,3): adjacent? 
                                                  up: (1,3) is B -> not D.
                                                  down: (3,3) is A -> not D.
                                                  left: (2,2) is C -> not D.
                                                  right: (2,4) is C -> not D.
                                                  -> so isolated? one component.

                                        Row3: (3,2): adjacent? 
                                                  up: (2,2) is C -> not D.
                                                  down: (4,2) is C? (row4: (4,2) is C) -> not D.
                                                  left: (3,1) is B -> not D.
                                                  right: (3,3) is A -> not D.
                                                  -> isolated? 
                                              (3,4): similarly isolated? 
                                              So two more components.

                                        Row4: (4,3): 
                                                  up: (3,3) is A -> not D.
                                                  down: (5,3) is D -> adjacent! 
                                                  left: (4,2) is C -> not D.
                                                  right: (4,4) is C -> not D.
                                                  -> so (4,3) is adjacent to (5,3). Then (5,3) is adjacent to (5,2) and (5,4) and (6,3) (border). 
                                                  But (5,2) is D? and (5,2) is adjacent to (5,1) which is B? and (5,0) which is border? 
                                                  Actually, (5,0) is border -> connected. Then (5,2) is adjacent to (5,3) and (5,0) is adjacent to (5,1) is B? so (5,0) is adjacent to (5,2)? No: (5,0) is adjacent to (5,1) which is B, and then (5,2) is adjacent to (5,3) and (5,1) is B -> so (5,2) is not adjacent to (5,0). 

                                                  But (5,0) is adjacent to (6,0) (border) -> so (5,0) is part of the border? 
                                                  Then (5,2) is adjacent to (5,3) and to (6,2) (border) -> because (6,2) is D? -> so (5,2) is adjacent to border? 
                                                  Then (5,3) is adjacent to (5,2) and (5,4) is B? and (6,3) is border -> so (5,3) is adjacent to border.

                                                  Therefore, (4,3) is adjacent to (5,3) which is adjacent to border -> so (4,3) is connected to border? 

                                            But wait: (4,3) is at row4, col3. It is adjacent to (5,3) (row5, col3) -> which is D and adjacent to the border? So (4,3) is connected to border? 

                                            Then (4,1): adjacent to (4,0) (border) -> connected to border.
                                            (4,5): adjacent to (4,6) (border) -> connected to border.

                                        Row5: (5,0) is border -> connected.
                                              (5,2): adjacent to (5,3) -> which is adjacent to border? -> connected.
                                              (5,3): connected.
                                              (5,5): adjacent to (5,6) (border) -> connected.
                                              (5,6) is border.

                            So we have:
                                The border component (which includes (1,1), (1,2), (1,4), (2,1), (4,1), (4,3), (4,5), (5,0), (5,2), (5,3), (5,5), (5,6), ...) -> one component.
                                The D at (2,3) -> one component.
                                The D at (3,2) and (3,4): 
                                      (3,2) and (3,4) are not adjacent? -> so two separate components.

                            Total D components: 1 (border) + 1 (at (2,3)) + 2 (at (3,2) and (3,4)) = 4? but the requirement is 5.

                        Actually, the example input is 1 6 4 5 -> so d=5.

                        What's the fifth? 

                        We missed one? 

                        Check row3: 
                            (3,0) is D -> border? it's the first column -> connected to border? 
                            (3,6) is D -> border? connected.

                        Then what about (3,2) and (3,4): two components.

                        And (2,3): one.

                        And the border: one.

                        Total 4.

                        But wait: row5: 
                            (5,0) is border -> already counted.
                            (5,2) is D -> we said connected to border? 
                            (5,3) is D -> connected to border?
                            (5,5) is D -> connected to border?
                            (5,6) is border.

                        So no additional.

                        How about (4,3) is connected to border? so not separate.

                        Then I see: row3: (3,2) and (3,4) are two separate. And (2,3) is one. And then row5: is there an isolated D? 

                        In row5: 
                            (5,2) is adjacent to (5,3) -> so they form one component? and then that component is connected to the border? 
                            How? (5,2) is adjacent to (6,2) which is border -> so yes, connected.

                        Then the only isolated D's are (2,3), (3,2), (3,4). 

                        But (3,2) and (3,4) are two separate.

                        So total D components: border (1) + (2,3) (1) + (3,2) (1) + (3,4) (1) = 4.

                        There is one missing.

                        Look at row4: (4,3) is D. We thought it was connected to (5,3) which is connected to border. But what about row4, col3: 
                            adjacent: 
                                up: (3,3) is A -> not D.
                                down: (5,3) is D -> so adjacent to (5,3). 
                                left: (4,2) is C -> not D.
                                right: (4,4) is C -> not D.

                            So (4,3) is adjacent to (5,3) -> and then (5,3) is adjacent to border -> so (4,3) is part of the border component.

                        How about (3,0) and (3,6): already border.

                        Then I see: row1: (1,4) is D -> but we said it is connected to border? 

                        And row3: (3,0) and (3,6) are border.

                        Then what is the fifth?

                        The example output says: 
                            "DDDDDDD" -> row0
                            "DDDBDBD" -> row1: 
                                positions: 
                                    0: D, 1: D, 2: D, 3: B, 4: D, 5: B, 6: D.
                                So at (1,4) is D. 
                            How is (1,4) connected? 
                                up: (0,4) is D (border) -> connected to border.

                        Then I see: in the center, we have at row3: 
                            (3,0): D -> border.
                            (3,2): D -> isolated? 
                            (3,4): D -> isolated?
                            (3,6): D -> border.

                        And at row2: 
                            (2,3): D -> isolated.

                        And then at row5: 
                            (5,0): D -> border.
                            (5,2): D -> connected to border? 
                            (5,3): D -> adjacent to (5,2) and (5,4) is B? and (6,3) is border -> so (5,3) is adjacent to (6,3) -> border? 
                            Actually, (5,3) is adjacent to (6,3) (border) -> so connected to border.

                        So the isolated ones are: (2,3), (3,2), (3,4) -> only three.

                        How do we get 5? 

                        Let me recount the D components from the example output:

                            Component 1: the entire border (all the outer cells) and any inner D that is connected to the border.

                            Then we have three more: (2,3), (3,2), (3,4). 

                            What is the fifth? 

                        I see: row5: 
                            (5,5): D -> and it is at the border? No, because the last column is (5,6) which is border, and (5,5) is adjacent to (5,6) -> so connected to border.

                        How about (1,1) and (1,2): connected to border.

                        Then I notice: the example output has a D at (1,1) and (1,2) and (1,4) which are connected to border.

                        And row2: (2,0) is border, (2,1) is D -> connected to border? 
                        And (2,5) is D -> connected to border? 
                        And row4: (4,0), (4,1), (4,5), (4,6) -> connected to border.

                        And row5: (5,0), (5,2), (5,3), (5,5), (5,6) -> connected to border.

                        Then the only ones not connected to the border are: 
                            (2,3), (3,2), (3,4) -> three.

                        But the requirement is 5.

                        This suggests that the example output provided in the problem might have a mistake? Or our counting is wrong.

        Given the complexity, let me try a different approach.

        Known solution strategy from similar problems (e.g., CodeForces problems):

            Use a grid of 50x50, split into four quarters. But that might not be necessary.

        Another known solution:

            We can use a fixed grid size (say 50x50) and assign:

                The first 25 rows: 
                    Even rows: A and B alternating.
                    Odd rows: B and A alternating.
                    This gives a checkerboard pattern for A and B, each cell is isolated? -> so each cell is a component.

                Then we have a lot of components for A and B.

            But we also need to embed C and D.

        After research, a common solution for this specific problem is:

            Use a grid that has a base of D, and then we embed the other three letters in a pattern that isolates each occurrence.

            Steps:

                Let n = 50, m = 50.

                We create a grid of n rows and m columns, initially all 'D'.

                Then, we need to form:

                    d components for D: initially one big component. When we embed other letters, we break the D into more components? 
                    But note: if we put a non-D cell, it doesn't create a new D component, it just might reduce the size of the D background, but the D's remain connected.

                How to form d components for D? We can put d-1 additional isolated D's in the grid? But then we have to remove them from the background? 

                Alternatively, we can break the background into more components by using barriers of other letters. 

                Specifically, we can draw horizontal and vertical lines of other letters to separate the grid into regions.

                For example, to form k components for D, we can use k-1 barriers (lines) that are not D. But then the barriers must be one cell thick? and they must be of non-D.

                How to do:

                    We can reserve the even rows and even columns for D, and then the odd rows and columns for the other letters? 

                But we also need to form a, b, c for A, B, C.

            Instead, we can do:

                We will have the grid initially all D.

                Then, we want to create:

                    a components for A -> we will use a cells of A, each isolated (surrounded by D) -> then each is a component.
                    similarly for b and c.

                But then the D background: 

                    The entire grid is D, then we have removed a+b+c cells and replaced them with A, B, C. 

                    The D background might break into multiple components? 

                    In fact, if we remove one cell, the D background remains one component. If we remove two adjacent cells, it still remains one component? 

                    To break the D background into d components, we need to remove at least d-1 cells in such a way that they disconnect the grid? 

                    But it's not easy to control.

            Another idea: 

                We can have the grid partitioned into two parts: 

                    Part1: the background of the grid will be one letter (say A) and then we will put the other letters as isolated cells. But then the A background might break into multiple components.

                Specifically, to form a components for A, we can start with one big background of A, and then we break it by putting non-A cells. Each non-A cell can break the A into more components.

                But how many components do we get? It's the number of connected components in the A after removing the non-A cells. This is not directly controllable.

        Given the complexity, I found an known approach in CodeForces for this problem (problem id: 1112C, but not exactly) and known solutions:

            We can use a grid of 50x50, and we will have:

                n = 50, m = 50.

                We will divide the grid into three parts vertically: left, middle, right.

                Or we can do:

                  We will use the first 25 rows for A and B, and the last 25 rows for C and D.

                But then how to control the components.

        Insight from known solutions for the same problem:

            Solution for Example: 50 50 1 1:

                n=4, m=50.

                Row0: 'C'*50
                Row1: 'A' then 'B' repeated: so for i in range(50): if i%2==0: 'A' else 'B'
                Row2: 'B' then 'A' repeated: if i%2==0: 'B' else 'A'
                Row3: 'D'*50

                This gives 50 components for A, 50 for B, 1 for C, 1 for D.

            How to generalize?

                We can do:

                  We choose n = 2 (for two of the letters) + 2 (for the other two) = 4 rows.

                  We set:

                      row0: all X (one component for X)
                      row1: pattern for Y and Z: alternating between Y and Z -> then each cell of Y is a component, each cell of Z is a component.
                      row2: pattern for Y and Z: alternating but shifted -> so that no Y in row1 is adjacent to a Y in row2 (because it's checkerboard) -> so each Y cell is isolated.
                      row3: all W (one component for W)

                Then if we let:
                    X = C, Y = A, Z = B, W = D, then we get:
                        a = number of A's in row1 and row2.
                        b = number of B's in row1 and row2.
                        c = 1
                        d = 1

                But what if we need more than 1 for C or D? 

                For C (which is the top row) and D (which is the bottom row) we only have one component each.

                How to increase the components for C? 

                    We can break the top row by putting non-C in some positions. But then the C's in the top row might break into segments.

                    For example, if we put a non-C at position (0, i), then the C's to the left and right become disconnected? 

                    But then the non-C in the top row must be one of the other letters? and we have to account for that.

                Similarly for the bottom row.

            Therefore, we can do:

                We use n = 2 + 2 + ... ? 

            Another known solution from CodeForces for the same problem (problem 1112C) is to use a 50x50 grid and:

                Reserve the first row for letter A and the last row for letter B.
                Then the inner grid (rows 1 to n-2) will be used for C and D.

                But then how to control the components for A and B? 

                We have the first row: if we make it all A, then it is one component. If we break it, then we get more components.

                Specifically, to get a components for A, we can have a segments in the first row. How? 

                    We can put a-1 non-A's in the first row to break it into a components.

                Similarly, for B in the last row: put b-1 non-B's to break into b components.

                Then the inner grid is for C and D. We can do the same: the first and last column of the inner grid for C and the rest for D? 

                But then how to control c and d.

            Steps for this solution:

                Let n = 50, m = 50.

                Grid = [['' for _ in range(m)] for __ in range(n)]

                # First row: 
                for j in range(m):
                    grid[0][j] = 'A'

                # Last row:
                for j in range(m):
                    grid[n-1][j] = 'B'

                # Then, for the first row, to break into a components, we will put a-1 non-A's in the first row at positions (0, 1), (0,3), (0,5), ... (every other) until we have a-1 breaks.
                # But note: to break the first row into a components, we need a-1 breaks. We can put a-1 non-A's in the first row at positions that are not adjacent? -> actually, any position will break, and two consecutive breaks will break into more segments.

                # However, we also have to assign the non-A's to either B, C, or D? But we have not assigned B in the first row? and we want to save B for the last row.

                # Similarly, for the last row, we need b-1 non-B's.

                # How about we use letter C for the breaks in the first row and letter D for the breaks in the last row.

                # Then:

                    breaks in first row: a-1 positions, and we set grid[0][pos] = 'C' (or 'D') -> but note: then these 'C' will be isolated? or might form components with later C's.

                # Then for the inner grid (rows 1 to n-2), we can fill with 'C' and 'D' in a pattern that gives the required c and d.

                # But wait, we have already used some 'C' in the first row.

                # Let c_total = c, then we have used a-1 'C's in the first row, so the inner grid must provide c - (a-1) components for C? 

                # Similarly, for D: we might have used some in the last row.

            Given the complexity, and since the constraints are small (at most 100) and the grid is 50x50 (2500 cells), we can try a different approach.

        Final plan (inspired by known solution for this exact problem in CodeForces): 

            We will create a grid of 50 rows and 50 columns.

            We will use the entire grid initially with a base letter, say 'A'.

            Then, we will have a grid that looks like:

                We will have a chessboard-like pattern in the entire grid: 
                    even row, even col -> 'A'
                    even row, odd col -> 'B'
                    odd row, even col -> 'C'
                    odd row, odd col -> 'D'

            But then the components: 
                'A': each 'A' is isolated? because adjacent is 'B' or 'C'? -> then each 'A' is a component -> 25*25 = 625 components for A.
                similarly for B, C, D.

            But we need specific numbers.

            Then, to reduce the number of components for a particular letter, we can merge adjacent cells by changing the chessboard.

            For example, if we want only one component for 'A', we can connect all the 'A' cells by changing some 'B' (or 'C' or 'D') to 'A' in a path.

            But then we have to be careful not to merge other letters.

            Alternatively, we can use the following:

                We will have the grid as a big rectangle of 'A', and then we will plant the other components as isolated cells of 'B', 'C', 'D'. 

                Specifically, to form:

                    a components for 'A': we will have one big background and then we will have a-1 additional isolated 'A' cells? -> but then the background is one, and the isolated cells are separate, so total a components.

                    b components for 'B': we will have b isolated 'B' cells (each in a cell that is not adjacent to another 'B').

                    similarly for c and d.

                How to place the isolated cells? 

                    We can choose a cell that is currently 'A' and change it to 'B' to form one component for 'B'. But then that cell is no longer 'A'. 

                So we do:

                    We start with a grid full of 'A'. This is one connected component for 'A' (background).

                    Then, we need a-1 more components for 'A': so we will take a-1 cells and change them to 'A' again? -> but they are already 'A'. 

                Alternatively, to form an isolated 'A' cell, we need to separate it from the background. We can do:

                    Choose a cell in the interior. Change its four neighbors to a letter that is not 'A' (say 'B'). Then this cell becomes an isolated 'A' (one component). 

                But then we use 4 cells for one isolated 'A'. And we might affect the background connectivity.

                Moreover, we also need to create isolated cells for 'B', 'C', 'D'. 

                This seems complex.

        Given the complexity, and since the problem is from a contest, there is a known simpler solution.

        Known solution (from Example 2): 

            They used a grid of 4 rows and 50 columns for 50,50,1,1.

            How to handle 5,3,2,1? 

            Example1 output: 4 7 grid.

        Another known solution for this problem in CodeForces ( by a user ):

            #include <bits/stdc++.h>
            using namespace std;
            int a, b, c, d;
            char g[50][50];
            int main() {
                cin >> a >> b >> c >> d;
                a--; b--; c--; d--;
                for (int i = 0; i < 50; i++) {
                    for (int j = 0; j < 50; j++) {
                        if (i < 25) {
                            if (j < 25) {
                                g[i][j] = 'A';
                            } else {
                                g[i][j] = 'B';
                            }
                        } else {
                            if (j < 25) {
                                g[i][j] = 'C';
                            } else {
                                g[i][j] = 'D';
                            }
                        }
                    }
                }
                for (int i = 0; i < 25; i++) {
                    for (int j = 0; j < 25; j++) {
                        if (i % 2 == 0 && j % 2 == 0) {
                            if (d > 0) {
                                g[i][j] = 'D';
                                d--;
                            } else if (c > 0) {
                                g[i][j] = 'C';
                                c--;
                            } else if (b > 0) {
                                g[i][j] = 'B';
                                b--;
                            }
                        }
                    }
                    for (int j = 25; j < 50; j++) {
                        if (i % 2 == 0 && j % 2 == 0) {
                            if (c > 0) {
                                g[i][j] = 'C';
                                c--;
                            } else if (d > 0) {
                                g[i][j] = 'D';
                                d--;
                            } else if (a > 0) {
                                g[i][j] = 'A';
                                a--;
                            }
                        }
                    }
                }
                for (int i = 25; i < 50; i++) {
                    for (int j = 0; j < 25; j++) {
                        if (i % 2 == 0 && j % 2 == 0) {
                            if (b > 0) {
                                g[i][j] = 'B';
                                b--;
                            } else if (a > 0) {
                                g[i][j] = 'A';
                                a--;
                            } else if (d > 0) {
                                g[i][j] = 'D';
                                d--;
                            }
                        }
                    }
                    for (int j = 25; j < 50; j++) {
                        if (i % 2 == 0 && j % 2 == 0) {
                            if (a > 0) {
                                g[i][j] = 'A';
                                a--;
                            } else if (b > 0) {
                                g[i][j] = 'B';
                                b--;
                            } else if (c > 0) {
                                g[i][j] = 'C';
                                c--;
                            }
                        }
                    }
                }
                cout << 50 << " " << 50 << endl;
                for (int i = 0; i < 50; i++) {
                    for (int j = 0; j < 50; j++) {
                        cout << g[i][j];
                    }
                    cout << endl;
                }
            }

        How does it work?

            It creates a 50x50 grid, divided into four 25x25 blocks:

                Top-left: A
                Top-right: B
                Bottom-left: C
                Bottom-right: D

            Then, it goes through each block and in the even-even positions (i%2==0 and j%2==0) in each block, it changes the letter to one of the other letters to create isolated components.

            Specifically, in the top-left block (which is A), at even-even positions, it might change A to D, C, or B. 

            Why even-even positions? Because then these positions are not adjacent to other blocks? and within the block, the even-even positions are separated by one cell.

            Example: in the top-left block, positions (0,0), (0,2), (0,4), ... are even-even. The adjacent cells of (0,0) are (0,1) (which is A in the top-left? no, because the top-left is initially all A, but (0,1) is not even-even so it remains A) and (1,0) (A). 

            If we change (0,0) to 'D', then this 'D' is adjacent to A's. But then if we change (0,0) to 'D', it becomes isolated from other D's (because the next D is in the bottom-right block, far away) -> so it forms a new component for D.

            Therefore, each such change creates one new component for the letter we are changing to, and reduces the background component of the original letter by not at all? because the cell is now a different letter.

            But wait: the background: 
                for the top-left block, the background is A. When we change one cell to D, then the A's around it are still connected? 

            However, the changed cell is not A anymore, so the A background might have a hole. The A's are not broken into more components because the hole is only one cell? they remain one component.

            Therefore, the only new components are the ones we explicitly create by these changes.

            Initially, each block has one connected component for its letter.

            Then, for each even-even cell we change to a letter, we are:

                - Reducing the requirement for that letter (because we are creating one component per change) 
                - And the letter we are changing from loses one cell, but the component remains one.

            The algorithm does:

                a--; b--; c--; d--;   [because the background already provides one component for each]

                Then, in the top-left block (A), at even-even cells, we change to 'D' until d>0 becomes 0, then to 'C', then to 'B'. This creates new components for D, C, B respectively.

                In the top-right block (B), we change to 'C', then 'D', then 'A' (at even-even cells).

                In the bottom-left block (C), we change to 'B', then 'A', then 'D'.

                In the bottom-right block (D), we change to 'A', then 'B', then 'C'.

            Then the total components for A: 
                one (background in top-left) + however many times we created an 'A' in the other blocks (in top-right, bottom-left, bottom-right) 
                but note: in the top-left block, we might have changed some cells to 'A'? -> no, because in the top-left, we change to D, C, B. 
                in the top-right: we might change to 'A' if there are any a>0.
                similarly in bottom-left and bottom-right.

            So the total components for A = 1 (background in top-left) + (number of times we changed to 'A' in the even-even cells of the other three blocks)

            Similarly for B: 
                one (background in top-right) + (number of times we changed to 'B' in the even-even cells of the other three blocks)

            But wait, in the top-left block, if we change to 'B', then that is one component for B (isolated) -> so then B has one more.

            Therefore, after the changes, the components are:

                A: 1 (background) + (# of changes to 'A' in the other blocks) + (changes to 'A' in top-left? -> no, because in top-left we never change to 'A')
                Actually, in top-left: we change to D, C, B -> so if we change to 'B' in top-left, then that 'B' is one component for B.

            So for B: 
                background in top-right: one component.
                plus any change to 'B' in the other blocks (top-left, bottom-left, bottom-right).

            And note: in the top-left block, if we change to 'B', then it is not connected to the top-right block's B because they are separated by a column of B's? -> actually, the top-left and top-right are separated by a column of B's (initially) -> wait, the top-right block is initially 'B', and the boundary between top-left and top-right is at j=25. 
                The cell (0,24) in top-left is 'A' and (0,25) in top-right is 'B'. So the 'B' in the top-left block (at say (0,0)) is isolated from the top-right block's B by at least one cell of A in between.

            Therefore, each change to a letter in a block of a different letter creates an isolated component for that letter.

            So the total for letter X = 1 (background in its own block) + (number of even-even cells changed to X in the other three blocks) + (number of even-even cells in its own block that are changed to X? -> no, because in its own block, we only change to other letters, never to itself. 

            But wait: in the top-left block (A), we change to D, C, B, never to A. Similarly for others.

            Therefore, the a in the input should be: 
                a = 1 (background) + (number of changes to 'A' in the other blocks)

            However, at the beginning, we do a--, so then a becomes the number of changes to 'A' we need in the other blocks.

            Similarly, for b: b = 1 (background) + changes to 'B' in other blocks -> then we do b--, so b becomes the number of changes to 'B' in the other blocks.

            Therefore, the algorithm:

                a0 = a, b0 = b, c0 = c, d0 = d.
                a = a0 - 1, b = b0 - 1, c = c0 - 1, d = d0 - 1.

                Then in the top-left block (which is 'A'), at even-even cells, we will change to:
                  first: 'D' (d times) -> until d becomes 0
                  then 'C' (c times)
                  then 'B' (b times)

                In the top-right block (which is 'B'), at even-even cells, we change to:
                  first: 'C' (c times) 
                  then 'D' (d times)
                  then 'A' (a times)

                In the bottom-left block (which is 'C'), at even-even cells, we change to:
                  first: 'B' (b times)
                  then 'A' (a times)
                  then 'D' (d times)

                In the bottom-right block (which is 'D'), at even-even cells, we change to:
                  first: 'A' (a times)
                  then 'B' (b times)
                  then 'C' (c times)

                But note: the values of a, b, c, d are being reduced in the first block, and then used in the next blocks.

                However, the order is important: the changes in the first block might exhaust d, then in the next blocks we use the remaining.

            Example: a0=5, b0=3, c0=2, d0=1.

                a = 4, b = 2, c = 1, d = 0.

                Then in the top-left block (A): 
                  even-even cells: first we try to change to 'D' -> but d=0, so skip.
                  then to 'C' -> c=1, so we change one cell to 'C'. Then c becomes 0.
                  then to 'B' -> b=2, so we change two cells to 'B'. Then b becomes 0.

                In the top-right block (B): 
                  first change to 'C' -> c=0, skip.
                  then to 'D' -> d=0, skip.
                  then to 'A' -> a=4, so we change as many as we can in the even-even cells of the top-right block to 'A' until a becomes 0.

                In the bottom-left block (C): 
                  first to 'B' -> b=0, skip.
                  then to 'A' -> a might be 4 - (however many we changed in the top-right) = ... 

                How many even-even cells are there in one block? 
                    In a 25x25 block, even-even cells: 
                         rows: 0,2,4,...,24 -> 13 rows (0-indexed, inclusive)
                         columns: 0,2,4,...,24 -> 13 columns.
                    so 13 * 13 = 169 cells.

                Since a0, b0, c0, d0 are at most 100, and 169>100, we have enough cells.

            Therefore, this solution will work.

        Implementation:

            We'll create a grid of 50x50.

            We'll split into four blocks:

                Block 0: [0,24] x [0,24]   -> top-left
                Block 1: [0,24] x [25,49]   -> top-right
                Block 2: [25,49] x [0,24]    -> bottom-left
                Block 3: [25,49] x [25,49]   -> bottom-right

            Initialize each block to:

                Block0: 'A'
                Block1: 'B'
                Block2: 'C'
                Block3: 'D'

            Then, we do:

                a_count = a - 1
                b_count = b - 1
                c_count = c - 1
                d_count = d - 1

                In block0 (which is 'A'), iterate over even i and even j in [0,24] (within the block) and change to:
                    first: 'D' if d_count>0, then d_count--
                    else: 'C' if c_count>0, then c_count--
                    else: 'B' if b_count>0, then b_count--

                In block1 (which is 'B'), iterate over even i and even j in [0,24] for i and [25,49] for j (but note: in the block, the j index within the block is 0 to 24, but the actual j is 25+j0, so even j in [25,49] means j0 even in [0,24]) -> wait, 25 is odd, so j=25 is odd, j=26 is even, j=27 is odd, ... so the even indices in the overall grid are 26,28,... which are even in the overall grid.

                But the condition in the code is on the overall i and j: i%2==0 and j%2==0.

                We can iterate in the block0: i in [0,24] (step 2), j in [0,24] (step 2) -> and similarly for other blocks.

                For block1: i in [0,24] (step2), j in [25,49] (step2) -> but then the condition is automatically satisfied.

            Steps for block1:

                for i in range(0, 25, 2):   # i=0,2,4,...,24
                    for j in range(25, 50, 2): # j=25,27,29,...,49 -> but wait, 25 is not even in the overall grid? 
                However, note: 25 is odd, 26 is even, 27 is odd, ... -> so we should start at j=26? 

                But the known solution uses: in the even-even positions in the block, meaning the overall grid's even i and even j.

                In block1: i in [0,24] (even i: 0,2,...,24) and j in [25,49]: the even j in [25,49] are 26,28,...,48.

                We can do: for j0 in range(0, 25, 2): then j = 25 + j0? -> but then j0=0 -> j=25 (overall, which is odd) -> not even.

                Alternatively, we can iterate j in [26,49] with step 2.

                But the block1 has j from 25 to 49. The even j in the overall grid in this range are 26,28,...,48.

                Similarly, in block0: even j in [0,24] are 0,2,4,...,24.

            Therefore, we can iterate in the block by:

                for i in range(0, 25, 2):
                    for j in range(0, 25, 2):   # for block0: overall indices i and j are even.
                for i in range(0, 25, 2):
                    for j in range(26, 50, 2):  # for block1: note j from 26 to 48 inclusive, step=2 -> 26,28,...,48

                But wait, 26 is even, and then next is 28, etc.

            Alternatively, we can iterate over the entire grid and then check the block and the even-even condition.

            Given the simplicity, we'll iterate specifically over the blocks.

            We'll create a grid of 50 lists of 50 characters.

            Steps:

                n = 50, m = 50.

                # Initialize the four blocks
                for i in range(0, 25):
                    for j in range(0, 25):
                        grid[i][j] = 'A'
                for i in range(0, 25):
                    for j in range(25, 50):
                        grid[i][j] = 'B'
                for i in range(25, 50):
                    for j in range(0, 25):
                        grid[i][j] = 'C'
                for i in range(25, 50):
                    for j in range(25, 50):
                        grid[i][j] = 'D'

                a_count = a - 1
                b_count = b - 1
                c_count = c - 1
                d_count = d - 1

                # Block0: top-left (A)
                for i in range(0, 25, 2):   # step 2, even i
                    for j in range(0, 25, 2): # even j
                        if d_count > 0:
                            grid[i][j] = 'D'
                            d_count -= 1
                        elif c_count > 0:
                            grid[i][j] = 'C'
                            c_count -= 1
                        elif b_count > 0:
                            grid[i][j] = 'B'
                            b_count -= 1

                # Block1: top-right (B)
                for i in range(0, 25, 2):
                    for j in range(26, 50, 2): # even j in the overall grid: 26,28,...,48 (since 25 is the start and it's odd, then 26 is the first even)
                        # But note: we can also use j in range(25,50) and then check j%2==0, but 25,27,... are odd. We want even, so we start at 26 and step by 2.
                        if c_count > 0:
                            grid[i][j]

## Final Solution

