# Solution for 919_B.md

Generated at: 2025-07-22 06:05:04
Model: deepseek-reasoner

## Reasoning Process

We are to find the k-th smallest positive integer such that the sum of its digits is exactly 10.

Observations:
- The smallest perfect number is 19 (1+9=10). Then 28, 37, 46, 55, 64, 73, 82, 91, then 109 (1+0+9=10), 118, 127, etc.
- Note that the numbers are increasing, so we can generate them in increasing order? But k can be up to 10000, so we cannot generate one by one until we get k numbers (which might be very large).

Alternative approach: we can think of the numbers as being composed of digits that add up to 10. We can generate numbers by their digit representation.

We note:
- The number can have varying number of digits.
- The first digit cannot be 0.

We can model the problem as: we are looking for a nonnegative integer sequence of digits (d0, d1, ...) such that the sum of the digits is 10 and the first digit is not zero.

But note: the numbers are to be in increasing order. How to generate in increasing order?

Alternatively, we can use combinatorial generation. However, note that the numbers are not necessarily of fixed length.

We can consider the following:

Let the number have n digits. Then we are looking for nonnegative digits d0, d1, ... , d_{n-1} such that:
  d0 + d1 + ... + d_{n-1} = 10, and d0>=1.

But note: the numbers are ordered by value, which is not the same as by the digit representation (because 19 < 109, but 19 has two digits and 109 has three). Actually, the natural order of numbers is by length (shorter first) and then lexicographically? But note: 19 < 109? Actually, 19 is 19 and 109 is 109, so 19 < 109. So the numbers of fewer digits are smaller.

Therefore, we can break down the problem by the number of digits.

But how many digits can the k-th smallest number have? The smallest perfect numbers are two-digit numbers. Then we get three-digit, four-digit, etc.

How many two-digit numbers? 
They are: 
  d0 (from 1 to 9) and d1 = 10 - d0 (which must be from 0 to 9). So d0 must be at least 1 and at most 9, and 10-d0 must be at most 9 -> d0 must be at least 1 and at least 10-9=1. So d0 from 1 to 9? Actually, d0 from 1 to 9 and d1=10-d0 must be between 0 and 9 -> so d0 from 1 to 9? But wait: if d0=1, d1=9 -> 19; d0=2, d1=8->28; ... d0=9, d1=1 -> 91. So 9 numbers.

Then three-digit: 
  d0 (>=1) and d1, d2 >=0 such that d0+d1+d2=10.
  How many? This is a stars and bars problem: we have 10 stars, and we need to put 2 bars to separate into 3 nonnegative parts, but the first part (d0) must be at least 1. So we can let d0' = d0-1, then we have d0'+d1+d2 = 9, with d0', d1, d2 >=0. The number of solutions is C(9+2,2) = C(11,2)=55. But note: we must also ensure that each digit is at most 9? Because a digit cannot be more than 9.

But wait: we must subtract the cases where one digit is more than 9. We can use inclusion-exclusion.

Alternatively, we can generate the numbers in increasing order without having to iterate by digit length? But we need to know the k-th.

We can precompute the count for each digit length and then determine the digit length of the k-th number.

Steps:
1. For each digit length n (starting from 2 upwards), compute the number of n-digit numbers with digit sum 10 and without leading zeros (so first digit at least 1).

The problem: for fixed n, we have to count the solutions to:
  d0 + d1 + ... + d_{n-1} = 10, with d0>=1 and each d_i <=9.

We can transform by letting d0' = d0-1, then d0'>=0, and we have:
  d0' + d1 + ... + d_{n-1} = 9, with all nonnegative and each at most 9.

The total number without the upper bound constraint is: C(9+n-1, n-1) = C(8+n, n-1) [since we have 9 stars and n-1 bars? Actually: the formula for nonnegative solutions to x1+...+xr = t is C(t+r-1, r-1). Here t=9, r=n -> C(9+n-1, n-1)=C(n+8, n-1)=C(n+8, 9).

But then we must subtract the cases where one digit is >=10. Suppose one particular digit (say the first) is >=10: then we set that digit to x_i = x_i' + 10, then we have:
  x0' + 10 + ... = 9 -> x0' + ... = -1 -> impossible? Actually no: because we have 9 stars. If we set one digit to at least 10, then we have to solve for nonnegative integers: x0' + ... = 9 - 10 = -1 -> no solution. Similarly for any digit: if we set one digit to 10 or more, then the equation becomes: the rest of the digits must sum to a negative number -> impossible. Therefore, there are no solutions that violate the upper bound? 

Wait: note that the equation after transformation is 9. If we have one digit at least 10, then the rest would have to be negative? So indeed, no subtraction is needed. Therefore, the count for an n-digit number is C(9+n-1, n-1) = C(n+8, 8) [since C(n+8, n-1)=C(n+8,9) and also C(n+8,8)].

But note: the transformation: we have 9 stars and n-1 bars? Actually, the formula is C(9+n-1, 9) = C(n+8,9). However, note that we have n variables (d0', d1, ... d_{n-1}) and they are nonnegative. The formula is C(9+n-1, 9) or C(9+n-1, n-1). They are the same.

But wait: the formula for nonnegative solutions of x1+...+xr = t is C(t+r-1, r-1) or C(t+r-1, t). So here t=9, r=n -> C(9+n-1, 9)=C(n+8,9). 

But note: when n=2: C(2+8,9)=C(10,9)=10 -> but we know there are only 9 two-digit numbers. Why the discrepancy? 

Let me check: for two-digit numbers, we have:
  d0' + d1 = 9, with d0'=d0-1 (so d0>=1) and d0', d1>=0. 
  The solutions: 
      d0' from 0 to 9, and d1=9-d0'. 
  But then d0 = d0'+1, so d0 from 1 to 10? However, when d0'=9, d0=10 -> which is not a single digit. 

We forgot: each digit must be at most 9. But we argued that if a digit is 10 or more, then the equation becomes negative? But note: we only have two digits. When d0'=9, then d0=10 and d1=0 -> the number would be 100? But wait, that is a three-digit number. Actually, we are only considering two-digit numbers. So we must ensure that d0 (the first digit) is at most 9? and the other digits also at most 9. 

In the two-digit case: d0 = d0'+1, so d0 must be at most 9 -> d0' <=8. So we must subtract the cases where d0'>=10? Actually, d0' can be at most 9, but then d0 becomes 10 which is invalid. Similarly, d1 must be at most 9 -> so we require d0'<=9 and d1<=9 -> but since d1=9-d0', we have d0'<=9 and 9-d0'<=9 -> which is always true. The constraint that d1<=9 forces d0'>=0? But we also have d0'<=9? Actually, the constraint d1<=9 is automatically satisfied because d1=9-d0' and d0'>=0 -> d1<=9. However, the constraint for the first digit: d0 = d0'+1 <=9 -> d0'<=8.

Therefore, we must account for the constraint that each digit is at most 9. And in the transformed equation, the digit d0' is actually the first digit minus one, so it must be at most 8? Actually, no: the original first digit d0 must be at most 9 -> d0'=d0-1<=8. The other digits (d1, d2, ...) must be at most 9.

So the problem: we have to count nonnegative integer solutions to:
  d0' + d1 + ... + d_{n-1} = 9, 
  with d0' <= 8 and each d_i (for i>=1) <=9.

How to do that? We can use inclusion-exclusion.

Total without constraints: C(9+n-1, n-1) = C(n+8, n-1) = C(n+8,9).

Then subtract the solutions that break the constraints. 

Cases:
1. d0' >= 9. Let d0'' = d0'-9, then equation: d0'' + d1+...+d_{n-1} = 9-9=0 -> one solution: all zeros. So count for this case: 1.
But note: we also have to consider other digits? Actually, we have to consider any digit exceeding. But note: the constraint for the other digits: they are at most 9. However, in the transformed equation, the other digits are nonnegative and we are only subtracting the case for d0'? Actually, we have two constraints: 
   a) d0' <= 8
   b) for i from 1 to n-1: d_i <=9.

So we have multiple constraints. We can use inclusion-exclusion for multiple constraints.

But note: the constraints are independent. The constraints are:
  d0' <= 8
  for each j in [1, n-1]: d_j <=9.

We start with the total: C(n+8, 9)

Then subtract:
  A0: d0' >= 9 -> as above: set d0''=d0'-9, then equation: d0''+d1+...+d_{n-1}=0 -> 1 solution.
  For j from 1 to n-1: A_j: d_j>=10. Then set d_j'=d_j-10, then equation: d0'+...+d_j'+... = 9-10 = -1 -> no solution.

Therefore, the only constraint that can be violated is d0'>=9? So the count for n-digit numbers is: C(n+8,9) - 1.

But for n=2: then C(2+8,9)=C(10,9)=10, then subtract 1 -> 9. This matches.

What about n=3?
  Total without constraint: C(3+8,9)=C(11,9)=55.
  Subtract the case d0'>=9: that gives 1 case -> 54.

But let's list: 
  The three-digit numbers: 
      d0 from 1 to 9, then the remaining two digits must sum to 10-d0. But note: we transformed to d0'=d0-1, then the remaining two digits must sum to 9 - (d0-1)? Actually, the transformation: 
          d0' + d1 + d2 = 9, with d0'=d0-1, so d0>=1 -> d0'>=0, and then d0'<=8? and d1,d2>=0 and <=9.

  Without the constraint d0'<=8, the solutions are: 
        d0' from 0 to 9, and then d1 from 0 to 9, and d2=9-d0'-d1, which must be nonnegative -> so d1<=9-d0'. 
        So the count without constraint: 
            for d0'=0: d1 from 0 to 9 -> 10
            d0'=1: d1 from 0 to 8 -> 9
            ... 
            d0'=9: d1=0 -> 1
        Total: 10+9+...+1 = 55.

  Then subtract the case when d0'>=9: that is d0'=9: then d1+d2=0 -> only one solution: (9,0,0) -> but then d0 = d0'+1=10 -> which is invalid (because then the number would be 100, which has three digits: first digit 1? wait: no, d0=10? that's impossible. Actually, the representation: the first digit is d0 = d0'+1. So if d0'=9, then d0=10 -> which is not a single digit. So we must subtract that one.

  Therefore, we have 55-1=54.

But wait: the number 109: d0=1, d1=0, d2=9 -> then d0'=d0-1=0, so that is included. The number 190: d0=1, d1=9, d2=0 -> included. The number 901: but that is larger? Actually, we are generating by the representation, but the problem is that we are generating the counts, not the order. 

But note: we are only counting the numbers, and we are going to use the counts to determine the digit-length of the k-th number.

So plan:
  Precompute for each n (starting from 2) the count of n-digit perfect numbers: 
      count(n) = C(n+8, 9) - 1   [if n+8>=9, else 0? But n>=2 -> n+8>=10, so always n+8>=10? so C(n+8,9) is defined? Actually, if n+8<9 then 0, but n>=2 -> n+8>=10, so we are safe? Actually, for n=2: 2+8=10 -> C(10,9)=10, then subtract 1 -> 9.

  However, wait: what about the constraints on the other digits? We argued that only d0' can exceed (and then only when d0'>=9) and the other digits cannot exceed because if they exceed 9 then the equation becomes negative? Actually, that is true only because the total is 9. For example, in the three-digit case: if we set d1>=10, then we have d0' + d1 + d2 =9, then if d1>=10, then d0'+d2<=-1 -> impossible. Similarly for d2. So only d0' can be too big? 

  Therefore, the formula: count(n) = C(n+8,9) - 1.

But wait: what if n is large enough that even without the constraint we have solutions that break the digit constraint? For example, when n is large, we might have two digits that are too big? Actually, no: because the total sum is only 9 (in the transformed equation). We cannot have two digits both at least 10 because 10+10=20>9. So at most one digit can exceed, and that digit must be d0' (because the others are bounded by 9? actually, no: we have to consider that any digit could be the one that exceeds, but note: for the other digits (d_i for i>=1) we require at most 9, and if one of them is 10 or more, then the equation becomes negative -> no solution. The only constraint that can be broken is d0'>=9? because then we have one solution (d0'=9 and the rest 0). 

But note: what if we have a digit that is 10? Then the equation: the rest must be negative -> no solution. So only d0' can break the constraint? and then only by being 9 or more? and then we subtract the one case (d0'=9 and all others 0).

Therefore, the count for n-digit numbers is:
   count(n) = C(n+8,9) - 1   [for n>=2]

But wait: what about the case when n=1? 
  One-digit: the digit sum must be 10. The only one-digit numbers are from 1 to 9. But 10 is not a one-digit number. So no one-digit perfect number. So we start at two-digit.

Now, we can compute cumulative counts:

   cumulative = 0
   n=2: count(2)= C(10,9)-1 = 10-1=9 -> cumulative=9
   n=3: count(3)= C(11,9)-1 = 55-1=54 -> cumulative=63
   n=4: count(4)= C(12,9)-1 = 220-1=219 -> cumulative=282
   n=5: count(5)= C(13,9)-1 = 715-1=714 -> cumulative=282+714=996
   n=6: count(6)= C(14,9)-1 = 2002-1=2001 -> cumulative=996+2001=2997

We see that the cumulative counts grow quickly. Since k is at most 10000, we can iterate n until the cumulative count >= k.

But note: the k-th perfect number must be in the smallest n such that the cumulative count >=k.

Steps for the algorithm:

  Step 1: Precompute cumulative counts by digit length n (starting from n=2) until cumulative >= k.

  Step 2: Determine the digit length n0 such that the cumulative count for all numbers with digits < n0 is less than k, and the cumulative for numbers with digits <= n0 is >=k.

      Let total_so_far = 0
      n = 2
      while True:
          count_n = C(n+8, 9) - 1   [if n+8>=9, else 0] 
          if total_so_far + count_n >= k:
              break
          total_so_far += count_n
          n += 1

      Then the k-th number is the (k - total_so_far)-th smallest n-digit perfect number.

  Step 3: Generate the (k - total_so_far)-th smallest n-digit number with digit sum 10.

How to generate the (k - total_so_far)-th smallest n-digit number with digit sum 10?

  We have the first digit d0 (>=1) and then the remaining n-1 digits (each in [0,9]) such that the total sum is 10.

  Actually, we can use a combinatorial generation by digits.

  We note: the numbers are ordered. How? 
      First by the first digit (from 1 to 9), then by the next digit, and so on.

  But note: the entire number is ordered numerically. And since they all have the same number of digits, lex order is the same as numerical order.

  So we can generate the digits from left to right.

  We have:
      Let rem = k - total_so_far   (the rank in the n-digit numbers, starting at 1)
      The first digit: we try d0 from 1 to 9.

      For a fixed d0, the remaining n-1 digits must sum to 10-d0, and each digit in [0,9]. Also note: the remaining digits form a number that is nonnegative and we want the numbers in increasing order.

      How many numbers for a fixed d0? 
          It's the count of nonnegative solutions to: 
             x1 + x2 + ... + x_{n-1} = 10 - d0, with each x_i in [0,9].

      But note: we don't have the constraint on the first digit for the remaining part? Actually, the remaining digits can start with zeros? But the entire number is n-digit, so the subsequent digits can be zero? 

      The count for a fixed d0: 
          We have to solve: 
             y1 + ... + y_{n-1} = 10 - d0, with each y_i in [0,9].

      How to compute the count? We can use stars and bars with inclusion-exclusion.

      Total without constraint: C( (10-d0) + (n-1) - 1, (n-1)-1 )? Actually, nonnegative solutions: C( (10-d0) + (n-1) - 1, (n-1)-1 )? 

      Actually: for nonnegative solutions to y1+...+y_r = s: count0 = C(s+r-1, r-1).

      Then subtract solutions where one y_i>=10: if s>=10, then we set y_i'=y_i-10, then equation: y1+...+y_i'+... = s-10 -> count1 = C(s-10+r-1, r-1) [if s-10>=0, else 0]. Then add back if two digits are at least 10? but note: if we subtract two times 10, then s-20 must be nonnegative? and so on.

      However, note that the maximum we can subtract is floor(s/10). But since the total s=10-d0, which is at most 9 (because d0>=1) and at least 1 (if d0<=9). Actually, if d0=10, then we skip, but d0 is at most 9. So s = 10-d0 is in [1,9]. Therefore, we cannot have any y_i>=10 because 10>9. So the count for a fixed d0 is: 
          count0 = C( (10-d0) + (n-1) - 1, (n-1)-1 ) = C(10-d0+n-2, n-2)   [if 10-d0>=0] 
          and we don't have to subtract anything because if any y_i>=10, then 10-d0>=10 -> but 10-d0<=9, so no subtraction.

      Actually, note: the formula is C(s + r - 1, r-1) = C( (10-d0) + (n-1) - 1, (n-1)-1 ) = C(10-d0+n-2, n-2) = C(10-d0+n-2, 10-d0).

      But note: if 10-d0 is negative, then skip.

      However, 10-d0>=1 (since d0<=9). So we can compute.

      Example: n=3, d0=1 -> s=9, r=2 -> count = C(9+2-1, 2-1)=C(10,1)=10? 
          But we can list: the two digits that sum to 9: 
              (0,9), (1,8), (2,7), ... (9,0) -> 10 numbers. So correct.

      But wait: the formula: C(9+2-1, 2-1)=C(10,1)=10? Actually, the formula for nonnegative solutions is C(s+r-1, s) or C(s+r-1, r-1). Both are the same. So we can compute count = C( (10-d0)+n-2, n-2) = C(10-d0+n-2, 10-d0) [because C(a,b)=C(a,a-b)].

  Therefore, we can iterate d0 from 1 to 9:

      s = 10 - d0
      count_d0 = C(s+n-2, n-2)   [if s>=0, else 0]   [but note: s>=1]

      Then if rem <= count_d0, then we choose d0 as the first digit, and then we need to generate the (rem)-th smallest (n-1)-digit sequence (which can have leading zeros) that sums to s.

      Otherwise, subtract count_d0 from rem and try next d0.

  Step 4: Generate the remaining digits.

      Now we have to generate the (rem)-th smallest sequence of (n-1) nonnegative digits (each in [0,9]) that sum to s (which is 10-d0).

      How to generate the sequence? We traverse the digits from left to right.

      For the next digit (the second digit of the entire number), we try d1 from 0 to min(9, s):

          Then the remaining digits (n-2 digits) must sum to s - d1.

          The count for a fixed d1: 
              count_d1 = C( (s-d1) + (n-2) - 1, n-2-1 ) = C(s-d1+n-3, n-3)   [if s-d1>=0] 
              But note: again, because s-d1 <= s <=9, we don't have to worry about digits exceeding 9.

          Then if rem <= count_d1, then choose d1 and then recurse for the next digit with s'=s-d1 and length = n-2.

          Else, rem -= count_d1, and try next d1.

      We do this for all the digits.

But note: we are generating the entire number. Since n is at least 2, and the cumulative counts we computed earlier for n-digit numbers might be large (like 2001 for n=6) but k is only 10000, and the digit length n is at most maybe 10? (because for n=6 we already have cumulative 2997, so the k=10000 must be in n=6? Actually, we computed for n=6: cumulative=2997, then n=7: count(7)=C(7+8,9)-1=C(15,9)-1=5005-1=5004 -> cumulative=2997+5004=8001, then n=8: count(8)=C(16,9)-1=11440-1=11439 -> cumulative=8001+11439=19440>10000. So n=8 for k=10000? Actually, k=10000: 
   n=2: 9 -> k1=10000-9=9991
   n=3: 54 -> 9991-54=9937? Actually, we break at cumulative>=k. 

But note: we break at the n such that total_so_far (for digits < n) < k <= total_so_far + count(n). 

So for k=10000:
   total_so_far (for n<2)=0
   n=2: total_so_far + count(2)=9 <10000 -> then total_so_far=9
   n=3: 9+54=63 <10000 -> total_so_far=63
   n=4: 63+219=282 <10000 -> total_so_far=282
   n=5: 282+714=996<10000 -> total_so_far=996
   n=6: 996+2001=2997<10000 -> total_so_far=2997
   n=7: 2997+5004=8001<10000 -> total_so_far=8001
   n=8: 8001+11439=19440>=10000 -> so the k-th number is an 8-digit number, and the rank in the 8-digit perfect numbers is: rem = 10000-8001=1999.

  Then we generate the 1999-th smallest 8-digit number with digit sum 10.

  How to generate the 1999-th smallest? 

      First digit d0: from 1 to 9.

      For d0=1: then s=9, and we have 7 digits that sum to 9. The count = C(9+7-1, 7-1)=C(15,6)=5005? 
          Actually: count = C(9+7-1, 9) = C(15,9)=5005? But note: the formula: C(s+n-2, n-2) = C(9+8-2, 8-2)=C(15,6)=5005? 
          Actually: for the first digit: n=8, so the remaining digits: 7 digits, and s=9. Then count_d0 = C(9+7-1, 7-1) = C(15,6)=5005.

          Since 1999<=5005, so we choose d0=1.

      Then we generate the 1999-th smallest 7-digit sequence (with leading zeros allowed) that sums to 9.

      Now, for the next digit (second digit of the number, first of the remaining 7) we try d1 from 0 to min(9,9):

          d1=0: then the count for the remaining 6 digits that sum to 9: C(9+6-1,6-1)=C(14,5)=2002.
          Since 1999<=2002 -> we choose d1=0.

          Then the next digit: d2: from 0 to min(9,9). 
             d2=0: then remaining 5 digits sum to 9: count=C(9+5-1,5-1)=C(13,4)=715.
             d2=1: count=C(8+5-1,4)=C(12,4)=495 -> cumulative: 715+495=1210 -> still less than 1999? 
             Actually, we are not accumulating over the entire sequence? 

          How we do: 
             We have the current state: we are generating a sequence of 7 digits (with 7 digits remaining) that sum to 9. We break it down by the first digit.

          Actually, we have: 
             We are at the second digit of the entire number, and we have to generate a sequence of 7 digits that sum to 9. Then we break the sequence by the first digit of this sequence (which is the second digit of the entire number). Then we try d1 from 0 to 9.

          But note: we have already fixed d0=1 and then we are generating the remaining 7 digits. The entire number is then: 1, d1, d2, ... d7.

          Now, for the sequence of 7 digits that sum to 9, we iterate d1 from 0 to 9:

            count for d1 = C( (9-d1) + 6 - 1, 6-1) = C(9-d1+5,5) = C(14-d1,5)   [if 9-d1>=0]

            d1=0: count0 = C(14,5)=2002 -> 2002>=1999 -> so we choose d1=0? and then we are to generate the 1999-th sequence in the group d1=0? But wait: the sequences in d1=0 start at rank 1 to 2002. So the 1999-th sequence is in the d1=0 group? 

            Actually, we break the entire set of sequences of 7 digits that sum to 9 by the first digit. The sequences that start with 0 are the first 2002 sequences. So the 1999-th sequence is the 1999-th sequence in the group d1=0.

            Then we move to the next digit: now we have 6 digits that sum to 9. 

            For the next digit (third digit of the entire number) we try d2 from 0 to min(9,9):

                d2=0: count = C(9+5-1,5-1)=C(13,4)=715 -> 715 < 1999? -> no, wait: we are now in the group d1=0, and we are generating the 1999-th sequence in that group. But the entire group has 2002 sequences, and we are at the beginning? So the 1999-th sequence in the group d1=0 is the 1999-th sequence that starts with 0.

                Now, for the next digit: 
                    d2=0: then we have 5 digits that sum to 9 -> count=C(9+5-1,5-1)=C(13,4)=715. 
                    Then if 1999 <=715? no -> so we subtract 715 -> rem = 1999-715=1284.

                Then d2=1: count=C(8+5-1,4)=C(12,4)=495 -> 1284>495 -> subtract -> rem=1284-495=789
                d2=2: count=C(7+5-1,4)=C(11,4)=330 -> 789>330 -> subtract -> rem=789-330=459
                d2=3: count=C(6+5-1,4)=C(10,4)=210 -> 459>210 -> rem=459-210=249
                d2=4: count=C(5+5-1,4)=C(9,4)=126 -> 249>126 -> rem=249-126=123
                d2=5: count=C(4+5-1,4)=C(8,4)=70 -> 123>70 -> rem=53
                d2=6: count=C(3+5-1,4)=C(7,4)=35 -> 53>35 -> rem=18
                d2=7: count=C(2+5-1,4)=C(6,4)=15 -> 18>15 -> rem=3
                d2=8: count=C(1+5-1,4)=C(5,4)=5 -> 3<=5 -> so we choose d2=8, and then the next digits must form a sequence of 5 digits that sum to 1? (because 9-8=1) and we are at the 3rd smallest sequence? 

            Then we have: 
                The number so far: 1 0 8 ... 

            Then for the next digit (fourth digit): 
                We have 5 digits that sum to 1? 
                We iterate d3 from 0 to min(9,1):

                  d3=0: count = C(1+4-1,4-1)=C(4,3)=4 -> 3<=4? -> then we choose d3=0? and then we are to generate the 3rd smallest sequence of 4 digits that sum to 1.

            Then for the fifth digit (with 4 digits remaining and sum=1):
                d4 from 0 to 1:
                  d4=0: count = C(1+3-1,3-1)=C(3,2)=3 -> 3<=3 -> so we choose d4=0? and then we are to generate the 3rd smallest sequence of 3 digits that sum to 1.

            Then for the sixth digit (with 3 digits remaining and sum=1):
                d5 from 0 to 1:
                  d5=0: count = C(1+2-1,2-1)=C(2,1)=2 -> 3>2 -> subtract, rem=1, then d5=1: then the next two digits must be 0 and 0? 
                  Actually, if d5=1, then the remaining two digits must sum to 0 -> both 0. So the entire sequence for the last two digits is "00".

            Therefore, the entire sequence for the last 5 digits (from d3 to d7) is: 0,0,0,1,0? 
                But wait: we have 5 digits: 
                  d3 (the fourth digit) =0
                  then we have 4 digits: we set d4=0 (the fifth digit) and then we have 3 digits: then we set d5 (the sixth digit)=0? and then we set d6 (the seventh digit)=1? and d7=0? That doesn't sum to 1.

            Actually, we made a mistake: after we choose d3=0, then we have 4 digits (d4,d5,d6,d7) that must sum to 1. Then we choose d4=0? then we have 3 digits (d5,d6,d7) that must sum to 1. Then we choose d5=0: then we have 2 digits that must sum to 1 -> then the next digit d6 can be 1 and d7=0? or d6=0 and d7=1? 

            But note: we are generating the sequence in increasing order? So the smallest sequence for 2 digits that sum to 1: 
                (0,1) -> then (1,0) -> but note: (0,1) is smaller than (1,0). 

            How many for 2 digits that sum to 1: 
                d6 from 0 to 1: 
                  d6=0: then d7=1 -> one sequence: [0,1]
                  d6=1: then d7=0 -> second sequence: [1,0]

            But the count for 2 digits that sum to 1: 
                without constraint: C(1+2-1,1)=C(2,1)=2.

            Now, we are at the state: we have 3 digits (d5,d6,d7) to generate that sum to 1, and we are at the 3rd smallest? But wait: we had after choosing d5=0: then we have two digits that sum to 1 -> there are 2 sequences. Then the 3rd smallest would be the first sequence of the next group? 

            Actually, we broke the state:

                For the three digits (d5,d6,d7) that sum to 1: 
                  d5 from 0 to min(9,1):
                      d5=0: then we have two digits that sum to 1 -> count=2 -> but we have rem=3? and 3>2 -> so we subtract 2 -> rem=1, then d5=1: then the next two digits must be 0 and 0? -> sequence [1,0,0]? 

            Then the entire sequence for the last 5 digits: 
                d3=0, d4=0, d5=1, d6=0, d7=0 -> so the number: 1001000? 

            Then the entire number: 1 0 8 0 0 1 0 0 -> 1001000? 

            But the digit sum: 1+0+8+0+0+1+0+0=10 -> correct.

            And is it the 1999-th? 

            Actually, we have:
                The first digit: 1 -> then the next 7 digits: we are generating the 1999-th sequence that sums to 9? 
                But note: the entire set of 7-digit sequences that sum to 9: the sequences are ordered lexicographically. 

                We broke the first digit (d1) of the 7-digit sequence: 
                    d1=0: then we had 2002 sequences. 
                Then we broke the next digit (d2): 
                    d2=0: 715 sequences
                    d2=1: 495
                    d2=2: 330
                    d2=3: 210
                    d2=4: 126
                    d2=5: 70
                    d2=6: 35
                    d2=7: 15
                    d2=8: 5 -> and we chose d2=8 and then we were at the 3rd sequence in the group d1=0, d2=8.

                How many sequences before d2=8? 
                  715+495+330+210+126+70+35+15 = let's compute:
                  715+495=1210; 1210+330=1540; 1540+210=1750; 1750+126=1876; 1876+70=1946; 1946+35=1981; 1981+15=1996.

                So the first sequence in the group d1=0 and d2=8 is the 1997-th sequence? Then the 1998-th, 1999-th, 2000-th, 2001-th, 2002-th are the five sequences in that group.

                We wanted the 1999-th: so that is the third sequence in the group d1=0, d2=8.

                Then we generated: 
                    d3=0 -> then we had 4 digits that sum to 1? 
                    Then for the next digit (d4): 
                         d4=0: then 3 digits that sum to 1 -> count= C(1+3-1,3-1)=C(3,2)=3? 
                         Then we were at the 3rd sequence? 
                         The sequences for the last 3 digits that sum to 1: 
                             [0,0,1] -> first: d5=0, then the last two: [0,1] -> but wait, we broke the last two: 
                                 For the last two digits: 
                                     d5=0: then the last two digits must sum to 1: 
                                         d6=0: then d7=1 -> sequence: [0,0,1] -> that's the first.
                                         d6=1: then d7=0 -> second.
                                     Then d5=1: then the last two digits must be 0: [1,0,0] -> third.

                         So the third sequence is [1,0,0] -> so the entire 7-digit sequence: [0,8,0,0,1,0,0]? 
                         But wait: we have d3=0, d4=0, then the last three: d5=1, d6=0, d7=0 -> so [0,8,0,0,1,0,0] -> the entire number: 1001000? 

                Therefore, the entire number is 1001000? But that is 1,001,000? 

                However, we have 8 digits: 1,0,0,1,0,0,0,0? -> wait, we broke the digits: 
                    d0=1, d1=0, d2=8, d3=0, d4=0, d5=1, d6=0, d7=0 -> so the number is 10800100? 

                But then the digit sum: 1+0+8+0+0+1+0+0=10 -> correct.

            However, is this the 1999-th? Actually, the entire set of 7-digit sequences that start with 0 and then 8: and then we have the next digits: 
                The group d1=0, d2=8: the sequences are ordered by the next digits. The smallest: 
                    [0,8,0,0,0,0,9]? -> but wait: the remaining sum is 1, not 9. Actually, after d2=8, the remaining digits must sum to 1.

            So the sequences in the group d1=0, d2=8 are:
                The next 5 digits (d3,d4,d5,d6,d7) that sum to 1. How many? 
                    The count is the same as the number of nonnegative solutions to: 
                         x1+x2+x3+x4+x5=1 -> which is 5? 
                But we computed 5? 

                The sequences: 
                    [0,0,0,0,1] -> then the entire sequence: [0,8,0,0,0,0,1] -> but wait, we have 5 digits: d3,d4,d5,d6,d7. 
                    [0,0,0,1,0]
                    [0,0,1,0,0]
                    [0,1,0,0,0]
                    [1,0,0,0,0]

                But we generated: 
                    [0,0,1,0,0] -> which is the third? 

                Actually, we broke the digits from left to right. The lex order: 
                    d3=0: then the remaining 4 digits: 
                        d4=0: then the remaining 3: 
                            d5=0: then the last two: 
                                [0,1] -> so the sequence: [0,0,0,0,1] -> the entire sequence: [0,8,0,0,0,0,1] -> the number: 10800001? 
                            then [0,0,0,1,0] -> 10800010
                        d4=1: then the remaining 3: must be 0 -> [1,0,0,0,0] -> 10801000
                    ... 

                Actually, we generated in a DFS? 

                How we did: 
                  We fixed d3=0 -> then we had 4 digits left that sum to 1 -> then we tried d4 from 0 to 1:
                     d4=0: then we had 3 digits left that sum to 1 -> count= C(1+3-1,3-1)=C(3,2)=3 -> which means the sequences that start with d3=0, d4=0 are the first 3 sequences? 
                     Then we tried d4=1: then the next three digits must be 0 -> so only one sequence: [0,8,0,1,0,0,0] -> 10801000.

                So the sequences in lex order for the last 5 digits:

                  [0,0,0,0,1] -> rank1
                  [0,0,0,1,0] -> rank2
                  [0,0,1,0,0] -> rank3
                  [0,1,0,0,0] -> rank4
                  [1,0,0,0,0] -> rank5

                But we were at the 3rd sequence? so we generated [0,0,1,0,0] -> which is the third.

                Therefore, the entire 7-digit sequence: [0,8,0,0,1,0,0] -> so the entire number: 10800100? 

                But wait: the digits: 
                    d0=1 -> first digit
                    d1=0 -> second
                    d2=8 -> third
                    d3=0 -> fourth
                    d4=0 -> fifth
                    d5=1 -> sixth
                    d6=0 -> seventh
                    d7=0 -> eighth

                So the number: 10,800,100 -> 10800100.

                But is that the 1999-th? 

                The total sequences we skipped in the group d1=0: 
                    d2 from 0 to 7: 715+495+330+210+126+70+35+15 = 1996 sequences? 
                Then the group d2=8: the first two sequences are [0,8,0,0,0,0,1] and [0,8,0,0,0,1,0] -> then the third is [0,8,0,0,1,0,0] -> which is the 1996+3=1999-th.

            So it matches.

  Therefore, the algorithm for generating the n-digit number with rank r (1-based) and digit sum 10 is:

      Let digits = []   # we will build the digits of the entire number (which will be n digits)
      Let current_sum = 10
      Let remaining_digits = n
      Let rem = r   # the current rank in the n-digit numbers

      Step 1: the first digit: from 1 to 9 (since first digit cannot be 0)
          For d in range(1,10):
              s_remaining = current_sum - d
              # The remaining digits: remaining_digits-1 digits that sum to s_remaining, and each in [0,9]. 
              # How many sequences? 
                  If s_remaining < 0: break
                  If s_remaining==0: then the only sequence is all zeros -> count=1
                  Else: 
                      # We have to count nonnegative solutions to: x1+...+x_{m}=s_remaining, with m=remaining_digits-1, and each x_i<=9? 
                      # But note: since s_remaining<=9 (because d>=1) and the number of digits is remaining_digits-1, then we don't have to worry about digits exceeding 9? 
                      # Actually, we can use the formula: 
                      count = nCr(s_remaining + (remaining_digits-1) - 1, (remaining_digits-1)-1) = nCr(s_remaining + remaining_digits-2, remaining_digits-2)
                      # But note: if s_remaining==0, then we can also use the same formula? 
                         For s_remaining=0: count = nCr(0+remaining_digits-2, remaining_digits-2)=nCr(remaining_digits-2, remaining_digits-2)=1 -> so it works.

              If rem <= count:
                  append d to digits
                  current_sum = s_remaining
                  remaining_digits -= 1
                  break
              Else:
                  rem -= count

      Step 2: for the subsequent digits (which can be zero) and we have remaining_digits digits and current_sum remaining:

          For each position from the second to the last:
              For d in range(0,10):   # d from 0 to 9
                  If d>current_sum: break
                  s_remaining = current_sum - d
                  If remaining_digits-1==0: 
                      if s_remaining==0: then we have one solution -> then we take d and break.
                      else: skip.
                  Else:
                      count = nCr(s_remaining + remaining_digits-2, remaining_digits-2)   # because m = remaining_digits-1, then count = C(s_remaining + (remaining_digits-1)-1, (remaining_digits-1)-1)=C(s_remaining+remaining_digits-2, remaining_digits-2)

                  If rem <= count:
                      append d to digits
                      current_sum = s_remaining
                      remaining_digits -= 1
                      break
                  Else:
                      rem -= count

      But note: we must be cautious: the combinatorial function nCr(n, r) returns 0 if n<r or if n<0.

  However, note: s_remaining might be negative? We break if d>current_sum.

  Also, if s_remaining==0 and remaining_digits>0, then we have to assign zeros to the rest? but our count formula for the next step: 
      If we have remaining_digits-1 digits and s_remaining=0, then count=1 (as above). 

  But if we have multiple zeros to assign, that is one solution.

  Therefore, the code for the combinatorial count for the remaining part: 
      def count_ways(s, r):
          # s: the remaining sum, r: the number of digits (each in [0,9]) 
          # Since s<=9 (in our case, because we subtract at least 1 in the first digit and then the rest we subtract at least 0) and then we have the remaining digits? Actually, after the first digit, the remaining sum is 10-d0, which is at most 9. Then in the next digits, we subtract d, so the next s_remaining is even smaller. So we never have s_remaining>9? 
          # But note: we are using the formula: 
          #   count = C(s + r - 1, r - 1)   [if s>=0, else 0]
          # However, if r==0: then we require s==0? 
          if s < 0:
              return 0
          if r == 0:
              return 1 if s==0 else 0
          # But note: our formula: for nonnegative solutions to x1+...+xr = s: count = C(s+r-1, r-1) 
          # But we must ensure that s is nonnegative? 
          if s == 0:
              return 1
          # But if s>0, then:
          # However, if s is large? but in our case s<=9? 
          # But actually, we are using this formula only when we are in the subsequent digits? and the initial s_remaining after the first digit is at most 9. Then in the next steps, the current_sum is nonnegative and we subtract d, so s_remaining is nonnegative and <=9. And the number of digits r is at least 1. 
          # But we have to compute nCr(s+r-1, r-1). 
          # However, note: if s+r-1 < r-1, then it's 0? Actually, if s==0, we handled above. 
          # We can compute using a helper function that uses dynamic programming? or we can precompute a Pascal's triangle? 

  But note: the parameters: 
      s: at most 9? (but actually, after the first digit, the remaining sum is at most 9. Then in the next steps, it decreases. But if we have many digits, then s can be 0? 
      r: the number of digits, which is at most (the entire digit length minus the digits we already fixed) and the entire digit length n is at most 10? (we saw for k=10000, n=8) -> so r is at most 8. 

  Therefore, we can precompute a Pascal triangle for n up to 100? or we can compute nCr using a direct formula since the numbers are small.

  Alternatively, we can compute the combinatorial number on the fly with a helper function:

      def nCr(n, r):
          if r < 0 or r > n:
              return 0
          r = min(r, n-r)
          num = 1
          for i in range(1, r+1):
              num = num * (n-i+1) // i
          return num

      Then count_ways(s, r) = nCr(s+r-1, r-1)   [if s>=0 and r>=1] 
          and for r==0: we use the base case.

  But note: in the subsequent digits, we have:
      count = count_ways(s_remaining, remaining_digits-1)

  Actually, we have: the next state: we have (remaining_digits-1) digits that must sum to s_remaining? 
      So the count = nCr(s_remaining + (remaining_digits-1) - 1, (remaining_digits-1)-1) = nCr(s_remaining+remaining_digits-2, remaining_digits-2)

  But note: if remaining_digits-1==0? then we are at the last digit? then we do a separate check.

  Alternatively, we can write a unified function:

      def count_comb(s, num_digits):
          # Count nonnegative solutions to x1+...+x_{num_digits} = s, with each x_i<=9? 
          # But we argued that since s<=9, we don't have to worry about the constraint? 
          # So we can use: 
          if s < 0:
              return 0
          if num_digits == 0:
              return 1 if s==0 else 0
          # Otherwise: 
          return nCr(s+num_digits-1, num_digits-1)

  However, note: the constraint that each digit is at most 9? But we are only using this when the remaining sum s is at most 9? then each digit cannot be more than 9? So it's safe.

  But wait: what if s>9*num_digits? Then the formula would give a positive number? but actually it's impossible. 
      However, our s is at most 9, and num_digits>=1, so 9*num_digits>=9>=s? so it's always possible? 

  Actually, if s>9*num_digits, then we return 0. But in our case, s<=9, and num_digits>=1, so 9*num_digits>=9>=s? so we are safe.

  Therefore, we can compute:

      count = count_comb(s_remaining, remaining_digits-1)   # for the remaining part

  However, note: the above formula is for nonnegative solutions without the upper bound? and we are using the fact that the upper bound (<=9) is automatically satisfied because s_remaining<=9 and the number of digits is arbitrary? 

  But wait: what if we have many digits? For example, if we have 3 digits and s_remaining=9, then the formula gives C(9+3-1,3-1)=C(11,2)=55? but we know that without the constraint we have 55, but with the constraint? we argued that since s_remaining=9, we don't have to subtract any? 

  But note: the constraint is each digit<=9? and 9<=9, so we don't have to subtract? 

  Therefore, the formula holds.

  However, note: the above formula is for nonnegative solutions? and that is what we want.

  Implementation:

      We'll write a helper function for nCr for small numbers.

      Steps:

        total_so_far = 0
        n = 2
        while True:
            count_n = nCr(n+8, 9) - 1   # number of n-digit perfect numbers
            if total_so_far + count_n >= k:
                rem = k - total_so_far   # the rank in the n-digit numbers
                # Generate the n-digit number that is the rem-th smallest
                digits = []
                s_remaining = 10
                digits_remaining = n
                # For the first digit: from 1 to 9
                for d in range(1,10):
                    s_after = s_remaining - d
                    if s_after < 0:
                        break
                    # Count the number of solutions for the remaining (n-1) digits that sum to s_after
                    # The remaining digits: digits_remaining-1 = n-1
                    cnt = count_comb(s_after, digits_remaining-1)
                    if cnt <=0: 
                        # but if s_after==0, then we have one solution? 
                        # Actually, if digits_remaining-1==0 and s_after==0, then cnt=1. So we don't skip.
                        # So we only break if d>s_remaining, which we did above.
                        pass
                    if rem <= cnt:
                        digits.append(str(d))
                        s_remaining = s_after
                        digits_remaining -= 1
                        break
                    else:
                        rem -= cnt

                # Then for the next digits (if any left)
                while digits_remaining > 0:
                    # d from 0 to 9
                    for d in range(0,10):
                        if d > s_remaining:
                            break
                        s_after = s_remaining - d
                        # Count the solutions for the remaining digits_remaining-1 digits that sum to s_after
                        cnt = count_comb(s_after, digits_remaining-1)
                        if rem <= cnt:
                            digits.append(str(d))
                            s_remaining = s_after
                            digits_remaining -= 1
                            break
                        else:
                            rem -= cnt
                # Now we have the digits, form the number
                return int(''.join(digits))

            else:
                total_so_far += count_n
                n += 1

  But note: what if we don't break in the first digit? then we break the for-loop by the break condition (d>s_remaining) and then we go to the next part? but we didn't append any digit? 

  We must ensure that we break the for-loop only when we have chosen a digit.

  Actually, we break the for-loop when we choose a digit? 

  Also, we must ensure that the count_comb function is defined.

  Let's test with k=1: 
        total_so_far=0, n=2: count_2 = nCr(10,9)-1 = 10-1=9 -> total_so_far+9>=1 -> true.
        rem=1.
        Then we generate the 2-digit number: 
            first digit: d from 1 to 9:
                d=1: s_after=9, then cnt = count_comb(9,1) -> which is: 
                    count_comb(9,1): 
                        s=9, num_digits=1 -> nCr(9+1-1,1-1)=nCr(9,0)=1? 
                Then rem=1<=1 -> so we choose d=1.
                Then we have one digit left: then we run the next part: 
                    digits_remaining=1, s_remaining=9.
                    Then for d in [0,9]: 
                        d=0: then s_after=9, then cnt = count_comb(9,0) -> which is: 
                              if num_digits==0: return 1 if s_after==0 else 0 -> here s_after=9, so cnt=0 -> skip.
                        d=1: cnt=0 -> skip.
                        ... until d=9: 
                            s_after=0 -> then cnt = count_comb(0,0)=1? 
                            Then rem=1<=1 -> so we choose d=9.
                    Then the number: 19 -> correct.

  k=2: 
        same n=2, rem=2.
        first digit: d=1: cnt=1 -> rem=2>1 -> so rem=2-1=1, then try d=2.
            then s_after=8, cnt=count_comb(8,1)=nCr(8+1-1,0)=nCr(8,0)=1? 
            Then rem=1<=1 -> so choose d=2, then the next digit: 
                digits_remaining=1, s_remaining=8: 
                    d=0 to 8: 
                        d=0: cnt=0 -> skip until d=8: 
                            then s_after=0 -> cnt=1 -> choose d=8 -> 28.

  So it works.

  But note: the count_comb function for the last digit: 
        count_comb(s_after, 0) -> returns 1 if s_after==0, else 0.

  However, we can also break when digits_remaining==1: then we assign the last digit to s_remaining? 
      But we are doing: 
          for d in range(0,10):
              if d>s_remaining: break   -> if s_remaining>0 and <=9, then we break when d>s_remaining, which is after d=s_remaining? 
          Then we try d from 0 to min(9, s_remaining). 
          Then when we choose d, we require that s_after= s_remaining-d must be 0? because the next count_comb(0,0)=1? 
          But if we have one digit left and we assign d, then we require d==s_remaining? 

      Actually, we can do: 
          if digits_remaining==1:
              if s_remaining>9: -> no solution? but we have already broken by the for-loop condition? 
              Then we must assign d = s_remaining? and then we break.

      But our count_comb function for the next state (with 0 digits) will return 1 only if s_after==0. So we are effectively requiring that the last digit is exactly s_remaining? 

      Therefore, we can also skip the combinatorial count in the last digit? 

  However, the current code works.

  But note: the combinatorial count for the last digit: 
        count_comb(s_after, 0) = 1 if s_after==0, else 0.

      Then if we have one digit left and we choose d, then we only take it if d==s_remaining? 

      Then the count for a fixed d in the last digit is 1 if d==s_remaining and 0 otherwise? 

  So we can do without the combinatorial function for the last digit? 

  But the code is general and the numbers are small, so it's acceptable.

  We need to implement nCr(n, r) for n up to? 
      In the first part: we computed nCr(n+8,9) for n up to? 
          n=2: 10
          n=3: 11
          ...
          n=8: 16 -> so nCr(16,9) -> which is about 11440.

      The parameters in the combinatorial count for the generation: 
          s_after: at most 9, and digits_remaining-1: at most n-1, which is at most 8 (for n=8) -> so the arguments for nCr in count_comb: 
              n = s_after + (digits_remaining-1) - 1 -> at most 9+7=16? 
          So the maximum n in the combination is 16? 

  Therefore, we can precompute a Pascal triangle for n from 0 to 20? 

  However, we can also compute on the fly with a helper function that uses a loop for small numbers.

  Let's write:

      def nCr(n, r):
          if r < 0 or r > n:
              return 0
          if r == 0 or r == n:
              return 1
          r = min(r, n-r)
          num = 1
          for i in range(1, r+1):
              num = num * (n - i + 1) // i
          return num

      Then count_comb(s, num_digits) = 
          if num_digits==0:
              return 1 if s==0 else 0
          else:
              return nCr(s+num_digits-1, num_digits-1)

  But note: the formula: nonnegative solutions: C(s+num_digits-1, num_digits-1) = C(s+num_digits-1, s)

  Alternatively, we can write: 
      return nCr(s+num_digits-1, s)   # because C(a,b)=C(a,a-b) and s+num_digits-1 - (num_digits-1)=s, so C(s+num_digits-1, s) = C(s+num_digits-1, num_digits-1)

  But we can use either.

  However, we must be cautious: when s==0, then nCr(num_digits-1, num_digits-1)=1? 
        But: s+num_digits-1 = num_digits-1, and we take min(s, num_digits-1) -> but we are using the formula: 
          nCr(n, r) with r=min(r, n-r) -> but then we compute.

  Alternatively, we can use:

      def count_comb(s, num_digits):
          if s < 0:
              return 0
          if num_digits == 0:
              return 1 if s==0 else 0
          return nCr(s+num_digits-1, num_digits-1)

  Now, test: 
      count_comb(9,1) -> s=9, num_digits=1: 
          return nCr(9+1-1, 1-1)=nCr(9,0)=1 -> correct.

      count_comb(0,0) -> returns 1? 

      But the definition: if num_digits==0: then we return 1 if s==0 -> so for (0,0): 1.

      Also, count_comb(0,1): 
          num_digits=1: nCr(0+1-1,0)=nCr(0,0)=1? 
          But what is the solution for 0 in 1 digit? -> [0] -> one solution.

  Therefore, we can use.

  But note: when s=0 and num_digits>0, we have one solution: all zeros.

  This matches.

  Now, what if s=1 and num_digits=2: 
        nCr(1+2-1, 2-1)=nCr(2,1)=2 -> which is: [0,1] and [1,0] -> two solutions.

  So the code:

      We'll implement the helper functions.

  Let's code accordingly.

  However, note: the first part: we computed count_n = nCr(n+8,9) - 1.

      We need to compute nCr(n+8,9) for n from 2 up to at most? 
          We break when total_so_far+count_n>=k, and k<=10000.
          We computed: 
              n=2: 9
              n=3: 54
              n=4: 219
              n=5: 714
              n=6: 2001
              n=7: 5004
              n=8: 11439 -> cumulative=19440>10000 -> so we break at n=8.

          So we only need n from 2 to 8.

  Therefore, we can compute nCr for n from 10 to 16 (for n=2: 2+8=10, n=8:8+8=16) and r=9.

  But we can compute with the same nCr function.

  But note: the nCr function we wrote works for n up to 16, which is small.

  Implementation:

      We'll write the helper functions as described.

  Let's test with the examples:

      Example: k=1 -> 19
      Example: k=2 -> 28

      Example: k=10: the 10th smallest? 
          The first 9 are the two-digit numbers: 19,28,...,91 -> then the 10th is the first three-digit number: 109.

      How our algorithm for k=10:
          total_so_far=0
          n=2: count_2=9 -> total_so_far+9=9 <10 -> so total_so_far=9, then n=3: count_3 = nCr(3+8,9)-1 = C(11,9)-1 = 55-1=54 -> total_so_far+54=63>=10 -> so rem=10-9=1.

          Then we generate the 1st smallest 3-digit number with digit sum 10.

          First digit: d from 1 to 9:
            d=1: s_after=9, cnt = count_comb(9,2) = nCr(9+2-1,2-1)=nCr(10,1)=10? 
            Since rem=1<=10 -> choose d=1.
          Then the next two digits: 
            digits_remaining=2, s_remaining=9.
            Then for d1 in [0,9]:
                d1=0: s_after=9, then count = count_comb(9,1)=1? 
                Then rem=1<=1 -> choose d1=0? then the next digit must be 9? 
            So the number: 109.

          Correct.

  Therefore, we write the code accordingly.

  Note: the problem says k>=1 and k<=10000.

  We'll code accordingly.

  Let's code:

      We assume we have the nCr function and the count_comb function.

      Steps:

        def nCr(n, r):
            if r < 0 or r > n:
                return 0
            if r == 0 or r == n:
                return 1
            r = min(r, n - r)
            num = 1
            for i in range(1, r+1):
                num = num * (n - i + 1) // i
            return num

        def count_comb(s, num_digits):
            if s < 0:
                return 0
            if num_digits == 0:
                return 1 if s == 0 else 0
            # Otherwise: nonnegative solutions to x1+...+x_{num_digits}=s
            # and we don't have to subtract for upper bound because s<=9? 
            return nCr(s + num_digits - 1, num_digits - 1)

        total_so_far = 0
        n = 2
        # We'll iterate n until we find the n that contains the k-th number
        while

## Final Solution

