# Solution for 855_C.md

Generated at: 2025-07-22 05:56:02
Model: deepseek-reasoner

## Reasoning Process

Alright, I'm going to tackle this programming problem step by step. Let me start by understanding the problem clearly.

**Problem Understanding:**
We have a tree representing the vaults in <PERSON><PERSON><PERSON>'s bank. Each vault is to be assigned a type from 1 to m. There's a specific type k that is the highest security type. Conditions:
1. All vaults of type k are the highest security, and there can be at most x such vaults (so 0 to x vaults can have type k).
2. If a vault is of type k, then none of its adjacent vaults can be of type k, and their types must be strictly less than k.

Important constraints:
- n (number of vaults) up to 10^5
- m (types) up to 10^9
- x (max highest-security vaults) up to 10

From the examples, we can infer:
- In Example 1: n=4, m=2, edges between (1,2), (2,3), (1,4); k=1, x=2. Output is 1. Explanation: Since k=1, if any vault is assigned 1, then its neighbors must have type <1, which is impossible. So only possibility is all vaults are type 2. Hence, 1 way.
- Example 2: n=3, m=3, edges (1,2) and (1,3); k=2, x=1. Output is 13. How?
- Example 3: n=3, m=1, edges same; k=1, x=1. Output is 0. Because if we assign k=1 to any vault, adjacent must be <1, which is impossible (since types start at 1). Also, if we don't assign k=1, but m=1, then all must be type 1, which again forces adjacent to be <1 — impossible. So 0.

So, the problem is counting the number of ways to assign types to each node (from 1 to m) such that:
- The highest security type is k, meaning that any vault assigned k must satisfy that no neighbor is k and all neighbors must be assigned a type less than k.
- The total number of vaults assigned k is at most x.

Note: Types for non-highest-security vaults can be any number from 1 to m, except that if a vault is adjacent to a k, then it must be in [1, k-1]. Otherwise, it can be any value from 1 to m (including k? Actually, no: because if a neighbor is k, then we cannot assign k to adjacent. But if no neighbor is k, then we can assign k? Wait, condition says: only the vaults of type k are the highest security. Also, condition 2: if a vault is of type k, then adjacent must not be k and must be < k. But what if a vault is not adjacent to any k? Then it can be k? Actually, no: because if we assign k to a vault that is adjacent to a non-k, that's okay as long as that non-k is not k? Actually, condition 2 only imposes restrictions when a vault is assigned k. So for a vault that is not k, there are no restrictions from condition 2. However, condition 1 says that all vaults of type k are highest security and that there can be at most x of them. So, we can have multiple k's as long as no two k's are adjacent and the total count of k's is at most x.

But wait: condition says "if a vault is of the highest security, its adjacent vaults are guaranteed to not be of the highest security and their type is guaranteed to be less than k". So, adjacent vaults of a k must be <k and not k. But what about non-adjacent? They can be k? Actually, yes, but only if they are not adjacent to any k? But then we can have two k's that are not adjacent? That is allowed, as long as they are not adjacent to each other.

Therefore, the constraints are:
- The set of nodes assigned k must form an independent set (no two adjacent).
- The size of this independent set is at most x.
- For any node assigned k, its neighbors must be assigned a type in [1, k-1]. For nodes not assigned k, they can be assigned any type in [1, m] except that if a node is adjacent to a k, then it must be in [1, k-1]. Otherwise, it can be any type from 1 to m.

Wait, but what if a node is not adjacent to a k? Then it can be assigned k? Actually, yes, because then we would have two k's that are not adjacent. But then that node being k would impose that its neighbors must be <k and not k. So, we can have multiple k's as long as they are non-adjacent and each k forces its neighbors to be in [1, k-1].

But also, a node that is not adjacent to any k can be assigned a type from 1 to m. However, if we assign k to it, that's acceptable as long as we don't exceed x and as long as its neighbors are not k and are <k. But if we assign k to a node, then we must check that its neighbors are not k and are assigned <k? Actually, when we assign k to a node, we are forcing its neighbors to be non-k and <k. But if a neighbor was already assigned a value >=k? Then that would be invalid.

Therefore, we need to assign types to the entire tree such that:
- The set of nodes with value k is an independent set and has size <= x.
- For every node u that is assigned k, each neighbor v of u must be assigned a type in [1, k-1].
- For a node not assigned k, if it has a neighbor that is k, then it must be assigned a type in [1, k-1]. Otherwise, it can be assigned any type in [1, m].

But note: a node that is not adjacent to any k can be assigned any type from 1 to m, including k? Actually, that's correct. However, if we assign k to such a node, then we must ensure that none of its neighbors is k? But if the node is not adjacent to any k, then by definition, its neighbors are not k. So, we can assign k to it. But then, after assigning, we must enforce that its neighbors are now forced to be in [1, k-1]. So, we have a dependency.

This seems complex. How can we model the assignment?

We can use dynamic programming on trees. Given that x is at most 10, we can use a DP that tracks the number of highest security vaults (i.e., the count of k) in the subtree and also the state of the current node.

Let me think of a DP state:

We can define:
dp[u][j] = number of ways to assign types to the subtree rooted at u, given that the number of highest security vaults (type k) in the subtree is j (0<=j<=x), and we have to also account for the constraint that if u is assigned k, then its children cannot be k and must be <k; and if u is not assigned k, then we have two cases: whether u is adjacent to a k in the parent? Actually, no, because the constraint is only about adjacent. But the constraint for a node u from above (parent) might be different than from below (children). Actually, the tree is undirected, but in a rooted tree, we process from leaves to root.

However, note that the constraint for a node u: if u is k, then all neighbors (parent and children) must be non-k and <k. If u is not k, then if any neighbor (parent or child) is k, then u must be <k. But wait: if u is not k, then we don't have the restriction that u must be <k unless a neighbor is k? Actually, condition says: if a vault is of the highest security (k), then adjacent must be non-k and <k. But if a vault is not of the highest security, then there's no such restriction from the adjacent condition? However, the adjacent condition is only imposed by the highest security vaults. So, if a node is not adjacent to any k, then it can be any type (including k? but if it's k, then it becomes highest security and then forces its neighbors to be non-k and <k). So, actually, we can have a node that is k only if none of its neighbors are k? And then, for a node that is not k, if it has a neighbor that is k, then it must be in [1, k-1]. Otherwise, it can be in [1, m] (including k? but then if we assign k to it, then we must check that none of its neighbors is k? But if we assign k to a node that is not adjacent to any k, that's allowed. So, the assignment of k to a node is independent as long as no two adjacent nodes are k.

But then, we can reframe the problem: we are to count the assignments of types to nodes such that:
- The set of nodes assigned k is an independent set and has size at most x.
- For any edge (u, v):
   - If u is k, then v must be in [1, k-1].
   - Similarly, if v is k, then u must be in [1, k-1].

But note: the condition for non-k nodes that are adjacent to a k is automatically enforced by the independent set condition and the type restriction for neighbors of k. For non-k nodes that are not adjacent to any k, they can be any type from 1 to m? Actually, yes.

But wait: what if a non-k node is adjacent to a non-k node? Then there's no restriction? So, they can be any type from 1 to m? Actually, no: because if a non-k node is adjacent to a k node, then it must be <k. But if a non-k node is adjacent to only non-k nodes, then it can be any type from 1 to m? Including values >=k? But note: the condition only restricts the neighbors of k. For two non-k nodes, there's no restriction. So, they can be any type from 1 to m. For example, they can be k+1, k+2, ... m? Yes.

So, to summarize:
- Each node can be assigned a type from 1 to m.
- The set S of nodes assigned k must be an independent set and |S|<=x.
- For any node u in S (assigned k), all neighbors of u must be assigned a value in [1, k-1].
- There are no other restrictions.

Now, how to count the assignments? We can use DP on trees. Since x is small (<=10) and n is large (10^5), we can design a DP that runs in O(n*x^2) or similar.

Idea for DP:

We traverse the tree (rooted at an arbitrary node, say 1). For each node u, we compute a DP table for the subtree rooted at u.

We need to remember:
- The state of u: whether it is assigned k or not.
- Also, we need to remember the number of k's in the subtree so far (up to x).
- Additionally, if u is not assigned k, we have two subcases: 
   - Case 1: u is forced to be in [1, k-1] because one of its neighbors (in this case, the parent) is k? Actually, no: the parent's state is not known when processing the children? So we need to know from the parent's assignment? Actually, we can use the common technique: let the state of the node u include whether u is assigned k or not, and the count of k's in the subtree. But also, we must account for the constraint that if the parent is k, then u must be in [1, k-1]. However, when processing u, we don't know the parent's state? So we have to consider the state of u and then when merging children, we have to account for the constraints between u and its children.

Alternatively, we can use a state that also records the type of u (in a compressed way) and the count of k's in the subtree.

Given that k is fixed and m can be very large (up to 10^9), we cannot iterate over types. But note: the restrictions only care about whether the type is k or not, and for non-k, we have two categories: 
- When the node is adjacent to a k (so must be in [1, k-1]), 
- When it is not adjacent to a k (so can be any type in [1, m] except that we can also choose k? but if we choose k, then we have to treat it as k? Actually, we cannot choose k for a node that is adjacent to a k? So if a node is adjacent to a k, then it must be in [1, k-1] (so k-1 choices). If a node is not adjacent to any k, then we have two options: 
   - Assign k: then it becomes a highest security vault (and then we have to enforce that its neighbors are non-k and <k). 
   - Assign non-k: then we can assign any type from 1 to m except k? Actually, no: we can assign k? But if we assign k, then we are making it highest security. So, for a node not adjacent to any k, we have the option to assign k (if we haven't exceeded x) or assign non-k. But for non-k, we have two possibilities: 
        a) Assign a type in [1, k-1] (which gives k-1 choices)
        b) Assign a type in [k+1, m] (which gives m-k choices)

But note: if we assign k to a node that is not adjacent to any k, that's allowed. So, for a node that is not adjacent to any k (in the entire tree, but in the DP we are building from children to parent, we don't know the parent's state) — actually, we have to account for the parent's state when we are at the root. So, we need a DP that is independent of the absolute type but depends on the constraints from the parent.

Therefore, we design a DP that for each node u, we compute for the subtree rooted at u, and we consider the state of u and the constraint from the parent (which we don't know yet) — so we have to consider two states for u: 
- Option 1: u is assigned k.
- Option 2: u is not assigned k.

But also, if u is not assigned k, we have to consider: 
   - If u's parent is assigned k, then u must be in [1, k-1] (so k-1 choices). 
   - If u's parent is not assigned k, then u can be assigned any type in [1, m] except that if u is assigned k, then we have to account for that in the state? Actually, we are already separating the state of u: either k or not k. 

Wait, perhaps we can have a state that records:
- The number of k's in the subtree rooted at u.
- And the state of u: either u is k, or u is not k.

But then, when combining with the parent, we have to enforce: 
- If u is k, then the parent cannot be k. 
- If the parent is k, then u must not be k and must be in [1, k-1] — but we don't know the parent's state in the DP of u. 

So, we can use a DP that is computed for each node and for each possible state (the state being: the count of k in the subtree and whether u is k or not), and then when we go up, we combine the children's states accordingly.

But actually, a common technique is to have the state of the current node include the necessary information for the parent. We can design:

Let dp[u][j][c] where:
   j: the number of k's in the subtree rooted at u (0<=j<=x)
   c: the state of u, which can be 0, 1, or 2? Actually, we need to know for the parent: 
   Actually, we can use two states:
   - state 0: u is assigned k. Then the parent must not be k.
   - state 1: u is not assigned k, and also u is not constrained by having a k parent? Actually, no: we need to know for the children: but for the parent, we don't know. Alternatively, we can use the following:

Actually, we can have:
   dp[u][j][0] = number of ways for the subtree rooted at u, with j vaults of type k in the subtree, and u is assigned k.
   dp[u][j][1] = number of ways for the subtree rooted at u, with j vaults of type k in the subtree, and u is not assigned k, but note: if u is not assigned k, then we have to account for the choices and also for the constraint that if the parent is k, then u must be in [1, k-1]. However, when processing u, we don't know the parent's state. So we have to account for both possibilities? Actually, we can split state 1 into two: 
      - state 1: u is not k and is assigned a type in [1, k-1] (so that if the parent is k, it's safe; and if the parent is not k, it's also safe). 
      - state 2: u is not k and is assigned a type in [k+1, m]. But then, if the parent is k, we cannot assign this because then u would be >=k and adjacent to k? Actually, condition: if a node is k, then adjacent must be <k. So if u is assigned a type >=k and not k, and the parent is k, that's invalid. Therefore, we cannot use state 2 if the parent is k.

But we don't know the parent's state when processing u. So we have to consider the possibilities that u might be adjacent to a k in the parent. Therefore, we design the DP so that at node u, we compute the states independently of the parent, but then when we combine with the parent, we will enforce the constraint.

Alternatively, we can design the state to be independent of the parent and then when merging, we only enforce the constraint between u and its children. Actually, the constraint is symmetric: if u is k, then children must not be k and must be <k. Similarly, if a child is k, then u must not be k and must be <k? Actually, no: if a child is k, then u (as a parent) must not be k and must be assigned a type in [1, k-1]. So, the constraint between u and any child v is: they cannot both be k, and if one is k, the other must be in [1, k-1].

Therefore, we can have for each node u:

We define a state for u that is one of three:
   - state 0: u is assigned k.
   - state 1: u is assigned a type in [1, k-1] (so that if any child is k, then u is safe because it's <k, and if a child is not k, then we don't care about u's type? Actually, no: the child's constraint: if the child is k, then u must be <k, which is satisfied by state 1. If the child is not k, then no constraint on u from the child? But note: if the child is not k and is in [k+1, m], then u can be anything? Actually, no: the constraint only goes from k to neighbors. So, for u, if a child is k, then u must be in [1, k-1] (which is state 1). But if the child is not k, then u can be any type (including k? but then if u is k, then the child must be in [1, k-1] — so we have to check the child's state). Therefore, the constraint is bidirectional: we must avoid having u and a child both k, and if one is k, the other must be in [1, k-1].

So, we need to know the state of u and the state of the children to enforce the constraints.

We design the DP state as:

dp[u][j][s] for node u, with j k's in the subtree, and s in {0, 1, 2}:
   s=0: u is assigned k.
   s=1: u is assigned a type in [1, k-1] (i.e., safe for being adjacent to a k)
   s=2: u is assigned a type in [k+1, m] (i.e., not safe for being adjacent to a k: meaning that if a neighbor is k, then it would be invalid; but if no neighbor is k, then it's acceptable)

However, note: if u is in state 2, and one of its children is k, then that would be invalid because the child is k and u is not k but is >=k (so the child's neighbor u is not <k). Similarly, if u is k (state0) and a child is k, that's invalid (two adjacent k's). Also, if u is k and a child is in state2 (>=k), that's invalid because the child is not <k.

Therefore, the constraints when merging node u and a child v:
- If u is state0 (k), then v must be state1 (because v must be <k and not k).
- If u is state1, then v can be:
      - state0: then u is <k, so it's safe for v (which is k) — because u is <k? Yes, because state1 is [1,k-1]. But then, when v is k, u must be <k, which is satisfied. Also, u is not k? Yes, so no adjacent k's. So allowed.
      - state1: then both are non-k and u is <k. No constraint: allowed.
      - state2: then u is <k and v is >k. There's no constraint: because v is not k, so u can be any type? Actually, condition only imposes restrictions from k. So if v is state2 (non-k and >k), then there's no restriction on u. So allowed.
- If u is state2, then v must not be state0 (because if v is k, then u must be <k, but u is >k, which is invalid). But v can be state1 or state2? 
      - If v is state1: then v is <k, which is safe for u? But u is not k, and v is not k, so no constraint? Actually, condition: only when a node is k, then adjacent must be <k. Since neither is k, no constraint. So allowed.
      - If v is state2: also allowed.

But wait: what if u is state2 and v is state0? That's invalid: because v is k and u is state2 (which is >=k) -> violates the condition that neighbor of k must be <k.

So, the allowed combinations for (u, v) are:
   (0,1): valid
   (1,0): valid
   (1,1): valid
   (1,2): valid
   (2,1): valid
   (2,2): valid
   (0,0): invalid (adjacent k's)
   (0,2): invalid (because u is k and v is not k but >=k)
   (2,0): invalid (because v is k and u is >=k)

Therefore, the invalid pairs: (0,0), (0,2), (2,0).

Now, the number of choices for each state for a leaf node:

For a leaf u (with no children), we have:

State0: if we assign k to u. Then, we have 1 way? But also, we must have j=1. However, we have to check: the parent might be k? But at the leaf, we don't know the parent. Actually, the state is independent of the parent. We are building the state for the node without knowing the parent's state. Then, when we use the state in the parent, we enforce the constraint.

But the choices for the leaf:

   s=0: assign k -> 1 way? Actually, we are only counting the state: the type is fixed to k? Then yes, 1 way. But note: if we assign k, then the count of k is 1.

   s=1: assign a type in [1, k-1]: there are (k-1) choices.

   s=2: assign a type in [k+1, m]: there are (m-k) choices.

But wait: if k=1, then k-1=0, so state1 is 0 choices? Similarly, if m==k, then state2 has 0 choices. That's acceptable.

Now, for the DP at a node u:

We start with an initial state: j0 and s for u.

Then, we combine the children one by one.

We can initialize:
   dp[u][1][0] = 1   (if we assign k to u, then count=1, state0)
   dp[u][0][1] = k-1
   dp[u][0][2] = m-k

But note: we cannot assign k to u if we have state1 or state2? So the above is the base for the leaf.

Then, we process each child v. We need to merge the DP of u and the DP of v? Actually, we start with the base for u (without any children) and then for each child, we update the entire DP for u.

Let me denote the current DP for u (after processing some children) as dp[u][j][s]. Then we consider a child v. We have to consider the combinations of states for u and v.

But note: the constraint between u and v: they must not form an invalid pair (as above). Also, the count of k's in the subtree of u will be the current u's k count plus the k counts in the subtree of v? But wait: if u is state0, then v cannot be state0 or state2 (only state1). Similarly, if u is state1, then v can be state0,1,2? But actually, we have to consider the constraints: the pair (u,v) must be valid.

But the DP state for v: we have dp[v][j_v][t] for t in {0,1,2}. Then, we combine: the new state for u will have j = j_old + j_v (if u is state0 and v is state0, then we add, but that is invalid so we skip). But actually, we skip invalid pairs.

So, we iterate over the current state of u and the state of v and the counts.

The recurrence:

We'll create a temporary DP for u (call it temp) and then update temp from the current dp[u] and the dp[v] for each state.

For each current state s_u for u (0,1,2) and current count j_u (0<=j_u<=x), and for each state t for v (0,1,2) and count j_v (0<=j_v<=x) such that j_u + j_v <= x, and that the pair (s_u, t) is valid (not in the invalid set):

   Then, we can add: 
        temp[j_u + j_v][s_u] += dp[u][j_u][s_u] * dp[v][j_v][t]

But note: if u is state0, then v must be state1 (and only state1). Similarly, if u is state2, then v must not be state0 (so state1 or state2). If u is state1, then v can be any state? Actually, no: we have the invalid pairs: (1,0) is allowed? Actually, above we said (1,0) is allowed. But wait: if u is state1 and v is state0, then u is <k and v is k. Then, for v (which is k), u is adjacent and is <k, so that's allowed. Also, for u (which is not k), there's no constraint from v being k? Actually, the condition only imposes that if a node is k, then neighbors must be <k. Since v is k and u is <k, it's okay. So (1,0) is allowed.

But the invalid pairs are only (0,0), (0,2), (2,0). So the rest are allowed.

Therefore, the merging:

For each j_u from 0 to x, for each s_u in {0,1,2}:
   For each j_v from 0 to x, for each t in {0,1,2}:
        if j_u + j_v <= x and (s_u, t) is not in {(0,0), (0,2), (2,0)}:
            temp[j_u+j_v][s_u] += dp[u][j_u][s_u] * dp[v][j_v][t]

But note: the state of u remains the same? Yes, because we are merging the subtree of v with u, and u's state doesn't change. We are just accumulating the counts.

However, the state of v matters for the constraint with u, but once we merge, we only remember the state and count for u? Actually, no: the state of u remains, and the count for the entire subtree (u and its children) is j_u+j_v. Then we move to the next child.

But the DP for u after processing all children: we have dp[u][j][s] for the entire subtree.

After processing all children, we have the DP for u.

Finally, at the root (node 0), we sum over all states and for j from 0 to x.

Time complexity: for each edge, we do O(x * 3 * x * 3) = O(9*x^2) per edge. Since there are n-1 edges, total is O(n * x^2). With x<=10, so 9*100=900 per edge? Then total 900 * 10^5 = 90e6, which is acceptable in Python? Maybe in PyPy/C++ but in Python we might need to optimize. Actually, 90e6 operations in Python might be borderline in Pyton (in C++ it's acceptable, but in Python we might need to use PyPy or hope that it runs in Pyton in under 10 seconds? Or we can try to optimize by using only up to x (which is 11 states) and then inner loops over 3 states for u and 3 for v, and j_u and j_v from 0 to x. So for each edge, we do (x+1) * 3 * (x+1) * 3 = 11*3*11*3 = 1089 operations. Then for 10^5 edges: 1089 * 10^5 = 108.9e6, which is about 100 million. In Python, each operation is not too heavy (a multiplication and addition modulo 10^9+7). But 100 million operations in Python might run in 10-20 seconds? We might need to optimize.

But note: we are doing for each child of u: and u might have many children. So we do for each child: a triple nested loop: j_u (0..x), j_v (0..x), and states (3*3). So inner loop is (x+1) * (x+1) * 9. Since x is 10, that's 11*11*9 = 1089 per child per parent.

The total number of children over the entire tree is n-1? Actually, each edge is processed once. So the total operations is 10^5 * 1089 = 108.9e6, which is acceptable in C++ but in Python we have to hope that it runs in Pyton with PyPy or in Pyton with pypy or we need to write efficient code in Pyton (using Pyton with pypy or Pyton with PyPy). Alternatively, we can try to optimize by using a 2D temp array and updating in a smarter way.

But 100 million in Pyton might be borderline in Pyton (in Pyton, 10^8 operations per second is typical in C++ but in Python it's more like 10^6 to 10^7 per second). So 100 million might be 10 seconds? We can try to optimize the inner loop by using local variables and avoiding function calls.

But let's proceed with the DP.

Steps:

1. Build the tree from edges.
2. Root the tree at node 0 (index 0) or node 1 (if nodes are 1-indexed). We'll use 0-indexed.
3. Do a DFS (or BFS) to process the tree from leaves to root.

We'll use:

dp[u][j][s] -> we can use a 2D array: dp[u] is a 2D array of dimension (x+1) x 3.

Initialize for each leaf (in DFS, we start with a node and then when we see a child, we process the child first). Actually, we can initialize for every node:

   dp[u][1][0] = 1   # if we assign k to u
   dp[u][0][1] = k-1
   dp[u][0][2] = m - k

But note: if k==1, then state1 has 0 choices? Then dp[u][0][1]=0. Similarly, if m==k, then state2 has 0 choices: dp[u][0][2]=0.

Then, for each child v of u, we:

   - Create a temporary 2D array temp[0..x][0..2] initialized to 0.
   - For each j_u from 0 to x, for each s_u in [0,1,2]:
          if dp[u][j_u][s_u] is non-zero, then for each j_v from 0 to x, for each s_v in [0,1,2]:
                 if (s_u, s_v) is not invalid and j_u+j_v<=x:
                      j_new = j_u + j_v
                      temp[j_new][s_u] += dp[u][j_u][s_u] * dp[v][j_v][s_v]

   - Then, set dp[u] = temp.

But note: the state of u remains the same? Yes, we are accumulating the children's assignments and the state of u doesn't change. We are only updating the counts.

But also, note: the assignment for u is already set (we started with the base for u without children). Then, when we add a child, we are multiplying the ways from the child's subtree with the current ways of u. And the child's subtree is independent? Yes, but with the constraint that the edge (u,v) must be valid.

After processing all children, we have the entire subtree rooted at u.

Finally, at the root, we sum over j=0 to x and s in {0,1,2}: dp[root][j][s].

But note: the root has no parent? Actually, the state of the root is independent: we don't have a parent constraint? So all states are acceptable? But the state2 at the root: that's acceptable because the root has no parent that is k? Actually, the condition is: if the root is state2, that's acceptable because there's no parent to force a constraint. Similarly, the root being k is acceptable? Yes. So we sum all.

But wait: the states we defined are independent of the parent? Actually, the state2 is defined as "assigned a type in [k+1, m]". That's acceptable at the root because the root has no parent. Similarly, state1 is acceptable. State0 is acceptable.

Therefore, the answer is sum_{j=0}^{x} sum_{s in {0,1,2}} dp[root][j][s] mod (10^9+7).

But let's test with the examples.

Example 1: n=4, m=2, edges: (1,2), (2,3), (1,4); k=1, x=2.

We know the answer is 1.

Build tree: let root at node 0 (which is vault1).

Nodes: 0,1,2,3 (vaults 1,2,3,4)

Edges: 
   0-1, 1-2, 0-3.

We'll process:

First, we need to do DFS. Let's choose root 0.

Children of 0: node1 and node3.
Children of 1: node2.

Initialize:

For node2 (leaf):
   dp[2][1][0] = 1
   dp[2][0][1] = k-1 = 0 (because k=1)
   dp[2][0][2] = m-k = 1 (because m=2)

But wait: for state0: 1 way; state1:0; state2:1 way.

Now, for node1: 
   Initially (without children): 
        dp[1][1][0] = 1
        dp[1][0][1] = 0
        dp[1][0][2] = 1

Then, we process child node2.

We create temp for node1: a 2D array for j=0..2, s=0,1,2.

Now, for u=1 and child v=2:

We consider:
   s_u=0: then v must be state1? But state1 for v is 0 (because k-1=0). Also, state0 for v: then (0,0) is invalid. State2 for v: (0,2) is invalid. So no valid state for v when u is state0? Then we cannot have u=0? Actually, we have to skip.

   So for s_u=0: no valid v state? Then we set temp for j from ... but we have to consider: the current u state0 without children: then we cannot add any child? Then the state0 for u becomes 0? Because no way to assign the child.

   Similarly, for s_u=1: not applicable because dp[1][0][1] = 0? Actually, we have dp[1][0][1]=0, so skip.

   For s_u=2: then v can be state1 or state2? 
        state1: invalid because (2,1) is allowed? Actually, no: the invalid pairs are (0,0), (0,2), (2,0). So (2,1) is allowed. And (2,2) is allowed.
        But v has state1: 0 ways, state2: 1 way.

        So for j_u=0 (because s_u=2: j_u=0) and j_v: 
            for state1: j_v=0? But state1 has j_v=0? Actually, no: the state1 for v is j_v=0? But wait: the state of v: if v is state2, then j_v=0 (because state2 is non-k). So we can take j_v=0, state2: which is 1 way.
            then j_new = 0+0 = 0, so temp[0][2] += dp[1][0][2] * dp[2][0][2] = 1 * 1 = 1.

        Also, for state0 of v: not allowed (because (2,0) is invalid). 

So after processing child, for u=1:
   state0: 0
   state1: 0
   state2: 1 (with j=0)

But wait, initially for u=1, state2 had 1 way. Then after merging with child, state2 has 1 way? But we multiplied the state2 of u with state2 of v? And the count j: 0 (from u) + 0 (from v) = 0.

Now, for node0 (root): 
   Initialize:
        dp[0][1][0] = 1
        dp[0][0][1] = k-1 = 0
        dp[0][0][2] = m-k = 1

   Then, process children: first child1 (node1) and then child3 (node3, leaf).

Process child1 (node1): 
   We have for u=0: current states:
        state0: j=1 -> 1 way
        state1: j=0 -> 0
        state2: j=0 -> 1 way

   For child1: 
        state0: 0
        state1: 0
        state2: j=0 -> 1 way (from above)

   Now, merge:

   For u=0 state0: then child1 must be state1? But child1 state1=0. Or state0? invalid. state2: invalid (because (0,2) is invalid). So no valid state for child1 when u is state0? Then state0 becomes 0.

   For u=0 state1: 0 -> skip.

   For u=0 state2: then child1 can be state1 or state2? 
        state1: 0 ways, state2: 1 way -> valid.
        Then j_u=0, j_v=0 -> j_new=0.
        So temp[0][2] += 1 * 1 = 1.

   Then, we set dp[0] = temp: so state0=0, state1=0, state2=1 (with j=0).

Then, process child3 (node3): 
   node3 is a leaf: 
        dp[3][1][0]=1
        dp[3][0][1]=0
        dp[3][0][2]=1

   Now, for u=0: current state: 
        state0:0
        state1:0
        state2: j=0 -> 1 way

   Merge with child3:

   For u=0 state2: 
        child3: 
            state0: (2,0) is invalid -> skip.
            state1: 0
            state2: valid -> j_v=0, so j_new=0+0=0 -> add 1*1=1.

        So temp[0][2] = 1 (from current) * 1 (from child3 state2) = 1? Actually, we do:

        We have for u: state2, j=0 -> value=1.
        For child3: state2: j=0 -> value=1.
        Then, new value for state2: 1*1 = 1.

   So after child3, dp[0][0][2]=1.

Then, at root: sum j=0 to 2 and s: 
   j=0: s0=0, s1=0, s2=1 -> total 1.

Which is the expected answer.

Now, Example2: 
   Input: "3 3\n1 2\n1 3\n2 1"
   So n=3, m=3, edges: (1,2) and (1,3); k=2, x=1.

   Answer=13.

   Build tree: root at node0 (vault1). Children: node1 (vault2) and node2 (vault3).

   For leaves (node1 and node2) (vault2 and vault3):

      For a leaf:
          state0: 1 way
          state1: k-1 = 1 (because k=2) -> 1 way
          state2: m-k = 1 (because 3-2=1) -> 1 way

   So for node1 and node2: 
        dp[leaf][1][0]=1
        dp[leaf][0][1]=1
        dp[leaf][0][2]=1

   Now, root (node0): 
        Initial without children: 
            state0:1
            state1: k-1=1
            state2: m-k=1

        Then, process child1 (node1) and then child2 (node2).

   Process child1 (node1):

        Create temp for u=0.

        For u=0 state0: 
             child1: must be state1? (because (0,1) is allowed? Actually, state0 for u and state1 for child: (0,1) is allowed? 
                 But the constraint: if u is k (state0), then child must be <k and not k. So state1 is [1,k-1] -> which for k=2 is [1,1] -> so type1. That's allowed. 
                 So for child1: 
                    state0: invalid (0,0) and (0,2) are invalid? But (0,1) is allowed. 
                 Then, for state0: 
                    j_u=1, j_v: for child1: 
                         state0: j_v=1 -> j_new=1+1=2 -> but x=1, so skip.
                         state1: j_v=0 -> j_new=1+0=1 -> valid: add 1 (from u) * 1 (from child state1) = 1.
                         state2: j_v=0 -> but (0,2) is invalid -> skip.

                 So for state0: we have 1 way (with j=1).

        For u=0 state1: 
             child1 can be any state? Actually, (1,0): allowed? (1,0): u is state1 (which is type in [1,1] for k=2) and child is state0 (k=2). Then, for the child (k), u is adjacent and is 1 which is <2 -> valid. 
             Also, (1,1) and (1,2) are allowed? 
                 (1,0): allowed -> j_u=0 (for state1: no k) and j_v=1 (for state0) -> j_new=1.
                 (1,1): j_new=0+0=0
                 (1,2): j_new=0+0=0

             Then:
                 state0: j_v=1 -> 1 way: then temp[1][1] += 1*1 = 1.
                 state1: j_v=0 -> 1 way: then temp[0][1] += 1*1 = 1.
                 state2: j_v=0 -> 1 way: then temp[0][1] += 1*1 = 1.

             So for state1: we have two contributions: 
                 j=1: 1 way (from state0 of child)
                 j=0: 2 ways (from state1 and state2 of child)

        For u=0 state2: 
             child1: cannot be state0 (because (2,0) is invalid). But can be state1 and state2? 
                 state0: invalid -> skip.
                 state1: allowed -> j_v=0 -> j_new=0+0=0 -> 1*1=1.
                 state2: allowed -> j_v=0 -> 1*1=1.

             So for state2: 
                 j=0: 2 ways.

        So after child1, temp:
            state0: j=1 -> 1
            state1: j=0 -> 2, j=1 -> 1
            state2: j=0 -> 2

        But note: j for state0: we have j=1 (only one k: u is k and the child is non-k? Then total k's=1). 
        For state1: j=1: that comes from the child being k and u being non-k? Then total k's=1. j=0: no k's.
        For state2: j=0: no k's.

        Then, we set dp[0] to this temp.

   Then, process child2 (node2): same as node1? 

        Now, u=0 has a temp state from child1. We'll create a new temp2.

        For each state of u (0,1,2) and j_u from 0 to x (x=1) and for the child2 (which has the same dp as node1: state0: j=1->1 way; state1: j=0->1; state2: j=0->1).

        We iterate:

        For u state0 (j_u=1): 
            child2 must be state1? 
                then j_v=0 -> j_new=1+0=1 -> valid: so add temp[state0][1] * (child2 state1:1) = 1*1 = 1.
            child2 state0: invalid? (0,0) -> skip.
            child2 state2: invalid? (0,2) -> skip.

            So state0: j=1: 1 way.

        For u state1: 
            j_u can be 0 or 1.
            For j_u=0: 
                child2: 
                    state0: allowed (1,0) -> j_v=1 -> j_new=0+1=1 -> add: temp[state1][0] (which is 2) * child2[state0] (1) = 2*1=2.
                    state1: allowed -> j_v=0 -> j_new=0 -> 2 * child2[state1] (1) = 2.
                    state2: allowed -> j_new=0 -> 2 * 1 = 2.
            For j_u=1:
                child2:
                    state0: allowed -> j_new=1+1=2 -> skip (x=1).
                    state1: allowed -> j_new=1+0=1 -> temp[state1][1] (which is 1) * child2[state1] (1) = 1.
                    state2: allowed -> j_new=1+0=1 -> 1*1=1.
            So for state1: 
                j=0: 2 (from state1) + 2 (from state2) = 4? Actually, we break by j_u and j_v:
                    j_u=0: 
                         state0: j_new=0+1=1 -> 2 (from u state1 j=0) * 1 (child state0) = 2 -> for j=1.
                         state1: j_new=0+0=0 -> 2*1=2 -> for j=0.
                         state2: j_new=0+0=0 -> 2*1=2 -> for j=0.
                    j_u=1:
                         state0: j_new=1+1=2 -> skip.
                         state1: j_new=1+0=1 -> 1*1=1 -> for j=1.
                         state2: j_new=1+0=1 -> 1*1=1 -> for j=1.

                So for state1: 
                    j=0: 2+2 = 4? Actually, we have two contributions for j=0: from j_u=0 and child state1 and state2: 2+2=4? But no: from j_u=0 and child state1: 2 (from u) * 1 (child) = 2 for j=0? And from j_u=0 and child state2: 2 for j=0? So total j=0: 2+2=4? 
                    j=1: from j_u=0 and child state0: 2, and from j_u=1 and child state1:1, and j_u=1 and child state2:1 -> total 2+1+1=4.

        Actually, we have to do:

          For j_u and j_v:

          j_u=0 (state1): 
             with child j_v=1 (state0): j_new=0+1=1 -> add: 2 * 1 = 2 to state1, j=1.
             with child j_v=0 (state1): j_new=0+0=0 -> 2 * 1 = 2 to state1, j=0.
             with child j_v=0 (state2): j_new=0+0=0 -> 2 * 1 = 2 to state1, j=0.

          j_u=1 (state1): 
             with child j_v=1 (state0): 1+1=2 -> skip.
             with child j_v=0 (state1): 1+0=1 -> 1 * 1 = 1 to state1, j=1.
             with child j_v=0 (state2): 1+0=1 -> 1 * 1 = 1 to state1, j=1.

          So state1: 
             j=0: 2+2 = 4
             j=1: 2+1+1 = 4

        For u state2: 
            j_u=0: 
                child2: 
                   state0: invalid? (2,0) -> skip.
                   state1: allowed -> j_v=0: j_new=0 -> 2 (from u state2 j=0) * 1 = 2 -> j=0.
                   state2: allowed -> 2 * 1 = 2 -> j=0.
            So j=0: 4.

        Then, temp2:
            state0: j=1: 1
            state1: j=0:4, j=1:4
            state2: j=0:4

        Then, the root has:
            state0: 1
            state1: 4 (j0) + 4 (j1) = 8? But we store by j: so we have separate j.
            state2:4

        Then total ways: 1 (state0) + 4+4 (state1) + 4 (state2) = 1+8+4=13.

        Which matches.

So the algorithm:

   mod = 10**9+7
   Build graph (adjacency list) from edges.
   We'll do a DFS (recursive or iterative) to avoid recursion depth issues? n=10^5, so recursion might be deep. We can use iterative DFS (stack) or BFS.

   Steps:
      graph = [[] for _ in range(n)]
      for each edge (u,v): 
          graph[u-1].append(v-1)
          graph[v-1].append(u-1)

      We need a parent pointer to avoid going back.

      We'll use a stack for DFS: 
          stack = [0]  (root at 0)
          parent = [-1]*n
          order = []
          while stack:
             u = stack.pop()
             order.append(u)
             for v in graph[u]:
                 if v == parent[u]: continue
                 parent[v] = u
                 stack.append(v)

          Then, process nodes in reverse order (from leaves to root).

      Alternatively, we can do:
          order.reverse()
          for u in order:
             # initialize dp[u] as a 2D array: (x+1) x 3
             dp_u = [[0]*3 for _ in range(x+1)]
             dp_u[1][0] = 1   # state0: k
             dp_u[0][1] = k-1  # state1: [1, k-1]
             dp_u[0][2] = m - k # state2: [k+1, m]

             for each child v of u (excluding parent):
                 # We have a dp for v: dp_v (a list of 3 states for each j in 0..x? Actually, we stored a 2D: j from 0 to x, and 3 states.

                 # Create a temporary array for the updated dp_u: temp[j][s] for j in 0..x, s in 0..2, initialized to 0.
                 temp = [[0]*3 for _ in range(x+1)]

                 # For the current u state: j_u from 0 to x, s_u in 0..2
                 for j_u in range(x+1):
                     for s_u in range(3):
                         if dp_u[j_u][s_u] == 0: 
                             continue
                         # For the child v: j_v from 0 to x, s_v in 0..2
                         for j_v in range(x+1 - j_u):   # j_v from 0 to min(x - j_u, x) -> but we can do 0 to x
                             for s_v in range(3):
                                 if j_u + j_v > x:
                                     continue
                                 # Check if (s_u, s_v) is invalid?
                                 if (s_u == 0 and s_v == 0) or (s_u == 0 and s_v == 2) or (s_u == 2 and s_v == 0):
                                     continue
                                 # Otherwise, valid
                                 # Multiply: dp_u[j_u][s_u] * dp_v[j_v][s_v]
                                 # and add to temp[j_u+j_v][s_u]
                                 temp[j_u+j_v][s_u] = (temp[j_u+j_v][s_u] + dp_u[j_u][s_u] * dp_v[j_v][s_v]) % mod

                 # After processing child v, set dp_u = temp
                 dp_u = temp

             # After processing all children, set dp[u] = dp_u

      Then, at root: 
          total = 0
          for j in range(x+1):
              for s in range(3):
                  total = (total + dp_root[j][s]) % mod
          return total

But note: we are storing dp_u for the current u. Then, we process the next child? 

But we have multiple children: so we have to iterate over each child and update the entire dp_u.

However, we must initialize dp_u for u (without any children) and then for each child, we update.

But the above code for the child loop: we start with the base dp_u (without children) and then for the first child, we create a new temp and then set dp_u = temp. Then for the next child, we use the updated dp_u and the next child's dp_v to compute a new temp.

That's the standard tree DP.

But the inner loops: j_u from 0 to x, s_u in 0..2, j_v from 0 to x, s_v in 0..2: so (x+1)*3*(x+1)*3 = 9*(x+1)^2 per child per parent.

Total over the tree: the sum over all nodes of (number of children) * 9*(x+1)^2 = 9*(x+1)^2 * (n-1). Because each edge is a child of one node.

With x<=10, (x+1)=11, then 9*121=1089 per edge. Then total 1089 * (n-1) ~ 1089 * 10^5 = 108.9e6.

In Python, we hope that the inner loop is optimized and the modulo operation is cheap.

But we can try to optimize by:

   - Precomputing the invalid pairs: we can skip the inner loops for invalid pairs by breaking early.

   - Also, we can iterate j_v only when dp_v[j_v][s_v] is non-zero.

   - Similarly, for j_u: only when dp_u[j_u][s_u] is non-zero.

   - We can also use local variables to avoid repeated array lookups.

But 100e6 operations in Python might be acceptable in Pyton if we use PyPy or Pyton with pypy, or in Pyton in C++? But we have to write in Python. However, 100e6 might be acceptable in Pyton if we run in Pyton with PyPy or in Pyton in C++? Actually, in Pyton, 10^8 operations might take 10 seconds? So 100e6 might take 1 second? So it should be acceptable.

But let me test with the constraints: worst-case 10^5 * 11 * 3 * 11 * 3 = 10^5 * 1089 = 108.9e6.

In Python, we can run 10^7 operations per second? Then 108.9e6 would be 10 seconds? That might be borderline in Pyton. We need to optimize the inner loops.

We can change the order: iterate j_u and s_u, and then for s_v, we iterate j_v. Then we can skip invalid pairs by breaking early.

Alternatively, we can precompute for each s_u, the allowed s_v.

For s_u=0: only s_v=1 is allowed? 
   Actually: invalid: (0,0) and (0,2) -> so only (0,1) is allowed.

For s_u=1: allowed s_v: 0,1,2? 
   But (1,0): allowed? yes, (1,1): allowed, (1,2): allowed.

For s_u=2: allowed s_v: 1 and 2? (because (2,0) is invalid).

So we can precompute:

   allowed = {
        0: [1],
        1: [0,1,2],
        2: [1,2]
   }

Then, for a fixed s_u, we only iterate over the allowed s_v.

This reduces the inner s_v loop from 3 to 1 for s_u=0, and 3 for s_u=1, and 2 for s_u=2.

Average: (1+3+2)/3 = 2.

So then the inner loop becomes: (x+1) * (x+1) * (average s_v per s_u) = 11 * 11 * 2 = 242 per (j_u, s_u). Then per (j_u, s_u): 242 operations.

Total per child: for j_u from 0 to x (11 states) and s_u (3 states): 11*3*242 = 7986 per child? Then for 10^5 edges: 7986 * 10^5 = 798.6e6, which is worse? 

Wait, no: the outer loop is j_u and s_u, and then for each j_v and s_v in the allowed list. The inner loop is over j_v and the allowed s_v. The total operations per child is: 
   for j_u: 0..x (x+1 iterations)
   for s_u: 3 iterations
   for each allowed s_v: |allowed[s_u]| 
   for j_v: 0..x (x+1 iterations)

So total: (x+1) * (number of s_u) * (|allowed[s_u]|) * (x+1) = (x+1)^2 * (number of s_u) * (|allowed[s_u]|) / (we are iterating per s_u and then per allowed s_v and per j_v).

Actually, the inner loop is over j_v and the allowed s_v for the current s_u.

But we can break the loops: 

   for j_u in range(x+1):
      for s_u in range(3):
          ways_u = dp_u[j_u][s_u]
          if ways_u == 0: skip
          for s_v in allowed[s_u]:
              for j_v in range(x+1 - j_u):   # j_v from 0 to x, but we break if j_u+j_v>x?
                  ways_v = dp_v[j_v][s_v]
                  if ways_v == 0: continue
                  j_new = j_u + j_v
                  if j_new <= x:
                      temp[j_new][s_u] = (temp[j_new][s_u] + ways_u * ways_v) % mod

But the inner loop is over j_v: 0 to x (x+1 iterations). So the total operations per child: 
   (x+1) * 3 * (|allowed[s_u]|) * (x+1) = 3 * (x+1)^2 * (average |allowed|) 
   = 3 * 121 * ( (1+3+2)/3 ) = 3 * 121 * 2 = 726 per child? 

But wait: the average |allowed| is 2, so 3 * (x+1)^2 * 2 = 3*121*2=726 per child? 

But we have n-1 edges (children) -> total 726 * (n-1) = 726 * 10^5 = 72.6e6.

That's better: 72.6e6 operations.

But we can do better: we can iterate j_v only from 0 to min(x - j_u, x). But we are already iterating j_v from 0 to x, and then check j_new<=x? Actually, we can break early: but j_v goes from 0 to x, so it's fixed.

Alternatively, we can precompute for the child v: an array for each s_v, the list of j_v that are non-zero? But since x is small, we can just iterate j_v from 0 to x.

So 72.6e6 operations: that should be acceptable in Pyton (about 0.7 seconds in C++ and about 7 seconds in Pyton? But we are doing modulo multiplication and addition).

We can try to optimize by using a local variable for the inner loops.

But 72e6 is acceptable in Pyton? We hope so.

Edge: Example 3: 
   Input: "3 1\n1 2\n1 3\n1 1"
   n=3, m=1, edges: (1,2), (1,3); k=1, x=1.

   Then, for a leaf: 
        state0: 1
        state1: k-1 = 0
        state2: m-k = 0

   So each leaf: only state0: 1 way (with j=1) and state1=0, state2=0.

   Then, root: 
        Initial: 
            state0:1, state1:0, state2:0.

        Then, for the first child (leaf): 
            For u state0: 
                allowed s_v: only s_v=1? but the child has no state1? only state0: which is invalid? 
                So no valid state for the child? Then state0 becomes 0.
            For state1:0 -> skip.
            For state2:0 -> skip.

            Then, after first child: temp: all zeros.

        Then, for the second child: same -> remains 0.

        So total 0.

   Output 0 -> correct.

So we write the code accordingly.

We have to be cautious: when k-1 is negative? For k=1, k-1=0 -> then state1 has 0 ways. Similarly, m-k: if m<k, then negative? But m>=k per constraints: k is between 1 and m. So m>=k, so m-k>=0.

But if k=1, then state1: k-1=0. Then we set 0.

Similarly, if m==k, then state2: 0.

So the initialization is safe.

Now, code:

Steps:

   mod = 10**9+7
   n, m = map(int, input().split())
   graph = [[] for _ in range(n)]
   for _ in range(n-1):
        u, v = map(int, input().split())
        u -= 1
        v -= 1
        graph[u].append(v)
        graph[v].append(u)
   k, x_val = map(int, input().split())   # x_val is x

   # Build tree: 
   parent = [-1] * n
   stack = [0]
   parent[0] = -1
   order = []
   while stack:
        u = stack.pop()
        order.append(u)
        for v in graph[u]:
            if v == parent[u]:
                continue
            parent[v] = u
            stack.append(v)

   # Reverse the order: from leaves to root
   order.reverse()

   # dp[i] for node i: a 2D list: [x_val+1][3] -> dp[i][j][s] for j in 0..x_val, s in 0..2.
   # We'll create a list for each node: we only need to store for the current node.

   # Precompute allowed: for each s_u, the allowed s_v.
   allowed = [[1], [0,1,2], [1,2]]   # for s_u=0,1,2

   # Initialize for each node: we'll create a 2D array: (x_val+1) x 3, initialized to 0.
   dp = [[0]*3 for _ in range(x_val+1)]   # for the current node, we'll use this and then update.

   # But we need to store per node: we'll create an array for each node? Actually, we process one node at a time.

   # Instead, we create a list `res` for dp for each node? Or we store in an array `dp_node` of size n? 
   # Actually, we only need the dp for the children when processing the parent. We can store the dp for each node in an array? But we are processing in reverse order, and we don't need the parent's dp for children? So we can compute and then discard.

   # We'll create an array `node_dp` of size n, each is a list of (x_val+1) lists of 3 integers.

   # Alternatively, we can store for each node: a list of (x_val+1) * 3. We'll do:

   node_dp = [[[0]*3 for _ in range(x_val+1)] for __ in range(n)]   # This is 10^5 * 11 * 3 = 3.3e6 integers? That's acceptable.

   # But we can also compute on the fly and store only the current node? Actually, we need to store the children's dp.

   # Instead, we can do: 
        # For each node u (in reverse order), we initialize its dp.
        # Then, for each child, we update the dp of u by merging the child's dp (which we computed and stored).

   # We'll create an array `dp_arr` for each node: we can store in a list `dp_arr[i]` for node i.

   # Initialize for each node: 
        # dp_arr[u][1][0] = 1
        # dp_arr[u][0][1] = k-1
        # dp_arr[u][0][2] = m-k

   # But then, for leaves, we do nothing (no children) so they remain.

   # Then for a non-leaf, we update.

   # Actually, we can initialize the dp for u as above, and then for each child, we update.

   # We'll create a 2D array for the current node u: 
        # cur = [[0]*3 for _ in range(x_val+1)]
        # cur[1][0] = 1
        # cur[0][1] = k-1
        # cur[0][2] = m-k

   # Then, for each child v (we have the dp_arr for v: which is a 2D array: dp_v = dp_arr[v])

   # Then, we create a new_cur = [[0]*3 for _ in range(x_val+1)]

   # Then, we iterate j_u from 0 to x_val, s_u from 0 to 2, and then for each allowed s_v, and j_v from 0 to x_val, and update new_cur[j_u+j_v][s_u] += cur[j_u][s_u] * dp_v[j_v][s_v]   (if j_u+j_v<=x_val)

   # Then, set cur = new_cur.

   # Then, after all children, set dp_arr[u] = cur.

   # Then, at root: sum_{j=0}^{x_val} sum_{s=0}^{2} dp_arr[0][j][s] mod mod.

   # But we need to store dp_arr for each node? The memory: 10^5 * 11 * 3 = 3.3e6 integers, which is about 26.4 MB? (each integer 8 bytes) -> 3.3e6 * 8 = 26.4 MB? That's acceptable.

   # Steps:

        dp_arr = [[[0]*3 for _ in range(x_val+1)] for _ in range(n)]

        for u in order:   # from leaves to root
            # Initialize the current node u without children
            for j in range(x_val+1):
                for s in range(3):
                    dp_arr[u][j][s] = 0
            dp_arr[u][1][0] = 1
            dp_arr[u][0][1] = (k-1) % mod
            dp_arr[u][0][2] = (m-k) % mod

            # Then, for each child v of u (excluding parent)
            children = [v for v in graph[u] if v != parent[u]]
            for v in children:
                # We'll create a new_dp for u: new_dp[j][s] = 0 for all j,s
                new_dp = [[0]*3 for _ in range(x_val+1)]
                # Now, merge: for the current state of u (dp_arr[u]) and the child v (dp_arr[v])
                for j_u in range(x_val+1):
                    for s_u in range(3):
                        ways_u = dp_arr[u][j_u][s_u]
                        if ways_u == 0:
                            continue
                        # For each allowed s_v for s_u
                        for s_v in allowed[s_u]:
                            for j_v in range(x_val+1):
                                if j_u + j_v > x_val:
                                    continue
                                ways_v = dp_arr[v][j_v][s_v]
                                if ways_v == 0:
                                    continue


## Final Solution

