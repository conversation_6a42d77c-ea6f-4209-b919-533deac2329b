# Solution for abc308_b.md

Generated at: 2025-07-22 06:13:48
Model: deepseek-reasoner

## Reasoning Process

We are given:
  N: number of plates
  M: number of distinct colors that have specific prices (non-default)
  C: list of N strings, the color of each plate
  D: list of M strings, the colors that have specific prices
  P: list of M+1 integers: P[0] is the default price, and then P[1] to P[M] are the prices for the colors in D[0] to D[M-1]

For each plate color in C:
  If the color is found in D, then we use the corresponding price from P (specifically, the price is P[j+1] for the color D[j]).
  If the color is not found in D, we use the default price P[0].

Constraints: N, M up to 100 -> so even O(N*M) is acceptable (100*100 = 10000 operations, which is fine).

Approaches:

1. Brute Force:
   For each plate color in C:
      Check sequentially in D to see if it exists. If found, use the corresponding price (which is P[index_in_D+1]). If not found, use P[0].

   Time Complexity: O(N*M) per test case.

2. Using a dictionary (hash map) for the specific prices:
   We can create a mapping from color string to price for the M specific colors. Then for each plate color, we can check the dictionary: 
        if the color is in the dictionary, use the value from the dictionary; 
        else, use P[0].

   Steps:
        Create a dictionary: 
            mapping = {}
            for i in range(M):
                mapping[D[i]] = P[i+1]   # because P[0] is default, then P[1] is for D[0], P[2] for D[1], etc.

        Then, for each color in C:
            total += mapping.get(color, P[0])

   This is O(N+M) because building the dictionary is O(M) and then iterating over C is O(N), and each dictionary lookup is O(1) on average.

Given that M and N are at most 100, both the brute force and dictionary are acceptable. However, the dictionary approach is more efficient in terms of time complexity for larger inputs (even though here constraints are small) and is cleaner.

Edge Cases:
  - A color in C might be repeated? The problem doesn't say distinct, so yes, we have to account for repeated colors.
  - The list D has distinct colors, so no duplicates.

Example 1:
  Input: 
      3 2
      red green blue
      blue red
      800 1600 2800

  Here, P[0] = 800, P[1] = 1600 (for blue? but note: the next line is D: "blue red", meaning D[0] = blue, D[1] = red.
  Then the prices: 
      blue -> P[1] = 1600? But wait, the example says blue plate is 1600, red plate is 2800, and green (default) is 800.

  Actually, the example says: 
      blue plate: P1 = 1600 -> but that corresponds to the first element in D? 
      However, the example output: 2800 (for red) + 800 (for green) + 1600 (for blue) = 5200.

  The input for P: "800 1600 2800" -> 
      P0 = 800
      P1 = 1600 (for the first color in D: blue)
      P2 = 2800 (for the second color in D: red)

  So: 
      blue -> P1 -> 1600
      red -> P2 -> 2800
      green -> not in D -> 800

  But wait, the example says: 
      blue: 1600, red: 2800, green: 800 -> total 1600+2800+800 = 5200.

  However, the example output is 5200.

  But note: the example says: "blue plate, red plate, and green plate" and then lists: 
      blue: 1600 (P1), red: 2800 (P2), green: 800 (P0) -> then the total is 1600+2800+800 = 5200.

  But wait, the example says: "the total amount of the prices of the sushi that he ate is 2800 + 800 + 1600 = 5200" -> so the order of plates doesn't matter.

Example 2:
  Input:
      3 2
      code queen atcoder
      king queen
      10 1 1

  So:
      D = ["king", "queen"]
      P0 = 10, P1 = 1 (for king), P2 = 1 (for queen)

  Plates: 
      "code" -> not in D -> 10
      "queen" -> in D (at index 1) -> so we use P2 = 1
      "atcoder" -> not in D -> 10

  Total: 10 + 1 + 10 = 21.

Implementation:

  We'll use the dictionary approach.

  Steps:
      total = 0
      price_map = {}
      for j in range(M):
          color = D[j]
          price = P[j+1]   # because P[0] is the default, and P[1..M] correspond to D[0..M-1]
          price_map[color] = price

      for i in range(N):
          plate_color = C[i]
          if plate_color in price_map:
              total += price_map[plate_color]
          else:
              total += P[0]

  Alternatively, we can use:
        total += price_map.get(plate_color, P[0])

Given the constraints, we can also do without a dictionary by using a list and linear search (brute force). But the dictionary is more efficient and clean.

Let's code accordingly.

Note: The input is given as:
  First line: "N M"
  Second line: the N colors for the plates
  Third line: the M colors in D
  Fourth line: M+1 integers: P0, P1, ... PM

But in the function signature, we have:
      solve(self, N: int, M: int, C: List[str], D: List[str], P: List[int]) -> int

So we are given the lists.

Therefore, we can implement:

  def solve(self, N, M, C, D, P):
      # Create a mapping from D's colors to their prices
      color_to_price = {}
      for i in range(M):
          color = D[i]
          # The price for this color is at P[i+1] because P[0] is the default
          color_to_price[color] = P[i+1]
      
      total = 0
      for color in C:
          if color in color_to_price:
              total += color_to_price[color]
          else:
              total += P[0]
      
      return total

Alternatively, we can use:

      total = 0
      for color in C:
          total += color_to_price.get(color, P[0])

This is the same.

Let me test with the examples:

Example 1:
  C = ["red", "green", "blue"]
  D = ["blue", "red"]
  P = [800, 1600, 2800]

  color_to_price:
      "blue" -> P[1] = 1600
      "red"  -> P[2] = 2800   # wait, note: i=0 -> D[0]="blue" -> P[1]=1600; i=1 -> D[1]="red" -> P[2]=2800.

  Then:
      "red": 2800
      "green": not found -> 800
      "blue": 1600
      total = 2800+800+1600 = 5200 -> matches.

Example 2:
  C = ["code", "queen", "atcoder"]
  D = ["king", "queen"]
  P = [10, 1, 1]

  color_to_price:
      "king" -> P[1] = 1
      "queen" -> P[2] = 1

  Then:
      "code": not found -> 10
      "queen": found -> 1
      "atcoder": not found -> 10
      total = 10+1+10 = 21.

So it works.

Since the constraints are small, we don't need to worry about performance. The dictionary approach is O(N+M) which is optimal.

We'll implement accordingly.

Let's write the code.

## Final Solution

### Step 1: Problem Understanding and Modeling
The problem involves calculating the total price of sushi plates based on their colors. Each plate's price is determined by:
- A specific price if its color matches one of the predefined colors in list D
- A default price if its color is not found in D

**Key Points:**
- **Input Structure:** 
  - First line: N (number of plates) and M (number of special colors)
  - Second line: List C of N plate colors
  - Third line: List D of M distinct special colors
  - Fourth line: List P of M+1 prices (P[0] is default price, P[1..M] are prices for D[0..M-1])
- **Constraints:** 
  - N, M ≤ 100, so even O(N*M) is efficient enough
  - Strings are lowercase English letters (1-20 characters)
- **Edge Cases:**
  - Plate colors not in D should use default price P[0]
  - Repeated plate colors in C are handled individually
  - D contains distinct colors, so no duplicates

### Step 2: Multiple Solution Exploration

1. **Brute Force Approach:**
   - **Idea:** For each plate color in C, linearly search through D to find a match. If found, use corresponding price from P; otherwise, use P[0].
   - **Time Complexity:** O(N*M) - For each of N plates, search through M colors in D.
   - **Space Complexity:** O(1) - No extra space beyond input.
   - **Applicability:** Simple and sufficient for small constraints (N, M ≤ 100).

2. **Optimized Dictionary Approach:**
   - **Idea:** Pre-store special color-price mappings in a dictionary. For each plate color, use dictionary lookup to get price or default if not found.
   - **Time Complexity:** O(N + M) - O(M) to build dictionary, O(N) for lookups (each O(1) average).
   - **Space Complexity:** O(M) - Store M key-value pairs.
   - **Advantage:** Faster lookups, especially beneficial if constraints were larger.

3. **Space-Optimized Approach:**
   - **Idea:** Since N and M are small (≤100), space is not a concern. The dictionary approach already optimizes time without significant space overhead.

4. **Special Technique (List Indexing):**
   - **Idea:** Not applicable here as direct color matching doesn't leverage mathematical properties.

### Step 3: Optimal Solution Selection and Implementation
**Selection Rationale:** The dictionary approach offers optimal time complexity (O(N+M)) and is clean to implement. Given the constraints, brute force (O(N*M)) is also acceptable, but dictionary is more efficient and scalable.

**Implementation:**
```python
from typing import List

class Solution:
    def solve(self, N: int, M: int, C: List[str], D: List[str], P: List[int]) -> int:
        # Create a dictionary mapping special colors to their prices
        color_to_price = {}
        for i in range(M):
            color_to_price[D[i]] = P[i + 1]  # P[0] is default, specials start at P[1]
        
        total = 0
        for color in C:
            # Use dictionary lookup: if color not found, default to P[0]
            total += color_to_price.get(color, P[0])
        return total
```

**Key Optimization Points:**
- **Dictionary Lookups:** O(1) average time per plate color, making overall time O(N+M).
- **Simplicity:** Direct mapping and use of `get` method for clean default handling.

### Step 4: Verification and Summary
**Verification with Examples:**
- **Example 1:** 
  - C = ["red", "green", "blue"], D = ["blue", "red"], P = [800, 1600, 2800]
  - Mapping: "blue"→1600, "red"→2800. 
  - red: 2800, green: 800 (default), blue: 1600 → Total = 2800 + 800 + 1600 = 5200.
- **Example 2:**
  - C = ["code", "queen", "atcoder"], D = ["king", "queen"], P = [10, 1, 1]
  - Mapping: "king"→1, "queen"→1.
  - code:10, queen:1, atcoder:10 → Total = 10 + 1 + 10 = 21.

**Summary:**
- **Core Insight:** Efficient lookups using dictionaries optimize time complexity from O(N*M) to O(N+M).
- **Edge Handling:** Default price is cleanly managed via `dict.get()`.
- **Further Optimization:** For larger constraints, this approach remains optimal. If strings were very long, hashing might add overhead, but here strings are short (≤20 chars).

This solution efficiently handles all constraints and edge cases with optimal time and space usage.