# Solution for abc305_e.md

Generated at: 2025-07-22 06:22:44
Model: deepseek-reasoner

## Reasoning Process

Alright, I'm going to tackle this problem step by step. Let me start by understanding what the problem is asking.

We have an undirected graph with N vertices and M edges. There are K guards placed on distinct vertices. Each guard i is at vertex p_i and has a stamina h_i. A vertex v is guarded if there's at least one guard such that the distance from v to that guard's vertex (p_i) is at most h_i. The goal is to list all guarded vertices in ascending order.

Constraints are quite large: N up to 200,000, M up to 200,000, and K up to N. So, we need an efficient solution.

First, I think about the brute force approach. For each vertex, we could check all guards to see if the distance from the vertex to the guard's position is <= the guard's stamina. But calculating the distance for each vertex-guard pair would be too slow. Since there are N vertices and K guards, and K can be up to 200,000, and each distance calculation might require a BFS or Dijkstra (if unweighted, BFS is O(N+M)), the total time would be O(K*(N+M)) which is 200,000 * 400,000 operations (since M can be 200,000, N 200,000, so N+M is 400,000). That's 80e9 operations, which is way too slow.

So, brute force isn't feasible. We need a better approach.

Another idea: Instead of checking from each vertex, we can think from the guards' perspective. Each guard i can cover all vertices within distance h_i from p_i. So, the problem reduces to finding the union of all vertices within distance h_i from each guard's p_i.

But how to compute this union efficiently? We can run a multi-source BFS or Dijkstra where we start from all guard positions with initial distances set to 0, but then we also have the stamina constraint. Actually, we can model it as: for each guard, we can mark vertices that are within h_i distance from p_i. Then, the guarded vertices are the union of these.

But if we run BFS for each guard individually, it's again O(K*(N+M)) which is too slow.

We need a way to combine these guards' coverage without processing each separately. 

I recall that in unweighted graphs, we can use multi-source BFS. But each guard has a different starting point and a different limit (h_i). So, it's not straightforward.

Alternative idea: We can use the concept of "guards with extra stamina" and propagate their coverage. 

Consider this: each guard at p_i with stamina h_i means that from p_i, we can cover up to h_i steps. So, we can think of it as having an initial stamina at p_i, and as we traverse edges, we decrease the stamina by 1. If we reach a vertex with a higher remaining stamina than we've seen before, then we can update that vertex's max stamina and continue.

This sounds like a Dijkstra-like approach. We can set up an array, say max_stamina, for each vertex, which records the maximum remaining stamina that can reach that vertex from any guard. Initially, for each guard at p_i, max_stamina[p_i] = h_i. Then, we can use a priority queue (or a BFS with a queue that prioritizes higher remaining stamina) to propagate.

Why higher remaining stamina? Because if we have two paths to the same vertex, we care about the one that has more stamina left so it can cover more vertices further away.

So, the steps would be:

1. Create an array `residual` of length N+1, initialized to -1 (or negative infinity) because stamina is non-negative. This will store the highest residual stamina (remaining stamina) that can be present at each vertex.

2. Create a max-heap (or priority queue that pops the maximum residual stamina). For each guard (p_i, h_i), we set residual[p_i] = h_i, and we push (h_i, p_i) into the priority queue.

But note: since we are propagating and we want to update neighbors only when we can achieve a higher residual stamina, we can do:

   While the priority queue is not empty:
      Pop the vertex u with current residual stamina r.
      If r is less than the current residual[u], skip (since we have a better residual already for u).
      For each neighbor v of u:
          The residual stamina when moving to v would be r - 1 (because each edge costs 1 stamina).
          If r - 1 is greater than the current residual[v], then set residual[v] = r - 1 and push (r-1, v) into the priority queue.

But wait, if we use a max-heap, we can use negative values to simulate a max-heap in Python's heapq which is a min-heap. Alternatively, we can push (-r, u) but then we have to invert. Actually, we can use a list and heapq for max-heap by storing (-value), but since we want the highest residual to propagate first, we can store (-r) and then pop the smallest negative, which is the largest r.

Alternatively, we can use a list and always pop the maximum. But in Python, the heapq is a min-heap. So, to simulate max-heap for residuals, we can push (-r) and then when popping, we take the smallest negative (which is the largest positive). But then we have to manage that.

Actually, we can use a heap that stores tuples (residual, node). Since we want to process higher residuals first (because they can cover more neighbors), we can push negative residuals? Or just use a max-heap by pushing (-residual) and then take the smallest? 

Standard way: we store (-residual, node) because heapq in Python is min-heap. Then, the highest residual (say 10) becomes -10, and when we pop, the smallest -residual (which is -10) corresponds to the highest residual 10.

So:

   Initialize:
      residual = [-1] * (N+1)   # 1-indexed, index 0 unused.
      heap = []

      For each guard (p, h):
          residual[p] = h
          heapq.heappush(heap, (-h, p))

   While heap is not empty:
        neg_r, u = heapq.heappop(heap)
        r = -neg_r
        If r is less than residual[u]: skip   # because we might have updated to a higher residual already

        Actually, we want to process only if the popped residual is still the current best? But we update residual when we find a better one. So, if residual[u] > r? Then we skip. But note: we push when we get a better residual. So, when we pop, it's possible that we have multiple entries for the same node. We can check if the current residual[u] is greater than r? Then skip. Because we pushed a better one.

        Actually, we set residual[u] = h initially, and then when we push, we set residual and push. So, when we pop, if r < residual[u], that means we have a better residual already, so we skip.

        Then, for each neighbor v of u:
            new_r = r - 1
            If new_r < 0, then we can't cover v, so skip.
            If new_r > residual[v]:
                residual[v] = new_r
                heapq.heappush(heap, (-new_r, v))

But note: we are updating residual[v] only if we found a better (higher) residual. And we push that state.

After this, any vertex that has residual >= 0 is covered? Actually, the residual array: if residual[v] >=0, then it was reached by a guard with at least 0 stamina remaining, meaning that the distance from some guard is <= the guard's initial stamina.

But wait: if a guard has stamina h, and we traverse d edges, then the residual is h - d. So, if h - d >=0, then d <= h, which is exactly the condition for being guarded.

So, at the end, we can collect all vertices v for which residual[v] >=0.

But note: what if a vertex is not reached? Then residual[v] remains -1, which is <0, so not guarded.

This algorithm is similar to a multi-source Dijkstra where the "distance" is the negative of the residual stamina. Actually, we are propagating the maximum residual stamina.

Time complexity: Each edge is processed only when we get a higher residual. But how many times per edge? Since residual stamina is decreasing and non-negative, and we update only when we get a higher residual, each vertex can be updated at most h_max times? But h_max is at most N (200,000) and each vertex might have many updates? Actually, the residual for a vertex is non-decreasing (we update only when we get a higher value). Since the residual stamina is an integer from -1 to h_i (and h_i<=N), each vertex can be updated at most O(N) times? That would be too slow (N*N = 4e10).

But wait, the residual for a vertex is the maximum residual that can reach that vertex. Since we are updating only when we get a higher residual, and the residual values are bounded (0 to N), each vertex can be updated at most O(N) times? Then total updates would be O(N*N) which is 40e9 for N=200000, which is too slow.

We need a better approach.

Actually, we note that the residual for a vertex, once set to a value, the next update must be with a strictly larger residual. And the residual stamina is an integer in [0, h_max]. Since we start from guards and then decrease by 1 at each edge, the residual stamina at a vertex v is the maximum over all guards of (h_i - dist(p_i, v)). 

Therefore, the residual for a vertex can be updated at most once per guard? But no, because we are propagating and we might get a better residual from a different guard. But the maximum residual at v is fixed: it's max_{i} { h_i - dist(p_i, v) }.

So, the value for each vertex is fixed. But we are updating via propagation. However, the propagation algorithm I described might update a vertex multiple times? But we are updating only when we get a higher residual. Since the maximum residual for a vertex is fixed, each vertex is updated at most once? Actually, no, because we might get a residual from a guard that gives a small residual first, then later a higher residual from another guard.

But we are updating only when we get a higher residual. So, the number of updates per vertex is at most the number of distinct residuals that are increasing? But the residual is increasing? Actually, the residual for a vertex is set to the maximum residual we've seen. Since we start at -1, then we set to some value, then we set to a higher one, and so on. The residual can only increase. And since the residual values are integers from -1 to at most max(h_i) (which is <=N), the number of updates per vertex is at most O(N). That's too many.

But note: the residual stamina that we propagate is decreasing as we move away from the guard. However, the update for a vertex v is determined by the maximum of h_i - dist(p_i, v) over all guards. So, we don't need to update multiple times per guard per vertex. 

Wait, we can compute the residual for each vertex as the maximum over all guards of (h_i - dist(p_i, v)). Then, the propagation algorithm I described is actually a way to compute this maximum. But the problem is that the same guard can lead to multiple updates? Actually, no. Each guard's propagation is independent? But we are combining them.

Actually, the algorithm I described is a well-known variant of Dijkstra for multi-source with different initial values. It's used in problems like "escape from the maze" or "guards covering". The key is that we start with all guards in the heap, and then we propagate the residual. The condition is that we update a neighbor only if we get a higher residual than what was previously achieved. 

The total number of updates: each edge is relaxed at most a few times? Actually, the algorithm is efficient because each vertex is processed at most once? 

Wait, no. Consider: if we have two guards, one gives a residual of 10 to a vertex, then we push its neighbors. Then a different guard gives a residual of 15 to the same vertex, then we update and push again. So, the same vertex is processed twice. But the total number of times a vertex is updated is the number of distinct residuals that are increasing? Actually, since the residual we set is the maximum, and we update only when we get a higher residual, the number of updates per vertex is bounded by the number of distinct residuals that are increasing? But the residual can only increase. And the residual value is bounded by the maximum h_i (<=N) and the minimum is -1. So, each vertex can be updated at most O(N) times? Then worst-case total operations would be O(N*M) which is 200,000 * 200,000 = 40e9, which is too slow for Python.

But wait, is there a better way? 

Alternatively, we can note that the condition for a vertex v to be guarded is: there exists a guard i such that dist(p_i, v) <= h_i.

We can rephrase: dist(p_i, v) <= h_i  =>  dist(p_i, v) - h_i <= 0.

But we want to know for each v, the minimum over i of (dist(p_i, v) - h_i) is <= 0.

But note: min_i (dist(p_i, v) - h_i) <=0  <=> there exists an i such that dist(p_i, v) - h_i <=0.

So, we can define for each vertex v: d(v) = min_i ( dist(p_i, v) - h_i ). Then v is guarded if d(v) <=0.

Now, how to compute d(v) for all v?

This is equivalent to having multiple sources (each guard at p_i with initial value -h_i) and then the "distance" from the set of sources is defined as min_i { dist(p_i, v) - h_i }.

But note: the usual multi-source Dijkstra adds the initial value. Actually, we can run a Dijkstra that starts with all guards, and the initial distance for guard i is set to -h_i? Then when we traverse an edge, we add 1 to the distance? Then the value at v would be min_i { -h_i + dist(p_i, v) }.

Yes: because we start at p_i with value = -h_i, and then each edge adds 1. Then at v, the value is -h_i + dist(p_i, v). Then d(v) = min_i ( -h_i + dist(p_i, v) )? But that's the negative of what we want. Actually, we defined d(v) as min_i (dist(p_i, v) - h_i) = min_i ( - (h_i - dist(p_i, v)) ) = - max_i (h_i - dist(p_i, v)). 

Wait, that's not the same. We want d(v) = min_i ( dist(p_i, v) - h_i ). 

But if we define an initial value for guard i: let it be -h_i. Then when we traverse an edge from u to v, we set the new distance to the distance at u plus 1. Then at v, the value for guard i is (-h_i) + dist(p_i, v). Which is dist(p_i, v) - h_i. Then d(v) = min_i ( dist(p_i, v) - h_i ) = min_i ( value at v from guard i). 

So, we can run a multi-source Dijkstra (with initial values) to compute d(v) for each v.

The steps:

1. Create an array d of size N+1, initialize to a big number (or infinity).
2. Create a min-heap for Dijkstra: (current_d, node)
3. For each guard (p_i, h_i):
        initial_d = - h_i   # because d(p_i) = dist(p_i, p_i) - h_i = 0 - h_i = -h_i? Actually, at p_i, dist=0, so 0 - h_i = -h_i.
        So, set d[p_i] = min(d[p_i], -h_i)   [but if multiple guards at same vertex? But the problem says distinct p_i, so each vertex has at most one guard? So, we set d[p_i] = -h_i.
        Then push (-h_i, p_i) into the heap.

But wait: if we have two guards at different vertices, then we set d for each guard's vertex to their respective -h_i.

4. Then, while heap is not empty:
        pop the smallest d-value, say (d_val, u)
        If d_val is not equal to d[u] (if we have a better d already), skip.
        For each neighbor v of u:
            new_val = d_val + 1   # because we traverse an edge: adds 1 to the distance, so adds 1 to the expression (dist - h_i)
            If new_val < d[v]:
                d[v] = new_val
                heapq.heappush(heap, (new_val, v))

After this, d[v] for each vertex v is min_i ( dist(p_i, v) - h_i ). Then, we check if d[v] <=0. If yes, then v is guarded.

But note: the value d[v] can be negative? Yes, because at a guard's vertex, it's -h_i, which is negative. Then, as we move away, we add 1. So, if -h_i + d <=0, then d <= h_i, which is the condition.

Therefore, the guarded vertices are exactly those with d[v] <=0.

But let's test with Example 1:

Example1: 
Vertices: 1,2,3,4,5
Edges: (1,2), (2,3), (2,4), (3,5), (1,5)
Guards: (1,1) and (5,2)

So, we set:
d[1] = -1   (from guard at 1)
d[5] = -2   (from guard at 5)

Then, from 1 (d=-1), we go to 2: d[2] = -1+1 = 0 -> then 0<=0, so guarded.
From 1, we also go to 5: but d[5] is already -2, which is less than -1+1=0? So we don't update 5? Actually, we compare: new value for 5 from 1: -1 + dist(1,5). The edge (1,5) is present, so dist=1. Then new_val = -1+1=0. But d[5] is -2, which is less than 0? Then we don't update. But that's okay because we only care if d[v] <=0. Since -2<=0, 5 is guarded.

From 5 (d=-2), we go to 3: d[3] = -2+1 = -1 -> guarded? Then also to 1: but we already set d[1]=-1, and -2+1=-1, same? So we don't update? Or we update? Actually, we update only if new_val is less? But we are doing a min Dijkstra: we want the minimum value. So, we update only if we get a smaller value? But in Dijkstra, we are computing the minimum value of d(v). 

But note: d(v) = min_i (dist(p_i, v) - h_i). So, we want the smallest (most negative) value? Actually, the condition for being guarded is that d(v)<=0. But the value d(v) can be negative and we are taking min, which is the worst-case? Actually, no: the condition is: if there exists a guard i such that dist(p_i, v) - h_i <=0. But if we compute d(v) = min_i (dist(p_i, v) - h_i), then d(v)<=0 implies that at least one guard satisfies the condition? Actually, yes: because if the minimum over the expressions is <=0, then at least one is <=0.

But also, if d(v) >0, then for all guards, dist(p_i, v) - h_i >0, so none cover.

So, the algorithm is correct.

Now, from 5, we update 3 to -1. Then from 3, we update 2: from 3 to 2: new_val = -1+1=0. But 2 already has 0 (from guard1's propagation), so we don't update. Then from 2, we update 4: new_val=0+1=1. Then 4 has d[4]=1, which is >0, so not guarded.

Also, from 2, we go to 1 and 5? But we already have them.

So, the guarded vertices: 
1: d=-1 <=0 -> guarded
2: 0 -> guarded
3: -1 -> guarded
5: -2 -> guarded
4: 1 -> not guarded.

That matches the example.

What about Example 2:
Input: "3 0 1\n2 3"
So, N=3, M=0, K=1, and the guard is at 2 with stamina 3.

The graph has no edges, so vertices: 1,2,3. Only vertex 2 has the guard. Then:
d[2] = -3 (because guard at 2 with stamina 3: d[2]= -3)
Then, from 2, we can't go anywhere because no edges. So d[1] and d[3] remain infinity? Then only vertex 2 is guarded? Output: 1 vertex: 2.

But the input format: 
"3 0 1
2 3"

Wait, the guard is given as "2 3", meaning p1=2, h1=3.

But the edges: there are no edges. So the graph has 3 vertices and no edges. Then the distances: from 2 to 1: no path? So dist(2,1)=infinity? Then d[1] = min_i (dist(p_i,1)-h_i) = infinity -3 -> but we don't have that. Actually, in the algorithm, we only traverse edges. So vertices not connected to any guard will remain with d[v] = infinity, which is >0. So only vertex 2 has d[2]=-3<=0.

But what about vertex 3? The guard is at 2, and there's no edge from 2 to 3? So same as vertex1: not reachable. But the example input says "2 3" as the guard? And then output is 1 and vertex 2.

But wait, the example input: "3 0 1\n2 3" — that's two lines? The first line is "3 0 1", then the next line is the guard: "2 3". There are no edges because M=0. So we have three isolated vertices? Then the guard is at vertex 2, so only vertex 2 is guarded.

So the algorithm works.

Now, Example3: 
Input: 
10 10 2
2 1
5 1
6 1
2 4
2 5
2 10
8 5
8 6
9 6
7 9
3 4
8 2

So, edges: 10 edges, and two guards: (3,4) and (8,2)

We need to compute d for all vertices.

But we can simulate:

Guards: 
Guard1: p=3, h=4 -> d[3] = -4
Guard2: p=8, h=2 -> d[8] = -2

Now, we start from 3 (d=-4) and 8 (d=-2).

From 3: 
   edges: 3-4? We see the edges: 
      The edges: 
        2-1,5-1,6-1,2-4,2-5,2-10,8-5,8-6,9-6,7-9
      Also, the guard line: 3-4? Wait, the input has an edge "2 1", then ... then "3 4" is the guard? Actually, the input:

      "10 10 2
       2 1
       5 1
       6 1
       2 4
       2 5
       2 10
       8 5
       8 6
       9 6
       7 9
       3 4
       8 2"

      The last two lines: 
        "3 4" is an edge? Or the guard? 

      Actually, the problem says:
        First: N M K
        Then M edges
        Then K guards

      So, M=10, K=2. So the first 10 lines are edges, then two guards.

      The edges: 
        1: 2-1
        2: 5-1
        3: 6-1
        4: 2-4
        5: 2-5
        6: 2-10
        7: 8-5
        8: 8-6
        9: 9-6
        10: 7-9

      Then the guards: 
        first guard: 3 4 -> p1=3, h1=4
        second guard: 8 2 -> p2=8, h2=2

      Now, the graph: 
        Vertex 1: connected to 2,5,6
        Vertex 2: connected to 1,4,5,10
        Vertex 3: not in any edge? But then we have an edge 2-4? And guard at 3? How is 3 connected? 

      Wait, the edges: 
        edge4: 2-4 -> so vertex4 is connected to 2.
        Then guard1: at 3, but no edge from 3 to anyone? Then vertex3 is isolated? 

      Also, the last guard line is 8 2? But that's the guard at 8 with stamina 2.

      So, the graph has an isolated vertex 3? And vertex 7 connected to 9? 

      Now, the guarded vertices: 
        Guard at 3: stamina 4. Since vertex3 is isolated, it can cover only itself: because distance to itself is 0, which <=4. So vertex3 is guarded.

        Guard at 8: stamina 2. 
          From 8: connected to 5 and 6 (edges 7 and 8). Then from 5: connected to 1,2,8? 
          Edges: 5-1, 2-5, 8-5. 
          From 6: connected to 1,8,9? (edges: 6-1, 8-6, 9-6). 
          Then from 9: connected to 6,7? (edge: 9-6, 7-9). 
          So from 8: 
            distance0: 8 -> covered (d[8]=-2 -> which is <=0, so guarded)
            distance1: 5,6 -> d[5] = -2+1 = -1 -> guarded; d[6]=-1 -> guarded
            distance2: from 5: 1,2; from 6: 1,9 -> 
                for 1: min from guard8: -2+2=0 -> guarded
                for 2: from 5: -2+2=0 -> guarded
                for 9: -2+2=0 -> guarded
            distance3: from 1: 2 (already covered), 5 (covered), 6 (covered); 
                       from 2: 1 (covered), 4,5 (covered),10; 
                       from 9: 6 (covered),7 -> 
                so 4: from 2: -2+3=1 -> not guarded? 
                so 10: from 2: -2+3=1 -> not guarded
                so 7: from 9: -2+3=1 -> not guarded

        Also, guard at 3: only vertex3: d[3]=-4 -> guarded.

        So guarded vertices: 1,2,3,5,6,8,9 -> 7 vertices: 1,2,3,5,6,8,9.

        The output: 
          "7
          1 2 3 5 6 8 9"

        So matches.

Now, time complexity: We are running a Dijkstra in an unweighted graph? Actually, the edge weights are 1. So we can use BFS? But the initial values are different. However, we are using a min-heap. But the values in the heap are the d(v) = min_i (dist(p_i, v) - h_i). And each edge adds 1. So the heap operations: each vertex and edge is processed? But the heap can have multiple entries per vertex? However, we update a vertex only when we get a lower d(v) (since we are doing a Dijkstra that relaxes when we get a lower value). And the d(v) values are integers. The total number of updates: each edge is relaxed at most once? Actually, no: because when we update a vertex, we might get a lower d(v) and then we push that. But the relaxation condition: we update only if we get a lower d(v). So each vertex can be updated at most a few times? Actually, the value d(v) can decrease? But we are starting from the guards and propagating. The value d(v) is set to the minimum value we've found so far. So we update a vertex only when we find a path that yields a lower d(v). But the d(v) is the expression we defined, and it can only decrease? Then we update each vertex at most once? Because once we set d(v) to a value, we update only if we get a smaller one. But we don't know the range. However, in practice, we can have multiple updates per vertex? But we use a heap and we skip if we have a better value. So the total operations are O((N+M) log (N+M))? 

Actually, each edge might be considered at most a constant number of times? Because when we update a vertex, we push its neighbors. But if we update a vertex only once (with the minimal d(v)), then each edge is considered once. But we might update a vertex multiple times if we find a better d(v). However, the total number of updates per vertex is the number of distinct d(v) values? But d(v) is decreasing? Actually, the d(v) we compute is the minimum over the guards of (dist from that guard - h_i). And that value can be updated only once? Actually, no: we might first get a value from one guard, then a lower value from another guard.

But the algorithm is standard multi-source Dijkstra. The time complexity is O((N+M) log N) because each vertex is pushed to the heap at most a few times? Actually, worst-case, we might push each vertex once. Because once we set the minimal d(v), we never update it again? 

Wait: in the relaxation step, we update a neighbor only if we get a smaller d(v). And we process the smallest d(v) first. So, when we pop a vertex for the first time, we have the minimal d(v) for that vertex. Then, we never update it again. Why? Because if we later get a different path, it will have a larger d(v) (since we pop the smallest first) and then we skip. 

So, the algorithm:

   d = [inf]* (N+1)
   heap = min-heap of (d_value, node)

   for each guard (p, h):
        if d[p] > -h:   # we want minimal d(v) = min_i (dist(p_i, v)-h_i. So we set to -h? 
            d[p] = -h
            heapq.heappush(heap, (-h, p))

   while heap:
        d_val, u = heapq.heappop(heap)
        if d_val != d[u]: continue   # skip if outdated

        for each neighbor v of u:
            new_val = d_val + 1   # because we traverse an edge: adds 1 to the distance, so adds 1 to the expression (dist - h_i) which is d_val
            if new_val < d[v]:
                d[v] = new_val
                heapq.heappush(heap, (new_val, v))

Then, after the algorithm, for each vertex v, if d[v] <=0, it's guarded.

In this algorithm, each vertex is pushed at most once? Because once we set d[v] to a value, we only update if we get a smaller one, and then we push. But when we pop a vertex, we process it and we relax its neighbors. Then, we never push the same vertex again because we only push when we update and then we set d[v] to the new value. But then, when we pop the same vertex again, we skip because d_val != d[u] (since we updated to a smaller value). Actually, no: we update and push, and then when we pop the new one, we process. But we might have multiple pushes? 

Actually, the standard Dijkstra algorithm (with non-negative weights) does not require multiple pushes: because when we pop a vertex, we have the minimal distance. But here, the weights are 1 (non-negative) and we are starting from multiple sources. So, the first time we pop a vertex, we have the minimal d(v) for that vertex. Then, we can skip subsequent pops for the same vertex.

Therefore, we can also skip by checking when popping: if d_val is greater than d[u]? Actually, we set d[u] to the minimal, and then we push. But when we push, we push (d[v], v). Then, if we later pop an entry with a value greater than the current d[v], we skip.

So, the algorithm runs in O((N+M) log N) time, which is acceptable for N, M <= 200,000.

Space: O(N+M) for the graph and the heap.

So, this is efficient.

Steps for implementation:

1. Build the graph: we have N vertices and M edges. Since the graph is undirected, we add both directions.

2. Initialize:
   d = [10**18] * (N+1)   # a big number, since d[v] can be negative but we want to initialize to infinity (a big positive)
   heap = []

3. For each guard (p, h) in guards:
        if -h < d[p]:   # because we want the minimal d[p] (which is the expression). -h is the initial value at p.
            d[p] = -h
            heapq.heappush(heap, (-h, p))

   Note: we use a min-heap, so we push (value, node). The value is d[p] = -h.

4. While heap is not empty:
        pop (d_val, u) from heap.
        If d_val != d[u]: skip.  (because we might have updated d[u] to a smaller value already, and this entry is outdated)
        For each neighbor v of u:
            new_val = d_val + 1   # because we move one edge
            If new_val < d[v]:
                d[v] = new_val
                push (new_val, v) to heap.

5. Then, collect all vertices v (from 1 to N) such that d[v] <= 0. And sort them? But we are to output in ascending order, so we can iterate from 1 to N and collect if d[v]<=0, then the list is naturally sorted? Or we can collect and then sort.

But the vertices are numbered from 1 to N. If we collect all v with d[v]<=0, and then sort the list of v, that's O(G log G). But G can be up to N, so O(N log N). But total time is O((N+M) log N) which is acceptable.

Alternatively, we can just iterate from 1 to N and add to a list if guarded, then sort. Since the list has at most N elements, and N=200000, it's acceptable.

Let me code accordingly.

Edge case: if there are no guards? Then d[v] remains big for all v, so no guarded vertices. Output 0 and then nothing? But the output format: first line G, then G numbers. So if G=0, then we output 0 and no next line? Or output 0 and then an empty line? The problem says: 
   "G
    v1 v2 ... vG"

So if G=0, then we output "0" and that's it? Or "0" and then nothing? The example outputs: "1" then "2" for example2. So for G=0, we output:
   0

But the problem doesn't say that there is at least one guard? K>=1, so K is at least 1. So we don't have to handle K=0? But the constraint says K>=1.

Another edge case: if the graph is disconnected. Then the d[v] for vertices not connected to any guard remains big, so they are not guarded. Correct.

Now, implement the graph as an adjacency list.

Steps:

   graph = [[] for _ in range(N+1)]
   for each edge (a,b):
        graph[a].append(b)
        graph[b].append(a)

Then proceed.

Let me test with Example1:

   N=5, M=5, K=2.
   edges: [ [1,2], [2,3], [2,4], [3,5], [1,5] ]
   guards: [ [1,1], [5,2] ]

   Build graph:
        graph[1]: [2,5]
        graph[2]: [1,3,4]
        graph[3]: [2,5]
        graph[4]: [2]
        graph[5]: [1,3]

   d = [10**18]*(6)  # indices 0 to 5, we use 1..5.

   Guards: 
        guard1: p=1, h=1 -> d[1]=min(d[1], -1) -> set to -1, push (-1,1)
        guard2: p=5, h=2 -> d[5]=min(d[5],-2) -> set to -2, push (-2,5)

   Heap: [ (-1,1), (-2,5) ]

   Pop the smallest: (-2,5). Then check neighbors: 1 and 3.
        For 1: new_val = -2+1 = -1. Is -1 < d[1] (-1)? No, because -1 == -1? So skip? 
        Actually, d[1] is -1, and new_val is -1 -> not less, so skip.
        For 3: new_val = -2+1 = -1. d[3] is 10**18 -> update to -1, push (-1,3)

   Then pop: the next smallest: (-1,1) and (-1,3). The heap has both. Which one pops first? Doesn't matter. 
        Pop (-1,1): 
            neighbors: 2 and 5.
            For 2: new_val=-1+1=0. d[2] is big -> update to 0, push (0,2)
            For 5: new_val=-1+1=0. d[5] is -2, which is less than 0 -> skip.
        Then pop (-1,3): 
            neighbors: 2 and 5.
            For 2: new_val=-1+1=0. d[2] is 0? so skip? (if we updated already) -> but if we haven't popped the (0,2) yet, then we update? Actually, we set d[2]=0 when processing from 1, so when we get 0 again, we skip.
            For 5: new_val=-1+1=0. d[5]=-2 -> skip.

        Then pop (0,2):
            neighbors: 1,3,4.
            For 1: d[1]=-1, 0+1=1 -> not less, skip.
            For 3: d[3]=-1, 0+1=1 -> not less, skip.
            For 4: new_val=0+1=1. d[4]=big -> set to 1, push (1,4)

   Then pop (1,4): 
        neighbors:2 -> d[2]=0, 1+1=2 -> skip.

   Then heap empty.

   Now, d[1]=-1, d[2]=0, d[3]=-1, d[4]=1, d[5]=-2.

   Guarded: v with d[v]<=0: 1,2,3,5 -> [1,2,3,5] -> sorted: [1,2,3,5]. Output 4 and then the list.

Correct.

So, I'll implement accordingly.

Code:

   import heapq
   d = [10**18] * (N+1)
   heap = []
   graph = [[] for _ in range(N+1)]
   for a, b in edges:
        graph[a].append(b)
        graph[b].append(a)

   for p, h in guards:
        # set d[p] = min(d[p], -h) but since distinct p, we can set directly? 
        # But multiple guards might cover the same vertex? The problem says distinct p_i, so each guard is on a distinct vertex.
        # But what if two guards are on the same vertex? The input says distinct p_i, so we don't have to worry.
        if -h < d[p]:   # we want the minimal d[p] (the expression). -h is the value for guard at p.
            d[p] = -h
            heapq.heappush(heap, (-h, p))

   while heap:
        val, u = heapq.heappop(heap)
        if val != d[u]:   # if not the latest value, skip
            continue
        for v in graph[u]:
            new_val = val + 1
            if new_val < d[v]:
                d[v] = new_val
                heapq.heappush(heap, (new_val, v))

   guarded = []
   for i in range(1, N+1):
        if d[i] <= 0:
            guarded.append(i)

   Then output: 
        G = len(guarded)
        and then the sorted guarded list? But guarded is in increasing order of vertex id? Because we iterate from 1 to N. But if we append by increasing i, then guarded is sorted. So we don't need to sort? 

   But the problem says: "List all guarded vertices in ascending order."

   Since we iterate from i=1 to N, and we append i if guarded, the list 'guarded' will be in increasing order? Yes.

   However, if we collect in a different order? We are iterating from 1 to N, so the list will be [1,2,3,...] for the guarded ones? But we skip some. For example, if vertex1 is guarded, then vertex3 is guarded, we append 1 then 3. Then the list is [1,3] which is ascending. So we don't need to sort.

But if we collect in a different way? We are iterating from 1 to N, so the list is sorted by vertex id.

So, output:
   print(len(guarded))
   if guarded: 
        print(" ".join(map(str, guarded)))
   else:
        print()

But the problem says to output:
   G
   v1 v2 ... vG

So, we can do:
   print(len(guarded))
   if guarded: 
        print(" ".join(map(str, guarded)))

But if guarded is empty, then we output "0", and then nothing? The problem says: 
   "G
    v1 v2 ... vG"

So if G=0, then we output:
   0

and no second line.

But the example2: output is 
   1
   2

So we must output the second line only if G>0.

Alternatively, we can output the second line only if G>0? Or the problem doesn't care? Actually, the output format says: 
   G
   v1 v2 ... vG

So if G>0, then we output two lines; if G=0, then we output one line: "0".

But the problem constraints say K>=1, so there is at least one guard. But there might be no guarded vertices? Actually, each guard guards at least itself? Because distance to itself is 0 <= h_i (h_i>=1). So G>=K? At least the guard positions are guarded. So G>=1.

Therefore, we can always output two lines? Actually, no: if no vertex is guarded? But each guard guards itself, so G>=K and K>=1, so G>=1.

So we can always output two lines.

But wait: what if the guard is placed at a vertex that is not in the graph? But the guard is placed at p_i, and p_i is between 1 and N. And the graph has vertices 1..N. So the guard is on a valid vertex. And the guard's vertex is guarded. So G>=1.

So we can safely output two lines.

But in the code, we have:

   guarded = [i for i in range(1, N+1) if d[i] <=0]
   print(len(guarded))
   print(" ".join(map(str, guarded)))

That will output the second line even if guarded is empty? But guarded is not empty because at least the guard positions are guarded.

So, we are safe.

Now, test with Example2: 
   N=3, M=0, K=1, guard: (2,3)
   Build graph: no edges, so graph[1]=[], graph[2]=[], graph[3]=[]
   d = [10**18]*4
   For guard: p=2, h=3 -> d[2]=-3, push (-3,2)
   Then pop: ( -3,2) -> then look at neighbors of 2: none, so done.
   Then guarded = [i for i in 1..3 if d[i]<=0] -> only i=2: d[2]=-3<=0 -> guarded=[2]
   Output: 
        1
        2

Correct.

Now, code the solution accordingly.

Let me write the function:

   import heapq
   class Solution:
        def solve(self, N: int, M: int, K: int, edges: List[List[int]], guards: List[List[int]]) -> List[int]:
            # Build graph: N+1 vertices
            graph = [[] for _ in range(N+1)]
            for a, b in edges:
                graph[a].append(b)
                graph[b].append(a)
            
            # Initialize d with a big number: 10**18
            d = [10**18] * (N+1)
            heap = []
            
            # Process guards: distinct p_i
            for p, h in guards:
                # set d[p] = -h
                if -h < d[p]:   # since 10**18 is big, -h is negative, so always true? But to be safe.
                    d[p] = -h
                    heapq.heappush(heap, (-h, p))
            
            # Dijkstra
            while heap:
                val, u = heapq.heappop(heap)
                if val != d[u]:
                    continue
                for v in graph[u]:
                    new_val = val + 1
                    if new_val < d[v]:
                        d[v] = new_val
                        heapq.heappush(heap, (new_val, v))
            
            # Collect guarded vertices
            guarded = [i for i in range(1, N+1) if d[i] <= 0]
            # The problem expects to return the list? Or output? 
            # The problem says: return the list of guarded vertices in ascending order.
            return guarded

But the problem says: the function should return the list. And the output format is handled by the caller? Actually, in the starter code, the function returns the list.

But the problem says: 
   Returns: list of guarded vertices in ascending order.

And we return the list [v1, v2, ...] in ascending order? Yes, because we iterate from 1 to N, and we append only guarded, so the list is sorted by vertex id.

But the list is in increasing order of vertex id? Yes.

However, if we want to be safe, we could sort? But it's already sorted. Because we iterate from 1 to N. So the list is increasing.

But what if we do:
   guarded = []
   for i in range(1, N+1):
        if d[i] <=0:
            guarded.append(i)

Then guarded is sorted.

So, we can return guarded.

Now, test with example3: 
   We did the reasoning and it matches.

But let me test a small example: 
   N=1, M=0, K=1: guard (1,1)
   Then d[1] = -1 -> guarded=[1]. Correct.

Another: N=2, M=0, K=1: guard (1,1)
   Then d[1]=-1 -> guarded, d[2]=10**18 -> not guarded. So output [1]. Correct? 
   But distance from 1 to 2: no edge -> infinity. So 2 not guarded.

But if we have an edge: 
   N=2, M=1: edge (1,2), K=1: guard (1,1)
   Then:
        d[1]=-1, push (-1,1)
        Pop: u=1, val=-1
        Then for neighbor v=2: new_val=-1+1=0 -> d[2]=0, push (0,2)
        Then pop: (0,2) -> then for neighbors: 1 -> new_val=0+1=1 -> but d[1]=-1, so skip.
        Then guarded: 1 and 2. Correct.

Another: guard (1,0): 
   Then d[1]=0 -> push (0,1)
   Then for neighbor v=2: new_val=0+1=1 -> d[2]=1 -> not guarded. So only 1. Correct.

So, the algorithm is correct.

Complexity: O((N+M) log N) which is acceptable for N, M up to 200000.

Now, code accordingly.

Note: We use heapq, which is min-heap. 

We'll write the code as described.

Let me write the code.

One more thing: the initial value for d: we set to 10**18. But if the guard has h_i=200000, then -h_i=-200000, which is greater than 10**18? No, because 10**18 is positive and -200000 is negative. So the condition -h < d[p] will be true because negative is always less than a positive? Actually, d[p] is initially 10**18 (positive), and -h is negative, so we set d[p]=-h and push.

But we can also initialize d to a big number that is larger than any possible d(v). The possible d(v) is at least -max_h (which is -N) and at most ...? The minimal value (most negative) is -max_h, and the maximum value can be ... as large as the graph diameter? But we don't care. We only care about comparisons: we update when we get a value less than the current. So if we set d to a very big number initially, then we update for every guard and then for every reachable vertex.

But we can also initialize d to a very large positive number, and then the condition new_val < d[v] will be true for any finite new_val when d[v] is the initial big number.

So, we can avoid the condition in the guard processing? 

Instead, we can do:

   for p, h in guards:
        if -h < d[p]:
            d[p] = -h
            heapq.heappush(heap, (-h, p))

But since d[p] is initially a big positive and -h is negative, the condition is always true. So we can skip the condition? 

But to be safe, we can do:

   d = [10**18] * (N+1)
   for p, h in guards:
        d_val = -h
        if d_val < d[p]:
            d[p] = d_val
            heapq.heappush(heap, (d_val, p))

But if there are multiple guards? But the problem says distinct p_i. So each p is unique. So we set d[p] only once per guard? 

But what if two guards are at the same vertex? The problem says: "All p_i are distinct." So we don't have to worry.

So we can write:

   for p, h in guards:
        d_val = -h
        if d_val < d[p]:
            d[p] = d_val
            heapq.heappush(heap, (d_val, p))

But the condition is redundant because d[p] is 10**18 and d_val is negative, so always true. So we can just set and push.

But to be generic, we leave the condition.

Now, implement.

I think we are good.

Let me run the code for example1.

Code:

   import heapq
   from typing import List

   class Solution:
        def solve(self, N: int, M: int, K: int, edges: List[List[int]], guards: List[List[int]]) -> List[int]:
            graph = [[] for _ in range(N+1)]
            for a, b in edges:
                graph[a].append(b)
                graph[b].append(a)
            
            d = [10**18] * (N+1)
            heap = []
            
            for p, h in guards:
                val0 = -h
                if val0 < d[p]:
                    d[p] = val0
                    heapq.heappush(heap, (val0, p))
            
            while heap:
                val, u = heapq.heappop(heap)
                if val != d[u]:
                    continue
                for v in graph[u]:
                    new_val = val + 1
                    if new_val < d[v]:
                        d[v] = new_val
                        heapq.heappush(heap, (new_val, v))
            
            res = []
            for i in range(1, N+1):
                if d[i] <= 0:
                    res.append(i)
            return res

Test with example1: returns [1,2,3,5] -> correct.

Now, run with example3: we expect [1,2,3,5,6,8,9]

But we have to build the graph from the example3 input.

Input for example3:

   N=10, M=10, K=2
   edges: 
        [2,1]
        [5,1]
        [6,1]
        [2,4]
        [2,5]
        [2,10]
        [8,5]
        [8,6]
        [9,6]
        [7,9]
   guards: 
        [3,4] -> guard at 3 with stamina 4
        [8,2] -> guard at 8 with stamina 2

In the graph, vertex3 has no edge? Then:

   graph[3] = []  (from the edges, no edge incident to 3? Then we have an edge [2,4] and [3,4] is not an edge? 

Wait, the edges are:

   edge0: 2-1
   edge1: 5-1
   edge2: 6-1
   edge3: 2-4
   edge4: 2-5
   edge5: 2-10
   edge6: 8-5
   edge7: 8-6
   edge8: 9-6
   edge9: 7-9

So, no edge incident to 3? Then vertex3 is isolated.

Then the algorithm:

   Guards: 
        guard1: p=3, h=4 -> d[3]=-4, push (-4,3)
        guard2: p=8, h=2 -> d[8]=-2, push (-2,8)

   Then pop from heap: the smallest is (-4,3) -> no neighbors, so done for 3.
   Then pop (-2,8): 
        neighbors: 5 and 6? 
        For v=5: new_val=-2+1=-1 -> set d[5]=-1, push (-1,5)
        For v=6: new_val=-2+1=-1 -> set d[6]=-1, push (-1,6)

   Then pop the next smallest: (-1,5) and (-1,6). Let's say (-1,5) first.
        For 5: neighbors: 1 (from edge1:5-1), 2 (from edge4:2-5), 8 (from edge6:8-5) -> but 8 already done.
            For 1: new_val=-1+1=0 -> set d[1]=0, push (0,1)
            For 2: new_val=-1+1=0 -> set d[2]=0, push (0,2)
        Then pop (-1,6):
            neighbors: 1 (edge2:6-1), 8 (edge7:8-6), 9 (edge8:9-6)
            For 1: 0? but d[1] is 0 now? so skip? (because -1+1=0, and 0 is not less than 0? so skip)
            For 8: skip
            For 9: new_val=-1+1=0 -> set d[9]=0, push (0,9)

        Then pop (0,1): 
            neighbors: 2,5,6 -> 
                2: d[2]=0, new_val=0+1=1 -> skip
                5: d[5]=-1, skip
                6: d[6]=-1, skip
        Then pop (0,2):
            neighbors: 1,4,5,10
                1: skip
                4: new_val=0+1=1 -> set d[4]=1, push (1,4)
                5: skip
                10: new_val=0+1=1 -> set d[10]=1, push (1,10)
        Then pop (0,9):
            neighbors:6,7
                6: skip
                7: new_val=0+1=1 -> set d[7]=1, push (1,7)

        Then pop (1,4): 
            neighbors:2 -> skip (because 1+1=2, and d[2]=0, so skip)
        Then pop (1,10): 
            neighbors:2 -> skip
        Then pop (1,7): 
            neighbors:9 -> skip

   Then guarded: 
        d[1]=0 -> guarded
        d[2]=0 -> guarded
        d[3]=-4 -> guarded
        d[4]=1 -> not
        d[5]=-1 -> guarded
        d[6]=-1 -> guarded
        d[7]=1 -> not
        d[8]=-2 -> guarded
        d[9]=0 -> guarded
        d[10]=1 -> not

   So guarded: [1,2,3,5,6,8,9] -> sorted: [1,2,3,5,6,8,9] -> correct.

So the code should be correct.

Now, we write the final code.

Note: We use heapq, so import heapq.

We'll write the solution accordingly.

Let me code.

We'll run the examples to be sure.

But since we tested the examples, I think it's correct.

Final code:

## Final Solution

### Approach
The problem involves determining all vertices in a graph that are guarded by at least one security guard. Each guard is positioned at a distinct vertex and can cover vertices within a distance equal to their stamina. The solution efficiently computes the guarded vertices using a modified Dijkstra's algorithm to propagate the coverage from each guard's position.

### Solution Explanation
1. **Graph Representation**: The graph is represented as an adjacency list to efficiently traverse neighbors of each vertex.
2. **Initialization**: An array `d` is initialized to store the minimal value of `(distance from a guard to the vertex) - (guard's stamina)` for each vertex. Initially, all values are set to a large number (infinity).
3. **Guard Processing**: For each guard at vertex `p` with stamina `h`, set `d[p]` to `-h` (since `distance(p, p) = 0`, so `0 - h = -h`). Push this value and vertex into a min-heap.
4. **Dijkstra's Algorithm**: Process vertices from the heap. For each vertex `u` popped from the heap, check its neighbors. For each neighbor `v`, compute `new_val = d[u] + 1` (since moving to a neighbor increases the distance by 1). If `new_val` is less than the current `d[v]`, update `d[v]` and push it into the heap.
5. **Result Collection**: After processing, any vertex `v` with `d[v] <= 0` is considered guarded. Collect these vertices in ascending order (which is naturally achieved by iterating from vertex 1 to N).

### Solution Code
```python
import heapq
from typing import List

class Solution:
    def solve(self, N: int, M: int, K: int, edges: List[List[int]], guards: List[List[int]]) -> List[int]:
        graph = [[] for _ in range(N+1)]
        for a, b in edges:
            graph[a].append(b)
            graph[b].append(a)
        
        d = [10**18] * (N+1)
        heap = []
        
        for p, h in guards:
            val0 = -h
            if val0 < d[p]:
                d[p] = val0
                heapq.heappush(heap, (val0, p))
        
        while heap:
            val, u = heapq.heappop(heap)
            if val != d[u]:
                continue
            for v in graph[u]:
                new_val = val + 1
                if new_val < d[v]:
                    d[v] = new_val
                    heapq.heappush(heap, (new_val, v))
        
        res = []
        for i in range(1, N+1):
            if d[i] <= 0:
                res.append(i)
        return res
```

### Explanation
1. **Graph Construction**: The graph is built using an adjacency list from the input edges.
2. **Initial Setup**: The distance array `d` is initialized to a large value (10^18) to represent infinity. Each guard's position `p` is initialized to `-h` (indicating `distance - stamina`) and added to a min-heap.
3. **Processing Vertices**: The algorithm processes each vertex from the heap. For each vertex `u`, it checks all neighbors `v`. The value `d[u] + 1` (representing the increased distance from the guard) is compared to `d[v]`. If it's smaller, `d[v]` is updated, and `v` is added to the heap.
4. **Result Extraction**: Vertices with `d[i] <= 0` are collected into the result list. Since vertices are processed in order from 1 to N, the result list is naturally sorted in ascending order.

This approach efficiently computes the guarded vertices using Dijkstra's algorithm with a time complexity of O((N + M) log N), which is optimal for the given constraints.