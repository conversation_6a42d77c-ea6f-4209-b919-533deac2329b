# Solution for 975_A.md

Generated at: 2025-07-22 06:05:26
Model: deepseek-reasoner

## Reasoning Process

Alright, I'm trying to solve this Aramic script problem. Let me make sure I understand it correctly.

The problem says that in Aramic, words represent objects, and each object has a root. The root is defined as a string that contains each letter appearing in the word exactly once, regardless of the original order or duplicates. For example, the root of "aabb", "bab", "baabb", and "ab" is "ab". Also, any permutation of the root represents the same object.

The task is: given a list of words, count the number of distinct objects mentioned. That is, after converting each word to its root, how many unique roots are there?

Constraints: 
- n (number of words) is up to 1000.
- Each word's length is up to 1000.
- All characters are lowercase Latin letters.

Examples:
1. Input: 5 words: "a", "aa", "aaa", "ab", "abb" → Output: 2. Because roots are "a" and "ab".
2. Input: 3 words: "amer", "arem", "mrea" → Output: 1. Because all are permutations of "amer", so root is "amer" (with distinct letters).

So, the key steps are:
1. For each word, convert it to its root. The root is the set of unique characters in the word, sorted? Or just the set? Actually, since the problem says "the root is the word that contains all letters that appear in y, each letter once", and then says permutations of the root are the same. But for counting distinct objects, we don't care about the order of the root; we only care about which set of letters it has. So actually, the root is uniquely determined by the set of distinct characters. However, the problem says the root is a word, so we can represent it as a sorted string of the distinct characters? Or just the set? But we need to compare roots.

But note: the problem states that the root is the word containing each letter once. But when counting distinct objects, two roots that are permutations of each other are the same? Actually, no. The root is defined to be the word that has each letter exactly once, but the problem also says that a root and all its permutations represent the same object. So actually, for the purpose of distinct objects, we don't need to worry about the order of the root. We can represent the root as a canonical form, such as the sorted string of distinct characters. Then, two words that have the same set of distinct characters (regardless of order) will have the same canonical root representation.

But actually, the root itself is not unique in string form because it can be any permutation. However, for the purpose of counting distinct objects, we can use the set of distinct characters as the key. So, for each word, we can:

- Extract the distinct characters (remove duplicates).
- Then, we can sort these distinct characters to form a canonical representation? Or, we can use an unordered set? But in programming, sets are unhashable in Python if we use the built-in set, but we can use a frozenset. Alternatively, we can sort the distinct characters and form a string. Then, two words that have the same distinct characters (in any order) will have the same sorted string.

For example:
Word "aabb" → distinct characters: {'a','b'} → sorted: ['a','b'] → string "ab"
Word "bab" → distinct: {'b','a'} → sorted: ['a','b'] → "ab"
Similarly, "amer", "arem", "mrea" all have distinct characters: {'a','m','e','r'} → sorted to "aemr"

But wait, the example output for the second example is 1, so the root is "amer" but actually, the root should be the distinct letters. However, the problem says: "the root of a word y is the word that contains all letters that appear in y" (each once). So, the root for "amer" is the same as for "arem" — they both have the same set of letters. So, the root is the set of letters, and we can represent it as a sorted string of the unique characters.

But note: the root is defined to be the word containing the distinct letters. But it doesn't specify the order. However, since permutations are considered the same object, we can choose a canonical order, like sorted, to represent the root uniquely.

So the plan:
1. For each word, remove duplicate characters and then sort the remaining unique characters to form a string (the canonical root).
2. Add these canonical roots to a set (which inherently keeps unique entries).
3. The answer is the size of that set.

But is sorting necessary? Actually, we can also use a frozenset of the characters. Then, two words with the same distinct characters will have the same frozenset. However, comparing frozensets might be a bit slower, but for small sets (max 26 distinct letters) it's negligible. Alternatively, sorted string might be more straightforward and easier to handle.

Considering constraints: the distinct letters per word can be at most 26 (since only lowercase Latin letters). So sorting each word's distinct letters is O(26 log 26) which is constant time. And for each word, we have to process up to 1000 characters to extract distinct ones.

So overall, for each word, we do:
- Traverse each character to form a set of unique characters. Time: O(len(word)), which is <= 1000.
- Then, convert the set to a sorted list and then to a string. The set size is at most 26, so sorting is O(26*log26) ≈ constant.

Then, we do this for up to 1000 words. So total time is 1000 * (1000 + constant) ≈ 10^6 operations, which is acceptable in Python.

Alternatively, we can avoid sorting by using a frozenset. But then, two sets with same letters are equal. However, the problem does not require the root to be in any particular order, so using a set (or frozenset) is valid. But when we store in a set, the set will consider two frozensets with same elements as same. So that would work.

But let me test with the examples:

Example 1: words = ["a", "aa", "aaa", "ab", "abb"]
- For "a": set is {'a'} → frozenset({'a'})
- "aa": {'a'} → same
- "aaa": {'a'} → same
- "ab": {'a','b'} → frozenset({'a','b'})
- "abb": {'a','b'} → same

So the distinct frozensets are: {frozenset({'a'}), frozenset({'a','b'})} → 2.

Example 2: words = ["amer", "arem", "mrea"]
Each gives the set {'a','m','e','r'} → same frozenset → size 1.

So both methods (sorted string or frozenset) would work.

But which is more efficient? In Python, creating a frozenset might be a bit faster than sorting because we don't need to sort. But the set size is small, so the difference is negligible. However, using a frozenset might be simpler in code.

But note: we cannot put a set in a set because set is mutable. So we have to use frozenset.

Alternatively, we can use a tuple of sorted characters. But that would be similar to the sorted string.

But for the purpose of hashing, both frozenset and tuple of sorted characters are acceptable. However, frozenset doesn't require sorting, so it might be slightly faster. But again, with max 26, sorting is negligible.

But let me think: if we have two sets that are the same, but we represent them as frozenset, then the hash is based on the elements, regardless of order. Similarly, for the tuple of sorted characters, the hash is the same for the same set. So both are valid.

However, the problem says the root is a word. But we don't have to output the root, just count distinct ones. So either representation is fine.

I think I'll go with the sorted string method because it might be more explicit and easier to understand. Also, in the problem examples, the roots are given as strings (like "a", "ab", "amer"). So if we output the sorted string, it will be in lex order, which is a standard representation.

But note: the root for "amer" is "amer" (without duplicates) but the distinct letters are a, m, e, r. When sorted, it becomes "aemr". But the problem doesn't specify the order of the root. So it's acceptable.

Alternatively, we could not sort and just use the set? But then two sets that are equal will be considered the same, which is correct. But the problem says the root is a word. However, for counting distinct objects, we don't care about the word representation of the root, only that two words with the same distinct set are the same object. So using a set (via frozenset) is correct.

But let me see: the problem says "the root x of a word y is the word that contains all letters that appear in y" (each once). So actually, the root is a specific string? But then, note that any permutation is the same object. So the root is not unique as a string? But then, for the purpose of distinct objects, we are grouping by the set of letters. So we can use the set.

Actually, the problem says: "the root x of a word y is the word that contains all letters that appear in y" (each once). But it doesn't specify the order. So any order is acceptable. Then, for consistency, we can choose a canonical order, but it's not necessary for counting.

So, I have two methods:

Method 1: For each word, create a set of characters, then convert to a sorted string. Then use that string as the key.

Method 2: For each word, create a frozenset of its characters. Then use that as the key.

Both are correct.

But which is more efficient? 

In terms of time:
- For Method 1: O(len(word)) to build the set, then O(1) to sort (because max 26 elements) and O(1) to form a string (max 26 characters). So overall per word: O(len(word)).
- For Method 2: O(len(word)) to build the set, then O(1) to create a frozenset (because set size is small). Then, hashing the frozenset: worst-case O(set size) which is 26. So same as Method 1.

But in practice, Method 2 might be slightly faster because it avoids sorting. However, for 26 elements, the difference is negligible.

In terms of space: 
- Method 1: stores a string of up to 26 characters per word. 
- Method 2: stores a frozenset which is a set of up to 26 characters. The frozenset might take more space than a string? Because a set has overhead (like hash table) while a string is a contiguous array. But again, with 26, it's negligible.

But I think both are acceptable.

I'll choose Method 2 for simplicity in code? Actually, in Python, using a set and then converting to frozenset is straightforward. But we can also use a tuple of sorted, but that adds sorting.

Alternatively, we can do:

keys = set()
for word in words:
    key = frozenset(word)
    keys.add(key)

Then, the answer is len(keys).

But wait, will this work? Let me test with the examples.

Example 1: 
- word "a": frozenset({'a'})
- "aa": frozenset({'a'}) → same
- "aaa": same
- "ab": frozenset({'a','b'})
- "abb": same

So distinct keys: 2.

Example 2: all words become frozenset({'a','m','e','r'}) → one key.

But what if the word has different letters but same set? It will be same. So correct.

But note: the word might be empty? The constraints say each string length is at least 1? Actually, the input says "The length of each string does not exceed 10^3", but it could be zero? Wait, the problem says "words", so probably non-empty? But to be safe, let me check: the constraints say each string length is up to 1000, but not minimum. So it could be zero? However, the problem states "words", and the example words are all non-empty. But the input guarantee says "all characters are small latin letters", which implies that the string might be empty? Actually, the problem says "the second line contains n words", so if n is 0, but n>=1, so at least one word. And each word: the length can be zero? The constraint says "each string length does not exceed 1000", so it can be zero. So we have to handle empty string.

If a word is empty string, then its set is empty. So root is the empty string. Then, we would have a key of frozenset().

But the problem says: the root is the word that contains all letters that appear in y. So if the word is empty, the root should be the empty word. Then, it's one distinct object.

So our method handles that: frozenset() for empty string.

So the code:

n = int(input().strip())
words = input().split()  # because the second line contains n words

But wait, the input format: the second line contains n words. So we can split by space.

But what if the words are separated by multiple spaces? The problem says: the second line contains n words. So we can split the second line and get a list of words.

So steps:

1. Read n (but we might not need it, because we can get the number from the split list).
2. Read the next line and split to get list of words.

Then, for each word in the list, convert to frozenset of its characters.

Then, create a set of these frozensets.

Then, output the size of that set.

But note: the problem says the first line is n, and the second line has n words. So if n is 1000, the second line has 1000 words. But each word length up to 1000, so the total length of the second line could be up to 1000*1000 + 999 spaces = 10^6 + 1000, which is acceptable.

But in Python, reading a line of 10^6 characters is acceptable.

So code:

def solve(n, words):
    distinct_roots = set()
    for word in words:
        # Convert the word to its set of characters, then to frozenset? Or just use set and then add the frozenset?
        # But set is mutable, so we cannot put in a set. So we can use frozenset directly.
        s = frozenset(word)
        distinct_roots.add(s)
    return len(distinct_roots)

But wait, is this correct? Actually, we don't need to use frozenset; we can use a tuple of sorted unique characters? But with the set method, we are not removing duplicates? Actually, no: when we do frozenset(word), it automatically removes duplicates because set only contains unique elements. So that's correct.

But actually, the root is defined as having each letter once. So by taking the set of the word, we get the distinct letters. Then, two words with the same distinct letters (in any order and any frequency) will have the same set. So this is exactly the root representation we need.

So the above code is correct.

But let me test with an example: word "aabb" and "ab"

"aabb": frozenset('a','a','b','b') = frozenset({'a','b'})
"ab": frozenset('a','b') = same.

Similarly, "a" and "aaa" both become frozenset({'a'}).

So correct.

Edge cases:
- Empty word: word = "" → frozenset() → empty set. Then, if there are multiple empty words, they all map to the same root. So one object.
- One word: output 1.
- All words same distinct letters: output 1.
- Words with different distinct letters: output the number of distinct sets.

What about different sets? For example, "a" and "b": two sets.

So the code should be:

class Solution:
    def solve(self, n: int, words: List[str]) -> int:
        distinct_roots = set()
        for word in words:
            s = frozenset(word)
            distinct_roots.add(s)
        return len(distinct_roots)

But we can also do:

return len({frozenset(word) for word in words})

That's more concise.

But let me check with the examples.

Example 1: words = ["a", "aa", "aaa", "ab", "abb"]
frozenset for each:
- "a" -> {'a'}
- "aa" -> {'a'}
- "aaa" -> {'a'}
- "ab" -> {'a','b'}
- "abb" -> {'a','b'}

Then the set of these: has two elements: one is frozenset({'a'}), the other is frozenset({'a','b'}).

Example 2: words = ["amer", "arem", "mrea"] → all become frozenset with {'a','m','e','r'}, so one element.

So correct.

But wait, what if two words have the same set of distinct letters, but the sets are represented differently? For example, frozenset({'a','b'}) is equal to frozenset({'b','a'})? Yes, because sets are unordered. So they are equal.

So this method is correct.

But is there any performance issue? The worst-case, we have 1000 words, each word up to 1000 characters. Creating a set from a string of length L is O(L). So total time is O(n * L) = 1000 * 1000 = 1e6, which is acceptable in Python.

Space: We are storing a set of frozensets. Each frozenset has at most 26 elements. The total number of distinct frozensets is at most 1000 (if all are distinct). But actually, distinct sets: the distinct sets are the distinct subsets of 26 letters. But we are storing each set as a frozenset, which for each set, the size is the number of elements. The total space is O(n * |set|) worst-case, which is 1000*26 = 26,000, which is fine.

Alternatively, we can do:

distinct_roots = set()
for word in words:
    distinct_roots.add(''.join(sorted(set(word))))

But that would create a sorted string. Then, we store strings. The set will compare the strings. This is also acceptable.

But the frozenset method is simpler and avoids sorting, even though the sorting is cheap. I think both are acceptable.

But let me test the sorted string method with the same examples.

Example 1: 
"a": set is {'a'} → sorted is ['a'] → string "a"
"aa": same → "a"
"aaa": "a"
"ab": set {'a','b'} → sorted ['a','b'] → "ab"
"abb": same → "ab"

Then distinct strings: {"a", "ab"} → size 2.

Example 2: 
"amer": set {'a','m','e','r'} → sorted ['a','e','m','r'] → "aemr"
Similarly, "arem" and "mrea" become "aemr"

So same.

But note: what if the set has letters that are not adjacent in the alphabet? For example, "ba" and "ab": both become set {'a','b'} → sorted to "ab", same.

So both methods are correct.

But the frozenset method is more straightforward and doesn't require sorting. However, the sorted string method gives a human-readable representation. But for the code, we don't care.

I think I'll stick with the frozenset method.

But one more thing: are there any collisions? For example, two different sets that become the same frozenset? No, because the frozenset is uniquely determined by the distinct characters.

So the solution is:

Read n (we can read and ignore, or use it to check)
Read the next line and split to get words.
Create a set and for each word, add frozenset(word) to the set.
Then output the size of the set.

But in the function signature, we are given n and the list of words. So we don't need to read.

Code:

class Solution:
    def solve(self, n: int, words: List[str]) -> int:
        distinct_roots = set()
        for word in words:
            distinct_roots.add(frozenset(word))
        return len(distinct_roots)

Alternatively, using a set comprehension:

class Solution:
    def solve(self, n: int, words: List[str]) -> int:
        return len({frozenset(word) for word in words})

I think that's concise and clear.

Now, let me think of edge cases:

1. n=0: but n>=1, so no.
2. Empty word: as above, frozenset() is the empty set. Then if we have two empty words, they both map to the same set. So count as one.
3. One word: returns 1.
4. All words same: returns 1.
5. All words different sets: returns n.

Another test: 
Input: n=3, words = ["abc", "acb", "bac", "bca", "aa", "a"] 
Roots: 
- "abc", "acb", "bac", "bca": all have set {'a','b','c'} → one root.
- "aa" and "a": both have set {'a'} → one root.
So total distinct roots: 2.

So the code should return 2.

Another test: 
Input: n=2, words = ["", ""] → both are frozenset() → one root → output 1.

But the problem says: "the length of each string does not exceed 1000", so empty string is allowed.

But in the example, the input words are split by space. If there's an empty word, how is it represented? For example, if the input is "  " (two spaces), then splitting by space might give ['', '', '']? Actually, in Python, splitting a string of spaces gives a list of empty strings for the consecutive spaces? Actually, no: if the line is "a  b", split gives ['a','','b']? No, actually, by default, split without arguments splits by any whitespace and removes leading/trailing and consecutive whitespace are treated as one separator. So if we have two words separated by two spaces, we get two words. But if we have an empty string as a word? The problem says the second line contains n words. So if n=3, and the line is "a b c", that's three words. But if the line is "a  b" (with two spaces), then split gives ['a','b'] (two words). So we don't get empty words.

But the problem says: "The second line contains n words". So there are exactly n words. Each word is a string of lowercase letters, which can be empty? The constraints say "each string does not exceed 1000", so the string can be empty. So we must consider the possibility of empty words.

But in the input format, the words are separated by spaces. How is an empty word represented? For example, if we have two words: one is "a" and the other is empty. The input line might be "a "? Then splitting gives ['a', '']? Or if it's " a" → same? Actually, in Python, str.split() without argument splits by any whitespace and removes leading/trailing, but if we have consecutive spaces, it doesn't create empty strings. However, the problem says: the second line contains n words. So if n=2, then the line must have two words. How is the empty word represented? 

Actually, the problem does not specify that the words are separated by exactly one space. It says: "the second line contains n words". So we should read the line and split it, but if we use the default split, it will skip empty words. But if we have an empty word, how can it be included? 

Looking at the problem: "The second line contains n words". So if n=3, the line must have three words. So if one of the words is the empty string, it should be represented as, for example, the first word: then the line might be " a b" (if the empty string is at the beginning, it becomes tricky). Actually, in standard input, splitting by space with no arguments would not include the empty strings. 

But the problem says: "The second line contains n words", so we can assume that the words are non-empty? Or the words can be empty? 

The constraints: "each string does not exceed 1000", meaning the length can be 0. So we must consider empty words.

But in the input, how is an empty word represented? For example, if n=2 and the words are "" and "a", the input line would be " a" (with a space at the beginning) or " a" (with two words: the first is empty, the second is "a")? 

Actually, in many programming problems, when reading words, if the input has consecutive spaces, it might denote empty words? But in Python, using str.split() without arguments will remove empty strings. But we can use str.split(' ') which keeps empty strings if there are consecutive spaces.

But the problem does not specify the exact format of the second line. However, in the starter code, the function is given a list of words. So the input reading is already handled for us. The function parameters are n and words (a list of strings). So we can assume that words is a list of n strings, and each string is a word (which can be empty).

So in our function, we are given the list of words. So we don't have to worry about parsing. Therefore, we can handle empty strings as above.

So our solution already handles empty strings.

Therefore, the code is:

class Solution:
    def solve(self, n: int, words: List[str]) -> int:
        # Using a set of frozensets
        return len({frozenset(word) for word in words})

This is efficient and concise.

But let me test with the sample inputs.

Example 1: 
Input: n=5, words = ["a", "aa", "aaa", "ab", "abb"]
Output: 2 → correct.

Example 2:
Input: n=3, words = ["amer", "arem", "mrea"]
Output: 1 → correct.

Edge: n=1, words=[""] → output 1.

Edge: n=2, words=["", "a"] → output 2 (because frozenset() and frozenset({'a'}) are different).

So the code is correct.

Now, what about performance? Worst-case, n=1000, each word length=1000. Then, building a set for each word: for each word, we do 1000 operations to build the set. Total operations: 1000*1000 = 1e6, which is acceptable in Python.

So I think this is the optimal solution.

Alternative approaches:

1. Brute force: For each word, we could try to generate the root by removing duplicates and then sorting, then store the sorted string in a set. As discussed, that is also O(n * (L + 26 log 26)), which is similar. But we did the frozenset which avoids the sorting. However, the set building is O(L) and then the frozenset creation is O(1) for the operation, but building the set is O(L). So same as the sorted string method.

2. Space-optimized: Instead of storing the frozenset for each word, we could compute a hashable representation on the fly. But we are already doing that. Alternatively, we could use a bitmask to represent the set. Since there are only 26 letters, we can represent the set as an integer bitmask. For example, for each letter, set a bit: 'a'=0, 'b'=1, etc. Then, for each word, we can compute an integer bitmask:

bitmask = 0
for c in word:
    bitmask |= (1 << (ord(c) - ord('a'))

Then, we store the bitmask. Then, the distinct objects are distinct bitmasks. This is also O(L) per word, and the bitmask is an integer, which is hashable. Then, we can store these integers in a set.

But the problem says the string can be empty? Then bitmask=0. So that works.

But is this more efficient? In terms of space: an integer is 28 bytes? While a frozenset of k elements is more. But for k up to 26, the frozenset is about 200-300 bytes? But we are storing only one set of the distinct bitmasks (the set of integers). The integers are 28 bytes each, and the set of integers is efficient. But the frozenset method is also efficient. The total distinct sets is at most 2^26 (if all subsets appear), which is 67M, but n is only 1000, so we have at most 1000 distinct sets. So either way is fine.

But the bitmask method:

class Solution:
    def solve(self, n: int, words: List[str]) -> int:
        distinct = set()
        for word in words:
            mask = 0
            for c in word:
                # Only lowercase letters
                shift = ord(c) - ord('a')
                mask |= (1 << shift)
            distinct.add(mask)
        return len(distinct)

This is also O(n*L), and same time. But it avoids creating a set. However, the inner loop is O(L). So same time.

But in practice, the bitmask might be faster because it uses integer operations, which are fast. But for each character, we do an OR and a shift. But building a set also does hashing and checks, which might be similar.

But with n=1000 and L=1000, the bitmask method does 10^6 iterations, which is acceptable.

But the frozenset method is more Pythonic and clear. Unless performance is critical, I'd prefer clarity.

But the problem constraints are small, so both are acceptable.

I'll stick with the frozenset for simplicity.

Therefore, the final solution is:

class Solution:
    def solve(self, n: int, words: List[str]) -> int:
        return len({frozenset(word) for word in words})

Or if we want to avoid the set comprehension for clarity:

class Solution:
    def solve(self, n: int, words: List[str]) -> int:
        distinct_roots = set()
        for word in words:
            distinct_roots.add(frozenset(word))
        return len(distinct_roots)

Either is fine.

I'll write the set comprehension as it's concise.

Let me test with the examples in code.

Example 1: 
words = ["a", "aa", "aaa", "ab", "abb"]
set of frozensets: 
{frozenset({'a'}), frozenset({'a','b'})} → size 2.

Example 2: 
words = ["amer","arem","mrea"] → one frozenset.

So we output 2 and 1 respectively.

Thus, the solution is verified.

## Final Solution

### Step 1: Problem Understanding and Modeling
The problem requires counting distinct objects represented by words in an Aramic script. Each object is represented by its root, which is the set of distinct letters in the word (order and duplicates do not matter). Two words represent the same object if their roots (sets of distinct letters) are identical.

**Key Insights:**
- The root of a word is determined by its unique characters.
- Permutations of the same set of characters represent the same object.
- The solution involves converting each word to its root (set of distinct characters) and counting distinct roots.

**Constraints Handling:**
- **Input Size:** Up to 1000 words, each up to 1000 characters. Efficient O(n * L) solution is feasible.
- **Character Set:** Only lowercase Latin letters, so each root has at most 26 characters.
- **Edge Cases:** Empty strings (root is an empty set) and single-character words.

### Step 2: Multiple Solution Exploration
1. **Brute Force with Sorting:**
   - Convert each word to a sorted string of unique characters.
   - Store these strings in a set and return its size.
   - **Time Complexity:** O(n * (L + 26 log 26)) ≈ O(n * L) due to sorting small sets.
   - **Space Complexity:** O(n * 26) for storing sorted strings.

2. **Optimized with Frozenset:**
   - Represent each root as a frozenset of characters (automatically removes duplicates and is hashable).
   - Store frozensets in a set and return its size.
   - **Time Complexity:** O(n * L) for processing each character.
   - **Space Complexity:** O(n * 26) for storing frozensets.

3. **Bitmask Representation:**
   - Represent each root as a bitmask (integer) where each bit corresponds to a letter.
   - For each word, compute bitmask by OR-ing bits for each character.
   - Store bitmasks in a set and return its size.
   - **Time Complexity:** O(n * L) for processing characters.
   - **Space Complexity:** O(n) for storing integers.

4. **Space-Optimized with Early Termination:**
   - Not beneficial here since roots are small (max 26 characters) and input size is manageable.

### Step 3: Optimal Solution Selection and Implementation
**Selection Rationale:**
- **Frozenset Approach** is optimal due to clarity and efficiency:
  - Directly represents the root as a set of unique characters.
  - Automatically handles permutations and duplicates.
  - Hashable for O(1) set operations.
- **Time and Space:** O(n * L) and O(n * 26) are feasible within constraints (n ≤ 1000, L ≤ 1000).
- **Simplicity:** Concise implementation in Python using set comprehension.

**Implementation:**
```python
class Solution:
    def solve(self, n: int, words: List[str]) -> int:
        return len({frozenset(word) for word in words})
```

**Key Optimization Points:**
- Using `frozenset` for O(1) hashing and automatic duplicate removal.
- Set comprehension for efficient distinct root counting.

### Step 4: Verification and Summary
**Verification with Examples:**
- **Example 1:** Words `["a", "aa", "aaa", "ab", "abb"]` → Roots `{frozenset('a'), frozenset('a','b')}` → Output `2`.
- **Example 2:** Words `["amer", "arem", "mrea"]` → Root `frozenset('a','m','e','r')` → Output `1`.
- **Edge Cases:** 
  - Empty string `[""]` → Root `frozenset()` → Output `1`.
  - Mixed lengths `["", "a"]` → Roots `frozenset(), frozenset('a')` → Output `2`.

**Summary:**
- **Core Insight:** Distinct objects are determined by distinct sets of characters in words.
- **Algorithm:** Convert each word to its set of characters, count distinct sets.
- **Optimization:** Frozenset provides efficient hashing and uniqueness.
- **Further Optimization:** Bitmask could be used for minimal space but offers no significant gain here.