# Solution for abc327_d.md

Generated at: 2025-07-22 06:41:26
Model: deepseek-reasoner

## Reasoning Process

We are given two sequences A and B of length M, and an integer N (the maximum integer in the sequences, and the length of the binary sequence X we are to assign).

Condition: We need to assign a binary value (0/1) to each integer from 1 to N (i.e., build a sequence X of length N) such that for every index i (from 0 to M-1), we have X[A_i] != X[B_i].

Note: The problem is equivalent to building a graph where each node is an integer from 1 to N, and each constraint (A_i, B_i) is an edge. Then we require that the graph is bipartite. Why?
Because the condition X[A_i] != X[B_i] is exactly the condition that adjacent nodes in the graph must have different colors (with the two colors being 0 and 1).

However, note that the graph might have multiple edges and self-loops.

Important cases:
1. Self-loop: If there is an edge (a, a) for some a, then we require X[a] != X[a] -> which is impossible. Hence, if there is any i such that A_i == B_i, then we immediately return "No".

2. Multiple edges: Multiple edges between the same pair of nodes are acceptable because the condition is the same for each edge.

Thus, the problem reduces to:
- Check for any edge i where A_i == B_i -> if found, output "No".
- Else, build a graph with nodes from 1 to N (but note: not all numbers from 1 to N necessarily appear? Actually, the constraints say that the integers are at most N, but we must consider all integers from 1 to N? Actually, the assignment of X must assign to every index from 1 to N. However, the constraints on the graph: we only have edges for the given pairs. But note: the sequence X has length N and must assign a value to every index from 1 to N. The condition only constrains the pairs (A_i, B_i). However, if a node does not appear in any edge, then we can assign it arbitrarily. So the graph may have multiple connected components.

Therefore, we can model the problem as an undirected graph with:
- Nodes: all integers from 1 to N? Actually, we only need to consider nodes that appear in the constraints? But note: the sequence X must assign a value to every integer from 1 to N. However, the constraints only involve the pairs (A_i, B_i). The condition for a node that never appears in A or B is automatically satisfied regardless of the assignment. But we must assign every node. However, the bipartiteness condition of the entire graph (with all nodes from 1 to N) is required? Actually, we can build the graph for the nodes that appear and then also consider the isolated nodes? But isolated nodes do not cause any problem because we can assign either 0 or 1 arbitrarily.

However, note: the graph must be built on the set of nodes that appear in the edges? Actually, we have edges that connect two nodes. But what if a node does not appear in any edge? Then it is isolated and we can assign it arbitrarily. The condition for the edges only applies to the edges we have.

But the problem says: "There exists a sequence X of length N" meaning we assign every index from 1 to N. The constraints only say for each i: X[A_i] != X[B_i]. So if a node is not in any edge, there is no constraint on it.

Therefore, we can build a graph on the set of nodes that appear in at least one edge? Actually, we can build a graph that includes every node from 1 to N? But that would be inefficient if N is large and the graph has few nodes. However, note that M can be up to 200,000 and N can be up to 200,000, so we must build a graph of at most N nodes.

But we can simply build the graph for all nodes from 1 to N? Actually, we don't need to explicitly add isolated nodes. We can build the graph only for the nodes that appear in the edges? But then we must also consider: what if a node is not in the graph? Then we skip it in the BFS/DFS. However, the condition is automatically satisfied for that node. But we must assign it arbitrarily, and that doesn't break any constraint.

But note: the constraints say that the integers in A and B are between 1 and N, so we have to consider all nodes from 1 to N? Actually, no: we only have to assign values for integers from 1 to N. However, the graph we build for the constraints does not need to include nodes that are not mentioned in any edge. Why? Because there is no constraint that involves them. Therefore, we can build a graph only for the nodes that appear in A or B. But note: if a node does not appear in any edge, then we don't need to process it. However, we must note that the graph might not include all nodes from 1 to N, but that is acceptable.

But wait: the problem says that the sequence X has length N and assigns to indices 1..N. However, the constraints only involve the pairs (A_i, B_i). So if a node is not in any edge, we don't care about it. So we can build a graph only for the nodes that appear.

However, we must check for self-loops: we are already checking that if A_i == B_i, then we output "No". 

But what about nodes that do not appear? They are not involved in any constraint, so they are free. Therefore, we only need to check the connected components that have at least one edge.

Steps:
1. For i in range(M):
   if A_i == B_i: return "No"

2. Build an undirected graph. But note: we have M edges. The graph has at most min(N, 2*M) nodes? Actually, the number of distinct nodes is at most 2*M, but also bounded by N.

3. Check if the graph (built from the edges) is bipartite.

But note: what if the same edge appears multiple times? Then we have duplicate edges. We can avoid adding duplicate edges? Actually, duplicate edges are harmless because if we have two identical edges, then when we build the graph we will have multiple edges. However, the condition for bipartite graph does not care about multiple edges: if two nodes are connected by multiple edges, then the condition is the same as one edge. So we can ignore duplicate edges? Actually, we can build the graph without worrying about duplicates? But the graph building: if we have multiple edges, we will have multiple entries in the adjacency list. But then when we do BFS/DFS, we might traverse the same edge multiple times? That is inefficient? We should avoid duplicate edges? Actually, we can use a set for edges? But note: the constraints say M up to 200000, so even if we have duplicate edges, we can skip them? But wait: the condition is about each edge. However, if we have multiple edges between two nodes, then we require the condition multiple times, but the condition is the same as one edge. So we can remove duplicate edges? Actually, no: because if we have two identical edges, then the constraint is the same as having one. But the graph bipartiteness condition only requires that the two nodes are in different colors, regardless of how many edges there are. Therefore, we can remove duplicate edges? Actually, we don't need to. Because if we leave duplicate edges, then when we traverse the graph, we might get multiple edges to the same neighbor. But we can mark visited nodes by the node itself, so that we don't process the same node twice. So duplicate edges are handled naturally: when we traverse from a node, we will see the same neighbor multiple times? But then we might push the same node multiple times into the queue? That would be inefficient.

Therefore, we can build the graph without duplicate edges? Actually, we can use an adjacency list and avoid duplicate edges? But note: the input might have duplicate edges. We can use a set for each node's neighbors? But that would be acceptable: we can use a list of sets? Or we can use a list of lists and then when building, we can avoid adding duplicates? Actually, we don't need to: because the total M is 200000, so the graph will have at most 200000 edges? But if we have duplicates, then the actual edges in the graph are less. However, worst-case there are 200000 distinct edges? So we can use a set per node? Alternatively, we can use an array of lists and then deduplicate? Actually, we can:

Option 1: Build the graph as an array of lists, and then for each node, we can deduplicate the neighbors? But that would require O(degree) per node and total M. Alternatively, we can use a set for each node? But then the total memory might be O(M) but with a constant factor overhead.

Alternatively, we can just build the graph with duplicates and then when traversing, we skip the neighbor if we have already visited it? Actually, in BFS, we mark a node once and then we don't process it again. So even if we have multiple edges to the same neighbor, we will only enqueue the neighbor once (if we haven't visited it). But when we traverse the edges, we will iterate over the same neighbor multiple times. That could be inefficient if there are many duplicates. However, worst-case, the number of edges is M, so the total time for BFS/DFS is O(M). So we can just build the graph with duplicate edges? Actually, worst-case M=200000, and we might have up to 200000 edges (with duplicates) so it's acceptable.

But we must note: if we have a self-loop we already returned "No". So we don't have to worry about self-loops in the graph building.

Therefore, the algorithm:

1. Read N, M and the two lists A and B.
2. For each i from 0 to M-1:
      if A[i] == B[i]:
          print "No" and return.
3. Build an undirected graph: 
      graph = [[] for _ in range(N+1)]   # since nodes are 1-indexed, we use 0..N, but we don't use index0 if N>=1.
   Then for each i:
      u = A[i], v = B[i]
      add v to graph[u]
      add u to graph[v]

4. Then we traverse the graph for each connected component (that has at least one edge) and check bipartiteness.

But note: what if a node does not appear in any edge? Then it won't be in the graph? Actually, we built the graph for nodes that appear. However, we have an array of size N+1. For a node that does not appear, graph[node] will be an empty list, so we skip it? Actually, we must traverse all nodes? But we don't have to: because a node with no edge is isolated and trivially bipartite. However, we must assign it arbitrarily, but that doesn't break the condition.

But in the BFS/DFS, we traverse all nodes from 1 to N? That would be O(N) which is 200000, acceptable. Alternatively, we can only consider nodes that appear? How? We can note that the nodes that appear are the ones that are in A or B. We can create a set of all nodes that appear, and then traverse that set? But then we have to also consider that the graph might have nodes that are not in the set? Actually, we built the graph for all nodes that appear? Actually, we built the graph for nodes that are in A or B. But we built the graph for every index from 1 to N? Actually, we allocated an array of size N+1, and then we added edges only for the nodes that appear. But for a node that does not appear, we have an empty list. Then when we traverse the graph for all nodes from 1 to N, we will skip the nodes with no edges (because they are isolated) and we don't need to do anything. However, we must initialize the color array for all nodes? Actually, we can do:

   color = [-1] * (N+1)   # -1 for unvisited

   Then for i from 1 to N:
        if color[i] == -1 and graph[i] is not empty? Actually, we don't care about the emptiness: we can start BFS/DFS from every unvisited node. But if the node is isolated, we can assign it 0 (or 1) arbitrarily. Then we don't have any conflict. So we can do:

        if color[i] == -1:
            then we start BFS/DFS from i.

   But note: the isolated node: we assign a color (say 0) and then we are done. There is no edge to check.

5. In the BFS/DFS for a connected component:
   - Start with node i, assign color[i] = 0.
   - Then traverse its neighbors. For each neighbor j:
          if color[j] == -1: set color[j] = 1 - color[i], and enqueue it (or DFS).
          else if color[j] == color[i]: then we have a conflict -> return "No"

6. If we complete the BFS/DFS for all connected components without conflict, then return "Yes".

But note: we must check for self-loops at the beginning. Also, we are building the graph for all edges. However, we already checked for self-loops? But what about self-loops that we might add to the graph? We didn't add them because we skipped when A_i==B_i? So we don't have self-loops in the graph.

But what if we have a self-loop that we missed? We already checked: if A_i==B_i, we return "No". So we don't add self-loops.

However, what if we have an edge that is not a self-loop but then the graph has a cycle? We know that a graph is bipartite if and only if it has no odd-length cycles. Our BFS/DFS will detect an odd-length cycle? Actually, if we find an edge connecting two nodes of the same color, that means we have an odd cycle? Yes.

But note: the graph is undirected and we are building an undirected graph.

Therefore, the algorithm:

   if any A_i == B_i: return "No"

   graph = [[] for _ in range(N+1)]
   for i in range(M):
        u = A[i]
        v = B[i]
        graph[u].append(v)
        graph[v].append(u)

   color = [-1] * (N+1)
   for i in range(1, N+1):
        if color[i] == -1:
            # We start BFS/DFS from node i
            queue = collections.deque()
            queue.append(i)
            color[i] = 0
            while queue:
                cur = queue.popleft()
                for neighbor in graph[cur]:
                    if color[neighbor] == -1:
                        color[neighbor] = 1 - color[cur]
                        queue.append(neighbor)
                    else:
                        if color[neighbor] == color[cur]:
                            return "No"

   return "Yes"

But wait: what if a node has no edge? Then we set color[i] = 0, and then we look at its neighbors: there are none, so we skip. Then we move to the next node.

However, note that we are iterating from 1 to N. For a node that is isolated, we set its color and then we don't do anything else. Then we move to the next node. But if we have a node that is isolated and then we set its color, and then we never see it again? That's correct.

But what if the graph is not connected? We are iterating over all nodes, so we will cover every connected component.

However, we are doing BFS for every unvisited node. So this algorithm should work.

But consider performance: 
   - We have N nodes and M edges. The graph building is O(M). The BFS/DFS is O(N+M). Since M and N are up to 200000, this is acceptable.

But note: worst-case, M and N are 200000, so the total operations are about 400000, which is acceptable in Python? We must use efficient BFS and avoid recursion if the graph is large (to avoid recursion depth). We can use iterative BFS or DFS. Since the graph might be large, we use BFS with a deque.

But what about memory? We are storing the graph: an array of lists. The total memory is O(N+M). Since M and N are 200000, the total memory is about 200000 * (some constant) for the array and then each edge stored twice? So about 400000 edges? That's acceptable.

Let's test with examples:

Example 1: 
   Input: N=3, M=2, A=[1,2], B=[2,3]
   Step2: no self-loop -> build graph:
        graph[1] = [2]
        graph[2] = [1,3]
        graph[3] = [2]
   color: initially [-1]*4 -> [ -1, -1, -1, -1] (for indices 0,1,2,3; we care about 1,2,3)

   Start at node1: color[1]=0, then neighbors: node2 -> color[2] is set to 1. Then from node2: neighbors are 1 and 3. Node1 is already colored 0 (which is not 1, so no conflict). Then node3: set color[3]=0. Then check neighbors of 3: which is node2 (colored 1, which is different). So no conflict -> "Yes".

Example 2:
   Input: N=3, M=3, A=[1,2,3], B=[2,3,1]
   Step2: no self-loop? Because A_i != B_i for all? Then build graph:
        graph[1] = [2,3]
        graph[2] = [1,3]
        graph[3] = [2,1]
   Then we start at node1: color[1]=0. Then neighbors: node2 and node3. We set color[2]=1, color[3]=1. Then we process node2: neighbors are 1 (0) and 3 (1). Now, when we look at node2 and node3: we see that the edge (2,3): both are 1 -> conflict? Actually, when we process node2, we look at node3: at that time, node3 is already colored 1. Since node2 is 1 and node3 is 1, we have conflict -> return "No".

But note: we also have the edge (1,3): that is already set to 0 and 1? Actually, when we set node3 to 1 from node1, that is fine. Then when we process node2, we see the edge to node3: same color -> conflict.

Alternatively, we could have started at node1, then set node2=1 and node3=1. Then when we traverse from node2, we see node3: same color? So we return "No".

But what if we set the colors differently? Actually, the order of setting might matter? Let's see:

   We start at node1: color[1]=0.
   Then we enqueue node2 and node3. Then we take node2: color[2]=1, then we look at neighbors: node1 (0, no conflict) and node3 (which is currently 1? But we haven't set node3 from the perspective of node2? Actually, when we set node3 from node1, we set it to 1 and then we enqueued node3. Then when we process node2, we see node3 is already 1. Then we check: color[2]==1 and color[3]==1 -> conflict.

So the algorithm returns "No", as expected.

Example 3:
   Input: "10 1\n1\n1"
   Then we have A=[1], B=[1] -> self-loop: so we return "No".

Example 4:
   Input: 
        N=7, M=8
        A = [1,6,2,7,5,4,2,2]
        B = [3,2,7,2,1,2,3,3]

   First, check self-loops: 
        i0: 1 and 3 -> no
        i1: 6 and 2 -> no
        i2: 2 and 7 -> no
        i3: 7 and 2 -> no
        i4: 5 and 1 -> no
        i5: 4 and 2 -> no
        i6: 2 and 3 -> no
        i7: 2 and 3 -> no -> no self-loop.

   Then build the graph:

        Edges:
          (1,3), (6,2), (2,7), (7,2) [duplicate of (2,7)], (5,1), (4,2), (2,3), (2,3) [duplicate]

        We can build without duplicates? Actually, we are building with duplicates. But we can avoid by using sets? However, as discussed, duplicates are harmless in the BFS because we check the color of the neighbor and we only enqueue if unvisited. But duplicates might cause the same edge to be processed twice? But when we check the neighbor, if it's already visited, we check the color. So if we have two identical edges, then the second time we see the same two nodes, and we check if they have the same color? That might be redundant but acceptable.

        Actually, the graph edges (without duplicates) are: (1,3), (1,5), (2,6), (2,4), (2,7), (2,3), (3,2) [duplicate?] and (3,1) already exists? We have:

          node1: [3, 5, 3]  -> but we can have duplicates: actually, the input has two (1,3) and one (5,1). Also, for node2: [6,7,4,3,3] -> so duplicates.

        We can build without duplicates? Actually, we can use a set per node to avoid duplicate edges? But the problem says M up to 200000, so worst-case the graph has 200000 edges? But if we have many duplicates, we might have many repeated edges. To avoid processing the same edge multiple times (which doesn't break correctness but increases the BFS time) we can deduplicate.

        However, the constraints say M up to 200000, so the total number of edges (including duplicates) is 200000*2? Actually, we add each edge twice? No: we add each pair twice? Actually, for each edge (u,v), we add v to u's list and u to v's list. So if we have duplicate edges in the input, then we will have multiple entries. For example, the input has two edges (2,3): then we add to graph[2]: two 3's, and to graph[3]: two 2's.

        The BFS: when we traverse from node2 to node3, we will check the edge twice? But when we process the first occurrence, we set the color (if unvisited) and then the second occurrence: we see the neighbor is already colored? Then we check the condition. So it's O(duplicates) in time. Since the total M is 200000, the worst-case total edges in the graph is 2*M (if we have no duplicates) but if we have duplicates in the input, then we have more? Actually, the input has M edges, and we add each edge twice. So the total edges in the graph is 2*M. So worst-case 400000 edges, which is acceptable.

        But to avoid duplicate edges in the graph (to reduce the number of edges we traverse) we can use a set per node? Then we build:

          graph = [set() for _ in range(N+1)]   # but then we have to initialize, and then for each edge, we add to the set.

        Then the total edges stored would be at most 2*M (if we have distinct edges) and if there are duplicates, we avoid storing multiple times. This would reduce the BFS time.

        However, worst-case M=200000, so without deduplication we have 400000 edges. With deduplication we have at most 2*M? Actually, the number of distinct edges is at most M? But we store each distinct edge twice? Actually, if we have an edge (u,v) and (v,u) we store it as two entries? Actually, no: we store for u: v, and for v: u. So if we have multiple (u,v) in the input, then we try to add v to u's set multiple times: but the set will store only one copy. Similarly for u in v's set.

        So we can build:

          graph = [set() for _ in range(N+1)]

        Then for each edge (u, v) (with u != v, because we already checked self-loop):
            graph[u].add(v)
            graph[v].add(u)

        Then the graph has each distinct edge stored only once per node.

        But note: if we have an edge (u,v) and then (v,u) in the input, that's the same as one undirected edge. So we only need to store one copy.

        This will reduce the total edges to traverse in BFS. The worst-case distinct edges is M, so the total edges stored is at most 2*M? Actually, for each distinct edge, we store it twice? But the set for u has v, and the set for v has u. So the total storage is 2 * (number of distinct edges). But the distinct edges are at most M.

        So we can do:

          graph = [set() for _ in range(N+1)]
          for i in range(M):
              u = A[i]
              v = B[i]
              if u != v:   # we already skip self-loop? Actually, we checked at the beginning? Actually, we break if we see a self-loop? Or we check all? We must check all self-loops? Actually, if we have one self-loop, we return "No". So we can break early? Or we check all? The problem: we must return "No" if any self-loop exists. So we can check all edges for self-loop first.

        Alternatively, we can do:

          for i in range(M):
              if A[i] == B[i]:
                  print("No")
                  return

          Then we build the graph without self-loops.

        Then we build the graph with sets:

          graph = [set() for _ in range(N+1)]
          for i in range(M):
              u = A[i]
              v = B[i]
              graph[u].add(v)
              graph[v].add(u)

        Then we run BFS.

        But note: if we have an edge (u,v) and then (u,v) again, the set will avoid duplicates.

        This will save memory and time.

        However, worst-case the distinct edges are M (if all edges are distinct) so the total storage is 2*M, which is acceptable.

        But building sets: the cost per add is O(1) average. So total time for building the graph is O(M).

        Then BFS: each distinct edge is traversed twice? Actually, no: in the set representation, we iterate over each neighbor only once. The BFS will traverse each distinct edge once in each direction? Actually, no: we traverse each edge once? Because when we traverse from u to v, we then traverse from v to u? But that's two distinct edges? Actually, the undirected edge (u,v) is stored as u->v and v->u. In BFS, we traverse the edge from u to v (when we are at u) and then from v to u (when we are at v). But when we are at v, we see u and then we skip because u is already visited? Actually, no: if we are at v, we see u, and if u is visited, we check the color. So we do traverse both directions.

        But in the set representation, we only have one entry for each neighbor. So we will only see u once in v's neighbor list? Then we check the condition for the edge (v,u) only once. So the total work in BFS is O(V+E) where E is the number of distinct edges.

        Therefore, we can use sets to avoid duplicate edges in the graph building.

        Alternatively, we can use lists and then remove duplicates? But that would require extra time.

        Given that M is 200000, we can use sets.

        Steps:

          if any self-loop -> "No"

          graph = [set() for _ in range(N+1)]   # we create a list of sets for nodes 0..N, but we use 1..N
          for i in range(M):
              u = A[i]
              v = B[i]
              graph[u].add(v)
              graph[v].add(u)

          Then we do BFS with a queue and a color array.

        But note: the graph might be large? The memory for a set per node: each set has a constant overhead? And the total storage is about O(M). So it's acceptable.

        However, we must note: if we have many isolated nodes, we are creating a set for each node from 1 to N. That is O(N) sets. But N is 200000, which is acceptable.

        But we can do: we only build sets for the nodes that appear? Actually, we don't know which nodes appear? We can build the graph only for nodes that appear? We can:

          nodes = set(A) | set(B)

          Then we build a dictionary: graph = {node: set() for node in nodes}

          But then we have to also consider nodes from 1 to N that are not in the graph? Actually, the condition for the coloring: we must assign colors to all nodes from 1 to N. But the isolated nodes (not in A or B) are free. However, we are iterating over all nodes from 1 to N for the BFS? We have an array "color" of size N+1. But if we build the graph only for the nodes that appear, then for a node that is not in the graph (but in 1..N) we have to skip? Actually, we can set the color for such a node arbitrarily. But we don't need to build an entry in the graph? 

        Alternatively, we can build the graph for all nodes from 1 to N? Then we have an array of sets of size N+1. The memory for the sets: the sets that are not used (isolated nodes) will be empty sets. The memory for an empty set is small. And we have 200000 sets? That might be acceptable? But the overhead of having 200000 sets? Each set in Python has some overhead (like 200 bytes per empty set? or more). Then total memory: 200000 * 200 bytes = 40e6 bytes = 40 MB? Then for non-empty sets, we store the edges. The total memory for the edges is about 2*M * (size of an integer) and the set overhead? But worst-case M=200000, so 400000 integers? and 200000 sets? That might be acceptable in Pyton? But 40 MB for the sets alone? Then the integers: 400000 integers * 28 bytes (if in Python) is about 11.2 MB? So total about 50 MB? which is acceptable.

        But we can avoid the empty sets? We can build the graph only for the nodes that appear? How?

          We can do:

            graph = {}
            for i in range(M):
                u = A[i]
                v = B[i]
                if u not in graph: graph[u] = set()
                if v not in graph: graph[v] = set()
                graph[u].add(v)
                graph[v].add(u)

            Then we also need to know about the isolated nodes? Actually, we don't: because when we iterate over nodes from 1 to N for coloring, if a node is not in the graph dictionary, then we set its color to 0 (or 1) arbitrarily. But note: we have the color array of size N+1. So we can:

            color = [-1] * (N+1)
            for i in range(1, N+1):
                if color[i] != -1:   # already visited? Actually, no: we haven't visited any.
                    continue
                if i not in graph:   # isolated node
                    color[i] = 0
                    continue
                # else, we start BFS

            But wait: what if a node is in the graph? Then we do BFS. But if we use the dictionary, then we can only iterate over the nodes that are in the graph? Actually, we must also include the nodes that are not in the graph? We are iterating from 1 to N.

        Alternatively, we can build an array of sets for all nodes from 1 to N? That might be simpler and faster.

        Let me choose: since N is 200000, we can create an array of sets of size N+1. But if we are concerned about memory, we can also do:

            graph = [set() for _ in range(N+1)]

        and then we only add edges for the given pairs.

        Then we run BFS for all nodes from 1 to N.

        This is straightforward.

        However, we note: if we have a node that does not appear in any edge, then graph[i] is an empty set. Then we set color[i]=0 and move on.

        This is acceptable.

        So we'll do:

            graph = [set() for _ in range(N+1)]

        But note: we are using indices 0..N. We don't use index0.

        Steps:

            import collections

            if M==0: then we don't have any constraint? Then we can return "Yes", but we must assign all nodes arbitrarily. So we return "Yes". However, we also have to check self-loops: if there is any self-loop we return "No", but if M==0, we don't have any self-loop. So we return "Yes". But what if we have no edges? Then we skip the graph building. Then we set color for each node arbitrarily. So we return "Yes".

        Therefore, the algorithm:

            Step1: Check for any self-loop: for i in range(M):
                    if A[i] == B[i]:
                        return "No"

            Step2: Build graph as list of sets for indices 0 to N (we only use 1..N)
                    graph = [set() for _ in range(N+1)]
                    for i in range(M):
                         u = A[i]
                         v = B[i]
                         graph[u].add(v)
                         graph[v].add(u)

            Step3: color = [-1] * (N+1)   # 0-indexed array of length N+1; indices 0 unused? We use 1..N.

            Step4: for i in range(1, N+1):
                     if color[i] == -1:
                         # if the node is isolated, then we set to 0 and skip BFS?
                         # but we can do: we start BFS, and if there are no neighbors, we break immediately.
                         queue = collections.deque([i])
                         color[i] = 0
                         while queue:
                             cur = queue.popleft()
                             for nb in graph[cur]:
                                 if color[nb] == -1:
                                     color[nb] = 1 - color[cur]
                                     queue.append(nb)
                                 elif color[nb] == color[cur]:
                                     return "No"

            Step5: return "Yes"

        But note: we must use a deque. We can import collections.

        However, what if we have a large connected component? The queue might grow to the size of the component, which is acceptable.

        Let me test with example 4: 
            A = [1,6,2,7,5,4,2,2]
            B = [3,2,7,2,1,2,3,3]

            After building the graph (with sets) we have:

                graph[1] = {3,5}   # because edges: (1,3) and (1,5) [from the edge (5,1)]
                graph[3] = {1,2}   # from (1,3) and (2,3) and (2,3) -> so {1,2}
                graph[6] = {2}
                graph[2] = {3,4,6,7}   # from (6,2), (2,7), (4,2), (2,3) twice -> {3,4,6,7}
                graph[7] = {2}
                graph[5] = {1}
                graph[4] = {2}

            Then we do BFS:

                We start with node1? (order: from 1 to 7)

                Start at node1: color[1]=0.
                    neighbors: 3 and 5.
                    color[3]=1, color[5]=1.

                Then we process node3: 
                    neighbors: 1 (0) -> skip? Then 2: color[2]=0 (because 1 - color[3]=1? -> 0)
                Then we process node5: 
                    neighbors: 1 -> already colored 0 -> check: color[5]=1 !=0 -> okay, so nothing to do.

                Then we process node2: 
                    neighbors: 3 (already 1) -> check: color[2]=0, color[3]=1 -> okay.
                    then 4: set color[4]=1
                    then 6: set color[6]=1
                    then 7: set color[7]=1

                Then we process node4: 
                    neighbors: 2 -> color[2]=0, and color[4]=1 -> okay.
                Then node6: 
                    neighbors: 2 -> already colored 0 -> check: 1 vs 0 -> okay.
                Then node7: 
                    neighbors: 2 -> already 0 -> check: 1 vs 0 -> okay.

                So no conflict -> "Yes".

            This matches the example.

        Therefore, we write the code accordingly.

        However, note: the order of BFS might vary? But the algorithm is standard for bipartite checking.

        We'll write the code accordingly.

        Edge Case: M=0 -> then we skip the for loop for edges, and then we set all nodes to 0? and return "Yes".

        Also, if we have duplicate edges, the set will avoid them.

        But note: what if the graph is large? We are iterating over all nodes from 1 to N. But if N is 200000, that's acceptable.

        We'll implement accordingly.

        Let me code accordingly.

        However, we must consider: the worst-case memory for the graph: 
            We have an array of N+1 sets. The total number of elements in all sets is 2*M (because each distinct edge is stored in two sets). So memory is O(N + M). 

        Time: O(N+M)

        This is optimal.

        Code:

            import collections

            class Solution:
                def solve(self, N: int, M: int, A: List[int], B: List[int]) -> str:
                    # Check for any self-loop
                    for i in range(M):
                        if A[i] == B[i]:
                            return "No"
                    
                    # Build graph: list of sets for 0..N (we use 1..N)
                    graph = [set() for _ in range(N+1)]
                    for i in range(M):
                        u = A[i]
                        v = B[i]
                        graph[u].add(v)
                        graph[v].add(u)
                    
                    # Color array: -1: unvisited; 0 and 1: colors.
                    color = [-1] * (N+1)   # 0-indexed; index0 unused.
                    
                    # We'll iterate over nodes 1 to N
                    for i in range(1, N+1):
                        if color[i] == -1:
                            # If the node is isolated, we set to 0 and then skip the BFS? 
                            # Actually, we can set it to 0 and then the BFS will not enqueue any neighbor? 
                            # But we can do BFS: if the node has no neighbor, then the while loop will break.
                            queue = collections.deque()
                            queue.append(i)
                            color[i] = 0
                            while queue:
                                cur = queue.popleft()
                                for nb in graph[cur]:
                                    if color[nb] == -1:
                                        color[nb] = 1 - color[cur]
                                        queue.append(nb)
                                    else:
                                        if color[nb] == color[cur]:
                                            return "No"
                    return "Yes"

        But note: we are using sets, so we are using hash sets. The iteration over neighbors is efficient.

        However, we must note: the graph might be disconnected. We are iterating over all nodes, so we cover all components.

        Let me run the example 2: 
            A=[1,2,3], B=[2,3,1]
            After building graph: 
                graph[1] = {2,3}
                graph[2] = {1,3}
                graph[3] = {1,2}

            Then we start at node1: color[1]=0. Then we enqueue neighbors: 2 and 3. 
            Then we process node2: set color[2]=1. Then we look at its neighbors: 1 (color0, okay) and 3: which is currently -1? Actually, we set color[3] when we enqueued it? Actually, we set color[3] to 1 (because from node1: 1-0=1). Then when we process node2, we see neighbor 3: color[3] is 1, same as node2? -> return "No".

        So it returns "No".

        Example 3: 
            Input: "10 1\n1\n1" -> we have M=1, A[0]=1, B[0]=1 -> self-loop -> return "No".

        Example 1: returns "Yes".

        Example 4: returns "Yes".

        Therefore, we are done.

        But note: we are building a set for every node from 0 to N. We don't use node0? Actually, the nodes are from 1 to N. So we don't use graph[0]. We can skip it? But we built an array of N+1 sets. We can avoid building graph[0] by using range(0, N+1) but then we don't use graph[0]. Alternatively, we can build for range(1, N+1)? But then if we have an edge that includes a node that is 0? We don't, because the constraints say integers from 1 to N. So we can build graph for indices 1..N? Actually, we built for 0..N, but we don't use graph[0]. We can do:

            graph = [set() for _ in range(N+1)]   # indices 0..N

        But we can also do:

            graph = [set()]   # for index0, then we don't use it? Or we can build from 1 to N.

        Actually, we are iterating from 1 to N. So we can build:

            graph = [set() for _ in range(N+1)]   # we use indices 0 to N, but we only use 1..N.

        This is acceptable.

        However, we can also build:

            graph = [[] for _ in range(N+1)]   # but then we have to deduplicate by using set in each BFS? Actually, no: we can use sets in the graph building.

        We already decided to use sets to avoid duplicate edges in the graph.

        Therefore, the code is as above.

        But note: if there are multiple self-loops? We break at the first self-loop? Actually, we break as soon as we see any self-loop. So we don't build the graph. That is efficient.

        However, we can also break early in the self-loop check: we can do:

            for i in range(M):
                if A[i] == B[i]:
                    return "No"

        Then we build the graph.

        Alternatively, we can check for self-loop while building the graph? But then we do the same.

        We'll leave it as is.

        Let me test with the example that has a self-loop in the middle: 

            Input: 
                3 2
                1 2
                2 2   # self-loop

            Then we break at the second edge (i=1) and return "No".

        This is correct.

        We'll write the code accordingly.

        However, note: the constraints M and N up to 200000. The self-loop check is O(M). The graph building is O(M). The BFS is O(N+M). So total time is O(N+M), which is acceptable.

        We'll now write the code.

        Note: we must import collections and List.

        Since the problem uses List, we do:

            from typing import List
            import collections

        Code:

        Note: we must be cautious of the memory: building a list of sets of size N+1. But N=200000, so about 200000 sets. The memory for each set is about 200 bytes? Then 200000*200 bytes = 40 MB? Then the edges: 2*M integers, M=200000 -> 400000 integers. In Python, an integer is about 28 bytes? Then 400000 * 28 = 11.2 MB. Then the color array: N+1 integers -> 200001 * 28 bytes = about 5.6 MB. Total memory: 40+11.2+5.6 = 56.8 MB, which is acceptable.

        We'll implement accordingly.

        Let me write the code.

        One more edge: if N=0? The constraints say N>=1, so we don't have to worry.

        We'll run the examples.

        Example 1: 
            N=3, M=2, A=[1,2], B=[2,3] -> "Yes"

        Example 2: 
            N=3, M=3, A=[1,2,3], B=[2,3,1] -> "No"

        Example 3: 
            N=10, M=1, A=[1], B=[1] -> "No"

        Example 4: 
            N=7, M=8, A=[1,6,2,7,5,4,2,2], B=[3,2,7,2,1,2,3,3] -> "Yes"

        We are confident.

        Code:

        We'll write accordingly.

        But note: we can also use DFS instead of BFS? But the graph might be large, and BFS avoids recursion depth. We use iterative BFS.

        We'll use BFS.

        Implementation:

            from collections import deque
            from typing import List

            class Solution:
                def solve(self, N: int, M: int, A: List[int], B: List[int]) -> str:
                    # Check for self-loop
                    for i in range(M):
                        if A[i] == B[i]:
                            return "No"
                    
                    # Build graph: list of sets for indices 0 to N (we use 1..N)
                    graph = [set() for _ in range(N+1)]
                    for i in range(M):
                        u = A[i]
                        v = B[i]
                        graph[u].add(v)
                        graph[v].add(u)
                    
                    color = [-1] * (N+1)   # 0-indexed, index0 unused
                    
                    # We traverse each node from 1 to N
                    for i in range(1, N+1):
                        if color[i] == -1:
                            color[i] = 0
                            queue = deque([i])
                            while queue:
                                cur = queue.popleft()
                                for neighbor in graph[cur]:
                                    if color[neighbor] == -1:
                                        color[neighbor] = 1 - color[cur]
                                        queue.append(neighbor)
                                    elif color[neighbor] == color[cur]:
                                        return "No"
                    return "Yes"

        Let me run with the examples.

        Example 1: 
            graph[1] = {2}, graph[2] = {1,3}, graph[3] = {2}
            Start at 1: color[1]=0 -> queue: [1]
                cur=1: neighbors: 2 -> color[2]=1, queue=[2]
                cur=2: neighbors: 1 (0 -> skip) and 3: color[3]=0 -> then when we see edge (2,3): we set color[3]=0? But then we check: when we process neighbor 3: we set it to 0? Then we check: from 2: color[2]=1 and we set color[3]=0 -> then no conflict. Then we enqueue 3.
                Then we process 3: neighbors: 2 (color=1) -> no conflict.

            Then return "Yes".

        Example 2: 
            graph[1] = {2,3}, graph[2] = {1,3}, graph[3] = {1,2}
            Start at 1: color[1]=0, then neighbors 2 and 3: set color[2]=1, color[3]=1, and enqueue [2,3].
            Then process 2: neighbors: 1 (0 -> skip) and 3 (color[3]=1) -> conflict? Because color[2]=1 and color[3]=1 -> return "No".

        Example 3: 
            M=1, A[0]=1, B[0]=1 -> returns "No" immediately.

        Example 4: as above -> "Yes".

        So we are good.

        But note: the graph might have multiple connected components? We are iterating over all nodes from 1 to N, so we cover all.

        We'll submit.

        However, we can also use a visited array to avoid the color array? Actually, we are using color array as both visited and color.

        This is efficient.

        We'll write the code accordingly.

        Note: we are building a set for each node. We can avoid sets and use lists and then skip duplicates by using a separate visited set for edges? But that would be more complicated.

        We use sets.

        We'll run on the sample input.

        But note: worst-case performance: building the sets: each add is O(1) on average. Then BFS: each edge is traversed once? Actually, each distinct edge is stored twice? But in BFS, we traverse each edge once? Actually, no: we store the edge (u,v) and (v,u). But in BFS, we traverse from u to v and then from v to u? But when we traverse from u to v, we set the color for v. Then when we traverse from v to u, we check the color? So we do use both. But the total work is O(N+M).

        We are done.

        However, we can try to optimize the memory: we don't need sets if we use a list and then remove duplicates? We can use:

            graph = [[] for _ in range(N+1)]
            for i in range(M):
                u = A[i]
                v = B[i]
                # We avoid self-loop already, so u != v
                # We can avoid duplicate by checking the last element? But that would be O(degree) per insertion -> too slow.
                # Alternatively, we can build the list and then deduplicate each node's list? Then we do:
                #   for i in range(1, N+1):
                #       graph[i] = list(set(graph[i]))
                # That would be O(degree) per node, total O(M). So we can do:

            graph = [[] for _ in range(N+1)]
            for i in range(M):
                u = A[i]
                v = B[i]
                graph[u].append(v)
                graph[v].append(u)
            for i in range(1, N+1):
                graph[i] = list(set(graph[i]))   # deduplicate

            Then we use the same BFS.

        This would avoid the set overhead per node? But the time is O(M) for building the list and then O(M) for deduplication? Actually, the deduplication per node: total over all nodes is O(M) because the total number of edges is O(M) (if we count each distinct edge once). But initially, we have 2*M entries? Then deduplication: the total distinct edges is at most M? So the total work is O(M) for the deduplication? Actually, the sum of distinct degrees is at most 2*M? Actually, the total distinct edges is at most M, so the total distinct edges stored is 2*M? Then the total work to deduplicate is the sum of the degrees? which is 2*M? So it's O(M).

        Then the memory for the graph: we store each distinct edge twice? So 2*M? and the array of lists: N+1 pointers to lists. The memory for the list headers is O(N). The total memory for the lists is O(M). 

        This is the same as using sets? Actually, the sets might have more overhead per element? But in Python, sets are more memory hungry than lists? We don't know. But the problem constraints are 200000, so both are acceptable.

        We'll choose the set method for clarity.

        But if we are concerned about the constant factors, we can use the deduplication with lists. However, the set method is clear and the constant factors are acceptable.

        We'll use the set method.

        Final code:

            from collections import deque
            from typing import List

            class Solution:
                def solve(self, N: int, M: int, A: List[int], B: List[int]) -> str:
                    # Check for any self-loop
                    for i in range(M):
                        if A[i] == B[i]:
                            return "No"

                    graph = [set() for _ in range(N+1)]
                    for i in range(M):
                        u = A[i]
                        v = B[i]
                        # Since we already checked self-loop, u != v
                        graph[u].add(v)
                        graph[v].add(u)
                    
                    color = [-1] * (N+1)   # 0-indexed, index0 unused
                    
                    for node in range(1, N+1):
                        if color[node] == -1:
                            color[node] = 0
                            queue = deque([node])
                            while queue:
                                cur = queue.popleft()
                                for nb in graph[cur]:
                                    if color[nb] == -1:
                                        color[nb] = 1 - color[cur]
                                        queue.append(nb)
                                    elif color[nb] == color[cur]:
                                        return "No"
                    return "Yes"

        We'll run the examples.

        Example 1: 
            A = [1,2], B=[2,3]
            After building graph: 
                graph[1] = {2}
                graph[2] = {1,3}
                graph[3] = {2}
            Then we start at node1: color[1]=0 -> queue=[1]
                cur=1: nb=2 -> color[2]=1, queue=[2]
                cur=2: nbs: 1 (color0 -> skip), 3: color[3]=0 -> queue=[3]
                cur=3: nb=2: color[2]=1 -> no conflict -> return "Yes"

        Example 2:
            A=[1,2,3], B=[2,3,1]
            graph[1] = {2,3}
            graph[2] = {1,3}
            graph[3] = {1,2}
            Start at node1: color[1]=0 -> queue=[1]
                cur=1: nbs: 2 -> color[2]=1, 3 -> color[3]=1, queue=[2,3]
                cur=2: nbs: 1 (0, skip), 3: color[3]=1 -> conflict? because color[2]=1 and color[3]=1 -> return "No"

        Example 3: 
            M=1, A=[1], B=[1] -> "No"

        Example 4: 
            As above -> "Yes"

        Therefore, we are done.

        However, note: the problem says "print" the answer. We return "Yes" or "No".

        We'll submit.

        But note: we must be careful that the graph might have a large star? Then the BFS queue might be large? But that is acceptable.

        We are done.

        Let me test with a small cycle of even length: 
            Input: 
                4 4
                1 2 3 4
                2 3 4 1
            This is a cycle of 4 nodes -> bipartite? 
                graph[1] = {2,4}
                graph[2] = {1,3}
                graph[3] = {2,4}
                graph[4] = {1,3}
            Start at 1: color[1]=0 -> set 2=1, 4=1 -> then from 2: set 3=0 -> then from 4: set 3=0? but wait, 3 is already set to 0? Then when we process 2: we set 3 to 0? Then when we process 4: we see 3? color[3]=0 and color[4]=1 -> no conflict? Then we process 3: we see 2 (1) -> no conflict? and 4 (1) -> no conflict? Then return "Yes".

        This is correct.

        And odd cycle: 
            3 3: [1,2,3] and [2,3,1] -> we already saw: returns "No".

        Therefore, we are confident.

        Code:

        We'll write accordingly.

        Note: We are using sets, so the order of neighbors is arbitrary? But the algorithm does not depend on the order.

        We are done.

        We'll write the code.

## Final Solution

### Approach
The problem reduces to checking if a graph can be bipartitioned, where each node represents an integer from 1 to N, and each pair (A_i, B_i) forms an undirected edge between nodes A_i and B_i. The solution involves the following steps:

1. **Check for Self-Loops**: If any pair (A_i, B_i) has A_i equal to B_i, it's impossible to satisfy X_{A_i} ≠ X_{B_i}, so the answer is immediately "No".
2. **Build the Graph**: Construct an adjacency list using sets to avoid duplicate edges. Each node points to its neighbors without self-loops.
3. **Check Bipartiteness**: Use BFS to traverse each connected component. Assign alternating colors (0 and 1) to adjacent nodes. If any neighbor has the same color as the current node during traversal, the graph is not bipartite, and the answer is "No". If all components are bipartite, the answer is "Yes".

### Solution Code
```python
from collections import deque
from typing import List

class Solution:
    def solve(self, N: int, M: int, A: List[int], B: List[int]) -> str:
        for i in range(M):
            if A[i] == B[i]:
                return "No"
        
        graph = [set() for _ in range(N+1)]
        for i in range(M):
            u = A[i]
            v = B[i]
            graph[u].add(v)
            graph[v].add(u)
        
        color = [-1] * (N+1)
        
        for node in range(1, N+1):
            if color[node] == -1:
                color[node] = 0
                queue = deque([node])
                while queue:
                    cur = queue.popleft()
                    for neighbor in graph[cur]:
                        if color[neighbor] == -1:
                            color[neighbor] = 1 - color[cur]
                            queue.append(neighbor)
                        elif color[neighbor] == color[cur]:
                            return "No"
        return "Yes"
```

### Explanation
1. **Self-Loop Check**: The loop iterates through each pair (A_i, B_i). If any pair has identical values, the function returns "No" immediately because X_{A_i} cannot differ from itself.
2. **Graph Construction**: The graph is built using a list of sets to store neighbors for each node from 1 to N. This ensures no duplicate edges, optimizing subsequent BFS traversal.
3. **Bipartite Check**: For each unvisited node, BFS initializes the node's color to 0. It then processes each neighbor, assigning the opposite color (1 - current color). If a neighbor already has the same color as the current node, the graph is not bipartite, and "No" is returned. If BFS completes without conflicts, "Yes" is returned, indicating all constraints are satisfiable.

This approach efficiently checks the bipartiteness of the graph, leveraging BFS for component traversal and set-based adjacency lists to handle edge deduplication, ensuring optimal performance for large inputs. The time complexity is O(N + M), and space complexity is O(N + M), suitable for the problem constraints.