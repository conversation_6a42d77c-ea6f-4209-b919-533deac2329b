# Solution for abc329_f.md

Generated at: 2025-07-22 06:46:05
Model: deepseek-reasoner

## Reasoning Process

Alright, I'm trying to solve this problem about moving balls between boxes and counting distinct colors after each move. Let me break it down step by step.

First, the problem: We have N boxes, each starting with one ball of color C_i. We need to process Q queries where each query (a, b) means moving all balls from box a to box b and then output the number of distinct colors in box b after the move. Constraints are large (N and Q up to 200,000), so efficiency is critical.

Initial thoughts: The brute force approach would be to simulate each move. For each query, take all balls from box a, add them to box b, and then count distinct colors in box b. But with up to 200,000 queries, and each box could accumulate many balls, counting distinct colors each time by scanning all balls would be O(n) per query, leading to O(n^2) worst-case, which is too slow (40e9 operations worst-case). So we need a smarter way.

Key insight: Instead of moving balls one by one, we can maintain a data structure for each box that tracks the distinct colors and their counts. But even then, merging two boxes and updating distinct color count could be expensive if we do it naively.

Alternative idea: Use a union-find or DSU? But we're moving entire boxes, and after moving, the source box becomes empty. So it's not a typical union. Actually, after moving, box a is empty. So it's more like we're merging box a into box b and then setting a to empty.

Wait, but note: the problem says "move all balls from box a to box b". So box a becomes empty after the move, and box b gains all the balls from a. Also, we have to print the number of distinct colors in box b after the move.

So we need to maintain for each box:
- The set of colors it has? But maintaining a set per box and merging them when moving could be O(n) per operation in worst-case, which is too slow.

Better idea: We can use a "union-find" with a "small to large" merge strategy? That's often used in problems where we merge sets and want to keep the total cost low. The idea is to always merge the smaller set into the larger one. This way, each element is moved at most O(log n) times. The total time over all merges would be O(n log n).

But what exactly would we store? For each box, we need to know the distinct colors. However, if we store a mapping from color to count for each box, then when merging, we can iterate over the smaller set and update the counts. Then, we can also maintain a distinct color count per box.

Let me formalize:

We'll maintain:
- `distinct[b]`: the number of distinct colors in box b.
- `boxes[b]`: a dictionary (or map) that maps color -> count for box b.

But storing a dictionary per box might be heavy in memory, but note that total elements is N (each ball is moved at most O(log N) times). So total memory is O(N log N) worst-case? Actually, with small-to-large, each element is moved at most O(log N) times, so the total operations over all moves would be O(N log N). And each move operation would update the distinct count.

How to update distinct count? When merging two boxes:

1. Take box a and box b. We'll move all balls from a to b.
2. Then set box a as empty (but note: future queries might try to move from a again? But the problem says boxes may be empty. So after moving, a is empty, and then in a future query, if a is the source, we move nothing? Correct.

But wait: after moving, box a becomes empty. But what if a is the destination in a later query? Then we move balls to a again. So we need to handle that. So we can't just discard the box; we need to be able to have an empty box that can later be filled.

But in our data structure, when we move from a to b, we'll update box b and then set a's data to empty. But then if later we move from some other box to a, we can start filling a again.

But note: the query specifies box numbers a and b. We need to handle any box number from 1 to N.

So the plan:

We'll maintain:
- `color_count[i]`: a dictionary for box i, but we can avoid storing for empty boxes? But boxes can become non-empty again. So we need to initialize each box with the initial ball. Then after a move, we set the source to empty.

But when moving, we are moving from a to b. We can do:

- If we use small-to-large: we want to always merge the smaller set into the larger one. But note: we have to update the distinct count for the destination.

But also, we have to update the mapping for the boxes. However, after the move, box a is empty. So we can do:

- We'll have an array (or list) `box_data` of size (N+1) where `box_data[i]` is either:
   - a dictionary: color -> count, and also we can store the distinct count? Or we can store distinct count separately.

Alternatively, we can maintain:
- `counts[i]`: the total number of balls in box i? Actually, we don't need total count, but distinct count per box and the color counts.

But to avoid merging two large sets repeatedly, we use the small-to-large heuristic.

Algorithm for a move (a, b):

1. If the size of box a is greater than box b, we swap a and b? Actually, no: we are moving from a to b. But to minimize the number of moves, we want to merge the smaller set into the larger one. But note: the destination is b. So if a has more balls than b, then we should merge a into b? But then we are moving a to b, so we are forced to put the balls from a into b. However, we can choose to update the data structure so that after the move, box b will have the combined balls, and then we can reassign the box id? Actually, we can use a technique: maintain for each box a pointer to its current "representative". But the problem doesn't require that; we just have to update box b.

But here's the trick: after moving, box a becomes empty. So we can reassign: we can have an array `current_box` that maps a box id to the actual set that holds its balls. But wait, the queries specify box ids. So if we move from a to b, then later if someone tries to move from a again, a is empty. Similarly, if they move to a, then we start filling a again. So we don't need to reassign the box id.

But to optimize merging, we can do:

- Let `s` be the source (box a) and `t` the destination (box b). We want to move the balls from `s` to `t`. We can choose to update `t` by merging the set of `s` into `t`. However, if the size of `s` is greater than `t`, then we do:

   a. First, swap the contents of `s` and `t`? But that would be inefficient. Instead, we can update the destination to be the merged set, but then we have to update the box id mapping? Alternatively, we can change the meaning: after the move, the balls are in `t`. But if `s` is larger, then moving all balls from `s` to `t` would require moving many elements. So to minimize, we can instead move the smaller set to the larger one and then update the box id for `s` to be empty and for `b` to be the merged set. But wait, the problem doesn't let us change the box ids. The next query might refer to box a or box b by their original numbers.

So we cannot change the box id. Therefore, we must update the set for `t` to include the balls from `s`, and then set `s` to empty.

But we can choose to swap the roles: if `s` is bigger than `t`, then we can move `t` into `s` and then assign the set of `s` to `t` and set `s` as empty? But then the balls are in `t`, but we moved from `s` to `t`, so we want the balls to end up in `t`. So if we do:

   - If the size of `s` > size of `t`, then:
        - First, swap the contents of `s` and `t`? Then we are effectively going to move `t` (which now has the original `s` balls) to `s`? That doesn't make sense.

Alternative approach: use a data structure that supports merging of two sets with small-to-large, but without changing the box id. We can do:

   - We maintain for each box a dictionary and a size (number of balls). Also, we maintain the distinct count for the box.

   - For a move (a, b):
        if the size of a is greater than the size of b, then we can merge a into b by iterating over the smaller set (which is b) and then set b to the merged set? But no, because we are moving from a to b. So we must move a to b.

   But if a is bigger, then we have to move many balls. So to minimize the total time, we can do:

        Let's always move from the smaller set to the larger set. But the problem says "move from a to b". So we have to put the balls in b. But we can store the actual data in a separate location and have the box id point to that data. Then, when we move, if the source is larger, we can merge the smaller (b) into the larger (a) and then set b to point to the merged set? But then we are effectively moving the balls from a to b by having b point to the new set? But that would be incorrect because the move is from a to b, so after the move, a is empty. 

   Actually, we can do a trick: maintain an array `box_set` that maps a box id to a set object. But we can reassign the set objects. Specifically:

        We'll have for each box an id that points to the actual set. Initially, each box points to its own set.

        When moving from a to b:
            Let set_a = the set of a, set_b = the set of b.
            If |set_a| > |set_b|, then we merge set_b into set_a, and then set box b to point to set_a, and then set box a to point to an empty set? But then we have moved the balls from a to b? Actually, no: we are moving from a to b, so we want the balls from a to be in b. But if we merge set_b into set_a, then set_a has both a and b, and then we set b to set_a and a to empty. Then the balls from a are in b? They are in the set that b now points to, which is the merged set. But the balls from a are still in that set. So that works.

        Then we also need to update the distinct count for the merged set. But note: we are merging set_b into set_a, but then assigning the merged set to b. Alternatively, we can merge set_a into set_b if set_a is smaller, and if not, we merge set_b into set_a and then assign that set to b and set a to empty.

        Steps:

            Let s = box_set[a]   (the set for a, which we are moving from)
            Let t = box_set[b]   (the set for b)

            We are going to move all balls from s to t. But to minimize the number of element moves, we want to merge the smaller set into the larger one.

            So if |s| < |t|:
                merge s into t: for each color in s, update t's dictionary and update distinct count if needed.
                Then set s = empty set, and box_set[a] = a new empty set? But we don't need to store an empty set, we can set it to None and then when needed, we can initialize an empty set.

            If |s| >= |t|:
                Instead, we merge t into s, then set box_set[b] = s (so b now points to s), and then set box_set[a] to a new empty set. But note: we are moving from a to b, and we want the balls to end up in b. So by having b point to s (which now has both s and t), and a becomes empty, that is correct.

            However, we must update the distinct count for the merged set and also output the distinct count after the move.

        But how do we maintain the distinct count? We can have for each set a variable `distinct` that is the number of distinct colors in that set. And we also have a dictionary (color -> count) for the set.

        When merging two sets, we iterate over the smaller set. For each color in the smaller set:

            old_count = the count in the target set for that color (if any, else 0)
            new_count = old_count + count from the smaller set

            Then, if old_count == 0 and new_count > 0, then distinct_count increases by 1.
            But if old_count > 0 and new_count is still >0, then distinct_count doesn't change.

            Actually, more precisely:

                if old_count == 0: then we are adding a new color -> distinct_count += 1
                if old_count > 0: then distinct_count remains.

            However, we don't need to decrease distinct_count because we are only adding.

        But note: we are only adding, so distinct_count can only increase or stay the same.

        Then, after merging, the distinct count for the merged set is the distinct count of the target set plus the number of colors in the smaller set that were not present in the target set.

        We can compute that as we merge.

        Also, we must update the counts: for each color in the smaller set, we add the count to the target set.

        Then, after the merge, we set the source set to empty and update the destination set to be the merged set.

        But if we merged the destination set (t) into the source set (s) and then reassigned the set for b to be s, then the distinct count for b becomes the distinct count of the merged set.

        So we can do:

            if |s| < |t|:
                for each color, count in s.items():
                    if color in t:
                        # already present
                        t[color] += count
                        # distinct_count doesn't change
                    else:
                        t[color] = count
                        distinct_t += 1   # because new color
                Then set s = {} (empty) and output distinct_t.

            else: # |s| >= |t|
                # we merge t into s, then set b to s
                for each color, count in t.items():
                    if color in s:
                        s[color] += count
                    else:
                        s[color] = count
                        distinct_s += 1   # because new color
                Then set t = {} (but then we assign box_set[b] = s) and set distinct_s = the merged distinct count. But actually, we are moving from a to b, so we want the destination b to have the merged set. So we set box_set[b] = s, and then set box_set[a] = a new empty set (or None). Then output distinct_s (which is the distinct count for s).

        However, in the else branch, we merged t into s, but then we set the set for b to s. Then the distinct count for b is distinct_s (which is now updated). But we have to update the distinct count for s? Actually, we are updating s as we merge.

        But note: we are destroying the set t (merging it into s) and then setting b to point to s. Then we set a to empty.

        But in the else branch, we are effectively moving both the balls from a and the original balls of b into s, and then assigning that set to b. And a becomes empty.

        This is acceptable.

        However, we have to update the global state: each box id points to a set. We have:

            box_set[i] = the set object (a dictionary) for box i? Or we can have an array of dictionaries and an array of distinct counts? But then when we reassign, we can do:

            We maintain:
                size[i]: the size (number of balls) in box i. But we can also get that from the dictionary? Actually, we can store the dictionary and the distinct count per box. But when we reassign the set, we have to update the pointer.

        Alternatively, we can avoid reassigning the set by always updating the destination set. But the small-to-large requires that we reassign the set for the destination to the merged set when we merge the larger into the smaller? Actually, we are not reassigning the set id, we are reassigning which set the box points to.

        So we'll maintain:

            box_data[i] = {
                'dict': dictionary of color counts,
                'distinct': integer, the distinct count for box i,
                'size': integer, the total number of balls in the box (which is the sum of the counts, but we can also maintain it as we add)
            }

        But we don't need the size? Actually, for small-to-large, we need the size to decide which to merge. So we can store the size.

        Steps:

            Let source = a, dest = b.

            If the size of source is 0, then we do nothing? Then output distinct of dest. But the problem says boxes may be empty. So if a is empty, then moving does nothing.

            Otherwise, we compare the size of source and dest.

            Let s_data = box_data[a]
            Let t_data = box_data[b]

            If s_data['size'] > t_data['size']:
                # we want to merge the smaller (b) into the larger (a) but then assign the merged set to b? Actually, no: we are moving a to b. So we want to move the balls from a to b. But if a is larger, we can merge b into a and then set b to point to a's data, and then set a to empty.

                Actually, we can do:

                    # Merge t_data (box b) into s_data (box a): but we are moving a to b, so we want the balls to end up in b. So we can instead swap the contents? Or we can reassign the pointers.

                Specifically:

                    We will merge the set of b (t_data) into the set of a (s_data). Then we set box_data[b] = s_data (so b now points to the merged set). Then we set box_data[a] to a new empty set.

                    But we must update the distinct count and size of the merged set.

                    How to merge: iterate over t_data['dict'] and update s_data? Actually, no: we are merging t_data into s_data? But that would be the opposite of what we want? We want to merge the source (a) and the destination (b) together and put the result in b. But if a is larger, we can merge the destination (b) into the source (a) and then assign that merged set to b. Then the source a becomes empty.

                    Steps:

                        For each color, count in t_data['dict'].items():
                            if color in s_data['dict']:
                                s_data['dict'][color] += count
                            else:
                                s_data['dict'][color] = count
                                s_data['distinct'] += 1
                            # Also, update the total size: s_data['size'] += count

                        But wait: we are moving the balls from a to b. The set of a is already in s_data. And we are adding the balls from b to a? That doesn't make sense because we want to move a to b. Actually, we are moving a to b, so we want to combine the balls of a and b in b. But if a is bigger, we can reassign the set of b to the set of a and then merge the original b into the new b (which was originally a). Then we also need to merge the original b (which we stored in t_data) into the new b (s_data). But that is correct because we are moving from a to b: so we are combining the balls of a and b in b.

                        Actually, we are going to:

                            Move the balls from a to b: so we are adding the balls of a to b. But if a is bigger, we can avoid moving many elements by instead merging the current b into a and then saying that b now is a. Then we set a to empty.

                        This is the standard union-find "small to large" but without path compression.

                        So:

                            We want to move a to b. We have two sets: A (from a) and B (from b). We want the union to be stored in b. If |A| > |B|, then we swap the roles: we let the new b be A (the larger set) and then merge B into A. Then we set a to empty.

                        Therefore, we do:

                            If |A| > |B|:
                                swap the box_data for a and b? But then the move is from a to b: so if we swap, then we are effectively moving b to a? That's not what we want.

                        Alternatively, we can do:

                            If |A| > |B|:
                                # We will merge B into A, then set box b to point to A, and box a to empty.
                                Then we merge the set B (which was the old b) into A (which was a). Then b becomes A (which has A and B). Then we set a to empty.

                            But note: we are moving from a to b. So we are adding A to b (which is B). But if we merge B into A, then we have set A that has both A and B, and then we set b to A. Then we have effectively moved the balls from a to b? Actually, the balls from a (set A) are now in b (which points to A), and the balls from b (set B) are also in b. That is correct.

                        So the steps:

                            If |A| > |B|:
                                # Merge B into A: 
                                for color, count in B's dictionary:
                                    if color in A:
                                        A[color] += count
                                        # distinct unchanged
                                    else:
                                        A[color] = count
                                        A['distinct'] += 1
                                    A['size'] += count
                                Then, set box_data[b] = box_data[a]   # because now b points to the merged set (which was a's set)
                                Then set box_data[a] = a new empty set (with dict={}, distinct=0, size=0)

                                Then output A['distinct'] (which is the distinct count of the merged set)

                            Else (|A| <= |B|):
                                # Merge A into B:
                                for color, count in A's dictionary:
                                    if color in B:
                                        B[color] += count
                                        # distinct unchanged
                                    else:
                                        B[color] = count
                                        B['distinct'] += 1
                                    B['size'] += count
                                Then set box_data[a] = a new empty set? But we don't change box_data[b] because it's already B.

                                Then output B['distinct']

            But note: in the else branch, we merged A into B, and then we set a to empty. We don't change b's pointer because it's already pointing to B.

        However, in the if branch (|A|>|B|), we set box_data[b] = box_data[a]. But after that, we set a to empty. But note: we just set box_data[b] to the set that was a's, and then we set a to empty. That is correct.

        But we have to be cautious: after the move, a should be empty, and b should have the combined set.

        Also, we must update the pointers: in the if branch, we set box_data[b] to the set that was a's. Then we set box_data[a] to empty. Then if in the future, we move from b, we will use the merged set. And if we move to a, we start filling a again.

        However, what if we move from a again? Then we see that a is empty (size=0) so we skip? Actually, we can handle the empty case at the beginning: if the source is empty, then we just output the distinct count of the destination and do nothing.

        But in the if branch, we set a to empty. So next time we move from a, we skip.

        Also, we must consider: when merging, we are destroying the set for the old b? Actually, in the if branch, we merged the old b into a, and then we set b to point to a's set. Then the old b set is no longer referenced? We don't need it.

        Similarly, in the else branch, we merge a into b, and then set a to empty.

        This small-to-large ensures that each ball is moved at most O(log n) times. Why? Because each time a ball is moved, the set it is in must be at least as large as the set it is being merged into? Actually, when we move a set, it is only moved if it is the smaller set. And each move at least doubles the size of the set it is in. So each ball is moved at most O(log n) times.

        Total operations: each move operation, we iterate over the smaller set. The total cost over all moves is O(n log n). Since n is 200000, n log n is about 200000 * 18 = 3.6e6, which is acceptable.

        But note: we have Q up to 200000. But the total cost of all moves is O(n log n) in the worst-case, which is acceptable.

        Now, what about the initial state? Each box has one ball. So for each box, we have:

            box_data[i] = {
                'dict': { C_i: 1 },
                'distinct': 1,
                'size': 1
            }

        Then we process queries.

        Example: Example 1.

        Query 1: move 1 to 2.
            Box1: {1:1}, distinct=1, size=1
            Box2: {1:1}, distinct=1, size=1

            Since size of box1 (1) <= size of box2 (1), we merge box1 into box2.

            For color 1 in box1: 
                in box2, color1 is present, so box2['dict'][1] += 1 -> becomes 2.
                distinct remains 1 (since it was already present)
                size becomes 2.

            Then set box1 to empty: {dict: {}, distinct:0, size:0}
            Output distinct of box2: 1.  Correct.

        Query 2: move 6 to 4.
            Box6: {3:1}, distinct=1, size=1
            Box4: {2:1}, distinct=1, size=1

            Merge 6 into 4: since both size 1, we can merge 6 into 4 (or 4 into 6? but we do if source is smaller or equal, we merge source into dest? Actually, we compare the sizes: both are 1, so we merge box6 (source) into box4 (dest).

            For color3 in box6: not in box4, so box4['dict'][3]=1, distinct becomes 2, size=2.
            Then set box6 to empty.
            Output 2. Correct.

        Query 3: move 5 to 1.
            Box5: {2:1}, distinct=1, size=1
            Box1: empty -> size=0. So we don't need to merge? We can handle separately: if source is empty, then we skip the merge and output the distinct count of the destination. But the destination is box1, which is empty? Actually, no: we are moving to box1, which is currently empty. So we are moving from box5 to box1? But the move: move all balls from box5 to box1. Then box1 becomes the set of box5, and box5 becomes empty.

            How do we handle if the destination is empty? Actually, we can treat an empty set as size=0. So:

                Check source: box5 has size=1, destination box1 has size=0.

                Then we compare: 1>0? Then we do the if branch: because source size (1) > destination size (0). But 1>0 is true.

                Then we merge the destination (box1, which is empty) into the source (box5) and then set the destination (box1) to point to the source's set? Then set the source to empty? 

                Actually, we can handle empty sets without special case? Because if the destination is empty, then we can simply assign the set of the source to the destination and then set source to empty.

            Alternatively, we can check: if destination is empty, we don't need to merge; we can just assign the set of the source to the destination and set source to empty.

            But our algorithm: if source size > destination size, we merge destination into source and then set destination to source and source to empty.

            In this case: source (5) has size=1, destination (1) has size=0. Then:

                We merge destination (box1) into source (box5): but box1 is empty, so no iteration. Then we set box_data[1] = box_data[5] (so box1 now points to the set that was box5's). Then set box_data[5] = new empty set.

                Then output distinct of box1: which is 1. Correct.

            But note: the move is from 5 to 1. After the move, box1 has the ball from 5, and box5 is empty. So that's correct.

        Query 4: move 3 to 6.
            Box3: {1:1}, distinct=1, size=1
            Box6: empty (since we moved in query2). So same as above: we merge the empty set into the source? Then set box6 to the set of box3, and box3 to empty. Output distinct=1. Correct.

        Query5: move 4 to 6.
            Box4: has the set from query2: {2:1, 3:1}, distinct=2, size=2.
            Box6: has the set from query4: {1:1}, distinct=1, size=1.

            Now, we move from 4 to 6: source is 4 (size=2), destination is 6 (size=1). Since 2>1, we do:

                Merge the destination (box6) into the source (box4). 
                Then set box_data[6] = box_data[4] (so box6 now points to the merged set that was box4's) and set box_data[4] to empty.

                How to merge: 
                    Box6 has {1:1}. 
                    For color1 in box6: 
                        Check in box4: currently has {2:1, 3:1}. So color1 is not present -> add it: distinct becomes 3, and set color1=1.
                    Then update size: box4's size becomes 2+1=3.

                Then output distinct=3. Correct.

        So the algorithm works for example 1.

        Example2:

        Input: 
            5 3
            2 4 2 4 2
            3 1
            2 5
            3 2

        Initial boxes:

            Box1: {2} -> distinct=1, size=1
            Box2: {4} -> distinct=1, size=1
            Box3: {2} -> distinct=1, size=1
            Box4: {4} -> distinct=1, size=1
            Box5: {2} -> distinct=1, size=1

        Query1: 3 1 -> move from 3 to 1.
            Box3: {2}, Box1: {2}. 
            Sizes: both 1 -> merge source (3) into dest (1).
                For color2: already in box1 -> box1[2] becomes 1+1=2. distinct remains 1 (since color2 was already present).
                Then set box3 to empty. Output 1.

        Query2: 2 5 -> move from 2 to 5.
            Box2: {4}, Box5: {2}. 
            Sizes: both 1 -> merge source (2) into dest (5).
                For color4: not in box5 -> add it. So box5 becomes {2:1, 4:1}, distinct=2. Output 2.

        Query3: 3 2 -> move from 3 to 2.
            Box3: empty -> so we skip: output distinct of box2. But what is box2? 
            After query1, box3 is empty. Then in query2, we moved from box2 to box5: so after that, box2 is empty? 
            Then in query3: move from box3 (empty) to box2 (empty). 
            So we output distinct of box2: which is 0. Correct.

        So the algorithm works for example2.

        Implementation details:

            We'll create a list for box_data of length N+1 (indexed 1..N).

            Each element is a dictionary with:
                'dict': a dictionary (could be a defaultdict or just a dict) for the color counts.
                'size': total number of balls.
                'distinct': the distinct count.

            But note: we are going to reassign entire sets. In the if branch (when source is larger than destination), we do:

                # Merge the destination's set (which is the old b) into the source's set (which is a)
                # Then set the destination (b) to point to the source's set.
                # Then set the source (a) to an empty set.

            We can create an empty set as: {'dict': {}, 'size':0, 'distinct':0}

            Steps for the move:

                a, b = query

                if a == b: # but the constraint says a != b, so skip.

                if box_data[a]['size'] == 0:
                    # then moving nothing: output box_data[b]['distinct'] and continue.
                    result.append(box_data[b]['distinct'])
                    continue

                if box_data[b]['size'] == 0:
                    # then we can simply move the set: assign box_data[b] = box_data[a] and then set box_data[a] to empty?
                    # But our algorithm: we compare sizes. If source size >0 and dest size=0, then source size>0 and dest size=0 -> source size>dest size? Then we do the if branch: merge dest into source? But that would work: we merge an empty set -> nothing to do. Then set box_data[b] = box_data[a], and set box_data[a] = empty. Then output the distinct of b (which is the distinct of the source set).

                But we can handle uniformly.

            Steps:

                if box_data[a]['size'] == 0:
                    # output box_data[b]['distinct'] and do nothing else.
                    result.append(box_data[b]['distinct'])
                    continue

                if box_data[a]['size'] > box_data[b]['size']:
                    # Merge b into a: 
                    for color, count in box_data[b]['dict'].items():
                        if color in box_data[a]['dict']:
                            box_data[a]['dict'][color] += count
                        else:
                            box_data[a]['dict'][color] = count
                            box_data[a]['distinct'] += 1
                        box_data[a]['size'] += count
                    # Then, set box_data[b] = box_data[a]   # now b points to the merged set (which was a's)
                    # But wait: we are going to set a to empty, but we just updated a. Then we set a to empty? But we want b to have the merged set.

                    Actually, we are going to set box_data[b] to point to the same set that a is pointing to (which we just updated). Then we set a to a new empty set.

                    However, we cannot set box_data[b] to box_data[a] and then set box_data[a] to empty? Because then we lose the merged set? Actually, we want to set box_data[b] to the set that a was pointing to, and then set a to empty.

                    We can do:

                        # First, we update the set of a to include the set of b.
                        # Then, we set box_data[b] = box_data[a]   -> but now both a and b point to the same set? Then when we set a to empty, we don't want to affect b.

                    How? We need to reassign the entire set. Actually, we can do:

                        # We'll create a new set for a? But we are going to set a to empty.

                    Instead, we can do:

                        # Let set_a = box_data[a]
                        # We merge set_b (box_data[b]) into set_a.
                        # Then, we set box_data[b] = set_a   (so b now points to set_a)
                        # Then, we set box_data[a] = a new empty set.

                    But we must also clear the set for b? Actually, after we set box_data[b] = set_a, the old set_b is no longer referenced? Then we can leave it.

                    However, we must update the distinct count and size for set_a.

                    Then output set_a['distinct'].

                Else: # box_data[a]['size'] <= box_data[b]['size']
                    Merge a into b:
                    for color, count in box_data[a]['dict'].items():
                        if color in box_data[b]['dict']:
                            box_data[b]['dict'][color] += count
                        else:
                            box_data[b]['dict'][color] = count
                            box_data[b]['distinct'] += 1
                        box_data[b]['size'] += count
                    Then set box_data[a] = a new empty set? And output box_data[b]['distinct']

            But in the if branch, we have to set box_data[b] = set_a? Actually, no: we already updated set_a to include set_b. Then we want b to point to set_a. But before, b was pointing to set_b. So we have to update the pointer for b to set_a.

            However, in the if branch, we are merging set_b into set_a. Then we want b to now have set_a. So we set box_data[b] = box_data[a] (which is set_a). Then we set box_data[a] to a new empty set.

            But then we lose the set_b? It's okay because we merged it.

            Code for if branch:

                # Merge b into a: 
                for color, count in box_data[b]['dict'].items():
                    if color in box_data[a]['dict']:
                        box_data[a]['dict'][color] += count
                    else:
                        box_data[a]['dict'][color] = count
                        box_data[a]['distinct'] += 1
                    box_data[a]['size'] += count

                # Then, we set box b to point to the set of a
                # But note: we are going to set a to empty, so we want to reassign the set of b to the set of a.
                # However, we cannot just do: box_data[b] = box_data[a] because that would be the same object? Actually, we are storing dictionaries, so we can do:

                # Instead, we can set: 
                box_data[b] = { 
                    'dict': box_data[a]['dict'], 
                    'size': box_data[a]['size'], 
                    'distinct': box_data[a]['distinct'] 
                }? 
                But that would create a new copy? We don't want that. We want box b to point to the same set data as a.

            Actually, we are storing the entire structure in a list. We can have each box_data[i] be a mutable object. Then we can reassign the entire object? But that would be heavy.

        Alternatively, we can represent each box by a pointer to a node. Then we can have:

            class Node:
                __slots__ = ('dict', 'size', 'distinct')

            Then box_data[i] is a Node.

            Then for the if branch:

                # Merge node_b into node_a: 
                for color, count in node_b.dict.items():
                    ... update node_a ...
                Then, set box_data[b] = node_a   # now b points to the same node as a?
                Then set box_data[a] = new Node() for empty? But wait: then node_a is now referenced by both a and b? Then when we set a to empty, we set box_data[a] to a new node, so that's okay.

            But then, after setting box_data[b] = node_a, then both a and b point to the same node? Then when we set box_data[a] to a new empty node, that doesn't affect b. That is correct.

            However, in the if branch, we are merging the set of b (which was node_b) into the set of a (node_a). Then we set b to point to node_a. Then we set a to a new empty node.

            This works.

        But note: we have to be careful for future moves: if we move from a again, it will be empty. And if we move to a, we start filling the new empty node.

        Steps:

            Initialize:
                box_data = [None] * (N+1)
                For i in range(1, N+1):
                    node = Node()
                    node.dict = { C[i-1]: 1 }   # because C is 0-indexed
                    node.size = 1
                    node.distinct = 1
                    box_data[i] = node

            Then for each query (a,b):

                if box_data[a].size == 0:
                    # nothing to move
                    ans = box_data[b].distinct
                    # and then we don't change anything? But what if b is also empty? Then distinct=0.
                    # Then output ans, and move to next.

                elif box_data[a].size > box_data[b].size:
                    # Merge b into a: 
                    node_a = box_data[a]
                    node_b = box_data[b]
                    for color, count in node_b.dict.items():
                        if color in node_a.dict:
                            node_a.dict[color] += count
                        else:
                            node_a.dict[color] = count
                            node_a.distinct += 1
                        node_a.size += count

                    # Then set box_data[b] = node_a   # now b points to the merged set (node_a)
                    box_data[b] = node_a
                    # Set a to a new empty node
                    box_data[a] = Node()   # which is empty: dict={}, size=0, distinct=0
                    ans = node_a.distinct

                else:
                    # Merge a into b
                    node_a = box_data[a]
                    node_b = box_data[b]
                    for color, count in node_a.dict.items():
                        if color in node_b.dict:
                            node_b.dict[color] += count
                        else:
                            node_b.dict[color] = count
                            node_b.distinct += 1
                        node_b.size += count
                    # Then set a to empty
                    box_data[a] = Node()   # empty
                    # b remains pointing to node_b
                    ans = node_b.distinct

            Then output ans.

        But wait: in the if branch, we set box_data[b] to node_a. Then if in the future, we have a move from b, we use node_a. And if we have a move to b, we update node_a? That's okay.

        However, what if the same set is shared by multiple boxes? Actually, after a move, the set that was in a and b is now only in b? But we set a to empty. And we set b to node_a. Then the set is only referenced by b. Then if we move from b later, we move the entire set.

        But note: we might have multiple boxes pointing to the same set? Only if we have moved to multiple boxes without cleaning? Actually, we always set the source to empty after a move. So each set is only pointed to by one box at a time? Actually, no: in the if branch, we set box_data[b] = node_a, and then we set box_data[a] to empty. So only box b points to node_a. Similarly, in the else branch, we set a to empty and leave b pointing to node_b.

        So that's safe.

        But what about the old set of b? In the if branch, we merged the old set of b (node_b) into node_a. Then we set box_data[b] to node_a. Then the old node_b is no longer referenced? So it will be garbage collected. Similarly, in the else branch, we merge node_a into node_b, then set box_data[a] to empty. Then node_a is no longer referenced.

        However, we are iterating over the entire set of the smaller set. But the set might be large. But the total cost over all moves is O(n log n) as argued.

        But note: the dictionary for a set might be large. But we are only iterating over the keys, which is the distinct colors. But worst-case, the distinct colors in a set can be up to the size of the set. But the small-to-large is about the total number of balls, not distinct colors. 

        Actually, we are storing the entire dictionary for the set. The size of the dictionary is the number of distinct colors in that set.

        But the small-to-large: we are comparing the total number of balls? The problem says: "size" is the total number of balls. So we are using the total number of balls for the comparison.

        But when we iterate, we are iterating over the distinct colors (the keys of the dictionary). The number of keys is the distinct count, which is at most the size. And the total cost over all moves is the sum over each ball of the number of times it is moved, multiplied by the work per distinct color? Actually, no: each time we move a set, we iterate over the distinct colors in that set, not the balls. 

        But the distinct count can be as large as the size. So worst-case, we iterate over a set of distinct colors of size s (the size of the set). Then the total cost is O( min(|A|, |B|) ) per move? 

        And by the small-to-large, the total cost is O(n log n) in terms of the distinct colors? Actually, the cost per move is the number of distinct colors in the smaller set. The total number of distinct colors over all moves might be O(n log n)? Actually, each distinct color might be moved O(log n) times, and each time we do O(1) work per distinct color. So total cost O(n log n). 

        But worst-case, the distinct count per set is the same as the size? For example, if all balls are distinct. Then the distinct count is the size. Then the cost per move is the size of the smaller set. And the total cost is O(n log n) as each ball is moved at most O(log n) times? Actually, no: because we are iterating by distinct color. But if each ball is a distinct color, then the distinct count is the same as the size. Then the cost per move is the size of the smaller set. Then the total cost is O(n log n) because each ball is moved at most O(log n) times, and each move of a ball we do O(1) work? Actually, no: when we move a set, we iterate over the distinct colors and update the counts. Each distinct color is updated once, but the distinct color is moved along with all its balls. But the cost per distinct color is O(1) per move. And each distinct color is moved O(log n) times. Since there are at most n distinct colors? Actually, no: because the same color can be in multiple boxes initially. But the colors are from 1 to N, so there are at most N distinct colors? But we have multiple balls of the same color.

        Actually, the total number of distinct color values in the entire input is at most N (because C_i between 1 and N). But each distinct color (say color x) can appear in multiple sets? But we are merging sets. 

        However, the distinct count per set is the number of distinct colors in that set, which is at most the size of the set.

        But the total cost: each time we move a set, we iterate over the distinct colors in the set. The total over all moves: each distinct color (meaning each unique color value) might appear in many sets? Actually, no: each ball is moved at most O(log n) times. But a color value might be present in multiple balls that are in the same set? Then when we move the set, we iterate over the distinct colors, so each distinct color in the set is processed once per move of the set. 

        But the same color value might be moved multiple times? For example, if we have 10 balls of color1, and they are all in the same set, then when we move the set, we iterate over the distinct colors: we see color1 once. Then when the set is merged into a larger set, we do one update for color1.

        So the total cost per distinct color (in the entire set of balls) is O(log n) times? Actually, each time a set containing that distinct color is moved (and the set is the smaller one in a merge), we do one operation. And the distinct color is moved only when the set that contains it is merged. And each time the set is merged, if it's the smaller one, then the distinct color is moved. And the set that contains the distinct color might be merged O(log n) times. 

        Therefore, the total cost is O( (number of distinct colors) * log n )? But the number of distinct colors in the entire input is at most N. So total cost O(n log n). Which is acceptable.

        But worst-case, we have 200000 distinct colors? Then 200000 * log2(200000) ≈ 200000 * 18 = 3.6e6, which is acceptable.

        However, note: we are iterating over the distinct colors of the smaller set. The total number of distinct colors in the smaller sets over all moves is O(n log n)? Actually, each distinct color is processed at most O(log n) times (because each time it is in a smaller set and then moved to a set at least twice as large). So total operations is O(n log n).

        So we are good.

        Implementation:

            We'll define a class for the Node, but we can also use a dictionary. However, for efficiency, we can use __slots__.

            Alternatively, we can avoid a class and just use a list or tuple? But we need mutable.

            We'll create a simple class:

            class Box:
                __slots__ = ('counts', 'size', 'distinct')
                def __init__(self, color=None):
                    if color is not None:
                        self.counts = {color: 1}
                        self.size = 1
                        self.distinct = 1
                    else:
                        self.counts = {}
                        self.size = 0
                        self.distinct = 0

            But then we have to create two types: for initial boxes and for empty.

            Alternatively, we can have:

                class Box:
                    __slots__ = ('counts', 'size', 'distinct')
                    def __init__(self):
                        self.counts = {}
                        self.size = 0
                        self.distinct = 0

            Then we initialize each box i with:
                b = Box()
                b.counts[C_i] = 1
                b.size = 1
                b.distinct = 1

            Then for each query:

                a, b = query
                box_a = boxes[a]
                box_b = boxes[b]

                if box_a.size == 0:
                    # output box_b.distinct and continue
                elif box_a.size > box_b.size:
                    # merge box_b into box_a
                    for color, count in box_b.counts.items():
                        # update box_a
                        if color in box_a.counts:
                            box_a.counts[color] += count
                        else:
                            box_a.counts[color] = count
                            box_a.distinct += 1
                        box_a.size += count
                    # Then set boxes[b] = box_a
                    boxes[b] = box_a
                    # Then set boxes[a] to a new empty box?
                    boxes[a] = Box()   # empty
                    ans = box_a.distinct
                else:
                    for color, count in box_a.counts.items():
                        if color in box_b.counts:
                            box_b.counts[color] += count
                        else:
                            box_b.counts[color] = count
                            box_b.distinct += 1
                        box_b.size += count
                    boxes[a] = Box()   # empty
                    ans = box_b.distinct

            Then output ans.

        But note: after a move, the source box becomes empty, so we set it to an empty Box.

        However, we are creating a new Box for the empty set. That's O(1) per move, so total Q moves, which is 200000, so acceptable.

        But memory: each empty box is a Box object with an empty dict. The dict is empty so it's small. But we have N boxes. Initially we created N boxes. Then each move creates one or two new Boxes? Actually, we create one new Box for the source when we empty it. But the source becomes empty, so we set it to a new Box. Then the old Box object (if it was the one we merged) is no longer referenced? Then garbage collected.

        So the total number of Box objects is O(n) at any time? Because we have n boxes. Each box has one Box object. So memory O(n).

        But the dictionaries: the total memory for dictionaries is the total distinct colors over all sets? But we are only having one set per non-empty box. And after moves, we have at most n boxes, but only a few non-empty? Actually, the sets are merged, so the total number of non-empty boxes is decreasing? Actually, no: because we can move to an empty box. So the number of non-empty boxes is at most n? But the sets are stored per non-empty box. And the total memory is the sum of distinct colors in each set. But the sets are disjoint? Actually, no: we are merging, so the sets are not stored in multiple non-empty boxes. Actually, each ball is in exactly one set. So the total memory for the dictionaries is O(n) (each ball is stored in one dictionary, and each distinct color in a set is stored once). But the dictionary for a set has an entry per distinct color in that set. The total over all sets: the sum of distinct colors over sets is not the same as the distinct colors overall? Actually, the sets are disjoint? No, they are merged. There is only one set per non-empty box. And the non-empty boxes are disjoint? Actually, the balls are partitioned among the non-empty boxes. So the total memory for all dictionaries is O(n) (since each ball is stored once, and each distinct color in a set is stored once, and the distinct colors across sets are disjoint? Actually, no: a color can appear in multiple sets? But the balls of the same color can be in the same set? Actually, the sets are disjoint in terms of balls. But the same color value can appear in multiple sets? Yes, if they are in different sets. But when we merge, we combine them.

        Actually, the entire set of balls is partitioned into non-empty boxes. So the total memory for dictionaries is the sum over non-empty boxes of (number of distinct colors in that box). The sum of distinct colors over boxes is at most the total distinct colors in the entire set? Actually, no: the same color can be in multiple boxes? But if a color is in two different boxes, it counts as distinct in both. Then the total over boxes of distinct counts is greater than the overall distinct. But we are not storing all boxes: we store each non-empty box. And the balls are partitioned, so each ball is in one box. Then the total distinct colors over all boxes is the same as the distinct colors in the entire set? Actually, no: if a color appears in multiple boxes, then it is counted in each box. So the sum of distinct counts over boxes is at least the overall distinct. And it can be as large as O(n) per box? 

        But we are storing only the non-empty boxes. And the total number of balls is n. The total distinct colors in the entire set is at most n. But the sum over boxes of distinct counts can be more than n? 

        Example: two boxes, each has color1: then each box has distinct=1, so total sum=2. But the entire set has distinct=1.

        So the total memory is the sum of distinct counts over non-empty boxes. And the non-empty boxes are disjoint? Actually, the balls are disjoint, but the same color can be in multiple boxes. So the total memory is O(n) (because each distinct occurrence of a color in a box is stored once) but the same color in different boxes are stored separately. 

        However, after moves, the sets are merged and then the same color in the same box is stored once. But when the sets are separate, they store the same color separately. So worst-case, we have many boxes with one ball, then the total memory is O(n). Then after moves, we merge and the memory decreases? Actually, when we merge, we combine the dictionaries and then the old dictionaries are garbage collected. So the total memory at any time is the current distinct counts in each non-empty box. The sum of distinct counts over non-empty boxes is at most the total distinct colors in the entire set? Actually, no: because the entire set of balls has a fixed set of colors. But if the same color appears in multiple boxes, then it is counted multiple times. So worst-case, if we never merge the same color, then the sum is the total number of balls? But we do merge: when we merge two boxes, we combine the same colors. 

        Actually, the worst-case memory is O(n) because the total number of distinct colors in the entire set is at most n (because the colors are integers from 1 to n). But each distinct color can appear in multiple sets? Actually, no: after we merge, the same color in the same set is stored once. But if the same color appears in two different sets (not merged yet), then it is stored twice. So the total memory is the sum of distinct colors per set. And the sum over sets of distinct colors is at most the total distinct colors in the entire set? Actually, no: the entire set of balls has at most n distinct colors? But we have n balls, and colors from 1 to n, so the entire set can have up to n distinct colors. But the same color can be present in multiple sets (if the balls of that color are in different sets). Then the distinct count per set is the number of distinct colors in that set, and the sum over sets of distinct counts is the total number of distinct color occurrences per set. And that can be O(n) because each ball has a color, and each distinct color in a set is counted once per set it appears in. But worst-case, if we have n boxes, each with one ball, then the total distinct counts is n. Then after merging, it becomes the distinct colors in the merged set. 

        Actually, the total memory for all dictionaries is the sum of distinct counts in each non-empty box. And the distinct count for a box is at most the size of the box. And the sum of sizes is n (the total balls). But the distinct count for a box is at most the size. So the sum of distinct counts over boxes is at most n? 

        Example: two boxes, each with k balls of one color. Then distinct count for each box is 1. Sum=2. But the total balls is 2k. So the sum of distinct counts is not bounded by n? 

        Actually, the total balls is n. The distinct count for a box is at most the size of the box. So the sum over boxes of distinct counts is at most the sum over boxes of the size of the box? Which is n. Because each ball is in one box. But the distinct count for a box is the number of distinct colors in that box, and it is at most the size. So the total sum of distinct counts over boxes is at most n? Actually, no: consider two boxes, each has two balls: 
            Box1: {1,2} -> distinct=2
            Box2: {3,4} -> distinct=2
            Total distinct counts = 4, total balls=4. So the sum of distinct counts is 4, which equals n.

        So the total memory for the dictionaries (the sum of distinct counts over non-empty boxes) is at most n? Because each ball is in one box, and the distinct count for a box is the number of distinct colors in that box, and each distinct color in a box corresponds to at least one ball. So the sum of distinct counts over boxes is at most n. 

        Therefore, the total memory is O(n).

        Implementation:

            We'll create an array `boxes` of length (N+1) of Box objects.

            Initialize:
                boxes = [None] * (N+1)
                for i in range(1, N+1):
                    boxes[i] = Box()
                    boxes[i].counts = {C[i-1]: 1}
                    boxes[i].size = 1
                    boxes[i].distinct = 1

            Then process queries.

        But note: the input C is 0-indexed? The problem: box i contains C_i. So for box1, color C_0? Actually, the input: 
            "C_1 C_2 ... C_N"

            So the first color is for box1.

            So we can do:

                colors = list of integers, index 0 to N-1: 
                    box i has color = colors[i-1]? Actually, the input:

                    N Q
                    C1 C2 ... CN

                So the first color is for box1, then box2, etc.

        So for i in range(1, N+1):
            color = C[i-1]   # because C is a list of length N: index0 to N-1.

        Then process queries.

        Let's test with example: 
            Example1: C = [1,1,1,2,2,3] -> for box1: C0=1, box2: C1=1, etc.

        Code for the Box class:

            class Box:
                __slots__ = ('counts', 'size', 'distinct')
                def __init__(self, color=None):
                    if color is not None:
                        self.counts = {color: 1}
                        self.size = 1
                        self.distinct = 1
                    else:
                        self.counts = {}
                        self.size = 0
                        self.distinct = 0

            Then we can initialize: 
                boxes = [0] * (N+1)
                for i in range(1, N+1):
                    boxes[i] = Box(C[i-1])

            But then if we have to create an empty box, we do: Box() -> which sets to empty. Then we can also have a method to reset? But we don't need.

        Alternatively, we can do without the color in the constructor: 

            class Box:
                __slots__ = ('counts', 'size', 'distinct')
                def __init__(self):
                    self.counts = {}
                    self.size = 0
                    self.distinct = 0

            Then for initial boxes:
                for i in range(1, N+1):
                    boxes[i] = Box()
                    boxes[i].counts[C[i-1]] = 1
                    boxes[i].size = 1
                    boxes[i].distinct = 1

        Either way.

        We'll go with the second to avoid having two constructors.

        Now, code:

            import sys

            class Box:
                __slots__ = ('counts', 'size', 'distinct')
                def __init__(self):
                    self.counts = {}
                    self.size = 0
                    self.distinct = 0

            def main():
                data = sys.stdin.read().split()
                if not data: return
                it = iter(data)
                n = int(next(it)); q = int(next(it))
                C = [int(next(it)) for _ in range(n)]
                queries = []
                for i in range(q):
                    a = int(next(it)); b = int(next(it))
                    queries.append((a, b))

                boxes = [None] * (n+1)
                for i in range(1, n+1):
                    b = Box()
                    b.counts[C[i-1]] = 1
                    b.size = 1
                    b.distinct = 1
                    boxes[i] = b

                ans = []
                for (a, b) in queries:
                    if a == b: 
                        # shouldn't happen, but skip
                        # but problem says a != b
                        continue
                    boxA = boxes[a]
                    boxB = boxes[b]
                    if boxA.size == 0:
                        # nothing to move, output boxB.distinct
                        ans.append(str(boxB.distinct))
                    elif boxA.size > boxB.size:
                        # merge boxB into boxA
                        for color, count in boxB.counts.items():
                            if color in boxA.counts:
                                boxA.counts[color] += count
                            else:
                                boxA.counts[color] = count
                                boxA.distinct += 1
                            boxA.size += count
                        # then set boxes[b] = boxA, and reset boxes[a] to empty
                        boxes[b] = boxA
                        # create a new empty box for a
                        boxes[a] = Box()
                        ans.append(str(boxA.distinct))
                    else:
                        for color, count in boxA.counts.items():
                            if color in boxB.counts:
                                boxB.counts[color] += count
                            else:
                                boxB.counts[color] = count
                                boxB.distinct += 1
                            boxB.size += count
                        boxes[a] = Box()
                        ans.append(str(boxB.distinct))

                # Output each ans in separate line.
                print("\n".join(ans))

        But wait: in the if branch, we update boxA, but then we set boxes[b] = boxA. But what if boxB was the same as boxA? No, because we are in a move from a to b, and a and b are different. But what if a previous move set boxes[b] to point to a set that is the same as boxes[a]? Actually, we set boxes[a] to a new empty box at the end. So after the move, a is empty and b points to the merged set.

        Also, note: in the if branch, we are iterating over boxB, but we are going to set boxes[b] to boxA. Then the old boxB is no longer referenced? Then we can iterate safely because we are not changing the reference for boxB until after the iteration.

        Similarly, in the else branch, we iterate over boxA and then set boxes[a] to a new empty box. The old boxA is no longer referenced.

        However, what if the move is: move 1 to 2, then later move 2 to 1? 

            Initially: boxes[1] and boxes[2] are both non-empty.

            Query1: (1,2): 
                Suppose box1.size <= box2.size -> we merge 1 into 2.
                Then we set boxes[1] to empty.

            Then query2: (2,1):
                Now, boxes[2] is the merged set (which we stored in the else branch for query1). 
                Then in query2: we move from 2 to 1: 
                    box2 is non-empty, box1 is empty.
                    Then we do: 
                        if box2.size > box1.size: (because box1.size=0) -> true.
                        Then we merge box1 (empty) into box2: nothing to do.
                        Then set boxes[1] = box2? 
                        Then set boxes[2] = new empty? 

                But then we output box2's distinct? Actually, after the merge, we set boxes[1] = box2 (the merged set) and boxes[2] = new empty. Then the distinct we output is box2's distinct (which is the merged set). Correct.

        So the code should work.

        But note: we are creating a new Box for each empty set. That's acceptable.

        However, worst-case, we might create up to Q new Boxes? Because each move creates one new Box (for the source). And Q can be 200000. So total Boxes: initially N, then Q more, so total N+Q which is 400000, acceptable.

        Let me run the example2 to see:

            Query1: (3,1): 
                Box3: size=1, Box1: size=1 -> else branch: merge 3 into 1? 
                Then we iterate over box3: color=2. 
                In box1: already has color2? yes -> then box1.counts[2] +=1 -> becomes 2. distinct unchanged. size=2.
                Then set boxes[3] = new empty.
                Output 1.

            Query2: (2,5): 
                Box2: size=1, Box5: size=1 -> else branch? 
                Merge 2 into 5: 
                    color=4 in box2: not in box5 (which has color2) -> so add it. 
                    Then box5: counts={2:1,4:1}, distinct=2, size=2.
                Set boxes[2]=empty, output 2.

            Query3: (3,2): 
                Box3: empty -> output distinct of box2, which is 0? But box2 was set to empty in query2? 
                In query2, we set boxes[2] to a new empty box. 
                So box2: size=0. Then we output box2.distinct =0. Correct.

        So it works.

        But note: in the if branch, after we merge, we set boxes[b] = boxA. Then if boxB was a set that we are going to use later? But we just merged it, so we don't need it. And we set boxes[a] to a new empty, so we don't reference the old set of a? Actually, we updated boxA (which is the set of a) to include the set of b. Then we set b to point to that set. Then we set a to empty. So we don't need the old set of b anymore.

        One more edge: moving from an empty source: we output the distinct of the destination. But the destination might be empty? Then distinct=0. Correct.

        But what if the destination is empty? Then in the if branch, we set boxes[b] = boxA (which is the set of a). Then we set a to empty. Then b is now non-empty. Correct.

        We'll run the example: move 1 to 2, then move 3 to 2, then move 2 to 4 (which is empty).

        Initially:
            Box1: {1} -> size1
            Box2: {2} -> size1
            Box3: {3} -> size1
            Box4: {4} -> size1

        Query1: (1,2): 
            Box1.size=1, Box2.size=1 -> else: merge 1 into 2.
                Box2 becomes: {1:1,2:1}? Actually, no: 
                    Box1 has color1: in box2? no -> so add it: box2.counts[1]=1, distinct=2, size=2.
                Then set box1 to empty.

        Query2: (3,2):
            Box3: size=1, Box2: size=2 -> if: no, else: merge 3 into 2: 
                Box3: color3 -> not in box2: so box2.counts[3]=1, distinct=3, size=3.
                Set box3 to empty.

        Query3: (2,4): 
            Box2: size=3, Box4: size=1 -> if: true (3>1), so merge box4 into box2.
                Box4: color4 -> not in box2: so box2.counts[4]=1, distinct=4, size=4.
                Then set boxes[4] = box2 (so box4 now points to the merged set) and set boxes[2] = empty.

            Output 4.

        Then if we move from 4 to 5: 
            Box4: size=4, Box5: size=1 -> if: true (4>1), so merge box5 into box4's set (which is the same as box2's set in the past, but now box4 points to it). 
            Then we merge the set of box5 (which has initial color5) into the set of box4: 
                color5: not present -> add it. distinct=5, size=5.
            Then set boxes[5] = box4 (which is the merged set) and set boxes[4] to empty.

        Correct.

        We'll code accordingly.

        However, note: we are using a dictionary per set. The worst-case time for one merge is O(min(size_a, size_b))? But the worst-case size of the smaller set can be large. But the total cost is O(n log n). 

        But worst-case, one merge might be O(n). But if we have a chain of merges, the total worst-case for one query is O(n),

## Final Solution

