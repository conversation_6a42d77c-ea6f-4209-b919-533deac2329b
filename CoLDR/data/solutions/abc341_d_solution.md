# Solution for abc341_d.md

Generated at: 2025-07-22 06:55:48
Model: deepseek-reasoner

## Reasoning Process

We are given three integers: N, M, K. We need to find the K-th smallest positive integer that is divisible by exactly one of N or M.

Observations:
- A number divisible by exactly one of N or M means:
   - It is divisible by N and not by M, OR
   - It is divisible by M and not by N.
- Note that if a number is divisible by both (i.e., by LCM(N, M)), then it is excluded.

We can break the numbers we want into two disjoint sets:
   A = { x | x is divisible by N and not by M }
   B = { x | x is divisible by M and not by N }

The entire set of numbers we are interested in is the union of A and B.

But note: A and B are disjoint because:
   - If a number is in A, it is divisible by N and not divisible by M -> so it cannot be divisible by M, hence not in B.
   - Similarly, a number in B is divisible by M and not by N, so it cannot be in A.

Therefore, the total count of numbers <= X that are in the set is:
   F(X) = (count of numbers <= X divisible by N but not by M) 
          + (count of numbers <= X divisible by M but not by N)

We can express:
   count of numbers <= X divisible by N = floor(X / N)
   count of numbers <= X divisible by M = floor(X / M)
   count of numbers <= X divisible by both N and M = floor(X / L), where L = LCM(N, M) = (N * M) / GCD(N, M)

Then:
   count of numbers <= X divisible by N and not by M = floor(X / N) - floor(X / L)
   count of numbers <= X divisible by M and not by N = floor(X / M) - floor(X / L)

So:
   F(X) = (floor(X / N) + floor(X / M)) - 2 * floor(X / L)

But note: the above formula counts the numbers that are divisible by exactly one. Why subtract twice the LCM?
   Because the set of numbers divisible by both (the LCM) is subtracted once from the multiples of N (to get the ones not divisible by M) and once from the multiples of M (to get the ones not divisible by N).

Alternatively, we can think:
   Total numbers divisible by N or M = floor(X/N) + floor(X/M) - floor(X/L)   [by inclusion exclusion]
   However, note that we want numbers divisible by exactly one. So we subtract the numbers divisible by both (which are counted in both) and then subtract them again? Actually:

   We have:
      Total numbers divisible by at least one = |A ∪ B| + |A ∩ B|? Actually, no: A and B are disjoint? Actually, no: the numbers divisible by both are in neither A nor B? 

Wait, let me clarify:
   We are defining:
      A: divisible by N and not by M -> excludes the LCM
      B: divisible by M and not by N -> excludes the LCM

   Therefore, the set we are counting is disjoint from the set of multiples of LCM (for the purpose of being in both A and B, but note: the multiples of LCM are not included in A or B). 

But the inclusion-exclusion for the set we want (exactly one) is:
   |A| = |multiples of N| - |multiples of LCM|
   |B| = |multiples of M| - |multiples of LCM|
   So total = |A| + |B| = |multiples of N| + |multiples of M| - 2 * |multiples of LCM|

Therefore, F(X) = floor(X/N) + floor(X/M) - 2 * floor(X/L)

Now, the problem reduces to: we want the smallest X such that F(X) >= K.

This is a typical binary search problem. We can use binary search over X (the candidate answer). The candidate X can be as large as?
   Since K can be up to 10^10 and N, M up to 10^8, the answer might be very large.

Estimate: 
   In the worst case, if N=1 and M=2, then the set we are counting is all odd numbers? 
      Actually: 
        Numbers divisible by exactly one of 1 and 2: 
          divisible by 1: every number -> but we have to subtract those divisible by 2? 
          So: divisible by 1 and not by 2: the odd numbers. 
          divisible by 2 and not by 1: none (because every number is divisible by 1). 
        So the set is the odd numbers: 1, 3, 5, ... 
        The K-th odd number is 2*K - 1.

But in worst-case, the answer might be as large as, for example, when N and M are large and close, then the LCM is about N*M (if coprime) but we have to account for K up to 10^10.

How big can X be?
   Note: F(X) = X/N + X/M - 2*X/(L)  (ignoring floors, which don't affect the asymptotic)

   Since L = LCM(N, M) = N*M / gcd(N,M) >= max(N,M) and <= N*M.

   The function F(X) is roughly (if we ignore the floor) X*(1/N + 1/M - 2/(L)). 

   The term (1/N + 1/M - 2/(L)) can be very small if N and M are large and close (so that gcd is large, making LCM = N*M/gcd, which is then about N if M is about N). 

   Actually, let g = gcd(N,M), then:
        L = (N * M) / g.
        Then: 1/N + 1/M - 2/L = (M + N)/(N*M) - 2*g/(N*M) = (N + M - 2*g) / (N*M)

   The worst-case for the coefficient being small is when N and M are large and g is large (so that N+M-2g is small). 

   However, note that K can be up to 10^10. So we need an upper bound for X.

   The minimal value of the coefficient (1/N+1/M-2/L) when N and M are large? 
        Since N and M are at least 1, the minimal positive coefficient? Actually, the coefficient is always positive? 
        Because: 
          We have: 
            1/N + 1/M - 2/(L) = (M + N)/(N*M) - 2/(L)
          = (N+M) * g/(N*M*g) - 2*g/(N*M*g)   [multiplying numerator and denominator by g? Actually, let me do it differently]

        Alternatively: 
          Since L is the LCM, we know that L is divisible by both N and M, so L>=max(N,M). Then 1/L <= 1/max(N,M). 
          Also, 1/N+1/M >= 2/max(N,M) (if N and M are equal? but they are different). Actually, if N and M are distinct, but we can have one much larger than the other.

        Actually, the expression (1/N+1/M-2/L) is at least 1/(N*M) in worst-case? 

        However, note: 
          Let g = gcd(N,M). Then:
            L = N*M/g, so 2/L = 2g/(N*M)
            Then: 1/N+1/M = (N+M)/(N*M) = (N+M)*g/(N*M*g) ? Not exactly. 

        Actually, we have:
          1/N + 1/M - 2/L = (M + N)/(N*M) - 2*g/(N*M) = (N + M - 2*g) / (N*M)

        Now, note that N and M are at least 1, and g is at least 1. The numerator (N+M-2g) is at least 1? 
          For example, if N=1, M=2, g=1: then 1+2-2=1 -> positive.
          If N=2, M=4, g=2: then 2+4-4=2 -> positive.

        Actually, because g <= min(N,M), we have:
          N+M-2g >= max(N,M) + min(N,M) - 2*min(N,M) = max(N,M) - min(N,M) >= 1 (since N != M).

        Therefore, the coefficient is at least 1/(N*M) and at least 1/(max(N,M)*max(N,M))? Actually, the denominator is N*M, which can be as large as 10^16, so the coefficient can be as small as about 1e-16.

        Then to get F(X) >= K, we might need X as large as about K * (N*M) / (N+M-2g). 

        Since K can be 10^10 and N*M can be 10^16, then X can be as large as 10^26? But that is too big for binary search? 

        However, note: we have an alternative expression: 
          F(X) = (X//N + X//M) - 2 * (X//L)

        But the problem constraints: K up to 10^10, and N, M up to 10^8.

        Actually, we can set an upper bound for X: 
          Since we are counting at least the multiples of the larger one? 
          For example, the set includes all multiples of the larger one that are not divisible by the smaller one? But that might be sparse.

        Alternatively, note that the set we are counting includes at least the multiples of N that are not divisible by M and the multiples of M that are not divisible by N. 
        The total number of multiples of N up to X is X//N, and similarly for M. 

        So F(X) <= X//N + X//M <= X/min(N,M) + X/min(N,M) = 2 * X / min(N,M)

        Therefore, to have F(X) >= K, we must have:
          2 * X / min(N,M) >= K   =>  X >= (K * min(N,M)) / 2.

        Also, note that the set we are counting has at least the numbers that are multiples of the larger one and skip the LCM? 

        Actually, we can set:
          low = 1
          high = 2 * max(N, M) * K   ? But note: F(X) is at least about (1/N + 1/M) * X (if we ignore the negative term). 

        However, to be safe, we note that the worst-case for the growth of F(X) is when the coefficient is minimal. The minimal coefficient we found is (N+M-2g)/(N*M). Since N and M are at most 10^8, the minimal value of the numerator is 1 (if N and M are consecutive integers, then gcd=1, so N+M-2g = N+M-2, which for consecutive integers is at least 1). Then the coefficient is at least 1/(N*M). Therefore, to get F(X) = K, we need at least X = K * (N*M) / (N+M-2g). 

        But N*M can be 10^16, and K is 10^10, so X can be up to 10^26? This is too big for binary search? 

        However, note: we don't need to use an upper bound that is 10^26. We can use a more refined bound.

        Actually, we have:
          F(X) >= (X//N + X//M) - 2*(X//L) 
                 >= (X/(N) - 1 + X/(M) - 1) - 2*(X/L)   [because floor division is at least the real minus 1? but we have negatives?]

        But we can also note that F(X) >= (X/N + X/M) - 2*(X/L) - 2   [because each floor operation reduces by at most 1, and we have three terms? actually two terms? no, we have three floor operations?]

        Actually, we have:
          F(X) = (X//N - X//L) + (X//M - X//L)
                = X//N + X//M - 2*(X//L)

        And we know: 
          X//N >= X/N - 1, etc.

        So:
          F(X) >= (X/N - 1) + (X/M - 1) - 2*(X/L) 
                 = X*(1/N + 1/M - 2/L) - 2

        Therefore, to have F(X) >= K, we require:
          X*(1/N + 1/M - 2/L) - 2 >= K
          => X >= (K+2) / (1/N + 1/M - 2/L)

        The denominator is (N+M-2g)/(N*M) = (N+M-2g)/(N*M). So:

          X >= (K+2) * (N*M) / (N+M-2g)

        Since N, M are at most 10^8, and g is at least 1, the denominator is at least 1 (as argued) and at most 2*max(N,M) (because N+M-2g <= N+M <= 2*max(N,M)). 

        So the upper bound for X is (K+2) * (N*M) / (N+M-2g) + 1. 

        However, note that N*M can be as large as 10^16, and (K+2) is about 10^10, so the product is 10^26. This is too big to use as an upper bound in a binary search? Because we cannot do binary search over [1, 10^26] (log2(10^26) is about 86, which is acceptable? 2^86 is about 10^25.8, so 86 iterations is acceptable?).

        But 10^26 is a huge number and we are doing arithmetic with numbers up to 10^26? The problem constraints say that N, M up to 10^8 and K up to 10^10. The product N*M is 10^16, and then multiplied by 10^10 gives 10^26. In Python, integers are arbitrary precision, but arithmetic operations on integers of 10^26 might be slow? However, we are only doing about 100 iterations. The operations we do per iteration are:
          floor division: X//N, X//M, and we also need to compute LCM and then X//L.

        But note: we can compute LCM = N * M // gcd(N, M). However, if we do this, then N*M can be 10^16, which is acceptable in Python? Yes, because 10^16 is a 64-bit integer.

        However, when X is 10^26, then X//N: N is at least 1, so 10^26//N: that's a big integer, but Python can handle it. But we must be cautious for performance: each division for such big integers is O(log(X)) which is about 86 digits? So it's acceptable.

        But note: we have to do about 100 iterations, and each iteration does a few big integer divisions. This should be acceptable in Pyton? 

        Alternatively, we can use an upper bound that is not 10^26? We note:

          F(X) <= X/N + X/M   (because we subtract a nonnegative term? actually we subtract 2*floor(X/L), which is nonnegative, so F(X) <= X/N+X/M)

        Then to have F(X) >= K, we need X/N+X/M >= K. So X >= (K) / (1/N+1/M) = (K * N * M) / (N+M). 

        Since N and M are at least 1, we have:

          X_upper_bound = max( 
              (K * N * M) // min(N, M)   [because (K * N * M) / (N+M) <= K * min(N,M) * max(N,M) / (min(N,M)+max(N,M)) <= K * min(N,M) * max(N,M) / max(N,M) = K * min(N,M) )   -> but that's not the way.

        Actually, we can set:

          low = 1
          high = 2 * ( (K * N * M) // min(N, M) )   ? 

        But we can do: 
          high = K * min(N, M) * 2   [because F(X) >= X/(max(N,M)) (if we take the larger set) and then we have two sets?]

        Actually, we know that the sequence we are counting has at least the multiples of the smaller number (if we ignore the multiples of LCM). The multiples of the smaller number that are not multiples of LCM: at least (X / min(N,M)) - (X / LCM). But LCM is at least max(N,M), so the subtraction is at least (X/min(N,M)) - (X/max(N,M)) which is positive. 

        Alternatively, we can set an upper bound: 
          Since the set includes all multiples of the smaller number that are not multiples of the LCM, and also multiples of the larger number that are not multiples of the LCM. The multiples of the smaller number alone (without the LCM) are at least X/min(N,M) - X/(LCM). But we know LCM >= max(N,M) >= min(N,M), so the LCM multiples are at least min(N,M) multiples? 

        Actually, we can set a safe upper bound: 
          high = 2 * K * min(N, M)   -> because the set must have at least K numbers, and the multiples of the smaller number are about X/min(N,M). Then we have two sets? Actually, the entire set we are counting is about X*(1/N+1/M) so we can set:

          high = 2 * (K * N * M) // (min(N,M))   -> but that could be huge.

        Actually, let me think: 
          We want an X such that F(X) >= K.
          We know: F(X) >= X*(1/N+1/M) - 2 - 2*X/(L)  [but we can ignore the negative term?]

        Actually, we can set: 
          F(X) <= X/N + X/M 
          So if X/N + X/M < K, then we need to increase X.

        Therefore, we can set:
          high = 2 * (K * N * M) // (N+M)   -> but this might be too small? 

        Alternatively, we can set high = 10**18 * max(N, M)  ? 

        But note: worst-case when N=1, M=2, K=10^10: then the answer is 2*K-1 = 19999999999, which is about 2e10. 

        How about when N and M are large and coprime? Then the coefficient (1/N+1/M-2/(N*M)) is about 1/N+1/M which is about 2/(min(N,M))? So X is about (K * min(N,M)) / 2? 

        Actually, the coefficient is (N+M-2)/(N*M) ~ (min(N,M)+max(N,M))/(N*M) = 1/min(N,M) + 1/max(N,M) ~ 1/min(N,M) if min(N,M) is much smaller? 

        Therefore, we can set:

          high = 2 * K * min(N, M)   # because then F(high) ~ (2*K*min(N,M))/min(N,M) = 2K, which is >= K.

        However, what if min(N,M) is large? For example, if N=M+1 (so consecutive), then the coefficient is (2M+1-2g)/(M*(M+1)). Since gcd(M, M+1)=1, so g=1. Then (2M-1)/(M*(M+1)) ~ 2/(M). Then:

          F(high) = high * (2/M) - ... (with floor adjustments) ~ (2 * K * M) * (2/M) = 4K, which is greater than K.

        So we can set: 
          high = 2 * K * min(N, M)   [but note: min(N,M) might be very large? K * min(N,M) might be 10^10 * 10^8 = 10^18, so 2*10^18 is acceptable?]

        But worst-case: min(N,M) is 10^8 and K=10^10, then high = 2 * 10^18, which is acceptable for binary search? Because log2(2*10^18) is about 60. So 60 iterations.

        However, we must account for the worst-case when the coefficient is very small. The worst-case coefficient is when N and M are large and their gcd is large? 

        Example: N = g * a, M = g * b, then the coefficient becomes (g*(a+b) - 2*g) / (g^2 * a * b) = (a+b-2) / (g * a * b). 
        Then to have F(X)>=K, we require X * (a+b-2) / (g * a * b) >= K (ignoring constants) -> X >= K * (g * a * b) / (a+b-2). 
        Since a and b are the quotients, and g is the gcd, then a and b are at least 1. The worst-case is when a and b are 1? But then N=M? not allowed. 

        Actually, if a and b are 1, then N=M=g, but the problem says they are different. 

        The worst-case is when a=1 and b=2? Then the coefficient = (1+2-2)/(g*1*2)=1/(2g). Then X >= 2g*K. 
        Since g can be as large as 10^8, then X can be 2*10^8 * 10^10 = 2*10^18. 

        So we can set high = 2 * 10**18? But what if the coefficient is even smaller? 

        Actually, the minimal coefficient we found is 1/(N*M) (if N and M are coprime and consecutive? then the numerator is about 2*min(N,M) and denominator is N*M, so coefficient ~ 2/min(N,M)? 

        But we have: 
          coefficient = (N+M-2g) / (N*M) 
          >= (max(N,M)) / (N*M)   [because N+M-2g >= max(N,M) - min(N,M) = |N-M|? Actually, no: we have N+M-2g >= 1 as argued?]

        Actually, we have already argued that the numerator is at least 1. Therefore, the coefficient is at least 1/(N*M). 

        Then: X >= K * (N*M)   [ignoring constants]. 

        Since N*M can be 10^16 and K=10^10, then X can be 10^26. 

        Therefore, we must set high = 10**26? 

        But note: we have the expression: 
          F(X) = X//N + X//M - 2*(X//L)

        We can compute this for X=10^26? But the numbers are huge. However, the number of iterations is about log2(10^26) which is about 86, which is acceptable.

        But we must be cautious: in Python, integer arithmetic for numbers this big is done in time proportional to the number of digits. The number of digits is about 26 (in base 10) or about 86 bits? Actually, 10^26 has 27 decimal digits. The operations (division) for such numbers might be O(27) or O(86) which is constant. So it's acceptable.

        So we can set:
          low = 1
          high = 10**30   # to be safe? because 10^26 might be the minimal, but we want to be safe for constants? 

        However, we have: 
          X <= (K * (N*M)) / ( (N+M-2g) ) + 10   [because of the floor adjustments?]

        Since (N+M-2g) is at least 1, then an upper bound is K * (N*M) + 10. But N*M is 10^16, K=10^10, so 10^26 + 10, which is 10^26. So we can set high = (K * (N*M)) // 1 + 10   -> but we don't know g? 

        Alternatively, we can set high = 10**30? 

        But we want to avoid setting a fixed huge number? We can compute an upper bound:

          Let g = gcd(N, M)
          denominator = N + M - 2 * g   (which is at least 1)
          Then an upper bound for X is: (K+2) * (N*M) // denominator + 100

        However, we have to avoid division by zero? denominator is at least 1.

        But note: we cannot use this expression because we are in the integer world: we want an integer upper bound. 

        Actually, we can set:

          high = (K+2) * (N * M)   # because denominator is at least 1, so (K+2)* (N*M) / denominator >= (K+2)* (N*M) / (N+M) ... but actually it's bigger? 

        But denominator could be 1, then (K+2)*N*M might be 10^26? 

        So we can set: 
          high = (K+2) * (N * M) + 100

        But this might be too big? because (K+2)*N*M can be 10^26? and we are going to do about 100 iterations? 

        However, the problem constraints: K up to 10^10, N*M up to 10^16, so (K+2)*N*M is 10^26? which is acceptable for 100 iterations? 

        But note: we are doing floor divisions for numbers that are 10^26: the time complexity of division is O(log(X)) which is about 86 (for base 2) which is acceptable.

        Therefore, we set:
          low = 1
          high = (K+2) * (N * M)   # because denominator is at least 1, so (K+2)*N*M is an upper bound? 

        Actually, we have: 
          F(X) >= X*( (N+M-2g)/(N*M) ) - 2
          => if X = (K+2) * (N*M) / (N+M-2g), then F(X) >= (K+2) - 2 = K.

        So we can set high = (K+2) * (N*M)   (since we use integer arithmetic, we can set high = (K+2) * (N*M) // 1? but we need an integer, and (K+2)*N*M is an integer? 

        Actually, we can set high = (K+2) * (N * M)  // 1   -> but that's the same as (K+2)*N*M. 

        But note: we must avoid division by zero? denominator is at least 1, so it's safe.

        However, we can also use:

          high = 10**18 * max(N, M)   # but this might be 10^8 * 10^18 = 10^26, same as above.

        So I will set:

          g = gcd(N, M)
          # denominator = N + M - 2*g
          # Then high = (K+2) * (N * M) // denominator   -> but we want an integer and we want to be safe: we need an upper bound. 

        Actually, we can set:

          denominator = N + M - 2*g
          # Since denominator>=1, we can set:
          high = (K+2) * (N * M) // denominator + 100   # but we must avoid integer division? we want an integer.

        But note: we are going to do binary search. We can set:

          high = (K+2) * (N * M) // 1   # but that is (K+2)*N*M, which is an integer? 

        Alternatively, we can avoid computing denominator? because we have the expression for F(X) and we can set high to be, say, 10**30? 

        Considering the worst-case X is about 10^26, we can set high = 10**30? 

        However, to be safe for all cases, we can set:

          high = 10**30   # if 10^30 is large enough? 
          But worst-case: if K=10^10 and N=M=10^8, then the coefficient is (10^8+10^8-2g)/(10^16). The gcd g is 10^8? no, because N and M are different. Let g be the gcd. Then denominator = 10^8+10^8-2g = 2*(10^8 - g). The minimal denominator? if g is 1, then denominator=2*(10^8-1) ~ 2e8. Then X ~ (10^10 * 10^16) / (2e8) = 10^26 / (2e8) = 5e17. So 10^30 is more than enough.

        So we can set high = 10**30.

        Steps for the binary search:

          low = 0
          high = 10**30   # an upper bound we are sure F(high) >= K

          while low < high:
             mid = (low+high)//2
             count = F(mid)   = (mid//N + mid//M) - 2 * (mid // L)

             if count < K:
                 low = mid+1
             else:
                 high = mid

          Then the answer is low.

        But note: we must be cautious: F(mid) counts the numbers <= mid that are in the set. We are looking for the smallest X such that F(X)>=K. Then X = low is the K-th? 

        Actually, the K-th smallest number is the smallest X such that F(X) >= K. But note: F(X) is the count of numbers <= X that are in the set. Then when we break the binary search, low is the smallest X such that F(X)>=K. 

        However, we must check: is F(low)>=K and F(low-1)<K? Then low is the K-th smallest.

        But our binary search does exactly that: 
          It finds the smallest X such that F(X)>=K.

        Why? 
          We are doing:
            if count < K: low = mid+1 -> meaning we are looking for a larger X.
            else: high = mid -> meaning we are lowering the high bound.

        This is a standard lower_bound.

        However, we must test with the examples.

        Example: N=2, M=3, K=5.

          We want the 5th smallest: 9.

          Let's compute F(8) and F(9).

          F(8) = (8//2 + 8//3) - 2*(8//6) 
                = (4 + 2) - 2*(1) 
                = 6 - 2 = 4 -> which is less than 5.

          F(9) = (9//2 + 9//3) - 2*(9//6)
                = (4 + 3) - 2*(1) 
                = 7 - 2 = 5 -> which is >=5.

          So the algorithm would return 9.

        Example 2: N=1, M=2, K=3 -> output 5.

          F(4) = (4//1 + 4//2) - 2*(4//2)   [LCM(1,2)=2]
                = (4+2) - 2*(2) = 6-4=2 -> <3
          F(5) = (5+2) - 2*2 = 7-4=3 -> >=3 -> returns 5.

        Example 3: the big one.

        Therefore, we can implement:

          Step 1: compute g = gcd(N, M)
          Step 2: L = (N*M) // g   [but note: if we do this, we must use integer division? and N*M might be big? but we are in Python, so it's okay? But if N and M are 10^8, then N*M is 10^16, which is an integer in Python.]

          Step 3: Set low, high = 1, 10**30
          Step 4: while low < high:
             mid = (low+high)//2
             count = (mid//N) + (mid//M) - 2*(mid//L)

             if count < K:
                 low = mid+1
             else:
                 high = mid

          Step 5: return low

        But wait: what if the LCM computation overflows? 
          LCM = (N*M) // gcd(N,M) 
          But note: N*M might be 10^16, which is within the range of Python integers (which are arbitrary precision, but 10^16 is 1e16, which is 2^53 is about 9e15, so 10^16 is a bit above 2^53? but Python integers are arbitrary precision, so it's fine).

        However, we can avoid the big multiplication? 

          Actually, we can compute LCM without the product? 
            LCM = N * (M // gcd(N,M))   [because LCM = (N // gcd) * M = (M // gcd) * N]

          This avoids the product being as big as N*M? 

          Example: 
            gcd = g
            Then: LCM = (N // g) * M   [if we do: N * M // g -> same as (N//g)*M, but if we do (N//g)*M, then we avoid the product N*M?]

          But if we do (N//g)*M, then we are doing an integer division first, so the intermediate is (N//g) which is at most 10^8, then multiplied by M (10^8) -> 10^16, same as before.

        So we can do:

          g = gcd(N, M)
          LCM = (N // g) * M   # or (M // g)*N, to avoid the big product? but it's the same.

        However, we must be cautious: 
          LCM = (N * M) // g   -> but if we do (N * M) first, that might be 10^16, which is acceptable? 

        So we'll do:

          LCM = N * M // g

        Then we do the binary search.

        But note: if N and M are large, then LCM might be very large? then mid//LCM might be 0 for mid < LCM? which is acceptable.

        Let me test with the examples.

        Example: N=2, M=3: g=1, LCM=6 -> then mid//6: for mid<6, it's 0, which is correct.

        Example: N=1, M=2: g=1, LCM=2.

        Therefore, we can implement.

        However, one more edge: if LCM is 0? but N and M are positive, so LCM>=max(N,M)>=1.

        Code:

          import math
          g = math.gcd(N, M)
          L = N * M // g

        Then binary search.

        But note: we must be cautious that L might be very large? then when mid < L, then mid//L = 0, so F(mid)=mid//N+mid//M, which is correct.

        This solution has time complexity O(log(10^30)) which is about 100 iterations, and each iteration does a few divisions and one gcd (which is done once). 

        Let me test with the examples:

          Example 1: N=2, M=3, K=5 -> output 9.
          Example 2: N=1, M=2, K=3 -> output 5.

          Example 3: N=100000000, M=99999999, K=10000000000 -> output 500000002500000000

        How to compute for example 3?

          g = gcd(100000000, 99999999) = gcd(100000000, 1) = 1? 
          Actually, gcd(100000000, 99999999): 
            100000000 = 10^8
            99999999 = 10^8 -1
            gcd(10^8, 10^8-1) = 1.

          L = (10^8 * (10^8-1)) // 1 = 10^16 - 10^8.

          Then we do binary search for K=10^10.

          We need to compute F(X) for X = 500000002500000000.

          F(X) = (X//N + X//M) - 2*(X//L)

          X//N = 500000002500000000 // 100000000 = 5000000025
          X//M = 500000002500000000 // 99999999 
                 = 500000002500000000 // 99999999 = ?

          Let me compute: 
            500000002500000000 // 100000000 = 5000000025   (exactly? because 100000000 * 5000000025 = 500000002500000000)
            Then for M=99999999: 
                500000002500000000 // 99999999 = ?

          We note: 99999999 * 5000000025 = (100000000-1)*5000000025 = 500000002500000000 - 5000000025 = 500000002500000000 - 5000000025 = 499999997499999975

          Then 500000002500000000 - 499999997499999975 = 5000000025, which is the remainder? so:

            quotient = 5000000025, remainder = 5000000025? 
            But 5000000025 is greater than 99999999? so we have to add one? 

          Actually, we have:

            Let A = 500000002500000000
            A = 99999999 * 5000000025 + (5000000025) 
            But 5000000025 is greater than 99999999? So we do:

            quotient = 5000000025 + 5000000025 // 99999999 
            remainder = 5000000025 % 99999999

          Actually, we can do:

            A = 500000002500000000
            M = 99999999
            quotient = A // M = (5000000025 * M + 5000000025) // M   ??? 

          Alternatively, we can compute:

            A // M = (5000000025 * 100000000) // (100000000-1)

          But we can use: 
            A // M = (5000000025 * 100000000) // (100000000-1) 
            = (5000000025 * 100000000) // (99999999)

          But we know that 100000000 = 99999999 + 1, so:

            = 5000000025 * (99999999+1) // 99999999
            = 5000000025 * (1 + 1/99999999)   -> not integer.

          Actually, we can do:

            A = 500000002500000000
            M = 99999999
            quotient = A // M = 500000002500000000 // 99999999

          We can compute it as:

            500000002500000000 = 5000000025 * 100000000 
            = 5000000025 * (99999999+1)
            = 5000000025 * 99999999 + 5000000025

            Then: 
               5000000025 * 99999999 + 5000000025 = 5000000025 * 99999999 + 5000000025

            Therefore, A // M = 5000000025 + 5000000025 // 99999999

          Now, 5000000025 // 99999999: 
               5000000025 = 50 * 100000000 + 25? 
               but 99999999 * 50 = 4999999950
               5000000025 - 4999999950 = 75 -> so 50 times and remainder 75? 

          Actually: 
            99999999 * 50 = 4999999950
            5000000025 - 4999999950 = 75 -> so 5000000025 = 50 * 99999999 + 75.

          Therefore, 
            A = 5000000025 * 99999999 + 50 * 99999999 + 75 
               = (5000000025+50) * 99999999 + 75
            So quotient = 5000000025 + 50 = 5000000075, and remainder 75.

          Then:
            F(X) = (5000000025 + 5000000075) - 2 * (X // L)

          Now, L = 10^16 - 10^8? 
            X = 500000002500000000
            X // L = 500000002500000000 // (100000000*99999999) 
                   = 500000002500000000 // (9999999900000000) 

          How many times does 9999999900000000 go into 500000002500000000?
            Let B = 9999999900000000
            Then: 500000002500000000 // B = 500000002500000000 / (10^16 - 10^8) 

          We note: 
            500000002500000000 = 5.000000025e17
            B = 9.9999999e15
            Then 5.000000025e17 / 9.9999999e15 = 50.00000025 / 0.99999999  -> approximately 50.00000025 * (1+1e-8) ~ 50.00000025 + 0.0000005... -> about 50.00000075

          Actually, we can compute:

            Let B = 100000000 * 99999999 = 9999999900000000
            Then 500000002500000000 // B = 500000002500000000 // 9999999900000000

            We have:
              500000002500000000 = 5000000025 * 10^8
              9999999900000000 = 99999999 * 10^8

            So: 
               500000002500000000 // 9999999900000000 = (5000000025 * 10^8) // (99999999 * 10^8) 
                     = 5000000025 // 99999999 = 50   [because 5000000025 = 50 * 99999999 + 25?]

            Actually: 
                50 * 99999999 = 4999999950
                5000000025 - 4999999950 = 75 -> so 5000000025 = 50 * 99999999 + 75? 
                Then: 5000000025 // 99999999 = 50.

          Therefore, F(X) = 5000000025 + 5000000075 - 2*50 = 10000000100 - 100 = 10000000000.

          So F(500000002500000000)=10000000000, which is >=K (10^10). 

          But note: we are looking for the K-th smallest? and F(X)>=K, so 500000002500000000 is a candidate. However, we must check if it is the smallest X such that F(X)>=K? 

          Actually, the problem says the output is 500000002500000000.

          Therefore, our algorithm returns 500000002500000000 for that input.

        Implementation:

          We'll use:
            import math

            g = math.gcd(N, M)
            L = N * M // g   # LCM

            low, high = 1, 10**30

            while low < high:
                mid = (low+high)//2
                count = (mid//N) + (mid//M) - 2*(mid//L)

                if count < K:
                    low = mid+1
                else:
                    high = mid

            return low

        However, note: the problem constraints say that K can be as large as 10^10, and we are using an upper bound of 10^30, which is acceptable? 

        But what if the answer is larger than 10^30? 
          We argued the worst-case is about 10^26, so 10^30 is safe.

        Let me run the example 3: 
          N=100000000, M=99999999, K=10000000000 -> returns 500000002500000000.

        However, we must be cautious: the binary search might overshoot? 

        But the binary search is standard.

        One more edge: when K=1? 

          Then we return the smallest number that is divisible by exactly one of N or M.

          The smallest number is min(N, M) if that number is not divisible by the other? 

          But what if min(N,M) is divisible by the other? 

          Example: N=2, M=4, then the numbers: 
            divisible by 2: 2,4,6,8,10,12,...
            divisible by 4: 4,8,12,...
            Then the set: 
               2: divisible by 2 and not by 4 -> yes, because 2 is not divisible by 4? 
               4: divisible by both -> skip.
               6: divisible by 2 and not by 4? -> 6 is not divisible by 4 -> yes.
               8: divisible by 4 -> skip? 
               Then the set: 2,6,10,14,... and multiples of 4 that are not divisible by 2? impossible.

          Actually, multiples of 4 are divisible by 2? so there are no multiples of 4 that are not divisible by 2? 

          Therefore, the set is: multiples of 2 that are not multiples of 4 -> the numbers that are 2 mod 4.

          The smallest is 2.

          So for N=2, M=4, K=1: output 2.

          How does our F(2) work?
            F(2) = (2//2 + 2//4) - 2*(2//4)   [LCM(2,4)=4]
                  = (1+0) - 2*(0) = 1 -> >=1 -> so we return 2.

          But what if we have K=1 and the smallest candidate is the larger one? 
            Example: N=4, M=2: same as above? then the smallest candidate is 2.

          Actually, the condition: 
            divisible by exactly one: 
              if a number is divisible by 4, then it must be divisible by 2? so it fails. 
            So the set is the same: numbers divisible by 2 but not by 4.

          Therefore, the smallest is 2.

          How about: N=3, M=2, K=1? 
            The set: 
              multiples of 2 that are not multiples of 3: 2,4,8,10,14,...
              multiples of 3 that are not multiples of 2: 3,9,15,...
            The smallest is min(2,3)=2.

          Therefore, the smallest candidate is min(N,M) only if min(N,M) is not divisible by the other? But wait: 
            In the example N=2, M=4: min=2, and 2 is not divisible by 4? so it is included.

          So the smallest candidate is min(N,M) if min(N,M) is not divisible by the other? 
            But what if min(N,M) is divisible by the other? 
              Example: N=4, M=8: 
                  min=4, but 4 is divisible by 8? no, 4 is not divisible by 8? 
              Actually, if we have N=8, M=4: then 4 is the min, and 4 is divisible by 8? no. 
              But if we have N=6, M=3: 
                  min=3, and 3 is divisible by 6? no. 
              Then the next multiple: 6 is divisible by both? so we skip. 
              Then the next multiple of 3: 9 -> divisible by 3 and not by 6? 
                  But 9 is divisible by 3 and not by 6 -> yes.

          However, the smallest candidate should be 3? because 3 is divisible by 3 and not by 6? 

          Therefore, the smallest candidate is min(N,M) if min(N,M) is not divisible by the other? 

          But note: 
            Condition for min(N,M): 
              Without loss of generality, let N < M.
              Then the candidate = N.
              It is divisible by N. 
              Is it divisible by M? 
                 If N is divisible by M? but M>N, so it cannot be divisible by M? 

          Therefore, the smallest candidate is min(N,M). 

          Why? Because min(N,M) is divisible by itself and not by the larger one? (since the larger one is greater than min(N,M)). 

          Therefore, the smallest candidate is min(N,M). 

          Then for K=1, we return min(N,M). 

          But check: 
            F(min(N,M)): 
                Let X = min(N,M)
                Then X//N: if N is the min, then X//N = 1? 
                X//M: if M is larger than X, then 0.
                X//L: since L>=max(N,M)>X, then 0.
                So F(X)=1+0-0=1 -> so our binary search will return min(N,M) for K=1.

        Therefore, the algorithm is consistent.

        Code:

          import math

          class Solution:
              def solve(self, N: int, M: int, K: int) -> int:
                  g = math.gcd(N, M)
                  L = N * M // g   # LCM

                  # Set the bounds: low=1, high=10**30
                  low, high = 1, 10**30

                  while low < high:
                      mid = (low + high) // 2
                      # Count the numbers: divisible by exactly one
                      count = (mid // N) + (mid // M) - 2 * (mid // L)

                      if count < K:
                          low = mid + 1
                      else:
                          high = mid

                  return low

        However, we can optimize the bounds: 
          We can set low = 1, high = min(N,M) * 2 * K   # which is 2*K*min(N,M) -> but worst-case min(N,M) is 10^8, K=10^10 -> 2*10^18, which is acceptable and the binary search will be faster? 

        But we already have the worst-case that the answer might be 10^26? So we must set high to at least 10^26? 

        Therefore, we set high = 10**30 to be safe.

        However, we can compute an adaptive high:

          g = math.gcd(N, M)
          denom = N + M - 2 * g   # at least 1
          # Then we set high = (K+2) * (N * M) // denom + 10   # but note: we want an integer and we want to avoid division by zero? denom>=1.

          Actually, we can set:
             high = (K+2) * (N * M) // denom + 100   # but this might be 10^26? same as before.

        So for consistency, we use 10**30.

        Let me test with the examples:

          Example 1: N=2, M=3, K=5 -> returns 9? 
            We do binary search in [1, 10**30] -> mid = (1+10**30)//2, then we compute F(mid) until we get 9.

          Example 2: N=1, M=2, K=3 -> returns 5.

        But note: we must be cautious of integer overflow? In Python, integers are arbitrary precision, so no.

        We'll run the code for the examples.

        However, the problem constraints: K up to 10^10, and we do about 100 iterations, so it's efficient.

        Therefore, we write the code as above.

        But note: the LCM might be computed as a float? but we use integer division. 

        One more edge: if N*M is not divisible by g? 
          But by definition, LCM = (N*M) // gcd(N,M) is integer? because gcd divides both N and M, so it divides N*M.

        So we are safe.

        Let me run the example: N=100000000, M=99999999, K=10000000000 -> returns 500000002500000000.

        We computed F(500000002500000000)=10000000000, which is exactly K. Then we want the smallest X such that F(X)>=K. But note: the problem is that the K-th number might be less than 500000002500000000? 

        However, we computed F(500000002500000000)=10000000000, so the 10000000000th number is at most 500000002500000000. But what is the count at 500000002499999999? 

        Actually, the problem states that the output is 500000002500000000, so we are safe.

        However, our binary search might have found a candidate that is the K-th? 

        Actually, the K-th smallest number is the smallest X such that F(X) >= K. And we are returning the smallest such X. 

        But note: F(X) counts the numbers <= X. So if the K-th number is X0, then F(X0-1)=K-1 and F(X0)=K. Then our binary search will return X0.

        Therefore, the algorithm returns the K-th smallest number.

        We'll implement accordingly.

        Code:

          import math

          class Solution:
              def solve(self, N: int, M: int, K: int) -> int:
                  # If K==0? but K>=1.
                  g = math.gcd(N, M)
                  # Compute LCM
                  L = N * M // g

                  lo, hi = 1, 10**30
                  while lo < hi:
                      mid = (lo + hi) // 2
                      # Calculate the count: numbers <= mid divisible by exactly one of N or M.
                      # Count = (divisible by N) + (divisible by M) - 2*(divisible by both)
                      count = (mid // N) + (mid // M) - 2 * (mid // L)
                      if count < K:
                          lo = mid + 1
                      else:
                          hi = mid
                  return lo

        Let me test with the examples.

        Example 1: N=2, M=3, K=5 -> lo becomes 9.
        Example 2: N=1, M=2, K=3 -> lo becomes 5.

        But what if we have a candidate that is divisible by both? 
          For example, when mid=6 in the first example: 
            F(6) = (6//2 + 6//3) - 2*(6//6) = (3+2) - 2*1 = 5-2=3 -> so 6 is not included? and the count at 6 is 3? 
          Then the numbers: 
            <=2: [2] -> count=1
            <=3: [2,3] -> count=2
            <=4: [2,3,4] -> count=3
            <=8: [2,3,4,8] -> count=4
            <=9: [2,3,4,8,9] -> count=5

          So at mid=6, count=3? 
          Then at mid=7: 
            F(7) = (3+2) - 2*1 = 3? (because 7//2=3, 7//3=2, 7//6=1) -> 3+2-2=3.
          At mid=8: 
            F(8)= (4+2) - 2*1 = 6-2=4.

          Then mid=9: 5.

          So the binary search:
            low=1, high=10**30
            mid = (1+10**30)//2 -> huge, but we know the answer is 9.

          How to simulate? 
            We know that for mid=8: count=4 <5 -> then low becomes 9.
            Then for mid=9: count=5>=5 -> high=9, then break.

          So returns 9.

        Therefore, we are confident.

        We'll run the code for the big example? 
          We already computed that for X=500000002500000000, F(X)=10000000000, so we want the smallest X such that F(X)>=10000000000.

          We must check if there is a smaller X? 

          Actually, the problem states that the output is 500000002500000000, so we assume it is the smallest.

        However, we can check the previous number? 
          X = 500000002500000000 - 1 = 500000002499999999

          Then:
            count = (X//N + X//M) - 2*(X//L)

          We know:
            X//N = (500000002499999999) // 100000000 = 5000000024   (because 5000000024*100000000 = 500000002400000000, which is less than 500000002499999999, and 5000000025*100000000=500000002500000000 -> too big) 
            Actually, 500000002499999999 // 100000000 = (500000002499999999) // 100000000 
                = 5000000024   (because 5000000024 * 100000000 = 500000002400000000, and 500000002499999999 - 500000002400000000 = 99999999 -> so yes, 5000000024 with remainder 99999999)

          Then X//M = (500000002499999999) // 99999999 = ?

          We can compute similarly: 
            We know: 500000002500000000 = 5000000075 * 99999999 + 75? 
            Then 500000002499999999 = 5000000075 * 99999999 + 75 - 1 = 5000000075 * 99999999 + 74.

          So X//M = 5000000075.

          Then X//L: 
            L = 100000000 * 99999999 = 9999999900000000
            X = 500000002499999999
            Then X//L = 500000002499999999 // 9999999900000000 = 50   (because 50 * L = 499999995000000000, which is less than X, and 51*L = 499999995000000000 + 9999999900000000 = 509999994900000000, which is greater? 
            Actually: 50 * L = 50 * 9999999900000000 = 499999995000000000
            Then X = 500000002499999999 is greater than 499999995000000000? 
               500000002499999999 - 499999995000000000 = 7499999999, which is positive, so the quotient is 50? 

            But note: 499999995000000000 + 9999999900000000 = 509999994900000000, which is greater than 500000002499999999? 
            So yes, quotient=50.

          Then count = 5000000024 + 5000000075 - 2*50 = 10000000099 - 100 = 10000000000 - 1 = 9999999999? 

          Therefore, F(500000002499999999)=9999999999 < 10000000000, so the next number 500000002500000000 is the one we want.

        So the algorithm returns 500000002500000000.

        Therefore, we write the code accordingly.

        However, note: the LCM might be huge? then mid//L might be 0 for mid < L, so we don't have to worry about the LCM term until mid is large enough.

        We'll code accordingly.

        Let me run the provided examples.

        Example 1: 
          Input: 2 3 5 -> Output: 9

        Example 2:
          Input: 1 2 3 -> Output: 5

        Example 3:
          Input: 100000000 99999999 10000000000 -> Output: 500000002500000000

        Code:

          import math

          class Solution:
              def solve(self, N: int, M: int, K: int) -> int:
                  g = math.gcd(N, M)
                  # Calculate LCM = N*M // g
                  # If N and M are large, then N*M might be huge? but we are in Python.
                  L = N * M // g

                  lo, hi = 1, 10**30

                  while lo < hi:
                      mid = (lo + hi) // 2
                      # Count the numbers: 
                      #   divisible by N: mid // N
                      #   divisible by M: mid // M
                      #   divisible by both: mid // L
                      count = (mid // N) + (mid // M) - 2 * (mid // L)

                      if count < K:
                          lo = mid + 1
                      else:
                          hi = mid

                  return lo

        We'll test with the examples.

        Example 1: 
          N=2, M=3, K=5 -> L = 6
          mid= (1+10**30)//2 -> too big to do by hand? so we simulate:

          We know the answer is 9.

          We can do:

            lo=1, hi=10**30
            while lo<hi:
                mid = (lo+hi)//2
                count = mid//2 + mid//3 - 2*(mid//6)

                if count < 5: lo = mid+1
                else: hi = mid

          Step 1: lo=1, hi=10**30 -> mid = (1+10**30)//2, but we know that for mid=9, count=5, so we can break when lo becomes 9.

          Alternatively, we can run a small simulation:

            lo=1, hi=10**30
            mid = 500...0 (huge) -> count = huge, so we set hi = mid -> then we reduce.

          Actually, we can do:

            We know that for mid=8: 
                count = 8//2 + 8//3 - 2*(8//6) = 4+2 - 2*1 = 4 -> <5 -> so lo = 9
            then lo=9, hi=9 -> break.

          So returns 9.

        Example 2: 
          N=1, M=2, K=3 -> L=2
          lo=1, hi=10**30
          mid = huge -> but we know the answer is 5.

          Step:
            mid=1: count=1//1+1//2-2*(1//2)=1+0-0=1 -> <3 -> lo=2
            mid=2: count=2+1-2*(1)= 3-2=1 -> <3 -> lo=3
            mid=3: count=3+1-2*(1)=4-2=2 -> <3 -> lo=4
            mid=4: count=4+2-2*(2)=6-4=2 -> <3 -> lo=5
            then lo=5, hi=5 -> break.

          But wait, we break when lo becomes 5? then we return 5.

          However, at mid=5: 
            count=5//1+5//2-2*(5//2)=5+2-2*2=5+2-4=3 -> >=3 -> so we set hi=5, then break.

          So returns 5.

        But note: the set is [1,3,5] -> the third is 5.

        However, the above step-by-step is not the actual binary search? because the binary search does not step one by one. 

        Actually, the binary search:

          lo=1, hi=10**30
          mid = (1+10**30)//2 -> which is about 5e29? 
          Then count = 5e29 (divisible by 1) + 5e29//2 - 2*(5e29//2) = 5e29 + 2.5e29 - 2*(2.5e29) = 5e29 + 2.5e29 - 5e29 = 2.5e29 -> which is huge (>=3) -> then hi = mid.

          Then we keep reducing until we get to 5.

        But 10**30 is huge, and we do 100 iterations, so it's efficient.

        Therefore, we have the solution.

        Let me run the big example: 
          N=100000000, M=99999999, K=10000000000
          We know the answer is 500000002500000000.

          We'll set:
            lo=1, hi=10**30
            ... and eventually we get hi=500000002500000000.

          We already computed that for mid=500000002499999999, count=9999999999 < 10^10, so lo becomes 500000002500000000, then we break.

        So we return 500000002500000000.

        This matches.

        We'll write the code accordingly.

        Note: We must use integer arithmetic.

        Code:

          import math

          class Solution:
              def solve(self, N: int, M: int, K: int) -> int:
                  # If N and M are the same? but the problem says they are different.
                  g = math.gcd(N, M)
                  # Calculate LCM
                  # To avoid overflow in N*M? but in Python it's okay for 10^8 * 10^8 = 10^16.
                  L = N * M // g
                  lo, hi = 1, 10**30
                  while lo < hi:
                      mid = (lo + hi) // 2
                      count = (mid // N) + (mid // M) - 2 * (mid // L)
                      if count < K:
                          lo = mid + 1
                      else:
                          hi = mid
                  return lo

        Let me run the examples in code.

        Example1: 
          s = Solution()
          s.solve(2,3,5) -> 9

        Example2:
          s.solve(1,2,3) -> 5

        Example3: 
          s.solve(100000000, 99999999, 10000000000) -> 500000002500000000

        We'll run the code.

        But note: the big example might take a few iterations? 100 iterations is fast.

        We'll run locally? but we can trust.

        Submit the code.

        However, one more optimization: we can use a lower upper bound to reduce the number of iterations? 
          We can set: 
             hi = min( (K+2) * (N*M) // (N+M-2*g) + 10, 10**30)

        But we know that (K+2)*N*M might be 10^26, which is less than 10^30, so we can set hi = (K+2) * (N*M) // (N+M-2*g) + 100

        How to compute denominator: 
          denom = N + M - 2*g
          Then hi = (K+2) * (N * M) // denom + 100

        But note: denom>=1, so no division by zero.

        Then the binary search will run in about 100 iterations anyway? 

        We can do:

          denom = N + M - 2*g
          # We want an upper bound: 
          #   F(X) >= X * (denom) / (N*M) - 2   (as we had)
          #   So if X = (K+2) * (N*M) // denom, then F(X) >= (K+2) - 2 = K, so we can set hi = (K+2) * (N*M) // denom + 100

          Then:

            hi = (K+2) * (N * M) // denom + 100

        But we must avoid when denom=0? but denom>=1.

        Let me test with the examples:

          Example1: N=2, M=3, K=5: 
            g = gcd(2,3)=1
            denom = 2+3-2=3
            hi = (5+2)* (2*3) // 3 + 100 = 7*6//3+100 = 42//3+100=14+100=114

          Then we do binary search in [1,114] -> which is faster? 

        Example2: N=1, M=2, K=3:
            g=1
            denom=1+2-2=1
            hi = (3+2)* (1*2) // 1 + 100 = 5*2+100=110

        Example3: N=100000000, M=99999999, K=10000000000:
            g=1
            denom=100000000+99999999-2=199999997
            hi = (10000000000+2) * (100000000*99999999) // 199999997 + 100
            = 10000000002 * (9999999900000000) // 199999997 + 100

            We know: (10000000000+2) * (100000000*99999999) = 10000000002 * 9999999900000000
            Then divided by 199999997.

            But we know the answer is 500000002500000000, and 500000002500000000 is about 5e17.

            How much is 10000000002 * 9999999900000000? 
                10000000002 * 9999999900000000 = (10000000000+2) * (10000000000-100000) * 100? 
            Actually, 100000000 * 99999999 = 9999999900000000, then multiplied by 10000000002 -> 9999999900000000 * 10000000002 = 9999999900000000 * (10000000000+2) = 99999999000000000000000000 + 19999999800000000 = 100000000000000000000000000? approximately.

            Then divided by 199999997 -> about 100000000000000000000000000 / 199999997 ~ 5e25? which is much bigger than the answer (5e17). 

            Then we add 100.

            But then the binary search will still be about 100 iterations? and the initial hi is about 5e25? which is less than 10^30? 

            But the operations on numbers as big as 5e25? in Python, the division for numbers that big is O(log10(X)) which is about 26, so acceptable.

            However, we can avoid the huge multiplication? 

            Note: 
              hi = (K+2) * (N*M) // denom + 100

            We can compute:

              hi = (K+2) * (N * M) // denom + 100

            But if we do N*M first, that is 10^16

## Final Solution

