# Solution for abc334_e.md

Generated at: 2025-07-22 06:50:13
Model: deepseek-reasoner

## Reasoning Process

Alright, I'm going to tackle this programming problem step by step. Let me start by understanding the problem thoroughly.

### Problem Understanding
We have a grid of H rows and W columns. Each cell is either red ('.') or green ('#'). The goal is to compute the expected number of green connected components after choosing one red cell uniformly at random, repainting it to green, and then calculating the average over all possible choices of red cells.

The key points:
- The grid has at least one red cell (so we don't have division by zero).
- The grid dimensions can be up to 1000x1000, meaning we cannot try every red cell one by one in a brute-force manner (because there might be up to 1e6 red cells and each connected component computation is expensive).
- We need to output the expected value modulo 998244353.

The problem is essentially: 
Expected value = (Sum over all red cells of [number of connected components after turning that red cell to green]) / (total number of red cells)

But we have to do this modulo 998244353. Since we are dealing with fractions, we'll use modular inverses.

### Initial Thoughts
The challenge is that for each red cell we turn green, the number of connected components might change. The change depends on:
1. How many green connected components are adjacent to the red cell? Turning a red cell green might connect several adjacent green components into one, thereby reducing the total count.

So, if a red cell is adjacent to k connected components, then when we turn it green, the new connected component will merge those k components into one. Therefore, the change in the number of connected components would be: 1 - k (because we are adding one cell but merging k components, so we reduce by k-1: the new component count becomes (original_components - (k-1))).

But wait: originally, the grid has a certain number of connected components, say C. Then, after adding a green cell, the new number of components is C - (k - 1). Because we are merging k components into one, which reduces the count by k-1, and then adding one new cell (which is now part of that merged component) doesn't add a new component. Actually, the new cell becomes part of the merged component, so the total becomes C - (k-1).

Alternatively, we can think: 
New_components = Original_components + 1 - k
Because we are adding one new component (if the cell were isolated) but then merging k components into one, which subtracts k and adds 1? Actually, no. 

Let me clarify:
- Initially, there are C components.
- When we add a green cell, if it is adjacent to k different components, then these k components will become one. So we lose k-1 components (because k components merge into one). But we also add the new cell, which is now part of the merged component. So the total components become C - (k-1).

Therefore: New_components = C - (k-1)

So the change in the number of components is: (C - (k-1)) - C = - (k-1). Actually, we don't need the change per se, but the absolute number after the operation.

Thus, for each red cell, the number of connected components after repainting is: 
    original_components - (k - 1) 
But wait: what if the red cell is not adjacent to any green? Then k=0, and the new component count is original_components + 1? Because we are adding a new isolated green cell.

So the formula for the new number of components when repainting a red cell at (i, j) is:
    new_components = original_components + 1 - k
where k is the number of distinct green connected components adjacent to (i, j).

Therefore, the expected value is:
    E = (1 / R) * [ sum_{each red cell} (original_components + 1 - k_i) ]
where R is the total number of red cells.

We can rewrite:
    E = (original_components) + (1/R) * [ sum_{red cells} (1 - k_i) ]
    = original_components + (R - sum_{red cells} k_i) / R

So the problem reduces to:
1. Compute the original number of green connected components (using BFS/DFS, but note that grid can be up to 10^6 cells, which is acceptable for BFS/DFS with a visited matrix).
2. Count the total number of red cells (R).
3. For each red cell, compute k_i: the number of distinct green connected components adjacent to it.

But step 3 is tricky: adjacent means up, down, left, right. However, note that two adjacent green cells might belong to the same component. So we need to count distinct components.

So the challenge is: for each red cell, we look at its 4 neighbors. Each neighbor (if green) belongs to a component. But we have to avoid counting the same component multiple times. For example, if two adjacent neighbors are in the same component, we count that as one.

How to do this efficiently?
- We can precompute the connected components of the original grid and assign a label to each green cell (so each cell in a component has the same label, and different components have different labels).
- Then, for each red cell, we look at the labels of the green neighbors and count the distinct labels.

But note: the grid is large (10^6 cells). The red cells could be up to 10^6, and each has up to 4 neighbors. So the total operations would be O(4 * R) = O(4 * 10^6) which is acceptable? Actually, worst-case R can be about 10^6, so 4 million operations is acceptable in Python? Maybe, but we have to be cautious about the overall time. However, 4 million is acceptable in PyPy or in C++ but in Python we have to write efficient code.

But wait: how to get the label for a neighbor? We have a grid of labels, same size as the grid. For each red cell, we check the four neighbors: if the neighbor is green, then we get its label and add it to a set. The number of distinct labels is k_i.

So steps:
1. Precompute the connected components of green cells. We'll create a 2D array `comp_id` of size H x W, initially -1 for red cells, and for green cells, we assign an integer (the component id). Also, we count the total number of components (original_components).

2. Count R: the total number of red cells.

3. Precompute an array `k_i` for each red cell? Actually, we don't need to store all, we just need to accumulate the sum of k_i over all red cells.

But note: we can iterate over every red cell. However, there might be up to 10^6 red cells, and for each, we check 4 neighbors. So total operations 4 * 10^6 = 4e6, which is acceptable in Python? Probably, because 4e6 iterations is about 0.1 seconds in Pyton if we code in PyPy or use efficient loops.

But worst-case in Python might be borderline in Pyton (if each iteration is heavy), but 4e6 is acceptable.

However, we must be cautious: we have to avoid using recursion for DFS (to avoid recursion limits) and use iterative BFS or DFS with a stack.

But wait: the grid can be 1000x1000 = 1e6 cells. So BFS/DFS for connected components: we traverse each green cell once. That is acceptable.

So algorithm outline:

Step 1: Precomputation
- Read H, W, and grid.

Step 2: Build a visited matrix for green cells and assign component ids.
- Create a 2D array `comp` of size HxW, initialized to -1 (meaning no component).
- Create a variable `comp_count = 0`.
- For each cell (i, j):
    - if the cell is green and comp[i][j] == -1, then do BFS/DFS to mark all connected green cells with `comp_count`, then increment `comp_count`.

Step 3: Count R = total number of red cells.

Step 4: Precompute the contribution from each red cell: for each red cell, look at its four neighbors. For each neighbor that is green, record its comp id. Then k_i = number of distinct comp ids in the neighbors.

Step 5: 
    total_component_initial = comp_count
    total_k = sum(k_i for all red cells)   # Actually, we are going to compute the sum over red cells of k_i.

Then, 
    E = total_component_initial + (R - total_k) / R

But note: we are in modular arithmetic modulo 998244353.

So we have to express this as:
    E = (total_component_initial * R + R - total_k) / R   mod 998244353

Actually, from our expression:
    E = total_component_initial + (R - total_k) / R
        = [ total_component_initial * R + (R - total_k) ] / R

So:
    numerator = total_component_initial * R + (R - total_k)
    E = numerator / R   mod 998244353

But modulo arithmetic: we do:
    E = numerator * mod_inverse(R, 998244353) % 998244353

Step 6: Print E mod 998244353.

But wait: total_component_initial, R, and total_k are integers. We must do all arithmetic modulo mod? However, note that the numerator and denominator are integers, but we are working modulo 998244353. Since R can be up to 10^6, and total_component_initial up to maybe 500000? (because each component at least one cell, and there are at most 1e6 cells). And total_k is at most 4 * R (because each red cell has at most 4 neighbors, so k_i <=4, so total_k <= 4*R). So numerator is about 5e6 * 1e6 = 5e12? Which is within Python integers, but modulo 998244353 is about 1e9, so we can do:

    numerator = (total_component_initial * R + R - total_k) % mod
    Then E = numerator * pow(R, mod-2, mod) % mod

But wait: that's not correct. Because:
    numerator = total_component_initial * R + R - total_k
    Then E = numerator / R = total_component_initial + (R - total_k)/R

But modulo arithmetic: we have to do:

    numerator = (total_component_initial * R + R - total_k) % mod
    Then multiply by the modular inverse of R mod mod.

Alternatively, we can compute:
    E = (total_component_initial * R + R - total_k) * modinv(R, mod) % mod

But note: we are doing integer division? Actually, we are representing a rational number.

So in code:

    mod = 998244353
    invR = pow(R, mod-2, mod)
    E = (total_component_initial * R + R - total_k) % mod
    E = E * invR % mod

But wait: if we do (total_component_initial * R) % mod, then add (R - total_k) % mod, then multiply by invR? Actually, we can do:

    numerator = (total_component_initial * R + R - total_k) % mod
    E = (numerator * invR) % mod

But note: total_component_initial * R might be very big, but Python integers are arbitrary precision, but modulo mod we can do:

    numerator = (total_component_initial * R) % mod
    numerator = (numerator + R - total_k) % mod
    Then multiply by invR mod mod.

But we have to be cautious: R - total_k might be negative? total_k is at most 4*R, so R - total_k can be negative? Yes, so we should do:

    numerator = (total_component_initial * R + R - total_k) % mod

But modulo arithmetic: we can do:

    numerator = (total_component_initial * R) % mod
    numerator = (numerator + R) % mod
    numerator = (numerator - total_k) % mod

But if total_k is big, then (numerator - total_k) might be negative? Then we can do:

    numerator = (total_component_initial * R + R - total_k) % mod

But to avoid negative, we can:

    numerator = (total_component_initial * R + R - total_k) % mod
    if numerator < 0: numerator += mod   # but modulo operation in Python for negative? Actually, we can do:

Alternatively, do:

    numerator = (total_component_initial * R % mod + R - total_k) % mod

But total_component_initial * R might be huge? We can do modulo at each step.

However, the modulus is 998244353. Since total_component_initial, R, total_k are integers, we can compute:

    numerator = (total_component_initial * R + R - total_k) % mod

This is safe because even if the number is huge, modulo will reduce it.

But note: we are summing over all red cells for total_k, so total_k can be up to 4 * (number of red cells) = 4 * R. And R is up to 10^6, so total_k <= 4e6. total_component_initial is at most 500000 (if each component has 2 cells, then 500000, worst-case each component one cell: 1e6). So:

    total_component_initial * R <= 10^6 * 10^6 = 10^12, which is acceptable for Python integers and modulo 998244353.

So we can compute:

    numerator = (total_component_initial * R + R - total_k) % mod

But then multiply by the modular inverse of R mod 998244353.

### Implementation Details

1. **Connected Components Labeling**:
   - We traverse the grid. For each green cell that hasn't been visited, we start a BFS/DFS to mark all connected green cells with the current component id.

2. **Neighbor Check for Red Cells**:
   - For each red cell, we check the four neighbors (up, down, left, right). For each neighbor that is within the grid and is green, we note the component id of that neighbor. We use a set for each red cell? But we only have up to 4 neighbors, so we can use a set of size at most 4. Then k_i = len(set). Then add k_i to total_k.

But note: we have many red cells, so we cannot use a set per red cell? Actually, we can because the total red cells is up to 10^6, and each set has at most 4 elements. The memory for sets: 10^6 sets * 4 = 40e6 integers? Which is about 40*8 = 320 MB? In Python, each integer is about 24-28 bytes? And a set of 4 integers: the set has overhead. Actually, we don't need to store the sets, we can compute the distinct count without building a set? 

Alternatively, we can do:

    comp_ids = set()
    for each neighbor in [up, down, left, right]:
        if neighbor is green (and within grid), then add comp[neighbor_i][neighbor_j] to comp_ids (if not -1)

But we can use a set for each red cell. The total memory might be high? 10^6 sets? But each set is small (4 elements). The total memory would be about 10^6 * (overhead of a set + 4 integers). The set overhead in Python is about 200 bytes? Then total memory: 10^6 * 200 bytes = 200 MB? Plus the integers: 10^6 * 4 * 24 = 96 MB? So total 300 MB? That might be acceptable in Pyton? But the problem constraints say H, W up to 1000, so grid has 1e6 cells. The entire grid is about 1e6 characters. The comp matrix is 1e6 integers. Then we have an additional 300 MB for the sets? Total memory might be around 500 MB? But in Python, this might be borderline in some online judges, but we should try to avoid.

Alternatively, we can avoid building a set per red cell by using a fixed-size array? But the component ids can be from 0 to comp_count-1, which can be up to 500000. We cannot create an array of booleans of size comp_count for each red cell (that would be 500000 booleans per red cell, which is 10^6 * 500000 = 500e6 booleans = 500e6 bytes = 500 MB? But each boolean in an array is 1 byte? Then 500 MB? But then total memory is 500 MB? Actually, we don't need to store the entire array per red cell; we can use a local array? No, because comp_count is large.

Alternatively, we can use a fixed-size array for the entire algorithm? Actually, no. But note: we don't need to store the distinct count per red cell. We only need the total sum of k_i.

But we can avoid sets by using a trick? Since we have at most 4 neighbors, we can compare the labels of the neighbors and count distinct ones without a set? For example:

    labels = []
    for neighbor in neighbors:
        if neighbor is green and within grid, then get label = comp[i+di][j+dj]
        and then we can check duplicates by comparing the labels we have so far.

But for 4 neighbors, we can do:

    labels = []
    for each neighbor direction:
        if neighbor exists and is green:
            label = comp[ni][nj]
            if label not in labels:   # this is O(4) per neighbor, so 4*4 = 16 per red cell? Then 16 * R = 16e6 operations? Which is acceptable.

So we can do without sets? Then we don't need the set overhead.

Steps for a red cell (i, j):
    labels = []   # list of distinct labels so far
    for each direction (dx,dy) in [(1,0),(-1,0),(0,1),(0,-1)]:
        ni, nj = i+dx, j+dy
        if ni in [0, H-1] and nj in [0, W-1] and grid[ni][nj]=='#':
            label = comp[ni][nj]   # which we have from the BFS step
            if label is not in labels:   # we check by iterating over the current labels (which has at most 4 elements)
                labels.append(label)
    k_i = len(labels)

Then total_k += k_i

This avoids the set memory overhead. The time per red cell is O(4*4)=16, and total 16 * R = 16e6, which is acceptable.

But wait: what about the grid boundaries? We have to check that ni, nj are within [0, H) and [0, W).

So the plan is:

    total_k = 0
    for i in range(H):
        for j in range(W):
            if grid[i][j] == '.':   # red cell
                labels = []
                for dx,dy in [(1,0), (-1,0), (0,1), (0,-1)]:
                    ni, nj = i+dx, j+dy
                    if 0<=ni<H and 0<=nj<W and grid[ni][nj]=='#':
                        lab = comp_id[ni][nj]   # which we computed earlier
                        # if lab is -1? No, because if grid[ni][nj]=='#', then comp_id[ni][nj] is not -1.
                        # But we must have assigned comp_id for green cells. So it should be a non-negative integer.
                        found = False
                        for l in labels:
                            if l == lab:
                                found = True
                                break
                        if not found:
                            labels.append(lab)
                total_k += len(labels)

But note: what if the same component appears twice? We avoid by the above check.

Alternatively, we can use a small set. The memory for one set per red cell is acceptable? Let me test: 
    In Python, a set of 4 integers: the set structure itself has a fixed overhead. The total number of sets is R, which is up to 10^6. The memory for a set in Python is about 200 bytes? Then 10^6 * 200 bytes = 200 MB. Plus the grid and comp_id arrays: grid is 1e6 characters (1 byte per char) -> 1MB? comp_id is 1e6 integers -> 8 MB? Then total memory: 200+1+8 = 209 MB? That might be acceptable in Pyton? But in competitive programming, 200 MB is acceptable in many judges.

But to be safe and efficient, we can use the list method (with at most 4 elements per red cell) and then the inner loop is only 4 per neighbor? Actually, we have 4 neighbors and for each we check at most 4 labels? So 16 checks per red cell. Then total operations 16 * R = 16e6, which is acceptable.

But the set method is simpler to code and might be faster? Because set operations are O(1) per insertion? But for 4 elements, it's negligible.

I think using a set is more straightforward:

    total_k = 0
    for i in range(H):
        for j in range(W):
            if grid[i][j] == '.':
                s = set()
                for dx,dy in [(1,0),(-1,0),(0,1),(0,-1)]:
                    ni, nj = i+dx, j+dy
                    if 0<=ni<H and 0<=nj<W and grid[ni][nj]=='#':
                        s.add(comp_id[ni][nj])
                total_k += len(s)

This is clean and acceptable.

### Edge Cases
1. What if a red cell has no green neighbors? Then k_i=0, and we add 0 to total_k.

2. What if two neighbors are in the same component? Then we add the same comp_id twice, and the set will have one element.

3. What if the grid has no green cells? But the problem says there is at least one red cell. But it doesn't say there is at least one green cell. So original_components might be 0.

Example: all cells red. Then R = H*W. Then for each red cell, k_i=0 (since no green neighbors). Then:
    E = 0 + (R - 0)/R = 1.

So the expected number of connected components is 1? Because we are turning one red to green, so one isolated green cell.

But the problem says: "There is at least one (i,j) such that S_{i,j} = '.'", meaning at least one red cell. It doesn't say anything about green cells. So we must handle the case with no green cells.

In our algorithm:
    comp_count = 0
    Then we traverse: if we find a green cell, we start BFS. But if there are no green cells, we skip and comp_count remains 0.

    Then total_component_initial = 0.

    Then for each red cell, k_i=0.

    Then numerator = 0 * R + R - 0 = R.
    Then E = R / R = 1.

Which is correct.

Another edge case: only one green cell. Then original_components=1. Then when we turn a red cell to green, if the red cell is adjacent to the green, then k_i=1, so new_components = 1 + 1 - 1 = 1. If the red cell is not adjacent, then k_i=0, so new_components=2. 

So if we have a grid:

    . . 
    . # 

Then red cells: 3. The one adjacent to the green (bottom left and top right? Actually, the green is at (1,1). Then the adjacent red cells: (0,1) and (1,0). The red cell at (0,0) is not adjacent.

So:
    For (0,0): k_i=0 -> new_components = 1**** = 2? But actually, the original component is 1. Then after adding (0,0): we have two components: the original and the new one? So 2.

    For (0,1): adjacent to (0,0) is red? no, but adjacent to (1,1) (down) which is green. So k_i=1 -> new_components = 1****=1.

    Similarly for (1,0): adjacent to (1,1) (right) -> k_i=1 -> new_components=1.

So the expected value: (2+1+1)/3 = 4/3.

In our formula:
    total_component_initial = 1
    R=3
    total_k: 
        (0,0): 0
        (0,1): 1
        (1,0): 1
        total_k = 0+1+1 = 2
    Then E = 1 + (3 - 2)/3 = 1 + 1/3 = 4/3.

Correct.

### Complexity Analysis
- Precompute connected components: O(H*W) in time and space (for the comp_id matrix and the BFS queue).
- Then iterate over all red cells: O(H*W) and for each, check 4 neighbors: O(1) per red cell. So total O(H*W).

Space: O(H*W) for the grid and comp_id.

This is acceptable for H, W up to 1000 -> 1e6 cells.

### Implementation in Code

We'll use BFS for connected components (to avoid recursion depth issues).

Steps:

    mod = 998244353

    Read H and W, then grid.

    comp_id = [[-1]*W for _ in range(H)]
    comp_count = 0
    directions = [(1,0),(-1,0),(0,1),(0,-1)]

    for i in range(H):
        for j in range(W):
            if grid[i][j]=='#' and comp_id[i][j]==-1:
                comp_count += 1
                # BFS starting from (i,j)
                queue = collections.deque()
                queue.append((i,j))
                comp_id[i][j] = comp_count-1   # because we start from 0
                while queue:
                    x,y = queue.popleft()
                    for dx,dy in directions:
                        nx, ny = x+dx, y+dy
                        if 0<=nx<H and 0<=ny<W and grid[nx][ny]=='#' and comp_id[nx][ny]==-1:
                            comp_id[nx][ny] = comp_count-1
                            queue.append((nx,ny))

    Then count R: 
        R = 0
        for i in range(H):
            for j in range(W):
                if grid[i][j]=='.':
                    R += 1

    total_k = 0
    for i in range(H):
        for j in range(W):
            if grid[i][j]=='.':
                s = set()
                for dx,dy in directions:
                    ni, nj = i+dx, j+dy
                    if 0<=ni<H and 0<=nj<W and grid[ni][nj]=='#':
                        s.add(comp_id[ni][nj])
                total_k += len(s)

    Then:
        numerator = (comp_count * R + R - total_k) % mod
        invR = pow(R, mod-2, mod)
        ans = numerator * invR % mod

But note: if R is 0? The problem states there is at least one red cell, so we don't have to worry.

But wait: what if the grid is all green? Then R=0? But the problem says: "There is at least one (i,j) such that S_{i,j} = '.'". So R>=1.

Let me test with the example: 
    Example 1: 
        Input: 
            3 3
            ##.
            #.#
            #..

        Grid:
            row0: '##.' -> [0,0,1]? Actually, the green cells: 
                (0,0) and (0,1) are connected. 
                (1,0) is green -> connected to (0,0) via (0,1)? Actually, (0,0) and (0,1) are adjacent. Then (1,0) is adjacent to (0,0)? So they are all connected. 
                Then (1,2) is red, (2,1) and (2,2) are red.

            The green cells: 
                (0,0), (0,1), (1,0), (2,0) -> because the last row: "#.." -> the first character is '#' -> (2,0) is green.

            How are they connected?
                (0,0) is connected to (0,1) (right) and (1,0) (down). Then (1,0) is connected to (2,0). So one component.

            So comp_count = 1.

            R = number of red cells: 
                row0: (0,2) -> red
                row1: (1,1) and (1,2) -> but (1,2) is red? Actually, row1: "#.#" -> (1,0) is green, (1,1) is red, (1,2) is green? Wait, the input: 
                    "##."
                    "#.#"
                    "#.."
                row0: indices: [0,0]:'#', [0,1]:'#', [0,2]:'.' -> red.
                row1: [1,0]:'#', [1,1]:'.', [1,2]:'#' -> so red at (1,1)
                row2: [2,0]:'#', [2,1]:'.', [2,2]:'.' -> red at (2,1) and (2,2)
                So R=4.

            Now, for each red cell, compute k_i:

                (0,2): neighbors: 
                    down: (1,2) -> green -> comp_id[1][2] is the same as the main component? (since all green are connected) -> so one component -> k_i=1.
                    left: (0,1) -> green -> same component -> so set has one element -> k_i=1.

                (1,1): neighbors: 
                    up: (0,1) -> green -> comp0
                    down: (2,1) -> red -> skip
                    left: (1,0) -> green -> comp0
                    right: (1,2) -> green -> comp0
                    -> set has one element: k_i=1.

                (2,1): neighbors:
                    up: (1,1) -> red -> skip
                    left: (2,0) -> green -> comp0
                    right: (2,2) -> red -> skip
                    down: none
                    -> k_i=1.

                (2,2): neighbors:
                    up: (1,2) -> green -> comp0
                    left: (2,1) -> red -> skip
                    -> k_i=1.

            Then total_k = 1+1+1+1 = 4.

            Then numerator = 1*4 + 4 - 4 = 4.
            Then E = 4/4 = 1.

            But the example says: 
                If cell (1,3) -> actually, they use 1-indexed: 
                    (1,3) -> in 0-indexed: (0,2) -> then becomes 1 component? 
                (2,2) -> (1,1) -> becomes 1 component? 
                (3,2) -> (2,1) -> becomes 2? 
                (3,3) -> (2,2) -> becomes 2? 
                Then E = (1+1+2+2)/4 = 6/4 = 3/2.

            Why the discrepancy?

            Let me check the example again: 
                Grid:
                  0: ['#','#','.'] 
                  1: ['#','.','#'] 
                  2: ['#','.','.'] 

            After repainting (0,2) (which is the '.' at top right) to green:
                row0: "###"
                row1: "#.#" -> but wait, (1,1) is still red. 
                row2: "#.."

            Now the green cells: 
                row0: all three: connected? 
                row1: (1,0) and (1,2) are green, but (1,1) is red so they are not connected. 
                row2: only (2,0) is green.

            So the components:
                Component1: (0,0),(0,1),(0,2) -> connected.
                Component2: (1,0) -> adjacent to (0,0) -> so connected to the top row? Actually, (0,0) is adjacent to (1,0). So the entire top and bottom left are connected: 
                    (0,0) connected to (1,0) and (2,0). And (0,0) connected to (0,1) and (0,2). Then (1,2) is adjacent to (0,2)? (0,2) is now green, and (1,2) is green -> adjacent. 
                So actually, the entire green cells become one connected component.

            So after repainting (0,2): 1 component.

            Now (1,1): repainting the center.
                Then row1: "###"
                Then the grid: 
                    row0: "##." -> becomes "## "? No, we repainted (1,1). So:
                    row0: unchanged: "##." -> but wait, we are repainting only one cell: the center (1,1) becomes green.
                Then we have:
                    row0: (0,0),(0,1) -> connected.
                    row1: (1,0), (1,1), (1,2) -> connected.
                    row2: (2,0) -> alone.

                Now, (0,1) is adjacent to (1,1) -> so the top and middle rows are connected. Then (1,0) is adjacent to (2,0) -> so all connected? 
                Actually, (0,1) is adjacent to (1,1) -> so the top and middle are connected. (1,0) is adjacent to (2,0) -> so the middle and bottom left are connected. Then the entire grid is one connected component.

            So 1 component.

            Then (2,1): repainting the bottom middle.
                Then row2: "#.#" -> becomes "# #"? Actually, row2: initially (2,0) is green, (2,1) becomes green, (2,2) is red.
                Then we have:
                    The existing green: 
                    (0,0),(0,1) -> connected to (1,0) and (1,2) is connected to (0,2)? But (0,2) is red? 
                    Actually, we did not repaint (0,2). So the green cells:
                        row0: (0,0),(0,1) -> connected.
                        row1: (1,0), (1,2) -> not connected? because center (1,1) is red. 
                        row2: (2,0) and (2,1) -> connected.

                    Now, (1,0) is adjacent to (0,0) -> so (0,0) and (1,0) and (2,0) and (2,1) are connected? 
                        (0,0) -> (1,0) -> (2,0) -> (2,1): connected.
                    Then (0,1) is adjacent to (0,0) -> so the entire left and bottom middle is one component.
                    Then (1,2) is adjacent to (0,2) (which is red) and (2,2) (which is red) -> so it's isolated? 
                    So two components: one big one (left side) and (1,2).

            So 2.

            Then (2,2): repainting the bottom right.
                Then row2: (2,0) and (2,2) are green, and (2,1) is red? 
                Now, (2,2) is green: 
                    Then (1,2) is adjacent to (2,2)? So (1,2) and (2,2) are connected. 
                    And (1,2) is adjacent to (0,2)? which is red -> so the right side: (1,2) and (2,2) form one component.
                    The left side: (0,0),(0,1),(1,0),(2,0) is one component (because (0,0) to (1,0) to (2,0), and (0,0) to (0,1)).
                So two components.

            Therefore, the expected value = (1+1+2+2)/4 = 6/4 = 3/2.

            But in our algorithm, we computed k_i for each red cell as 1 for all? That led to E=1, which is wrong.

Why? 

In our algorithm, for the red cell (0,2): 
    neighbors: 
        down: (1,2) -> green -> comp_id=0 (because the entire green was one component originally).
        left: (0,1) -> green -> comp_id=0.
    Then k_i=1.

But after repainting, the entire grid becomes one component. So that's correct: 1 component.

For (1,1): 
    neighbors: 
        up: (0,1) -> comp0
        down: (2,1) -> red -> skip
        left: (1,0) -> comp0
        right: (1,2) -> comp0
    k_i=1.

But after repainting, we got 1 component. Correct.

For (2,1): 
    neighbors: 
        left: (2,0) -> comp0 -> so k_i=1.
        But then we computed k_i=1.

But after repainting (2,1), we got 2 components? Why?

The problem: originally, the entire green was one component. But when we repaint a red cell, we break the grid? Actually, no. The original green component is one. Then when we repaint (2,1), we are adding a green cell that is adjacent to (2,0) (which is in comp0) and no other green? Actually, (2,1) is adjacent to (2,0) and (1,1) is red. So in the original grid, (2,0) is connected to (1,0) and then to (0,0) and (0,1). (1,2) is connected to (0,2)? But (0,2) is red originally. So (1,2) is only adjacent to (0,2) (red) and (2,2) (red) -> so it is isolated? Actually, in the original grid, (1,2) is adjacent to (0,2) which was red and (2,2) red? Then how was (1,2) connected to the main component? 

Wait, originally: 
    Green cells: 
        (0,0), (0,1), (1,0), (2,0), (1,2)
    How are they connected?
        (0,0) is adjacent to (0,1) and (1,0).
        (1,0) is adjacent to (2,0).
        (1,2) is adjacent to (0,2) -> which is red -> not connected? 
        And (1,2) is adjacent to (1,1) (red) and (1,3) out of bound? and (2,2) (red). 
        So (1,2) is isolated? 

So originally, we have two connected components: 
    Component0: (0,0), (0,1), (1,0), (2,0)
    Component1: (1,2)

So comp_count = 2.

I see the mistake: in the original grid, the green cell at (1,2) is not connected to the main component. So we have two components.

Therefore, we must recompute the connected components for the original grid correctly.

In the example, the green cells are:
    (0,0), (0,1), (1,0), (2,0) -> connected? yes.
    (1,2) -> isolated.

So two components.

Then our algorithm:

    comp_count = 2.
    R=4.

    For red cells:
        (0,2): neighbors: down (1,2) -> comp1, and left (0,1) -> comp0 -> distinct: {0,1} -> k_i=2.
        (1,1): neighbors: up (0,1) -> comp0, left (1,0) -> comp0, right (1,2) -> comp1, down (2,1) -> red -> skip. Then distinct: {0,1} -> k_i=2.
        (2,1): neighbors: left (2,0) -> comp0 -> k_i=1.
        (2,2): neighbors: up (1,2) -> comp1 -> k_i=1.

    Then total_k = 2+2+1+1 = 6.

    Then numerator = 2*4 + 4 - 6 = 8****=6.
    E = 6/4 = 3/2.

Which matches.

So the mistake: originally, the green cells are not connected as one component. We must compute the connected components properly.

Therefore, we must do a BFS that does not assume connectivity between (1,0) and (1,2) because they are separated by a red cell.

So the algorithm is fixed.

Now, we can code accordingly.

### Code Implementation

We'll use BFS for connected components.

We note: the grid can be large (1000x1000). We use a deque for BFS to avoid recursion limits.

Steps:

    import collections

    mod = 998244353

    H, W = map(int, input().split())
    grid = [input().strip() for _ in range(H)]

    comp_id = [[-1] * W for _ in range(H)]
    comp_count = 0
    directions = [(1,0), (-1,0), (0,1), (0,-1)]

    for i in range(H):
        for j in range(W):
            if grid[i][j] == '#' and comp_id[i][j] == -1:
                comp_count += 1
                comp_id[i][j] = comp_count - 1   # assign id: from 0 to comp_count-1
                queue = collections.deque()
                queue.append((i,j))
                while queue:
                    x, y = queue.popleft()
                    for dx,dy in directions:
                        nx, ny = x+dx, y+dy
                        if 0<=nx<H and 0<=ny<W and grid[nx][ny]=='#' and comp_id[nx][ny]==-1:
                            comp_id[nx][ny] = comp_count-1
                            queue.append((nx,ny))

    R = 0
    for i in range(H):
        for j in range(W):
            if grid[i][j]=='.':
                R += 1

    total_k = 0
    for i in range(H):
        for j in range(W):
            if grid[i][j]=='.':
                seen = set()
                for dx,dy in directions:
                    ni, nj = i+dx, j+dy
                    if 0<=ni<H and 0<=nj<W and grid[ni][nj]=='#':
                        comp_label = comp_id[ni][nj]
                        seen.add(comp_label)
                total_k += len(seen)

    # Now compute expected value
    # E = (comp_count * R + R - total_k) / R
    numerator = (comp_count * R + R - total_k) % mod
    # Since R>=1, we can compute modular inverse
    invR = pow(R, mod-2, mod)
    ans = numerator * invR % mod
    print(ans)

Let me test with the example: 
    comp_count=2, R=4, total_k=6.
    numerator = (2*4 + 4 - 6) = 6.
    ans = 6 * pow(4, mod-2, mod) % mod

But 6/4 = 3/2, and 3/2 mod 998244353 = 3 * inv(2) mod 998244353.

    inv2 = pow(2, mod-2, mod) -> 2^(mod-2) mod mod.

But 3/2 mod 998244353 = 3 * inv2 mod mod.

But we have 6/4 = 3/2.

So we can check: 
    Example output: 499122178.

    Let me compute: 3 * pow(2,998244351,998244353) % 998244353.

    Actually, 3 * pow(2, 998244353-2, 998244353) % 998244353 = 3 * pow(2,998244351,998244353) % 998244353.

    But 2 * 499122178 % 998244353 = 998244356 % 998244353 = 3? 
        because 499122178 * 2 = 998244356, and 998244356 mod 998244353 = 3.

    So 499122178 = 3 * inv(2) mod mod? 

    Actually, 499122178 * 2 = 998244356 = 3 mod 998244353? 
        because 998244356 - 998244353 = 3.

    So 499122178 = 3/2 mod 998244353.

    Therefore, our answer for the example is 499122178.

So the code should output 499122178 for the example.

Now, we run the code on the example.

We'll also test with the other examples? But we can trust the logic.

But note: the grid representation: 
    Example1: 
        grid = [
            "##.",
            "#.#",
            "#.."
        ]

    The green cells: 
        (0,0): '#' -> comp0? 
        (0,1): '#' -> comp0? 
        (0,2): '.' -> skip.
        (1,0): '#' -> comp0? 
        (1,1): '.' -> skip.
        (1,2): '#' -> comp1? 
        (2,0): '#' -> comp0? 
        (2,1): '.' -> skip.
        (2,2): '.' -> skip.

    Then we have two BFS: 
        Start at (0,0): mark (0,0), (0,1) [from (0,0) right], then (1,0) [down], then (2,0) [from (1,0) down]. 
        Then (1,2) is not visited -> new component: comp1.

    So comp_id:
        row0: [0,0,-1]
        row1: [0,-1,1]
        row2: [0,-1,-1]

    Then R: count the '.' -> 4.

    Then for each red cell:
        (0,2): neighbors: down (1,2) -> comp1, left (0,1) -> comp0 -> distinct: {0,1} -> k_i=2.
        (1,1): neighbors: up (0,1) -> comp0, down (2,1) -> skip, left (1,0) -> comp0, right (1,2) -> comp1 -> distinct: {0,1} -> k_i=2.
        (2,1): neighbors: left (2,0) -> comp0 -> k_i=1.
        (2,2): neighbors: up (1,2) -> comp1 -> k_i=1.

    total_k = 2+2+1+1 = 6.

    Then numerator = 2*4 + 4 - 6 = 6.

    Then ans = 6 * inv(4) mod mod.

    inv4 = pow(4, mod-2, mod)

    6 * inv4 mod mod = (6 * pow(4, mod-2, mod)) % mod

    But 6/4 = 3/2, so we know it's 499122178.

    So we can compute: 
        inv4 = pow(4, 998244351, 998244353) 
        then 6 * inv4 % mod -> should be 499122178.

    Alternatively, 6 * inv4 = 6 * (inv(4)) = 6 * (1/4) = 3/2.

    So it's the same.

Therefore, the code is correct.

### Example 2 and 3
We can run the provided examples to check, but we assume the logic is sound.

### Final Code
Let me write the code accordingly.

Note: We must use collections.deque for BFS.

But worst-case: one BFS for the entire grid of green cells: which is 10^6 nodes. But we do BFS only once? Actually, we do BFS for each unvisited green cell. Total work is O(H*W).

We'll implement as above.

Let me code accordingly.

One more note: the comp_id for green cells are 0,1,...,comp_count-1. For red cells, comp_id is -1.

We are iterating only over neighbors that are green, so we only use comp_id for green cells.

We'll write the solution in the function as per the starter code.

Starter code:

    class Solution:
        def solve(self, H: int, W: int, grid: List[str]) -> int:

We'll implement accordingly.

But note: the input is given as H, W and list of strings.

We'll do:

    import collections
    from typing import List

    class Solution:
        def solve(self, H: int, W: int, grid: List[str]) -> int:
            mod = 998244353
            comp_id = [[-1] * W for _ in range(H)]
            directions = [(1,0),(-1,0),(0,1),(0,-1)]
            comp_count = 0
            # BFS for green cells
            for i in range(H):
                for j in range(W):
                    if grid[i][j]=='#' and comp_id[i][j]==-1:
                        comp_count += 1
                        comp_id[i][j] = comp_count-1
                        queue = collections.deque()
                        queue.append((i,j))
                        while queue:
                            x,y = queue.popleft()
                            for dx,dy in directions:
                                nx, ny = x+dx, y+dy
                                if 0<=nx<H and 0<=ny<W and grid[nx][ny]=='#' and comp_id[nx][ny]==-1:
                                    comp_id[nx][ny] = comp_count-1
                                    queue.append((nx,ny))
            # Count R: number of red cells
            R = 0
            for i in range(H):
                for j in range(W):
                    if grid[i][j]=='.':
                        R += 1

            # If R==0, but the problem states at least one, so skip check.
            total_k = 0
            for i in range(H):
                for j in range(W):
                    if grid[i][j]=='.':
                        seen = set()
                        for dx,dy in directions:
                            ni, nj = i+dx, j+dy
                            if 0<=ni<H and 0<=nj<W and grid[ni][nj]=='#':
                                seen.add(comp_id[ni][nj])
                        total_k += len(seen)

            # Compute expected value
            # E = (comp_count * R + R - total_k) / R
            # numerator = comp_count * R + R - total_k
            numerator = comp_count * R + R - total_k
            # Since numerator might be negative mod? But we do mod arithmetic.
            # We do: numerator * inv(R) mod mod
            invR = pow(R, mod-2, mod)   # modular inverse of R mod mod
            # But note: numerator is an integer that might be huge? But modulo mod we can do:
            numerator %= mod
            ans = numerator * invR % mod
            return ans

But wait: if numerator is negative? Then we should do:

            numerator = comp_count * R + R - total_k
            numerator %= mod
            if numerator < 0: 
                numerator += mod
            ans = numerator * invR % mod

But modulo operation in Python for negative: 
    a % mod gives non-negative result? Actually, if a is negative, then a % mod gives a value in [0, mod-1]. But let me check: 
        -1 % mod = mod-1.

    But our numerator: comp_count*R + R - total_k: 
        total_k can be as large as 4*R, so if comp_count is 0 and R is small, then numerator = R - total_k <= R - 0 = R (positive) or if total_k> R+comp_count*R, then negative? 
        But comp_count is at least 0, and R>=1, and total_k <= 4*R. 
        Then: 
            numerator = R*(comp_count+1) - total_k >= R*(0+1) - 4*R = R - 4*R = -3*R, which is negative.

        For example: if comp_count=0, then numerator = R - total_k. 
        And if total_k = 4*R, then numerator = -3*R.

        Then we do: numerator % mod: 
            (-3*R) % mod = mod - (3*R) % mod? 

        But then we multiply by invR? 

        Actually, we want (numerator / R) mod mod, which is the same as (numerator mod mod) * (invR mod mod) mod mod.

        But note: 
            (numerator / R) mod mod = ( (numerator mod mod) * (invR mod mod) ) mod mod.

        However, if numerator is negative, then we can compute:

            numerator * invR mod mod = ( (numerator mod mod) * invR ) mod mod.

        So we don't need to adjust the sign? Because modulo operation for negative is defined.

        But let me test: 
            numerator = -3*R, then 
            numerator * invR = -3 mod mod = mod-3.

        But the expected value: 
            E = (comp_count * R + R - total_k) / R = -3.

        But in reality, the expected value cannot be negative. 

        How can we get negative? 
            When total_k > comp_count * R + R.

        But total_k <= 4*R, and comp_count * R + R = R*(comp_count+1). 
            Then R*(comp_count+1) - total_k >= R*(comp_count+1) - 4*R = R*(comp_count****)

        So if comp_count**** < 0, i.e., comp_count < 3, then it can be negative.

        Example: comp_count=0, then numerator = R - total_k, and total_k can be up to 4*R, so numerator = R - 4*R = -3*R.

        But in that case, what is the expected value? 
            Original components: 0.
            Then we add one green cell: the new number of components is 1 - k_i? Actually, from the formula: 
                new_components = original_components + 1 - k_i = 0 + 1 - k_i = 1 - k_i.

            Then E = (1/R) * sum_{red cells} (1 - k_i) = (R - total_k) / R.

            And if total_k = 4*R, then E = (R - 4R)/R = -3.

        But that doesn't make sense: the number of components cannot be negative.

        Why? Because k_i is the number of adjacent distinct components. But if we start with no green, then k_i=0 for every red cell? Then new_components = 1 for every red cell. 

        So total_k should be 0? 

        How do we compute total_k for a grid with no green? 
            For each red cell, we look at neighbors: but no green, so we don't add any label. Then total_k=0.

        Then numerator = 0*R + R - 0 = R, then E = R/R = 1.

        Why did we get total_k=0? Because there are no green neighbors.

        So total_k cannot be 4*R in that case. 

        Actually, total_k is the sum of k_i, and k_i is the count of distinct green components adjacent. If there are no green cells, then k_i=0 for every red cell.

        Therefore, total_k=0.

        So numerator = 0*R + R - 0 = R, E=1.

        So the negative scenario I described (comp_count=0 and total_k=4*R) is impossible.

        Why? Because if comp_count=0, then there are no green cells, so for each red cell, k_i=0. So total_k=0.

        Similarly, if comp_count=1, then total_k <= 4*R? But actually, each red cell can have at most 4 distinct components? But wait: the entire grid has only one component? Then for a red cell, all adjacent green neighbors (if any) are in the same component. So k_i is either 0 or 1. Then total_k <= R.

        In fact, for a red cell, k_i is at most the number of connected components that are adjacent to it. But the entire grid has comp_count components, so k_i <= min(4, comp_count). 

        Therefore, total_k <= R * min(4, comp_count).

        And comp_count * R + R - total_k >= R*(comp_count+1) - R * min(4, comp_count) 
            = R * (comp_count+1 - min(4, comp_count))

        If comp_count >= 4, then min(4, comp_count)=4, so >= R*(comp_count****) = R*(comp_count-3) >= R*(4-3)=R>0.

        If comp_count=0: then total_k=0, so numerator=R>0.
        If comp_count=1: then total_k<=R, so numerator>= R*(1+1) - R = R>0.
        If comp_count=2: then total_k<=2*R, so numerator>= R*(2+1)-2*R = R>0.
        If comp_count=3: then total_k<=3*R, so numerator>= R*(3+1)-3*R = R>0.

        Therefore, numerator is always positive.

        So we don't have to worry about negative.

Therefore, we can simply do:

            numerator = comp_count * R + R - total_k
            numerator %= mod   # this will be in [0, mod-1] if nonnegative, but if negative we get mod-? but we know it's positive.

            ans = numerator * pow(R, mod-2, mod) % mod

But to be safe, we can do:

            numerator = (comp_count * R + R - total_k) % mod

But if the number is positive and less than mod, then modulo doesn't change it? But comp_count*R might be huge: up to 10^6*10^6=10^12, which mod 998244353 might be necessary.

But 10^12 mod 998244353: 
    998244353 * 1000 = 998244353000, which is about 10^12? 
    10^12 // 998244353 ≈ 1000 (since 998244353 ≈ 1e9, so 1e9 * 1000 = 1e12). 
    So we can do modulo to avoid huge integers? But Python integers are arbitrary precision, but modulo is cheap.

So we do:

            numerator = (comp_count * R) % mod
            numerator = (numerator + R) % mod
            numerator = (numerator - total_k) % mod

But then if (numerator - total_k) is negative, we get a negative? Then we can adjust:

            numerator = (comp_count * R + R - total_k) % mod

This expression: even if the whole is negative, modulo will convert to positive? But modulo operation for negative: 
    a % mod = (a + k*mod) % mod for some k to bring to [0, mod-1]. 

But we want the positive representation.

Alternatively, we can do:

            numerator = (comp_count * R % mod + R - total_k) % mod

But then if (R - total_k) is negative, then we get negative? Then we do:

            numerator = (comp_count * R % mod + (R - total_k)) % mod

But if (R - total_k) is negative, then we can do:

            numerator = (comp_count * R % mod + (R - total_k) % mod) % mod

But (R - total_k) % mod is the same as (R - total_k) mod mod? 

But R and total_k are integers. We can do:

            numerator = (comp_count * R + R - total_k) % mod

This is the safest.

So:

            numerator = (comp_count * R + R - total_k) % mod

But if this is negative, then we do:

            numerator %= mod

In Python, if the number is negative, then a % mod gives a nonnegative result? Yes.

Example: 
    -3 % 7 = 4.

But we know numerator is positive? So it's not necessary, but safe.

So we'll do:

            numerator = (comp_count * R + R - total_k) % mod

But actually, we can compute:

            numerator = (comp_count * R) % mod
            numerator = (numerator + R) % mod
            numerator = (numerator - total_k) % mod

But then if (numerator - total_k) is negative, then we get a negative? Then we can do:

            numerator = (numerator - total_k) % mod

But modulo with a negative: 

            (a - b) % mod = (a - b + mod) % mod   if a-b is negative? 

But we can do:

            numerator = (comp_count * R + R - total_k) % mod

to avoid intermediate steps.

I think the first way is simpler.

Final code:

            numerator = comp_count * R + R - total_k
            numerator %= mod
            invR = pow(R, mod-2, mod)
            ans = numerator * invR % mod

But note: if R is 0? But problem states at least one red cell.

So we are safe.

Now, we run the examples.

Example1: numerator=6, mod=998244353, then 6 % mod=6, then 6 * pow(4, mod-2, mod) % mod = 499122178.

Example2: 
    Input: 
        4 5
        ..#..
        .###.
        #####
        ..#..

    We'll compute comp_count, R, total_k.

    How many green? 
        row0: "..#.." -> 1 green at (0,2)
        row1: ".###." -> 3 green
        row2: "#####" -> 5 green
        row3: "..#.." -> 1 green at (3,2)

    Now, connected components:
        row0: (0,2) is isolated? 
        row1: (1,1),(1,2),(1,3) -> connected.
        row2: (2,0) to (2,4) -> connected. Also, (2,2) is adjacent to (1,2) -> so the entire row1 and row2 are connected? 
            (1,2) is adjacent to (2,2) -> so row1 and row2 are connected.
        row3: (3,2) is adjacent to (2,2) -> so connected to row2.

        Then (0,2): is it adjacent to (1,2)? (down) -> so it is adjacent to (1,2) which is in the big component? 
            Therefore, the entire grid is one connected component? 

        But wait: (0,2) is adjacent to (1,2): yes. 
        So one component.

        Then comp_count=1.

    R: count of red cells: 
        row0: 4 red (positions 0,1,3,4)
        row1: 2 red (positions 0,4)
        row2: 0
        row3: 4 red
        Total R = 4+2+0+4 = 10.

    Now, total_k: for each red cell, count distinct adjacent green components (which is one component).

    For a red cell, if it has at least one green neighbor, then k_i=1? If no green neighbor, then k_i=0.

    Which red cells have at least one green neighbor?

        row0: 
            (0,0): neighbors: down (1,0) -> '.' (red) -> skip; right (0,1) -> red; so no green -> k_i=0.
            (0,1): neighbors: down (1,1) -> green -> k_i=1.
            (0,3): neighbors: down (1,3) -> green -> k_i=1.
            (0,4): neighbors: down (1,4) -> '.' -> skip; left (0,3) -> red -> skip -> k_i=0.

        row1:
            (1,0): neighbors: up (0,0) -> red; down (2,0) -> green; right (1,1) -> green -> k_i=1.  [But note: both down and right are in the same component, so distinct=1]
            (1,4): neighbors: up (0,4) -> red; down (2,4) -> green; left (1,3) -> green -> k_i=1.

        row3:
            (3,0): neighbors: up (2,0) -> green -> k_i=1.
            (3,1): neighbors: right (3,2) -> green -> k_i=1.
            (3,2): is green -> skip (we are iterating red cells only) -> but (3,2) is green, so skip.
            (3,3): neighbors: left (3,2) -> green -> k_i=1.
            (3,4): neighbors: up (2,4) -> green -> k_i=1.

        So total_k = 
            row0: 0+1+1+0 = 2
            row1: 1+1 = 2
            row3: 1+1+1+1 = 4
            total_k = 2+2+4 = 8.

        Then numerator = 1*10 + 10 - 8 = 12.
        E = 12/10 = 6/5.

        Then we compute 6/5 mod 998244353: 
            = 6 * pow(5, mod-2, mod) % mod.

        The example output is 598946613.

        Let me check: 
            5 * 598946613 % 998244353 = 6? 
            Let me compute: 5*598946613 = 2994733065
            Then 2994733065 % 998244353 = 2994733065 - 3*998244353 = 2994733065 - 2994733059 = 6.

        So correct.

Example3: 
    Input: 
        3 4
        "#..."
        ".#.#"
        "..##"

    We'll compute:
        Green cells:
            row0: (0,0) -> green
            row1: (1,1) and (1,3) -> green
            row2: (2,2) and (2,3) -> green

        Components:
            (0,0): isolated? 
            (1,1): adjacent to (2,1) -> but (2,1) is red. Adjacent to (1,0) -> red. Adjacent to (1,2) -> red. Adjacent to (0,1) -> red. -> isolated? 
            (1,3): adjacent to (0,3) -> red; (1,2) -> red; (2,3) -> green -> so (1,3) is adjacent to (2,3). Then (2,2) is adjacent to (2,3). So (1,3), (2,3), (2,2) form a component.

            So two components: comp0: (0,0); comp1: (1,3),(2,3),(2,2).

        R: 
            row0: (0,1),(0,2),(0,3) -> 3
            row1: (1,0),(1,2) -> 2
            row2: (2,0),(2,1) -> 2
            Total R=7.

        Now, total_k: for each red cell, count distinct adjacent components.

        row0:
            (0,1): neighbors: 
                up: none; down: (1,1) -> green -> comp1? Actually, (1,1) is green -> comp_id=1? 
                left: (0,0) -> green -> comp0
                right: (0,2) -> red -> skip.
                -> distinct: {0,1} -> k_i=2.
            (0,2): neighbors: 
                left: (0,1) -> red -> skip
                down: (1,2) -> red -> skip
                right: (0,3) -> red -> skip
                -> k_i=0.
            (0,3): neighbors:
                left: (0,2) -> red
                down: (1,3) -> green -> comp1? 
                -> k_i=1.

        row1:
            (1,0): neighbors:
                up: (0,0) -> green -> comp0
                right: (1,1) -> green -> comp1? 
                down: (2,0) -> red -> skip
                -> k_i=2.  [comp0 and comp1]
            (1,2): neighbors:
                up: (0,2) -> red
                left: (1,1) -> green -> comp1
                right: (1,3) -> green -> comp1 -> same as left? 
                down: (2,2) -> green -> comp1
                -> distinct: {1} -> k_i=1.

        row2:
            (2,0): neighbors:
                up: (1,0) -> red
                right: (2,1) -> red -> skip
                -> k_i=0.
            (2,1): neighbors:
                left: (2,0) -> red
                up: (1,1) -> green -> comp1
                right: (2,2) -> green -> comp1 -> same as up? 
                -> k_i=1.

        total_k = 2+0+1+2+1+0+1 = 7.

        Then numerator = 2*7 + 7 - 7 = 14.

        E = 14/7 = 2.

        But the example output is 285212675.

        Why? 

        Let me check: 2 mod 998244353 = 2? But 285212675 != 2.

        So there must be a mistake.

        Let me recompute the connected components.

        Grid:
            row0: "#..." -> [0,0]:'#', [0,1]:'.', [0,2]:'.', [0,3]:'.' 
            row1: ".#.#" -> [1,0]:'.', [1,1]:'#', [1,2]:'.', [1,3]:'#'
            row2: "..##" -> [2,0]:'.', [2,1]:'.', [2,2]:'#', [2,3]:'#'

        Green cells:
            (0,0)
            (1,1), (1,3)
            (2,2), (2,3)

        Now, (0,0) is isolated? 
        (1,1): adjacent to (1,0) -> red; (1,2) -> red; (0,1) -> red; (2,1) -> red -> so isolated? 
        (1,3): adjacent to (1,2) -> red; (0,3) -> red; (2,3) -> green -> so adjacent to (2,3). 
        (2,2) and (2,3): adjacent to each other.

        So components: 
            Component0: (0,0

## Final Solution

