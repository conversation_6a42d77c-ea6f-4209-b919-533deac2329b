# Solution for 925_D.md

Generated at: 2025-07-22 06:06:29
Model: deepseek-reasoner

## Reasoning Process

Alright, I'm going to tackle this problem about <PERSON> in the Aztec catacombs. Let me try to understand what's going on here.

So, we have n caves connected by corridors that can be open or closed. Indiana starts at cave 1 and needs to get to cave n. The twist is that when he moves from cave x to cave y through an open corridor, all corridors connected to cave x flip their state: open ones close and closed ones open. We need to find the minimum number of corridors he has to pass through to get to cave n, and also output the path. If it's impossible, we return -1.

First, I'll look at the examples to get a better sense.

**Example 1:**
Input: n=4, m=4, corridors: [1-2, 2-3, 1-3, 3-4]
Output: 2 (corridors) and path 1->3->4.

Let me simulate this:
- Start at cave 1. Initially open corridors: to 2 and 3 (since 1-2 and 1-3 are open).
- He goes from 1 to 3. After moving, all corridors from cave 1 flip. So now, 1-2 and 1-3 become closed? But wait, he just used 1-3, so that was open. After moving, the corridors connected to 1 (which are 1-2 and 1-3) flip. So 1-2 was open, now closed; 1-3 was open, now closed. But he's now at 3.

At cave 3, initially open corridors were 2-3, 1-3, 3-4. But after moving from 1 to 3, the corridors from 1 have been flipped. So now, the state of the corridors:
- 1-2: closed (was open, flipped)
- 1-3: closed (was open, flipped)
- 2-3: still open? Because flipping only affects corridors connected to the starting cave (1). So 2-3 remains open, and 3-4 remains open.

Now at cave 3, open corridors: 2-3 and 3-4. He can go to 4. So total corridors: 2. Path: 1->3->4.

**Example 2:**
Input: n=4, m=2, corridors: [1-2, 2-3]
Output: 4 corridors and path 1->2->3->1->4.

This one is trickier. Initially, open corridors: 1-2 and 2-3. So from cave 1, he can only go to 2.

Step-by-step:
1. Start at 1. Open corridors from 1: to 2 only. So he goes to 2.
   - After moving, all corridors from 1 flip. So 1-2 becomes closed? But wait, initially it was open, so flipping it makes it closed. Now, the state: 1-2 closed, 2-3 still open (because flipping only affects corridors from the start cave, which was 1). Also, are there any other corridors? The problem says each pair of caves is connected, but initially only two are open. So other corridors are closed? Like 1-3, 1-4, 2-4, 3-4 are closed initially? 

Wait, the input says there are m initially open corridors. So for n=4, there are 6 possible corridors (since it's a complete graph). But initially, only 1-2 and 2-3 are open. So 1-3, 1-4, 2-4, 3-4 are initially closed.

After step 1: 
- Move 1->2: flip corridors at 1. So 1-2 becomes closed, 1-3 (was closed) becomes open, 1-4 (was closed) becomes open. So now, open corridors: 1-3, 1-4, and 2-3 (still open). 2-4 is still closed? Because flipping only at 1.

So now at cave 2, open corridors: 2-3 (open) and 2-4 is closed? But we have 2-3 open. So he can go to 3.

2. Move 2->3: now, after moving, flip corridors at 2. Corridors from 2: 2-1 (which was closed? Wait, after step1, 1-2 was closed, but 2-1 is the same. So 2-1 is closed, 2-3 was open, 2-4 closed. After flipping: 2-1 becomes open? Because it was closed; 2-3 becomes closed; 2-4 becomes open.

So now, open corridors: 1-3 (open), 1-4 (open), 2-1 (open), 2-4 (open). 2-3 is closed.

Now at cave 3, what corridors are open? From 3: 3-1 (was it open? After step1, 1-3 became open, so 3-1 is open), 3-2 (now closed because we flipped at 2 when leaving), 3-4 (initially closed, still closed? Because flipping at 2 didn't affect 3-4? But 3-4 was closed and no flip at 3 yet? Actually, when moving from 2 to 3, we flipped corridors at 2, which doesn't affect 3-4. So 3-4 is still closed.

So from cave 3, open corridors: to 1 (since 1-3 is open). So he can go back to 1.

3. Move 3->1: flip corridors at 3. Corridors from 3: 3-1 (currently open), 3-2 (closed), 3-4 (closed). After flipping: 3-1 becomes closed, 3-2 becomes open, 3-4 becomes open.

Now, after flipping at 3, the state is:
- 1-2: was open? Wait, after step 2, we had 2-1 open? Yes, because when we flipped at 2, we opened 2-1. Then, at step 3, we flipped at 3: so 3-1 was open and becomes closed. Also, 3-4 becomes open.

So now, open corridors: 
- 1-2: open? Because 2-1 is open? Actually, the corridor is the same. So 1-2 is open? But after step1, we closed 1-2? Then at step2, when flipping at 2, we opened 2-1, which is the same as 1-2. So yes, 1-2 is open. Also, 1-4 is open (from step1). 2-4 is open (from step2). 3-4 is open (from step3). 3-1 is now closed, 3-2 is open (from step3).

Now, Indiana is at cave 1. From cave 1, open corridors: to 2 and 4. So he can go directly to 4.

4. Move 1->4: done.

So the path: 1->2->3->1->4. Total 4 corridors.

So the problem requires the minimum number of corridors. In the first example, 2 is minimal. In the second, 4 is minimal.

Now, how to solve this for large n (up to 300,000) and m (up to 300,000). So we need an efficient solution.

**Key observations:**

1. **State Representation:** The state of the corridors changes dynamically as Indiana moves. The state of a corridor between u and v depends on the number of times the caves u and v have been visited? Actually, no. Because when you leave a cave, you flip all its corridors. So the state of a corridor (u, v) is determined by the number of times either u or v has been left? Actually, when leaving u, you flip all corridors incident to u, including (u,v). Similarly, leaving v flips (u,v). So the state of (u,v) is flipped every time you leave u or leave v.

But note: when you traverse from u to v, you leave u, so you flip the corridor (u,v) as part of leaving u. But then you are entering v, so that doesn't flip v's corridors until you leave v. So after moving from u to v, the corridor (u,v) is actually flipped twice? No: when you leave u, you flip (u,v). Then, when you leave v later, you flip (u,v) again. But at the moment of arrival at v, the corridor (u,v) is in the opposite state from when you started the move? Actually, no: because when you leave u, you flip (u,v) to the opposite state. Then, when you leave v, you flip it again. But for the current move, you used it when it was open. After leaving u, it becomes closed. Then when you leave v, it becomes open again? 

Wait, let's think about the state of a corridor (u,v). Initially, it has a state (open or closed). Then, every time Indiana leaves u or leaves v, the state flips. So the state is determined by the parity (odd or even) of the total number of times either u or v has been exited. 

But note: when he moves from u to v, he leaves u, so the state of (u,v) flips. Then, when he leaves v (to go to some other cave w), then the state of (u,v) flips again. So if he leaves u and then later leaves v, the corridor (u,v) is flipped twice, so it returns to its original state? 

But at the moment he is at v, the state of (u,v) is flipped (because he just left u). So from v, the corridor (u,v) is now closed? Because it was open when he traversed, then flipped to closed. 

This seems complex. Maybe we can model the problem as a state machine or use graph transformations.

2. **Graph as Two Layers?** I recall problems where state flips lead to a layered graph. For example, each cave can be in two states: even or odd number of flips? But actually, the state of the entire graph depends on the history. However, note that the flipping happens per cave and affects all incident corridors. But the state of a corridor (u,v) is determined by the number of times u and v have been exited. Specifically, if we let f(u) be the number of times we have left u, then the state of (u,v) is open if the initial state XOR (f(u) + f(v)) is odd? Actually, each flip at u or v changes the state. So the current state is the initial state plus the number of flips at u and at v modulo 2. So: state(u,v) = initial_state(u,v) XOR (f(u) XOR f(v))? Actually, if we represent the flips as binary, then each flip at u toggles the state. So if we flip u once, the state changes; flip again, it goes back. Similarly for v. So the total flips for (u,v) is f(u) + f(v). Then state = initial_state XOR ( (f(u) + f(v)) % 2). But actually, modulo 2, addition is XOR? No, modulo 2, addition is the same as XOR only for bits. Actually, (f(u) + f(v)) mod 2 is equivalent to (f(u) XOR f(v))? No, wait: if both are 1, then 1+1=2 mod2=0, and 1 XOR 1 is 0. If one is 1 and the other 0, then 1+0=1 mod2=1, and 1 XOR 0=1. So yes, (f(u) + f(v)) mod 2 = f(u) XOR f(v). So state(u,v) = initial_state(u,v) XOR (f(u) XOR f(v)).

But note: when we leave a cave, we are about to traverse an edge. So the state of the edge we are about to traverse is determined by the current flips. 

However, for the purpose of the graph, we can model each cave with an additional state: the parity of the number of times we have left that cave. But note: the number of times we leave a cave is the same as the number of times we have visited it? Actually, we leave a cave when we move to another cave, except the last cave. So for a cave that is visited, the number of times we leave it is the number of times we were there and then left (so if we visit a cave k times, we leave it k-1 times? Or k times? Actually, we start at cave 1. Then, each time we move from a cave, we leave it. So for the starting cave, we leave it once when we move to the next. Then if we come back, we leave it again. So the number of leaves is the number of times we moved out of the cave.

But we don't know the path in advance. So perhaps we can model the problem with a state for each node: (node, parity of leaves from that node). Or more precisely, for each cave, we can represent two states: even or odd number of times we have left it. Then the entire state of the system is the set of parities for all nodes. But that would be 2^n states, which is too big.

Alternatively, we can model the state of the system by the parities of all nodes. However, the problem has n up to 300,000, so we cannot have exponential states.

3. **Alternative Insight:** Consider that when Indiana is at a cave u, the state of an edge (u,v) is determined by the initial state and the parities of u and v. Specifically, the current state of (u,v) is: initial_state(u,v) XOR (p_u XOR p_v), where p_u is the parity of leaves from u (so far), and similarly for v. But when he is at u, the p_u we have is the current parity. Then, if he moves to v, then as he leaves u, we will flip the state of all edges incident to u, including (u,v). But note: at the moment of moving, the edge must be open. So we require that at the moment, state(u,v) is open, i.e., 1. So:

condition for moving from u to v: initial_state(u,v) XOR (p_u XOR p_v) = 1.

After moving, we leave u, so p_u is flipped (because we leave u, so we add one leave). Then, we arrive at v. But note that the parity of v hasn't changed? Actually, we are arriving at v, so we haven't left v yet. So the new state at u: p_u becomes p_u XOR 1. The p_v remains the same? Or does arriving at v count? The problem says "when Indiana goes from cave x to cave y", then after the move, all corridors connected to x flip. It doesn't say anything about y. So only the leaving cave (x) is flipped. So the parity of y (p_v) remains the same.

But then, when we are at v, the parity for u is now p_u XOR 1, and for v is still p_v.

Now, the challenge is that we don't know the parities in advance. We are building the path.

However, we can model this as a graph where each node is represented by (cave, parity_vector)? But the parity vector has n components, which is too big.

Alternatively, we can think of the entire parity state as a bitmask? But that's 2^n, which is infeasible.

4. **Reduction to a Standard Graph Problem?** 

I recall that similar problems (with state flips) are sometimes solved by duplicating the graph: one copy for even parity at each node and one for odd? But that would be two copies per node? Actually, we can have a state (node, p) where p is the parity of the number of times we have left the current node? But wait, actually, the state of each node is independent. However, when we move, we change the parity of the current node (because we leave it). So we can model each node as having two states: even and odd (for the number of leaves from that node). Then, the entire state of the system is the current cave and the parities of all nodes? But that is too much.

But note: we don't need the entire parity vector. Because the condition for an edge (u,v) being open at a state depends on the parities of u and v. However, the parities of other nodes do not directly affect the edge (u,v). So for the purpose of traversing an edge from u to v, we only care about p_u and p_v.

But then, when we leave u, we flip p_u and also flip the state of all edges incident to u. But that flip affects edges incident to u, which are to other nodes. So the state of an edge (u,w) is affected by p_u and p_w. But when we are at u, we only consider edges from u to other nodes. So for each edge (u,v), the condition to traverse is: initial_state(u,v) XOR (p_u XOR p_v) = 1.

After traversing, we change p_u to p_u XOR 1 (because we left u). Then we are at v, and p_v remains the same.

So the state for a node can be represented as (cave, p_1, p_2, ... p_n)? Still too big.

Alternatively, we can model the state as (current cave, p_current), but that is insufficient because the state of edges also depends on the parity of the other node.

Wait, actually, for an edge from u to v, we need to know p_u and p_v. So if we are at u and we know p_u, then to traverse to v, we need to know p_v. But p_v might be different for different v. So we would have to store the entire vector? 

But note that the problem does not require the entire path to be known in advance, but we are to compute the shortest path. We can use Dijkstra? But the state space is 2^n, which is too big.

This seems challenging.

5. **Another Insight:** After moving from u to v, the only node that changes parity is u. Then, the condition for an edge (u,w) after leaving u is flipped, but for other edges, they remain. 

Also, note that the problem asks for the minimum number of corridors. So we are to minimize the number of moves.

I recall that in Example 2, we had to go back to cave 1. So sometimes backtracking is necessary.

6. **Breadth-First Search (BFS) in State Space?** Since each move changes one node's parity, and the state space is 2^n, which is too big for n=300,000.

We need a more efficient approach.

7. **Modeling as a Graph with Two Layers per Node?** We can duplicate each node into two states: even and odd. So we have two copies of the graph: node u_even and u_odd, representing the current cave and the parity of that cave (the number of leaves from that cave mod 2). 

But note: the condition for an edge (u,v) also depends on the parity of v. So when we are at u_even, the parity of u is 0. Then, for an edge (u,v) that is initially open, the current state is: initial_open XOR (p_u XOR p_v) = 1 XOR (0 XOR p_v). We want this to be 1. So 1 XOR (0 XOR p_v) = 1  => 1 XOR p_v = 1 => p_v must be 0. Similarly, if the edge is initially closed, then initial state is 0, so 0 XOR (0 XOR p_v) = p_v. We want that to be 1, so p_v must be 1.

Similarly, when at u_odd, for an initially open edge (u,v): 1 XOR (1 XOR p_v) = 1 XOR (1 XOR p_v) = p_v. We need p_v=1 for the edge to be open? Because we require the current state to be 1. Similarly, for initially closed: 0 XOR (1 XOR p_v) = 1 XOR p_v. We need that to be 1 => p_v=0.

So, to summarize:

- For an edge that is initially open:
  - From u_even: can go to v_even? Because then p_v=0, and condition: 1 XOR (0 XOR 0) = 1 -> valid? Actually, 0 XOR 0 is 0, then 1 XOR 0 = 1 -> valid. So to v_even.
  - From u_odd: 1 XOR (1 XOR p_v) = 1? So 1 XOR (1 XOR p_v) = p_v. We need p_v=1 -> so to v_odd.

- For an edge that is initially closed:
  - From u_even: 0 XOR (0 XOR p_v) = p_v. We need p_v=1 -> so to v_odd.
  - From u_odd: 0 XOR (1 XOR p_v) = 1 XOR p_v. We need that to be 1 => p_v=0 -> so to v_even.

But note: after traversing from u to v, we leave u, so the parity of u flips. Therefore, the state of u in the new graph will be the opposite.

Wait: in the state (u_even), we are at u and u's parity is even. Then, we move to v: then we leave u, so u's parity becomes odd. But we are leaving u, so we are no longer at u. We are now at v. So in the new state, u is now odd (but we don't represent u anymore) and we are at v. Also, we haven't left v, so the parity of v remains as it was? 

But when we move from u to v, we only change the parity of u. The parity of v does not change? So in the new state, we are at v, and the parity of v is unchanged. But the parity of u has been flipped.

However, in the graph of states (node, parity_of_that_node), the state of other nodes is not stored. So when we move from u to v, we change the parity of u (so we would have to update all edges incident to u? But we are not storing the entire vector. 

Actually, in the two-layer graph, the state is (current_node, parity_of_current_node). But the parity of the current node? Or the parities of all nodes? 

Wait, actually, the state of the system is defined by the current cave and the entire set of parities? But we are only storing the current cave and its parity? That is insufficient because the condition for an edge (u,v) also depends on the parity of v. And the parity of v might not be the same as the last time we visited v? 

So if we model the state as (current_cave, parity_vector), it's too big. If we model it as (current_cave, parity_of_current_cave) and ignore the others, then we don't know the parity of the other nodes. Therefore, we cannot determine if an edge is open.

This suggests that we need to store the entire parity state? But that's 2^n states, which is infeasible.

8. **Rethinking the State Representation:** 

Note that when we are at a cave u, the only edges we care about are incident to u. And the state of an edge (u,v) is initial_state(u,v) XOR (p_u XOR p_v). Since we are at u, we know p_u. But we don't know p_v. So we cannot decide if the edge (u,v) is open unless we know p_v. 

But p_v might be different each time we come to u. So we would have to try different possibilities? 

Alternatively, we can store the state as (current_cave, p_1, p_2, ... p_n) but that's too big.

9. **Observations on Parity Changes:** 

Notice that the entire system's state is defined by the vector p = (p_1, p_2, ..., p_n). The initial state: all p_i=0 (since no leaves yet). Then, each move from a cave u to a cave v: we flip p_u (because we leave u). 

Also, the condition for the move: we require that the edge (u,v) is open: so initial(u,v) XOR (p_u XOR p_v) = 1.

After the move, we are at v, and the new state has p_u flipped.

So the entire state is the vector p. The graph has 2^n nodes, which is too big.

10. **More Insight:** 

But note that we start with p = (0,0,...,0). The goal is to reach cave n with any state p. We want the minimum number of moves.

The moves are: from a state p, at cave u, we can move to v if (u,v) is an edge and initial(u,v) XOR (p[u] XOR p[v]) = 1. Then, the new state is p' = p with p[u] flipped. And we move to cave v.

But we don't care about the entire state p, because the cost is the number of moves, and each move is cost 1. 

But the state space is 2^n, which is too big for n=300,000.

11. **Another Idea: Is the Graph Bipartite?** 

I recall that in similar problems (like lights out), sometimes the system is linear over GF(2). But here, we have a constraint per move.

Alternatively, we can model the problem as follows:

We have an initial state of edges. We start at 1. We want to reach n. Each time we leave a cave u, we flip all edges incident to u. 

But note that flipping edges twice cancels out. 

Also, the path is a sequence of moves. Let x_i be the number of times we leave cave i (for i=1 to n). Then, the state of an edge (u,v) is initial(u,v) XOR (x_u + x_v) mod 2. 

For an edge (u,v) to be traversed from u to v at the moment of traversal, we require that at that point in the path, the state of (u,v) is open. But when we traverse it, we are leaving u, so we are about to increment x_u by 1. 

Wait, let me denote:

- Let x_u be the number of times we have left u just before the current move.

Then, when we move from u to v, the condition is: initial(u,v) XOR (x_u XOR x_v) = 1.

After the move, we set x_u = x_u + 1 (so the parity of x_u flips).

But note: the move itself is the event of leaving u, so x_u is the count before leaving.

Then, the new x_u becomes x_u+1. And we are now at v, and we haven't left v yet, so x_v remains.

But also, we might traverse the same edge multiple times.

The problem is to find a sequence of moves (a walk) from 1 to n, minimizing the number of moves (edges traversed), such that for each move from u to v at step i, we have:

   initial(u,v) XOR (x_u_i XOR x_v_i) = 1,

where x_u_i is the number of times we have left u before this move, and similarly for v.

And after the move, x_u is incremented by 1.

This seems like a system with n counters (x_1,...,x_n) and we start with all 0. We want a walk (sequence of vertices) starting at 1 and ending at n, with the above condition for each consecutive edge.

But the counters are updated as we go.

We are to minimize the number of moves.

12. **Looking for Known Problems:** This resembles a shortest path problem in a graph with state-dependent edges, but the state is the entire vector of counters mod 2? Because the condition is mod 2.

So we can consider the state as (current cave, x_1 mod 2, x_2 mod 2, ..., x_n mod 2) but that is 2^n states.

However, note that we start with all 0 and the goal is to reach n with any state. But the state space is huge.

But perhaps we can reduce the state representation? 

Notice that the condition for an edge (u,v) is: initial(u,v) XOR (x_u XOR x_v) = 1.

Which means: x_u XOR x_v = 1 XOR initial(u,v).

Let c_{u,v} = 1 XOR initial(u,v). Then, the condition is: x_u XOR x_v = c_{u,v}.

So for each edge, we have a constraint: x_u XOR x_v = c_{u,v} at the time of traversal.

But the counters are changing as we move. 

Also, each move from u to v increments x_u (and leaves x_v unchanged until we leave v).

This seems complex.

13. **BFS in terms of counters?** We are to find a walk that satisfies the constraints. The minimal number of moves is the length of the walk.

But the walk is a sequence of vertices. The counters are determined by the number of times we have left each node. 

The condition for moving from u to v at a certain step is: x_u (current) XOR x_v (current) = c_{u,v}.

After the move, x_u increases by 1, so x_u becomes x_u+1, which flips the bit.

So the new state has x_u flipped, and we are at v.

This is similar to a graph where each node is labeled by the current cave and the current vector of parities. But the vector of parities has n bits, so 2^n states.

But n is up to 300,000, so 2^n is astronomical.

14. **Additional Insight from Constraints:** The number of moves can be up to 10^6, as per the output constraint. So the shortest path is at most 10^6 moves. This suggests that the BFS might be feasible in terms of the number of states visited, but the state space is defined by the current cave and the entire parity vector, which is 2^n states, which is too big.

15. **Alternative Approach: Can we use Dijkstra with state = (current cave, parity_vector) but compressed?** But the parity vector is n bits, which is 300,000 bits, which is 37.5 KB per state. And there could be up to 10^6 states? Then the memory would be 37.5 KB * 10^6 = 37.5 GB, which is too much.

So we need a smarter way.

16. **Observations on the XOR Constraints:** 

We have for each edge traversal a constraint: x_u XOR x_v = c_{u,v} at the time of traversal.

But the counters x_u and x_v are updated as we move. 

Consider the entire walk. Let d_u be the number of times we leave u in the entire walk. Then, for each move from u to v (at the time of the move), the value of x_u is the number of times we have left u before this move. Similarly for x_v.

But the entire walk has a fixed set of moves. The sequence of moves defines the order.

This seems intractable.

17. **Research or Known Similar Problems:** 

I recall a problem from CodeForces: "Indiana Jones and the Catacombs". In fact, the problem statement matches. 

Looking for known solutions: 

In the known solutions for this problem, they often use BFS in a graph where each node is duplicated into two states: even and odd. But then, how do they handle the parity of the adjacent nodes? 

Ah, I see: they store the state as (node, parity_of_this_node) and then the edge conditions depend on the parity of the adjacent node, but they relax the edge by considering that the parity of the adjacent node might be updated on the fly? 

Wait, no. In the two-layered graph, each node u is split into u0 and u1 (even and odd for the number of times we have left u). Then, the edge from u to v is represented as follows:

- From u0 to v0 or v1, based on the initial edge state and the condition.

As derived earlier:

- For an initially open edge (u,v):
  - From u0, we require p_v=0, so to v0. But after the move, we flip u's parity. So we are at v0, and u's parity becomes 1. But we don't care about u anymore? In the state, we only store the current node and its parity. And also, the other nodes' parities are not stored. 

This is the issue: the state of the entire graph is not stored, only the current node and its parity. But the condition for the edge (u,v) depends on the parity of v, which might not be up-to-date in our state representation. 

However, in the two-layered graph, we are going to store the state as (node, parity_of_that_node). And when we are at u0, that means that the current parity of u is 0. The parity of v is stored as part of the state when we arrive at v. 

But how do we know the parity of v when we are at u? We don't. But in the graph, we will have to try both possibilities? 

No. In the two-layered graph, we assume that the state is (current_node, p_current), and the parity of other nodes is not stored. Then, when moving from u to v, we have two choices for v's parity? But that is not correct.

I found a known solution for a similar problem:

```cpp
#include <bits/stdc++.h>
using namespace std;
const int N = 300000;
vector<int> edges[2][N];
int d[2][N], p[2][N];

int main() {
    int n, m;
    scanf("%d %d", &n, &m);
    for (int i = 0; i < m; i++) {
        int u, v;
        scanf("%d %d", &u, &v);
        u--; v--;
        edges[0][u].push_back(v);
        edges[1][v].push_back(u);
    }
    // ... 
}
```

Wait, this solution seems to build two graphs: one for the given edges and one for the reverse edges? 

Actually, the known solution for this problem (from CodeForces) uses a BFS on a state (node, parity) and maintains distance and parent. 

Here's a typical solution:

```cpp
#include <bits/stdc++.h>
using namespace std;
const int MAXN = 300000;
vector<int> e[MAXN];
int dist[2][MAXN], parent[2][MAXN];

int main() {
    int n, m;
    scanf("%d%d", &n, &m);
    for (int i=0; i<m; i++) {
        int a, b;
        scanf("%d%d", &a, &b);
        a--; b--;
        e[a].push_back(b);
        e[b].push_back(a); // bidirectional
    }

    memset(dist, -1, sizeof(dist));
    queue<pair<int, int>> q;
    dist[0][0] = 0;
    q.push({0,0});

    while (!q.empty()) {
        int u = q.front().first;
        int parity = q.front().second;
        q.pop();
        for (int v : e[u]) {
            int new_parity = 1 - parity;
            if (dist[new_parity][v] == -1) {
                dist[new_parity][v] = dist[parity][u] + 1;
                parent[new_parity][v] = u;
                q.push({v, new_parity});
            }
        }
    }

    // Then, if either dist[0][n-1] or dist[1][n-1] is not -1, choose the minimal
    // and then reconstruct the path.
```

But wait, this BFS does not consider the initial state of the edge! It simply does: from u with parity p, then to v with new_parity = 1-p, and the edge is always traversable? That doesn't match the problem.

In the problem, the traversability depends on the initial state of the edge and the parities of u and v.

So why in the above solution, they are not checking the condition? 

Ah, perhaps I have a misunderstanding. In the known problem "Indiana Jones and the Catacombs", the corridors are initially open or closed, and the input gives the initially open corridors. But in the above solution, they are building an adjacency list for all edges (regardless of initial state) and then doing a BFS on two states.

But then how do they account for the initial state? 

Alternatively, the problem might be that the corridors are initially open, and the closed ones are not given? But the input says: "The next m lines describe the open corridors." and "Each unordered pair of caves is presented at most once." So if a corridor is not in the input, it is initially closed.

So the graph is complete, but we are only given the initially open ones. The others are initially closed.

In the above solution, they are building an adjacency list for all edges that are present (initially open) and also for the reverse? But they are doing:

```cpp
        e[a].push_back(b);
        e[b].push_back(a);
```

So they are including both directions for an initially open corridor.

But then in the BFS, they are not checking the condition based on the initial state and parities. Why?

Let me simulate with Example 1: n=4, m=4, edges: 1-2, 2-3, 1-3, 3-4.

In the BFS, states: (node, parity)

Start: (0,0) [cave 0, which is 1 in 1-indexed]

Then, from 0 (parity 0), we can go to its neighbors: 1, 2, and also 3? Because the open corridors are 0-1, 0-2, 0-3? Wait, in the input, initially open corridors are 1-2, 2-3, 1-3, 3-4. In 0-indexed: 0-1, 1-2, 0-2, 2-3.

So e[0] = [1,2] (because 0-1 and 0-2 are open), and 0-3 is not given? But wait, 3-4 is given, but 0-3 is not. So initially, 0-3 is closed.

So from 0 (cave1), the open corridors are to 1 and 2 (caves2 and 3). 

In the BFS above, from (0,0), they go to each neighbor v, and then new state is (v, 1) because new_parity = 1-0=1.

So from (0,0), they push (1,1) and (2,1).

Then, from (1,1) [cave2, parity1], they will go to its neighbors: 0, 2. So to 0: new_parity = 1-1=0, so state (0,0) already visited. To 2: new state (2,0). But dist[0][2] might be unvisited, so they set it to dist[1][1]+1 = 2, and parent is 1.

From (2,1) [cave3, parity1], neighbors: 0,1,3. 
- To 0: (0,0) visited.
- To 1: (1,0) -> new state, set dist[0][1] = 2, parent (1,0) = 2.
- To 3: new state (3,0) with dist=2.

Then, we have dist[0][3] = 2? So the answer is 2.

That matches.

But how about the edge condition? In the BFS, they are not checking the condition. Why is that? 

Let me check the condition for the first move: from 0 to 1.

At state (0,0): p0=0. For edge (0,1): initially open, so condition: 1 XOR (0 XOR p1) = 1? But what is p1 initially? We start with all parities 0. So p1=0. So 1 XOR (0 XOR 0) = 1 XOR 0 = 1 -> satisfied.

After moving, we flip p0 to 1, and we are at 1. So now, the state of the system: p0=1, p1=0 (unchanged), and we are at 1.

But in the BFS state, we are storing at node 1 the new_parity=1. But the new_parity for node 1 is 1? Or is it the parity of the current node? In the state, they store (node, parity) where parity is the number of times we have left this node? But we haven't left node 1 yet. We just arrived.

In the BFS state, the parity stored is for the current node: but the parity of a node is the number of times we have left it. When we arrive, we haven't left it, so it should be even? But in the state, after moving to 1, we set the state as (1,1). That doesn't match.

In the problem, when we arrive at a node, we haven't left it yet. So the parity should be the number of times we have left it, which for the first arrival is 0. But in the BFS, after moving from 0 to 1, we set the state for 1 as parity 1.

This is confusing.

Let me look at the known solution's explanation. In the editorial for the problem, it says:

We consider states (v, parity), where parity is the number of times the current vertex has been exited, mod 2. But wait, in the state, we are at vertex v, and the parity stored is for vertex v. 

When we arrive at v for the first time, we haven't left it, so its parity is 0. But after we leave it, we will set it to 1. 

In the BFS, when we are at state (u, p), then for each neighbor v, we will leave u (so we haven't left u yet in the current state? But then when we leave u, we flip it, so the new parity for u becomes p_u+1. But we are not storing u anymore. We are storing v with a new parity.

For the new node v, after arriving, what is its parity? It remains unchanged. But the BFS state for v is stored with new_parity = 1 - p? That doesn't seem to be the parity of v.

Let me reread the known solution's comments.

In the known solution, they do:

```cpp
        for (int v : e[u]) {
            int new_parity = 1 - parity;
            if (dist[new_parity][v] == -1) {
                dist[new_parity][v] = dist[parity][u] + 1;
                parent[new_parity][v] = u;
                q.push({v, new_parity});
            }
        }
```

The new_parity is stored for v. But what does new_parity represent for v? It is not the parity of v, but rather the parity of u after leaving? Or something else.

Actually, after moving from u to v, we leave u, so u's parity flips. But the parity of v doesn't change. So why is the new state for v having new_parity = 1 - parity? And what is 'parity' in the state? It is the parity of the current node at the time of being at that node.

In the state (u, parity), it means: we are at u, and the number of times we have left u is 'parity' (mod 2). 

When we are at u, we are about to leave it. So we will leave u, which will flip its parity. But then we move to v. The act of leaving u flips u's parity, but that's not stored in the state for v. For v, we are just arriving, so we haven't left it yet. Therefore, the parity for v in the new state should be 0? Or the same as before? 

But in the BFS, they are not storing the entire vector. They are storing per node a parity state. 

The key is: the state (v, p_v) means that we are at v, and the number of times we have left v is p_v. But we haven't left v yet in this state. 

When we move from u (with state (u, p_u)) to v, we are about to leave u. So we will increment the leave count for u, so u's parity becomes p_u+1 mod 2. And for v, we are arriving, so we haven't left it. But the leave count for v is already p_v (from previous visits)? 

This is the catch: the leave count for v may have been set by previous visits. But in the BFS, we are storing the state (v, p_v) and we don't know what the actual leave count is. 

This BFS only works if the leave count for v in the state (v, p_v) is exactly p_v. But how do we know that when we arrive at v from u, the leave count for v is p_v? 

We don't. But in the BFS, we are only storing two states per node. And we update them as we see a shorter path. 

The condition for the edge (u,v) to be traversable from u to v is: initial(u,v) XOR (p_u XOR p_v) = 1.

But in the BFS, when we are at (u, p_u), we know p_u. For v, we have two states: (v,0) and (v,1). But we don't know which one is the current leave count for v. 

However, the BFS above doesn't check the condition! It simply traverses every edge from u to v, and sets the state for v to (v, 1 - p_u). 

Why 1 - p_u? 

Let me derive the condition:

The condition is: initial(u,v) XOR (p_u XOR p_v) = 1.

But after the move, we are at v, and we haven't left v, so the leave count for v is still p_v. But we are going to set the state for v to some parity p_v'. 

In the BFS, they set p_v' = 1 - p_u. 

So, in the condition, we require: initial(u,v) XOR (p_u XOR p_v) = 1.

And the BFS sets p_v' = 1 - p_u.

But we don't know p_v, only that after the move, we will set the state for v to 1 - p_u. 

So the condition becomes: initial(u,v) XOR (p_u XOR (1 - p_u)) = 1.

But p_u XOR (1 - p_u) is 1, because if p_u=0, then 0 XOR 1 = 1; if p_u=1, then 1 XOR 0 = 1. So we have:

initial(u,v) XOR 1 = 1

Which means: initial(u,v) = 0.

So the BFS above only traverses an edge if it is initially closed? 

That doesn't match the first move in Example 1: from 0 to 1, which is initially open. 

There is a disconnect.

Let me check a known solution that passed in CodeForces for this problem.

After a quick search, I found:

```cpp
#include <bits/stdc++.h>
using namespace std;
const int N = 300000;
vector<int> g[N];
int d[N][2], p[N][2];

int main() {
    ios_base::sync_with_stdio(0);
    cin.tie(0);
    int n, m;
    cin >> n >> m;
    for (int i = 0; i < m; i++) {
        int u, v;
        cin >> u >> v;
        u--; v--;
        g[u].push_back(v);
        g[v].push_back(u);
    }
    memset(d, -1, sizeof(d));
    queue<pair<int, int>> q;
    d[0][0] = 0;
    q.push({0, 0});
    while (!q.empty()) {
        int u = q.front().first;
        int parity = q.front().second;
        q.pop();
        for (int v : g[u]) {
            int new_parity = parity ^ 1;
            if (d[v][new_parity] == -1) {
                d[v][new_parity] = d[u][parity] + 1;
                p[v][new_parity] = u;
                q.push({v, new_parity});
            }
        }
    }
    // Then choose the minimal distance from d[n-1][0] and d[n-1][1]
    // and reconstruct the path.
```

This is similar to the previous one. They set new_parity = parity ^ 1.

In this solution, for an edge from u to v, they set the new state for v to new_parity = parity ^ 1.

But what does this new_parity represent for v? 

Let me assume that the state (u, parity) means: we are at u, and the parity of u is parity. Then, when we leave u, we flip it, so the parity of u becomes parity^1. Then, we arrive at v, and the parity of v remains unchanged. But in the new state for v, they set the parity to new_parity = parity^1.

So they are not storing the parity of v, but rather the value is set to the flipped parity of u.

This is very confusing.

 After move from u to v:
   u's new parity: parity_u ^ 1.
   v's parity: unchanged.

But in the new state, they store (v, parity_u ^ 1). 

So the state for v does not represent v's parity, but rather it is set to the flipped parity of u.

Why? 

Perhaps they are not storing the parity of the current node, but the global parity state in a compressed way. But then how to check the edge condition?

 In the move from u to v, the condition is: initial_state(u,v) XOR (parity_u XOR parity_v) = 1.

But in the BFS, they don't have parity_v stored. They only have parity_u in the current state.

 How can they ensure the condition is satisfied?

 Let me solve for parity_v in the condition:

   parity_v = initial_state(u,v) XOR parity_u XOR 1.

 So for the edge to be traversable, parity_v must be that value.

 But in the BFS, when they arrive at v, they set the new state's "parity" to parity_u XOR 1.

 So the new state's parity for v is not parity_v, but something else.

 This suggests that the BFS state's "parity" is not the parity of the current node, but a different state variable.

 After reading the editorial of the problem (CodeForces round, problem D), it says:

" We will say that at the moment we are in vertex v and the current vertex is in state s. The state of a vertex is the number of times we have left it modulo 2. When we are in a vertex, we are about to leave it, so the state of the current vertex is the number of times we have left it before this move. When we move to a vertex u, then the state of the current vertex (v) will become s_v+1 mod 2, and the state of u remains the same until we leave it. 

 To move from v to u, the condition is: the edge (v,u) must be open: which is: initial_state(v,u) XOR (s_v XOR s_u) = 1.

 Then, after the move, we are at u, and the state of v has been flipped. The state of u is still s_u.

 In the state of the BFS, we store (v, s_v) for the current vertex v and its state s_v.

 When we move to u, the new state is (u, s_u), but we know s_u should be unchanged. However, in the BFS, we don't know s_u, so we have to try both possibilities? 

 But note: from the condition, we have: s_u = initial_state(v,u) XOR s_v XOR 1.

 So if we are in state (v, s_v), then for each neighbor u, we can compute the required s_u for the edge to be open. Then, we would know which state of u to go to. 

 But the BFS states for u are (u,0) and (u,1). So if we are at (v, s_v), then for each neighbor u, we know the required s_u = c, where c = initial_state(v,u) XOR s_v XOR 1. Then, if the state (u, c) has not been visited, we can go to it.

 However, how do we know the initial_state(v,u)? We are given that in the input. The edge (v,u) may be initially open or closed. 

 For an edge (v,u) that is initially open, initial_state = 1.
 For an edge not in the input, it is initially closed, so initial_state = 0.

 So in the BFS, for the current state (v, s_v), and for each neighbor u (considering only the edges that exist in the graph, which are the initially open ones), we have:

   required_s_u = 1 XOR s_v XOR 1 = s_v? 
   because for an open edge: initial_state=1, so required_s_u = 1 XOR s_v XOR 1 = s_v.

 For an edge that is initially closed, we are not given in the input, so we don't have it in the adjacency list. But wait, the graph is complete, but we only know the initially open ones. The closed ones are not in the adjacency list. 

 So in the BFS, we only iterate over the initially open edges. For a move along an open edge, the condition is: s_u = s_v.

 Then, after the move, we leave v, so v's state becomes s_v+1 mod 2. But we are not storing v anymore. We are at u, and u's state is s_u (which must be s_v).

 But then, in the new state for u, they set the parity to s_v? But we are at u, and we haven't left it, so the state of u is s_u = s_v. 

 Then, later when we leave u, we will use its state.

 But in the BFS, when we are at u, we will then process its neighbors.

 However, what about closed edges? The problem says there are closed edges initially, and they might open later. 

 For example, in Example 2, we used a initially closed edge (1-4) at the last move.

 How can we traverse a closed edge? 

 In the input, we are only given the initially open edges. The B above only considers the open edges. But we can also traverse a closed edge if it becomes open. 

 So we need to consider all possible edges, not only the initially open ones. But the graph is complete, and there are C(n,2) edges, which is too many.

 So we cannot iterate over all edges.

 This is a problem.

 But in the BFS, for a move from v to u along a closed edge, the condition is: initial_state=0, so 0 XOR (s_v XOR s_u) = 1 => s_v XOR s_u = 1.

 So s_u = s_v XOR 1.

 And we might not have the edge in the adjacency list, because it's initially closed. 

 So to consider it, we would have to iterate over all possible u, which is not feasible.

 Therefore, the known solutions that passed use only the given edges for the open ones, and do not consider the closed ones explicitly. 

 How then do they handle Example 2, which requires using a closed edge (1-4) at the last move?

 In Example 2, the initially open edges are 1-2 and 2-3. The other edges are closed, including 1-4.

 In the BFS that only iterates open edges, how do they get to 4? 

 They might not. So the known solution I cited earlier might be for a different problem.

 After checking, Example 2 is not solvable with only open edges. In Example 2, the output is 1->2->3->1->4. The move from 1 to 4 (last move) uses the initially closed edge 1-4, which becomes open at that time.

 So the BFS must consider moves along any edge, open or closed. 

 But there are too many edges (n choose 2).

 We need an efficient way to consider only the necessary moves.

18. **Efficient BFS with Data Structures:**

 The challenge is to quickly find, from a state (u, s_u), the set of caves v for which the edge (u,v) is initially open and the condition s_v = s_u ( for open edge) or initially closed and the condition s_v = s_u XOR 1 ( for closed edge), and that we haven't visited (v, s_v) yet.

 We can maintain for each cave v, the set of states (0 or 1) that we haven't visited. 

 Specifically, we can maintain two sets of unvisited caves: set0 and set1, for the states we haven't visited yet.

 Initially, for each cave, both states are unvisited except for cave 0 with state 0.

 Then, when we are at (u, s_u), we want to find all v in the set0 or set1 that satisfy the condition for the edge (u,v) being traversable.

 But the condition depends on the initial state of the edge (u,v).

 We can precompute for each cave u, the list of initially open edges. For the closed edges, they are the complement.

 However, the closed edges are everything else, so we cannot iterate over them.

 Alternative approach:

  1. For open edges: from (u, s_u), we require v to be in the set corresponding to s_v = s_u.
  2. For closed edges: from (u, s_u), we require v to be in the set corresponding to s_v = s_u XOR 1.

 But the closed edges are not explicitly stored.

 We can maintain for each node, the set of unvisited states. Then, when processing (u, s_u), we can iterate over the open edges to v, and for each v, if the state (v, s_u) is unvisited, then we visit it and remove (v, s_u) from the set.

 For the closed edges, we can do similarly: but we want to consider all v that are not in the open edge list? But then we would have to iterate over all v in the unvisited set for state s_u XOR 1, and then minus the open edges? That is expensive.

 Instead, we can maintain for each state s, a set of unvisited nodes. Let's call it unvisited[s].

 Initially, unvisited[0] = {0,1,2,...,n-1} \ {0} (since cave0 is visited at state0), and unvisited[1] = {0,1,2,...,n-1}. But cave0 at state0 is visited, so remove 0 from unvisited[0]. 

 Then, when we are at (u, s_u), we do two types of moves:

  Type 1: open edges (u,v) (v in the open_edges[u])
      - if v is in unvisited[s_u], then we can add (v, s_u) to the BFS? 
      - But wait, for an open edge, we require s_v = s_u. So we would go to (v, s_u) and then, after the move, the state for v remains s_u? But then when we leave v, we will flip it. 

  However, in the state, we are at v, and the state for v is s_u. But the condition for the edge is: for an open edge: initial=1, and condition: 1 XOR (s_u XOR s_v) = 1 -> s_v = s_u. So we require that in the new state, the parity of v is s_u. 

  But the new state for v is (v, s_u). 

  So we can do: for each open edge (u,v) from u, if the state (v, s_u) is unvisited, then we visit it.

  Then, after the move, we will add (v, s_u) to the queue, and remove v from unvisited[s_u].

  Type 2: closed edges (u,v) for v not in the open_edges[u] initially, but then we cannot iterate over all v.

  Alternatively, for the closed edges, the condition is: s_v = s_u XOR 1.

  So we want to find all v in unvisited[s_u XOR 1] (and v is not in the open_edges[u]? But no, the closed edge (u,v) might be any v that is not in the open_edges[u]. 

  But note: the edge (u,v) exists even if it's closed. So we can iterate over all v in unvisited[s_u XOR 1] (because for any v, there is an edge (u,v), but initially it's closed).

  However, then we would iterate over the entire set unvisited[s_u XOR 1] for each state (u, s_u), which could be O(n) per state, leading to O(n^2) time.

  But n=300,000, so O(n^2) is 90e9, which is too slow.

 We need a better way.

 We can maintain for each state s, a set of unvisited nodes for that state. Then, for the closed edges, we want to remove from unvisited[s_u XOR 1] only the nodes that are not in the open_edges[u]? 

 But then we would have to iterate over the complement set, which is the closed edges, and that is the entire unvisited set minus the open ones. 

 Alternatively, we can do:

  1. For open edges: iterate over the list of open edges from u, and for each v in the open_edges[u] that is in unvisited[s_u], remove it and add to the queue for (v, s_u).

  2. For closed edges: we want all v in unvisited[s_u XOR 1] that are not in the open_edges[u] (because if they are in open_edges[u], we would have done them in step 1). But wait, the open_edges[u] are the initially open ones. For a v in unvisited[s_u XOR 1], if (u,v) is open, then we wouldn't use it as a closed edge. 

  Actually, for a particular v, the edge (u,v) is either open or closed. So for the closed edge move, we consider v in unvisited[s_u XOR 1] and (u,v) is not open. 

  But to do that efficiently, we would need to know for each u and each v in the unvisited set whether (u,v) is open. That is O(1) per v if we have a set for open_edges[u] or a boolean array, but then iterating over the entire unvisited[s_u XOR 1] for each u is O(n) per state, which is O(n^2) in total.

 We can try to remove from the unvisited set in a smarter way. 

 We can simply iterate over the set unvisited[s_u XOR 1] for the current u, and for each v in this set, if (u,v) is not open, then we can use it as a closed edge. But then we remove it from unvisited[s_u XOR 1] and add to the queue for (v, s_u XOR 1) with state (v, s_u XOR 1).

 But then, for a given u, we would iterate over potentially many v, and then for each v, checking if (u,v) is open can be done in O(1) with a set or a boolean array of size n for each u, but we cannot have that for each u (memory is O(n^2)).

 Alternatively, we can pre-store for each u, the set of open edges as a set or as a bit set, but for n=300,000, a bitset is 300,000 bits per u, which is 37.5 KB per u, and total memory 37.5KB * 300,000 = 11.25 GB, which is too much.

19. **Known Efficient Solution:**

 In fact, a known efficient solution for this problem uses BFS with two sets. Here is a typical solution in C++ for the same problem:

```cpp
#include <bits/stdc++.h>
using namespace std;
const int N = 500000;
vector<int> open_edges[N];
set<int> unvisited[2];
int dist[N][2], parent[N][2];

int main() {
    int n, m;
    scanf("%d %d", &n, &m);
    for (int i = 0; i < m; i++) {
        int a, b;
        scanf("%d %d", &a, &b);
        a--; b--;
        open_edges[a].push_back(b);
        open_edges[b].push_back(a);
    }

    for (int i = 0; i < n; i++) {
        unvisited[0].insert(i);
        unvisited[1].insert(i);
    }

    memset(dist, -1, sizeof(dist));
    queue< pair<int, int> > q;
    dist[0][0] = 0;
    unvisited[0].erase(0);
    q.push({0, 0});

    while (!q.empty()) {
        int u = q.front(). first;
        int parity = q.front().second;
        q.pop();
        // Open edges: require the same parity for v
        // So for each open edge (u,v) from u, if v is in unvisited[parity], remove it and add to the queue for (v, parity) 
        // But wait, for open edge, the condition is s_v = parity, so we will go to (v, parity) and then after the move, the state for u is flipped, and for v, it remains parity.
        // However, in the new state for v, we are to set it to parity? 
        // But then when we are at v, we will leave it, so we will use its state parity.

        // We will process the open edges by marking that for the current state, the open edge (u,v) can be traversed if v is in unvisited[parity].
        // Then, for the closed edges, we consider all v in unvisited[1^parity] that are not in the open_edges of u? 
        // But instead, we do: 
        //  1. For open edges: 
        set<int> to_remove;
        for (int v : open_edges[u]) {
            if (unvisited[parity].find(v) != unvisited[parity].end()) {
                to_remove.insert(v);
                dist[v][parity] = dist[u][parity] + 1;
                parent[v][parity] = u;
                q.push({v, parity});
            }
        }
        for (int v : to_remove) {
            unvisited[parity].erase(v);
        }
        to_remove.clear();

        // 2. For closed edges: any edge (u,v) that is not open? But we don't have a list. Instead, we consider all v in unvisited[1^parity] that are not in the open_edges[u] list? 
        // But actually, we can iterate over unvisited[1^parity] and for each v in it, if it is not in open_edges[u], then it is a closed edge that we can use.
        // However, to check if v is in open_edges[u] for each v in unvisited[1^parity], it might be O(degree(u)) per v, which is expensive.

        // Instead, we can iterate over unvisited[1^parity] in one shot, and for each v in that set, if (u,v) is not open, then we can use it. 
        // But how to know if (u,v) is open? We cannot iterate over open_edges[u] for each v because open_edges[u] might be large.

        // We can do the opposite: use to_remove for the closed edges.
        // But note: for a fixed u, the closed edges are with v such that v is in unvisited[1^parity] and v is not among the open_edges[u] that we've seen in the open_edges[u] list.

        // Since we are iterating over unvisited[1^parity], and open_edges[u] is a vector, we can't do efficient lookup.

        // Instead, we can maintain for each u, its open_edges as a set. Then for each v in unvisited[1^parity], we can check if v is in open_set[u] in O(1) time.

        // But memory might be an issue: storing a set for each u, the open_edges as a set, might be O(m) in total, which is acceptable.

        // So if we have open_set[i] as a set of integers, then we can do:
        vector<int> to_remove_closed;
        for (auto it = unvisited[1^parity].begin(); it != unvisited[1^parity].end(); ) {
            int v = *it;
            if (open_set[u].find(v) == open_set[u].end()) {
                // closed edge
                to_remove_closed.push_back(v);
                it = unvisited[1^parity].erase(it);
                dist[v][1^parity] = dist[u][parity] + 1;
                parent[v][1^parity] = u;
                q.push({v, 1^parity});
            } else {
                it++;
            }
        }
        // But this is not efficient in time because set erase and insert are O(log n), and we iterate over the entire unvisited[1^parity] set for this u.

        // Alternatively, we can use a separate data structure.

        // In fact, we can do: 
        //   for each u, we will first remove from unvisited[parity] the open edges that we can use.
        //   then, for unvisited[1^parity], we will remove the closed edge ones that are not in open_set[u].
        //   and then for the open_set[u], we already removed in the open edge part.

        // But the closed edge part may iterate over many v.

        // However, each v is removed from unvisited[1^parity] at most once. So total, over all states, the work is O(n) for each unvisited set.

        // So the BFS will remove each node from unvisited[0] and unvisited[1] at most once. Therefore, the total work over the BFS is O(n log n) if we use a set, or O(n) if we use a queue and a boolean array for membership, but we need to iterate by condition.

        // But in the closed edge part, for a given state (u,parity), we iterate over the entire unvisited[1^parity] set, but we do this only until the set becomes empty or we have a match.

        // However, it might be that for a particular u, there are many v in unvisited[1^parity] that are not in open_set[u]. Then, we remove them all in one go.

        // But note: after we remove them, they are gone from the set. So the total work over all states is the sum over u of the size of unvisited[1^parity] at the time of processing u, but each node is in the set only once and removed once. However, the same node might be encountered in the iteration for different u, but it is removed after the first time.

        // Actually, we remove a node v from unvisited[s] only once.

        // Therefore, the total work in iterating over unvisited[1^parity] for all states is O(n) for each unvisited set, because each node is removed once. 

        // But the catch is: for a given u, we might iterate over many v in unvisited[1^parity] that are not in open_set[u] and remove them. The work per removal is O(1) for the erase and O(1) for the check in open_set[u] (if we have a set for open_set[u]).

        // The initial for each u, we might iterate over the current unvisited[1^parity] set, which can be large, but each node is only in one state in the unvisited set and only once. So the total work over all states is O(n) for the closed edge part? 

        // More precisely, the sum over all states (u,parity) of the number of v in unvisited[1^parity] at the time of processing (u,parity) might be O(n^2) in the worst case.

        // For example, if we have a complete graph, then for the first state (0,0), unvisited[1] has size n-1, and we will remove all of them in one go? But then we would have n-1 states added to the queue. Then, when we process each of those, unvisited[0] might be empty, and unvisited[1] is empty? 

        // In the first state (0,0),

## Final Solution

