# Solution for abc318_e.md

Generated at: 2025-07-22 06:29:39
Model: deepseek-reasoner

## Reasoning Process

We are counting triples (i, j, k) such that:
  1 <= i < j < k <= N
  A_i = A_k
  A_i != A_j

Note: The condition A_i = A_k and A_i != A_j implies that the value at i and k is the same (say x), and the value at j is different from x.

We can think of grouping the indices by the value. For a fixed value x, let the indices where x appears be: 
  indices[x] = [i1, i2, i3, ..., im]  (in increasing order)

In any triple (i, j, k) with value x at i and k, we have i and k being two distinct indices from indices[x] (with i < k), and j must be an index between i and k such that A_j != x.

But note: j must be in the range (i, k) and also must not have the value x? Actually, the condition is A_j != x. However, note that if there are multiple x's in the array, then between i and k there might be other x's? But then j cannot be at an index with x. So we have to avoid those.

Alternatively, we can count all triples (i, k) from the same group and then subtract the ones that have j with the same value? Actually, the condition for j is that it must be different. So for a fixed pair (i, k) (with i and k in the same group and i < k), the number of valid j is the number of indices j in the interval (i+1, k-1) that do not have the value x.

But note: the entire sequence has indices from 1 to N. The total numbers of indices between i and k (exclusive) is (k - i - 1). However, in between i and k, there might be other indices that also have the value x. So the invalid j (for this x) would be the indices between i and k that are in the same group (i.e., the same x). Therefore, for a fixed pair (i, k) (with i and k in indices[x]), the number of valid j is:
  (k - i - 1) - (number of indices from the group x that lie strictly between i and k)

But note: we are iterating over pairs (i, k) in the same group. How to do this efficiently? The total number of pairs (i, k) for group x is C(m,2) and m might be large. And if we iterate over all pairs, the overall complexity might be O(n^2) in the worst case (if one group has n elements). But n can be up to 300,000, so O(n^2) is too slow.

Alternative approach:

We can consider the entire sequence and traverse from left to right. However, note that the condition requires two endpoints of the same value and a middle index of a different value.

We can also think: total number of triples (i, j, k) with A_i = A_k and i<j<k is the same as: for each distinct value x, consider two indices i and k (with i<k) from the group of x. Then for each such pair, the j must be any index between i and k that is not in the group of x. 

But we can break it down for each group x: 
  Let f(x) = the list of indices for x: [i1, i2, ..., i_m]
  Then the total valid j for the entire group x is:
      Sum_{p=0}^{m-1} Sum_{q=p+1}^{m-1} [ (i_q - i_p - 1) - (number of indices of x between i_p and i_q) ]

Note: the number of indices of x between i_p and i_q is (q - p - 1). Why? Because the indices are sorted, and the indices between i_p and i_q in the list are from i_{p+1} to i_{q-1}, which is (q-p-1) numbers.

Therefore, for a pair (i_p, i_q) in group x, the number of valid j is:
    (i_q - i_p - 1) - (q - p - 1) = i_q - i_p - 1 - (q-p-1) = i_q - i_p - (q-p)

So the contribution from group x is:
    Sum_{p<q} [ i_q - i_p - (q-p) ]

We can split this:
    = [Sum_{p<q} (i_q - i_p)] - [Sum_{p<q} (q-p)]

Now, let's compute each part:

1. Sum_{p<q} (i_q - i_p) 
    = Sum_{q} i_q * (number of p with p<q) - Sum_{p} i_p * (number of q with p<q)
    = Sum_{q} i_q * q - Sum_{p} i_p * (m - p - 1)   [if we index from 0, then for a fixed q, there are q choices for p (from 0 to q-1); for a fixed p, there are (m-p-1) choices for q (from p+1 to m-1)]

But note: we can also rearrange:
    = (0-indexed) 
        = Sum_{q=0}^{m-1} i_q * q  - ??? Actually, let's do:

    = Sum_{p<q} i_q + Sum_{p<q} (-i_p)
    = [for each q, i_q appears in (q) pairs (as the second element)] and for each p, i_p appears in (m-1-p) pairs (as the first element) for the negative part? Actually:

    The term i_q appears for every p from 0 to q-1 -> q times.
    The term i_p appears for every q from p+1 to m-1 -> (m-1-p) times (with a negative sign).

    Therefore: 
        = [Sum_{q=0}^{m-1} i_q * q] - [Sum_{p=0}^{m-1} i_p * (m-1-p)]

    But note: we can use the same index variable? Let's use index q for both, so:
        = Sum_{q} [ i_q * ( q - (m-1-q) ) ]
        = Sum_{q} i_q * ( 2*q - (m-1) )

2. Sum_{p<q} (q-p) 
    = Sum_{q} q * (number of p with p<q) - Sum_{p} p * (number of q with p<q)
    = Sum_{q} q * q - Sum_{p} p * (m-1-p)   ? 

    Alternatively, we can note that for each p, the q runs from p+1 to m-1, so:
        = Sum_{p=0}^{m-2} Sum_{q=p+1}^{m-1} (q-p)
        = Sum_{p=0}^{m-2} [ (q-p) for q from p+1 to m-1) ]
        = Sum_{p} [ (1+2+...+(m-1-p)) ] 
        = Sum_{p} [ T_{m-1-p} ] where T_i = i*(i+1)/2.

    But we can compute it as:
        = Sum_{d=1}^{m-1} d * (m - d)   [d = q-p, and the number of pairs with difference d is (m-d)]

    Actually, the number of pairs (p,q) with q-p = d is (m-d). So:
        = Sum_{d=1}^{m-1} d*(m-d)

    Alternatively, we can use the formula for the sum of natural numbers:

        = (m-1)*m*(m+1)/6 *? Actually, let's compute:

        We know that: 
          Sum_{p<q} (q-p) = Sum_{q=0}^{m-1} q * (q) - ... no, let me use:

        = (0+1+...+(m-1)) * (m-1) - ... Actually, we can use:

        Let S = 0 for the entire group. 
        We can also note: 
          = Sum_{q} q*(q) - ... 

        Actually, a simpler way: 
          = Sum_{p=0}^{m-1} [ (p+1) + (p+2) + ... + (m-1) - p*(m-1-p) ]
          But that is the same as above.

        Alternatively, we can compute by:
          = Sum_{q=1}^{m-1} q * (m - q)   [if we let q be the gap? Actually, the gap d from 1 to m-1]

        So: 
          = m * Sum_{d=1}^{m-1} d - Sum_{d=1}^{m-1} d^2
          = m * [ (m-1)*m/2 ] - [ (m-1)*m*(2*m-1)/6 ]

        But note: the total number of pairs is m*(m-1)//2, and we can also compute the sum of gaps without this formula? 

        Actually, we don't need a closed form: we can precompute for each group? But note that m can be large and there might be many groups, but the total n is 300000, so the sum of m over groups is n. However, we are iterating per group. And for each group we can compute the second part in O(1) if we have a closed form.

        Alternatively, we can compute the second part without a closed form by iterating? But that would be O(m) per group and overall O(n). Since the total n is 300000, and the sum of m over groups is n, then O(n) is acceptable.

        However, we can use closed forms for the second part:

          Let T1 = Sum_{p<q} (q-p) 
          = Sum_{d=1}^{m-1} d * (m - d)   [because for a fixed d, the number of pairs with gap d is (m-d) in a list of m elements? Actually, no: the gap d in indices? Actually, the gap in the index of the list: if we have m elements, then the gap in the list index (the positions in the sorted list of the group) is from 0 to m-1. The gap d between the positions in the group (not the actual indices in the array) is the difference in the group index (q-p). 

          But note: here in the second part we are summing (q-p) for the group indices (the positions in the group, not the array indices). So for a fixed group x, the group indices are 0,1,...,m-1. Then the gap d = q-p (which is the difference in the group index) and the number of pairs with gap d is (m-d). So:

          T1 = Sum_{d=1}^{m-1} d*(m-d) 
          = m * (sum_{d=1}^{m-1} d) - sum_{d=1}^{m-1} d^2
          = m * ( (m-1)*m//2 ) - ( (m-1)*m*(2*m-1)//6 )

        However, note: we are using integers and we have to avoid floating point. We can use integer arithmetic.

        But we can also compute T1 in O(1) with these formulas.

        Alternatively, we can avoid the closed form and compute the second part by iterating? Actually, we are already iterating over the group to compute the first part? 

        So for group x, we can compute:

          Part1 = 0
          For q in range(m):
              Part1 += i_q * (2*q - (m-1))

          Part2 = 0
          # We can compute by iterating over gaps? Actually, we have a closed form for part2.

          Part2 = (m-1)*m*(m+1)//6   ??? Let me check:

          Actually, we have:
            T1 = m * ( (m-1)*m//2 ) - ( (m-1)*m*(2*m-1)//6 )
                = (m^2*(m-1))//2 - (m*(m-1)*(2*m-1))//6
                = [3*m^2*(m-1) - m*(m-1)*(2*m-1)] // 6
                = m*(m-1) * [3m - (2m-1)] // 6
                = m*(m-1)*(m+1)//6

          So T1 = m*(m-1)*(m+1)//6? 

          But wait: let's test for m=2: 
            pairs: (0,1): gap=1 -> total=1.
            formula: 2*1*3//6 = 6//6 = 1 -> correct.

          For m=3:
            pairs: (0,1):1, (0,2):2, (1,2):1 -> total=4.
            formula: 3*2*4//6 = 24//6 = 4 -> correct.

          Therefore, T1 = m*(m-1)*(m+1)//6 = (m*(m^2-1))//6.

        So then the contribution from group x is:
          Part1 - Part2 = [Sum_{q} i_q*(2*q - (m-1))] - [m*(m^2-1)//6]

        Then the total answer is the sum over all groups x.

However, note: the above derivation for Part1: 
          Part1 = Sum_{q} i_q*(2*q - (m-1))

        Let me test with a small group: 
          Group: [i0, i1] (m=2)
          Then: 
            for q=0: i0*(2*0 - 1) = i0*(-1)
            for q=1: i1*(2*1 - 1) = i1*(1)
            Part1 = -i0 + i1 = i1 - i0.

          Then the pairs: only (0,1): i1-i0, which matches the first part.

          Then Part2 = 2*(2^2-1)//6 = 2*3//6 = 1? But the second part for the pair (0,1) is (1-0)=1? So the entire contribution = (i1-i0) - 1.

          But according to our formula for the pair: 
            valid j = (i1 - i0 - (1-0)) = i1 - i0 - 1.

          Which matches.

        Another test: group of three: [i0, i1, i2]
          Part1 = 
            q0: i0*(0 - 2) = -2*i0
            q1: i1*(2 - 2) = 0
            q2: i2*(4 - 2) = 2*i2
            = -2*i0 + 2*i2 = 2*(i2 - i0)

          The first part for the three pairs:
            (0,1): i1 - i0
            (0,2): i2 - i0
            (1,2): i2 - i1
            Total = (i1 - i0) + (i2 - i0) + (i2 - i1) = 2*i2 - 2*i0 = 2*(i2-i0) -> matches.

          Part2 = 3*(9-1)//6 = 3*8//6 = 24//6 = 4.
          The second part: 
            (0,1): 1
            (0,2): 2
            (1,2): 1
            total = 4 -> matches.

          Then the contribution for the group: 2*(i2-i0) - 4.

          Now, the actual valid j counts for the three pairs:
            (0,1): (i1 - i0 - 1)
            (0,2): (i2 - i0 - 2)   [because there is one element in the group between i0 and i2? Actually, the group has i0, i1, i2. The number of group indices between i0 and i2 is 1 (i1) so the gap in group index is 2 (from 0 to 2) -> then the term is (i2-i0) - (2) = i2-i0-2.
            (1,2): (i2 - i1 - 1)
          Total = (i1-i0-1) + (i2-i0-2) + (i2-i1-1) = (i1-i0-1) + (i2-i0-2) + (i2-i1-1) = 2*i2 - 2*i0 - 4 = 2*(i2-i0) - 4 -> matches.

Therefore, the solution:

  Precompute a dictionary mapping each value to the list of indices (positions) in the array.

  For each group x with list L (of indices) and m = len(L):
      if m < 2: skip (because we need at least two indices to form a pair)
      Then:
          part1 = 0
          for q in range(len(L)):
              part1 += L[q] * (2*q - (m-1))

          part2 = m*(m-1)*(m+1)//6   # Note: m*(m^2-1)//6

          Then the contribution of group x is part1 - part2

  Then answer = sum of contributions for all groups.

But note: the array indices in the problem are 1-indexed? The problem says: "positive integers (i,j,k)" and the input is given as A_1, A_2, ... A_N.

However, in our code, we can use 0-indexed indices. The above derivation is independent of the absolute indices because we are using the actual array indices (which are 0-indexed in our list). But note: the formula uses the actual array indices (like the position in the array). For example, the array [1,2,1,3,2] has indices 0,1,2,3,4.

But our formula for the valid j count: 
      For a pair (i_p, i_q) in the group: the number of j in between that are not x is: 
          (i_q - i_p - 1) - (number of x's between i_p and i_q) 
      = (i_q - i_p) - (q-p) - 1? 

Wait, actually we had:
      = i_q - i_p - (q-p)   [because we subtracted the count of x's (which is q-p-1) from the total in between (i_q - i_p - 1) and then we got i_q-i_p-1 - (q-p-1) = i_q - i_p - (q-p) ]

But note: the term (i_q - i_p) is the distance in the array, and (q-p) is the gap in the group index. And we don't subtract 1? 

But wait: the total indices between i_p and i_q (exclusive) is (i_q - i_p - 1). The number of x's between them is (q-p-1). Then the valid j count is (i_q - i_p - 1) - (q-p-1) = i_q - i_p - (q-p). 

So the formula is correct.

Therefore, we can use the above method.

Complexity: We iterate over each group, and for each group we iterate over the list of indices. The total length of all groups is N, so the overall complexity is O(N). 

Let me test with the example:

Example 1: [1,2,1,3,2] -> indices: 
  group1: [0,2] -> m=2
      part1: 
        q=0: L[0]*(2*0 - 1) = 0 * (-1) = 0
        q=1: L[1]*(2*1-1) = 2*1 = 2
        part1 = 2
      part2 = 2*(2^2-1)//6 = 2*(4-1)//6 = 6//6 = 1
      contribution = 2-1 = 1.

  group2: [1,4] -> 
        q0: 1*(0-1) = 1*(-1) = -1
        q1: 4*(2-1)=4*1=4
        part1 = 3
        part2 = 2*(4-1)//6 = 2*3//6=1
        contribution=2.

  group3: [3] -> skip.

  Total = 1+2 = 3 -> matches.

But wait, the example output is 3, and we have 1 from group1 and 2 from group2 -> total 3.

But what about the triple (2,4,5)? 
  This triple: i=2, j=4, k=5 -> A_2=2, A_4=3, A_5=2 -> valid.
  This triple is counted in group2: the pair (1,4) -> the indices 1 (which is the second element, index1 in the array) and 4 (the fifth element, index4 in the array). The j can be at index3 (value 3) and index4 is the k? Actually, note: j must be between i and k. The indices: i=1, j=3 (which is the fourth element? Actually, the array is 0-indexed: 
      A0=1, A1=2, A2=1, A3=3, A4=2.

  The triple (1,3,4) in 0-indexed? 
      (i,j,k) = (1,3,4): 
        i=1 -> A1=2
        j=3 -> A3=3
        k=4 -> A4=2 -> valid.

  How is it counted? 
      In group2: we have indices [1,4]. The pair (1,4): 
          j must be in the indices between 1 and 4: indices 2,3. 
          At index2: value=1 -> valid? yes, because not 2 -> so two valid j? 
          But our formula: 
            count = 4-1 - (1-0) = 3? 
          Actually, the group indices: for the pair (1,4): 
            p=0 (value 1) and q=1 (value4) -> gap in group index = 1-0=1.
            count = 4-1 - (1) = 2.

          Then why the contribution for group2 is 2? Actually, we are summing over all pairs in group2: 
            The two pairs in group2: 
               (0,1): [1,4] -> count=2
            So the entire group2 gives 2.

          Then the two triples from group2: 
            (1,2,4): j=2 -> A2=1 -> valid? but wait: the indices: i=1, j=2, k=4: yes, because 1<2<4? Actually, in 1-indexed: (2,3,5) and (2,4,5). 
            The two j indices: 2 and 3? 
            j=2: gives (1,2,4) -> in 0-indexed: (1,2,4) -> but the array: A1=2, A2=1, A4=2 -> valid.
            j=3: gives (1,3,4) -> A1=2, A3=3, A4=2 -> valid.

          So two triples from group2.

          Then group1: 
            indices: [0,2]: 
                pair (0,2): 
                  j must be between 0 and 2: only index1 -> A1=2 -> valid? 
                  so one triple: (0,1,2) -> in 0-indexed: i=0, j=1, k=2: A0=1, A1=2, A2=1 -> valid.

          Total 1+2=3.

Therefore, the solution is:

  Steps:
    Read N
    Read the list A of N integers.

    groups = defaultdict(list)
    for index, value in enumerate(A):
        groups[value].append(index)

    total = 0
    for key, lst in groups.items():
        m = len(lst)
        if m < 2:
            continue
        # Compute part1: sum_{q} lst[q] * (2*q - (m-1))
        part1 = 0
        for q in range(m):
            part1 += lst[q] * (2*q - (m-1))
        # Compute part2: m*(m*m-1)//6
        part2 = m*(m*m-1)//6   # because m*(m-1)*(m+1)//6 = m*(m^2-1)//6
        total += (part1 - part2)

    print(total)

But note: the formula for part2: we have derived it as m*(m-1)*(m+1)//6 = m*(m^2-1)//6.

However, we must be cautious: integer division? Since the numbers are integers, and the formula is divisible by 6? We have shown for m=2,3 that it works.

But for m=1: we skip.

Let me test with the third example: 
  Input: 
      13
      9 7 11 7 3 8 1 13 11 11 11 6 13
  Output: 20

  We'll have to compute by hand? Instead, we can run the code on the example.

But we can write the code accordingly.

However, note: the constraints say that A_i are between 1 and N, and N up to 300000. The above algorithm is O(N), so it should be efficient.

Let me run the example 3:

  We'll build the groups:

    9: [0]
    7: [1,3]
    11: [2,8,9,10]   -> m=4
    3: [4]
    8: [5]
    1: [6]
    13: [7,12]
    6: [11]

  Now, we only consider groups with at least two indices: 7, 11, 13.

  Group 7: [1,3] -> m=2
      part1: 
          q0: 1 * (0 - 1) = 1*(-1) = -1
          q1: 3 * (2-1) = 3*1=3
          part1 = 2
      part2 = 2*(4-1)//6 = 6//6=1
      contribution = 1.

  Group 13: [7,12] -> m=2
      part1:
          q0: 7*(0-1) = -7
          q1: 12*(1) = 12
          part1 = 5
      part2 = 1
      contribution = 4.

  Group 11: [2,8,9,10] -> m=4
      part1: 
          q0: 2 * (0 - 3) = 2*(-3) = -6
          q1: 8 * (2-3) = 8*(-1) = -8
          q2: 9 * (4-3) = 9*1 = 9
          q3: 10 * (6-3) = 10*3=30
          part1 = -6-8+9+30 = 25
      part2 = 4*(16-1)//6 = 4*15//6 = 60//6=10
      contribution = 25-10=15

  Total = 1 + 4 + 15 = 20.

  Matches.

Therefore, code:

  Note: We are using 0-indexed indices.

Implementation:

  We'll read the input.

  Steps:
      import sys
      from collections import defaultdict

      data = sys.stdin.read().split()
      n = int(data[0])
      A = list(map(int, data[1:1+n]))

      groups = defaultdict(list)
      for i, num in enumerate(A):
          groups[num].append(i)

      total = 0
      for key, lst in groups.items():
          m = len(lst)
          if m < 2:
              continue
          # Compute part1
          part1 = 0
          # We have the list of indices for this group, and it is sorted (by the index, which it is because we traverse in increasing index)
          for idx, pos in enumerate(lst):
              part1 += pos * (2*idx - (m-1))
          part2 = m * (m*m - 1) // 6
          total += (part1 - part2)

      print(total)

But note: m*m can be up to 300000^2 = 90e9, which is within the range of Python integers? But we are using integers, and 90e9 is about 9e10, which is 90 billion, which is acceptable in Python? Yes, because Python integers are arbitrary precision, but we have to be cautious for performance? Actually, the total number of groups is at most n, and n is 300000, but each group we do O(m) and the total m over groups is n, so it's O(n). The multiplication m*(m*m) might be heavy? Actually, m is the size of the group, and the worst-case group has size 300000. Then m*m = 90000000000, which is 9e10, and then multiplied by m (300000) would be 2.7e16? But wait: we don't have a group of size 300000? The entire array could be the same number? 

  Constraints: A_i between 1 and N, and N up to 300000. So it is possible that the array has one repeated number. Then m = 300000.

  Then part2 = 300000 * (300000^2 - 1) // 6 = 300000 * (90000000000 - 1) // 6 = 300000 * 90000000000 // 6 - ... 

  That is: 300000 * 90000000000 = 2.7e16, which is a big number, but Python integers can handle it? It is 27,000,000,000,000,000? That is 27 quadrillion. In Python, integers are arbitrary precision, but the operation might be slow? However, we do it once per group. The worst-case group: only one group, so we do it once. And 2.7e16 is a 55-bit number? Actually, 2^55 is about 3.6e16, so it's about 55 bits. Python can handle that quickly.

  Alternatively, we can compute without such big numbers? But note: the problem constraints say that N up to 300000, so worst-case group has 300000, and we have to compute part2 for that group. We can compute:

        part2 = m*(m-1)*(m+1)//6

  That is the same as (m*(m^2-1))//6. But we can compute in steps? 

        part2 = (m*(m-1)*(m+1)) // 6

  This might avoid the huge m^2? Actually, the product m*(m-1)*(m+1) is about m^3. For m=300000, m^3 = 2.7e16, same as above. So we can compute it as:

        part2 = m * (m-1) * (m+1) // 6

  But note: the product is divisible by 6? Because among m-1, m, m+1, we have three consecutive integers? Actually, no: we have (m-1), m, (m+1) -> consecutive. So one is divisible by 2 and one by 3? Yes, so divisible by 6.

  So we can write:

        part2 = (m * (m-1) * (m+1)) // 6

  This might be more efficient? Actually, it avoids the m^2 term and multiplies three numbers. The numbers are m, m-1, m+1, which are about the same as m. But the product is still m^3. But we are doing the same big multiplication.

  Alternatively, we can do:

        part2 = (m * (m*m - 1)) // 6   # which is the same.

  Both are O(1) per group.

  However, we have to do one big multiplication per group. The worst-case group has 300000, and the total groups are at most 300000? Actually, worst-case: each group has at least 2 elements? The total groups cannot exceed N/2? Actually, the total groups is the number of distinct numbers? But worst-case, the array has one distinct number -> one group. So we do one big multiplication. 

  But 300000 is 3e5, and 3e5^3 is 2.7e16, which is acceptable? In C++ it might be an issue with 64-bit integers (because 2.7e16 is less than 2^64, which is 1.8e19). In Python, it is acceptable, but we have to consider performance: one big multiplication for a 300000-sized group. 

  However, the multiplication of integers of size 300000 is not the same as multiplying two huge numbers? Actually, 300000 is a small integer (fits in a machine word? no, because 300000 is 19 bits, so 300000^3 is 19*3=57 bits, which fits in 64 bits? Actually, 2^64 is 1.8e19, and 300000^3=2.7e16, which is 2.7 * 10^16, which is less than 2^64? 2^64 is about 1.8e19, so yes. Therefore, in Python, these integers are handled in constant time? Because they fit in 64 bits? Actually, Python integers are arbitrary precision, but for numbers that fit in a machine word, the multiplication is fast. And 2.7e16 is less than 2^64, so it is one machine word.

  Therefore, we can do either.

  We'll write:

        part2 = (m * (m-1) * (m+1)) // 6

  This might be slightly more efficient because we avoid one big square? But actually, m*m is about the same as m*(m-1) is about the same as m*(m+1). 

  But note: if m is large, m*m is the same as m*(m-1) in terms of magnitude.

  So the final code:

  import sys
  from collections import defaultdict

  def main():
      data = sys.stdin.read().split()
      n = int(data[0])
      A = list(map(int, data[1:1+n]))
      groups = defaultdict(list)
      for i, num in enumerate(A):
          groups[num].append(i)
      total = 0
      for lst in groups.values():
          m = len(lst)
          if m < 2:
              continue
          part1 = 0
          for idx, pos in enumerate(lst):
              part1 += pos * (2*idx - (m-1))
          part2 = (m * (m-1) * (m+1)) // 6
          total += (part1 - part2)
      print(total)

  if __name__ == "__main__":
      main()

But let me test with the provided examples.

  Example 2: [1,2,3,4,5,6,7] -> no group has more than one element -> total=0 -> correct.

  Example1: we did by hand.

  Example3: we did by hand.

  We are confident.

Edge: m=1 -> skip.

  What about m=0? The groups only contain numbers that appear, so m>=1.

  What about negative numbers? The constraints: positive integers, so no.

  But note: the indices are nonnegative.

  We'll run the example1 in the code.

  Example1: A = [1,2,1,3,2] -> n=5

  groups:
      1: [0,2]
      2: [1,4]
      3: [3]

  For group1: 
      m=2
      part1: 
          idx0: 0*(0 - 1) = 0
          idx1: 2*(2*1 - 1) = 2*(2-1)=2*1=2
          part1=2
      part2 = (2*1*3)//6 = 6//6=1
      contribution=1

  For group2: 
      m=2
      part1: 
          idx0: 1*(0-1)=1*(-1)=-1
          idx1: 4*(2*1-1)=4*1=4
          part1=3
      part2=1
      contribution=2

  Total=3 -> correct.

Therefore, the code is:

Note: We are using (2*idx - (m-1)) for the coefficient.

Let me write the code accordingly.

We'll submit the code as the solution.

## Final Solution

### Step 1: Problem Understanding and Modeling
The problem requires counting the number of triples (i, j, k) in a sequence of positive integers such that:
1. \(1 \leq i < j < k \leq N\)
2. \(A_i = A_k\)
3. \(A_i \neq A_j\)

**Key Insights:**
- The triple consists of two equal elements at positions \(i\) and \(k\) with a different element at position \(j\) between them.
- For each value \(x\) in the sequence, we can group the indices where \(x\) occurs. For any pair of indices \((i, k)\) in this group (with \(i < k\)), the number of valid \(j\) positions is the count of indices between \(i\) and \(k\) that do not have the value \(x\).

**Mathematical Formulation:**
- For a fixed value \(x\) with \(m\) occurrences at indices \(L = [i_1, i_2, \ldots, i_m]\) (sorted), the contribution to the answer is the sum over all pairs \((i_p, i_q)\) (where \(p < q\)) of \((i_q - i_p - (q - p))\).
- This can be split into two parts:
  - **Part 1:** \(\sum_{q=0}^{m-1} L[q] \cdot (2q - (m-1))\)
  - **Part 2:** \(\frac{m(m-1)(m+1)}{6}\)
- The total contribution for \(x\) is Part 1 minus Part 2.

**Constraints Handling:**
- \(N\) can be up to \(3 \times 10^5\), so the solution must be efficient (\(O(N)\) or \(O(N \log N)\)).
- Values \(A_i\) are between 1 and \(N\), so grouping by value is feasible.

### Step 2: Multiple Solution Exploration
1. **Brute Force Solution:**
   - Iterate over all possible triples \((i, j, k)\) and check the conditions.
   - Time Complexity: \(O(N^3)\), which is infeasible for \(N \leq 3 \times 10^5\).

2. **Optimized Solution:**
   - Group indices by value. For each group, compute the contribution using the derived formula.
   - Time Complexity: \(O(N)\) (since each index is processed once per group).
   - Space Complexity: \(O(N)\) for storing groups.

3. **Space-Optimized Solution:**
   - Similar to the optimized solution but processes groups on-the-fly without storing all indices if possible. However, grouping is necessary, so space remains \(O(N)\).

4. **Special Technique Solution:**
   - Use prefix sums or Fenwick trees for counting valid \(j\)'s between \(i\) and \(k\). However, the mathematical derivation provides a direct linear approach per group.

### Step 3: Optimal Solution Selection
**Selection Rationale:**
- The grouped mathematical approach reduces the problem to linear processing per group, with total time \(O(N)\).
- It efficiently handles the constraints by leveraging arithmetic properties to avoid nested loops.
- The solution is optimal as each index is processed exactly once, and the formula for Part 2 is computed in constant time per group.

**Implementation:**
```python
import sys
from collections import defaultdict

def main():
    data = sys.stdin.read().split()
    n = int(data[0])
    A = list(map(int, data[1:1+n]))
    groups = defaultdict(list)
    for i, num in enumerate(A):
        groups[num].append(i)
    
    total = 0
    for lst in groups.values():
        m = len(lst)
        if m < 2:
            continue
        part1 = 0
        for idx, pos in enumerate(lst):
            part1 += pos * (2 * idx - (m - 1))
        part2 = m * (m - 1) * (m + 1) // 6
        total += (part1 - part2)
    
    print(total)

if __name__ == "__main__":
    main()
```

**Key Optimization Points:**
- **Group Processing:** Indices are grouped by value for efficient access.
- **Linear Per Group:** Each group is processed in linear time relative to its size.
- **Arithmetic Formula:** The closed-form formula for Part 2 avoids explicit pair enumeration.

### Step 4: Verification and Summary
**Verification:**
- **Example 1:** Input `[1, 2, 1, 3, 2]` yields output `3`.
- **Example 2:** Input `[1, 2, 3, 4, 5, 6, 7]` yields output `0`.
- **Example 3:** Input `[9, 7, 11, 7, 3, 8, 1, 13, 11, 11, 11, 6, 13]` yields output `20`.

**Summary:**
- The solution efficiently counts valid triples by leveraging grouping and arithmetic properties.
- It handles the upper constraint of \(N = 3 \times 10^5\) in linear time.
- Further optimizations are unnecessary as the solution is already optimal.

**Possible Extensions:**
- If the sequence were dynamic (with updates), a Fenwick tree or segment tree could be used, but the static nature of the problem makes the current approach sufficient.