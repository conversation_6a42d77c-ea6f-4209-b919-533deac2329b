# Solution for abc306_c.md

Generated at: 2025-07-22 06:15:38
Model: deepseek-reasoner

## Reasoning Process

We are given an integer N and a list A of 3N integers. Each integer from 1 to N appears exactly three times.
We need to assign to each integer i (from 1 to N) the index of its middle occurrence in A. Then, we sort the integers i by these middle indices.

Example: 
  Input: N=3, A = [1,1,3,2,3,2,2,3,1]
  For i=1: occurrences at indices 0,1,8 (0-indexed) -> middle index is 1 (so f(1)=1, but note: the problem uses 1-indexed indices? Let's see the example output)

However, note the example output: 
  Output: 1 3 2

But the problem states: 
  - The indices for 1: j=1,2,9 -> so the middle is 2 (the second one) -> and the problem says f(1)=2 (which is 1-indexed).

Important: The problem says the input is given as A_1, A_2, ... A_{3N}. So the first element is at index 1.

But in our code, we typically use 0-indexed. So when we traverse the array A (which we get as a list of 3N integers), the first element is at index 0.

However, the problem defines f(i) as the index in the 1-indexed position? 

Looking at example: 
  A = [1,1,3,2,3,2,2,3,1] for N=3.

In 1-indexed:
  position1:1, position2:1, position3:3, position4:2, position5:3, position6:2, position7:2, position8:3, position9:1.

For 1: positions 1,2,9 -> middle is position2 -> so f(1)=2.
For 2: positions 4,6,7 -> middle is 6.
For 3: positions 3,5,8 -> middle is 5.

Then we sort 1,2,3 by f(i): 
  i=1:2, i=3:5, i=2:6 -> so the order is 1,3,2.

Therefore, we must record the middle occurrence in 1-indexed? Or does it matter?

Actually, for sorting, we can use the 0-indexed index because the relative order is the same. However, note that the problem uses 1-indexed in the example.

But the problem says: "f(i) is the index" meaning the actual index in the 1-indexed sequence.

However, if we are to sort by the index, then we can use 0-indexed indices because the relative order of the indices (whether 0-indexed or 1-indexed) is the same. The only difference is a constant shift.

But note: if we use 0-indexed, then for the first element we have index0 which would be 0, and the second element index1 which is 1. The ordering is the same as 1-indexed: the first element is the smallest, then the next, etc.

Therefore, we can work entirely in 0-indexed. Then when we output the numbers i sorted by the 0-indexed middle index, it will be the same as if we used 1-indexed.

So we can do:

For each number i from 1 to N, we need to record the three positions. Then take the middle one. Then sort the numbers i by that middle position.

But note: N can be up to 10^5, and the array length is 3*10^5.

How to do it efficiently?

We can traverse the array once and for each occurrence of a number, record the positions. Since each number appears exactly three times, we can store the three positions for each number.

We can use an array `positions` of size N+1, each element is a list of up to 3 positions.

Steps:
1. Initialize an array `pos` of size N+1, each as an empty list.
2. Traverse the array A (0-indexed index from 0 to 3N-1):
   - For each element a = A[i], append the current index (0-indexed) to the list `pos[a]`.
3. For each number i from 1 to N, we know the list `pos[i]` has three indices. We sort this list (though the indices we get are in increasing order because we traverse from left to right, so we don't need to sort? Actually, we are appending in increasing index order, so the list is sorted. But we can take the middle element: the second one, i.e., the element at index 1 in the list of three).

But note: the three occurrences for a number might not be in order? Actually, as we traverse from left to right, we first meet the first occurrence, then the second, then the third. So the list for each number will be [first_index, second_index, third_index] in increasing order.

So we can take `middle = pos[i][1]`.

4. Then we have a list of tuples: for each i, we have (middle, i). Then we sort this list by `middle`.

5. Then we output the list of i's in the sorted order.

But what if we have multiple same middle indices? The problem does not specify, but the middle indices are distinct? Actually, the middle index is the index in the array A. Since each index in A is unique, the middle indices are unique.

Therefore, we can do:

Example 2: 
  N=1, A = [1,1,1]
  For i=1: positions = [0,1,2] -> middle = 1 (0-indexed) -> then we output [1].

Example 3:
  Input: N=4, A = [2,3,4,3,4,1,3,1,1,4,2,2] (12 elements)
  We need to record:
    i=1: at indices 5,7,8 -> middle index = 7 (0-indexed) -> which is the 8th element? But the example output is 3 4 1 2.

  How do we get that?
    i=1: middle index 7 (0-indexed) -> which is 8 in 1-indexed? But let me check:

  Actually, the example input: 
      "2 3 4 3 4 1 3 1 1 4 2 2"

  Positions for each number (0-indexed):
    1: indices 5,7,8 -> middle at index7 (which is the second occurrence of 1? Actually, the occurrences: 
        index5: the first 1
        index7: the second 1
        index8: the third 1 -> so middle is index7.

    2: indices 0,10,11 -> middle at index10.
    3: indices 1,3,6 -> middle at index3? 
        Actually: 
          first 3: index1 -> the first element is 2 at0, then 3 at1 -> that's the first 3.
          then 3 at index3 (the fourth element) -> that's the second 3.
          then 3 at index6 (the seventh element) -> so middle is index3 (0-indexed).

    4: indices 2,4,9 -> middle at index4.

  Now, we sort the numbers by the middle index (0-indexed):
    i=3: middle index = 3
    i=4: middle index = 4
    i=1: middle index = 7
    i=2: middle index = 10

  So the sorted order is 3,4,1,2 -> which matches the example.

But note: the example output: "3 4 1 2"

Therefore, we can implement:

Steps in code:

  positions = [[] for _ in range(N+1)]   # index from 0 to N, we use 1..N

  for idx, num in enumerate(A):
      positions[num].append(idx)

  # Now, we'll create a list of tuples (middle_index, number) for each number from 1 to N.
  res = []
  for i in range(1, N+1):
      # positions[i] has three indices: [first, second, third]
      mid_index = positions[i][1]   # the second element (0-indexed in the list of three)
      res.append( (mid_index, i) )

  # Then sort res by mid_index
  res.sort(key=lambda x: x[0])

  # Then extract the numbers: [i for each (mid_index, i) in res]
  output = [i for (_, i) in res]

But note: the positions for each number are stored in the order of occurrence (because we traverse from left to right). So the list for each number is already sorted.

Time Complexity: 
  - Traversing the array: O(3N) = O(N)
  - Building the list `res`: O(N)
  - Sorting the list `res`: O(N log N)

Space Complexity: 
  - positions: O(3N) = O(N)
  - res: O(N)

Since N can be up to 10^5, O(N log N) is acceptable.

But can we avoid the sort? 

Alternatively, we can record the middle index for each number and then use counting sort? However, the middle indices are in the range [0, 3N-1] (0-indexed) and 3N is 300,000 which is acceptable. But we don't need to because N log N for 10^5 is about 10^5 * log2(10^5) ≈ 1.7e6, which is acceptable in Python? 

But let's see: 10^5 * log2(10^5) ≈ 10^5 * 17 = 1.7e6, which is acceptable in Pyton in most online judges.

However, we can do a non-comparison sort? But the problem doesn't require it.

But note: we can also avoid storing all three positions? We only need the middle one. How?

Actually, we can track for each number the count of occurrences. When we see the second occurrence, that is the middle one. Then we can record the index at the second occurrence.

Idea:
  We can use an array `count` of size N+1, initialized to 0, and an array `mid_occurrence` of size N+1, to record the index of the middle occurrence for each number.

  Then traverse the array:
      for index i from 0 to 3N-1:
          num = A[i]
          count[num] += 1
          if count[num] == 2:
              then we set mid_occurrence[num] = i   # this is the middle occurrence (0-indexed)

  Then we have for each number i, mid_occurrence[i] = the index of the second occurrence.

  Then we create a list: for i from 1 to N: (mid_occurrence[i], i)
  Then sort by mid_occurrence[i] and output i.

This avoids storing the entire list of positions. We only use two arrays: one for counts and one for the middle occurrence.

Space: O(N) for the arrays.

Time: O(N) for the traversal and O(N log N) for the sorting.

But note: we don't even need the counts beyond 2? Actually, we can use the counts to know when we are at the second occurrence.

However, we must note: what if a number appears and we haven't seen two occurrences? But we know each number appears exactly three times. So when we see the second occurrence, we record the index. We don't care about the third.

So this method is efficient.

Steps for this method:

  count = [0] * (N+1)
  mid_occurrence = [0] * (N+1)   # or we can leave as 0 for unused? Actually, we will set when we see the second.

  for idx in range(3*N):
      num = A[idx]
      count[num] += 1
      if count[num] == 2:
          mid_occurrence[num] = idx

  Then create a list: 
      res = []
      for i in range(1, N+1):
          res.append( (mid_occurrence[i], i) )
      res.sort(key=lambda x: x[0])
      output = [i for (_, i) in res]

This is more space efficient? We save storing two extra integers per number (because we don't store the first and third positions). But the asymptotic space is still O(N). However, we use two arrays of size N+1 and one list of size N for the result.

But the first method stored a list of three positions for each number: so 3N integers. This method: we store one integer per number for mid_occurrence and one for count (which we can discard after we build mid_occurrence). So we use about 2N integers for the arrays (plus the result list). 

But both are O(N). The second method might be slightly more efficient in space and also in time because we avoid storing the other two positions.

But the first method is clear. However, the second method is also clear and more efficient in constants.

Let's test with the examples.

Example1: 
  A = [1,1,3,2,3,2,2,3,1] (N=3)

  count: [0]*4, mid_occurrence: [0]*4
  idx0: num=1 -> count[1]=1 -> nothing
  idx1: num=1 -> count[1]=2 -> set mid_occurrence[1]=1
  idx2: num=3 -> count[3]=1 -> nothing
  idx3: num=2 -> count[2]=1 -> nothing
  idx4: num=3 -> count[3]=2 -> set mid_occurrence[3]=4
  idx5: num=2 -> count[2]=2 -> set mid_occurrence[2]=5
  ... the rest we don't care.

  Then we have:
      i=1: (1,1)
      i=2: (5,2)
      i=3: (4,3)

  Then sort by the first element: (1,1), (4,3), (5,2) -> output [1,3,2] -> matches.

Example3: 
  A = [2,3,4,3,4,1,3,1,1,4,2,2] (N=4)

  count: [0]*5, mid_occurrence: [0]*5
  idx0: num=2 -> count[2]=1
  idx1: num=3 -> count[3]=1
  idx2: num=4 -> count[4]=1
  idx3: num=3 -> count[3]=2 -> set mid_occurrence[3]=3
  idx4: num=4 -> count[4]=2 -> set mid_occurrence[4]=4
  idx5: num=1 -> count[1]=1
  idx6: num=3 -> count[3]=3 -> skip
  idx7: num=1 -> count[1]=2 -> set mid_occurrence[1]=7
  idx8: num=1 -> count[1]=3 -> skip
  idx9: num=4 -> count[4]=3 -> skip
  idx10: num=2 -> count[2]=2 -> set mid_occurrence[2]=10
  idx11: num=2 -> skip

  Then:
      i=1: (7,1)
      i=2: (10,2)
      i=3: (3,3)
      i=4: (4,4)

  Then sort by the first: 
      (3,3), (4,4), (7,1), (10,2) -> output [3,4,1,2] -> matches.

Therefore, we choose the second method.

Edge: N=1, as above.

Implementation:

  Read N
  Read the list A of 3N integers.

  Initialize:
      count = [0] * (N+1)   # indices 0 to N, we care about 1..N
      mid_occurrence = [0] * (N+1)   # we'll set for each i from 1 to N.

  for index, num in enumerate(A):
      count[num] += 1
      if count[num] == 2:
          mid_occurrence[num] = index

  Then we create a list of (mid_occurrence[i], i) for i in range(1, N+1)
  Sort this list by the first element.

  Then output the i's.

But note: the problem expects the output as space separated integers.

Let's code accordingly.

However, note: the second method requires that we set the middle occurrence at the second time we see the number. And we are sure that each number appears exactly three times, so every number will have count==2 exactly once.

So we can do.

Let me write the code accordingly.

Time complexity: O(3N) for the traversal and O(N log N) for the sort. Since N is 10^5, and 3N is 300,000, the traversal is linear. The sort is O(N log N) which is acceptable.

We'll implement the solution accordingly.

Code:

  import sys

  def main():
      data = sys.stdin.read().split()
      n = int(data[0])
      A = list(map(int, data[1:1+3*n]))

      count = [0] * (n+1)
      mid_occurrence = [0] * (n+1)   # we will store the index (0-indexed) of the middle occurrence for each number

      for idx, num in enumerate(A):
          count[num] += 1
          if count[num] == 2:
              mid_occurrence[num] = idx

      # Now, create a list of tuples for numbers 1 to n
      pairs = []
      for i in range(1, n+1):
          pairs.append( (mid_occurrence[i], i) )

      pairs.sort(key=lambda x: x[0])

      # Now extract the numbers
      result = [str(i) for (_, i) in pairs]
      print(" ".join(result))

  But note: the problem has a class Solution? We are to implement a method.

  Alternatively, we can write:

  class Solution:
      def solve(self, n: int, A: List[int]) -> List[int]:
          count = [0] * (n+1)
          mid_occurrence = [0] * (n+1)

          for idx, num in enumerate(A):
              count[num] += 1
              if count[num] == 2:
                  mid_occurrence[num] = idx

          # Create list of numbers 1 to n, then sort by their mid_occurrence
          numbers = list(range(1, n+1))
          numbers.sort(key=lambda i: mid_occurrence[i])

          return numbers

  However, note: we are using the fact that we can index mid_occurrence by i. This is efficient.

  But we can also do without creating the pairs list? The sort of numbers by key uses the mid_occurrence for each number.

  Let me test with example1:
      numbers = [1,2,3]
      sort key: 
          1: mid_occurrence[1] = 1 (from the example above, 0-indexed index1)
          2: mid_occurrence[2] = 5
          3: mid_occurrence[3] = 4
      Then sorted by: [1, 4, 5] -> [1,3,2] -> correct.

  This is the same.

  But note: we are using an array `mid_occurrence` that we built. This is O(1) per access.

  This method is efficient.

  However, note: if we use `numbers.sort(key=lambda i: mid_occurrence[i])`, we are creating a list of size N and then sorting it. This is the same as the pairs method.

  We can choose either.

  I'll choose the direct sort of the list [1..N] by key.

  Code:

      count = [0] * (n+1)
      mid_occurrence = [0] * (n+1)   # we will store the index (0-indexed) of the middle occurrence for each number

      for idx, num in enumerate(A):
          count[num] += 1
          if count[num] == 2:
              mid_occurrence[num] = idx

      numbers = list(range(1, n+1))
      numbers.sort(key=lambda x: mid_occurrence[x])

      return numbers

But note: the array `count` and `mid_occurrence` are of size n+1, and we traverse the list A of size 3n. The list numbers is of size n. The sort is O(n log n). 

This meets the constraints.

Let me run the provided examples.

Example 1: n=3, A = [1,1,3,2,3,2,2,3,1]
  We'll build:
      count: initially [0,0,0,0]
      mid_occurrence: [0,0,0,0]

      idx0: num=1 -> count[1]=1 -> no set
      idx1: num=1 -> count[1]=2 -> set mid_occurrence[1]=1
      idx2: num=3 -> count[3]=1 -> no
      idx3: num=2 -> count[2]=1 -> no
      idx4: num=3 -> count[3]=2 -> set mid_occurrence[3]=4
      idx5: num=2 -> count[2]=2 -> set mid_occurrence[2]=5
      ... the rest we don't set.

      Then mid_occurrence = [0,1,5,4]   # indices: 0 unused, 1:1, 2:5, 3:4.

      numbers = [1,2,3] 
        sort key: 
          1 -> mid_occurrence[1]=1
          2 -> mid_occurrence[2]=5
          3 -> mid_occurrence[3]=4
        sorted: [1,3,2] -> [1,3,2] -> matches.

Example 2: n=1, A=[1,1,1]
      count = [0,0]
      mid_occurrence = [0,0]

      idx0: num=1 -> count[1]=1 -> no
      idx1: num=1 -> count[1]=2 -> set mid_occurrence[1]=1
      idx2: skip.

      numbers = [1] -> returns [1].

Example 3: n=4, A=[2,3,4,3,4,1,3,1,1,4,2,2]
      mid_occurrence[2] set at the second occurrence of 2: which is at index10? 
        But wait: 
          idx0: num=2 -> count[2]=1
          idx1: num=3 -> count[3]=1
          idx2: num=4 -> count[4]=1
          idx3: num=3 -> count[3]=2 -> set mid_occurrence[3]=3
          idx4: num=4 -> count[4]=2 -> set mid_occurrence[4]=4
          idx5: num=1 -> count[1]=1
          idx6: num=3 -> count[3]=3 -> skip
          idx7: num=1 -> count[1]=2 -> set mid_occurrence[1]=7
          idx8: num=1 -> skip
          idx9: num=4 -> skip
          idx10: num=2 -> count[2]=2 -> set mid_occurrence[2]=10
          idx11: skip.

      Then mid_occurrence: 
          index0: unused
          index1: 7
          index2: 10
          index3: 3
          index4: 4

      numbers = [1,2,3,4]
        key: 
          1: mid_occurrence[1]=7
          2: mid_occurrence[2]=10
          3: mid_occurrence[3]=3
          4: mid_occurrence[4]=4
        sorted by key: [3,4,1,2] -> [3,4,1,2] -> matches.

So we'll implement accordingly.

But note: the array `count` is only used to check when we hit the second occurrence. We don't need it beyond that. We can also use a list of booleans? Actually, we only need to know if we have seen the first occurrence to then set at the second. But we are using a counter. Alternatively, we can use three states: 
  0: not seen, 
  1: seen once, 
  2: seen twice (and then we set and we don't care about the third).

But the counter is simple.

We can also avoid the count array by using a dictionary? But we know the numbers are from 1 to n, so an array is efficient.

Let me write the code accordingly.

We'll use:

  count = [0] * (n+1)

But we can also use a list of zeros for count.

This is efficient.

We'll implement the solution as described.

Note: We must be cautious of the size: n up to 10^5 -> 100,000, so the arrays are of size 100001, which is acceptable.

We'll now write the code.

One more note: the problem says the input is given as:
  N
  A1 A2 ... A_{3N}

So in the method, we are given N and the list A.

Code:

  from typing import List

  class Solution:
      def solve(self, n: int, A: List[int]) -> List[int]:
          # We'll create the count and mid_occurrence arrays
          count = [0] * (n+1)   # from 0 to n, index0 unused
          mid_occurrence = [0] * (n+1)   # same, we'll use indices 1..n

          # Traverse A with index
          for idx, num in enumerate(A):
              count[num] += 1
              if count[num] == 2:
                  mid_occurrence[num] = idx   # store the current index (0-indexed)

          # Now, we want to sort the numbers 1 to n by mid_occurrence[num]
          numbers = list(range(1, n+1))
          numbers.sort(key=lambda x: mid_occurrence[x])
          return numbers

This should be the solution.

Let me test with the examples in the code.

Example 1: 
  n=3, A = [1,1,3,2,3,2,2,3,1]
  numbers = [1,2,3] -> sorted by mid_occurrence: 
      mid_occurrence[1] = 1 (from the second occurrence at index1)
      mid_occurrence[2] = 5
      mid_occurrence[3] = 4
  sorted: [1,3,2] -> [1,3,2]

Example 3: [3,4,1,2] as above.

But wait: what if two numbers have the same middle index? 
  The problem states: "Each i occurs exactly three times", and the middle index is the index in the array. Since each array index is unique, the middle indices are unique. So no collision.

Therefore, we are safe.

We'll submit this.

## Final Solution

### Approach
The problem requires sorting integers from 1 to N based on the index of their middle occurrence in a given sequence A of length 3N. Each integer from 1 to N appears exactly three times in the sequence. The middle occurrence of an integer i is the second time it appears in the sequence when traversed from left to right.

#### Approach
1. **Problem Analysis**: The key observation is that for each integer i, we need to identify the index of its second occurrence in the sequence. The integers 1 to N are then sorted based on these indices.
2. **Intuition**: By traversing the sequence once, we can track the count of each integer. When an integer is encountered for the second time, its index is recorded as the middle occurrence.
3. **Algorithm Selection**: 
   - **Tracking Occurrences**: Use an array `count` to keep track of how many times each integer has been encountered.
   - **Recording Middle Indices**: For each integer, when encountered for the second time, record the current index in an array `mid_occurrence`.
   - **Sorting by Middle Indices**: Generate a list of integers 1 to N and sort them based on their recorded middle indices.
4. **Complexity Analysis**: 
   - **Time Complexity**: O(N log N) due to the sorting step. The traversal of the sequence is O(3N) = O(N).
   - **Space Complexity**: O(N) for storing the count and middle occurrence arrays, and the list of integers to sort.

### Solution Code
```python
from typing import List

class Solution:
    def solve(self, n: int, A: List[int]) -> List[int]:
        count = [0] * (n + 1)
        mid_occurrence = [0] * (n + 1)
        
        for idx, num in enumerate(A):
            count[num] += 1
            if count[num] == 2:
                mid_occurrence[num] = idx
        
        numbers = list(range(1, n + 1))
        numbers.sort(key=lambda x: mid_occurrence[x])
        
        return numbers
```

### Explanation
1. **Initialization**: Arrays `count` and `mid_occurrence` are initialized to store the occurrence counts and middle indices of integers 1 to N.
2. **Traversal**: The sequence A is traversed once. For each integer encountered:
   - Its count is incremented.
   - If the count reaches 2, the current index is recorded as the middle occurrence in `mid_occurrence`.
3. **Sorting**: The integers 1 to N are sorted based on their recorded middle indices from `mid_occurrence`.
4. **Result**: The sorted list of integers is returned, representing the integers ordered by the indices of their middle occurrences in the sequence.

This approach efficiently identifies the middle occurrences and leverages sorting to produce the desired output, adhering to the problem constraints.