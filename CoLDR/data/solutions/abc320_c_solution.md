# Solution for abc320_c.md

Generated at: 2025-07-22 06:36:57
Model: deepseek-reasoner

## Reasoning Process

We are given an integer M and three strings S1, S2, S3, each of length M<PERSON>
<PERSON><PERSON><PERSON> can press a button for a reel at non-negative integer time t (exactly t seconds after the start). 
When he presses the button for the i-th reel at time t, the reel stops and shows the character at position (t mod M) in the string S_i (note: the problem says ((t mod M)+1)-th character, meaning 1-indexed, so we adjust: if we use 0-indexed, then we take t mod M).

We must stop all three reels. The goal is to have all three reels show the same digit.

We are to find the minimum total time from the start until the last reel is stopped (which is the maximum of the stopping times for each reel) such that the three stopped digits are the same.

Note: we cannot press two buttons at the same time? Actually, the problem says: "For each non-negative integer t, <PERSON><PERSON><PERSON> can either choose and press one button or do nothing exactly t seconds after the reels start spinning." So at each integer time, he can press at most one button. However, note that we can press different reels at different times.

Therefore, we assign to each reel i a stopping time t_i (non-negative integer). The constraints are:
1. The digit shown by reel i is S_i[t_i mod M] (if we consider 0-indexed, then the position is (t_i mod M)).
2. The three digits must be the same: let that digit be d, so we require:
   S1[t1 mod M] = S2[t2 mod M] = S3[t3 mod M] = d.
3. The stopping times t1, t2, t3 must be distinct? Actually, we can press at most one button at each time. Therefore, the same time cannot be used for two different reels. So we require t1, t2, t3 to be distinct? But note: the problem says "for each non-negative integer t, he can press one button". So if we press two buttons at the same time, that is not allowed. Therefore, the three stopping times must be distinct.

However, wait: what if we press one button at time t1, then another at time t2, and the third at time t3? And we require that at each time we press at most one. So the times must be distinct.

Therefore, we are looking for three distinct non-negative integers t1, t2, t3 such that:
   S1[t1 mod M] = S2[t2 mod M] = S3[t3 mod M] = d   (for some digit d)
and the overall time (which is the maximum of t1, t2, t3) is minimized.

But note: we are allowed to press the buttons at any time (as long as we don't press two at the same time). Also, we can press them in any order.

How to solve?
Since M is at most 100, we can consider the following:

Approach 1: Brute Force over the digit and then over the positions for each reel.

But note: the stopping time for a reel i can be arbitrarily large? However, because the reels are periodic with period M, we can consider modulo M. For a fixed digit d, we can consider the set of residues (i.e., positions in the string) for each reel that show d.

For reel1, let A_d = { j in [0, M-1] such that S1[j] = d }
Similarly, B_d for reel2 and C_d for reel3.

Then, we want to assign to reel1 a time t1, to reel2 t2, to reel3 t3 such that:
   t1 mod M ∈ A_d, t2 mod M ∈ B_d, t3 mod M ∈ C_d
and t1, t2, t3 are distinct non-negative integers, and we want to minimize max(t1,t2,t3).

How to assign the times? Note that for a residue r in A_d, the time t1 can be r, r+M, r+2M, ... Similarly for the others.

But we require distinct times. We are free to choose the multiples.

We can think: for each residue set (a in A_d, b in B_d, c in C_d) we can assign:
   t1 = a + k1 * M   (k1>=0)
   t2 = b + k2 * M   (k2>=0)
   t3 = c + k3 * M   (k3>=0)

But we must have t1, t2, t3 distinct and we want to minimize T = max(t1,t2,t3).

Alternatively, we can fix the time T (the maximum time) and then check if there is an assignment such that each t_i <= T and t_i mod M is in the set for that reel, and the t_i are distinct. But T could be as large as ...? Since M is only 100, the residues are fixed. However, the minimal T might be as large as 3*M? Actually, in the worst case, we might have to go up to 3*M? But note: we can have overlapping residues. 

But note: we are minimizing the maximum time. The minimal maximum time we can get for a fixed (a,b,c) is by assigning the residues in increasing order? Actually, we can assign the residues arbitrarily to multiples. However, we have to avoid the same time.

We can try to compute the minimal maximum time for a fixed triple (a, b, c) (which are residues) as follows:

We want to assign nonnegative integers k1, k2, k3 such that:
   t1 = a + k1*M, t2 = b + k2*M, t3 = c + k3*M
and t1, t2, t3 are distinct and we minimize T = max(t1, t2, t3).

But note: we can also assign the same residue to different multiples. How to minimize T? We can sort the residues: but the problem is that we can assign different multiples to each residue.

Alternatively, we can consider the following: we are going to assign the smallest multiples such that the times are distinct and the maximum is as small as possible.

We can do: for each residue, we have multiple choices: 0, 1, 2, ... multiples. We want to cover the residues with distinct times and minimize the maximum.

But note: the residues a, b, c are fixed. We can consider the residues modulo M, and then the times are in the form of residue + k*M.

We can think of the three residues as three points on the circle (mod M). But we can also go to higher cycles.

We can also note: the minimal maximum time for the triple (a, b, c) is the minimal T such that there exist k1, k2, k3 in nonnegative integers with:
   a + k1*M <= T
   b + k2*M <= T
   c + k3*M <= T
and the three numbers a+k1*M, b+k2*M, c+k3*M are distinct.

How to compute the minimal T for fixed (a,b,c)? Since M is small, we can try to iterate over the possible offsets? Actually, we can try to assign the multiples in increasing order.

We can consider the following: without loss of generality, assume we assign the smallest multiples to the residues in an order. But note: we can assign arbitrarily.

Alternatively, we can consider the minimal T must be at least max(a, b, c) and at most 3*M? Actually, we can have T as large as needed, but we are minimizing. However, we can use the pigeonhole principle: in the range [0, T] there are floor(T/M)+1 numbers that are congruent to a mod M? Similarly for b and c. We require at least one number for each residue and that they are distinct.

But note: the residues might be the same. For example, if a=b=c, then we have to pick three different multiples: so the times would be a, a+M, a+2M. Then T = a+2M.

In general, for a fixed triple (a,b,c), we can compute the minimal T such that we can assign distinct times to the three residues. How?

We can consider the distinct residues. But note: even if two residues are the same, we can assign different multiples. So the minimal T is the minimal value such that we can pick three distinct numbers: one from the set {a, a+M, a+2M, ...}, one from {b, ...}, one from {c, ...} and all <= T, and distinct.

We can do:

Let T_min = 0
We can iterate T from 0 to some upper bound. What upper bound? The worst-case minimal T might be 3*M? Actually, consider: we have three residues. The worst case is when a=b=c, then the smallest distinct times we can assign are a, a+M, a+2M, so T = a+2M. Since a is at most M-1, then T_min = (M-1) + 2*M = 3*M-1.

But note: we might have a,b,c not all equal. However, we can set an upper bound at 3*M (or 3*M-1) because beyond that we can always get a solution? Actually, we can: because we can take the residue with the largest value and then add multiples until we have distinct times. The maximum multiple we would need for one residue is 2 (if all residues are the same) and then T = max(a,b,c) + 2*M, which is <= (M-1) + 2*M = 3*M-1.

Therefore, for a fixed triple (a,b,c), we can iterate T from 0 to 3*M-1 (or 3*M) and check if we can assign distinct times for a,b,c that are <= T.

But note: we can also assign multiples that make the time larger than 3*M-1? But we are looking for the minimal T. Since we know that the minimal T cannot exceed 3*M-1 (because we can always assign the residues with multiples 0,1,2 respectively, then T = max(a,b,c) + 2*M <= (M-1)+2*M = 3*M-1), we can set an upper bound of 3*M.

So for fixed (a,b,c), we can iterate T from 0 to 3*M (inclusive) and check:

   Let set1 = { a + k*M <= T } -> the set of times for a that are <= T. Similarly for b and c.
   Then we need to pick one time from each set such that they are distinct.

But note: the sets might be empty? Then skip.

Alternatively, we can compute the minimal T for (a,b,c) without iterating? 

We can try to assign the smallest possible times to each residue:

We want to assign the smallest distinct times that are at least the residue and in the arithmetic progression for that residue.

We can do:

   Let t1 = a, t2 = b, t3 = c.

   But if any two are equal, then we have to increase one of them by M. We can do:

   Sort the residues: but we have to assign multiples arbitrarily.

   We can consider the three residues and then assign the smallest multiple that hasn't been taken.

   Algorithm for fixed (a,b,c):
      Let times = sorted([a, b, c])
      But they might be repeated.

      We can use a greedy: 
        Let t = [a, b, c]
        We sort t, but then we have to resolve duplicates.

      Alternatively, we can do:

        Let k0 = 0, k1 = 0, k2 = 0.
        Then we have:
            t0 = a + k0*M
            t1 = b + k1*M
            t2 = c + k2*M

        We want distinct t0, t1, t2 and minimize max(t0, t1, t2).

        We can start with k0=k1=k2=0. Then if they are distinct, then T = max(a,b,c).

        Otherwise, we increase the duplicates: 
          If two are equal, then we can add M to one of them. Then if that makes a duplicate again, we can add M again.

        However, we have three numbers. We can do:

          We maintain a multiset of the current times and we know which are duplicated.

        But note: we want to minimize the maximum. So we should increase the smallest duplicate? Actually, we can try all orders? There are only 3! = 6 orders? 

        Alternatively, we can iterate the multiples for each residue: since the minimal T is at most 3*M, then the multiples k0, k1, k2 are at most 2 (because 0,1,2). So we can iterate k0 in {0,1,2}, k1 in {0,1,2}, k2 in {0,1,2} and then check if the three times (a+k0*M, b+k1*M, c+k2*M) are distinct. Then T = max(a+k0*M, b+k1*M, c+k2*M). Then we take the minimum T over all k0,k1,k2 such that the times are distinct.

        Why multiples only up to 2? Because if we take k0=0,1,2 then we have three distinct multiples? But note: the residues are fixed. Actually, if we take multiples beyond 2, then the time would be at least residue + 3*M, which is at least 3*M. But we know that we can get a solution at 3*M-1 (as argued) so we don't need to go beyond 2.

        Therefore, for fixed (a,b,c), we can iterate k0, k1, k2 in {0,1,2} (so 3^3=27 iterations) and then:
            t0 = a + k0 * M
            t1 = b + k1 * M
            t2 = c + k2 * M
            if t0, t1, t2 are distinct, then candidate = max(t0, t1, t2)
            then take the minimum candidate over the 27.

        But note: it is possible that we need a multiple greater than 2? Actually, we argued that the minimal T is at most 3*M-1. However, if we take k0=2, then t0 = a+2M, which is <= (M-1)+2M = 3*M-1. Similarly, k0=3 would yield at least 3M, which is more than 3M-1. So we don't need to go beyond 2.

        Therefore, for each triple (a,b,c) of residues, we can compute candidate_min = min{ max(a+k0*M, b+k1*M, c+k2*M) for k0,k1,k2 in {0,1,2} and the three times are distinct }.

        Then we take the minimum candidate_min over all a in A_d, b in B_d, c in C_d and over all digits d that appear in all three reels? (Note: if a digit d does not appear in one of the reels, then skip d.)

Then the overall answer is the minimum candidate_min over all d and over all residues.

But note: what if the same digit appears in multiple residues per reel? Then we consider every combination.

However, the problem says: if impossible, output -1. So if for no digit d we have A_d, B_d, C_d non-empty, then we output -1.

But note: the residues a, b, c for a given digit d must be chosen independently.

Therefore, the algorithm:

   Precompute for each digit d (which is from '0' to '9') the sets:
        A_d = { i in [0, M-1] such that S1[i]==d }
        B_d = { i in [0, M-1] such that S2[i]==d }
        C_d = { i in [0, M-1] such that S3[i]==d }

   Initialize best = a large number (like 10**9)

   For each digit d:
        If A_d, B_d, C_d are all non-empty:
            For each a in A_d:
                For each b in B_d:
                    For each c in C_d:
                         For k0 in {0,1,2}:
                            For k1 in {0,1,2}:
                                For k2 in {0,1,2}:
                                    t1 = a + k0*M
                                    t2 = b + k1*M
                                    t3 = c + k2*M
                                    if t1, t2, t3 are distinct:
                                        candidate = max(t1, t2, t3)
                                        best = min(best, candidate)

   If best is still 10**9, then return -1, else return best.

But what is the time complexity? 
   The outer loop: digits d: 10 possibilities at most.
   Then for each d: 
        |A_d| * |B_d| * |C_d|: worst-case, if the digit d appears in every position, then |A_d|=M, |B_d|=M, |C_d|=M -> 100*100*100 = 10^6 per digit? Then 10*10^6 = 10^7, which is acceptable for M<=100? Actually, 10 * (100^3) = 10 * 1000000 = 10,000,000, which is acceptable in Python? But then we have an inner loop of 27 (3^3). So total operations: 10 * (100^3) * 27 = 27000000, which is 27 million. This might be borderline in Pyton in worst-case, but note that the digit d might not appear that often? Actually, worst-case all strings are all the same digit? Then we do 10 * (100^3)*27 = 27e6. In Python, 27e6 iterations might be acceptable in PyPy or C++ but in Python it might be around 1-2 seconds? But the constraints say M<=100, and 27e6 is acceptable in Pyton in optimized C++ but in Python we must be cautious.

Alternatively, we can optimize:

   We note that the sets A_d, B_d, C_d for a digit d might be large, but if we precompute the sets, we can avoid iterating over every triple? 

   But note: worst-case if the digit appears in all positions, then we have 100*100*100 = 1e6 per digit, and 10 digits gives 10e6, and then multiplied by 27 -> 270e6, which is 270 million, which might be too slow in Python.

We need a better approach.

Alternative approach for a fixed digit d:

   We want to minimize T = max(t1, t2, t3) where t1 = a + k0*M, t2 = b + k1*M, t3 = c + k2*M, and the times are distinct, and a in A_d, b in B_d, c in C_d.

   Instead of iterating over all a,b,c, we can precompute for each residue set the possible residues and then for each residue in the set, we can consider the multiples 0,1,2 to form up to 3*|set| candidate times? But then we have to combine three sets? 

   Alternatively, we can iterate over the residues for the first reel, and for each residue a, we can consider the two other reels independently? 

   But note: we have to avoid duplicate times. 

   Actually, we can precompute for each residue set the candidate times (for multiples 0,1,2) and then we have three sets: 
        T1 = { a + k0*M for a in A_d, k0 in {0,1,2} }
        T2 = { b + k1*M for b in B_d, k1 in {0,1,2} }
        T3 = { c + k2*M for c in C_d, k2 in {0,1,2} }

   Then we want to pick t1 in T1, t2 in T2, t3 in T3 such that t1, t2, t3 are distinct and minimize max(t1,t2,t3).

   How to do that? We can iterate over the candidate maximum time T from 0 to 3*M, and check if there is a solution with max(t1,t2,t3) <= T? Then we take the minimal T for which there is a solution.

   But how to check for a fixed T? 
        We require:
            There exists t1 in T1, t2 in T2, t3 in T3 such that t1<=T, t2<=T, t3<=T, distinct.

        We can iterate over t1 in T1 that are <= T, then t2 in T2 that are <= T and not equal to t1, then t3 in T3 that are <= T and not equal to t1 and t2? 

        The size of T1, T2, T3 is at most 3*|A_d|, which is at most 300 (because |A_d|<=100, so 300). Then the triple iteration would be 300*300*300 = 27e6 per digit? Then 10*27e6 = 270e6, which is too high.

   Alternatively, we can use a state of (t1, t2, t3) but that is the same as above.

   We can avoid iterating over all by sorting the sets and then using two loops? Actually, for each T, we can check:

        For each t1 in T1 with t1<=T:
            For each t2 in T2 with t2<=T and t2 != t1:
                For each t3 in T3 with t3<=T and t3 != t1 and t3 != t2:
                    then we have a solution.

        Then we break at the smallest T.

   But how to iterate T? We can iterate T from 0 to 3*M (which is 300) and then for each T, we do at most |T1| * |T2| * |T3| = (300)^3 = 27e6 per digit? Then 10*300*27e6 = 81e9, which is too high.

We need a better way.

Let me go back: we can compute the minimal T for a fixed triple (a,b,c) quickly (with 27 iterations). The problem is that iterating over all a,b,c for a digit d is 100^3 = 1e6 per digit, then 10 digits gives 10e6, then multiplied by 27 gives 270e6, which is 270 million. In Python, this might be acceptable? Let me test: 270 million iterations in Pyton might take a few seconds? We need to see the constraints: M<=100, but worst-case 270 million iterations might be about 10-30 seconds in Python? That might be borderline in Pyton for online judges.

But note: the worst-case is when every string is the same digit? Then we have 100^3 = 1e6 per digit, and we have one digit (because the entire string is the same digit). Then we do 1e6 * 27 = 27e6, which is acceptable (about 1 second in Python). But if we have 10 digits? Then we do 10 * (100^3 * 27) = 270e6, which might be 2-3 seconds in Pyton? We can try to optimize by breaking early? Or we can note that the sets A_d, B_d, C_d for digits that don't appear in all three reels we skip.

But worst-case: if the string has all digits? Then for each digit d, the sets A_d, B_d, C_d might be non-empty? Actually, worst-case: each digit appears about M/10 times? Then the sizes of the sets are about 10 (if uniform). Then the total over digits: 10 * (10 * 10 * 10) * 27 = 10 * 1000 * 27 = 270000, which is acceptable.

But worst-case when one digit appears in every position? Then we do 1 * (100^3) * 27 = 27000000, which is 27e6, acceptable in Pyton.

But worst-case overall: if we have 10 digits, and each digit appears in all positions? Then we do 10 * (100^3 * 27) = 270000000, which is 270e6. In Pyton, 270e6 iterations might be about 10-20 seconds? This might be borderline in Pyton. We need to optimize.

We can try to avoid iterating over every triple for a digit d by precomputing the minimal T for the digit d without iterating over every residue triple? 

Alternative idea for a fixed digit d:

   We want to minimize T = max(t1, t2, t3) where t1 = a + k0*M, t2 = b + k1*M, t3 = c + k2*M, and the times are distinct, and a in A_d, b in B_d, c in C_d.

   We can iterate over the possible multiples for the reels? Actually, we can consider the residues modulo M, but then the problem is that the same residue in different reels must be assigned different multiples.

   We can do: for each residue r (from 0 to M-1), we can have:

        For reel1: the possible times that give residue r: r, r+M, r+2M.
        Similarly for reel2 and reel3.

   But we are constrained to residues that are in A_d, B_d, C_d for the digit d.

   Then the problem becomes: we have three sets of numbers (for reel1: the set T1 = { r + k*M for k in {0,1,2} and r in A_d }), similarly T2 for reel2, T3 for reel3. Then we want to pick one number from each set that are distinct and minimize the maximum.

   How to do that without iterating over T1, T2, T3? The sets T1, T2, T3 are of size at most 3*|A_d|, which is at most 300.

   Then we can do:

        best_candidate = a big number
        for t1 in T1:
            for t2 in T2:
                if t2 == t1: continue
                for t3 in T3:
                    if t3 == t1 or t3 == t2: continue
                    candidate = max(t1, t2, t3)
                    best_candidate = min(best_candidate, candidate)

        Then the minimal T for digit d is best_candidate.

   Then we take min over d.

   How much time per digit d? |T1| * |T2| * |T3| = (3 * |A_d|) * (3 * |B_d|) * (3 * |C_d|) = 27 * |A_d| * |B_d| * |C_d|.

   Then total over all digits d: 27 * sum_{d} (|A_d| * |B_d| * |C_d|).

   What is the worst-case for sum_{d} (|A_d| * |B_d| * |C_d|)? 

        Note: for each reel, the sets A_d (for d) form a partition of the residues (each residue is in exactly one set). Therefore, the sum over d of |A_d| is M. Similarly for B_d and C_d.

        But we are summing the product |A_d| * |B_d| * |C_d| over d.

        By the Cauchy-Schwarz inequality, the maximum of this sum occurs when the sets are as concentrated as possible? 

        Actually, worst-case: one digit d has |A_d|=M, |B_d|=M, |C_d|=M. Then the term for that digit is M*M*M = 100^3 = 1e6, and the other digits have 0? Then total sum is 1e6.

        Alternatively, worst-case if every digit appears equally: then |A_d| = M/10 for each d. Then the sum is 10 * ( (M/10)^3 ) = (M^3)/100 = 100^3/100 = 10000.

        But worst-case overall: we have one digit d that appears in every position? Then we get 100^3 = 1000000.

        Then total operations: 27 * 1000000 = 27000000, which is 27e6, which is acceptable.

        But note: we have 10 digits? Actually, we iterate over digits d that have non-empty sets. If a digit d does not appear in one of the reels, we skip it. So worst-case we only do one digit? 

        Actually, worst-case: we have one digit that appears in all three reels in every position? Then we do one digit with |A_d|=100, |B_d|=100, |C_d|=100 -> 27 * 100 * 100 * 100 = 27000000.

        Also, if we have two digits that are common to all three reels? Then we do two digits, each with |A_d|,|B_d|,|C_d|? But note: the sets for different digits are disjoint. So the total over all digits for one reel: sum_d |A_d| = M. So the worst-case for the product sum is when one digit d has |A_d|=M, |B_d|=M, |C_d|=M, and the rest 0. Then we do 27*M^3.

        But if we have two digits? For example, digit d1 has |A_d1|=50, |B_d1|=50, |C_d1|=50, and d2 has |A_d2|=50, |B_d2|=50, |C_d2|=50? Then the sum is 50^3 + 50^3 = 2 * 125000 = 250000. Then 27 * 250000 = 6750000.

        So worst-case overall: 27 * M^3 = 27 * 100^3 = 27000000.

        This is acceptable in Pyton? 27 million iterations? In Pyton, each iteration is a few operations, so we hope it runs in 1-2 seconds.

Therefore, we can do:

   Precompute:
        sets1 = [set() for _ in range(10)]   # for digit '0' to '9'
        sets2 = [set() for _ in range(10)]
        sets3 = [set() for _ in range(10)]

   For i in range(10):
        sets1[i] = set of indices j in [0, M-1] such that S1[j] = str(i)
        similarly for sets2 and sets3.

   Then, for each digit d in range(10):
        A = sets1[d]
        B = sets2[d]
        C = sets3[d]
        if not A or not B or not C: continue

        # Build T1: for each residue in A, we have three candidate times: [r, r+M, r+2*M]
        T1 = set()
        for a in A:
            for k in range(0,3):  # k=0,1,2
                T1.add(a + k*M)
        T2 = set()
        for b in B:
            for k in range(0,3):
                T2.add(b + k*M)
        T3 = set()
        for c in C:
            for k in range(0,3):
                T3.add(c + k*M)

        # Now iterate over all t1 in T1, t2 in T2, t3 in T3 that are distinct, and compute candidate = max(t1,t2,t3)
        best_candidate_d = 10**9
        for t1 in T1:
            for t2 in T2:
                if t2 == t1:
                    continue
                for t3 in T3:
                    if t3 == t1 or t3 == t2:
                        continue
                    candidate = max(t1, t2, t3)
                    if candidate < best_candidate_d:
                        best_candidate_d = candidate
        # update global best
        if best_candidate_d < best_global:
            best_global = best_candidate_d

   If best_global is 10**9, return -1, else return best_global.

But note: the candidate times in T1, T2, T3 are at most 3*|A|, etc. The worst-case |A|=100, so |T1|=300. Then the triple loop: 300*300*300 = 27e6 per digit. Then worst-case one digit: 27e6, which is acceptable? Actually, worst-case we have one digit with 100 in each set, then 300*300*300=27e6 per digit? Then for one digit we do 27e6. But if we have two digits? Then 2 * 27e6 = 54e6, which is acceptable.

But worst-case: if we have 10 digits, each with 10 residues? Then |T1|=30 per digit. Then per digit: 30*30*30 = 27000. Then 10*27000 = 270000, which is very fast.

So the worst-case overall is when one digit has 100 residues, then 300^3 = 27e6. And 27e6 iterations in Pyton might be about 1-2 seconds? We can hope.

But note: worst-case overall might be when we have one digit that appears in all three reels in every position? Then |A|=100, |B|=100, |C|=100 -> 300^3 = 27e6. Then we do 27e6 iterations for that digit, and skip the others. So total 27e6.

We can test with M=100 and all same digit: then we have to do 300^3 = 27e6. Let me test in Pyton: 

   On my machine: 
        for a in range(300):
            for b in range(300):
                for c in range(300):
                    ... 

        This is 300*300*300 = 27e6 loops. In Pyton, a simple loop might be about 0.1 seconds per million? Then 27e6 would be 2.7 seconds? But we have to do a max and comparisons? So maybe 5-10 seconds? 

We can try to optimize by breaking early? 

   Actually, we can avoid the inner loop if we break early: we can sort the sets? Then we can iterate in a smarter way? 

   Alternatively, we can precompute the minimal candidate for each digit d without the triple loop? 

   We can do:

        candidate = 10**9
        # We want the smallest max(t1,t2,t3) for distinct t1 in T1, t2 in T2, t3 in T3.

        # We can iterate over the possible maximum value T from 0 to 3*M (which is 300) and check if there exists a triple (t1,t2,t3) with max=T and distinct.

        But note: the candidate times can be up to 3*M-1 = 300? Actually, the maximum candidate time is max( max(T1), max(T2), max(T3) ) which is (M-1)+2*M = 3*M-1 = 299 (if M=100). 

        Then we can iterate T from 0 to 300 (inclusive) and check:

            Let available1 = [ t for t in T1 if t<=T ]   -> list of times in T1 <= T
            available2 = [ t for t in T2 if t<=T ]
            available3 = [ t for t in T3 if t<=T ]

            Then we need to pick distinct t1, t2, t3 from available1, available2, available3.

            How? We can iterate over available1 and then available2 and then available3? The sizes are at most 3 per set? Actually, no: in worst-case, for a given T, the available1 might be the entire T1? which is 300. Then the triple loop would be 300*300*300=27e6 per T? Then total for T from 0 to 300: 301 * 27e6 = 8.127e9, too high.

   We can use a different approach: for a fixed T, we can use a matching algorithm? 

        We have three sets and we want to pick one from each that are distinct. This is a 3-dimensional matching? But we can do:

            for t1 in available1:
                for t2 in available2:
                    if t1 != t2:
                        for t3 in available3:
                            if t3 != t1 and t3 != t2:
                                then we have a solution for T.

        But worst-case, the sizes of available1, available2, available3 are about 300, then 300*300*300=27e6 per T, and then 300*27e6 = 8.1e9, which is too high.

   Alternatively, we can do:

        For a fixed T, we can check:

            Let A1 = available1, A2 = available2, A3 = available3.

            We can try to fix the value of the maximum? Actually, we are iterating T as the candidate maximum, so we require that the maximum is T? Not necessarily: we are iterating T and we are checking if there is a triple with maximum <= T. Actually, we are checking for the existence of any triple with all times <= T and distinct.

        We can do:

            # We want to know: are there distinct t1 in A1, t2 in A2, t3 in A3?
            We can iterate over t1 in A1:
                # then we need distinct t2 in A2 and t3 in A3 such that t2 != t1 and t3 != t1 and t2 != t3?
                # but note: we don't require the maximum to be T, just <= T.

            Actually, we can do:

                for t1 in A1:
                    # remove t1 from the other sets? 
                    A2_prime = [t for t in A2 if t != t1]
                    A3_prime = [t for t in A3 if t != t1]
                    # now we need to pick t2 from A2_prime and t3 from A3_prime such that t2 != t3?
                    # but note: we don't require them to be distinct? Actually, they are already distinct from t1, but they might be the same as each other.

                    # Actually, we require distinct triple, so we also need t2 != t3.

                    # How many distinct numbers in A2_prime and A3_prime? 

                    # We can do: if there exists t2 in A2_prime and t3 in A3_prime such that t2 != t3? 
                    # This is equivalent to: |A2_prime|>=1 and |A3_prime|>=1 and (|A2_prime|>=2 or |A3_prime|>=2 or (min(A2_prime) != min(A3_prime))? 

                    # Actually, we can check: 
                        if A2_prime and A3_prime and (len(A2_prime)>=2 or len(A3_prime)>=2 or next(iter(A2_prime)) != next(iter(A3_prime))):
                            then we have a triple.

                    But note: if there is at least one element in A2_prime and at least one element in A3_prime, then we can pick two distinct numbers? Not necessarily: if both sets have the same single element? Then we cannot. 

                    Actually, we can do:

                        if A2_prime and A3_prime:
                            # if there are two distinct numbers in A2_prime ∪ A3_prime? 
                            # Actually, we can pick one from A2_prime and one from A3_prime that are distinct? 
                            # How? We can pick the smallest element from A2_prime and if it is in A3_prime, then we can try to pick a different one? 
                            # Actually, we can always pick two distinct numbers if the union has at least two distinct numbers? But note: we are forced to pick one from each set.

                    Actually, we can pick distinct t2 and t3 if and only if there is a pair (t2, t3) with t2 in A2_prime, t3 in A3_prime, and t2 != t3. This is equivalent to: the sets A2_prime and A3_prime are not both singletons and equal? 

                    But wait: if A2_prime has {x} and A3_prime has {x}, then we cannot. 
                    If A2_prime has {x,y} and A3_prime has {x}, then we can pick y from A2_prime and x from A3_prime? -> distinct? yes, because y != x? no: y might be x? Actually, the sets are of distinct numbers? Actually, T2 and T3 are sets? Then A2_prime and A3_prime are sets? 

                    Actually, we built T1, T2, T3 as sets. Then the available sets are sets.

                    Then: 
                        if |A2_prime| + |A3_prime| > 1: 
                            then we can pick distinct t2 and t3? 
                        But no: if A2_prime = {x}, A3_prime = {x}, then |A2_prime|+|A3_prime|=2 but we cannot pick distinct.

                    Actually, we can pick distinct t2 and t3 if and only if the two sets are not exactly the same set and both of size 1? Or more simply: 
                        if there exists t2 in A2_prime and t3 in A3_prime such that t2 != t3.

                    How to check without iterating? 
                        if (len(A2_prime) >= 2) then we can pick two distinct? But we only need one distinct pair: we can pick any t2 and then pick a t3 that is not equal to t2? But note: we are not forced to avoid duplicates beyond the fixed t1? 

                    Actually, we can do:

                        if len(A2_prime) > 1 or len(A3_prime) > 1:
                            return True
                        else:
                            # both are at most 1
                            if not A2_prime or not A3_prime:
                                continue
                            # both non-empty and both size 1
                            if next(iter(A2_prime)) != next(iter(A3_prime)):
                                return True

                    But wait: if both sets have one element, and they are different, then we can pick those two.

                    Therefore, we can break as soon as we have one t1 for which there exists at least one t2 in A2_prime and at least one t3 in A3_prime with t2 != t3? 

                    Actually, we don't even care about the values? 

                    How about: 
                        if A2_prime and A3_prime:
                            if not (len(A2_prime)==1 and len(A3_prime)==1 and A2_prime == A3_prime):
                                then we have a solution for T.

                    But note: if A2_prime and A3_prime have the same single element? Then we cannot. Otherwise, we can.

                    However, what if A2_prime has {1,2} and A3_prime has {1}? Then we can pick t2=2 and t3=1? Then distinct.

                    But the condition above: if both sets are non-empty and not (both are singleton and the same element) -> then we can? Actually, no: if A2_prime has {1,2} and A3_prime has {1}, then we can pick t2=2 and t3=1 -> distinct. 

                    Therefore, the only problem is when both sets are singleton and the same element. 

                    So condition: 
                        if A2_prime and A3_prime and (len(A2_prime)>=2 or len(A3_prime)>=2 or next(iter(A2_prime)) != next(iter(A3_prime))):
                            then we have a solution.

                    But note: if one set has at least two, then we can always pick two distinct? Actually, if one set has two, then we have at least two distinct numbers in that set? But we only need one distinct pair: we can pick one element from the set with two that is different from the element in the other set? Not necessarily: what if the set with two is {x,x}? But it's a set -> no duplicates. 

                    Therefore, the condition is: 
                        if A2_prime and A3_prime and not (len(A2_prime)==1 and len(A3_prime)==1 and (list(A2_prime)[0] == list(A3_prime)[0])):
                            then we have a solution.

                    Actually, we can write:

                        if A2_prime and A3_prime:
                            if len(A2_prime) > 1 or len(A3_prime) > 1:
                                return True
                            else:
                                if A2_prime != A3_prime: 
                                    return True
                                else:
                                    # both are the same singleton set
                                    pass

                    But note: if they are both singleton and the same element, then we skip.

                    However, we can also have the case: A2_prime has one element x, A3_prime has one element y, and x != y -> then we can pick them.

                    So:

                        if A2_prime and A3_prime:
                            if len(A2_prime) == 1 and len(A3_prime)==1:
                                if next(iter(A2_prime)) != next(iter(A3_prime)):
                                    return True
                            else:
                                return True   # because if at least one set has more than one, then we can pick two distinct numbers (by picking one from the larger set that is not the singleton if the other is singleton? Actually, no: but we can always pick two distinct numbers: 
                                    # Example: A2_prime = {1,2}, A3_prime = {1} -> we can pick 2 from A2_prime and 1 from A3_prime -> distinct.
                                    # Example: A2_prime = {1,2}, A3_prime = {3} -> we can pick 1 and 3 -> distinct.
                                    # Example: A2_prime = {1,2}, A3_prime = {1,2} -> we can pick 1 from A2_prime and 2 from A3_prime -> distinct.

                    Actually, if both sets have more than one, we can always pick distinct? 

                    Therefore, the condition is: 
                        if A2_prime and A3_prime:
                            if len(A2_prime)==1 and len(A3_prime)==1 and next(iter(A2_prime)) == next(iter(A3_prime)):
                                continue
                            else:
                                return True   # we have found a triple for this T

                    But note: we are iterating t1. We break the outer loop as soon as we find one t1 for which the condition holds.

        Therefore, we can do:

            for T in range(0, 3*M+1):   # 0 to 300 inclusive
                A1 = [t for t in T1 if t <= T]
                A2 = [t for t in T2 if t <= T]
                A3 = [t for t in T3 if t <= T]
                if not A1 or not A2 or not A3: 
                    continue

                found = False
                for t1 in A1:
                    # remove t1 from A2 and A3 -> create sets A2_prime and A3_prime that exclude t1
                    A2_prime = [t for t in A2 if t != t1]
                    A3_prime = [t for t in A3 if t != t1]
                    if not A2_prime or not A3_prime:
                        continue
                    # Check if we can pick distinct t2 and t3 from A2_prime and A3_prime?
                    if len(A2_prime) == 1 and len(A3_prime)==1 and A2_prime[0]==A3_prime[0]:
                        continue
                    else:
                        found = True
                        break
                if found:
                    candidate = T
                    break   # for this digit d, we break at the smallest T

            Then update best_global = min(best_global, candidate)

        But note: it is possible that we don't break? Then no solution for this digit? But we know that there is a solution at T<=3*M? Actually, we know that by adding multiples we can get distinct times? So we should always break by T=3*M? 

        However, what if the sets T1, T2, T3 have duplicates? But we built them as sets. And then we remove t1? 

        Actually, the condition above: we break the inner loop as soon as we find one t1 for which the remaining sets A2_prime and A3_prime can yield distinct numbers? 

        But note: we require distinct triple: t1, t2, t3. We have removed t1 from A2 and A3? But what if the same time appears in two reels? Then we built T1, T2, T3 as sets independently. 

        Actually, the same time might appear in two reels? For example, if residue a and residue b are the same? Then t1 = a + k1*M and t2 = b + k2*M might be equal? But then we built T1 and T2 as sets, so they are distinct within the reel? But across reels, we might have the same time? 

        For example: 
            reel1: residue 0 -> times: 0, 100, 200
            reel2: residue 0 -> times: 0, 100, 200
            reel3: residue 0 -> times: 0,100,200

        Then at T=0: 
            A1 = [0], A2=[0], A3=[0]
            Then we try t1=0: 
                A2_prime = A2 without 0 -> empty -> skip.

            Then we don't find.

        Then T=100: 
            A1 = [0,100], A2=[0,100], A3=[0,100]
            Then we try t1=0: 
                A2_prime = [100] (removed 0), A3_prime = [100] -> then we check: both singleton and 100==100 -> skip.
            Then t1=100:
                A2_prime = [0] (removed 100), A3_prime = [0] -> same problem.

            Then T=200: 
                A1 = [0,100,200], A2=[0,100,200], A3=[0,100,200]
                t1=0: A2_prime = [100,200], A3_prime=[100,200] -> then we have: 
                    since A2_prime has two elements, then we can pick two distinct? Actually, we only need one distinct pair? But the condition: 
                        if len(A2_prime)==1 and ... -> no, so we break: found=True.

            So it returns T=200.

        But the expected minimal T for this triple (0,0,0) is 200? Yes.

        However, we might have a better triple? Actually, we are iterating T from 0 to 300, so we break at the smallest T that works.

        But note: the triple (0,100,200) has max=200. But we could also have (0,200,100) -> max=200.

        But what about (100,0,200)? max=200.

        There is no triple with max<200? 

        So the algorithm returns 200, which is correct.

        But what if we have a triple that has max=100? Is it possible? Only if we have one time 100 and the others are <=100. But we require distinct. Then we would have to assign:
            reel1: 100, reel2: 0, reel3: 100 -> then reel1 and reel3 are 100 -> duplicate -> invalid.
            reel1: 100, reel2: 100, reel3: 0 -> duplicate at 100.
            reel1: 0, reel2: 100, reel3: 100 -> duplicate at 100.

        So 200 is minimal.

        Therefore, the algorithm for a fixed digit d:

            Build T1, T2, T3 (each set of up to 300 elements)

            Then for T from 0 to 3*M:
                A1 = [t for t in T1 if t<=T]   -> list, we don't care about order.
                A2 = [t for t in T2 if t<=T]
                A3 = [t for t in T3 if t<=T]
                if any of A1, A2, A3 is empty: continue

                found = False
                for t1 in A1:
                    # remove t1 from A2 and A3: create A2_prime and A3_prime (as lists? we only care about the set properties: distinct and membership)
                    A2_prime = [t for t in A2 if t != t1]
                    A3_prime = [t for t in A3 if t != t1]
                    if not A2_prime or not A3_prime: 
                        continue
                    if len(A2_prime)==1 and len(A3_prime)==1 and A2_prime[0]==A3_prime[0]:
                        continue
                    else:
                        found = True
                        break
                if found:
                    candidate = T
                    break   # for this digit d, we break at T
            if found: 
                best_global = min(best_global, candidate)

        Then after d, if best_global is 10**9, return -1.

        But worst-case: the inner loop over t1: |A1| <= 300, and T from 0 to 300: then total 300 * 301 = 90300 per digit? And we do 10 digits? Then 903000, which is acceptable.

        However, we build A1, A2, A3 for each T? And each building is O(|T1|) which is 300. Then for each T, the cost is O(300) for building the lists and then O(|A1|) * (cost of building A2_prime and A3_prime) which is O(300) for each t1? So total per T: O(300) + O(300 * (300+300)) = O(300) + O(300*600) = O(180000) per T? Then 301 * 180000 = 54180000, which is 54e6, which is acceptable.

        But worst-case over 10 digits: 10 * 54e6 = 540e6, which is 540 million? That might be borderline in Pyton (about 10-20 seconds).

        We can optimize by precomputing for T from 0 to 300 the sets A1, A2, A3? 

            We can pre-sort T1, T2, T3 and then use pointers? 

            Alternatively, we can iterate T from 0 to 300 and update the available sets incrementally.

        But note: 54e6 per digit? Actually, worst-case we have one digit with large sets, and we do 54e6 for that digit. Then 54e6 is acceptable? 

        Actually, worst-case overall: if we have one digit, we do 54e6, which is about 5 seconds in Pyton? 

        We can do better: 

            Instead of building the lists A1, A2, A3 for each T from scratch, we can do:

                Let A1 = sorted(T1)   # size 300
                Similarly for A2 and A3.

                Then we can use three pointers to iterate T from 0 to 300, and maintain three lists for the available times? 

            But the inner loop over t1 is over the entire A1 that is <= T? 

        Alternatively, we can avoid building the entire lists for each T? 

        However, 54e6 per digit is acceptable for one digit? Then for 10 digits we do 540e6, which is too high.

        But note: worst-case we only do one digit? Because if a digit d does not appear in all reels, we skip. And if a digit appears in all reels, then we do the above. And worst-case we have one digit that appears in all reels? Then we do 54e6.

        So worst-case 54e6 per digit, and we have one digit: total 54e6, which is acceptable.

        Let me test: 54e6 iterations in Pyton: about 5 seconds? 

        But we have to build the lists: for each T, we build A1, A2, A3 by scanning the entire T1, T2, T3? Then for each T, we scan 300 for T1, 300 for T2, 300 for T3: that is 900 per T. Then 301 * 900 = 270900, which is 0.27e6, which is negligible.

        Then the inner loop: for each t1 in A1 (which is at most 300) and for each t1 we build A2_prime and A3_prime by scanning A2 and A3? That is 300+300 = 600 per t1. Then per T: 300 * 600 = 180000, and 301*180000 = 54.18e6.

        Then we do that for one digit: 54e6. 

        We can avoid building the entire lists for A2_prime and A3_prime? 

            We can precompute for each T the entire sets, and then for each t1, we can have:

                A2_prime = A2 - {t1}   # as a set, but we want to check the condition: 
                Then condition: 
                    if A2_prime and A3_prime and not (len(A2_prime)==1 and len(A3_prime)==1 and next(iter(A2_prime)) == next(iter(A3_prime)))

            Actually, we can precompute for each T the sets for A1, A2, A3 (as sets, not lists) and then:

                for t1 in A1_set:
                    A2_prime = A2_set - {t1}
                    A3_prime = A3_set - {t1}
                    if not A2_prime or not A3_prime:
                        continue
                    if len(A2_prime)==1 and len(A3_prime)==1 and next(iter(A2_prime)) == next(iter(A3_prime)):
                        continue
                    else:
                        found = True
                        break

            The cost per t1: set difference is O(|A2_set|)? Then total per T: O(|A1_set| * |A2_set|) which is 300*300 = 90000 per T? Then 301 * 90000 = 27.09e6, which is 27 million, which is better.

        How about: 
            Precomputation: 
                T1_sorted = sorted(T1)
                T2_sorted = sorted(T2)
                T3_sorted = sorted(T3)

            Then we can maintain for T from 0 to 300:

                A1_set = set of all t in T1 that are <= T. We can start T=0 and then add as T increases.
                Similarly for A2_set and A3_set.

            Then we iterate T from 0 to 300:

                if T in T1_sorted: then add to A1_set? Actually, we can do:

                    We can have three pointers that point to the next element in T1_sorted, T2_sorted, T3_sorted.

            Alternatively, we can iterate T from 0 to 300 and update the sets by checking if T is in T1, T2, or T3? 

            But we built T1 as a set? Then we can do:

                if T in T1: then add to A1_set? 

            Then per T: O(1) for updating each set.

            Then the inner loop: for t1 in A1_set: ... 

            But note: the set A1_set is changing? Then we have to iterate over the current set? 

            Actually, we can do:

                A1_set = set()
                A2_set = set()
                A3_set = set()
                best_candidate_d = a big number
                for T in range(0, 3*M+1):
                    if T in T1: 
                        A1_set.add(T)
                    if T in T2:
                        A2_set.add(T)
                    if T in T3:
                        A3_set.add(T)
                    if not A1_set or not A2_set or not A3_set:
                        continue
                    found = False
                    for t1 in A1_set:
                        A2_prime = A2_set - {t1}
                        A3_prime = A3_set - {t1}
                        if not A2_prime or not A3_prime:
                            continue
                        if len(A2_prime)==1 and len(A3_prime)==1 and next(iter(A2_prime)) == next(iter(A3_prime)):
                            continue
                        else:
                            found = True
                            break
                    if found:
                        best_candidate_d = T
                        break

                Then update best_global.

            The cost per T: 
                We iterate over A1_set (which grows) and do set differences. 

                The size of A1_set is at most the number of times we've added, which is at most the number of elements in T1 that are <= T. Since T goes to 300, then |A1_set| <= 300.

                For each t1 in A1_set, the set difference is O(|A2_set|) for A2_set - {t1}? Actually, it's O(1) because set difference for one element is constant? 

                But then we check the condition: 
                    if not A2_prime: ... -> O(1)
                    then check len and possibly the elements? 

                Then the inner loop per T: O(|A1_set|) which is <= 300.

            Then total cost per digit: O(300 * 301) = 90300.

        This is very efficient.

        Therefore, we can do for a fixed digit d:

            T1 = set( ... )   # candidate times for reel1: { a + k*M for a in residues1, k in [0,1,2] }
            T2 = set( ... )
            T3 = set( ... )

            A1_set = set()
            A2_set = set()
            A3_set = set()

            candidate = 10**9
            for T in range(0, 3*M+1):   # T from 0 to 300
                if T in T1:
                    A1_set.add(T)
                if T in T2:
                    A2_set.add(T)
                if T in T3:
                    A3_set.add(T)
                if not (A1_set and A2_set and A3_set):
                    continue
                found = False
                for t1 in A1_set:
                    # remove t1 from A2_set and A3_set
                    A2_prime = A2_set - {t1}
                    A3_prime = A3_set - {t1}
                    if not A2_prime or not A3_prime:
                        continue
                    # Check if we can pick distinct t2 in A2_prime and t3 in A3_prime? 
                    # We only need to check: if there exists a pair (t2, t3) that are distinct? 
                    # But note: we are forced to pick one from each set. The only problem is if both sets are singleton and the same element.
                    if len(A2_prime)==1 and len(A3_prime)==1 and next(iter(A2_prime)) == next(iter(A3_prime)):
                        continue
                    else:
                        found = True
                        break
                if found:
                    candidate = T
                    break

            if candidate < best_global:
                best_global = candidate

        Then after all digits, if best_global is 10**9, return -1, else best_global.

        This is O(301 * 300) per digit, which is 90300 per digit, and 10 digits: 903000, which is very fast.

        Let me test with the example: 
            Example: M=10, 
                S1 = "1937458062"
                S2 = "8124690357"
                S3 = "2385760149"

            We are looking for digit '8'? 

            For reel1: positions where '8' appears: 
                S1: "1937458062" -> at index 6 (0-indexed) -> residue 6? 
                But note: the problem says: the 7th character? 
                Actually: 
                    S1: index0: '1', index1: '9', ... index6: '0'? 
                Wait: the example says: 
                    first reel: press at 6 seconds -> ((6 mod 10)+1 = 7th character -> S1[6]? -> in 0-indexed, that's index6 -> which is '0'? 

                But we want '8'. In S1, '8' is at index 7: because the string is "1937458062": 
                    index0: '1'
                    index1: '9'
                    index2: '3'
                    index3: '7'
                    index4: '4'
                    index5: '5'
                    index6: '8'? -> wait, the example says: the first reel displays the 7th character of S1? and that is '8'. So index6 is '8'? 

                Actually: 
                    "1937458062": 
                    positions: 
                        0:1, 1:9, 2:3, 3:7, 4:4, 5:5, 6:8, 7:0, 8:6, 9:2.

                So for reel1: A_d = {6} for d='8'
                For reel2: S2 = "8124690357": 
                    '8' at index0 -> A_d = {0}
                For reel3: S3 = "2385760149": 
                    '8' at index1 -> A_d = {1}? 
                    Actually: 
                        index0:2, index1:3 -> no, wait: 
                    "2385760149": 
                        index0:2, index1:3 -> no '8'? 
                    Actually: 
                        index2:8? -> no: 
                        index0:2, index1:3, index2:8? 
                    But the example says: press the third reel at 2 seconds: (2 mod 10)=2 -> the (2+1)=3rd character -> which is the third character? which should be the character at index2? -> then it's '8'. So residue=2? 

                So A_d for reel3 = {2}

            Then T1 = {6, 16, 26}   (for k=0,1,2: 6+0*10=6, 6+1*10=16, 6+2*10=26)
            T2 = {0,10,20}
            T3 = {2,12,22}

            Now we iterate T from 0 to 30 (3*M=30):

                T=0: 
                    A1_set: empty? because 0 not in T1 -> skip.
                T=1: skip
                T=2: 
                    add 2 to A3_set? because 2 in T3 -> A3_set = {2}
                    A1_set and A2_set: A1_set is still empty -> skip.
                T=0 to T=5: skip
                T=6: 
                    add 6 to A1_set: A1_set={6}
                    A2_set: we haven't added 0? We did T=0: we should have added 0 to A2_set? 

                Actually, we do:

                    T=0: 
                        0 in T1? no -> skip
                        0 in T2? yes -> A2_set.add(0) -> A2_set={0}
                        0 in T3? no -> skip
                    Then T=0: we have A1_set={} -> skip.

                    T=1: no
                    T=2: A3_set={2}
                    T=3: no
                    T=4: no
                    T=5: no
                    T=6: 
                        6 in T1: yes -> A1_set={6}
                        6 in T2? no
                        6 in T3? no

                    Then at T=6: 
                        A1_set={6}, A2_set={0}, A3_set={2}
                        Then we iterate t1 in A1_set: only 6.
                            A2_prime = A2_set - {6} = {0} (unchanged)
                            A3_prime = {2} (unchanged)
                            Then: not (len(A2_prime)==1 and len(A3_prime)==1 and 0==2) -> false, so we use the else branch: found=True.

                    Then candidate=6.

            So we return 6.

            But the example output is 6.

        Example2: 
            Input: 
                20
                "01234567890123456789"
                "01234567890123456789"
                "01234567890123456789"

            We want to stop at the same digit? say '0'. 
                Then residues: 
                    reel1: A_d = {0}
                    reel2: B_d = {0}
                    reel3: C_d = {0}
                Then T1 = {0,20,40}
                T2 = {0,20,40}
                T3 = {0,20,40}

                We iterate T:
                    T=0: 
                        A1_set={0}, A2_set={0}, A3_set={0}
                        Then we iterate t1 in A1_set: only 0.
                            A2_prime = A2_set - {0} = {} -> skip.
                        not found.

                    T=1..19: no new additions? 
                    T=20: 
                        A1_set = {0,20}
                        A2_set = {0,20}
                        A3_set = {0,20}
                        Then we iterate t1 in A1_set: 
                            t1=0: 
                                A2_prime = {20} (removed 0) -> nonempty
                                A3_prime = {20} -> nonempty
                                but both are singleton and 20==20 -> skip.
                            t1=20:
                                A2_prime = {0} (removed 20) 
                                A3_prime = {0} -> skip (both singleton and 0==0) -> skip.

                        not found.

                    T=21..39: no change? 
                    T=40: 
                        A1_set = {0,20,40}
                        A2_set = {0,20,40}
                        A3_set = {0,20,40}
                        t1=0: 
                            A2_prime = {20,40}, A3_prime = {20,40} -> then condition: 
                                not (len(A2_prime)==1 and len(A3_prime)==1 and ...) -> so we break: found=True.

                        candidate = 40? 

                But the expected output is 20? 

            Why? The example output is 20.

            What went wrong? 

            We have a solution at T=20: 
                We can assign: 
                    t1 = 20, t2 = 0, t3 = 40? -> but max=40? 
                Actually, we must stop all by time 20? 

            But at T=20, we have the available times: 
                A1_set = {0,20}
                A2_set = {0,20}
                A3_set = {0,20}

            How about: 
                t1 = 20, t2 = 0, t3 = 20? -> then t1 and t3 are the same -> duplicate? 
                or t1=20, t2=20, t3=0 -> duplicate.

            Actually, we must assign distinct times. 

            What is the minimal T? 
                We have to assign:
                    one of the reels: 0, then the next: 20, then the third must be 40? so T=40.

            But the example output is 20? 

            Wait, the example says: 
                Output: 20

            And the example input: 
                20
                01234567890123456789
                01234567890123456789
                01234567890123456789

            How to achieve 20? 
                Press the first reel at time 0: stops at '0'
                Press the second reel at time 20: 
                    t=20: 20 mod 20 = 0 -> stops at '0'
                Press the third reel at time 20? -> not allowed: we can only press one button per time.

            So we cannot press the second and third at the same time.

            How about:
                Press the first at 0: time0 -> '0'
                Press the second at 20: time20 -> '0'
                Press the third at 40: time40 -> '0'

            Then max time=40.

            But the example output is 20.

            How to achieve 20? 
                The problem: we must stop all by time 20? 

            Note: we can press the buttons at times that are not necessarily distinct? But the problem says: distinct times. 

            How about:
                Press the first reel at time 0 -> shows S1[0] = '0'
                Press the second reel at time 20 -> shows S2[0] = '0'
                Press

## Final Solution

