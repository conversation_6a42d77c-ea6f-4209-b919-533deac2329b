# Solution for 958_C2.md

Generated at: 2025-07-22 06:11:01
Model: deepseek-reasoner

## Reasoning Process

We are given: N, k, p, and an array A of N integers.
We need to split the array into k non-overlapping contiguous parts (each at least one element) to maximize the total score S, 
where the score of a part is the sum of its elements modulo p.

Important observations:
1. The modulo operation: note that (a mod p) is in the range [0, p-1]. However, because the numbers are positive, the mod of a sum of a segment might be less than the actual sum. But note: the mod operation can sometimes reduce the sum. However, the problem is asking for the maximum total score.

2. Constraints: 
   - N up to 20,000, k up to 50, p up to 100.
   - This suggests that we can design a dynamic programming solution with state as [position][number of parts] and the state might involve the modulo of the current segment? But note: the total score is the sum of the mods of each segment.

However, the challenge: 
   The score of a segment is (sum(segment) mod p. But note that if the sum of a segment is s, then the mod is s % p, which is s if s < p, but if s>=p, then it is s mod p (which is less than p). 

But note: we are summing the mod values of the segments. 

We cannot simply use the entire array modulo p because the segments are contiguous and we are free to choose the splits.

Let's denote:
   Let S = s1 + s2 + ... + sk, where each si is the mod of the sum of the i-th segment.

But note: the entire array is fixed, so the total sum of the array is fixed. However, the mods break the additivity: 
   (a mod p) + (b mod p) might not equal (a+b) mod p, and in fact it can be less than (a+b) mod p? Actually, we have:
      (a+b) mod p = (a mod p + b mod p) mod p   but that is for a single mod. However, we are not modding the total but each segment individually.

So the problem is: we break the array into k contiguous segments, and then we get the sum of (segment_sum mod p) for each segment.

How to approach?

We can try to use dynamic programming:

Let dp[i][j] = maximum total score we can achieve by considering the prefix ending at i (i.e., the first i elements) and having split into j segments.

But note: the score of a segment depends on the entire segment's sum. However, the modulo operation breaks linearity.

We have:
   dp[i][j] = max_{t from j-1 to i-1} { dp[t][j-1] + ( (sum from t+1 to i) mod p ) }

But note: the state i (which can be up to 20,000) and j (up to 50) would lead to a state of 20,000 * 50 = 1e6 states. For each state, we need to iterate t from j-1 to i-1, which in worst-case is about 20,000 per state -> total 1e6 * 20,000 = 20e9 operations, which is too slow.

We need to optimize the transition.

Let prefix[i] = A[0] + A[1] + ... + A[i-1] (with prefix[0]=0, and then prefix[i] for i from 1 to N).

Then the sum from t+1 to i is: prefix[i] - prefix[t].

So: 
   dp[i][j] = max_{t in [j-1, i-1]} { dp[t][j-1] + ( (prefix[i] - prefix[t]) mod p ) }

But note: the modulo operation is not linear. However, we can note that:

   (prefix[i] - prefix[t]) mod p = ( (prefix[i] mod p) - (prefix[t] mod p) ) mod p? 
   But subtraction modulo: actually, (a - b) mod p = (a mod p - b mod p) mod p, but if negative we add p.

However, that is not the same as (prefix[i] - prefix[t]) mod p. Actually, it is the same.

But note: the expression (prefix[i] - prefix[t]) mod p can be written as:
   x = (prefix[i] - prefix[t]) % p
   which is equivalent to ( (prefix[i] % p) - (prefix[t] % p) ) % p? Not exactly: because modulo is distributive over addition and subtraction in the ring. Actually:

   (a - b) mod p = (a mod p - b mod p) mod p, but if negative we add p to make it nonnegative? Actually, in programming, the modulus operator for negative numbers? But our prefix[i] and prefix[t] are positive? 

But note: prefix[i] >= prefix[t] because the numbers are positive. So (prefix[i] - prefix[t]) is nonnegative. However, when we take mod, we have:

   (prefix[i] - prefix[t]) mod p = ( (prefix[i] mod p) - (prefix[t] mod p) ) mod p? 
   Actually, no: because if prefix[i] mod p < prefix[t] mod p, then we get a negative number? Then we have to add p.

But we can write:
   (a - b) mod p = (a mod p - b mod p + p) % p   [to handle negatives]

But note: the value we are taking mod of is nonnegative, but when we break it into mods, we might have:

   a = p * q1 + r1, b = p * q2 + r2, then a-b = p*(q1-q2) + (r1-r2). Then mod p is (r1 - r2) mod p = (r1 - r2 + p) % p.

So: 
   (prefix[i] - prefix[t]) % p = ( (prefix[i] % p) - (prefix[t] % p) + p) % p   if we are doing nonnegative modulus.

But note: the modulus operation in the problem is defined as the nonnegative remainder. 

However, we can precompute:
   Let r[i] = prefix[i] % p.

Then: 
   (prefix[i] - prefix[t]) % p = ( (r[i] - r[t]) % p ) 
   but if r[i] >= r[t], then it's r[i]-r[t]; if r[i] < r[t], then it's r[i]-r[t]+p.

So: 
   = (r[i] - r[t] + p) % p   -> which is the same as: (r[i] - r[t] + p) % p, but note that (r[i]-r[t]+p) is in [0, 2p-1], so mod p is: if r[i] >= r[t] then r[i]-r[t], else r[i]-r[t]+p.

Alternatively, we can write: 
   value = (r[i] - r[t]) % p
   but this in Python would be negative if r[i] < r[t]. Actually, we want nonnegative mod. So we can use:
   value = (r[i] - r[t]) % p   -> but this is computed as (r[i] - r[t] + p) % p to make nonnegative? Actually, in Python, the modulus of a negative is nonnegative? Yes, because in Python, the modulus result is always nonnegative.

But let me check: 
   (3 - 4) % 10 -> (-1) % 10 = 9? 
   But we want (3-4) mod 10? Actually, (3-4) = -1, and then mod 10 should be 9? However, in our case, the actual value (prefix[i]-prefix[t]) is nonnegative, so we are not supposed to get negative? 

But wait: prefix[i] >= prefix[t] so r[i] and r[t] are mods, but prefix[i] might be less than prefix[t] modulo p? Actually, no: because prefix[i] >= prefix[t] so the actual value prefix[i]-prefix[t] is nonnegative. However, when we break into mods, we have:

   prefix[i] = a * p + r_i, prefix[t] = b * p + r_t, then prefix[i]-prefix[t] = (a-b)*p + (r_i - r_t). 
   Then mod p is (r_i - r_t) mod p, but note that if r_i < r_t, then we have (r_i - r_t) is negative, so we add p to get a positive number? Actually, the entire expression is nonnegative, so the mod should be (r_i - r_t + p) % p? 

But note: the value (prefix[i]-prefix[t]) is nonnegative, so:

   (prefix[i]-prefix[t]) mod p = (r_i - r_t) mod p   -> but when r_i >= r_t, then it's r_i - r_t, and when r_i < r_t, it's r_i - r_t + p.

So we can write:
   value = (r[i] - r[t]) if r[i] >= r[t] else (r[i] - r[t] + p)

But note: that is the same as (r[i] - r[t] + p) % p? Actually, if we do (r[i]-r[t]) % p, we get the same as above? 
   Example: r[i]=3, r[t]=4: (3-4) % p = -1 % p = p-1, but actually we want p-1? 
   But wait: (prefix[i]-prefix[t]) mod p = (actual value) mod p. The actual value is nonnegative. However, the mod operation doesn't care about the intermediate representation. 

So we can compute:
   value = (prefix[i] - prefix[t]) % p   -> which is the same as (r[i] - r[t]) % p? 
   Actually, no: because (a-b) mod p is not the same as (a mod p - b mod p) mod p? 
   But algebraically: 
        (a-b) mod p = ( (a mod p) - (b mod p) ) mod p   if we define mod as the nonnegative remainder? 
        Actually, it is: because (a mod p) = a - p*floor(a/p), similarly for b. Then:
        (a-b) mod p = (a - b) - p * floor((a-b)/p)
        and (a mod p - b mod p) = (a - p*floor(a/p)) - (b - p*floor(b/p)) = (a-b) - p*(floor(a/p)-floor(b/p))
        Then mod p: we have to subtract p*floor( (a mod p - b mod p)/p )? 

   Actually, the property: 
        (a - b) mod p = ( (a mod p) - (b mod p) + p ) % p   [if we want a nonnegative result]

   But note: our a and b are nonnegative? Yes. And a>=b? Yes (because prefix[i]>=prefix[t]). 
   However, even though a>=b, the mods might be such that a mod p < b mod p. Then (a mod p - b mod p) is negative. 

   Therefore, we can compute:
        value = ( (r[i] - r[t]) + p ) % p   -> but wait, if r[i] >= r[t], then (r[i]-r[t]) is nonnegative and less than p? Not necessarily: if r[i] is 10 and r[t] is 1, then 10-1=9, which is less than p? Actually, r[i] and r[t] are in [0, p-1], so the difference is in [-p+1, p-1]. So:

        We can define: 
           value = (r[i] - r[t]) % p   -> in Python, this will be nonnegative? 
           But if negative, we get a positive by adding p? Actually, the modulus operator in Python for a negative x: 
                x % p = x + p * ceil(|x|/p) ? 

        Actually, we can rely on: 
           value = (prefix[i] - prefix[t]) % p   -> which we can compute directly without mods? But that requires O(1) per transition? But we have the prefix sum array, so we can precompute the entire prefix array and then the difference is O(1). 

        However, the issue: we are iterating over t for each i, and if we do (prefix[i]-prefix[t]) % p, that is O(1). But we have O(N*k) states and each transition O(N) -> O(N^2 * k) which is 20000^2 * 50 = 20e6 * 50 = 1e9, which might be borderline in Pyton? Actually 1e9 operations in Python might take 10-20 seconds, but the constraints say N=20000, so O(N^2 * k) = 20e6 * 50 = 1e9, which is acceptable in C++ but in Python? We should avoid.

So we need to optimize the transition.

We have:
   dp[i][j] = max_{t from j-1 to i-1} { dp[t][j-1] + ( (prefix[i]-prefix[t]) % p ) }

We can rewrite the term: 
   (prefix[i]-prefix[t]) % p = ( (prefix[i] % p) - (prefix[t] % p) ) % p   -> but wait, we saw that is not true? 

But note: we can express it as:
   = (prefix[i] - prefix[t]) % p 
   = ( (prefix[i] mod p) - (prefix[t] mod p) ) mod p   [with the understanding that the modulus operation for subtraction in modular arithmetic is defined as above] -> but actually, we can write:

   = (r[i] - r[t]) % p   [which in Python is nonnegative]

But note: the expression (r[i] - r[t]) % p is the same as (prefix[i]-prefix[t]) % p? 

Yes, because:
   prefix[i] = a*p + r[i], prefix[t] = b*p + r[t], then:
        prefix[i]-prefix[t] = (a-b)*p + (r[i]-r[t])
        Then mod p: 
            = (r[i]-r[t]) mod p   [because (a-b)*p is divisible by p]

So we can write:
   value = (r[i] - r[t]) % p   -> but note: in Python, if r[i] < r[t], then (r[i]-r[t]) is negative, and then (r[i]-r[t]) % p = r[i]-r[t]+p.

Therefore, we have:
   dp[i][j] = max_{t from j-1 to i-1} { dp[t][j-1] + ( (r[i] - r[t]) % p ) }

Now, how to optimize the transition? 

We note that j is small (<=50) and i goes up to 20000. Also, p is small (<=100). 

We can break the expression:

   term = (r[i] - r[t]) % p = 
        if r[i] >= r[t]: r[i]-r[t]
        else: r[i]-r[t]+p

So: 
   = (r[i] - r[t] + p * I_{r[i] < r[t]} ) 

But note: we cannot avoid the condition? 

Alternatively, we can precompute for each j (the segment count) and for each residue class of r[t] (which is from 0 to p-1) the best dp[t][j-1] for a given residue. 

Specifically, for fixed j and fixed i, we want:

   dp[i][j] = max_{t from j-1 to i-1} { dp[t][j-1] + ( (r[i]-r[t]) % p ) }

We can split the max into two cases:

   Case 1: r[t] <= r[i] -> then the term is r[i] - r[t]
   Case 2: r[t] > r[i]  -> then the term is r[i] - r[t] + p

So:
   dp[i][j] = max( 
        max_{t: j-1<=t<=i-1 and r[t] <= r[i]} { dp[t][j-1] + r[i] - r[t] },
        max_{t: j-1<=t<=i-1 and r[t] > r[i]}  { dp[t][j-1] + r[i] - r[t] + p }
   )

We can maintain for the current j (i.e., for the j-1 layer) a data structure that stores for each residue value (from 0 to p-1) the best value of (dp[t][j-1] - r[t]) for a given residue. Then:

   For residue classes <= r[i]: we want max_{res in [0, r[i]]} { (dp[t][j-1] - r[t]) } + r[i]
   For residue classes > r[i]: we want max_{res in [r[i]+1, p-1]} { (dp[t][j-1] - r[t]) } + r[i] + p

But note: the residue of a prefix t is r[t] = prefix[t] % p.

So we can maintain for the j-1 layer an array best[res] for res in [0, p-1] that holds the maximum value of (dp[t][j-1] - r[t]) for all t that we have processed so far (with residue res). Then when we process a new i (for the j-th segment), we can:

   For the current residue r_i = r[i] = prefix[i] % p, we do:

   candidate1 = (max_{res=0}^{r_i} best[res]) + r_i
   candidate2 = (max_{res=r_i+1}^{p-1} best[res]) + r_i + p

   Then dp[i][j] = max(candidate1, candidate2)

But note: we are iterating i from 0 to N? Actually, we have to consider the entire prefix. However, we must ensure that we only consider t from the j-1 segment that are at least j-1 (which we will handle by iterating i and updating the data structure as we go).

However, note: we are iterating i from 0 to N? Actually, we cannot use t>=j-1: we start at t=j-1.

But in the state: for j segments, we must have at least j elements. So we iterate i from j to N (for the j-th segment, we need at least j elements). 

We can do:

   dp[0][0] = 0? But we have to split into k parts, so we start with no elements? Actually, we have:

   Let dp[i][j] = maximum total score for the first i elements split into j parts.

   Then we require that j>=1 and i>=j.

   We initialize: 
        dp[0][0] = 0, and then for j=0 and i>0: not valid? Actually, we need to split into k parts, so j from 1 to k.

   But note: we are going to build from j=1 to j=k.

   How about j=1? 
        dp[i][1] = (prefix[i] - prefix[0]) % p = (prefix[i]) % p.

   Then for j>=2, we iterate.

   Steps:

        Precompute prefix[0..N] and r[0..N] = [prefix[i] % p for i in range(N+1)]

        Initialize dp = a 2D array of size (N+1) x (k+1), with -infinity? But we know that we start at j=0: 
            dp[0][0] = 0, and for i>0, dp[i][0] is invalid -> we set to -10**18.

        But note: we are going to iterate j from 1 to k.

        For j from 1 to k:
            We will maintain a data structure (an array of length p) for the j-1 layer: best[res] = maximum value of (dp[t][j-1] - r[t]) for all t that we have processed so far (with residue res). But note: t must be at least j-1 (because we need j-1 segments and at least j-1 elements). 

            We will iterate i from j to N (because we need at least j elements for j segments).

            For each i (from j to N):
                Consider t from j-1 to i-1? But we are going to update the data structure as we go? Actually, we can update the data structure for t = i-1 after we have computed dp[i-1][j-1]? 

            However, note: in the inner loop for i (for the j-th segment), we want to consider all t from j-1 to i-1. 

            We can do:

                Initialize for the current j: 
                    We start with an array best of size p, initialized to -10**18 (a very small number) for each residue.

                Then we start i from j to N:

                    But note: we must have computed dp[t][j-1] for t from j-1 to i-1. 

                    We can update the data structure as we go: when we compute dp[t][j-1] for a t, we update best[r[t]] = max(best[r[t]], dp[t][j-1] - r[t])

                    Then for a fixed i, we have already updated all t from j-1 to i-1? 

                    How? We iterate i from j to N. For a fixed j, we start at i=j. At this point, we should have updated t from j-1 to j-1? Then for i=j, we only have t=j-1? Then for i=j+1, we update t=j (if we update after we compute the state?).

                Actually, we can update the data structure as we iterate i. Specifically:

                    For j from 1 to k:
                         Initialize best = [-10**18] * p   (for residues 0 to p-1)
                         Then we start i from j: 
                             But at i=j, we haven't updated any t from j-1? We need to include t=j-1. 

                         So before the inner loop for i, we can update the data structure for t = j-1? 

                    We can iterate t from j-1 to N-1? Actually, we are going to update the data structure for t as we go. 

                    Specifically:

                         for j in range(1, k+1):
                             best = [-10**18] * p
                             # we need to include all t from j-1 to ... for the j-1 layer. But note: we are going to iterate i from j to N, and at each i we update the data structure with t = i-1? 

                             Actually, when we compute dp[i][j], we have already computed dp[t][j-1] for all t from j-1 to i-1? 

                             And we can update the data structure at the end of each i in the inner loop? 

                         Steps:

                             for j in range(1, k+1):
                                 # initialize the best array for this j
                                 best = [-10**18] * p
                                 # we need to update the data structure for the t = j-1? But we have computed dp[j-1][j-1]? 
                                 # Actually, we start with i=j: then we want to use t from j-1 to j-1. So we must have computed dp[j-1][j-1] and then update the data structure for t=j-1.

                                 # But how do we get dp[j-1][j-1]? 
                                 # We can precompute for j=1: 
                                 #   for i from 1 to N: dp[i][1] = (prefix[i]) % p

                                 # Then for j>=2, we start at i=j: and we have t from j-1 to i-1 = j-1 to j-1 (only one t: t=j-1). 

                                 # So we can update the data structure at the beginning for t = j-1? 

                                 # Actually, we can do:

                                 #   for i from j to N:
                                 #       if i == j:
                                 #           # then we need to update the data structure for t = j-1 (which is the only t in the range [j-1, j-1])
                                 #           t0 = j-1
                                 #           residue0 = r[t0]   # which is prefix[t0] % p
                                 #           candidate = dp[t0][j-1] - r[t0]
                                 #           best[residue0] = max(best[residue0], candidate)
                                 #       then compute dp[i][j] = max( ... ) as above.

                                 # Then for the next i (i=j+1), we update the data structure for t = j (because we have computed dp[j][j-1] in the previous j-1 layer? But wait: we are in j-th segment, so we are using the j-1 layer. We have computed dp[j][j-1] already? Yes, because we iterate j from 1 to k, and for each j we iterate i from j to N. 

                                 # Actually, we can update the data structure for t = i-1 at the end of each i? 

                                 # Specifically:

                                 #   for j in [1, k]:
                                 #       best = [-10**18]*p
                                 #       # at the start, we update for t = j-1? But we haven't processed i=j? 
                                 #       # Instead, we can update the data structure as we iterate i from j to N, and at each step i, we update the data structure with the state t = i-1 (because we are going to use it for the next i).

                                 #   But note: for state i, we want to use t from j-1 to i-1. So when we are at i, we have not included t=i-1? Then we should update the data structure with t=i-1 at the beginning of the loop? But we haven't computed dp[i-1][j-1]? 

                                 # Actually, we are iterating i from j to N. The state dp[i-1][j-1] was computed in the previous step (for j-1) and is available. 

                                 # Therefore, we can do:

                                 #   for j in range(1, k+1):
                                 #       best = [-10**18]*p
                                 #       # We update the data structure for t from j-1 to ...? Actually, we start at i=j, so we need to include t=j-1. 
                                 #       # But we can update the data structure for t=j-1 at the beginning? 
                                 #       # Let t0 = j-1 -> we have dp[t0][j-1] computed? 
                                 #       residue0 = r[t0]
                                 #       value0 = dp[t0][j-1] - r[t0]
                                 #       best[residue0] = max(best[residue0], value0)

                                 #       Then for i from j to N:
                                 #           if i > j: then we update the data structure for t = i-1? Because for the next i, we want to include t=i-1? But note: for the current i, we are considering t from j-1 to i-1. And we have updated t=j-1 at the beginning, then for i=j, we use that. For i=j+1, we need to update t=j? 

                                 #       Actually, for i=j: we have updated only t=j-1. Then we compute dp[j][j]. Then we move to i=j+1: we update the data structure for t=j (which is the state we just computed for the j-1 layer? But wait: we are in the j-th segment, and we are using the j-1 layer states that were computed in the previous j-1 loop. 

                                 #       Therefore, we can update the data structure for t = i-1 at the end of the inner loop for i? 

                                 #       Specifically:

                                 #           for j in [1, k]:
                                 #               best = [-10**18]*p
                                 #               # update for t = j-1: 
                                 #               t0 = j-1
                                 #               update best[r[t0]] with (dp[t0][j-1] - r[t0])
                                 #               for i from j to N:
                                 #                   # Now, the data structure currently contains all t from j-1 to i-1? 
                                 #                   # Actually, at i=j, we have only t0=j-1. 
                                 #                   r_i = r[i]
                                 #                   candidate1 = (query_max(0, r_i) + r_i   if there is any in [0, r_i]
                                 #                   candidate2 = (query_max(r_i+1, p-1) + r_i + p   if there is any in [r_i+1, p-1]
                                 #                   dp[i][j] = max(candidate1, candidate2)
                                 #                   # Then, after processing i, we update the data structure for t = i? 
                                 #                   # But note: the next state i+1 will need t from j-1 to i, so we need to add t=i. 
                                 #                   # However, we have not computed dp[i][j-1]? Actually, we are in the j-th segment, and we are building j from j-1. We are only using j-1 layer. So we are going to update the data structure for the j-1 layer: we want to include the state (i) for the j-1 layer? 

                                 #                   But note: we are computing dp[i][j] now. The state we are going to use for the next j is for the j-th segment. But the j-1 layer states are fixed: they were computed in the previous j-1 loop. 

                                 #                   Actually, the state t = i is for the j-1 layer? But we are in the j-th segment and we are building j. The state for the j-1 layer at index i has already been computed? 

                                 #                   How do we get dp[i][j-1]? 
                                 #                   We are iterating j from 1 to k. For a fixed j, we are going to use the entire j-1 layer (which we computed in the previous j-1 loop). 

                                 #                   Therefore, we can update the data structure for t = i (for the j-1 layer) at the end of the inner loop? 

                                 #                   But note: we are at the j-th segment and we are computing the j-th segment states. We are not going to use the state for the j-1 layer at index i in the same j loop? Actually, we are going to use it for the next i? 

                                 #                   Specifically, when we go to i+1, we want to include t=i. 

                                 #                   So after we compute dp[i][j], we update:
                                 #                         residue_i = r[i]
                                 #                         value = dp[i][j-1] - r[i]   # note: we use the j-1 layer for state i? 
                                 #                         best[residue_i] = max(best[residue_i], value)

                                 #                   However, wait: we are building the j-th segment. The state for the j-1 layer at index i is dp[i][j-1] and we computed that in the previous j-1 loop. So we can update the data structure at any time? 

                                 #                   Actually, we can update the data structure for t = i (for the j-1 layer) as soon as we start the j-th segment? But we haven't started the j-th segment? 

                                 #                   But we are iterating i from j to N. We want to include all t from j-1 to i-1. So when we are at i, we have not included t=i? Then after we compute the state for i, we then update the data structure for t=i so that for the next i+1, we have it. 

                                 #                   But note: for the next state i+1, we need t from j-1 to i. 

                                 #                   Therefore, we update the data structure at the end of the inner loop (for i) for t = i (for the j-1 layer).

                                 #           However, we haven't computed dp[i][j-1] for i? Actually, we computed the entire j-1 layer in the previous j loop. So we have all the values. 

                                 #           But note: in the j-th segment loop, we are iterating i from j to N. The j-1 layer has been computed for all i from j-1 to N? 

                                 #           So we can update the data structure for t = i at the end of the inner loop.

            Therefore, algorithm:

                Precompute prefix[0..N] and r[0..N] = [prefix[i] % p for i in range(N+1)]

                Initialize dp = 2D array of size (N+1) x (k+1), with -10**18 for uncomputed states.

                # Base: 
                dp[0][0] = 0   # but we don't use j=0 for k>=1? 

                # For j=0: we set dp[i][0] = -10**18 for i>=1? Actually, we don't use j=0 for k>=1.

                # Instead, we can do:

                #   for j=1: 
                #       for i from 1 to N: 
                #           dp[i][1] = prefix[i] % p

                # Then for j from 2 to k:
                #       ... 

                But note: we have to cover j=1 to k.

                However, we can do:

                    for i in range(1, N+1):
                        dp[i][1] = r[i]   # because prefix[i] % p = r[i]

                Then for j from 2 to k:
                    best = [-10**18] * p   # for residues 0 to p-1
                    # We start by updating the data structure for t = j-1: 
                    #   t = j-1: we have state (j-1) for the j-1 layer? Actually, we need to update the state for the j-1 layer at index t = j-1? 
                    #   But note: we are in j>=2, so j-1>=1, and we have computed dp[j-1][j-1]? 
                    #   However, we have computed dp[i][j-1] only for i>=j-1? 

                    # Actually, we haven't computed dp[j-1][j-1] for j>=2? 
                    #   For j=2: we need dp[j-1][1] = dp[1][1] -> which we computed in j=1: yes.

                    # So:
                    t0 = j-1
                    # Update best: residue = r[t0], value = dp[t0][j-1] - r[t0]
                    best[r[t0]] = max(best[r[t0]], dp[t0][j-1] - r[t0])

                    for i from j to N:
                        # Now, we want to compute dp[i][j]:
                        #   We have the data structure that contains t from j-1 to i-1? Actually, we have updated t0 = j-1 at the beginning, and then for i from j to N, we will update at the end of each step for t = i (for the j-1 layer). But at the current i, we have only updated for t from j-1 to i-1? 

                        #   Actually, we updated at the beginning for t0=j-1, and then for i from j to N:
                        #       at i=j: we update the data structure for t=j? -> no, we haven't. But we haven't processed any i>j. 
                        #   So at i=j, the data structure has only t0 = j-1. Then we compute dp[j][j] from t in [j-1] only.

                        #   Then after i=j, we update the data structure for t = j? 

                        #   Therefore, we do:

                        r_i = r[i]
                        # We need to get:
                        #   candidate1 = max_{res in [0, r_i]} (best[res]) + r_i   [if any exists, otherwise -inf]
                        #   candidate2 = max_{res in [r_i+1, p-1]} (best[res]) + r_i + p

                        candidate1 = -10**18
                        candidate2 = -10**18

                        # We can do a linear scan over the residue array? Since p is only 100, we can do:

                        #   candidate1 = max(best[0:r_i+1]) 
                        #   candidate2 = max(best[r_i+1:p])

                        # Then:
                        candidate1 = max(best[0:r_i+1]) 
                        if candidate1 != -10**18:
                            candidate1 += r_i
                        candidate2 = max(best[r_i+1:p])
                        if candidate2 != -10**18:
                            candidate2 += r_i + p

                        dp[i][j] = max(candidate1, candidate2)

                        # Then update the data structure for the j-1 layer at index i: 
                        #   We are going to use this state for the next i's? 
                        residue_i = r[i]   # but note: we are using the j-1 layer state at index i: dp[i][j-1] 
                        # We update best[residue_i] = max(best[residue_i], dp[i][j-1] - residue_i)

                    # End for i

                Then the answer is max_{i from k to N} dp[i][k]? 
                Actually, we must use exactly k segments and cover the entire array? The problem says: split the whole array. So we require i=N? 

                But note: the problem says: split the sequence A into k non-overlapping contiguous parts. So we must use the entire array? Yes, because we are splitting the whole sequence.

                Therefore, the answer is dp[N][k]? 

                However, note: our prefix array: 
                    prefix[0] = 0
                    prefix[1] = A[0]
                    ... 
                    prefix[N] = A[0]+...+A[N-1]

                But our array A has N elements, and we are indexing from 0 to N-1. 

                In our dp state: 
                    dp[i][j] = maximum total score for the first i elements? 
                    But the first i elements are A[0] to A[i-1]. 

                Then if we use i = N, that covers the entire array.

                So the answer is dp[N][k].

            However, let me check the example:

                Example 1: N=4, k=3, p=10, A = [3,4,7,2]

                prefix[0]=0
                prefix[1]=3
                prefix[2]=3+4=7
                prefix[3]=14
                prefix[4]=16

                r = [0, 3, 7, 4, 6]   -> because 0%10=0, 3%10=3, 7%10=7, 14%10=4, 16%10=6.

                j=1: 
                    dp[1][1] = r[1] = 3
                    dp[2][1] = r[2] = 7
                    dp[3][1] = r[3] = 4
                    dp[4][1] = r[4] = 6

                j=2:
                    Initialize best = [-10**18]*10
                    t0 = j-1 = 2-1 = 1 -> update best[ r[1] ] = best[3] = max(best[3], dp[1][1]-3) = 3-3=0.

                    Then for i from 2 to 4:

                        i=2: 
                            r_i = r[2] = 7
                            candidate1: residues [0,7]: the best in [0,7] is 0 (at residue 3) -> candidate1 = 0+7 = 7
                            candidate2: residues [8,9]: max is -10**18 -> skip -> candidate2 = -10**18
                            dp[2][2] = 7

                            Then update: for t=2: residue = r[2]=7, value = dp[2][1] - 7 = 7-7 = 0 -> update best[7]=max(best[7],0) -> now best[7]=0.

                        i=3:
                            r_i = r[3]=4
                            candidate1: residues [0,4]: the best is 0 (at residue 3 and 7) -> 0+4=4
                            candidate2: residues [5,9]: best[7]=0 -> 0+4+10 = 14
                            dp[3][2]=14

                            Then update: for t=3: residue = r[3]=4, value = dp[3][1] - 4 = 4-4=0 -> update best[4]=max(best[4],0)=0.

                        i=4:
                            r_i = r[4]=6
                            candidate1: residues [0,6]: best in [0,6] -> residues 3,4,? 
                                residue3: 0, residue4:0, residue0: -10**18? -> max=0 -> 0+6=6
                            candidate2: residues [7,9]: residue7:0 -> 0+6+10=16
                            dp[4][2]=16

                j=3:
                    Initialize best = [-10**18]*10
                    t0 = j-1 = 3-1=2 -> update best[ r[2] ] = best[7] = dp[2][2] - 7 = 7 - 7 = 0.

                    Then for i from 3 to 4:

                        i=3:
                            r_i = r[3]=4
                            candidate1: residues [0,4]: max in [0,4] -> we have only updated residue7? no, we only updated residue7 for t0=2. Then we have best[7]=0, but 7 is not in [0,4] -> candidate1 = -10**18
                            candidate2: residues [5,9]: residue7:0 -> 0+4+10=14
                            dp[3][3]=14

                            Then update: for t=3: residue = r[3]=4, value = dp[3][2] - 4 = 14-4 = 10 -> update best[4]=10

                        i=4:
                            r_i = r[4]=6
                            candidate1: residues [0,6]: best[4]=10 -> 10+6=16
                            candidate2: residues [7,9]: best[7]=0 -> 0+6+10=16
                            dp[4][3]=16

                Then answer = dp[4][3]=16 -> matches.

            But note: in j=3, for i=4: we got 16.

            However, the example says the answer is 16.

            Example 2: 
                Input: 10 5 12
                       16 3 24 13 9 8 7 5 12 12

                We'll skip the detailed calculation because it is long, but we can code and test.

        Time complexity: O(k * N * p) -> but wait: for each j and for each i, we do two linear scans over the best array of length p (which is 100). So total O(k * N * p) = 50 * 20000 * 100 = 100e6, which is acceptable in Python? 

        Actually, 100e6 operations in Python might take a few seconds, but we can try to optimize the linear scan by maintaining segment trees or prefix/suffix arrays? Since p is small (100) we can precompute for the entire best array the prefix maximum and suffix maximum for the entire array? 

        Alternatively, we can maintain two arrays for the best array: 
            prefix_max[0] = best[0]
            prefix_max[i] = max(prefix_max[i-1], best[i]) for i from 1 to p-1.

            suffix_max[p-1] = best[p-1]
            suffix_max[i] = max(suffix_max[i+1], best[i]) for i from p-2 downto 0.

        Then for a given r_i, we can do:

            candidate1 = prefix_max[r_i]   # maximum in [0, r_i]
            candidate2 = suffix_max[r_i+1] if r_i+1 <= p-1 else -10**18   # maximum in [r_i+1, p-1]

        Then update:

            dp[i][j] = max( candidate1 + r_i, candidate2 + r_i + p )

        Then we update the best array at position r[i] with (dp[i][j-1] - r[i]) and then update the prefix_max and suffix_max arrays? 

        But note: we update one element at a time. We can update the prefix_max and suffix_max in O(p) per update? Then total per j: O(N * p) = 20000 * 100 = 2e6, which is acceptable? 

        Alternatively, since p is small (100), we can recompute the entire prefix_max and suffix_max arrays after each update? That would be O(p) per update -> total per j: O(N * p) = 20000*100 = 2e6, which is acceptable.

        Steps for j in [1, k]:
            best = [-10**18]*p
            # Build prefix_max and suffix_max arrays? 
            prefix_max = [-10**18]*p
            suffix_max = [-10**18]*p

            # Update t0 = j-1: 
            t0 = j-1
            residue0 = r[t0]
            value0 = dp[t0][j-1] - r[t0]
            if value0 > best[residue0]:
                best[residue0] = value0
                # update prefix_max and suffix_max for the entire array? 
                # We can recompute prefix_max and suffix_max: 
                prefix_max[0] = best[0]
                for i in range(1, p):
                    prefix_max[i] = max(prefix_max[i-1], best[i])
                suffix_max[p-1] = best[p-1]
                for i in range(p-2, -1, -1):
                    suffix_max[i] = max(suffix_max[i+1], best[i])

            Then for i from j to N:
                r_i = r[i]
                candidate1 = prefix_max[r_i]   # if r_i>=0, and if prefix_max[r_i] is -10**18, then skip?
                candidate2 = suffix_max[r_i+1] if r_i+1 < p else -10**18

                # But note: if no element in [0, r_i], then prefix_max[r_i] might be -10**18? same for candidate2.

                c1 = candidate1 + r_i if candidate1 != -10**18 else -10**18
                c2 = candidate2 + r_i + p if candidate2 != -10**18 else -10**18
                dp[i][j] = max(c1, c2)

                # Then update the data structure for t=i (for the j-1 layer) and update prefix_max and suffix_max?
                residue_i = r[i]
                value = dp[i][j-1] - r[i]   # note: j-1 layer state for index i
                if value > best[residue_i]:
                    best[residue_i] = value
                    # update prefix_max and suffix_max for the entire array? 
                    prefix_max[0] = best[0]
                    for idx in range(1, p):
                        prefix_max[idx] = max(prefix_max[idx-1], best[idx])
                    suffix_max[p-1] = best[p-1]
                    for idx in range(p-2, -1, -1):
                        suffix_max[idx] = max(suffix_max[idx+1], best[idx])

            # End for i

        However, note: we update the entire prefix_max and suffix_max arrays at each update to the best array. The update happens at most N times per j, and each update O(p) -> total O(N*p) per j, which is 20000*100=2e6 per j, and j up to 50 -> 100e6, which is acceptable in Pyton? 

        But 100e6 operations in Python might be borderline in Pyton (in C++ it's fine). We can try to update only the necessary parts? But p is only 100, so 100*20000*50 = 100e6, which in Python might run in 10 seconds? But the constraints are 20000 and k=50, p=100. 

        Alternatively, we can avoid rebuilding the entire prefix_max and suffix_max arrays and just update the changed residue? But since we are updating one element, we can update the prefix_max from the changed residue to the end? But the prefix_max might change for all indices from the changed residue to the end? Similarly for suffix_max.

        But since p is small (100), we can do a full recompute.

        However, note: we don't need to update the entire arrays for every i? We update the best array only once per i? Then we update the prefix_max and suffix_max. 

        So the inner loop per j: 
            We do: 
                update the initial t0 -> one update -> O(p) to rebuild.
                then for each i from j to N: 
                    O(p) for the two queries (if we don't store prefix_max and suffix_max we could scan, but we have stored so O(1))
                    then update the best array at one position -> then O(p) to rebuild the prefix_max and suffix_max.

            Total: O(N * p) per j -> 20000 * 100 = 2e6 per j -> 50 * 2e6 = 100e6.

        We can do 100e6 operations in Pyton? It might be borderline in Pyton (in Pyton, 1e8 operations might take about 10 seconds in Pyton, so 100e6 is about 1 second). 

        But let me check: 100e6 operations, each operation is a comparison and assignment? That should be acceptable.

        However, we can avoid rebuilding the entire prefix_max and suffix_max arrays by updating only the necessary parts? 

        For prefix_max: 
            When we update best[i] at residue = idx, then:
                prefix_max[0] = best[0]
                then for i from 1 to p-1: prefix_max[i] = max(prefix_max[i-1], best[i])

            We can update from idx to the end? Actually, if we update at residue idx, then prefix_max for indices < idx might be unchanged? But no: because prefix_max is computed from left to right. Actually, if we update at residue idx, then prefix_max for indices from idx to p-1 might change? 

            Actually, we can update from 0 to p-1? 

        Similarly for suffix_max: we update from p-1 to 0? 

        But since p is 100, we can do the entire recompute.

        Implementation:

            We'll use:
                dp = 2D array of size (N+1) x (k+1) -> but note: k is at most 50, and N=20000, so 20000*50 = 1e6 states -> acceptable.

            Steps:

                Precompute prefix[0..N] and r[0..N] = [prefix[i] % p for i in range(N+1)]

                dp = [[-10**18]*(k+1) for _ in range(N+1)]

                # Base: j=1
                for i in range(1, N+1):
                    dp[i][1] = r[i]   # because prefix[i] % p

                # For j from 2 to k:
                for j in range(2, k+1):
                    best = [-10**18] * p   # for residues
                    # Precompute prefix_max and suffix_max arrays for best
                    prefix_max = [-10**18] * p
                    suffix_max = [-10**18] * p

                    # Update the state for t0 = j-1: 
                    t0 = j-1
                    residue0 = r[t0]
                    value0 = dp[t0][j-1] - residue0
                    if value0 > best[residue0]:
                        best[residue0] = value0
                        # Recompute prefix_max and suffix_max for the entire array
                        prefix_max[0] = best[0]
                        for idx in range(1, p):
                            prefix_max[idx] = max(prefix_max[idx-1], best[idx])
                        suffix_max[p-1] = best[p-1]
                        for idx in range(p-2, -1, -1):
                            suffix_max[idx] = max(suffix_max[idx+1], best[idx])

                    # Iterate i from j to N:
                    for i in range(j, N+1):
                        residue_i = r[i]
                        # candidate1: from residues [0, residue_i]
                        c1_val = prefix_max[residue_i] 
                        c1 = c1_val + residue_i if c1_val != -10**18 else -10**18
                        # candidate2: from residues [residue_i+1, p-1]
                        if residue_i+1 < p:
                            c2_val = suffix_max[residue_i+1]
                        else:
                            c2_val = -10**18
                        c2 = c2_val + residue_i + p if c2_val != -10**18 else -10**18

                        dp[i][j] = max(c1, c2)

                        # Now, update the data structure for the j-1 layer state at index i (if we haven't computed the last state, then skip? but we need to update for the next i's? 
                        # But note: for the next i's (larger i) we will use this state. 
                        res_i = r[i]
                        val_here = dp[i][j-1] - res_i
                        if val_here > best[res_i]:
                            best[res_i] = val_here
                            # update prefix_max and suffix_max: we do a full recompute? 
                            prefix_max[0] = best[0]
                            for idx in range(1, p):
                                prefix_max[idx] = max(prefix_max[idx-1], best[idx])
                            suffix_max[p-1] = best[p-1]
                            for idx in range(p-2, -1, -1):
                                suffix_max[idx] = max(suffix_max[idx+1], best[idx])

                    # End for i
                # End for j

                return dp[N][k]

        But note: in the inner loop for i, we update the data structure for the state at i (which is the j-1 layer state). And then we use that for the next i's? 

        However, note: the next state we compute is for j segments at index i, and then we update the data structure for the j-1 layer at index i. This update is then available for the next state i+1.

        But what about the state at the same j for i? We don't use the state at the same j? 

        This matches.

        Let me test with the small example.

        But note: the above example for j=2: 
            i=2: dp[2][2]=7, then we update for t=2: residue = r[2]=7, value = dp[2][1] - 7 = 7-7=0 -> so we update best[7]=0 (if it is greater than the current, which is -10**18, so update). Then we update the prefix_max and suffix_max.

        Then for i=3: we use the updated best array.

        Then after processing i=3, we update for t=3: residue = r[3]=4, value = dp[3][1]-4 = 4-4=0 -> update best[4]=0.

        Then for i=4: we use the updated best array (with best[4]=0 and best[7]=0).

        Then after i=4, we update for t=4: residue = r[4]=6, value = dp[4][1]-6 = 6-6=0 -> update best[6]=0.

        But we don't use that in j=2? 

        Actually, we don't use it in j=2 again. 

        Then we move to j=3.

        In j=3: we start by updating t0 = j-1=2: residue = r[2]=7, value = dp[2][2]-7 = 7-7=0 -> update best[7]=0.

        Then for i=3: 
            residue_i = r[3]=4
            candidate1: residues [0,4] -> max = -10**18? 
            candidate2: residues [5,9] -> max = best[7]=0 -> 0+4+10=14 -> dp[3][3]=14.

        Then update for t=3: residue = r[3]=4, value = dp[3][2] - 4 = 14-4=10 -> update best[4]=10.

        Then for i=4: 
            residue_i = r[4]=6
            candidate1: residues [0,6]: max = best[4]=10 -> 10+6=16
            candidate2: residues [7,9]: max = best[7]=0 -> 0+6+10=16 -> dp[4][3]=16.

        Then update for t=4: residue=6, value = dp[4][2] - 6 = 16-6=10 -> update best[6]=10.

        Then return dp[4][3]=16.

        It matches.

        Edge: if k=1? Then we don't go into the j>=2 loop, so we return dp[N][1] = r[N].

        Another edge: if k==0? But k>=2 per constraints.

        Also note: we have to consider the entire array, so we use i=N.

        However, what if k>N? But constraint k<=N.

        So we are safe.

        Let me test with the second example.

        But we can run the second example: 
            Input: 10 5 12
                    16 3 24 13 9 8 7 5 12 12

            Expected: 37.

        We'll code and test.

        Alternatively, we can compute by hand? 

        But we trust the algorithm? 

        However, we note: the algorithm is O(k*N*p) = 50 * 20000 * 100 = 100e6, which is acceptable.

        But note: we are storing a 2D dp array of size (N+1) x (k+1) -> 20000*50=1e6 integers, which is acceptable.

        We'll code accordingly.

        One more note: the prefix array: 
            prefix[0]=0
            prefix[1]=A[0]
            prefix[2]=A[0]+A[1]
            ...
            prefix[i] = A[0]+...+A[i-1]

        Then for i in [1, N]: 
            dp[i][1] = prefix[i] % p

        But note: the segment for j=1 is the entire prefix [0, i]? Actually, the segment is from index0 to index i-1? 

        Yes.

        Then for the rest, we use the recurrence.

        Let's code accordingly.

        However, note: the example 2: 
            N=10, k=5, p=12, A=[16,3,24,13,9,8,7,5,12,12]

        We'll compute prefix and r:

            prefix[0]=0 -> r0=0
            prefix[1]=16 -> r1=16%12=4
            prefix[2]=16+3=19 -> r2=19%12=7
            prefix[3]=19+24=43 -> r3=43%12=7
            prefix[4]=43+13=56 -> r4=56%12=8
            prefix[5]=56+9=65 -> r5=65%12=5
            prefix[6]=65+8=73 -> r6=73%12=1
            prefix[7]=73+7=80 -> r7=80%12=8
            prefix[8]=80+5=85 -> r8=85%12=1
            prefix[9]=85+12=97 -> r9=97%12=1
            prefix[10]=97+12=109 -> r10=109%12=1

        Then j=1: 
            dp[1][1]=4
            dp[2][1]=7
            dp[3][1]=7
            dp[4][1]=8
            dp[5][1]=5
            dp[6][1]=1
            dp[7][1]=8
            dp[8][1]=1
            dp[9][1]=1
            dp[10][1]=1

        Then j=2: 
            We start at j=2: t0 = 1 -> residue0 = r1=4, value0 = dp[1][1]-4 = 4-4=0 -> update best[4]=0.

            i=2: 
                residue_i = r2=7
                candidate1: [0,7] -> max=0 (at 4) -> 0+7=7
                candidate2: [8,11] -> max=-10**18 -> skip -> dp[2][2]=7
                then update: for t=2: residue = r2=7, value = dp[2][1]-7 = 7-7=0 -> update best[7]=0 -> then update prefix_max and suffix_max.

            i=3:
                residue_i = r3=7
                candidate1: [0,7]: max(0,0)=0 -> 0+7=7
                candidate2: [8,11]: -10**18 -> dp[3][2]=7
                update: for t=3: residue=7, value = dp[3][1]-7=7-7=0 -> but best[7] is already 0, so no update.

            i=4:
                residue_i=8
                candidate1: [0,8]: max=0 -> 0+8=8
                candidate2: [9,11]: -10**18 -> dp[4][2]=8
                update: t=4: residue=8, value=dp[4][1]-8=8-8=0 -> update best[8]=0.

            i=5:
                residue_i=5
                candidate1: [0,5]: max=0 -> 0+5=5
                candidate2: [6,11]: max in [6,11] = 0 (at 7 and 8) -> 0+5+12=17 -> dp[5][2]=17
                update: t=5: residue=5, value=dp[5][1]-5=5-5=0 -> update best[5]=0.

            i=6:
                residue_i=1
                candidate1: [0,1]: max=0 -> 0+1=1
                candidate2: [2,11]: max=0 -> 0+1+12=13 -> dp[6][2]=13
                update: t=6: residue=1, value=dp[6][1]-1=1-1=0 -> update best[1]=0.

            i=7:
                residue_i=8
                candidate1: [0,8]: max=0 -> 0+8=8
                candidate2: [9,11]: -10**18 -> dp[7][2]=8
                update: t=7: residue=8, value=dp[7][1]-8=8-8=0 -> no update.

            i=8:
                residue_i=1
                candidate1: [0,1]: max=0 -> 0+1=1
                candidate2: [2,11]: max=0 -> 0+1+12=13 -> dp[8][2]=13
                update: t=8: residue=1, value=dp[8][1]-1=1-1=0 -> no update.

            i=9:
                residue_i=1
                same as above: dp[9][2]=13
                update: t=9: residue=1, value=0 -> no update.

            i=10:
                residue_i=1
                same: dp[10][2]=13
                update: t=10: residue=1, value=0 -> no update.

        Then j=3:
            t0 = j-1=2 -> residue0 = r2=7, value0 = dp[2][2]-7=7-7=0 -> update best[7]=0.

            i=3: 
                residue_i=7
                candidate1: [0,7]: max=0 -> 0+7=7
                candidate2: [8,11]: max=0 (from residue8? we haven't updated residue8? only residue7) -> but we updated residue7. Then candidate2: 0+7+12=19 -> so max(7,19)=19 -> dp[3][3]=19
                then update: t=3: residue=7, value=dp[3][2]-7=7-7=0 -> no update.

            i=4:
                residue_i=8
                candidate1: [0,8]: max=0 -> 0+8=8
                candidate2: [9,11]: -10**18 -> dp[4][3]=8
                update: t=4: residue=8, value=dp[4][2]-8=8-8=0 -> no update.

            i=5:
                residue_i=5
                candidate1: [0,5]: max=0 -> 0+5=5
                candidate2: [6,11]: max=0 -> 0+5+12=17 -> dp[5][3]=17
                update: t=5: residue=5, value=dp[5][2]-5=17-5=12 -> update best[5]=12.

            i=6:
                residue_i=1
                candidate1: [0,1]: max=0 -> 0+1=1
                candidate2: [2,11]: max=12 (at residue5) -> 12+1+12=25 -> dp[6][3]=25
                update: t=6: residue=1, value=dp[6][2]-1=13-1=12 -> update best[1]=12.

            i=7:
                residue_i=8
                candidate1: [0,8]: max(0,12 (from 1? but residue1=12, residue5=12) -> note: residue1 is 1, residue5 is 5, and we updated best[1]=12 and best[5]=12. 
                    The residues in [0,8]: residues 0,1,...,8 -> the maximum value is max(0, best[1]=12, best[5]=12, best[7]=0, best[8]=0) = 12 -> then 12+8=20.
                candidate2: [9,11]: -10**18 -> dp[7][3]=20
                update: t=7: residue=8, value=dp[7][2]-8=8-8=0 -> no update.

            i=8:
                residue_i=1
                candidate1: [0,1]: max(0, best[1]=12) = 12 -> 12+1=13
                candidate2: [2,11]: max(best[5]=12) -> 12+1+12=25 -> dp[8][3]=25
                update: t=8: residue=1, value=dp[8][2]-1=13-1=12 -> but best[1] is already 12, so no update.

            i=9: residue=1 -> same as i=8: 
                candidate1: 12+1=13
                candidate2: 12+1+12=25 -> dp[9][3]=25

            i=10: same -> dp[10][3]=25

        Then j=4:
            t0 = j-1=3 -> residue0 = r3=7, value0 = dp[3][3]-7=19-7=12 -> update best[7]=12.

            i=4: residue_i=8
                candidate1: [0,8]: max(best[1]=12, best[5]=12, best[7]=12, best[8]=? we haven't updated 8? -> 12 -> 12+8=20
                candidate2: [9,11]: none -> dp[4][4]=20
                update: t=4: residue=8, value=dp[4][3]-8=8-8=0 -> no update.

            i=5: residue_i=5
                candidate1: [0,5]: max(best[1]=12, best[5]=12) -> 12+5=17
                candidate2: [6,11]: best[7]=12 -> 12+5+12=29 -> dp[5][4]=29
                update: t=5: residue=5, value=dp[5][3]-5=17-5=12 -> but best[5] is 12, so no update.

            i=6: residue_i=1
                candidate1: [0,1]: best[1]=12 -> 12+1=13
                candidate2: [2,11]: best[5]=12, best[7]=12 -> 12+1+12=25 -> dp[6][4]=25
                update: t=6: residue=1, value=dp[6][3]-1=25-1=24 -> update best[1]=24.

            i=7: residue_i=8
                candidate1: [0,8]: max(24, 12,12,12) = 24 -> 24+8=32
                candidate2: [9,11]: none -> dp[7][4]=32
                update: t=7: residue=8, value=dp[7][3]-8=20-8=12 -> update best[8]=12? but we haven't had best[8]? Actually, we had updated best[8] in j=2? but then we reset for j=4. 
                    Now we update best[8]=12.

            i=8: residue_i=1
                candidate1: [0,1]: best[1]=24 -> 24+1=25
                candidate2: [2,11]: best[5]=12, best[7]=12, best[8]=12 -> max=12 -> 12+1+12=25 -> dp[8][4]=25
                update: t=8: residue=1, value=dp[8][3]-1=25-1=24 -> but best[1] is 24, so no update.

            i=9: same as i=8: dp[9][4]=25
            i=10: same: dp[10][4]=25

        Then j=5:
            t0 = j-1=4 -> residue0 = r4=8, value0 = dp[4][4]-8=20-8=12 -> update best[8]=12.

            i=5: residue_i=5
                candidate1: [0,5]: max(best[1]? we haven't updated any other residue? only residue8. Then for [0,5]: max=-10**18? 
                candidate2: [6,11]: best[8]=12 -> 12+5+12=29 -> dp[5][5]=29
                update: t=5: residue=5, value=dp[5][4]-5=29-5=24 -> update best[5]=24.

            i=6: residue_i=1
                candidate1: [0,1]: best[1]? not updated -> candidate1 = -10**18
                candidate2: [2,11]: best[5]=24, best[8]=12 -> max=24 -> 24+1+12=37 -> dp[6][5]=37
                update: t=6: residue=1, value=dp[6][4]-1=25-1=24 -> update best[1]=24.

            i=7:

## Final Solution

