# Solution for abc302_f.md

Generated at: 2025-07-22 06:21:59
Model: deepseek-reasoner

## Reasoning Process

Alright, I'm going to try to solve this problem. Let me first make sure I understand it correctly.

We have N sets of integers, each containing distinct integers between 1 and M. The goal is to determine if we can obtain a set that contains both 1 and M by repeatedly merging sets that have at least one common element. Each merge operation takes two sets that share a common element and replaces them with their union. We need the minimum number of operations to achieve this, and if it's impossible, return -1.

From the examples, I see:
- In Example 1, we start with three sets: {1,2}, {2,3}, {3,4,5}. We merge the first two to get {1,2,3}, then merge that with {3,4,5} to get {1,2,3,4,5}, which has both 1 and M (which is 5). So 2 operations.
- Example 2 has a single set that already has both 1 and 2, so 0 operations.
- Example 3: sets are {1,3}, {2,4}, {2,4,5}. There's no way to merge because the sets with 1 and the sets with M (5) don't have any overlapping elements with each other. So output is -1.
- Example 4: we have four sets. The answer is 2 operations.

Constraints: N up to 200,000, M up to 200,000, and the total elements across sets is up to 500,000. So we need an efficient solution.

I need to model the problem. Each operation merges two sets that share at least one common element. The process continues until we can't merge anymore, or until we get a set that has both 1 and M. The operations are similar to union-find operations, but the goal is to combine sets until we have one set that has both 1 and M.

But note: we don't necessarily have to merge all sets. We just need to form a set that has both 1 and M. So we can think of the sets as nodes in a graph? Or perhaps we can model the problem as a graph where each element is a node, and each set is a hyperedge connecting all the elements in it. Then, the operation is merging hyperedges that share a node. But that might be complex.

Alternatively, we can think in terms of connectivity. We want to know if there's a chain of sets that connect 1 to M. Each set can be seen as connecting all elements within it. But the merging operation requires that two sets share an element to be merged. So effectively, if we have sets that together form a connected path from 1 to M, we can merge them step by step.

The problem then reduces to: we have a hypergraph where each set is a hyperedge (a hyperedge connects all the elements in the set). Then, we want to find the minimum number of hyperedges that form a connected path from 1 to M? But actually, the operations merge two sets at a time, and the number of operations is one less than the number of sets we merge. So if we merge k sets, we have k-1 operations.

Therefore, the problem becomes: find the smallest number of sets (say k) such that the union of these sets contains both 1 and M, and the sets form a connected chain (via common elements). Then the operations required would be k-1.

But we need the minimum operations. So we want the smallest k (number of sets) that can be merged (i.e., they are connected and cover 1 and M) and then the operations would be k-1.

So we are to find the minimum number of sets that together form a connected component that includes both 1 and M. Then the answer is (k-1). If no such chain exists, output -1.

But how do we compute the minimum chain of sets from 1 to M?

We can model the problem as a graph where:
- Each set is a node? But then how do we connect sets? We can connect two sets if they share a common element. But then the graph would be huge: N nodes, and edges between two sets if they share an element. The number of edges could be O(N^2), which is 40e9 for N=200,000 — too big.

Alternative approach: consider the elements as nodes. Then, each set is a hyperedge that connects all the elements in it. Then, we want to find the shortest path from node 1 to node M in this hypergraph. But the path length in terms of hyperedges? Because each hyperedge (set) we use corresponds to one set, and the number of sets used is the length of the path. Then the operations would be (number of sets used) - 1.

But in a hypergraph, the shortest path in terms of hyperedges is non‐trivial. However, note that we can traverse from one element to another via a hyperedge: if two elements are in the same set, we can move from any element in the set to any other without any cost? Actually, in the hypergraph, we can represent each hyperedge by creating a dummy node and connecting every element in the set to the dummy node? Then the distance from one element to another would be half the actual path (because we traverse element -> hyperedge node -> element). But then the path from 1 to M would go through several hyperedges. The length of the path (in terms of edges) would be 2 * (number of hyperedges) - 1? Actually, if we use k hyperedges, the path from 1 to M would be: 
1 -> hyperedge1 -> element a -> hyperedge2 -> element b -> ... -> hyperedge k -> M.
So the number of edges traversed is 2*k. Then the actual number of hyperedges (sets) used is k, and the operations needed to merge them is k-1. But the path in the graph has 2*k edges? Actually, the path has k+1 nodes (the hyperedges and the elements) but that's not standard.

Alternatively, we can use the following transformation: 
- Create a bipartite graph with two types of nodes: the original elements (from 1 to M) and the sets (we can give them IDs, say 1 to N). Then, for each set i, we connect every element in the set to the set node. So each set is represented as a star: the set node connected to each element in the set.

Then, the path from element 1 to element M would go through a sequence of set nodes and element nodes. For example: 
1 -- set_i -- element a -- set_j -- element b -- ... -- set_k -- M.

The length of this path (in terms of edges) is (number of set nodes)*2 + 1? Actually, each set node contributes two edges: one from the incoming element and one to the outgoing element. So the entire path from 1 to M would have an odd number of edges: 1 (edge) to set i, then to element a (another edge), then to set j (third edge), then to element b (fourth edge), etc. until M.

But note: the number of set nodes in the path is the same as the number of sets we use. Let that number be k. Then the total number of edges in the path is 2*k + 1? Actually, the path goes from 1 to set1 (1 edge), set1 to element a (another edge) — that's 2 edges to get to element a. Then from element a to set2 (third edge), set2 to element b (fourth), and so on until setk to M (which is the 2*k-th edge?).

Wait: 
- Start at 1 (element node).
- Go to set1: 1 edge.
- Then from set1 to an element a: 2nd edge.
- Then from a to set2: 3rd edge.
- Then set2 to another element: 4th edge.
- ... until we get to M.

So if we use k sets, we have 2*k edges? Then the total path length in the bipartite graph is 2*k. But we start at 1 and end at M, both element nodes. So the path must have an even number of edges? Actually, no: the edges are:
1 (element) -> set1 (node): edge 1
set1 -> element a: edge 2
element a -> set2: edge 3
set2 -> element b: edge 4
...
setk -> M: edge (2*k)

So indeed, the path from 1 to M has 2*k edges. The number of sets used is k, and the operations needed to merge these k sets is k-1.

But the problem: we want the minimum number of operations, which is k-1. So we need to minimize k, which is the number of sets. Therefore, we need to find the path from 1 to M in this bipartite graph that minimizes k (the number of set nodes in the path). Then the operations = k - 1.

But note: in the bipartite graph, the path from 1 to M must alternate between element nodes and set nodes. So we can run a BFS on this graph to find the shortest path (in terms of number of edges) from node 1 to node M. Then the number of edges is 2*k, so k = (number of edges)/2. Then operations = k-1 = (number of edges)/2 - 1.

But wait: the number of edges in the path is 2*k, so the number of set nodes is k. Then the operations are k-1.

But the path from 1 to M in the bipartite graph: the length (in edges) is 2*k. Then the number of set nodes is k. So the operations are k-1.

Alternatively, we can think of the distance from 1 to M as (2*k). Then k = (distance)/2, and operations = (distance)/2 - 1.

But we must note: the BFS in the bipartite graph will give the distance in terms of the number of edges. So if the distance (number of edges) is d, then the number of set nodes is d/2? Because each set node adds two edges? Actually, the entire path is: 
1 (element) -> set node (step 1) -> element (step 2) -> set node (step 3) -> ... until M.

So the steps (number of edges) from 1 to M must be even? Actually, the parity: 
- Step 0: at element 1.
- Step 1: go to a set node (edge count 1).
- Step 2: go to an element node (edge count 2).
- Step 3: go to a set node (edge count 3).
- Step 4: go to an element node (edge count 4).

So to get to M (an element node), we must be at an even step. So the distance (in edges) must be even.

Therefore, the minimal d (even) from 1 to M. Then operations = d/2 - 1.

But what if we can avoid going through unnecessary elements? The BFS in the bipartite graph will find the shortest path in terms of edges. Since each set node adds two edges (one in and one out), we are effectively counting the number of sets.

However, the graph has two types of nodes: elements (M nodes) and sets (N nodes). The total number of nodes is M + N, which can be up to 400,000. The edges: each set i has A_i elements, so the set node is connected to A_i element nodes. The total edges is the sum of A_i, which is 500,000. So the graph has about 500,000 edges. This is manageable with BFS.

But note: we are building a graph with M + N nodes. The edges are only between element nodes and set nodes. We don't have edges between two element nodes or two set nodes.

So the BFS would start from node 1 (which is an element node). Then we traverse to all set nodes that include 1. Then from each set node, we traverse to all element nodes in that set. Then from each element node, we traverse to set nodes that contain that element, and so on.

But we have to avoid revisiting nodes? Yes, we can mark visited nodes to avoid cycles.

But note: if we revisit a set node, that would be redundant because we already visited it. Similarly, if we revisit an element node, we don't need to visit it again because the first time we reached it is the shortest.

So we can do:

- Let dist[node] be the distance (number of edges) from element 1 to this node.
- We start from element 1 with dist[1]=0.
- Then we traverse to all set nodes that contain 1: for each set s that contains 1, we set dist[s] = 1.
- Then from set s, we traverse to all elements in the set (except 1, but we might have other elements). For each element e in the set, if we haven't visited e, we set dist[e] = 2.
- Then from element e, we go to all sets that contain e and haven't been visited, and set dist[set] = 3, and so on.

We stop when we reach element M.

Then the distance to M is d (an even number). Then the number of sets used is d/2, and the operations = d/2 - 1.

But what if we start at 1? The distance 0: we are at 1. Then if 1 is already connected to M in the same set? Then we might not need to use any set? Actually, if 1 and M are in the same set, then we can get a set containing both without any operation? So that set already exists. Then the operations are 0.

But in the BFS, if we start at 1, and in the same set that contains 1 also contains M, then when we go from 1 to the set (distance=1) and then from the set to M (distance=2). Then d=2, so operations = 2/2 - 1 = 0. That matches.

So the formula holds.

But what if there's no path? Then we never reach M, so we output -1.

Now, we have to build the graph. However, note that we have up to 500,000 edges (the total elements in sets). So we can build the graph as:

- Nodes: 
  - Element nodes: labeled 1 to M.
  - Set nodes: we can label them arbitrarily, but to avoid conflict, we can label set i as i + M (so set1 becomes M+1, set2 becomes M+2, ... setN becomes M+N).

But we don't need to store the set nodes explicitly? Actually, we can do BFS without building the entire graph in advance? Alternatively, we can build an adjacency list.

The graph will have:
- For each element node e, we want to know which sets contain e.
- For each set node s, we want to know which elements are in the set.

But we are given the sets. So we can precompute:

- Create an array (or list) `graph` of size (M + N + 1) for adjacency list? But the total nodes is M + N, so we can index from 1 to M (elements) and M+1 to M+N (sets).

But we also need to avoid duplicate edges? Since each edge is unique: from element to set and set to element.

Alternatively, we can avoid storing the set nodes explicitly by doing two-level BFS. But for clarity, we can build the graph as:

- Adjacency list for each node (whether element or set) to list of neighbors.

But the total nodes is M+N (<= 400,000) and total edges is total elements (each element in a set contributes two directed edges? Actually, in the graph we are building, each element in a set s gives an undirected edge between the element node and the set node. So for each element in a set, we have two directed edges: from element to set and set to element.

But we can store the graph as:

graph = {}
For each set i (from 0 to N-1):
  set_node = M + i + 1? Actually, the sets are given in order. We can assign set i (the i-th set) to node M+i+1? But note: M and N are both given, and we have to be careful with indices.

Alternatively, we can use 0-indexed for elements: elements 0 to M-1? But the input uses 1-indexed. So we can let:
- Element nodes: 0 to M-1 (representing 1 to M) — but then we have to map: element 1 is node 0, element 2 is node 1, ..., element M is node M-1.
- Set nodes: from M to M+N-1.

But that might be messy. Alternatively, we can use a dictionary or just use two separate arrays for element nodes and set nodes. But we can do:

Let’s have an array `adj` of size (M + N + 1) (indexed from 0 to M+N). But we don't use index 0? Actually, we can index from 1 to M for elements, and M+1 to M+N for sets.

But note: M and N are up to 200,000, so M+N is 400,000. The total edges is 500,000. So we can build the graph with:

Initialize:
graph = [[] for _ in range(M+N+1)]   # indices 0 unused; 1 to M: elements; M+1 to M+N: sets.

Then, for the i-th set (0-indexed i), the set node is M + i + 1? Actually, i from 0 to N-1, so set node id = M + i + 1? Then we have to store up to M+N, which is M + N, so the last set node is M + N.

But we can also do set node id = M + i (if we 0-index the sets). Then the set nodes are from M+0 to M+N-1. Then the total node ids: 1 to M-1 (elements) and M to M+N-1 (sets). Then the total nodes is M + N.

But then we have to map the sets: the first set is at index M, the second at M+1, etc.

So:

For i in range(N):
   set_node = M + i   # if we use 0-indexed for sets, and elements 1-indexed? Then element 1 is at index 1, but set0 is at index M (which might be 200000). Then we have to connect.

But the elements are given as integers from 1 to M. So for an element x, we use node id = x. For the set i, we use node id = M + i (if we start from 0) but then the node id for the set is from M to M+N-1.

Then, for each set i, we have a list of elements. For each element x in set i:
   add an undirected edge between x and (M+i).

But in the graph, we can add:
   adj[x].append(M+i)
   adj[M+i].append(x)

Then the graph has M (elements) + N (sets) nodes, and the total edges is 2 * (total number of elements in all sets) = 2 * (sum of A_i) <= 1,000,000? Actually, each element in a set adds two directed edges? Or in the adjacency list, we store each undirected edge twice? Actually, for each element x in set i, we add an edge from x to set_i and from set_i to x. So the total number of entries in the adjacency list is 2 * (sum of A_i) <= 1,000,000.

Then we run BFS starting from node 1 (element 1) and we stop when we reach node M (element M). Then the distance d is the number of edges. Then the answer is (d // 2) - 1? Because the number of set nodes is d/2, and operations = (d/2) - 1.

But wait: the path from 1 to M: the distance d is the number of edges. For example:
- 1 to set0: 1 edge
- set0 to element x: 2 edges
- ... until we get to M: so if we use k sets, then the path has 2*k edges? Actually, no: the entire path is:

1 (start) -> set0: 1 edge
set0 -> element x: 2 edges? Actually, each step is one edge.

The entire path from 1 to M is a sequence of edges. The total number of edges is the distance d. Then the number of set nodes in the path is d/2? Because every two edges we traverse one set node? Actually, the path alternates between element and set nodes. So the path is: 
element1 -> set_node -> element2 -> set_node -> ... -> element_M.

The number of set nodes in the path is d/2? Actually, the path has d edges and d+1 nodes? But we are counting edges. The number of set nodes in the path is d/2? Because each set node is connected by two edges? Actually, each set node is traversed by one incoming edge (from an element) and one outgoing edge (to an element). So the entire path has k set nodes and k+1 element nodes (including 1 and M). The total edges is 2*k. Therefore, d = 2*k. Then k = d/2, and the operations = k - 1 = d/2 - 1.

So if we get the distance d (which is even) from 1 to M, then the answer is d//2 - 1.

But what if the distance is odd? That shouldn't happen because we start and end at element nodes, and the path alternates: element -> set -> element -> ... -> element. So the number of edges must be even? Actually, from element to element, we go through one set: two edges. Then the distance from one element to another is even? Actually, from 1 to M: if we go through k sets, the distance is 2*k. So d must be even.

Therefore, we can run BFS and when we reach M, we check d. Then answer = d//2 - 1.

But what if 1 and M are the same? Then d=0? Then operations = 0//2 -1 = -1? But M>=2, and 1 and M are distinct. So no problem.

Edge: if the set that contains 1 also contains M? Then we can get from 1 to the set and then to M: that's 2 edges. Then operations = 2//2 -1 = 0. Correct.

Now, what about multiple sets containing the same element? That's okay because we can use any set that contains an element we are at to jump to the set and then to other elements.

But we must avoid revisiting the same set node or same element node? Yes, because if we revisit, we would get a longer path.

So the BFS:

We'll have a queue. We start at node 1 (element 1) with distance 0.

We mark node 1 as visited.

Then, for each neighbor (which are sets containing 1) of node 1: we traverse to those set nodes with distance 1. Then from each set node, we traverse to all element nodes in that set (that we haven't visited) with distance 2. Then from each element node, we traverse to all set nodes that contain that element (that we haven't visited) with distance 3, and so on.

When we reach the element M, we return the distance.

But the total nodes is M+N, which is about 400,000, and edges 1,000,000, so BFS is O(M+N) which is acceptable.

But we have to build the graph. How?

We are given the sets. We can:

1. Precompute an array `sets` of the sets? Actually, we don't need the sets as sets, we need to build the graph.

We need:
- For each element, we need to know which sets contain it? But we can build that as we read the input.

Alternatively, we can build:

- `element_to_sets`: a list of lists, where `element_to_sets[x]` is a list of set node ids that contain x. But x is from 1 to M.

But M and the total elements are large, but the total elements is 500,000, so we can do:

Initialize `element_to_sets` as a list of empty lists for elements 1 to M.

Then for each set i (from 0 to N-1):
   read A_i, then the A_i elements.
   set_node_id = M + i   (if we 0-index the sets) OR M + i + 1? Let me decide: we want the set nodes to be distinct and above M.

But the problem: the element nodes are from 1 to M. So set nodes should be from M+1 to M+N? Then:

   set_node_id = M + i + 1   (because i from 0 to N-1, so set_node_id from M+1 to M+N)

But then the total nodes we have: 1 to M (elements) and M+1 to M+N (sets). So the total is M+N.

Now, for each element x in the set, we add:

   adj[x].append(set_node_id)
   adj[set_node_id].append(x)

But we don't need to store adj for set_node_id until we build the set? Actually, we can build the graph as:

   We'll create an array `adj` of size (M + N + 2) perhaps? Indexed from 1 to M+N.

But we can use:

   adj = [[] for _ in range(M+N+2)]   # index 0 to M+N+1, but we use 1 to M and M+1 to M+N.

But note: set_node_id = M + i + 1, so for i=0, set0 is M+1; i=1, set1 is M+2, ... i=N-1, set_{N-1} is M+N.

Then the last node is M+N, so we need indices from 1 to M+N.

So we can create `adj` with size (M+N+1) (if we use 1-indexed). Actually, in Python, we can make a list of size (M+N+1) and then use indices 1 to M+N.

But we can also use a list that has M+N+1 elements, and we index from 0 to M+N. Then we ignore index 0? Or we can use 0-indexed for the graph nodes: 
- For element x, we use node index = x-1? Then set node for set i: we can use index = M + i? Then the total indices: 0 to M+N-1.

But that might be messy because then we have to remember that element 1 is at index 0, element M at index M-1, and set0 at index M, set1 at index M+1, ... set_{N-1} at index M+N-1.

Alternatively, we can use a dictionary? But the total nodes is not too big, and we can use a list.

I think it's easier to use 1-indexed for the graph:

- Element nodes: 1 to M
- Set nodes: M+1 to M+N

Then we create an array `adj` of size (M+N+1) (so index 0 to M+N). We'll use indices 1 to M+N.

Steps:

1. Read N, M.
2. Initialize `adj = [[] for _ in range(M+N+1)]`   # so we have indices 0 to M+N, but we will use 1 to M+N.
3. We'll also need an array `dist` of size (M+N+1) to store the distance from 1 (element 1). Initialize with -1 (unvisited).
4. Precompute `element_to_sets`? Actually, we don't need to, because we are going to build the graph by iterating the sets.

But we can build the graph as we read the sets:

   sets = []   # we might not need the sets, just the graph
   for i in range(N):
        read A_i
        read the list of A_i elements: let arr = list of integers
        set_node_id = M + i + 1   # because i is 0-indexed, so set0 -> M+1, set1->M+2, ... set_{N-1} -> M+N
        for x in arr:
            # add edge between x and set_node_id
            adj[x].append(set_node_id)
            adj[set_node_id].append(x)

But note: we don't need to store the set's content beyond building the graph.

5. Then run BFS starting from node 1.

BFS:

   from collections import deque
   q = deque()
   dist[1] = 0
   q.append(1)

   while q:
        u = q.popleft()
        if u == M:   # we reached the element M
            break
        for v in adj[u]:
            if dist[v] == -1:   # not visited
                dist[v] = dist[u] + 1
                q.append(v)

   Then if we reached M, then d = dist[M] (which is the number of edges). Then operations = d//2 - 1.

But wait: if we reached M, the distance is even? As we reasoned, yes.

But what if we don't reach M? Then we output -1.

But let me test with Example 1:

Sets: 
Set0: {1,2} -> set_node_id = M+1 = 5+1=6
Set1: {2,3} -> set_node_id=7
Set2: {3,4,5} -> set_node_id=8

Graph:

adj[1] = [6]
adj[2] = [6,7]
adj[3] = [7,8]
adj[4] = [8]
adj[5] = [8]   # element M=5
adj[6] = [1,2]
adj[7] = [2,3]
adj[8] = [3,4,5]

BFS from 1:

dist[1]=0 -> visit neighbors: 6 -> dist[6]=1
Then from 6: neighbors 1 (visited) and 2 -> dist[2]=2
Then from 2: neighbors 6 (visited) and 7 -> dist[7]=3
Then from 7: neighbors 2 (visited) and 3 -> dist[3]=4
Then from 3: neighbors 7 (visited) and 8 -> dist[8]=5
Then from 8: neighbors 3 (visited), 4,5 -> then we set dist[4]=6, dist[5]=6.

So when we reach 5 (element M), the distance is 6. Then operations = 6//2 -1 = 3-1=2. Correct.

But note: when we are at set node 6 (with distance 1), we also add element 2 with distance 2. Then from element 2, we go to set node 7 (distance 3). Then from set node 7, we go to element 3 (distance 4). Then from element 3, we go to set node 8 (distance 5). Then from set node 8, we go to element 5 (distance 6). So that's 6 edges.

But is there a shorter path? 
From 1 to set6 to 2 to set7 to 3 to set8 to 5: that's 6 edges. 
Alternatively, from 1 to set6 to 2 to set7 to 3: then to set8 to 5: same.

But what if we can go from set6 to set7? No, because we don't have edges between sets. The graph is bipartite: edges only between element and set. So no direct connection between sets.

Now, Example 2: 
Input: 
1 2
2
1 2

Then:
adj[1] = [M+1]   # M=2, so set0=3
adj[2] = [3]
adj[3] = [1,2]

BFS:
Start at 1: dist[1]=0 -> then go to 3 (dist=1) -> then from 3 go to 2 (dist=2). Then operations = 2//2 -1 = 1-1=0. Correct.

Example 3:
Sets: 
Set0: {1,3} -> set0: M+1=5+1=6
Set1: {2,4} -> set1=7
Set2: {2,4,5} -> set2=8

adj[1]=[6]
adj[3]=[6]
adj[2]=[7,8]
adj[4]=[7,8]
adj[5]=[8]
adj[6]=[1,3]
adj[7]=[2,4]
adj[8]=[2,4,5]

BFS from 1:
1 ->6 (dist=1) -> then from 6: 1 (visited) and 3 (dist=2). 
From 3: only set6 (visited). 
Then we also have to consider: from 3, we go to set6 (visited) and nothing else. 
So we don't reach 5? Then we output -1.

But wait: can we merge sets? Actually, the sets {2,4} and {2,4,5} share 2 and 4, so they can be merged. But 1 is only in set0. There's no common element between set0 (1,3) and set1 (2,4) or set2? So we cannot merge. So indeed, we cannot connect 1 to 5.

In the BFS, we never reach 5. So we output -1.

Example 4: 
Input: 
4 8
3
1 3 5
2
1 2
3
2 4 7
4
4 6 7 8

Sets:
Set0: {1,3,5} -> node 8+1 = 9
Set1: {1,2} -> node 10
Set2: {2,4,7} -> node 11
Set3: {4,6,7,8} -> node 12

Graph:
adj[1] = [9,10]
adj[2] = [10,11]
adj[3] = [9]
adj[4] = [11,12]
adj[5] = [9]
adj[6] = [12]
adj[7] = [11,12]
adj[8] = [12]   # M=8
adj[9] = [1,3,5]
adj[10] = [1,2]
adj[11] = [2,4,7]
adj[12] = [4,6,7,8]

BFS from 1:

dist[1]=0
From 1: visit set9 (9) and set10 (10) -> dist[9]=1, dist[10]=1.

Then from set9: go to elements 1 (visited), 3, 5 -> dist[3]=2, dist[5]=2.
Then from set10: go to elements 1 (visited) and 2 -> dist[2]=2.

Now from element 3: go to set9 (visited) -> nothing new.
From element 5: set9 (visited) -> nothing.
From element 2: go to set10 (visited) and set11 (11) -> dist[11]=3.

Then from set11: go to elements 2 (visited), 4, 7 -> dist[4]=4, dist[7]=4.

Then from element 4: go to set11 (visited) and set12 (12) -> dist[12]=5.
Then from set12: go to elements 4 (visited), 6, 7, 8 -> so we set dist[6]=6, dist[7]=6 (but we already set dist[7]=4? Actually, when we first reached 7 from set11, we set dist[7]=4. Then from set12, we set dist[7] again? But we don't because we only traverse unvisited nodes. So when we reach 8, we set dist[8]=6.

So the distance to 8 is 6. Then operations = 6//2 -1 = 3-1=2.

But the example output is 2, so it matches.

But wait, is there a shorter path? 

From 1 to set10 to 2 to set11 to 4 to set12 to 8: that's 6 edges.

Alternatively, 1 to set10 to 2 to set11 to 7 to set12 to 8: also 6.

But note: we could also go 1 to set9? That doesn't lead to 8 faster.

So 6 is the minimal.

But the example says output 2.

So the algorithm seems correct.

But note: we have to be careful about the set node indexing: we are using set_node_id = M + i + 1, where i is the set index (0-indexed). But what if M is 8 and we have 4 sets: then the set nodes are 9,10,11,12. Correct.

Implementation details:

- We need to read the input: first line N and M.
- Then for each set, we read A_i (the size) and then A_i integers.

- We'll build the graph. The graph will have M (elements) + N (sets) nodes. We create an adjacency list of size (M+N+1) (if we use 1-indexed, then index 0 unused, and we use 1 to M for elements, M+1 to M+N for sets).

But note: we are going to create an array `adj` of size (M+N+1). Then we process each set:

   set_index = i   (0-indexed from 0 to N-1)
   set_node_id = M + 1 + i   # so the first set is at M+1, the next M+2, ... last at M+N.

But if we do that, then the set nodes are M+1, M+2, ..., M+N.

But the element nodes are 1 to M.

Then for each element x in the set, we add:

   adj[x].append(set_node_id)
   adj[set_node_id].append(x)

But note: the element x is given, and it's between 1 and M, so we use the same integer.

Then we run BFS from node 1 (element 1) to node M (element M). 

But what if M is 8, then we are looking for node 8.

We have to initialize the `dist` array for nodes 1 to M+N. We can use a list `dist` of size (M+N+1) (index 0 to M+N), and initialize with -1.

Then we set dist[1] = 0, and then run BFS.

Finally, if dist[M] is -1, return -1. Else, return dist[M] // 2 - 1.

But wait: what if we reach M through a different path? The BFS will find the shortest path.

However, note that the graph is unweighted, so BFS gives the shortest path.

But what if the same element appears in multiple sets? That's already handled because we traverse all edges.

But we must avoid revisiting nodes to avoid cycles.

Now, let me check the constraints: total nodes M+N <= 400,000. Total edges: 2 * (total elements) <= 2 * 500,000 = 1,000,000. So BFS is efficient.

But note: we are building an adjacency list for M+N nodes. The memory: (M+N) nodes, each with a list of neighbors. The total memory is O(M+N + total_edges) = O(400,000 + 500,000) = O(900,000) which is acceptable.

But in Python, we have to be cautious about the recursion limit? But BFS with queue is iterative.

Now, what about the case when there are multiple sets that have 1? Then we start from 1 and go to all sets that contain 1. Then from each of those sets, we go to all elements in those sets. So we might visit a lot of nodes, but the total is bounded.

But worst-case: we have a set that contains all elements? Then from 1 we go to that set, then from that set we go to all elements. Then we have to store all elements. But the total elements is M, which is 200,000. And the total sets is N=200,000. So worst-case, we store a lot, but it's acceptable.

But we have to consider the BFS queue: in worst-case, we might have to store all nodes. But that's 400,000 nodes, which is acceptable.

So the plan:

1. Read N and M.
2. Initialize an empty adjacency list for M+N+1 nodes (if we use 1-indexed, we need up to M+N). Actually, we can create `adj = [[] for _ in range(M+N+1)]` — this will create a list of M+N+1 empty lists. We use indices 0 to M+N, but we are going to use 1 to M (elements) and M+1 to M+N (sets).
3. For i in range(N):
        A = int(input().strip())
        arr = list(map(int, input().split()))
        set_node_id = M + i + 1   # because i is from 0 to N-1, so set_node_id from M+1 to M+N
        for x in arr:
            # x is an element (1<=x<=M)
            adj[x].append(set_node_id)
            adj[set_node_id].append(x)
4. Initialize a queue and a distance array:
        dist = [-1] * (M+N+1)   # 0-indexed, so index 0 is unused, and we care about 1 to M+N.
        from collections import deque
        q = deque()
        dist[1] = 0
        q.append(1)
5. While q is not empty:
        u = q.popleft()
        if u == M:
            break
        for v in adj[u]:
            if dist[v] == -1:
                dist[v] = dist[u] + 1
                q.append(v)
6. If dist[M] == -1: print(-1)
   Else: 
        operations = dist[M] // 2 - 1
        But wait: what if dist[M] is 0? Then 0//2-1 = -1. But that should not happen because M>=2, so 1 and M are distinct. So we start at 1 and M is different. The minimal distance from 1 to M is at least 2 (if they are in the same set: then 1->set->M: 2 edges). So dist[M] will be at least 2.

But what if 1 and M are the same? Then M=1? But the problem says M>=2, so we don't have that.

Therefore, we can do:

        if dist[M] == -1:
            print(-1)
        else:
            print(dist[M] // 2 - 1)

But wait: what if we have a direct set that contains 1 and M? Then dist[M] is 2, then operations = 0. Correct.

But in the BFS, we start at 1 (dist=0). Then we go to a set that has both 1 and M. Then from that set we go to M: that's 2 edges.

But what if there's a shorter path? There isn't.

But what if there's a set that contains 1 and M, but we also have another set that we go through? Then the direct set would be found first because from 1 we go to the set and then to M: 2 edges. So it's the shortest.

But note: we are doing BFS so we always find the shortest path.

Now, let me test with the examples.

Example 2: the direct set: dist[M] = 2, then 2//2-1 = 0. Correct.

Example 1: dist[5]=6 -> 6//2-1=2. Correct.

Example 3: we never set dist[5] (if M=5) — actually, in example 3, M=5. We never reach 5? Then output -1.

Example 4: dist[8]=6 -> 6//2-1=2. Correct.

But wait: in example 4, we set dist[8]=6? Actually, when we reach set12 (node 12) at distance 5, then we traverse to 8: that's distance 6.

So the code:

We'll write in the function:

    from collections import deque

    total_nodes = M + N  # the last set node is M+N, but we have elements 1..M and sets M+1..M+N -> total nodes M+N? Actually, we have M elements and N sets, so total nodes = M+N. But the node ids go from 1 to M (elements) and M+1 to M+N (sets). So the total node ids we use: M+N.

    adj = [[] for _ in range(M+N+1)]   # index 0 to M+N; we use 1 to M+N.

    for i in range(N):
        # the set i: 
        #   we have the list: sets[i] = the list of integers
        #   set_node_id = M + i + 1
        set_node_id = M + i + 1
        for x in sets[i]:   # sets[i] is the list of integers for the i-th set
            adj[x].append(set_node_id)
            adj[set_node_id].append(x)

    dist = [-1] * (M+N+1)
    q = deque()
    dist[1] = 0
    q.append(1)

    while q:
        u = q.popleft()
        if u == M:
            break
        for v in adj[u]:
            if dist[v] == -1:
                dist[v] = dist[u] + 1
                q.append(v)

    if dist[M] == -1:
        return -1
    else:
        return dist[M] // 2 - 1

But note: the sets are given as a list of lists: `sets` is a list of the sets (each set is a list of integers). So we can iterate over the sets.

But the input format: 
First line: N M
Then for each set: 
   first integer A_i (the size)
   then a line of A_i integers.

So in the function, we have:

    def solve(self, N: int, M: int, sets: List[List[int]]) -> int:

But the sets are given as a list of lists. Each inner list is a set. We don't have the A_i as a separate line? Actually, the problem input has A_i on a separate line, then the next line has the set.

But the function signature: `solve(N, M, sets)`, and `sets` is a list of sets, each represented as a list of integers. So the A_i is the length of each list. So we don't need to read the A_i again.

But the problem says: 
Input format:
N M
A_1
S_{1,1} ... 
A_2
...

But the function `solve` is given N, M, and sets (which is a list of the sets, each as a list). So we don't have the A_i in the sets list? Actually, each list in `sets` is the set, so the length of that list is A_i.

So we can do:

    adj = [[] for _ in range(M+N+1)]   # indices 0 to M+N
    for i in range(N):
        arr = sets[i]   # list of integers
        set_node_id = M + i + 1
        for x in arr:
            if x < 1 or x > M:
                # but the constraints say between 1 and M, so skip?
                pass
            else:
                adj[x].append(set_node_id)
                adj[set_node_id].append(x)

But we don't need to check because the input guarantees 1<=x<=M.

Now, test with small example: 
N=1, M=2, sets = [[1,2]]
Then set_node_id = 2+0+1 = 3.
adj[1] = [3]
adj[2] = [3]
adj[3] = [1,2]

Then BFS: 
dist[1]=0 -> then from 1, go to 3 (dist=1) -> then from 3, go to 2 (dist=2). Then operations = 2//2-1=0.

But wait: we have to break when u==M? Then when we pop 2, we break? Actually, in the BFS, we break when we pop M. But we are checking at the beginning of the while loop: we pop u and then if u==M, break. But when we pop 2, we break.

But in the BFS, we add 3 (from 1) and then from 3 we add 1 (already visited) and 2. Then we pop 1? Then we pop 3? Then we pop 2: and then we break. So we set dist[2]=2.

So we break at u==M, which is 2.

But note: we set dist[2] when we process the edge from 3 to 2. Then we add 2 to the queue. Then when we pop 2, we break.

So it's correct.

But what if we have a set that contains M? Then we might set dist[M] from a set node? Then we break when we pop M.

But note: we break only when we pop M. The BFS: we set the distance when we first traverse to M? Actually, when we traverse from a set node to M, we set dist[M] and then we add M to the queue. Then when we pop M, we break.

But we might have a shorter path? We are doing BFS so we always get the minimal distance.

One more corner: what if M is in the same set as 1? Then we set dist[M]=2? Then we break when we pop M? Then we return 2//2-1=0.

But what if there is a set that contains 1 and M? Then we will set dist[M] to 2. Then when we add M to the queue, and then when we pop M, we break.

But we also have to consider: we might have other paths? But BFS will find the shortest path.

But note: we are building the graph correctly.

Now, what about performance? The total edges is 2 * (sum of A_i) which is 1,000,000? Actually, each element in a set adds two edges (one from the element to the set and one from the set to the element). So the total number of edges in the graph is 2 * (total elements) = 2 * (sum of A_i). And the total elements is at most 500,000, so the total edges is 1,000,000. Then BFS runs in O(M+N + total_edges) = O(400,000 + 1,000,000) = 1,400,000, which is acceptable.

But note: the graph has M+N nodes and 2 * (total elements) edges.

But the total elements is the sum of A_i, which is at most 500,000. So total edges is 1,000,000.

So the algorithm should run within the constraints.

But what if we have 200,000 sets? Then we are creating 200,000 set nodes. Then the adjacency list for each set node has the size of the set (A_i). So the total memory is the sum of A_i for the set nodes (for the sets) and the element nodes: for each element, we store the list of sets that contain it. But that is the same as the total elements. So the memory is O(M+N + total_elements) = O(400,000 + 500,000) = 900,000, which is acceptable.

But in Python, each list of integers takes about 8 bytes per integer? Then total memory for the graph: 
- The `adj` array has M+N+1 entries, each is a list. The total number of integers stored in all lists is 1,000,000 (the edges). Then the memory is about (M+N+1) * (overhead of list) + 1,000,000 * (size of an integer in the list). That should be acceptable for 1e6 integers.

Now, let me code accordingly.

But note: we don't need to store the sets beyond building the graph. So we can build the graph and then discard the input sets.

Edge: when M is 1? But the problem says M>=2. So we don't have to worry.

Another edge: N=0? Then we have no sets? Then we start at 1 and cannot go anywhere? Then we cannot reach M (if M>1). Then output -1. But the constraints say N>=1? The constraints: N>=1? Actually, the constraints: 1<=N<=200000. So N>=1.

But what if there is no set that contains 1? Then we cannot start: we start at 1, but we cannot go to any set? Then we never leave 1. Then dist[M] remains -1. Then output -1.

Similarly, if there's no set that contains M, then we cannot reach M.

So the algorithm handles that.

Now, what if the same element is in the same set multiple times? But the problem says: each set has distinct integers. So no duplicates.

So we are good.

Let me code accordingly.

But note: the set_node_id: we are using M+i+1. But what if M+i+1 > M+N? For i from 0 to N-1, the maximum set_node_id = M+N. So it's within the range.

But we have to be cautious: the last set node is M+N, and we have allocated `adj` and `dist` from 0 to M+N (so index M+N is the last).

So the code:

    from collections import deque

    total_nodes = M + N
    # We'll create an adjacency list for nodes 1 to total_nodes (and also 0, but we ignore 0 for elements and sets)
    adj = [[] for _ in range(total_nodes+1)]   # index 0 to total_nodes (which is M+N)

    # Build the graph: for each set i in [0, N-1]
    for i in range(N):
        arr = sets[i]
        set_node_id = M + i + 1   # set node id: M+1, M+2, ..., M+N
        # But note: if M+i+1 might exceed total_nodes? 
        # Since total_nodes = M+N, and set_node_id = M+i+1, for i from 0 to N-1: the maximum is M+N, which is total_nodes. So it's okay.
        for x in arr:
            # x is from 1 to M
            adj[x].append(set_node_id)
            adj[set_node_id].append(x)

    dist = [-1] * (total_nodes+1)   # index 0 to total_nodes
    q = deque()
    dist[1] = 0
    q.append(1)

    while q:
        u = q.popleft()
        if u == M:
            break
        for v in adj[u]:
            if dist[v] == -1:
                dist[v] = dist[u] + 1
                q.append(v)

    if dist[M] == -1:
        return -1
    else:
        return dist[M] // 2 - 1

But wait: what if M is a set node? That cannot happen because M is an element (from 1 to M), and set nodes are from M+1 to M+N. So we are safe.

But note: we are checking `if u == M` — M is the element M, which is an integer between 1 and M. So we break when we pop the element M.

We don't break when we are at a set node? Correct.

But what if the set node is M? Then we would break? But set nodes are M+1 to M+N, which are greater than M (since N>=1). So M is always an element node.

So we are safe.

Let me run with the examples to verify.

Example 2: N=1, M=2, sets = [[1,2]]
Then total_nodes = M+N=3.
adj = [[], [], [], []]  # indices 0,1,2,3
But we are going to use indices 1,2,3.

Set0: set_node_id = M+0+1 = 3.
For x=1: adj[1] = [3]
For x=2: adj[2] = [3]
adj[3] = [1,2]

dist = [-1,-1,-1,-1]
q: start at 1 -> dist[1]=0.

Then u=1: 
   for v in adj[1]: v=3 -> dist[3]=1, q=[3]
Then u=3:
   for v in adj[3]: v=1 -> dist[1]!=-1, skip; v=2 -> dist[2]=2, q=[2]
Then u=2: we break because u==M (which is 2). Then dist[2]=2, return 2//2-1=0.

Correct.

Example 3: 
N=3, M=5, sets = [[1,3],[2,4],[2,4,5]]
Set0: id=5+0+1=6
Set1: id=7
Set2: id=8

adj[1]=[6]
adj[3]=[6]
adj[2]=[7,8]
adj[4]=[7,8]
adj[5]=[8]
adj[6]=[1,3]
adj[7]=[2,4]
adj[8]=[2,4,5]

BFS:
Start at 1: dist[1]=0 -> then to 6: dist[6]=1.
Then from 6: to 1 (visited) and 3: dist[3]=2. 
Then from 3: to 6 (visited) -> no new nodes.
Then we process 3: then we have no new nodes? Then we process set nodes? But we have only set6. Then we have to process element nodes: 3. Then we are stuck? Then we never reach 5? So dist[5] remains -1 -> return -1.

Correct.

So I think this should work.

But let me check the constraints: total elements is 500,000. So the graph building is O(total elements).

But note: we are building adj as a list of lists. For each element in each set, we do two appends. So total operations 2 * (sum of A_i) <= 1,000,000, which is acceptable.

Now, what about the BFS: the queue might have up to M+N nodes, and we traverse each edge once. So O(M+N+E) = O(400,000 + 1,000,000) = 1,400,000, which is acceptable in Python.

But note: we are storing the entire graph. The memory: 
- adj: list of (M+N+1) lists. The total number of integers stored is 2*(sum of A_i) <= 1,000,000.
- dist: list of M+N+1 integers -> 400,000 integers (each 4 or 8 bytes) — about 3.2 MB for 400,000 integers? And the adj: each integer is 4 or 8 bytes? Then 1,000,000 integers: about 8 MB. So total memory is about 12 MB, which is acceptable.

So I will implement accordingly.

But note: the problem says the total sum of A_i is 500,000, so 1,000,000 edges.

Now, one more corner: what if M is 1? But M>=2, so no.

Another: what if a set contains 1 and M? Then we break at the second step (when we set dist[M]=2 and then when we pop M we break). Then return 0.

But what if we have a set that contains only 1 and M? Then we have to merge nothing? So 0 operations.

But what if we have two sets: one that contains 1 and M, and one that also contains 1 and M? Then we don't need to use the second set. The BFS will find the path that uses the first set and returns 0.

So it's correct.

I think we are ready to code.

But wait: in the example 4, we have four sets. The minimal operations is 2. The algorithm returns 2. Correct.

Let me write the code accordingly.

But note: the function signature is:

    def solve(self, N: int, M: int, sets: List[List[int]]) -> int:

So we can write:

    from collections import deque

    total_nodes = M + N
    adj = [[] for _ in range(total_nodes+1)]   # 0-indexed: indices 0 to total_nodes

    for i in range(N):
        arr = sets[i]
        set_node_id = M + i + 1   # set node id: from M+1 to M+N
        for x in arr:
            # Connect the element x to the set_node_id
            adj[x].append(set_node_id)
            adj[set_node_id].append(x)
    
    # If M is 1? But M>=2, so skip.
    dist = [-1] * (total_nodes+1)
    q = deque()
    dist[1] = 0
    q.append(1)
    
    while q:
        u = q.popleft()
        if u == M:
            break
        for v in adj[u]:
            if dist[v] == -1:
                dist[v] = dist[u] + 1
                q.append(v)
                
    if dist[M] == -1:
        return -1
    else:
        return dist[M] // 2 - 1

But let me test with the examples in code.

Example 1: 
N=3, M=5, sets = [[1,2], [2,3], [3,4,5]]

Set0: id=5+0+1=6 -> edges: 1-6, 2-6
Set1: id=7 -> edges: 2-7, 3-7
Set2: id=8 -> edges: 3-8,4-8,5-8

Then BFS:
Start: u=1 -> dist[1]=0
  adj[1]=[6] -> set dist[6]=1, q=[6]
u=6: 
  adj[6]=[1,2] -> 1 is visited; 2: dist[2]=2, q=[2]
u=2:
  adj[2]=[6,7] -> 6 is visited; 7: dist[7]=3, q=[7]
u=7:
  adj[7]=[2,3] -> 2 visited; 3: dist[3]=4, q=[3]
u=3:
  adj[3]=[7,8] -> 7 visited; 8: dist[8]=5, q=[8]
u=8:
  adj[8]=[3,4,5] -> 3 visited; 4: dist[4]=6, q=[4]; 5: dist[5]=6, q=[4,5]
Then we pop 4: nothing new? Then pop 5: u=5 -> which is M? No, M=5. So when we pop u=5, then we break? Actually, we break when u==M? Then when we pop 5, we break. Then dist[5]=6 -> output 6//2-1=2.

But note: we set dist[5]=6 when we traverse from set8 to 5. Then we add 5 to the queue. Then when we pop 5, we break.

So it's correct.

But we are adding both 4 and 5? Then we process 4 first? Then 5? But the order in the queue is FIFO: we append 4 and then 5. Then we pop 4 first, then 5. Then when we pop 5, we break. So it's okay.

But the BFS from set8: we add 3 (already visited), then 4 and 5. So the queue becomes [4,5] (if we started with [8] and then we added 4 and 5). Then we process 4: then from 4, we go to set8 (visited) and any other sets? But we don't have. Then we process 5: then we break.

So it's correct.

But we could optimize: when we set dist[5]=6, we can break immediately? But the problem: we break when we pop the node, not when we set it. Because we might have a shorter path? But in BFS, the first time we set a node is the minimal distance. So we can break as soon as we set dist[M]? Actually, no: because we are still adding to the queue. But we can check when we set it? We could do:

   for v in adj[u]:
        if dist[v] == -1:
            dist[v] = dist[u] + 1
            if v == M:
                break_out = True
                break
            q.append(v)

But then we break out of the inner loop, and then we break out of the outer? Actually, we can break out of the BFS as soon as we set M? But we have to break the BFS. We can set a flag.

But it's not necessary because the BFS will break soon when we pop M. And we don't save much.

But for performance: if we break as soon as we set M, we avoid adding many nodes. But in the worst-case, M might be the last node we set. So it's not a big saving.

But we can do:

    found = False
    while q and not found:
        u = q.popleft()
        for v in adj[u]:
            if dist[v] == -1:
                dist[v] = dist[u] + 1
                if v == M:
                    found = True
                    break   # break inner loop
                q.append(v)
        # end for
    # end while

But then we break the inner loop and then the outer? Actually, we break the inner loop and then the while loop condition fails? No, we break the inner loop and then we check the while condition again. So we break the inner loop, then the outer loop condition is checked and we break because found is True.

But we can also break the outer loop from the inner? 

Alternatively, we can check after setting v: if v==M, then we break the outer loop.

But for clarity, we can leave as is: we break when we pop M. The BFS will eventually pop M, and we break then.

But if we break when we set M, we avoid having to process the rest of the queue? But the queue might be huge. So it might be better to break immediately.

For example, in a large graph, we set M at a distance d, but then we have a lot of nodes at distance <= d that we don't need to process. So we can break as soon as we set M.

So I'll modify:

    found = False
    while q and not found:
        u = q.popleft()
        for v in adj[u]:
            if dist[v] == -1:
                dist[v] = dist[u] + 1
                if v == M:
                    found = True
                    break   # breaks the inner for-loop
                q.append(v)
        if found:
            break   # break the while loop

But then we can also avoid the flag and do:

    while q:
        u = q.popleft()
        if u == M:
            break
        for v in adj[u]:
            if dist[v] == -1:
                dist[v] = dist[u] + 1
                if v == M: 
                    # We set M, so we break both loops.
                    # But we can break the for and then the while? We can set a flag and break the for, then break the while.
                    # Alternatively, we can break the for and then break the while by setting u to M? 
                    # But we can break the for and then break the while.
                    break
                q.append(v)
        else:
            continue
        break   # if we broke the for-loop (because we set M), then break the while

But that is a bit tricky.

Alternatively, we can do:

    while q:
        u = q.popleft()
        if u == M:
            break
        for v in adj[u]:
            if dist[v] == -1:
                dist[v] = dist[u] + 1
                if v == M: 
                    # We break both loops immediately.
                    q = None   # and break? 
                    break
                q.append(v)
        else:
            continue
        break

But then we have to break two loops.

I think the flag is clearer.

But actually, we can simply break the inner loop and then break the outer loop:

    found = False
    while q and not found:
        u = q.popleft()
        if u == M:
            found = True   # but we break the while? Actually, we break the while if we pop M? 
            # But we already break the while condition? Actually, we can break the inner and then the outer? 
            # But we are popping u. If u is M, we break the while? 
            # Actually, we can break the while when we pop M, or when we set M in the inner loop.
        else:
            for v in adj[u]:
                if dist[v] == -1:
                    dist[v] = dist[u] + 1
                    if v == M:
                        found = True
                        break   # break the inner
                    q.append(v)
            if found:
                break

    if found or dist[M] != -1:   # then we have set M
        return dist[M] // 2 - 1
    else:
        return -1

But this is messy. Actually, we can check at the end: if dist[M] != -1, then we return the value. And we break the while loop either by popping M or by setting M and breaking the while. But we don't necessarily set q to empty? 

Alternatively, we can do without the flag: 

    while q:
        u = q.popleft()
        if u == M:
            break
        for v in adj[u]:
            if dist[v] == -1:
                dist[v] = dist[u] + 1
                if v == M:
                    # We have set M, so we break the while loop after this for-loop.
                    # But we can break the for-loop and then break the while-loop.
                    break
                q.append(v)
        else:
            continue
        break   # if we broke the for-loop (because we set M), then break the while

But then we break the while. Then we can check dist[M] for the value.

But note: if we set M in the inner loop, we break the for-loop and then break the while-loop. But we haven't set the entire BFS? But we have set M, so we can break.

But we have to set dist[M] and then break. Then we return.

But what if we break the for-loop for a different reason? The for-loop has a break only when we set M. So if we break the for-loop, it's because we set M.

So we can do:

    while q:
        u = q.popleft()
        if u == M:
            break
        set_M_in_this_loop = False
        for v in adj[u]:
            if dist[v] == -1:
                dist[v] = dist[u] + 1
                if v == M:
                    set_M_in_this_loop = True
                    break   # break the for-loop
                q.append(v)
        if set_M_in_this_loop:
            break

    if dist[M] == -1:
        return -1
    else:
        return dist[M] // 2 - 1

But we can avoid the flag by:

    while q:
        u = q.popleft()
        if u == M:
            break
        broke = False
        for v in adj[u]:
            if dist[v] == -1:
                dist[v] = dist[u] + 1
                if v == M:
                    broke = True
                    break
                q.append(v)
        if broke:
            break

But I think the flag is acceptable.

But for simplicity, we can just do:

    if dist[M] != -1:
        break

But we are in the inner loop? We set dist[M] and then we break the inner loop, but we still have to break the outer.

Alternatively, we can simply not break and let the BFS run. The BFS will eventually pop M and break. The queue might be large, but worst-case we have to process the entire component. But if we break as soon as we set M, we avoid processing any more nodes. This is more efficient if the graph is large and M is reached early.

But the minimal path might be found when we set M, so we can break immediately.

I think it's worth breaking immediately.

So:

    from collections import deque

    total_nodes = M + N
    adj = [[] for _ in range(total_nodes+1)]   # 0-indexed: indices 0 to total_nodes

    for i in range(N):
        arr = sets[i]
        set_node_id = M + i + 1
        for x in arr:
            adj[x].append(set_node_id)


## Final Solution

