# Solution for 898_D.md

Generated at: 2025-07-22 06:01:10
Model: deepseek-reasoner

## Reasoning Process

We are given n, m, k and a list of distinct integers (alarm times)
<PERSON><PERSON> will wake up if there exists an m-consecutive minute window [x, x+m-1] such that at least k alarms ring in that window (each alarm rings at one specific minute, so we count the alarms that have their ringing time in [x, x+m-1]).

We want the minimal number of alarms to turn off so that there is NO window of m consecutive minutes that contains at least k alarms.

Note: The alarm times are distinct.

Approach:
We cannot check every possible window [x, x+m-1] because x can be in a large range (since a_i up to 10^6, and m up to 10^6). Instead, we can try to use a sliding window over the alarm times.

However, note that the windows are defined over time (minutes) and the alarms are events at specific minutes. We can sort the alarm times. Then, we consider the sorted array of alarm times. But note: the window in time might not be aligned with the alarm events? Actually, we can use a sliding window over the sorted alarms? But the condition is about consecutive minutes, not consecutive alarms.

But note: the problem says "during some m consecutive minutes". So we are free to choose the starting minute x. However, we can note that without loss of generality, we can assume that x is such that one of the alarms is at x (or at the left boundary) because shifting the window until the next event might be sufficient. Actually, we can consider all windows that start at an alarm time? But note: it is possible that the window starts between two alarms. However, since the condition only depends on the set of alarms that fall in the window, we can consider the windows that start at an alarm time and also the windows that end at an alarm time? Actually, we can use a standard technique: we consider the events and then use a sliding window that moves with events.

But note: the constraints: n up to 200,000 and m up to 1,000,000. The alarm times are in the range [1, 10^6].

Plan:
1. Sort the alarm times. Let the sorted array be `a[0..n-1]`.
2. We want to check for every possible window of time [x, x+m-1] the number of alarms in it. However, we don't have to check every integer x. We can use a sliding window on the sorted array of alarms. Specifically, we can fix the left boundary of the window to be an alarm time and then use binary search to find the right boundary? Or we can use two pointers.

But note: the window in time is of fixed length m. So for each alarm time `a[i]`, we can consider the window starting at x = `a[i]` (and then also windows that start between `a[i]` and `a[i+1]`?).

However, we don't need to check every x. We can use the standard technique: the events that matter are the alarms. We can consider the windows that start at x such that x is in the set of alarm times? Actually, the minimal window that changes the count is when x passes an alarm. So we can use an array of events? But note: we are only concerned with the condition: no window has k alarms.

But our goal: remove minimal alarms so that no window of m consecutive minutes has k alarms.

This is equivalent to: we want to remove as few alarms as possible such that for every x, the number of alarms in [x, x+m-1] is at most k-1.

We can think greedily: when we see a window that has >= k alarms, we must remove some alarms. But which ones? And note that one alarm might be in multiple windows.

Alternatively, we can use a greedy covering strategy: traverse the alarms in sorted order and when we find a window that has too many alarms, we remove the alarms that are causing the problem? But note: we are allowed to remove arbitrary alarms.

But note: the problem is similar to "minimum removals to avoid k alarms in any window of size m".

We can use a two pointers (sliding window) to find for each window [a[i], a[i]+m-1] the number of alarms. How?
- Sort the alarms.
- Then, for each alarm time `a[i]`, we can consider the window starting at `a[i]`? Actually, the window [a[i], a[i]+m-1] will include all alarms from a[i] to a[i]+m-1]. But note: we are free to choose x arbitrarily. However, the condition must hold for every x. But if we consider every window that starts at an alarm, is that sufficient? Actually, no: consider if the window starts between two alarms. However, note that the number of alarms in [x, x+m-1] is the same as the number of alarms in [x, x+m-1] that are >= x and <= x+m-1. The function of x is piecewise constant? Actually, it only changes when x passes an alarm.

But we can use the standard method: 
  Let F(x) = number of alarms in [x, x+m-1].
Then F(x) is a step function: it increases when x passes a value such that an alarm leaves the window (when x becomes greater than an alarm, that alarm leaves) and decreases when x passes a value such that an alarm enters the window? Actually, wait: as x increases, the window moves to the right. When x increases, the left boundary moves past an alarm (so that alarm leaves) and the right boundary moves to include new alarms? 

But note: the window is [x, x+m-1]. As x increases by 1, the alarm at x (if any) leaves and the alarm at x+m (if any) enters.

Therefore, the value F(x) can be computed for all x by an events method: we can do a sweep of x from 1 to max_time (which is max(alarms)+m? since the window goes to max(alarms)+m-1). But max_time might be up to 10^6 + 10^6 = 2e6? and n is 200,000. So we can do a sweep over x in [1, max_time] and use a Fenwick tree? Or use a difference array? 

But note: the constraints: m up to 10^6, and the time range is about 2e6. So we can do:

  Let events = []
  For each alarm at time t, we know that it is active from x = t - m + 1 to x = t? Why?
      The alarm at t is in the window [x, x+m-1] if and only if x <= t <= x+m-1
        => x <= t and x >= t - m + 1.

  So the alarm is active for x in [max(1, t-m+1), t].

  We can use a difference array for x in [1, max_time] (max_time = max(alarms) + m? actually, we can set max_time = max(alarms) + m).

  Then we do:
      diff = [0]*(max_time+2)
      for each alarm t:
          L = max(1, t - m + 1)
          R = t
          diff[L] += 1
          diff[R+1] -= 1

  Then we can compute F(x) for each x from 1 to max_time by prefix sums.

  Then we want: for every x, F(x) < k. But note: we are allowed to remove alarms. How to model the removal? 

But note: we are not forced to have F(x) < k for every x? Actually, we require that there is no x for which F(x)>=k.

However, the problem is: we can remove alarms arbitrarily. We want the minimal removals such that for every x, F(x) < k.

But note: the alarms are independent. Removing an alarm at t will subtract 1 from F(x) for every x in [max(1, t-m+1), t].

So the problem becomes: we have a set of intervals (each corresponding to an alarm) and each interval [L_i, R_i] (with L_i = max(1, t_i - m + 1), R_i = t_i) and we want to remove the minimal number of alarms (intervals) such that the maximum value of F(x) over x is at most k-1.

This is a classic "hitting set" for intervals: we want to remove a set of intervals so that no point x is covered by k (or more) intervals.

But note: the intervals are of fixed length? Actually, each interval has length m? but note: if t_i - m + 1 < 1, then it starts at 1.

But the problem is known: we are to remove the minimal number of intervals so that the maximum coverage (i.e., the depth) is at most k-1.

How to solve that?
  We can use a greedy strategy: traverse x from 1 to max_time and whenever we see that F(x) > k-1, we remove some intervals that cover x? But which one? We can remove the interval that ends last? (because it will cover the most future x) - this is a standard greedy for interval coverage.

But note: we are only allowed to remove entire intervals (alarms). 

Standard algorithm for "minimum removals to reduce depth to at most k-1" for a set of intervals?

Alternatively, we can use a greedy algorithm that uses a priority queue:

  Sort the intervals by their left endpoint? Actually, we have the events by x.

  Steps:
      Sort the alarms (which are the t_i). Then each alarm gives an interval [t_i - m + 1, t_i]. But note: we don't need to build the entire F(x) array? We can use a greedy covering.

  However, note: the problem is equivalent to: we want the maximum coverage (depth) to be at most k-1. The minimal number of removals is the total number of alarms minus the maximum number of alarms we can leave without having any point covered by k alarms? Actually, no: because if we leave a set S of alarms, then for every x, the number of alarms in S that have interval covering x must be < k.

  Alternatively, we can use a greedy algorithm that removes the minimal number of alarms so that no point is covered by k alarms. This is known as the "minimum number of points to hit all sets" but in reverse? Actually, it's the same as the "minimum hitting set" for the set of "bad events" (each bad event is a set of alarms that together cover a point x that has coverage k?).

  However, note: we don't need to cover all bad events? Actually, if we remove an alarm, it breaks all bad events (sets of k alarms that together cover a point) that include that alarm.

  But this is an NP-hard problem? Actually, no: because our intervals are of fixed length? Actually, the intervals are all of the same length? and they are not arbitrary? They are defined by a fixed m and each alarm gives an interval [t_i - m + 1, t_i]. Also, the intervals are not necessarily disjoint.

  But note: the problem is equivalent to: remove the minimal number of intervals so that the maximum depth is at most k-1.

  There is a known greedy for the case k=1: then we want the maximum depth to be 0 -> we remove all intervals? But k>=1 and we are allowed to have depth up to k-1.

  Actually, we can use a greedy algorithm that uses a priority queue to remove the intervals that end the latest? But for depth more than k? 

  Specifically, we can traverse x from 1 to max_time, and we maintain the current set of intervals that are active at x. When at a point x, the current depth is d, and if d >= k, then we must remove d - (k-1) alarms from the active set? But which ones? We should remove the ones that are going to cover as many future points as possible? Actually, we want to remove as few as possible. So we remove the ones that end the latest? Why? Because they will cause the most coverage in the future.

  Algorithm:
      events: 
          For each alarm t, we have:
             event at t-m+1: add alarm (with end time t) to the active set.
             event at t+1: remove alarm (but we don't need to remove explicitly? we can use a heap for active alarms and then remove when x moves beyond the alarm's end? Actually, we can use a min heap for the end times? But we need to remove when we pass the end time? Actually, we can do:

      Steps:
        Sort the alarms. Then we can also create events:
          event_start: at L = t_i - m + 1: add alarm (and store the end time R_i = t_i)
          event_end: at R_i+1: remove alarm? But we don't need to remove at R_i+1? Actually, we are traversing x from 1 to max_time. We can use a sweep-line.

      Alternatively, we can do:

        Let active = a min-heap (priority queue) for the end times of the intervals that are currently active? Actually, we want to remove the alarms that end the latest? So we use a max-heap? Actually, we want to know the active intervals and then when we have too many (>=k) at a point x, we remove the one that ends last (so that it doesn't cover future points) -> that minimizes the number of removals.

        Steps:
          events = []
          for each alarm t:
              L = max(1, t - m + 1)
              R = t
              events.append( (L, 'start', R) )
              events.append( (R+1, 'end', R) )   # to mark the removal at x = R+1

          Sort events by x, and then for same x, do 'end' events first? Actually, we should remove an interval at x = R+1, and then add new ones? Actually, when x = R+1, the interval [L, R] no longer covers x? So we remove it.

          Then we traverse x from 1 to max_time:
              At each x, we add all intervals that start at x (so we push their end time R into a max-heap?).
              Remove all intervals that end at x? Actually, we have events for removal at x (which we set as R+1, but actually we can set removal event at R+1, then at x=R+1 we remove the interval that ends at R?).

          Then we check: the size of the max-heap (active intervals) at x is the coverage at x? Actually, we need to remove the intervals that have ended? We do that by events.

          But we can also maintain the active intervals by having a heap for the end times (min-heap for the end times to know which ones are to be removed) and a separate max-heap for the active ones? Actually, we can use one heap? But we need to remove the ended intervals lazily.

          Alternatively, we can do:

            active: a max-heap (to get the largest end time) for the active intervals? Actually, we want to remove the one that ends latest when we have to remove one? So we use a max-heap for the end times? But when an interval ends, we remove it from the heap? How to remove? We can use a lazy heap: we mark the intervals that are still active.

          Instead, we can do:

            events: sorted by x.
            active = a max-heap (we store the end time, and we want to pop the largest end time when we remove one) for active alarms.
            We also have a min-heap for the end times? to know which intervals to remove when x increases? Actually, we can use a separate heap for the removal events? 

          Actually, we can do:

            We'll create an array for events: at each x, we have:
                to_add: list of intervals (by end time) that start at x.
                to_remove: list of intervals (by end time) that end at x? Actually, we set the removal event at x = R+1? But we don't need the list, we can just remove one? Actually, we don't need to_remove list per x? We can have a min-heap for the end times to know the next removal? Or we can simply store the events and then when we process x, we remove all intervals that end at x? But we set removal event at R+1: then at x = R+1, we remove the interval that ended at R? Actually, we can do:

            We maintain a multiset of active intervals? 

          Alternatively, we can use a Fenwick tree? That might be too heavy.

        Proposed greedy:

          events: list of (position, type, end_time)
          Sort events by position.

          active = []   # max-heap for the end times of active intervals? we'll store (-end_time) to simulate max-heap with min-heap in Python.

          removals = 0

          x from 1 to max_time (we traverse events, not every integer? because events are at O(n) points? Actually, we have 2*n events).

          For each event in sorted order:
            At position x, first process removals: events of type 'end'? Actually, we have two types: 
                type: 'start' -> add the interval (with end_time = t)
                type: 'end' -> remove the interval? But wait: we set removal event at R+1. However, we don't know which interval to remove? Actually, we do: we know the end_time for the interval that ends at R? But we don't store which interval? 

          Actually, we don't need to remove by event? Because when we process x, we can first remove all intervals that have ended? How? We can have a separate min-heap for the end times? Then we can remove those that end < x? Actually, the active intervals are those with end_time >= x? 

          But note: the interval [L, R] is active for x in [L, R]. So at x, we want to remove intervals that have R < x? 

          However, we are traversing x by events. We can do:

            We have a min-heap (end_min) that holds the end times of the active intervals? and a max-heap (active_max) that holds the end times? Actually, we need both? 

          Actually, we can maintain two data structures:
            active_max: a max-heap for the end times of the active intervals (so we can remove the one with the largest end time when we need to remove an alarm)
            active_min: a min-heap for the end times? to clean up the intervals that have ended? But we can do lazy removal: when we need to know the active intervals, we first clean the active_max by removing those that have ended (i.e., end_time < current x). But how do we know which ones to remove? We can store (end_time) in the max-heap and also check the min-heap? Actually, we don't need the min-heap: we can use the min-heap for the end times to know which one is the next to end? 

          Alternatively, we can do:

            We'll have a min-heap (end_heap) for the end times of all active intervals? and a max-heap (for the end times) for the active intervals that are still present? and we remove from the max-heap when we remove an interval? 

          This is getting complicated.

        Another idea: we don't traverse every integer x? We only care about the events? Actually, the function F(x) is constant between events. The events are the starts and ends. So we only need to check at the event points? But note: the coverage F(x) changes only at events. However, the maximum coverage might occur between events? Actually, no: the coverage is constant between events? 

          Actually, when x moves from an integer to the next, the coverage changes only at events? So we can process events and then at each event we update the coverage and if at an event the coverage becomes too high, we remove some alarms? But note: the coverage at an event point might be the same as the coverage in the entire segment? Actually, the coverage is a step function: it changes only at events.

        Therefore, we can:

          events: sorted by x (each event is (x, delta)), where at L: delta = +1, at R+1: delta = -1.

          Then we can compute F(x) for each event point? Actually, we can compute the coverage at x by the prefix sum of the deltas? Then we traverse the events and we know F(x) for each segment? But we don't need the entire array? We only care when F(x) >= k? 

        However, we want to remove alarms to make F(x) < k for all x. How to compute the minimal removals?

        This is a greedy covering: we traverse x in increasing order and when F(x) >= k, we remove some alarms that are active at x. Which one? The one that ends the latest (so that it covers as few future events as possible). 

        Steps:

          events = []
          for each alarm t:
              L = max(1, t - m + 1)
              R = t
              events.append( (L, +1, R) )
              events.append( (R+1, -1, R) )

          Sort events by x.

          We maintain:
            active: a max-heap for the end times of the currently active alarms (that haven't been removed and haven't ended) -> we store the end time? Actually, we store the end time? But we need to know which alarms are still active? We'll use a heap for the end times? and we also maintain a counter for the current coverage? 

          But we also need to account for removals: when we remove an alarm, we should remove it from active and also subtract one from the coverage? 

          Actually, we can do:

            current_coverage = 0
            active = []   # max-heap: we store end times? we'll store (-end) for min-heap to simulate max? or we use a max-heap? In Python, we use heapq as a min-heap, so we store (-end) to simulate max.

            removals = 0

            events sorted by x.

            We traverse events by x (in increasing order). For each event (x, delta, R):
                But note: we have two types: +1 and -1. However, we also have removals that we did? 

          Actually, we should process events in order. For a given x, we first update the coverage: we process all events at x? 

          However, we have two types: 
              For an event (x, +1, R): we add one to coverage and push R into active (as -R for max-heap).
              For an event (x, -1, R): we subtract one from coverage? But wait: this event means that an alarm ends? But we didn't remove it? Actually, this event is for an alarm that we haven't removed? 

          But note: we might have removed the alarm earlier? So we need to mark the removals? 

          Alternatively, we can maintain a separate heap for the active alarms that we haven't removed? and then we remove the ended alarms? 

        Let me reframe:

          We'll have a list of events: 
            at x = L: we add an alarm with end time R.
            at x = R+1: we remove that alarm (if it hasn't been removed already).

          We maintain:
            active: a heap (max-heap by end time) for the active alarms that are still present (not removed and not ended) at the current x.
            current_count = 0   # we don't need to track the coverage by events? Actually, we can compute the coverage as: 
                current_count = number of alarms that have started and not ended and not been removed? 

          However, we update current_count by events? 

          Actually, we can update current_count by the events: 
            For an event (x, +1, R): current_count += 1
            For an event (x, -1, R): current_count -= 1   [if this alarm has not been removed]

          But if we remove an alarm, then we should not count it? And also, we should not process its end event? 

          How to handle removal? We can mark the alarm as removed? But we don't know which alarm? 

        Instead, we can avoid the end events for alarms that we remove? 

        How? We can use a boolean array? But n is 200,000, so we can store per alarm? But we don't have the alarms as distinct ids? We have the alarm time t, and each alarm is represented by (L, R). But note: distinct t.

        Proposed:

          We assign an id to each alarm? Actually, we don't need to: we can store in the events the end time and then when we remove an alarm, we mark it as removed? and then when we get an end event for that alarm, we skip it? 

          But how to mark? We can have a set of removed alarms? Then when we process an event (x, -1, R), we check if the alarm with end time R has been removed? But note: multiple alarms could have the same end time? Actually, no: because the alarms are distinct? and each alarm has a unique t, and R = t. So the end time R is the alarm time, which is distinct.

          So we can have:
            removed = set()   # but we are going to remove alarms, and then when we see an end event for an alarm that we removed, we skip it? 

          Steps:

            events: sorted by x. For each event, we have (x, type, R) where type is either 'add' or 'remove'. Actually, we can use:
                events.append( (L, 'add', R) )
                events.append( (R+1, 'remove', R) )

            active: a max-heap (we store R, but for max-heap we store -R) for the active alarms that have not been removed? 
            current_count = 0
            removals = 0
            removed_set = set()

            For x from min_x to max_x (by events):
                Process all events at x:
                  For an event (x, 'add', R): 
                      push (-R, R) into active?   # so that we can pop by max R? 
                      current_count += 1
                  For an event (x, 'remove', R):
                      if R not in removed_set: 
                          current_count -= 1
                      else: 
                          # if it was removed, then we skip? 
                          # but then we don't do anything? 
                          # actually, if we removed it, then we don't count it in current_count? 
                          # but note: we never added it? Actually, when we remove an alarm, we remove it from active and also subtract one from current_count? 

                But wait: when we remove an alarm, we do:
                    removals += 1
                    current_count -= 1
                    and we add R to removed_set.

                However, we remove an alarm only when we see that current_count >= k? 

            Then, after processing all events at x, we check: 
                while current_count >= k: 
                    # we must remove one alarm
                    # we choose the alarm with the largest R (because it ends the latest, so it covers the most future x) -> that is the one we want to remove? Why? Because it will reduce coverage for the longest segment? so it is the most harmful? 

                    # Pop from active: 
                    candidate = heapq.heappop(active)[1]   # because we stored ( -R, R )
                    # But note: we might have popped an alarm that has already ended? Actually, we are maintaining active: it contains alarms that are added and not removed? But we are processing events at x, and we have just updated? 

                    # However, we might have an alarm that was ended? But we have an end event? and we processed the end event? Then if we removed it from current_count, then we don't have it in active? Actually, we don't remove the alarm from the heap? 

            How to remove an alarm? 
                We remove it: 
                    removals += 1
                    current_count -= 1
                    removed_set.add(candidate)

                But note: the end event for candidate will come at candidate+1? Then when we process that event, we check if candidate is in removed_set -> if yes, we skip.

            However, we have to be careful: the alarm we remove might not be active? But we are storing in active all alarms that have been added and not removed? 

            But when we remove an alarm, we remove it from the active set? Actually, we are popping from the heap. But the heap might contain alarms that have been ended? Actually, no: because we are using the heap only for active alarms that have been added and not removed? 

            However, we have not removed the alarms that ended? We process the end events: at the end event, we remove the alarm from current_count only if it hasn't been removed? 

            But the heap 'active' is not updated for ended alarms? Actually, we leave the ended alarms in the heap? Then when we pop from the heap, we might get an alarm that has ended? 

            How to avoid that? We can use lazy removal: when we pop from the heap, we check if the alarm has been removed (by the end event) or already removed? Actually, we have a removed_set? So we can:

                while active and active[0][1] in removed_set: 
                    heapq.heappop(active)
                if active is empty? then break? 

            Then, after cleaning the heap, we pop the next candidate? 

          Steps:

            events: sorted by x (and for same x, we process 'remove' events first? or 'add' first? Actually, we should process 'remove' events first? Why? Because if at x, we have an alarm ending at x and also a new alarm starting at x, then the coverage at x is: the alarm ending at x is not active at x? Actually, the alarm ending at x is active at x? and the removal event is at x+1? 

            Actually, the event for removal is at R+1, so at x=R+1 we remove. Therefore, at x, the alarm ending at x is still active? 

            So we process events at x in arbitrary order? Actually, we should process the 'add' events first? because at the same x, the alarm that starts at x is active at x? and the alarm that ends at x is also active at x? 

            Actually, the removal event is at R+1, so at x=R+1, we remove the alarm that ended at R? So at x=R, the alarm is still active? 

            Therefore, we can process events at x in any order? 

            Algorithm:

              events = []
              for each alarm t:
                  L = max(1, t - m + 1)
                  R = t
                  events.append( (L, 'add', R) )
                  events.append( (R+1, 'remove', R) )

              sort events by x (and for same x, we can do 'remove' first? but it doesn't matter because at the same x, if we have an add and a remove, then the net effect is 0? but actually, we are going to update current_count and then check. However, we want the current_count to be accurate? 

              But note: if an alarm starts at x and an alarm ends at x (by event at x for removal? but removal event is at R+1, so at x=R+1, which is not the same as L? unless R+1 = L? which is when t - m + 1 = t+1 -> -m+1=1 -> m=-0? not possible). So no same x for add and remove? 

              Actually, it is possible: 
                  Alarm1: t1 = 5, m=3 -> L1 = 5-3+1=3, R1=5, removal at 6.
                  Alarm2: t2 = 3, m=3 -> L2=1, R2=3, removal at 4.
                  Then at x=3: we have an add (for alarm1) and a remove (for alarm2? if we consider removal at R+1=4, then not at 3). 

              So at a given x, we have either add events or remove events? 

          Therefore, we can sort events by x only.

          Steps:

            events = sorted by x (each event is (x, type, R))

            active = []   # max-heap: we store (-R, R) for the active alarms that are not removed? 
            current_count = 0
            removals = 0
            removed_set = set()

            for x, typ, R in events:
                if typ == 'add':
                    # add the alarm
                    heapq.heappush(active, (-R, R))
                    current_count += 1
                else: # typ == 'remove'
                    if R not in removed_set:
                        # this alarm is still active? then we remove it naturally (it ends)
                        current_count -= 1
                    # else: it was already removed by us? then we skip

                # Now, clean the active heap: remove any alarm that is in removed_set?
                # Actually, we don't need to clean the heap now? because we only remove from the heap when we choose to remove an alarm? 
                # But when we remove an alarm by our own, we remove it from the heap? Actually, we don't remove it from the heap? we just mark it as removed? and then when we pop, we check.

                # Then we check: if current_count >= k, then we need to remove some alarms? 
                # How many? we need to remove at least (current_count - (k-1)) alarms? But we remove one at a time? 
                while current_count >= k:
                    # Clean the active heap: remove alarms that have been marked as removed?
                    while active and active[0][1] in removed_set:
                        heapq.heappop(active)
                    if not active:
                        break
                    # Pop the alarm with the largest R (because it's the one that ends last) -> that is the top of active (since we stored -R)
                    _, R_candidate = heapq.heappop(active)
                    # We remove this alarm
                    removals += 1
                    removed_set.add(R_candidate)
                    current_count -= 1   # because we removed one active alarm

            Then output removals.

          But note: we are processing events in increasing x. The condition current_count >= k must be fixed at every x? Actually, we are removing alarms at the event x? and then we move to the next event? 

          However, note: we are processing events in increasing x. After processing events at x, we remove as many alarms as needed to bring current_count < k? 

          Why is this greedy optimal? 
            We remove the alarm that ends the latest? because that alarm is active for the longest time? so it is the most harmful? 

          This is a classic greedy for "minimum removals to reduce the maximum coverage to at most k-1". 

          Time complexity: O(n log n) because each alarm is pushed and popped at most once.

        Let me test with the examples.

        Example 1: n=3, m=3, k=2, alarms = [3,5,1] -> sorted: [1,3,5]
          Alarms:
            t=1: L = max(1,1-3+1)=max(1,-1)=1, R=1 -> events: (1, 'add', 1) and (2, 'remove',1)
            t=3: L = 3-3+1=1, R=3 -> events: (1, 'add', 3) and (4, 'remove',3)
            t=5: L = 5-3+1=3, R=5 -> events: (3, 'add',5) and (6, 'remove',5)

          Events sorted by x:
            x=1: two add events: add (1 and 3) -> current_count=2 -> which is >=k=2 -> remove one.
                active = [ (-1,1), (-3,3) ] -> max R is 3? so we remove the one with R=3? 
                removals=1, removed_set={3}, current_count becomes 1 (because we removed one) -> now current_count=1<2 -> break.

          Then we process the rest:
            x=2: remove event for R=1 -> but 1 is not removed? so current_count becomes 0.
            x=3: add event for R=5 -> current_count=1 -> no removal.
            x=4: remove event for R=3 -> but 3 is in removed_set, so skip.
            x=6: remove event for R=5 -> current_count becomes 0.

          Output removals=1 -> matches.

        Example 2: n=5, m=10, k=3, alarms=[12,8,18,25,1] -> sorted [1,8,12,18,25]
          Compute L for each:
            t=1: L=max(1,1-10+1)=max(1,-8)=1, R=1 -> events: (1,add,1) and (2,remove,1)
            t=8: L=8-10+1=-1 -> use 1, R=8 -> events: (1,add,8) and (9,remove,8)
            t=12: L=12-10+1=3, R=12 -> events: (3,add,12) and (13,remove,12)
            t=18: L=18-10+1=9, R=18 -> events: (9,add,18) and (19,remove,18)
            t=25: L=25-10+1=16, R=25 -> events: (16,add,25) and (26,remove,25)

          Process events by x:
            x=1: add 1 and add 8 -> current_count=2 -> no removal (since 2<3)
            x=2: remove 1 -> current_count=1
            x=3: add 12 -> current_count=2
            x=9: remove 8 (x=9: event for R=8) -> current_count becomes 1? and then we add 18 -> current_count=2
            x=13: remove 12 -> current_count=1
            x=16: add 25 -> current_count=2
            ... never reaches 3.

          Output removals=0 -> matches.

        Example 3: n=7, m=7, k=2, alarms=[7,3,4,1,6,5,2] -> sorted [1,2,3,4,5,6,7]
          Compute L for each:
            t=1: L=1-7+1=-5 -> 1, R=1 -> events: (1,add,1) and (2,remove,1)
            t=2: L=2-7+1=-4 -> 1, R=2 -> events: (1,add,2) and (3,remove,2)
            t=3: L=3-7+1=-3 -> 1, R=3 -> events: (1,add,3) and (4,remove,3)
            t=4: L=4-7+1=-2 -> 1, R=4 -> events: (1,add,4) and (5,remove,4)
            t=5: L=5-7+1=-1 -> 1, R=5 -> events: (1,add,5) and (6,remove,5)
            t=6: L=6-7+1=0 -> 1, R=6 -> events: (1,add,6) and (7,remove,6)
            t=7: L=7-7+1=1, R=7 -> events: (1,add,7) and (8,remove,7)

          At x=1: add 7 alarms -> current_count=7 >=2 -> we must remove 7-1=6 alarms? 
            We remove the 6 alarms with the largest R? 
            active = [ ( -1,1), (-2,2), (-3,3), (-4,4), (-5,5), (-6,6), (-7,7) ] -> the max R is 7? we remove 7? then active_count becomes 6 -> still >=2 -> remove next: 6? then 5, then 4, then 3, then 2 -> then current_count=1 -> break. 
            So we removed 6 alarms.

          Output removals=6 -> matches.

        Example 4: n=2, m=2, k=2, alarms=[1,3]
          Events:
            t=1: L=1-2+1=0 -> 1, R=1 -> events: (1,add,1) and (2,remove,1)
            t=3: L=3-2+1=2, R=3 -> events: (2,add,3) and (4,remove,3)

          Process:
            x=1: add 1 -> current_count=1 -> no removal.
            x=2: remove 1 -> current_count=0 -> then add 3 -> current_count=1 -> no removal.

          Output removals=0.

        Therefore, we can implement the greedy.

        Steps for the algorithm:

          Step 1: Precompute events:
            events = []
            for t in alarms:
                L = max(1, t - m + 1)
                R = t
                events.append( (L, 'add', R) )
                events.append( (R+1, 'remove', R) )

          Step 2: Sort events by x. Since x can be as large as max(alarms)+m (which is <= 10^6+10^6=2e6) and n=200000, we have 400000 events. Sorting is O(n log n).

          Step 3: 
            active = []   # min-heap for negative R: we store (-R, R) for max-heap
            current_count = 0
            removals = 0
            removed_set = set()   # or we can use an array of booleans? But n=200000, so set is fine.

          Step 4: iterate over events (sorted by x):
            For each event (x, typ, R):
                if typ == 'add':
                    heapq.heappush(active, (-R, R))
                    current_count += 1
                else: # typ == 'remove'
                    if R not in removed_set:
                        current_count -= 1

            Then, while current_count >= k:
                # Clean the active heap: remove the top if it has been marked as removed?
                while active:
                    # Check the top: if it has been removed, pop it
                    top = active[0][1]
                    if top in removed_set:
                        heapq.heappop(active)
                    else:
                        break
                if not active:
                    break   # but current_count should be 0? so break

                # Pop the alarm with the largest R (which is at the top of active)
                _, R_candidate = heapq.heappop(active)
                removals += 1
                removed_set.add(R_candidate)
                current_count -= 1

          Step 5: output removals.

        However, note: we are processing events at the same x? But we sorted events by x. We have to process all events at the same x? Then do the removal? 

        Actually, we process one event at a time? 

        Correction: we should process all events at the same x first? and then check? 

        Why? Because if we have multiple events at the same x, then the current_count might change? 

        Example: at x=1, we have two add events. Then after processing both, current_count=2. Then we check and remove one? 

        But in our code above, we process one event and then immediately check? We should process all events at the same x first? 

        How? We can group events by x? 

        Alternatively, we can do:

          events = sorted by x.
          Then we traverse events, but we need to process all events at the same x? 

          We can:

            i = 0
            while i < len(events):
                x0 = events[i][0]
                j = i
                while j < len(events) and events[j][0] == x0:
                    j += 1
                # process events from i to j-1
                for index in range(i, j):
                    (x, typ, R) = events[index]
                    if typ == 'add':
                        heapq.heappush(active, (-R, R))
                        current_count += 1
                    else:
                        if R not in removed_set:
                            current_count -= 1

                # Now, after processing all events at x0, check for removal
                while current_count >= k:
                    # clean active heap
                    while active and active[0][1] in removed_set:
                        heapq.heappop(active)
                    if not active:
                        break
                    _, R_candidate = heapq.heappop(active)
                    removals += 1
                    removed_set.add(R_candidate)
                    current_count -= 1

                i = j

        But note: the events at the same x might be both add and remove? But we have already computed that they are at the same x? 

        However, as argued, for a given alarm, the add and remove events are at different x? So we don't have the same R in an add and remove at the same x? 

        But different alarms: we can have multiple events at the same x? 

        Example: two alarms: one with L1=1, R1=3 -> remove event at 4; another with L2=1, R2=4 -> remove event at 5. Then at x=1: two add events.

        So we must process all events at the same x? 

        Alternatively, we can process events in sorted order without grouping? and then do the removal after each event? but that might be inefficient? because we might remove after each event? 

        Actually, the removal condition must be met after processing all events at x? because the coverage at x is the result of all events at x? 

        Therefore, we group by x.

        Complexity: grouping by x: we have at most 2*n events, so grouping is O(n). 

        Let's code accordingly.

        However, note: the events are sorted by x, so we can do:

          prev_x = None
          for event in events:
              x = event[0]
              if x != prev_x and prev_x is not None:
                  # then we have finished events at prev_x -> we can check removal condition at prev_x?
                  # Actually, we want to check after processing all events at x? 
                  # But note: we process events at x? then after processing all events at x, we check? 
                  # We are at a new x? so we have processed all events at the previous x? 
                  # Then we can do the removal at the previous x? 
                  # But wait: the coverage at the previous x might be too high? and we have already passed that x? 
              ... 

        Alternatively, we can group by x.

        Since n is 200000, we can do:

          events_sorted = sorted(events, key=lambda e: e[0])
          i = 0
          while i < len(events_sorted):
              x0 = events_sorted[i][0]
              # collect all events at x0
              events_at_x = []
              while i < len(events_sorted) and events_sorted[i][0] == x0:
                  events_at_x.append(events_sorted[i])
                  i += 1

              # process events_at_x: 
              for event in events_at_x:
                  if event[1] == 'add':
                      heapq.heappush(active, (-event[2], event[2]))
                      current_count += 1
                  else:   # 'remove'
                      if event[2] not in removed_set:
                          current_count -= 1

              # now check for removal: 
              while current_count >= k:
                  # remove the alarm with the largest R that is still active
                  while active and active[0][1] in removed_set:
                      heapq.heappop(active)
                  if not active:
                      break
                  _, R_candidate = heapq.heappop(active)
                  removals += 1
                  removed_set.add(R_candidate)
                  current_count -= 1

        This should work.

        However, note: the removal step might be heavy? because we might remove multiple alarms at one x? But each alarm is removed only once, so the total removals is at most n. The inner while loop for cleaning the heap: each element is popped at most once. The overall complexity is O(n log n).

        Let me test with the example 3: 
            events at x=1: 7 add events -> current_count=7 -> then we remove 6 alarms? 
            In the while loop: we remove 6 times? and then current_count=1.

        This is acceptable.

        But note: we are removing alarms at x=1? but the coverage at x=1 is 7, which is too high? So we remove 6 alarms. 

        However, what about other x? For example, at x=2: we have a remove event for R=1? but we have already removed R=1? Actually, we didn't remove R=1? we removed the alarms with the largest R? In our algorithm, we remove the alarms with the largest R first? so we remove R=7,6,5,4,3,2? Then at x=2: we have a remove event for R=1? and then we do:
            if R=1 not in removed_set? -> it is not? so we subtract one? but current_count is already 1? then becomes 0.

        So it works.

        But note: we did not mark the alarms that we removed? We mark them in removed_set. 

        Implementation note: we store the events as (x, type, R). The R is the alarm time (which is the end time) and also the identifier.

        We assume that the same alarm time appears only once? because the alarms are distinct.

        Therefore, we can implement.

        Let me write the code accordingly.

        However, note: the x0 we are processing? the events are at x0, then we do removals. The removals are done at the time x0? and then we move to the next x0.

        This is valid? 

        Actually, the condition must hold for every x. At x0, after processing the events at x0 and then removing the alarms, we have current_count <= k-1? Then at x0, we are safe? 

        But note: the coverage at x0 might be computed as the current_count? but we have processed events at x0? and then we remove some alarms? so the coverage at x0 is then the current_count after removals? 

        And then for the next x, we start with that current_count? 

        Yes.

        Therefore, we can output removals.

        Let me run with example 1 again with grouping:

          events = [
            (1, 'add', 1),
            (1, 'add', 3),
            (2, 'remove', 1),
            (3, 'add', 5),
            (4, 'remove', 3),
            (6, 'remove',5)
          ]

          sorted by x: 
            x=1: two adds -> current_count=2 -> then we do removal: 
                active = [(-1,1), (-3,3)] -> we remove the one with R=3? 
                removals=1, removed_set={3}, current_count=1.

          Then x=2: remove event for R=1 -> R=1 is not in removed_set? so current_count=0.
          Then x=3: add R=5 -> current_count=1 -> no removal.
          Then x=4: remove R=3 -> skip.
          Then x=6: remove R=5 -> current_count=0.

          Output 1.

        It matches.

        Therefore, we implement as described.

        Edge: when m is very large? then L = max(1, t-m+1) = 1 for all? Then events: 
            add events at x=1 for all alarms? 
            remove events at x = t+1.

        Then at x=1: we add all n alarms -> then we remove n - (k-1) alarms? 

        But wait: we require that no window has k alarms? Then we must remove until the current_count is at most k-1? so we remove n - (k-1) alarms? 

        Example: n=7, k=2 -> remove 7-1 = 6 alarms? which matches example 3.

        But note: in example 3, m=7? which is not very large? but the same.

        Another edge: k=1 -> we must remove until current_count=0? so we remove all alarms? 

        Example: n=1, m=1, k=1: 
            alarm at t: events: (t, add, t) and (t+1, remove, t)
            At x=t: we add -> current_count=1 -> we must remove 1 alarm? then we remove it -> removals=1.
            Then at x=t+1: remove event? but we already removed it? so skip.

        Output 1.

        But is that correct? 
            The window [t, t] (since m=1) contains 1 alarm? so we must remove that alarm? so yes.

        Therefore, the algorithm seems correct.

        Let me code accordingly.

        However, note: the events might be a lot? but we have 2*n events.

        Code:

          import heapq
          from typing import List

          class Solution:
              def solve(self, n: int, m: int, k: int, alarms: List[int]) -> int:
                  # If k==0, then we don't need any alarm? but k>=1 per constraints.
                  # Precompute events: list of (x, type, R)
                  events = []
                  max_x = 0
                  for t in alarms:
                      L = t - m + 1
                      if L < 1:
                          L = 1
                      R = t
                      events.append((L, 'add', R))
                      events.append((R+1, 'remove', R))
                      # update max_x if needed? for the next event? but we sort by x, so no problem.

                  # Sort events by x
                  events.sort(key=lambda e: e[0])

                  # Initialize
                  active = []   # min-heap for (-R, R) to simulate max-heap for R
                  current_count = 0
                  removals = 0
                  removed_set = set()

                  # We traverse events by grouping by x
                  i = 0
                  total_events = len(events)
                  while i < total_events:
                      x0 = events[i][0]
                      events_at_x = []
                      # Collect all events at x0
                      j = i
                      while j < total_events and events[j][0] == x0:
                          events_at_x.append(events[j])
                          j += 1
                      i = j   # update i to j

                      # Process events_at_x
                      for event in events_at_x:
                          typ = event[1]
                          R_val = event[2]
                          if typ == 'add':
                              heapq.heappush(active, (-R_val, R_val))
                              current_count += 1
                          else: # 'remove'
                              if R_val not in removed_set:
                                  current_count -= 1

                      # Now, if current_count >= k, we remove the alarms with the largest R until current_count < k
                      while current_count >= k:
                          # Remove the alarms that have been marked as removed from the heap
                          while active:
                              # Check the top
                              top_val = active[0][1]
                              if top_val in removed_set:
                                  heapq.heappop(active)
                              else:
                                  break
                          if not active:
                              break   # no active alarms? then current_count should be 0? break
                          # Pop the alarm with the largest R (which is at the top of the max-heap)
                          _, R_candidate = heapq.heappop(active)
                          removals += 1
                          removed_set.add(R_candidate)
                          current_count -= 1

                  return removals

        Let me test with n=1, m=1, k=1: 
            alarms = [t] (say t=5)
            events: 
                L = 5-1+1=5 -> (5, 'add',5)
                remove at 6: (6, 'remove',5)
            Process:
                i=0: events at x=5: [ (5,'add',5) ] -> current_count=1 -> then we enter removal: 
                    active = [(-5,5)] -> then we clean: active[0][1]=5, not in removed_set -> break inner while.
                    then we pop: R_candidate=5, removals=1, removed_set={5}, current_count=0.
                Then i moves to next: events at x=6: [ (6,'remove',5) ] -> then we check: 5 in removed_set -> skip, so current_count=0.
            Output 1.

        It's correct.

        But note: what if k=0? but k>=1 per constraints.

        We can run with the examples.

        However, note: worst-case n=200000, and we have 400000 events, and each alarm is pushed and popped at most once? so the inner while loop for removals: each removal we pop one alarm? and the total removals is at most n. The cleaning of the heap: each element is popped at most once from the heap? so total operations O(n log n).

        This should be acceptable for n=200000.

        Let me run with the provided examples.

        Example 4: n=2, m=2, k=2 -> alarms=[1,3] -> removals=0.

        Example 2: removals=0.

        Example 3: removals=6.

        Example 1: removals=1.

        Therefore, we output the code.

        But note: the events might have x0 that are not integers? no, because we computed from integers.

        We use integers.

        We'll run with the examples.

        However, note: the events are sorted by x0? and x0 can be as large as max(alarms)+m? which is 10^6+10^6=2e6? which is acceptable.

        We'll code accordingly.

        One more test: k=0? but the constraints say k>=1.

        We are done.

        However, we can try to optimize the grouping? since the events are sorted, we can avoid grouping explicitly? 

        Alternative without grouping:

          events_sorted = sorted(events, key=lambda e: e[0])
          i = 0
          n_events = len(events_sorted)
          while i < n_events:
              x0 = events_sorted[i][0]
              # Process all events at x0: without grouping list
              while i < n_events and events_sorted[i][0] == x0:
                  event = events_sorted[i]
                  i += 1
                  typ = event[1]
                  R_val = event[2]
                  if typ == 'add':
                      heapq.heappush(active, (-R_val, R_val))
                      current_count += 1
                  else:
                      if R_val not in removed_set:
                          current_count -= 1

              # Then do the removal loop

        This is more efficient in terms of memory? 

        Let me write that.

        Code without explicit grouping list:

          events_sorted = sorted(events, key=lambda e: e[0])
          active = []   # min-heap for (-R, R) for max-heap
          current_count = 0
          removals = 0
          removed_set = set()
          i = 0
          total_events = len(events_sorted)
          while i < total_events:
              x0 = events_sorted[i][0]
              # Process all events with x0
              while i < total_events and events_sorted[i][0] == x0:
                  event = events_sorted[i]
                  i += 1
                  if event[1] == 'add':
                      heapq.heappush(active, (-event[2], event[2]))
                      current_count += 1
                  else:  # 'remove'
                      if event[2] not in removed_set:
                          current_count -= 1

              # Now, remove alarms until current_count < k
              while current_count >= k:
                  # Clean the heap: remove alarms that are already removed (marked in removed_set) from the top
                  while active and active[0][1] in removed_set:
                      heapq.heappop(active)
                  if not active:
                      break
                  # Remove the alarm with the largest R
                  _, R_candidate = heapq.heappop(active)
                  removals += 1
                  removed_set.add(R_candidate)
                  current_count -= 1

          return removals

        This is efficient.

        We'll use this.

        Let me test with the examples.

        Example: n=1, m=1, k=1: 
            events_sorted = [(5, 'add',5), (6, 'remove',5)] -> 
                i=0: x0=5: process event (5, 'add',5): current_count=1.
                Then removal: current_count=1>=1 -> 
                    clean: active[0][1]=5, not in removed_set -> break inner clean.
                    pop: removals=1, removed_set={5}, current_count=0.
                Then i=1: x0=6: process event (6, 'remove',5): 5 is in removed_set? -> skip, so current_count=0.
                Then no removal.

        Correct.

        We'll submit this.

        Note: worst-case n=200000, which is acceptable for a heap and for sorting 400000 events.

        Let me check the constraints: 
            n, m, k: 1<=k<=n<=200000, m up to 1000000.
            a_i up to 1000000.

        The events list has 2*n = 400000 events. Sorting 400000 events: O(400000 * log(400000)) which is acceptable in Python.

        We run the code.

        We'll now write the code accordingly.

        Note: we use heapq, which is min-heap. We use negative to simulate max-heap.

        We'll write the code.

        IMPORTANT: We must import heapq.

        Code:

## Final Solution

### Step 1: Problem Understanding and Modeling
The problem requires determining the minimal number of alarm clocks to turn off so that no consecutive `m`-minute window contains at least `k` alarms. Each alarm rings at a distinct minute. 

**Key Insights:**
- **Alarm Coverage:** An alarm at time `t` covers the interval `[t - m + 1, t]` since any window starting in this interval will include `t`.
- **Problem Transformation:** The goal reduces to ensuring no point `x` (start of a window) has more than `k-1` alarms covering it. This is a classic interval coverage problem.
- **Greedy Removal:** When a point `x` has `≥k` alarms, remove the alarm with the latest end time (i.e., largest `t`) to minimize future coverage.

**Constraints:**
- `n` (number of alarms) up to 200,000; `m` up to 1,000,000; time values up to 1,000,000.
- Efficient solution needed due to large input sizes.

**Edge Cases:**
- `k = 1`: Must remove all alarms covering any point to ensure no window has even one alarm.
- `m` very large: All alarms cover the starting point `x=1`, requiring removal of `n - (k-1)` alarms.

### Step 2: Multiple Solution Exploration

1. **Brute Force:**
   - For each possible window `[x, x+m-1]`, count alarms. If any window has `≥k` alarms, remove one alarm and repeat.
   - **Time Complexity:** O(n * m) — infeasible for large inputs.

2. **Event-Based Sweep Line with Greedy Removal:**
   - **Core Idea:** Represent each alarm as an interval `[t - m + 1, t]`. Sweep through start points `x`, maintaining active alarms. At any `x` with `≥k` active alarms, remove the alarm ending latest.
   - **Events:** 
     - `(start, 'add', t)` at `x = max(1, t - m + 1)`
     - `(end, 'remove', t)` at `x = t + 1`
   - **Processing:** Sort events by `x`. For each `x`, process all events at that `x`, then remove alarms if `count ≥ k`.
   - **Time Complexity:** O(n log n) due to sorting and heap operations.
   - **Space Complexity:** O(n) for events, heap, and removal set.

3. **Alternative: Binary Search on Removals:**
   - Search for minimal removals `r` such that no window has `≥k` alarms. For each candidate `r`, check feasibility by scanning sorted alarms with a sliding window.
   - **Feasibility Check:** Use a Fenwick tree or segment tree to track alarms, updating as window slides. 
   - **Time Complexity:** O(n log^2 n) — may be acceptable but more complex than sweep line.
   - **Space Complexity:** O(n) for tree structures.

4. **Sliding Window on Sorted Alarms:**
   - Sort alarms. For each alarm `a[i]`, consider the window `[a[i], a[i] + m - 1]` and count alarms using binary search. If `count ≥ k`, remove alarms in the window optimally.
   - **Challenge:** Optimal removal within a window isn't straightforward; greedy removal of latest alarm in window may not prevent overlaps.
   - **Time Complexity:** O(n log n) but harder to ensure correctness across overlapping windows.

### Step 3: Optimal Solution Selection
**Selected Approach:** Event-Based Sweep Line with Greedy Removal  
- **Rationale:** 
  - Directly addresses coverage with intervals and greedy removal ensures minimal alarms are removed by targeting the most impactful (latest ending) alarm first.
  - Efficient with O(n log n) time due to sorting and heap operations, feasible for constraints.
  - Intuitive and verifiable with examples.

**Implementation:**
1. **Event Creation:** For each alarm `t`, generate:
   - `(start, 'add', t)` at `start = max(1, t - m + 1)`
   - `(end, 'remove', t)` at `end = t + 1`
2. **Sort Events:** By `x` (start/end point).
3. **Sweep Line:** Process events in order. Maintain:
   - `current_count`: Active alarms at current `x`.
   - `active`: Max-heap (using negatives) of active alarms by end time.
   - `removed_set`: Alarms already removed.
4. **Greedy Removal:** After processing all events at `x`, if `current_count >= k`, remove the latest-ending alarm until `current_count < k`.

**Optimizations:**
- **Lazy Heap Cleaning:** Skip removed alarms during heap pops.
- **Event Processing:** Group events by `x` to handle all events at a point before removal.

### Step 4: Verification and Summary
**Correctness Verification:**
- **Example 1:** `[1, 3, 5]`, `m=3`, `k=2` → Remove alarm at 3. Output: 1.
- **Example 2:** `[1, 8, 12, 18, 25]`, `m=10`, `k=3` → No removal. Output: 0.
- **Example 3:** `[1, 2, 3, 4, 5, 6, 7]`, `m=7`, `k=2` → Remove 6 alarms. Output: 6.
- **Example 4:** `[1, 3]`, `m=2`, `k=2` → No removal. Output: 0.

**Key Insights:**
- Transforming the problem into interval coverage allows efficient sweep line solution.
- Greedy removal of latest-ending alarms minimizes removals by reducing future coverage.

**Further Optimization:**
- **Heap Management:** Use a balanced BST for O(1) max extraction, but heap suffices for constraints.
- **Memory:** Use a boolean array instead of a set for `removed` if alarm times are bounded.

**Solution Code:**
```python
import heapq
from typing import List

class Solution:
    def solve(self, n: int, m: int, k: int, alarms: List[int]) -> int:
        events = []
        for t in alarms:
            start = max(1, t - m + 1)
            events.append((start, 'add', t))
            events.append((t + 1, 'remove', t))
        
        events.sort(key=lambda e: e[0])
        
        active = []
        current_count = 0
        removals = 0
        removed_set = set()
        i, total_events = 0, len(events)
        
        while i < total_events:
            x0 = events[i][0]
            while i < total_events and events[i][0] == x0:
                x, typ, R_val = events[i]
                i += 1
                if typ == 'add':
                    heapq.heappush(active, (-R_val, R_val))
                    current_count += 1
                else:
                    if R_val not in removed_set:
                        current_count -= 1
            
            while current_count >= k:
                while active and active[0][1] in removed_set:
                    heapq.heappop(active)
                if not active:
                    break
                _, R_candidate = heapq.heappop(active)
                removals += 1
                removed_set.add(R_candidate)
                current_count -= 1
                
        return removals
```

**Explanation:**
1. **Event Creation:** Each alarm generates two events: one when it starts covering a window and one when it stops.
2. **Sweep Line:** Process events in chronological order. At each point `x`, update active alarms and handle removals if coverage exceeds `k-1`.
3. **Greedy Removal:** Remove the latest-ending alarm to minimize coverage in future windows. This ensures minimal removals while meeting the constraint. The algorithm efficiently processes events and maintains active alarms using a max-heap.