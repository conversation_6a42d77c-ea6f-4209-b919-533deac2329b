# Solution for abc318_b.md

Generated at: 2025-07-22 06:30:25
Model: deepseek-reasoner

## Reasoning Process

Alright, so I've got this problem about calculating the total area covered by one or more rectangular sheets on a coordinate plane. Each sheet is defined by four integers: A_i, B_i for the x-axis (from A_i to B_i) and C_i, D_i for the y-axis (from C_i to D_i). The constraints say that N is between 2 and 100, and all coordinates are integers between 0 and 100. 

First, I need to understand the problem. We're to find the union area of N rectangles. Since the rectangles can overlap, simply summing up individual areas would count overlapping regions multiple times, which isn't what we want. So, the challenge is to account for overlaps and only count each point once.

I remember that there are several ways to solve this. Let me think through the possible approaches.

**Approach 1: Brute Force with Grid Marking**
Given that the coordinates are integers and the maximum value is 100, we can create a 2D grid (an array) representing the plane. The grid would cover from x=0 to x=100 and y=0 to y=100. Then, for each rectangle, we mark all the cells (pixels) that it covers. After processing all rectangles, we can count the number of cells that are marked at least once, which gives the total area.

Since the grid is 101x101 (because from 0 to 100 inclusive, that's 101 points), the total cells are 10201. For each rectangle, we might need to iterate over its area. The worst-case rectangle could be 100x100, which is 10,000 cells. With N up to 100, the total operations could be 100 * 10,000 = 1,000,000, which is acceptable in Python for this problem size.

However, this might be a bit inefficient for the upper limits, but given that 100*100*100 is 1e6, which is acceptable in Pyton. So, we can do:

1. Create a 2D list (grid) of size 101x101, initialized to False (or 0).
2. For each rectangle (A_i, B_i, C_i, D_i):
   - For x from A_i to B_i - 1? Wait, note the problem: each sheet covers A_i <= x <= B_i and C_i <= y <= D_i. But in the grid, if we have integer coordinates, each point (x,y) is a cell. However, note that the area is continuous, but we are discretizing. Actually, the area of a rectangle is (B_i - A_i) * (D_i - C_i). But when we mark the grid, we have to mark each integer coordinate point that lies in the rectangle.

But wait: the problem says "exactly the region satisfying A_i <= x <= B_i and C_i <= y <= D_i". Since the coordinates are integers, we can represent each unit square? Or each point? Actually, the area is defined over the continuous plane, but we are going to discretize by unit squares. 

Alternatively, we can think of the grid as having unit squares. Each unit square can be identified by its lower-left corner (i, j) meaning the square [i, i+1) x [j, j+1). But the problem uses integer coordinates and the area is an integer. Actually, the constraints say that all input values are integers and the area is an integer. So, we can use a grid of unit squares.

But note: the rectangle from A_i to B_i (inclusive) actually covers the integer points from A_i to B_i? But the area is (B_i - A_i) * (D_i - C_i). However, if we consider the grid of points, the area of the rectangle is (B_i - A_i) * (D_i - C_i). But wait, if the rectangle goes from A_i to B_i inclusive, that's B_i - A_i + 1 in length? Actually, no. The problem states: "each side" meaning the region from x=A_i to x=B_i. The length in x is (B_i - A_i), and similarly in y is (D_i - C_i). So the area is (B_i - A_i) * (D_i - C_i). 

But when we mark the grid, if we consider each cell (i, j) as a unit square from (i, j) to (i+1, j+1), then the rectangle [A_i, B_i] x [C_i, D_i] would cover the cells where x in [A_i, B_i) and y in [C_i, D_i)? Or inclusive? Actually, if we have integer coordinates and the rectangle [A_i, B_i] x [C_i, D_i] in continuous space, each unit square that has its lower-left corner (x, y) such that A_i <= x < B_i and C_i <= y < D_i is entirely inside the rectangle. Then the area is the number of such unit squares, which is (B_i - A_i) * (D_i - C_i). 

But wait, the problem says "exactly the region" with inclusive bounds? Actually, the area of a rectangle with integer coordinates from A_i to B_i inclusive is (B_i - A_i + 1) * (D_i - C_i + 1)? But the constraints say A_i < B_i and the example inputs don't have +1.

Looking at Example 1: 
First rectangle: 0 5 1 3 -> area = (5-0)*(3-1)=5*2=10.
Second: 1 4 0 5 -> (4-1)*(5-0)=3*5=15.
Third: 2 5 2 4 -> (5-2)*(4-2)=3*2=6.
But total union is 20. And 10+15+6=31, which is more than 20. So the area is computed without +1.

Therefore, we can model the plane as a grid of unit squares, where the square at (i, j) (for integers i from 0 to 99, j from 0 to 99) represents the square [i, i+1) x [j, j+1). Then, the rectangle [A_i, B_i] x [C_i, D_i] would cover the unit squares (x, y) for which A_i <= x < B_i and C_i <= y < D_i. 

So, we can create a grid of size 100x100? Because the maximum x is 100, so x from 0 to 99, and same for y. Actually, the x coordinates go from 0 to 100, so the unit squares along x are from 0 to 99 (since the square [99,100) is the last one). Similarly for y.

So, steps for brute force:
1. Create a 2D array `grid` of size 100x100, initialized to False.
2. For each rectangle in the input:
   - For x from max(0, A_i) to min(100, B_i) - 1? Actually, we only go from A_i to B_i - 1? Because the unit square at x-coordinate x covers [x, x+1). So the rectangle from A_i to B_i covers x from A_i to B_i - 1. Similarly for y from C_i to D_i - 1.
   - But note: if A_i is 100, then we skip? Because then x from 100 to B_i-1 would be invalid. Similarly, if B_i is 100, then we go from A_i to 99. So we can do:
        for x in range(max(0, A_i), min(100, B_i)):
        for y in range(max(0, C_i), min(100, D_i)):
            grid[x][y] = True
3. Then, after all rectangles, we count the number of True in the grid.

But wait, what if the rectangle has A_i=0, B_i=100? Then x from 0 to 99 (100 units). Similarly, if A_i=99, B_i=100, then x from 99 to 99 (only one row). So that matches the area (100 units for the full width).

This should work. The grid size is 100x100=10,000, and for each rectangle we iterate over (B_i - A_i) * (D_i - C_i) unit squares. In worst-case, a rectangle could be 100x100, which is 10,000 iterations. With 100 rectangles, worst-case 1e6 iterations, which is acceptable.

But let me test with the examples.

Example 2: two rectangles [0,100]x[0,100]. Each would mark all 100x100=10,000 squares. After both, the grid has 10,000 True. Then output 10,000. Correct.

Example 1: 
Rectangle1: (0,5,1,3) -> x from 0 to 4 (5 units), y from 1 to 2 (2 units) -> 10 squares.
Rectangle2: (1,4,0,5) -> x from 1 to 3 (3 units), y from 0 to 4 (5 units) -> 15 squares.
Rectangle3: (2,5,2,4) -> x from 2 to 4 (3 units), y from 2 to 3 (2 units) -> 6 squares.

But the union: we have to mark them and count. How many total? The grid is 100x100 but we only care about x in [0,5) and y in [0,5). 

Let me draw a 5x5 grid (x=0..4, y=0..4) and mark:

Rect1: x0-4, y1-2: so rows 1 and 2 (for all x0 to x4) -> that's the entire row1 and row2.
Rect2: x1-3, y0-4: so columns 1,2,3 (x1 to x3) for all y0 to y4 -> so a 3x5 rectangle.
Rect3: x2-4, y2-3: so columns 2,3,4 (x2 to x4) and row2 only? (y2 to y3: only row2).

Now, counting the union:

- For y0: only rect2: columns 1,2,3 -> 3
- y1: rect1: entire row (x0-4: 5) and rect2: x1-3 (but already included) -> so 5
- y2: rect1: entire row (5), rect2: x1-3 (overlap), rect3: x2-4 (overlap) -> 5
- y3: rect2: x1-3 (3) and rect3: x2-4 (but note: x2 and x3 are in both, but also x4 from rect3) -> so x1, x2, x3, x4? Actually, rect2: x1, x2, x3; rect3: x2, x3, x4. So union: x1, x2, x3, x4 -> 4.
- y4: only rect2: x1-3 -> 3.

Total: 3+5+5+4+3 = 20. Correct.

But wait, in our grid, we have 5 rows? Actually, in the y direction: we have y0, y1, y2, y3, y4? That's 5 rows? But the grid is from y=0 to y=4? And each row is a unit in y? Then yes, 5 rows.

But in the grid we built, we have 100x100, so for this example, we are only considering x from 0 to 4 and y from 0 to 4. The rest is 0. So the count is 20. Correct.

So the brute force grid method is straightforward and should work.

But is there a better way? Because if the constraints were larger (like coordinates up to 10^6), we wouldn't use a grid. But here the max coordinate is 100, so it's acceptable.

However, let me think of other methods for completeness.

**Approach 2: Line Sweep and Interval Merging**
We can use a sweep line algorithm. We can sweep over the x-axis and then for each x-interval, we merge the y-intervals.

Steps:
1. Collect all x coordinates (A_i and B_i) and sort them. Then, we consider the intervals between consecutive x coordinates.
2. For each x-interval [x_i, x_{i+1}), we consider all rectangles that cover this x-strip. Then, we project the y-intervals of these rectangles and merge them. The length in x for the strip is (x_{i+1} - x_i), and the merged y-intervals give the total y-length for the strip. Then, the area for the strip is (x_{i+1}-x_i) * (total y-length).

This is a standard method for union area of rectangles. The steps:

- Create events: for each rectangle, at x=A_i, add the y-interval [C_i, D_i) as an opening event; at x=B_i, add a closing event for the same interval.
- Then, as we sweep x from left to right, we maintain a data structure of active y-intervals. For each x-interval between two consecutive x coordinates, we merge the active intervals and compute the total y-length.

But note: the events are at integer x? Actually, the x coordinates are integers. So we can collect all unique x coordinates, sort them, and then consider each segment.

But the intervals: we have to be careful. The events are at the boundaries: at x=A_i we start including the rectangle, at x=B_i we remove it. Then, the region between x_i and x_{i+1} is a constant set of active y-intervals.

Steps:
1. Create events: a list of tuples (x, type, y1, y2). Type 0 for open, 1 for close? But we can do: for each rectangle, event (A_i, +1, (C_i, D_i)) and (B_i, -1, (C_i, D_i)).
2. Sort events by x-coordinate. Then, traverse x from min to max.
3. We maintain a data structure for active y-intervals. But when we add or remove an interval, we update the active set and then merge the intervals to compute the total length in y.

But how to compute the total y-length? We can maintain a list of current intervals and then merge them. But each time we add or remove, we have to update the list and then merge. Alternatively, we can use a Fenwick tree or segment tree? But since the y-coordinates are integers and only from 0 to 100, we can use a boolean array for y? Actually, we can use a difference array for y.

Alternatively, since y is small (0 to 100), we can use an array `cover` of size 101 (for y from 0 to 100) that counts how many rectangles cover each unit segment in y? Then, the total y-length is the number of y units that have a positive count.

But note: the unit segments: for y, we have segments [0,1), [1,2), ... [99,100). So we need 100 segments. We can use an array `count` of length 100 (for y from 0 to 99) representing the segment [y, y+1). Then, when we add an interval [C_i, D_i), we do:

   for y_seg in range(C_i, D_i):
        count[y_seg] += 1

But we can use a difference array to update the counts in O(1) per event? Actually, we can use a lazy method: for each event, we update the entire active set and then scan the entire y-axis to compute the total length. Since the y-axis is 100 units, and events are 2*N (200 events), and each event we scan 100 units, then total operations 200*100=20,000, which is acceptable.

But actually, we don't need to scan for each event, only when the active set changes and we are about to compute the area for the next x-interval? Actually, we can do:

- We have events sorted by x. Then, we traverse the x-axis in increasing order. For each x-coordinate where an event occurs, we process all events at that x, updating the count array (for y segments). Then, if the next event is at x_next, the x-interval length is (x_next - x_current). Then, we compute the total y-length: the number of segments in y that have count > 0. Then add (x_next - x_current) * (total y-length) to the area.

But note: we process events at x_i, then the active set is updated, and then we use that active set for the entire interval [x_i, x_next) until the next event.

Steps:
1. Preprocess:
   - events = []
   - For each sheet (A, B, C, D):
        events.append( (A, C, D, 1) )   # start event: add the y-interval [C, D)
        events.append( (B, C, D, -1) )  # end event: remove the y-interval
   - Sort events by x-coordinate. If same x, then we should process removals first? Actually, at x=B, the rectangle does not cover the x=B? Because our intervals are [A, B) and [C, D). So at x=B, we remove, and then the interval [x_current, x_next) is from the current x to the next event, but at x=B we remove and then the next segment starts at B. So it's okay to process all events at the same x arbitrarily? Actually, if we have an add and remove at same x? Then we should process the remove first? Or it doesn't matter because the segment [x_i, x_i) has length 0? Actually, the events are at the same x, and the segment we compute is from the previous event to the current event? Actually, we are going to traverse the events and compute the segment between the previous event and the current event? 

Wait, the algorithm:

   events_sorted = sorted(events, key=lambda e: e[0])
   total_area = 0
   current_x = 0
   count = [0]*100   # for y segments 0 to 99
   # But note: the y-intervals are [C, D) meaning they cover y segments from C to D-1.

   Then, we traverse events. We need to keep track of the last event x. Actually, we can:

   last_x = 0
   for event in events_sorted:
        x = event[0]
        # The segment from last_x to x: if last_x < x, then we compute the area for [last_x, x)
        if last_x < x:
            total_y = number of indices i in [0,99] such that count[i] > 0
            total_area += (x - last_x) * total_y
        # Then update the event: 
        y_start = event[1]
        y_end = event[2]
        type = event[3]   # +1 or -1
        # Update the count array for y from y_start to y_end-1
        for y in range(y_start, y_end):
            count[y] += type   # which is either +1 or -1
        last_x = x

   But wait, what about the last segment? After the last event, we don't have any more events. But the last event is at x=100? Because the maximum B_i is 100. Then the segment [last_x, x) is computed at each event. After the last event, we break. And last_x becomes 100? Then we don't need to do anything.

   However, we must ensure that we process events at the same x together? Because if multiple events at same x, we should compute the area only once after updating all events at that x.

   So we can group events by x:

        events_sorted = sorted(events, key=lambda e: e[0])
        last_x = 0
        i = 0
        while i < len(events_sorted):
            x = events_sorted[i][0]
            if x > last_x:
                # Compute the area for [last_x, x)
                total_y = sum(1 for c in count if c > 0)   # or we can precompute and update incrementally?
                total_area += (x - last_x) * total_y
            # Process all events at x
            j = i
            while j < len(events_sorted) and events_sorted[j][0] == x:
                # update the count array for this event
                _, y_start, y_end, delta = events_sorted[j]
                for y in range(y_start, y_end):
                    count[y] += delta
                j += 1
            last_x = x
            i = j   # move to next distinct x

   But note: the y_start and y_end: the event has (x, y_start, y_end, delta). And we are updating the count array for each y segment from y_start to y_end-1? Actually, the event's y_start and y_end are the original C_i and D_i? Then we should use [y_start, y_end) meaning we update y segments from y_start to y_end-1.

   But wait: if a rectangle has C_i=0, D_i=100, then we update y from 0 to 99? Correct.

   However, the problem: if an event has y_end=100, then we update from y_start to 99. So that's correct.

   Also, if y_start=100, then range(100, y_end) is empty? So no update. So safe.

   The time complexity: events are 2*N, which is 200. For each event, we update (D_i - C_i) segments. In worst-case, one event updates 100 segments. Then total operations 200 * 100 = 20,000. Then we also compute total_y for each distinct x. How many distinct x? At most 200? And computing total_y by scanning 100 segments: 200 * 100 = 20,000. So total 40,000 operations, which is efficient.

   But we can optimize the total_y computation: we can maintain a running total of the total y-length. That is, when we update the count array, we can also update a variable `total_y` by checking which segments change from 0 to 1 (adding 1) or from 1 to 0 (removing 1). 

   Steps for optimized update:

        total_active = 0   # total length in y (number of segments that are covered)
        count = [0]*100   # for 100 y segments

        Then, when we update a segment y: 
            old = count[y]
            count[y] += delta
            new = count[y]
            if old == 0 and new == 1:
                total_active += 1
            elif old == 1 and new == 0:
                total_active -= 1

        Then, we don't need to scan the entire array each time.

   But note: if we update multiple segments at the same event, we can do:

        For an event (x, y_start, y_end, delta):
            for y in range(y_start, y_end):
                old = count[y]
                count[y] += delta
                new = count[y]
                if old == 0 and new == 1:
                    total_active += 1
                elif old == 1 and new == 0:
                    total_active -= 1

        Then, when we compute the area for the segment [last_x, x), we use total_active.

   Then, we don't need to scan the entire y-axis for each distinct x. 

   The inner loop for one event: worst-case 100 operations per event, total 200*100=20,000.

   This is acceptable.

   Let me test with Example 1:

   Sheets:
     1: (0,5,1,3) -> events: (0, 1, 3, +1) and (5, 1, 3, -1)
     2: (1,4,0,5) -> events: (1,0,5,+1) and (4,0,5,-1)
     3: (2,5,2,4) -> events: (2,2,4,+1) and (5,2,4,-1)

   Events sorted by x:
        x=0: (0,1,3,+1)
        x=1: (1,0,5,+1)
        x=2: (2,2,4,+1)
        x=4: (4,0,5,-1)
        x=5: (5,1,3,-1) and (5,2,4,-1)

   Now, we start with last_x=0, total_active=0.

   At x=0: 
        Process event: update y from 1 to 2 (i.e., y=1 only? Because range(1,3) -> y=1,2? Actually, no: range(1,3) in Python: 1,2. So two segments: y1 and y2.
        For y=1: count[1] becomes 1 -> total_active becomes 1? Then 2? Actually, we update one by one: 
            y=1: old=0, new=1 -> total_active +=1 -> 1
            y=2: old=0, new=1 -> total_active +=1 -> 2
        Then, we compute the area for the segment from last_x=0 to x=0? But last_x=0, and current x=0, so we skip (x-last_x=0).
        Then set last_x=0? Then we process the next event? Actually, we process all events at x=0, then set last_x=0? Then we move to next x.

   Then we see x=1: 
        The segment from last_x=0 to x=1: length=1-0=1. Then total_active=2? So area += 1*2 = 2.
        Then process event at x=1: add [0,5): y from 0 to 4 (segments 0,1,2,3,4). 
            For y in [0,1,2,3,4]:
                y0: old=0 -> new=1 -> total_active +=1 -> now 3
                y1: old=1 -> new=2 -> no change to total_active? Because we only care about positive? Actually, we only care if it becomes 1 (from 0) or becomes 0 (from 1). So here, for y1: old=1, new=2 -> no change. Similarly for y2: no change.
                y3: old=0 -> new=1 -> total_active becomes 4
                y4: old=0 -> new=1 -> total_active becomes 5.
        Then set last_x=1.

   Then at x=2:
        Segment from last_x=1 to x=2: length=1. total_active=5 -> area += 1*5=5. Now total area=2+5=7.
        Then process event at x=2: add [2,4): y=2,3.
            y2: old=2 -> new=3 -> no change.
            y3: old=1 -> new=2 -> no change.
        So total_active remains 5. Then last_x=2.

   Then at x=4:
        Segment from 2 to 4: length=2. total_active=5 -> area += 2*5=10. Now total=17.
        Process event at x=4: remove [0,5): update y0 to y4: 
            y0: old=1 -> new=0 -> total_active -=1 ->4
            y1: old=2 -> new=1 -> no change
            y2: old=3 -> new=2 -> no change
            y3: old=2 -> new=1 -> no change
            y4: old=1 -> new=0 -> total_active -=1 ->3
        Then last_x=4.

   Then at x=5: 
        Segment from 4 to 5: length=1. total_active=3 -> area += 1*3=3. Total area=17+3=20.
        Then process events: two events: 
            Remove (5,1,3): [1,3) -> y=1,2
                y1: old=1 -> new=0 -> total_active=2
                y2: old=2 -> new=1 -> no change
            Then remove (5,2,4): [2,4) -> y=2,3
                y2: old=1 -> new=0 -> total_active=1
                y3: old=1 -> new=0 -> total_active=0
        Then last_x=5.

   Then we break. Area=20. Correct.

   So this method works.

**Approach 3: Inclusion-Exclusion Principle**
But for union of rectangles, inclusion-exclusion would be:

   S = sum single areas - sum intersections of two + sum intersections of three - ... 

But the formula is:

   |A1 ∪ A2 ∪ ... ∪ An| = Σ|Ai| - Σ|Ai∩Aj| + Σ|Ai∩Aj∩Ak| - ... 

But the problem: N is up to 100, so the number of terms is 2^100, which is too big. So we cannot use inclusion-exclusion for N=100.

Therefore, this approach is not feasible.

**Comparison of Approaches**

1. **Brute Force Grid**: 
   - Time: O(N * (B_i-A_i)*(D_i-C_i)) worst-case 100*100*100 = 1e6. 
   - Space: O(100*100) = 10,000.
   - Simple to implement.

2. **Line Sweep**:
   - Time: O(N * (max_coordinate)) for the y-axis. Here, 200 events * 100 = 20,000.
   - Space: O(max_coordinate) for the count array (100) and events list (200).
   - More efficient in terms of space and might be faster in worst-case? But 1e6 vs 20,000: actually 1e6 is 1 million, which in Python might be acceptable. But 20,000 is better.

But note: in the brute force grid, we are iterating over each rectangle's area. The worst-case rectangle is 100x100, which is 10,000 per rectangle. For 100 rectangles, 1e6. The line sweep is 20,000. So line sweep is better in worst-case time.

However, the constraints are small so both are acceptable. But let's see: N=100, and worst-case each rectangle is 100x100. Then brute force grid does 100*100*100=1e6, which in Python might run in a fraction of a second. But for clarity, the line sweep might be more efficient.

But the problem says coordinates from 0 to 100. So the grid is fixed at 100x100. Actually, we can pre-allocate a 100x100 grid. Then for each rectangle, we iterate over the x in [A_i, B_i-1] and y in [C_i, D_i-1]. The worst-case is a full rectangle: 100x100=10,000 per rectangle. For 100 rectangles: 100*100*100 = 1e6. But note: the grid is 100x100=10,000 cells. Actually, we are iterating for each rectangle over its own area. So it's O(N * (B_i-A_i)*(D_i-C_i)). The worst-case when all rectangles are full: 100 * (100*100) = 1e6. But 1e6 is acceptable in Python.

But the line sweep is 20,000 operations. So it's more efficient.

However, the brute force grid is simpler to code and less error-prone. 

But I think both are acceptable. Let me see the constraints: N>=2 and <=100, and coordinates 0 to 100. So worst-case 1e6 iterations for grid method is acceptable.

But for the sake of learning, I'll implement both? But the problem asks for one optimal solution.

**Optimal Solution Selection**

Given the constraints, both methods are acceptable. The grid method is simpler to implement and easier to understand. The line sweep method is more efficient in worst-case (20k vs 1e6) but the difference might be negligible for N=100. 

However, in Python, 1e6 iterations might take around 0.1 seconds, which is acceptable. But the line sweep is more efficient and scales better if the coordinates were larger (but in this problem, they are fixed at 100). So for this problem, I think the grid method is simpler.

But let me consider: what if we had larger coordinates? Then the grid method would not work. But the constraints are fixed. So I can choose the grid method for simplicity.

But the problem says: "It can be proved that S is an integer under the constraints." and we are using integer coordinates. So the grid method is natural.

However, the line sweep method is also straightforward and efficient. Since the y-axis is small, the line sweep is efficient. 

I think I'll go with the grid method because it's straightforward and the constraints are small.

But let me write the grid method code:

Steps for grid method:
   grid = [[False]*100 for _ in range(100)]
   for each sheet in sheets:
        A, B, C, D = sheet
        for x in range(A, B):   # but B might be >100? Then min(B,100)
            for y in range(C, D):
                if x < 100 and y < 100:   # because the grid is 100x100 for [0,100) in both axes
                    grid[x][y] = True
        # But note: if B is 100, then x goes from A to 99. Similarly for D.
   Then, count = 0
   for x in range(100):
        for y in range(100):
            if grid[x][y]:
                count += 1
   return count

But wait: what if the rectangle has A=100? Then the range(A, B) is range(100, B). Since B>100? But the constraints say B_i<=100. So if A=100, then B must be 100? But the constraint A_i < B_i, so A=100 and B=100 is not allowed. So if A=100, then the rectangle is invalid? Actually, A_i < B_i and A_i>=0, B_i<=100. So A=100 is possible only if B_i>100? But B_i<=100. So A_i must be less than 100. Similarly, if A_i=99, then B_i could be 100. Then we iterate x in [99,100): which is only x=99.

Similarly, for y: if C_i=99, D_i=100, then we iterate y in [99,100) -> y=99.

So we can simply do:

   for x in range(A, min(B, 100)): 
        for y in range(C, min(D, 100)): 
            grid[x][y] = True

But note: if B>100, we use min(B,100). But the constraint says B_i<=100. So we don't need the min? Actually, the input constraints say 0<=A_i<B_i<=100. So B_i is at most 100. Then if A_i=99, B_i=100 -> we iterate x from 99 to 99 (because range(99,100) -> only x=99). Similarly, if B_i=100, then we go to 100, but then the loop condition is x<B_i -> x<100, so x from 99 to 99.

But in Python, range(A, B) goes from A to B-1. So:

   A=99, B=100: range(99,100) -> [99]
   A=100, B=100: range(100,100) -> empty.

But the constraint A_i < B_i, so A_i=100 is not possible because then B_i>100, but B_i<=100. So A_i is at most 99? Because if A_i=99, then B_i=100. So A_i from 0 to 99.

Similarly, C_i from 0 to 99.

Therefore, we can simply do:

   for x in range(A, B):
        for y in range(C, D):
            grid[x][y] = True

Because when B=100, the x will go from A to 99. And when D=100, y goes from C to 99.

So the grid is 100x100 (indices 0 to 99). 

But what if A_i is 100? Then the loop does nothing. But as argued, A_i cannot be 100 because then B_i>100 is not allowed. So safe.

So the code for grid method:

    grid = [[False]*100 for _ in range(100)]
    for rect in sheets:
        A, B, C, D = rect
        for x in range(A, B):
            for y in range(C, D):
                grid[x][y] = True
    total = 0
    for x in range(100):
        for y in range(100):
            if grid[x][y]:
                total += 1
    return total

Test with example 2: two rectangles [0,100,0,100]. Then for each rectangle, x from 0 to 99, y from 0 to 99. Then we mark all 100x100=10000. Then return 10000.

Example 1: as we calculated earlier, returns 20.

Example 3: 
Input: 
    3
    0 1 0 1
    0 3 0 5
    5 10 0 10

Compute:
First rectangle: [0,1) x [0,1) -> only (0,0). Area=1.
Second: [0,3) x [0,5) -> x:0,1,2; y:0,1,2,3,4 -> 3*5=15.
Third: [5,10) x [0,10) -> x:5,6,7,8,9; y:0,1,...,9 -> 5*10=50.

But are they overlapping? 
First and second: overlap at (0,0) which is already counted. 
Second and third: no overlap because second has x<3, third has x>=5. 
So total union: 1+15+50 = 66? But expected output is 65.

Wait, why 65? What is the union? 

First rectangle: only the unit square at (0,0) -> area 1.
Second rectangle: [0,3) x [0,5) -> 15 squares.
Third rectangle: [5,10) x [0,10) -> 50 squares.

No overlaps? Then total 1+15+50=66. But expected 65.

Wait, the input is:

    0 1 0 1
    0 3 0 5
    5 10 0 10

But note: the third rectangle: [5,10) x [0,10) -> that's 5 units in x (5,6,7,8,9) and 10 units in y? But the grid is only from y=0 to y=99 (since D_i=10, but our grid is 100x100? Actually, we have grid from 0 to 99 in both. So for the third rectangle, we do:

   x from 5 to 9 (inclusive) -> 5,6,7,8,9 -> 5 units.
   y from 0 to 9 (because D_i=10, so range(0,10) -> 0 to 9 -> 10 units.

So 5*10=50.

But the entire union: 1+15+50=66. But expected 65.

Wait, the example output is 65. So what's missing? 

I think I see: the second rectangle is [0,3) x [0,5) -> which is 3x5=15, and the first rectangle is [0,1) x [0,1) -> which is a part of the second rectangle? So the first rectangle is entirely contained in the second. Then the union is 15 (from second) plus 50 (from third) = 65. 

But the first rectangle is [0,1) x [0,1) -> which is the same as the part of the second rectangle at [0,1) x [0,1). So the union of first and second is 15. Then adding the third: 15+50=65.

So the grid method: when we mark the first rectangle: (0,0) becomes True. Then the second rectangle: we mark from x=0,1,2 and y=0,1,2,3,4. Then (0,0) is already True, so it remains True. Then the third rectangle: we mark from x=5 to 9 and y=0 to 9. Then we count: the total True cells: 

   The second rectangle: 3*5=15 cells? 
   The third rectangle: 5*10=50 cells? 
   But the first rectangle is already included in the second.

Then total True cells: 15+50=65? But actually, the grid has 65 True? 

But the grid: the second rectangle marks 15 cells, the third marks 50 cells, and they don't overlap. So total 65. Correct.

But in the grid method, we mark the first rectangle: (0,0) -> True. Then the second rectangle: we mark (0,0) again (but it's already True) and the rest 14. Then the third: 50. Then total True is 1 (from first) + 14 (from second that are new) + 50 (from third) = 65? Actually, no: the first and second both mark (0,0). But the grid is set to True, so after both, (0,0) is True. Then we count (0,0) only once. Similarly, the second rectangle marks 15 cells, and the third marks 50. So total 15+50? But wait: the second rectangle marks 15 cells, and the third marks 50, and they are disjoint. So total 65. 

So the grid method correctly counts 65.

So the code should work.

But why did I think 66? Because I added the areas: 1+15+50=66, but that counts the (0,0) twice. The grid method avoids that by setting the same cell only once. So the grid method naturally handles overlaps.

Therefore, the grid method is correct.

I'll implement the grid method.

But note: the grid method uses two nested loops per rectangle. Worst-case 100 rectangles, each 100x100, then 100*100*100=1e6 iterations. In Python, this is acceptable.

So the code:

    def solve(self, N: int, sheets: List[List[int]]) -> int:
        grid = [[False] * 100 for _ in range(100)]
        for sheet in sheets:
            A, B, C, D = sheet
            # Iterate over the x from A to B-1, and y from C to D-1
            for x in range(A, B):
                for y in range(C, D):
                    # Since x and y are at most 99 (because B<=100 and D<=100, so x from A to min(B-1,99) and same for y.
                    # But if A is 100, then skip. But as argued, A cannot be 100. Similarly, if B is 100, then x goes to 99.
                    grid[x][y] = True
        # Now count the True in the grid
        total = 0
        for x in range(100):
            for y in range(100):
                if grid[x][y]:
                    total += 1
        return total

But we can optimize the counting by using:

        total = sum(sum(1 for cell in row if cell) for row in grid)

But the double loop is clear.

Let me run the examples:

Example1: 20 -> correct.
Example2: 10000 -> correct.
Example3: 65 -> correct.

So we'll go with this.

But just for completeness, if we had a rectangle with A_i=100, then the loop for x in range(100, B) would not run because B<=100, so the range is empty. Similarly, for D_i=100, we run y from C to 99. So it's safe.

Therefore, the solution is implemented.

But what if the rectangle is entirely outside [0,100)? Actually, the constraints say A_i>=0, B_i<=100, so they are all inside.

So we're good.

Now, I'll write the code accordingly.

Note: the grid is 100x100, so we don't need to check boundaries because the loops are from A to B-1, and A>=0, B<=100, so x is in [0,99] and same for y.

So no need for min or max.

Final code:

    class Solution:
        def solve(self, N: int, sheets: List[List[int]]) -> int:
            grid = [[False] * 100 for _ in range(100)]
            for sheet in sheets:
                A, B, C, D = sheet
                for x in range(A, B):
                    for y in range(C, D):
                        grid[x][y] = True
            area = 0
            for i in range(100):
                for j in range(100):
                    if grid[i][j]:
                        area += 1
            return area

Let me test with the examples.

Example1: 
  Sheet1: (0,5,1,3) -> x:0-4, y:1-2 -> 5x2=10 marks. But we mark each cell.
  Sheet2: (1,4,0,5) -> x:1-3, y:0-4 -> 3x5=15. But some overlaps: for x in [1,3] and y in [1,2] are already marked. So we mark additional cells? But we set to True, so no problem. Then total True cells: 20.
Example2: two full sheets: mark 100x100=10000.
Example3: as above: 15+50=65? Actually, the first sheet is entirely in the second, so the second marks 15 cells (including the first). Then the third marks 50. Total 65.

Yes.

So I think this is correct.

But note: the grid is 100x100? But the plane goes to 100, so the unit squares are from 0 to 99. So the maximum rectangle [0,100) x [0,100) is 100x100=10000. Correct.

Therefore, the solution is as above.

## Final Solution

### Solution Analysis
The problem requires calculating the total area covered by one or more rectangular sheets on a coordinate plane. Each sheet is defined by integer coordinates, and the solution must efficiently compute the union area, accounting for overlaps between rectangles.

#### Approach
The chosen approach uses a brute-force method with a grid representation of the coordinate plane. Given the constraints (coordinates range from 0 to 100), a 100x100 grid is sufficient to represent all possible unit squares. Each unit square corresponds to a 1x1 area in the plane. The algorithm processes each rectangle, marking all unit squares it covers. After processing all rectangles, the total number of marked unit squares gives the union area.

1. **Grid Initialization**: A 100x100 grid is initialized with `False` values, indicating no coverage initially.
2. **Marking Covered Squares**: For each rectangle, iterate over all unit squares within its bounds (from `A_i` to `B_i - 1` in the x-direction and `C_i` to `D_i - 1` in the y-direction) and mark them as `True`.
3. **Counting Covered Squares**: After processing all rectangles, count all `True` values in the grid, which represents the total union area.

This approach efficiently handles overlaps by ensuring each unit square is marked only once, regardless of how many rectangles cover it.

#### Complexity Analysis
- **Time Complexity**: O(N * W * H), where N is the number of rectangles (up to 100), and W and H are the maximum width and height of any rectangle (each up to 100). In the worst case, this results in 100 * 100 * 100 = 1,000,000 operations, which is feasible given the constraints.
- **Space Complexity**: O(W * H) for the grid, which is 100 * 100 = 10,000 units, also feasible within the constraints.

#### Solution Code
```python
class Solution:
    def solve(self, N: int, sheets: List[List[int]]) -> int:
        grid = [[False] * 100 for _ in range(100)]
        for sheet in sheets:
            A, B, C, D = sheet
            for x in range(A, B):
                for y in range(C, D):
                    grid[x][y] = True
        area = 0
        for i in range(100):
            for j in range(100):
                if grid[i][j]:
                    area += 1
        return area
```

#### Explanation
- **Grid Initialization**: The grid is a 100x100 2D list initialized to `False`, representing no initial coverage.
- **Processing Rectangles**: For each rectangle, the algorithm iterates over all unit squares within its bounds (x from `A_i` to `B_i - 1` and y from `C_i` to `D_i - 1`), marking each as `True` to indicate coverage.
- **Area Calculation**: The algorithm counts all `True` values in the grid, each representing a 1x1 unit area covered by at least one rectangle. The sum of these values is the total union area.

This approach efficiently computes the union area by leveraging the constraints (small coordinate range) to use a direct grid representation, ensuring correctness and simplicity.