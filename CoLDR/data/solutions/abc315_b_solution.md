# Solution for abc315_b.md

Generated at: 2025-07-22 06:21:43
Model: deepseek-reasoner

## Reasoning Process

Alright, so I'm trying to solve this problem about finding the middle day in a calendar for AtCoderLand. Let me read the problem again to make sure I understand it.

The problem says that a year has M months, each month i has D_i days. The total number of days in the year is odd, so the middle day is the ((total_days + 1) / 2)-th day. I need to find which month and which day that corresponds to.

For example, in the first sample, the input is 12 months with the usual days: 31, 28, 31, etc. The total is 365, so the middle day is the 183rd day. Then, by adding up the days of each month, we find that after 6 months (January to June) we have 181 days, so July 1st is the 182nd day and July 2nd is the 183rd. Hence, output is 7 2.

Another example: if there's only one month with one day, then the middle day is that day, so output 1 1.

And a third example: 6 months with days [3,1,4,1,5,9]. Total days = 3+1+4+1+5+9=23, so the middle day is the 12th day. Let's compute: month1: 3 days (days 1-3), month2: 1 day (days 4), month3:4 days (days 5-8), month4:1 day (day 9), month5:5 days (days 10-14). So the 12th day is in month5, day 3 (since day1 of month5 is the 10th day, so day 3 is 10+2=12). So output 5 3.

Okay, so the problem is clear. Now, I need to model the solution.

The key points:
- Input: M (number of months), then a list D of M integers (days in each month).
- Total days = sum(D) is odd. So the middle position is (total_days + 1) // 2 (since integer division in Python, and we need an integer index).

Approach:
1. Calculate total_days = sum(D)
2. Find the middle position: mid = (total_days + 1) // 2
3. Traverse through the months, accumulating the number of days until the accumulated days >= mid. Then, the middle day is in the current month.

But wait, how exactly? Let me think.

We need to find the smallest k such that the cumulative sum of days from month1 to monthk >= mid.

Suppose we have cumulative sums: 
cumulative[0] = 0 (before month1)
cumulative[1] = D0 (end of month1)
cumulative[2] = D0+D1
...

Then, we look for the first month a (starting at 1) such that cumulative[a] >= mid.

But actually, the day we're looking for in that month is: 
day = mid - cumulative[a-1]

Because cumulative[a-1] is the total days before month a. So the remaining days needed to reach mid is mid - cumulative[a-1], which is the day in month a.

Example: for the first sample, mid = 183. Cumulative after June (month6) is 181 (cumulative[6]). Then, for July (month7), cumulative[7] = 181 + 31 = 212, which is >=183. So day = 183 - 181 = 2. So month 7, day 2.

Another example: the one month one day. Total=1, mid=(1+1)//2=1. Cumulative0=0, then for month1, cumulative1=1>=1. So day = 1-0=1. Output 1 1.

Third example: mid=(23+1)//2=12. Cumulative per month:
Month1: 3 (cumulative1=3) -> less than 12
Month2: 3+1=4 -> less
Month3: 4+4=8 -> less
Month4: 8+1=9 -> less
Month5: 9+5=14 -> >=12. So month5, day =12 - 9 =3. Correct.

So the algorithm is:
1. total_days = sum(D)
2. mid = (total_days + 1) // 2
3. cumulative = 0
4. For i in range(M): # i from 0 to M-1
   a. cumulative += D[i]
   b. If cumulative >= mid:
        month = i+1
        day = mid - (cumulative - D[i])   # because cumulative - D[i] is the sum before this month
        break

But actually, we can keep a running sum and subtract. Alternatively, we can remember the previous cumulative. Alternatively, we can do:

current_sum = 0
for i in range(M):
    prev_sum = current_sum
    current_sum += D[i]
    if current_sum >= mid:
        month = i+1
        day = mid - prev_sum
        break

Yes, that makes sense.

Now, what about edge cases? The one month one day is already covered. Also, if the middle day is the last day of a month? For example, if we have months [3,1] (total=4, but wait, total must be odd. So no, because 4 is even, but the constraint says the total is odd. So we don't have to worry about that.

But in the algorithm, if mid is exactly the last day of a month, then when we add the current month, cumulative will be equal to mid? Actually, cumulative is the total up to and including the current month. Then, day = mid - prev_sum = (mid) - (mid - D[i])? Wait, no.

Example: suppose months [1,3] (so total=4? but that's even, not allowed). Let me make an odd one: months [1,3,1] total=5. Then mid=(5+1)//2=3.

Cumulative:
Start: current_sum=0
After month1: current_sum=1 -> less than 3
After month2: current_sum=1+3=4 >=3 -> then month=2, day=3 - 1 =2. But what is the 3rd day? 
Day1: month1 day1
Day2: month2 day1
Day3: month2 day2
So indeed, month2 day2. Correct.

But what if the middle day is the last day of a month? Let's take months [1,3,5]. Total=9, mid=(9+1)//2=5.
Cumulative:
month1: 1 (prev=0, current=1) -> 1<5
month2: current=1+3=4 -> 4<5
month3: current=4+5=9 >=5 -> so month3, day=5-4=1. But wait, the 5th day is the first day of month3? Let me count:
Day1: m1d1
Day2: m2d1
Day3: m2d2
Day4: m2d3
Day5: m3d1
Yes, correct. So the algorithm is fine.

But in the example above, the middle day was the first day of the month. Similarly, if it were the last day, say in a different example: months [1,2,1] total=4? again even. Let me take months [3,1] is even, so not allowed. How about months [3,2]: total=5, mid=3.
Cumulative:
month1: 3 >=3 -> so day=3 -0=3. The third day is the last day of month1. Correct.

So the algorithm works.

Now, about complexity: O(M) time and O(1) space. Since M is at most 100, it's efficient.

So I think this is the optimal solution. I don't see a need for a more optimized one.

But just to cover multiple approaches:

1. Brute force: same as above? Or could we precompute the cumulative array? But that would be O(M) time and O(M) space. Then we can do a linear scan. But the above is already O(M) time and O(1) space.

2. Alternatively, we can use binary search on the cumulative array? But the cumulative array isn't sorted? Actually, it is increasing. So we can do:

Create a list C of length M+1: 
C[0]=0
C[i]=C[i-1]+D[i-1] for i from 1 to M.

Then we need the smallest index i such that C[i] >= mid. Then month = i, and day = mid - C[i-1].

But the time complexity is O(M) to build the cumulative array, and then O(M) for linear search or O(log M) for binary search. But since M is only 100, it doesn't matter. However, the original method without building an extra array is more space efficient and equally time efficient. So the running cumulative is better.

But for clarity, the cumulative array method is also acceptable. However, the problem doesn't require storing all cumulative values, so we can avoid that.

So I'll stick with the running cumulative.

Now, implementation:

Steps:
1. Read M.
2. Read the list D of M integers.
3. total_days = sum(D)
4. mid = (total_days + 1) // 2   # because total_days is odd, so this is integer.
5. current = 0
6. For i in range(M):
      prev = current
      current += D[i]
      if current >= mid:
          a = i+1
          b = mid - prev
          break

7. Print a and b.

But in the code, the function is supposed to return [a, b].

Let me test with the examples.

Example1: M=12, D=[31,28,31,30,31,30,31,31,30,31,30,31]
total_days=365, mid=183
i=0: current=31 (prev=0) -> 31<183 -> continue
i=1: prev=31, current=31+28=59 -> 59<183
i=2: prev=59, current=59+31=90
i=3: 90+30=120
i=4: 120+31=151
i=5: 151+30=181
i=6: prev=181, current=181+31=212 >=183 -> a=7, b=183-181=2 -> [7,2] correct.

Example2: M=1, D=[1]
total=1, mid=1
i=0: current=1>=1 -> a=1, b=1-0=1 -> [1,1]

Example3: M=6, D=[3,1,4,1,5,9]
total=23, mid=12
i0: current=3 <12
i1: current=3+1=4 <12
i2: 4+4=8<12
i3:8+1=9<12
i4:9+5=14>=12 -> a=5, b=12-9=3 -> [5,3]

Looks good.

Now, what if the middle day is in the first month? Let's test with months [5,3,1] (total=9, mid=5)
i0: current=5>=5 -> then a=1, b=5-0=5. Correct: day5 of month1.

Another test: months [2,3,4] total=9, mid=5
i0: current=2<5
i1: prev=2, current=2+3=5>=5 -> a=2, b=5-2=3. But day3 of month2? 
Day1: m1d1
Day2: m1d2
Day3: m2d1
Day4: m2d2
Day5: m2d3 -> so month2, day3. Correct.

So the algorithm is solid.

Now, code:

I'll implement in the function.

Note: We must break as soon as we find the month.

Edge: M=0? But constraints say M>=1, so no need.

Let me write the code accordingly.

I think we are ready.

Implementation:

class Solution:
    def solve(self, M: int, D: List[int]) -> List[int]:
        total = sum(D)
        mid = (total + 1) // 2
        current = 0
        for i in range(M):
            prev = current
            current += D[i]
            if current >= mid:
                return [i+1, mid - prev]

That's it.

But just to be safe, I'll test with the examples in code.

Example3: 
M=6, D=[3,1,4,1,5,9]
mid = (23+1)//2 = 12
i0: prev=0, current=3 -> 3<12 -> next
i1: prev=3, current=4 -> 4<12
i2: prev=4, current=8 -> 8<12
i3: prev=8, current=9 -> 9<12
i4: prev=9, current=14 -> 14>=12 -> return [5, 12-9=3] -> [5,3]

Perfect.

I think that's the solution.

## Final Solution

### Step 1: Problem Understanding and Modeling
The problem requires finding the middle day in a calendar year with `M` months, where each month `i` has `D_i` days. The total number of days in the year is odd, so the middle day is the `((total_days + 1) // 2)`-th day of the year. The solution involves:
1. Calculating the total number of days in the year.
2. Determining the middle day's position.
3. Iterating through each month to find the specific month and day corresponding to the middle position by accumulating days until the cumulative sum meets or exceeds the middle position.

**Key Constraints:**
- `1 ≤ M ≤ 100`: The number of months is manageable with linear iteration.
- `1 ≤ D_i ≤ 100`: Each month has at most 100 days.
- Total days is odd, ensuring the middle position is an integer.

**Edge Cases:**
- Single month with one day: Middle day is the only day.
- Middle day is the first day of a month: Correctly computed by offset calculation.
- Middle day is the last day of a month: Handled by cumulative sum comparison.

### Step 2: Multiple Solution Exploration
1. **Brute Force Solution:**
   - **Idea:** Iterate through each month, accumulating days until the cumulative sum meets or exceeds the middle position.
   - **Time Complexity:** O(M) since each month is processed once.
   - **Space Complexity:** O(1) as no additional data structures are used.
   - **Applicability:** Efficient for given constraints (M ≤ 100).

2. **Optimized with Cumulative Array:**
   - **Idea:** Precompute a cumulative sum array of days up to each month, then use binary search to find the month containing the middle day.
   - **Time Complexity:** O(M) for array construction and O(log M) for binary search. Still efficient but unnecessary for small M.
   - **Space Complexity:** O(M) for storing the cumulative array.
   - **Applicability:** Useful if multiple queries are needed, but overkill here.

3. **Mathematical Calculation:**
   - **Idea:** Directly compute the month and day using division and modulus operations. However, varying days per month make this impractical.
   - **Applicability:** Not feasible due to irregular month lengths.

4. **Space-Optimized Solution:**
   - **Idea:** Use a running total to avoid storing cumulative sums, breaking early once the middle day is found.
   - **Time Complexity:** O(M) in the worst case.
   - **Space Complexity:** O(1).
   - **Applicability:** Optimal for this problem due to minimal space usage.

### Step 3: Optimal Solution Selection and Implementation
**Selection Rationale:** The space-optimized solution is chosen because:
- It processes data in a single pass with O(1) space.
- It efficiently handles the upper constraint (M ≤ 100) with minimal operations.
- Early termination once the middle day is found optimizes performance.

**Complete Code Implementation:**
```python
from typing import List

class Solution:
    def solve(self, M: int, D: List[int]) -> List[int]:
        total_days = sum(D)
        mid = (total_days + 1) // 2
        cumulative = 0
        for i in range(M):
            prev = cumulative
            cumulative += D[i]
            if cumulative >= mid:
                return [i + 1, mid - prev]
```

**Key Optimization Points:**
- **Running Total:** Maintains a cumulative sum of days as we iterate, avoiding storage of intermediate sums.
- **Early Termination:** Breaks the loop immediately upon identifying the month containing the middle day.
- **Direct Calculation:** The day within the month is computed as the difference between the middle position and the cumulative days before the current month.

### Step 4: Verification and Summary
**Verification with Examples:**
1. **Example 1:** 
   - Input: `M=12`, `D=[31,28,31,30,31,30,31,31,30,31,30,31]`
   - Total days = 365, mid = 183.
   - Cumulative reaches 212 in month 7 (i=6), day = 183 - 181 = 2 → Output `[7, 2]`.

2. **Example 2:**
   - Input: `M=1`, `D=[1]`
   - Total days = 1, mid = 1.
   - Cumulative in month 1 is 1, day = 1 - 0 = 1 → Output `[1, 1]`.

3. **Example 3:**
   - Input: `M=6`, `D=[3,1,4,1,5,9]`
   - Total days = 23, mid = 12.
   - Cumulative in month 5 (i=4) is 14, day = 12 - 9 = 3 → Output `[5, 3]`.

**Summary:**
- The solution efficiently locates the middle day by leveraging a running total and early termination.
- It optimally handles all constraints and edge cases within O(M) time and O(1) space.
- Further optimizations are unnecessary given the problem's constraints, but the approach can be extended to larger inputs by maintaining the same logic.