# Solution for abc310_f.md

Generated at: 2025-07-22 06:29:51
Model: deepseek-reasoner

## Reasoning Process

Alright, I'm trying to solve this dice probability problem. Let me start by understanding what's being asked.

We have N dice, each die i has A_i faces (numbered from 1 to A_i). We throw all the dice at once, and we want the probability that there exists some subset of the dice whose sum is exactly 10. The output should be the probability modulo 998244353.

First, I need to model the problem. The total number of possible outcomes is the product of all A_i. For each outcome (each die shows one number), we need to check if there's a subset of dice that sums to 10.

Since N is up to 100, but each A_i can be up to 10^6, the total outcomes could be huge (like 10^600), so we cannot iterate over all outcomes. We need a smarter way.

The key is to count the number of outcomes (dice result combinations) such that there is at least one subset of the dice that adds up to 10. 

But note: The condition is about the existence of a subset. So, for a given outcome (a set of numbers x1, x2, ..., xN), we need to check if there's a subset of these numbers that sums to 10.

However, the numbers are bounded: each xi is at least 1 and at most A_i, and we're only interested in the sum 10. So, the subset-sum problem here is for a fixed target (10) and the numbers are positive integers. But note: we can't just use the classic subset-sum dynamic programming because the numbers can be large (up to 10^6) but the target is fixed (10). So the state space for subset-sum would be 10 * N? Actually, the target is 10, so we can use a bitmask or a boolean array of size 11 (from 0 to 10).

But the challenge is that each die can show any number from 1 to A_i, and we have to account for all possible outcomes and then for each outcome, check the subset-sum. But that's too expensive.

Alternative approach: Instead of iterating over outcomes, we can use generating functions and inclusion-exclusion? Or maybe dynamic programming that counts the outcomes that have a subset summing to 10? But note: the problem is that the same die can't be used twice, and the outcome of each die is independent.

Wait, let me think differently. The condition is: there exists a non-empty subset S of {1,...,N} such that the sum of the outcomes of the dice in S is 10.

But note: the same die outcome can be used only once. So, we need to count the outcomes for which 10 is achievable by some subset.

We can use complementary counting: count the outcomes where no subset of dice sums to 10. Then subtract that from total outcomes.

So, the probability = 1 - (number of outcomes where no subset sums to 10) / (total outcomes)

But how to count outcomes where no subset of dice sums to 10? 

This is tricky because the outcomes are independent per die, and we need to ensure that regardless of the numbers that come up, no subset of them adds to 10.

However, note that the dice outcomes are independent. So, we can model this with generating functions? Or use a DP that iterates over dice and tracks which subset sums are achievable?

But the subset-sums we care about are from 0 to 10 (since beyond 10, we don't care for the target 10). However, the numbers on the dice can be large, but when building the subset-sum, we are limited to 10. So, we can use a state that is a bitmask of achievable sums from 0 to 10.

But note: if a die shows a number greater than 10, then it cannot be included in any subset that sums to 10 (because including it would make the sum at least that number, which is >10). So, we can categorize dice:

- Dice with A_i >= 10: these dice can show numbers from 1 to 10, or above 10. But if they show above 10, they cannot be part of any 10-sum subset. If they show a number <=10, then they can.

- Dice with A_i < 10: they can only show numbers from 1 to A_i (all <=9), so they can potentially be part of a subset.

But actually, even if a die has A_i>=10, it can still show a number <=10 and be used. So we can't ignore them.

Idea: we can use a DP where the state is the set of achievable subset sums (from 0 to 10). The state can be represented as a bitmask or a boolean array of length 11. Since 10 is small, we can use a bitmask of 11 bits (from 0 to 10). The initial state is {0} (bitmask 1<<0 = 1). Then, for each die, we consider the possible outcomes and update the state of achievable sums.

But note: when we add a die, we can choose to include it or not. However, the condition is that the die must show a number x, and then we can update the set of achievable sums as the union of the old set and the old set shifted by x.

But wait: in the outcome, the die shows a specific number. So for each die, we have a set of possible numbers. Then for each possible x (from 1 to A_i), we can update the state.

But we have to count the outcomes that lead to the state where 10 is not achievable at the end. Then, we can do:

Let DP[i][s] = number of ways for the first i dice such that the set of achievable subset sums is s (where s is a bitmask of 11 bits) and no subset has achieved 10 so far? But actually, we want to avoid achieving 10. So we can design the DP to never allow the state that has 10 achievable.

But if at any die we achieve 10, we want to exclude that outcome? Actually, we are counting outcomes where no subset sums to 10. So we can let the state s be a bitmask of achievable sums (without including 10). Then, when processing a die, if we can form 10 by adding the current die's value to an existing sum, then that outcome would be invalid? Actually, we are avoiding counting that outcome. So we don't want to count any outcome that at any time can form 10. 

But note: the condition is that at the end, after all dice, we can choose any subset. So we can form 10 at the end. So we need to avoid outcomes that eventually can form 10.

Therefore, we can run a DP for i from 0 to N, with state s (a bitmask of achievable sums from 0 to 10). We start with state s0 = 1 (only 0 is achievable). Then for each die, we consider each possible outcome (each x from 1 to A_i) and update the state: new_state = s OR (s << x). But then we have to avoid states that have 10. Actually, if we ever form 10, we don't want to count that outcome.

But note: the same die outcome can be used only once. However, the way we build the subset-sum is by independently choosing each die. So the DP state is building the set of all subset-sums achievable from the first i dice.

So, we can do:

Let DP[i][s] = number of ways for the first i dice such that the set of achievable sums is s (a bitmask with 11 bits) and s does not include 10.

Then, for the next die (i+1) with face values from 1 to A_{i}, for each x in [1, A_{i}], we update:

new_s = s OR (s << x)   (but then we have to mask to 0..10: so modulo 11? Actually, we only care about sums up to 10. So if shifting by x causes a sum beyond 10, we can ignore that because we don't need to track sums above 10? Actually, we do: because if we have a sum of 5 and then add 6, that's 11 which we don't care about. But if we have a sum of 10, we skip. So we can cap the state to 0..10.

But note: if we have a sum that is 10 or above, we don't need to track beyond 10? Actually, for the purpose of forming 10, we don't need sums above 10. However, if we have a die that gives a number 10, then we can form 10 by itself. So we have to account for that.

So the state s is a bitmask of the achievable sums in the range [0,10]. But if a die has a number x, then we can form x by itself? Actually, the die can be chosen alone. So the update is: for each current achievable sum t, we can form t+x. But if t+x>10, we don't record it. However, if t+x==10, then we have achieved 10, so we skip that outcome.

But wait: the problem is that we are counting outcomes that never form 10. So for each die, when we consider a value x, if by adding x to any existing sum we get 10, then this outcome is bad and we skip it? Actually, no: we are counting the outcomes that do not form 10 at any time. So if for a die outcome x, it leads to forming 10 (either by itself or combined with previous dice), then that outcome is invalid and we don't count it.

But note: if we are at die i, and we have a state s (without 10), and we get an outcome x, then:

- If x alone is 10, then we form 10. So we skip this outcome.
- Or if there exists an achievable sum t in s such that t + x = 10, then we form 10. So we skip.

Otherwise, we update the state: new_s = s OR (s << x) OR {x}? Actually, note: the die alone is equivalent to starting from 0 and adding x. But our state s includes 0? We start with 0. So when we do s << x, that shifts the entire set by x, and then we OR with the original s? Actually, the update is: the new set of achievable sums is the old set (without using the new die) plus the old set shifted by x (which corresponds to adding the die to any existing subset). But note: we are not forced to use the die? Actually, the condition is: we can choose some subset. So the new set is the old set (if we don't choose the die) and the old set + x (if we choose the die). So the update is: new_s = s | (s + x). But we have to consider the die alone? That is included in s+x because s has 0: 0+x = x.

So the update is: new_s = s | (s << x)   [if we only care about sums up to 10, we can then mask by (1<<11)-1 and then if the bit for 10 is set, we skip this outcome.

But in our DP, we are only tracking states that do not have 10. So when we update, if the new state would have 10, then we discard this outcome for this die's outcome x. Otherwise, we update the state.

So the recurrence:

For die i (from 0 to N-1) and state s (bitmask without the 10 bit set), and for each possible outcome x (from 1 to A_i):

  new_state = s | (s << x) 
  Then, we mask new_state to 11 bits: new_state = new_state & ((1<<11)-1)

  If new_state has the 10 bit set, then this outcome x leads to forming 10 -> skip (do not add to any state).
  Else, we add the count to DP[i+1][new_state].

But note: what if the die outcome x is greater than 10? Then, shifting s by x (which is >10) would push some bits beyond 10? Actually, we mask to 11 bits, so sums beyond 10 are truncated. But if x>10, then:

- The die alone: x>10, so it doesn't form 10 (because x!=10? unless x==10, but if x>10, then x>=11). So x alone is not 10 and also adding x to any existing sum (which is at least 0) would be at least x>=11, which we don't care about. So the new_state would be s | (s << x) but then masked: the shifts beyond 10 are lost. Actually, shifting by x (>=11) would shift all bits beyond 10, so the OR with s would just leave s unchanged? Not exactly: if we shift by 11, then we shift each bit by 11, which is beyond the 10th bit, so after masking, those bits are gone. So effectively, if x>10, then the new_state = s? Because the shifted part is lost. So the state doesn't change.

But wait: what if we have a die with x>10? Then we can only form the new sums that we could form without using this die? Actually, we can choose to not include the die, so the old set remains. But if we include the die, we form sums that are old_sum + x, which are all >=x>10, so they don't contribute to the sums in [0,10]. So the set of achievable sums in [0,10] remains the same as s. So indeed, for x>10, new_state = s.

But also, if x==10, then:

  The die alone: 10 is formed. So we skip.

  Also, if we add 10 to any existing sum t, then we form t+10. But if t>0, then t+10>10, so we don't care. The only way to form 10 is by the die alone. So if x==10, we skip.

Similarly, if x<10, then we might form 10 by adding x to an existing sum of 10-x. So if the state s has the bit (10-x) set, then adding x would form 10. So we skip.

So the update for a die and a value x:

  if x == 10: skip (because 10 is formed by the die alone)
  else if x < 10: 
      if the state s has the bit (10 - x) set? Then adding x to that subset forms 10 -> skip.
      else: new_state = s | (s << x)   (then mask to 11 bits) and then check if 10 is set? Actually, we can skip if in the new_state the bit 10 is set? Because even if we form 10 by two or more dice? For example, we have a die with 5 and then another with 5: the first die: state becomes {0,5}. Then the second die: we shift by 5: then we have 5+5=10 -> sets the 10 bit. So we skip that outcome.

Alternatively, we can compute the new_state and then check the 10 bit. That might be simpler.

So the recurrence:

  for each state s (a bitmask of 11 bits, without the 10 bit) and each die i, and each x in [1, A_i]:
      if x == 10: skip (because we form 10 by the die alone) -> so this outcome x is invalid.
      else:
          new_s = s | (s << x) 
          new_s = new_s & ((1<<11)-1)   # to keep only sums 0..10
          if new_s has the 10th bit set: skip (because we formed 10 by a subset that might include multiple dice)
          else: then we can use new_s as the state for the next die, and count this outcome.

But note: if x>10, then as above, new_s = s (because the shifts are beyond 10 and masked away). So then we don't form 10? Actually, we don't form 10 because the die alone is too big and any combination including it would be too big. So we can include the outcome and the state remains s.

But the above check: we compute new_s and then check if the 10 bit is set. For x>10, new_s = s, and s doesn't have 10 (by the state definition), so we accept.

Similarly, for x<10: we compute new_s and then check if it has the 10 bit. If it does, we skip.

But wait: what if the die alone is x? Then, we add the die alone: which is x. If x is not 10, then we add x to the state. But if x is 10, we skip. For x<10, we add x. Then if we later add another die that can form 10-x? That's the next die. So we don't need to check immediately for 10? Actually, the state update might form 10 by two dice: for example, first die: 5 -> state becomes {0,5}. Then second die: 5 -> state update: {0,5} | {0+5, 5+5} = {0,5} | {5,10} -> {0,5,10}. Then we see the 10 bit and skip. So that outcome (5 and 5) would be skipped. That is correct.

But the problem: we are processing die by die, and we update the state. The state is the set of all subset sums achievable from the dice processed so far. Then when we add a die, we update the state. If at any time the state includes 10, we skip the outcome (for that die's value).

But note: the same outcome (a sequence of die rolls) is only counted once. However, we are iterating over dice, and we are updating the state. If at the end of processing all dice the state does not have 10, then we count the entire outcome. But if at any die we form 10, we skip? Actually, we skip the outcome as soon as we form 10. But that outcome is a complete assignment? Actually, no: we are processing one die at a time. For a fixed outcome (x1, x2, ..., xN), we process the dice in order. If at die i, we form 10, then we skip that outcome for the rest? Actually, we are counting the outcome only if we never form 10 at any die? But that outcome might form 10 at die i, so we skip it. So that is correct: we don't count that outcome.

But note: the condition is that there exists a subset that sums to 10. So if we form 10 at die i, then the outcome is bad and we skip it.

However, the order of dice: does it matter? In the state, we are maintaining the set of achievable sums from the dice processed so far. Since the dice are independent and the subset is any subset, the order doesn't matter. So processing in any fixed order is okay.

But the state space: the state is a bitmask of 11 bits, so 2^11 = 2048 states. And we have N up to 100. So we can do a DP with 100 * 2048 states. But for each state, we iterate over A_i possible outcomes? That would be 100 * 2048 * (max A_i) which is 100 * 2048 * 10^6 -> about 2e12, which is too high.

So we need to optimize the inner loop.

Instead of iterating over each x from 1 to A_i, we can precompute the effect of all x in one go.

Note: for a fixed state s, and for a die with A_i faces, we want to count the number of x in [1, A_i] such that:

  if x==10: then skip (so we don't count any x=10)
  else:
      new_s = (s | (s << x)) & mask
      then if new_s has the 10 bit, skip; else, we add the count to state new_s.

But we can aggregate by the effect of x.

But the effect of x depends on x. However, we can break the values of x into a few cases:

Case 1: x > 10. Then, as argued, new_s = s (the state doesn't change). And we skip if x==10? Actually, we skip x=10, so for x>10, we skip only if x==10? Actually, x>10 does not include 10. So for x>10, we have:

  new_s = s (because the shifts are beyond the mask) and we don't form 10 (since s doesn't have 10). So all x>10 are safe and lead to the same state s.

How many x>10? A_i - min(10, A_i). But note: we also skip x=10. So:

  total safe x: 
      if A_i < 10: then no x>=10, so skip only if x=10? Actually, if A_i<10, then x from 1 to A_i, and 10 is not in that range. So skip none? Then we have to check for each x whether the update forms 10.

      if A_i>=10: then we skip x=10, and for x>10, we have A_i - 10 values that lead to state s.

Case 2: x <=10 and x != 10. Then we have to compute new_s = (s | (s << x)) & mask, and then check if the 10th bit is set. But note: we can precompute for each state s and each x in [1,9] and 11? Actually, x in [1,9] and 11? But x>10 we handled separately. So we only need to handle x in [1,9] and x=10? Actually, we skip x=10. So we only need to handle x in [1,9] and x>=11.

Actually, we can split:

  Let mask11 = (1<<11)-1.

  For a given state s (without the 10 bit) and a die with A_i:

      count_x = 0
      Let F = [0]*(2048)   # to aggregate the counts for new states

      For x in [1, min(A_i, 10)]:  # x from 1 to min(10, A_i), but skip x=10
          if x == 10: skip
          else:
              ns = (s | (s << x)) & mask11
              if (ns >> 10) & 1:  # then skip this x
                  continue
              else:
                  F[ns] += 1

      Then, for x from 11 to A_i (if A_i>=11):
          # for these x, the state remains s and doesn't form 10 (because x>10 and s doesn't have 10)
          count_high = max(0, A_i - 10)   # but note: if A_i>=11, then count_high = A_i - 10? Actually, we skipped x=10 and the rest above 10 are safe. But note: if A_i is 10, then we don't have x>10. So:
          count_high = A_i - min(A_i, 10)   # this counts the numbers from 11 to A_i. But we also skipped x=10? So we have to subtract one for x=10? Actually, in the range [1, A_i], we skip x=10 only if A_i>=10. So:

          Actually, the total safe high numbers = max(0, A_i - 10)   # because if A_i>=10, then we skip x=10 and take the rest above 10? But above 10 starts at 11. So the count is max(0, A_i - 10). But if A_i=10, then we have x from 1 to 10: we skip x=10, so the high numbers (>=11) are 0. So count_high = max(0, A_i - 10)

      Then, we add count_high to the state s (so F[s] += count_high)

      Then, for each new state ns that we got from the low x (1..9), we add F[ns] to the next DP state.

But note: we must not count x=10 at all. So for x in [1, min(A_i,10)]: we skip x=10. So if A_i>=10, then we skip x=10. If A_i<10, we don't have x=10 so we process all x.

So the inner loop for a fixed state s and die A_i:

  count_high = max(0, A_i - 10)   # these x are safe and leave state unchanged

  Then, for x from 1 to min(A_i, 10):
      if x == 10: 
          skip
      else:
          ns = (s | (s << x)) & mask11
          if ns has the 10th bit set: skip
          else: then we count this x -> so we add one to the bucket for ns.

  Then, we update:

      We add count_high to the bucket for state s (because all high x leave the state as s).

  Then, for the next die, we iterate over all states ns (including s) that have at least one count.

But note: the state s might also be updated by the low x? For example, if we have x=1, we might get a new state ns that is different from s. But we are aggregating all the outcomes.

So the DP for the next die: we have to consider each next state and add the count.

But the state space is 2048 states. So we can do:

  Let dp[i] be an array of length 2048, where dp[i][s] = number of ways for the first i dice to end in state s (without having formed 10).

  Then for the next die (with A_i = A[i]), we iterate over each state s in dp[i], and then we compute the transitions as above:

      count_high = max(0, A_i - 10)
      Let transitions = a new array (or a dictionary) for the next state.

      For each x in [1, min(A_i,10)]:
          if x==10: skip
          else:
              ns_val = (s | (s << x)) & mask11
              if (ns_val >> 10) & 1: 
                  skip
              else:
                  transitions[ns_val] += 1   # one outcome for this x

      Then, we also have count_high outcomes that leave state s -> so transitions[s] += count_high

      Then, for each next state ns in the keys of transitions, we do:
          dp[i+1][ns] += dp[i][s] * transitions[ns]

  Then, we proceed.

But note: the transitions for a fixed state s and die A_i: we are iterating over x in [1, min(10, A_i)] (excluding 10) and then adding the high part. The total number of x we iterate is at most 9 (if A_i>=10) or at most min(10, A_i) (but skip 10, so at most 9 or min(10, A_i) if A_i<10). Actually, if A_i<10, then we skip 10? But 10 is not in [1, A_i] if A_i<10. So we iterate min(9, A_i) when A_i<10? Actually, if A_i=5, we iterate 5 values. So worst-case 9 iterations per state. Then the total per die is 2048 * 9 ~ 18432, and then multiplied by 100 dice -> 1.84e6, which is acceptable.

But we must be cautious: the min(A_i,10) is at most 10, so the inner loop per state is at most 9. So total operations per die: 2048 * 9 = 18432, and 100 dice: 1.8432e6, which is acceptable.

But note: the state s is an integer in [0, 2047]. The operation for each x: shifting and OR, then masking, then checking the 10th bit.

So we can code this DP.

Steps:

  MOD = 998244353
  mask11 = (1 << 11) - 1   # to mask to 11 bits.

  dp[0] = { initial state: state 1 (bit0 set) }.

  Actually, state0: only sum 0 is achievable. So s0 = 1 (binary: ...00000000001, with bit0 set).

  Then, iterate for each die i from 0 to N-1:

      dp_next = [0] * 2048   # we'll use an array for the next states.

      For each state s in [0,2047]:
          if dp[i][s] != 0:
              count_high = max(0, A[i] - 10)   # A[i] is the current die's faces

              # Process the high numbers: they leave the state unchanged.
              dp_next[s] = (dp_next[s] + dp[i][s] * count_high) % MOD

              # Process the low numbers: x from 1 to min(A[i],10), but skip x=10.
              low_bound = min(A[i], 10)
              for x in range(1, low_bound+1):
                  if x == 10: 
                      continue
                  # Compute new_state = s | (s << x)   then mask to 11 bits
                  new_state = s | (s << x)
                  new_state &= mask11

                  # Check if the new_state has the 10th bit set? (i.e., 1<<10)
                  if new_state & (1<<10):
                      # Skip this x: do nothing
                      continue
                  else:
                      dp_next[new_state] = (dp_next[new_state] + dp[i][s]) % MOD

              # But note: the above loop for low x: we are adding one outcome per x? Actually, we are iterating each x and adding dp[i][s] for each x. But we are adding each x one by one. So for each x, we add the entire dp[i][s] for that outcome? Actually, each outcome (each x) is one possibility. So for each x, we are adding 1 to the count for the new_state. Then we multiply by dp[i][s]? Actually, the above code: for each x, we add dp[i][s] to the state new_state. That is: we are doing:

                 dp_next[new_state] += dp[i][s]   for each x that doesn't form 10 and is not 10.

              But note: we are iterating over x and for each x we do a separate addition. So that's correct: each x is a different outcome and we are counting them separately.

      Then set dp = dp_next for the next die.

  After processing all dice, the total outcomes that never form 10 is the sum of dp[N][s] for all states s.

  Then, the number of good outcomes (that form 10) = total_possible_outcomes - outcomes_never_form_10.

  The probability = (total_possible_outcomes - outcomes_never_form_10) / total_possible_outcomes.

  But we need modulo 998244353.

  Total_possible_outcomes = product_{i=0}^{N-1} A_i.

  Then, the answer is: ( (total_possible_outcomes - outcomes_never_form_10) * mod_inverse(total_possible_outcomes, MOD) ) % MOD.

But we must compute everything modulo 998244353.

But note: the numbers can be huge. We have to compute total_possible_outcomes modulo MOD? Actually, we are going to do modular arithmetic.

But the modulus for the numerator and denominator: we are working modulo 998244353. But the counts are integers modulo 998244353.

So:

  total = 1
  for a in A:
      total = (total * a) % MOD

  bad = sum(dp_final)   # which is the sum over all states after N dice.

  good = (total - bad) % MOD

  Then, answer = good * mod_inverse(total, MOD) % MOD

But wait: we computed bad as the count of outcomes that never form 10. Then good = total - bad.

But the probability is good/total mod MOD.

So: answer = (total - bad) * mod_inverse(total, MOD) % MOD

But note: we have to do all arithmetic modulo MOD.

However, we must note: the total and bad are computed modulo MOD. But the modulus is prime? 998244353 is prime.

But the problem: the total can be huge. But we are doing modulo MOD.

But we have to compute the modular inverse of total modulo MOD.

But we have to be cautious: the product total might be 0? Only if one of the A_i is divisible by MOD? But the constraints say that the modulus is 998244353 and A_i up to 10^6. Since 998244353 is about 1e9, and A_i <= 10^6, so no A_i is divisible by MOD? Actually, 998244353 is 998244353, which is greater than 10^6. So A_i < MOD, so total = product A_i mod MOD is nonzero. So we can compute the inverse.

But we have to compute the product total modulo MOD.

Alternatively, we can compute total as:

  total = 1
  for a in A:
      total = total * a % MOD

Then bad = sum of dp[N] over all states.

Then answer = (total - bad) * pow(total, MOD-2, MOD) % MOD

But note: we computed bad as the count of outcomes that do not form 10. So:

  answer = ( (total - bad) * inv_total ) % MOD

But let me test with the example: N=4, A = [1,7,2,9]

Total outcomes = 1*7*2*9 = 126.

Then, the probability of satisfying the condition is 11/18.

So the bad outcomes = 126 - (11/18 * 126) = 126 * (1 - 11/18) = 126 * (7/18) = 49.

So the bad outcomes count should be 49.

Then the answer = (126 - 49) * inv(126) mod MOD = 77 * inv(126) mod MOD.

But 77/126 = 11/18.

We can check: 11 * inv(18) mod MOD = 942786334.

But let me compute 77 * inv(126) mod MOD:

  First, 77 * inv(126) mod MOD = 77 * (126^(MOD-2)) mod MOD.

But we can check: 77 * inv(126) = 11/18? Because 77/126 = 11/18.

So it should be 942786334.

So the DP must yield bad = 49.

But let me simulate the example manually? Or at least the first few dice.

Example: A = [1,7,2,9]

We start: state0 = 1 (binary 1: only sum0)

Die0: A0=1 -> only x=1. Since 1<=10 and not 10, we compute:

  new_state = 1 | (1<<1) = 1 | 2 = 3 (binary ...0011) -> which is {0,1}. Then check: 10th bit? No. So we set state=3.

But we have to count: how many outcomes for this die? Only x=1. So state becomes 3.

Die1: A1=7. We break:

  count_high = max(0, 7-10)=0.

  Then, x from 1 to 7:

      x=1: 
          new_state = 3 | (3<<1) = 3 | 6 = 7 (binary 0..0111) -> {0,1,2}? 
          Check: 10th bit? no. -> add to state 7.

      x=2:
          new_state = 3 | (3<<2) = 3 | (3*4) = 3 | 12 = 15 (binary 0..1111) -> sums {0,1,2,3}? 
          But 3<<2: 3 in binary is 0011, shifted by 2: 1100. Then OR with 0011 -> 1111 -> 15. Then mask to 11 bits: still 15. Then check 10th bit: no. So add to state 15.

      x=3:
          new_state = 3 | (3<<3) = 3 | 24 = 27 (binary 00011011). But we mask to 11 bits: 27 in 11 bits is 00000011011? Actually, 16+8+2+1=27. Then check bit10? no. So add to state 27.

      x=4:
          new_state = 3 | (3<<4) = 3 | (3*16)= 3 | 48 = 51. Then mask to 11 bits: 51 mod 2048? 51 is less than 2048. But we only care bits 0-10. 51 = 32+16+2+1 -> bit5,4,1,0. Then check: no 10. So add to state51.

      x=5:
          new_state = 3 | (3<<5) = 3 | (3<<5) = 3 | 96 = 99. Then mask: same. Check: no 10. Add to state99.

      x=6:
          new_state = 3 | (3<<6) = 3 | 192 = 195. Check: no 10. Add to state195.

      x=7:
          new_state = 3 | (3<<7) = 3 | 384 = 387. Check: no 10. Add to state387.

  So after die1, we have 7 states: each state is 7,15,27,51,99,195,387, each with count 1? Actually, we started with state3 (count=1) and then for each x we add 1 to the new state. So each new state gets 1.

Die2: A2=2. Then:

  count_high = max(0,2-10)=0.

  x from 1 to 2:

      For each state from die1, we do:

      Let me do for state7: which is 0b00000111 -> sums:0,1,2.

        x=1: 
            new_state = 7 | (7<<1) = 7 | 14 = 15. Check 10th bit? no. So add to state15.

        x=2:
            new_state = 7 | (7<<2) = 7 | 28 = 31. Check: no 10. So add to state31.

      Similarly, for state15: (0b1111: sums0,1,2,3)

        x=1: 15 | (15<<1) = 15 | 30 = 31 (0b11111). Then mask: 31. Check: no 10. Add to state31.

        x=2: 15 | (15<<2) = 15 | 60 = 63. Check: no 10. Add to state63.

      For state27: (0b0000011011 -> sums: 0,1,3,4? because 16+8+2+1? Actually, 27 in binary: 11011 -> bits 0,1,3,4? (since 16+8+2+1=27). But we have 5 bits? Actually, we are only tracking 11 bits. So the state27 has bits: 0,1,3,4 set.

        x=1: 
            new_state = 27 | (27<<1) = 27 | 54 = 63. Check: no 10. So add to state63.

        x=2: 
            new_state = 27 | (27<<2) = 27 | 108 = 111. Check: no 10. Add to state111.

      Similarly, we do for other states. It becomes very long.

But we see that the state space is growing. After die2, we have multiple states.

Die3: A3=9. Then we have to iterate over x=1..9 (skip x=10, but 10 is not in [1,9]) and then for each state.

But we are not forming 10? Actually, we will form 10 in some cases. For example, if we have state that has 1 and 9? Then adding 1 and 9: but we already have the state? Actually, we are updating the state: if we add a die with 9, then if the state has 1, then 1+9=10 -> we skip that outcome.

So we have to avoid that.

But in the example, the outcome that leads to 10: for instance, the dice show 1,3,2,7: then 3+7=10. How does our DP handle that?

After die0: die0 shows 1 -> state={0,1}.
After die1: die1 shows 3: then new_state = {0,1} OR {0+3,1+3} = {0,1,3,4}. Then we don't form 10 -> state becomes 0b000010110 (bits: 0,1,3,4) -> which is 1+2+8+16? Actually, 1<<0 + 1<<1 + 1<<3 + 1<<4 = 1+2+8+16=27? So state27.

Then die2: die2 shows 2: then new_state = 27 | (27<<2) = 27 | (27*4) = 27 | 108 = 111 (binary 1101111). Then we check: no 10. Then state111.

Then die3: die3 shows 7: then we update: new_state = 111 | (111<<7). But shifting 111 (binary 1101111) by 7: becomes 11011110000000. Then OR: 11011110000000 | 1101111 = 11011110000000 | 000000001101111 = 110111101101111? Then mask to 11 bits: we take the last 11 bits: 0b11011110111? But we only care about the 10th bit? The 10th bit is the 10th bit (counting from 0: so bit10 is the 11th bit). Actually, the mask11 = (1<<11)-1 = 0x7FF = 2047.

But 111<<7: 111 * 128? 111 in decimal is 1+2+4+8+16+32=63? Wait, no: state111 is an integer that represents the bitmask. The bits are set at positions: 0,1,2,3,4,5? Actually, 111 in binary: 64+32+8+4+2+1=111? 64+32=96, then 111-96=15 -> 8+4+2+1? So bits: 0,1,2,3,5,6? (since 2^0=1, 2^1=2, 2^2=4, 2^3=8, 2^5=32, 2^6=64).

Then shifting by 7: we get bits at 7,8,9,10,12,13. Then OR with the original 0,1,2,3,5,6. Then the new state has bit10? Because 10 = 7+3: we have a subset that sums to 3 (from the state: 3 is present? In state111, we have 3? The bit for 3: 2^3=8, and 8 is set? Then 3 is present. Then adding 7: 3+7=10. So the update: we set the bit for 10. Then we skip this outcome. So the outcome (1,3,2,7) is skipped? But that outcome should be counted as good because it forms 10.

Ah! We have a problem: we are counting the bad outcomes (that never form 10). But the outcome (1,3,2,7) forms 10, so we should not count it in bad. We skip it when we form 10 at die3. So we skip it, meaning it is not added to any bad state. That is correct: we are counting bad outcomes = outcomes that never form 10. This outcome forms 10 at die3, so we skip it -> so it is not in the bad count. Then at the end, we subtract the bad count from total to get the good count. So that outcome is in the good count.

But note: what if we form 10 earlier? For example, if the first two dice form 10? Then we skip at die1. So we never complete the assignment. But that is okay because we don't count that outcome at all in the bad count. Then at the end, we only have the outcomes that never formed 10 in the bad count.

But the outcome that forms 10 at die i: we skip it at die i. Then we don't process the remaining dice? Actually, we are processing one die at a time, and we skip the outcome as soon as it forms 10. Then we don't need to consider the rest of the dice for that outcome. So that outcome is not included in the bad count.

Therefore, the DP counts the outcomes that never form 10 at any die. Then the good outcomes = total - bad.

So the example: total=126, bad=49? Then good=77.

Then the probability=77/126=11/18.

So we must get bad=49.

But in our DP, after processing die3, the sum of counts over all states is the bad count.

So we have to run the entire DP and then sum the final states.

But the state space is 2048 states, and we have 100 dice, so we can do a 100*2048*9 = 1.84e6 operations, which is acceptable in Python? Maybe in PyPy/C++ it's acceptable, but in Python we have to be careful. But 1.84e6 is acceptable in Python.

But the example has 4 dice, so 4 * 2048 * 9 = 73728, which is acceptable.

But worst-case 100 * 2048 * 10 = 2e6, which is acceptable.

So we can code accordingly.

But note: we have to initialize the dp[0] with state0 = 1 (bit0 set). Then after all dice, we sum all states.

But we have to be careful: the state space is 2048 states, and we use an array of size 2048 for each die.

Code structure:

  MOD = 998244353
  mask11 = (1 << 11) - 1

  dp = [0] * 2048
  dp[1] = 1   # because state0: bit0 set -> state = 1 (binary 1)

  for a in A:
      next_dp = [0] * 2048
      for s in range(2048):
          if dp[s] == 0:
              continue
          # count_high: numbers from 11 to a (if any)
          count_high = max(0, a - 10)
          # add the high numbers: they leave state unchanged
          next_dp[s] = (next_dp[s] + dp[s] * count_high) % MOD

          # low numbers: from 1 to min(a, 10)
          low_bound = min(a, 10)
          for x in range(1, low_bound+1):
              if x == 10:
                  continue
              # Compute new_state from state s and x
              # Shift the state s by x, then OR with s, then mask to 11 bits.
              new_state = s | (s << x)
              new_state &= mask11

              # If the new_state has the 10th bit set, skip.
              if new_state & (1 << 10):
                  continue

              # Else, add to the next_dp for state new_state.
              next_dp[new_state] = (next_dp[new_state] + dp[s]) % MOD

      dp = next_dp

  bad = sum(dp) % MOD
  total = 1
  for a in A:
      total = total * a % MOD

  good = (total - bad) % MOD
  # Since we are working modulo MOD, we compute:
  ans = good * pow(total, MOD-2, MOD) % MOD

But note: if good is negative, we do: good % MOD.

But (total - bad) % MOD: if total < bad, then we do (total - bad + MOD) % MOD.

Alternatively: good = (total - bad) % MOD, then if good<0, add MOD? But in Python, % returns nonnegative.

But we can do: good = (total - bad) % MOD.

Then compute the answer.

But we must test with the example.

In the example: A=[1,7,2,9]

We start: dp0 = [0]*2048, dp0[1]=1. (state=1: which is the state with only sum0)

Die0: a=1.

  count_high = max(0,1-10)=0.
  low_bound = min(1,10)=1.
  x from 1 to 1: skip if x==10? no.
      new_state = 1 | (1<<1) = 1 | 2 = 3. Then mask: 3. Check: 3 & (1<<10) = 0 -> safe.
      Then next_dp[3] += 1.

  So after die0: dp = [0]*2048; dp[3]=1.

Die1: a=7.

  count_high = max(0,7-10)=0.
  low_bound=7.
  For state=3 (which is 0b11: sums0 and 1), we iterate x from 1 to 7 (skip x=10? but 10 not in range).

  x=1: 
      new_state = 3 | (3<<1) = 3 | 6 = 7 -> safe? 7 & (1<<10)=0 -> safe. Then next_dp[7] +=1.
  x=2: 
      new_state = 3 | (3<<2) = 3 | 12 = 15 -> safe. Then next_dp[15] +=1.
  x=3: 
      new_state = 3 | (3<<3) = 3 | 24 = 27 -> safe. Then next_dp[27] +=1.
  x=4: 
      new_state = 3 | 48 = 51 -> safe. Then next_dp[51] +=1.
  x=5: 
      new_state = 3 | 96 = 99 -> safe. Then next_dp[99] +=1.
  x=6: 
      new_state = 3 | 192 = 195 -> safe. Then next_dp[195] +=1.
  x=7: 
      new_state = 3 | 384 = 387 -> safe. Then next_dp[387] +=1.

  So after die1: we have 7 states: [7,15,27,51,99,195,387] each with count 1.

Die2: a=2.

  count_high=0.
  For each state in die1, we do:

  For state7: (0b111: which is 1+2+4: so sums:0,1,2? Actually, the state is a bitmask: bit0=1, bit1=1, bit2=1 -> sums 0,1,2.
      x=1: new_state = 7 | (7<<1) = 7 | 14 = 15 -> safe. Then next_dp[15] +=1.
      x=2: new_state = 7 | (7<<2) = 7 | 28 = 31 -> safe. Then next_dp[31] +=1.

  For state15: (0b1111: sums0,1,2,3)
      x=1: 15 | (15<<1)=15|30=31 -> next_dp[31] +=1.
      x=2: 15 | (15<<2)=15|60=63 -> next_dp[63] +=1.

  For state27: (binary: 11011: which is 16+8+2+1=27, so bits0,1,3,4 set: so sums0,1,3,4)
      x=1: 27 | (27<<1)=27|54=63 -> next_dp[63] +=1.
      x=2: 27 | (27<<2)=27|108= 111 -> next_dp[111] +=1.

  For state51: (binary: 110011: 32+16+2+1=51? 32+16=48, 51-48=3 -> 2+1, so bits0,1,4,5? Actually, 51 in binary: 00110011 (8 bits) -> bits0,1,4,5? Then shifting by 1: becomes 102, then OR 51 | 102 = 127? Then check: 127 has no 10th bit. But wait, we have to do:

      Actually, we can compute: 51 | (51<<2) = 51 | 204? 51: 00110011, 204: 11001100 -> OR: 11111111 = 255? Then mask: 255. Then check: 255 has the 10th bit? 10th bit is the 10th bit (bit index 10): 255 has only 8 bits? So we mask to 11 bits: 255 is 0b11111111, which is 8 ones. Then no 10th bit. So we add to state255.

      Actually, no: 51<<2 = 51*4=204 -> 204 in binary: 2048 is too big? 204 in 11 bits: 00011001100? Then OR with 51 (000000110011) -> 00011001100 OR 00000110011 = 00011111111? That's 255? Then 255.

      Then next_dp[255] +=1.

      Similarly for x=2: 51 | (51<<2)=51|204=255 -> same? But then for x=2 we get 255 again? Then we add 1 again? So next_dp[255] +=2? (because we have two x: x=1 and x=2) for state51.

  But wait: for state51, we do:
      x=1: 51 | 102 = 51+102? 153? Actually, OR is bitwise OR. 51: 0b110011, 102: 0b1100110 -> then OR: 0b1110111? That's 119? 

  Let me compute in decimal: 51: 0b00110011 (if we represent in 8 bits: 51 is 32+16+2+1? 32+16=48, 51-48=3 -> bits: 32,16,1,0 -> but that's 49? 

  Actually, 51 in binary: 110011 -> bits: 0:1, 1:1, 4:1, 5:1? (since 2^5=32, 2^4=16, 2^1=2, 2^0=1: 32+16+2+1=51). So:

      state51: bits0,1,4,5 set.

      Then for x=1: shift by 1: then bits become 1,2,5,6. Then OR with original: bits0,1,2,4,5,6. That is 1+2+4+16+32+64 = 1+2+4+16+32+64 = 119? 1+2+4=7, 16+32+64=112 -> 119.

      Then new_state=119.

      Then check: 119 in binary: 1110111 -> no bit10? so safe.

      Similarly, for x=2: shift by 2: bits2,3,6,7. OR with original: bits0,1,2,3,4,5,6,7? -> 255? 2^8-1? 255. Then safe.

  So next_dp[119] +=1 and next_dp[255] +=1? Actually, for x=1: state119, for x=2: state255.

  Similarly, we process the other states: 99,195,387. It's going to be a lot.

But after die2, we have many states. Then die3: a=9. Then we process each state and each x in 1..9 (skip 10). Some states will form 10 and be skipped, others not.

At the end, the sum of the dp states is the bad count. We expect 49.

But we don't have to simulate by hand. We trust the DP? But we must test with small cases.

Alternatively, we can test with a trivial case: N=1, A=[10]. Then:

  total = 10.
  bad: we start state=1 (bit0).

  Die0: a=10.
      count_high = max(0,10-10)=0? Actually, high numbers: from 11 to 10 -> 0.
      low_bound = min(10,10)=10.
      x from 1 to 10:
          x=10: skip (because we skip x=10) -> so no state update for x=10.
          for x in [1,9]:
              new_state = 1 | (1<<x) -> which is a state with bits0 and x set.
              then check: if x==10? no. But also, if we form 10? Only if 10 is set? The new_state has bit10 set only if x=10? But we skip x=10. So for x in [1,9], new_state doesn't have bit10. So we add each x to the state: we get 9 states.

      So after die0, we have 9 states? But the state for each x: 
          x=1: state = 1 | (1<<1) = 3 (binary 11) -> bits0,1.
          x=2: state = 1 | 4 = 5 (binary 101) -> bits0,2.
          ... 
          x=9: state = 1 | (1<<9) = 1+512=513.

      Then bad = 9.

      Then good = total - bad = 10 - 9 = 1.
      Probability = 1/10 -> which is the probability that the die shows 10? Because only then we have a subset (the die alone) that sums to 10.

      So the answer = 1 * inv(10) mod MOD.

      But 1/10 mod MOD = pow(10,MOD-2,MOD).

      So the DP for N=1, A=[10] should yield bad=9.

      Then the answer = (10-9)*inv(10) = 1/10.

      So that is correct.

Another test: N=1, A=[9]. Then:

  total=9.
  bad: we do the same: for x=1..9 (skip 10 not needed), and for each x, we form state= (1 | 1<<x) and then check: does it have bit10? no. So we get 9 states. So bad=9? Then good=0. But that is correct: with a die that only has 1..9, we cannot form 10? Because the die alone is at most 9. So no subset sums to 10. Therefore, the condition is not satisfied. So the probability=0. 

But the problem: the condition is that there is a way to choose some of the dice so that the sum is 10. For N=1, A_i=9, we cannot. So the probability is 0. Then our method: we count bad=9 (which are all outcomes) and then good = total - bad = 0. Then answer=0. Correct.

Another test: two dice: A1=10, A2=10.

We want the probability that there exists a subset that sums to 10. The outcomes: (x1,x2). The total outcomes=100.

The outcomes that do not have any subset summing to 10: 
  Conditions: 
      x1 != 10 and x2 != 10 and x1+x2 != 10.

But note: if x1=10, then we have a subset (die1) that sums to 10 -> good.
If x2=10, then good.
If neither is 10, but x1+x2=10, then we have a subset (both dice) that sums to 10 -> good.

So the bad outcomes: 
  x1 != 10, x2 != 10, and x1+x2 != 10.

So the bad outcomes: 
  x1 in [1,9], x2 in [1,9], and x1+x2 != 10.

Number of bad outcomes: 9*9 - (number of pairs (x1,x2) in [1,9]^2 such that x1+x2=10).

Number of pairs that sum to 10: (1,9),(2,8),...,(9,1) -> 9.

So bad = 81 - 9 = 72.

Then good = 100 - 72 = 28.

Probability = 28/100 = 7/25.

Now run the DP:

  Start: state0 = 1 (bit0 set)

  Die0: a0=10.
      count_high=0
      for x in 1..10: skip x=10 -> x in 1..9: 9 outcomes.
      Then we have 9 states: each state is 1 | (1<<x) for x in 1..9.

      So after die0: we have 9 states: states 3,5,9,17,33,65,129,257,513 (each with count 1).

  Die1: a1=10.
      For each state s (from the 9 states), and for x in 1..9 (skip 10) and then count_high=0.

      For a fixed state s (which has bit0 and bit x0 set, where x0 is the value from die0), and for each x in 1..9:

          new_state = s | (s<<x)

          Then check if the 10th bit is set.

          How can the 10th bit be set?
          - If x0+x == 10? Then when we shift s by x, the bit at x0 is shifted to x0+x. Then OR: we set the bit at x0+x=10? Then we skip.
          - Or if x0==10? no, because s doesn't have bit10 (we never set it) and x0<=9.
          - Or if x==10? skipped.

          So we skip if x0+x==10.

          Otherwise, we update.

      Also, we have to consider: if the die1 shows 10, we skip? Actually, we skip x=10. So we don't process x=10.

      So for each state s (with die0=x0), the outcomes for die1 that are bad: the x in [1,9] such that x0+x != 10.

      So for each state s, we have 8 outcomes? (since one x that makes x0+x=10 is skipped).

      Therefore, total bad outcomes = 9 * 8 = 72? Then the DP for die1: we have 9*8=72 states? Then the final bad count=72.

      Then good = 100-72=28.

      Then the answer = 28 * inv(100) mod MOD.

      So that matches.

Therefore, the DP should be correct.

But note: the state might be updated to a new state that has more than two bits? For example, if die0=1 and die1=2, then state after die0: 3 (bits0,1). Then for die1: x=2: 
  new_state = 3 | (3<<2) = 3 | 12 = 15 (bits0,1,2,3). Then we don't form 10? so we add to state15.

But then, if we have a third die, we can form more sums.

But the problem: we are only interested in avoiding 10. So the state is the set of achievable sums up to 10. The sums above 10 are masked away. So the state is a bitmask of 11 bits.

So we are good.

Now, the example input: [1,7,2,9] -> bad count=49? Then total=126, so good=77, then answer=77 * inv(126) mod MOD = 942786334.

But we have to compute that: 77 * pow(126, MOD-2, MOD) % MOD.

But we know the expected answer is 942786334.

So we can code and test.

But we must be cautious: the state space: we use an array of 2048 integers per die.

Let me code accordingly.

But note: the state is an integer in [0,2047]. We use an array of length 2048 for dp.

We initialize: dp[1] = 1.

Then for each die, we create a new dp_next[2048] and update.

Finally, bad = sum(dp) % MOD.

Then total = product(A) % MOD.

Then good = (total - bad) % MOD.

Then ans = good * pow(total, MOD-2, MOD) % MOD.

But note: the product total might be huge, but we do modulo MOD.

We'll run the example.

But the example has 4 dice: A=[1,7,2,9]. Then:

  total = 1*7*2*9 = 126.

  Then we compute bad = 49? Then good=77.

  Then 77 * mod_inv(126, MOD) mod MOD = 942786334.

We can compute mod_inv(126, MOD) using pow(126, MOD-2, MOD).

But let me compute 77 * pow(126, 998244353-2, 998244353) % 998244353.

We can do in Python:

  MOD = 998244353
  total = 126
  good = 77
  ans = good * pow(total, MOD-2, MOD) % MOD

  print(ans)  # should be 942786334

Yes, we can check.

Now, the second example: 
  Input: 7
         1 10 100 1000 10000 100000 1000000

  Output: 996117877

We have to run the DP for N=7 and A_i as given. The numbers are large, but the inner loop per state is at most 9 or 10? Actually, the low_bound is min(A_i,10), so at most 10, and we skip x=10, so 9 iterations per state. And the state space is 2048. So total operations: 7 * 2048 * 9 = about 129024, which is acceptable.

But the product total: 1*10*100*1000*10000*100000*1000000 is 10^1 * 10^2 * ... * 10^6 = 10^(1+2+3+4+5+6)=10^21. Then total mod MOD = 10^21 % MOD. Then we compute.

We can code and run.

But we must be cautious: the DP might be correct.

So we'll code accordingly.

Edge: when A_i==0? But constraints: A_i>=1.

Let me write the code.

But note: we are using a fixed state size: 2048 states. We use an array of 2048 for dp.

We'll initialize:

  dp = [0]*2048
  dp[1] = 1   # state0: bit0 set

  for i in range(N):
      a = A[i]
      next_dp = [0]*2048
      for s in range(2048):
          if dp[s]==0: 
              continue
          count_high = max(0, a - 10)
          # For the high numbers: they don't change the state (and don't form 10, because state s doesn't have 10 and x>10 won't form 10 with existing sums since existing sums are at most 10? Actually, existing sums are in [0,10], then adding x>10 gives at least 11, so not recorded. So state remains s.
          next_dp[s] = (next_dp[s] + dp[s] * count_high) % MOD

          low_bound = min(a, 10)
          for x in range(1, low_bound+1):
              if x == 10:
                  continue
              # Compute new_state = s | (s<<x)   then mask to 11 bits
              new_state = (s | (s << x)) & mask11
              # Check if bit10 is set
              if new_state & (1<<10):
                  continue
              else:
                  next_dp[new_state] = (next_dp[new_state] + dp[s]) % MOD
      dp = next_dp

  bad = sum(dp) % MOD
  total = 1
  for a in A:
      total = (total * a) % MOD
  good = (total - bad) % MOD
  ans = good * pow(total, MOD-2, MOD) % MOD

But note: the modulus for the inverse.

Let me test with the small cases we did.

Test: N=1, A=[10]: 
  dp0: [0]*2048, dp0[1]=1.
  Then for a=10:
      count_high = 0
      low_bound=10
      for x in 1..10: skip x=10 -> x in 1..9: 
          for each x: 
             new_state = 1 | (1<<x) -> which is (1 | (1<<x)) & mask11. Since 1<<x is at most 1<<9=512, then mask11 is 2047, so no problem.
          Then we set next_dp[new_state] +=1 for each x in 1..9.
      Then next_dp has 9 states set to 1? Then bad = 9.
      total = 10.
      good = 10-9=1.
      ans = 1 * pow(10, MOD-2, MOD) % MOD.

  But 1/10 mod MOD is a big number? But we know the answer for N=1,A=[10] should be the modular inverse of 10.

  But the example input: 4 dice -> output 942786334.

  We'll run the provided examples.

But we must note: the state is masked to 11 bits. So we do: mask11 = (1<<11)-1 = 2047.

Now, I'll code and test with the examples.

But we can also test: N=0? But constraints: N>=1.

We'll run the example: [1,7,2,9] and see if we get bad=49.

But in the code: we'll print bad and total.

Alternatively, we can compute:

  bad = 49 -> then good = 126-49=77 -> 77 * inv(126) mod MOD = 942786334.

So if the code returns 942786334, it's correct.

Now, let me write the code accordingly.

But note: the DP might be slow in Pyton for 100*2048*9=1.84e6? It should be acceptable.

We'll code.

But the second example: 7 dice, with A_i: [1,10,100,1000,10000,100000,1000000]

  We must get 996117877.

We'll run.

But we have to compute the product total modulo MOD: 1*10*100*...*1000000 mod MOD.

But 1000000! is huge? But we do modulo MOD at each multiplication.

Let me compute total:

  total = 1
  for a in [1,10,100,1000,10000,100000,1000000]:
      total = total * a % MOD

  Then we compute.

  But 1*10*100 = 1000, then *1000=10^6, then *10000=10^10, then *100000=10^15, then *1000000=10^21.

  10^21 % 998244353: we can compute.

  But we don't need to know, the code will compute.

Now, we code.

Potential pitfall: the state update: shifting s by x: s is an integer, and shifting by x (up to 10) is safe because we mask to 11 bits. So we do: (s | (s<<x)) & mask11.

But if we shift by x=10? But we skip x=10. So we only shift by 1..9.

So the shift is at most 9, so

## Final Solution

