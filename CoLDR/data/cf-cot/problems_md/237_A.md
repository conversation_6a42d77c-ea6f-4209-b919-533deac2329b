# Free Cash

**题目ID**: 237/A  
**比赛**: Codeforces Round 147 (Div. 2)  
**年份**: 2012  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON><PERSON> runs a 24/7 fast food cafe. He magically learned that next day n people will visit his cafe. For each person we know the arrival time: the i-th person comes exactly at hi hours mi minutes. The cafe spends less than a minute to serve each client, but if a client comes in and sees that there is no free cash, than he doesn't want to wait and leaves the cafe immediately.

<PERSON><PERSON> is very greedy, so he wants to serve all n customers next day (and get more profit). However, for that he needs to ensure that at each moment of time the number of working cashes is no less than the number of clients in the cafe.

Help <PERSON><PERSON> count the minimum number of cashes to work at his cafe next day, so that they can serve all visitors.

## 输入格式

The first line contains a single integer n (1 ≤ n ≤ 105), that is the number of cafe visitors.

Each of the following n lines has two space-separated integers hi and mi (0 ≤ hi ≤ 23; 0 ≤ mi ≤ 59), representing the time when the i-th person comes into the cafe.

Note that the time is given in the chronological order. All time is given within one 24-hour period.

## 输出格式

Print a single integer — the minimum number of cashes, needed to serve all clients next day.

## 样例

### 样例 1

**输入**:
```
4
8 0
8 10
8 10
8 45
```

**输出**:
```
2
```

### 样例 2

**输入**:
```
3
0 12
10 11
22 22
```

**输出**:
```
1
```

## 备注

In the first sample it is not enough one cash to serve all clients, because two visitors will come into cafe in 8:10. Therefore, if there will be one cash in cafe, then one customer will be served by it, and another one will not wait and will go away.

In the second sample all visitors will come in different times, so it will be enough one cash.

