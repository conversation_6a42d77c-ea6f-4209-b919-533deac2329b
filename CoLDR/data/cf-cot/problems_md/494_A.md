# Treasure

**题目ID**: 494/A  
**比赛**: Codeforces Round 282 (Div. 1)  
**年份**: 2014  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON><PERSON> has recently found a treasure map. While he was looking for a treasure he found a locked door. There was a string s written on the door consisting of characters '(', ')' and '#'. Below there was a manual on how to open the door. After spending a long time <PERSON><PERSON> managed to decode the manual and found out that the goal is to replace each '#' with one or more ')' characters so that the final string becomes beautiful.

Below there was also written that a string is called beautiful if for each i (1 ≤ i ≤ |s|) there are no more ')' characters than '(' characters among the first i characters of s and also the total number of '(' characters is equal to the total number of ')' characters.

Help <PERSON><PERSON> open the door by telling him for each '#' character how many ')' characters he must replace it with.

## 输入格式

The first line of the input contains a string s (1 ≤ |s| ≤ 105). Each character of this string is one of the characters '(', ')' or '#'. It is guaranteed that s contains at least one '#' character.

## 输出格式

If there is no way of replacing '#' characters which leads to a beautiful string print  - 1. Otherwise for each character '#' print a separate line containing a positive integer, the number of ')' characters this character must be replaced with.

If there are several possible answers, you may output any of them.

## 样例

### 样例 1

**输入**:
```
(((#)((#)
```

**输出**:
```
1
2
```

### 样例 2

**输入**:
```
()((#((#(#()
```

**输出**:
```
2
2
1
```

### 样例 3

**输入**:
```
#
```

**输出**:
```
-1
```

### 样例 4

**输入**:
```
(#)
```

**输出**:
```
-1
```

## 备注

|s| denotes the length of the string s.

