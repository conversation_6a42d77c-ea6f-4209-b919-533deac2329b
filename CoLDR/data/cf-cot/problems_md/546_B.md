# Soldier and Badges

**题目ID**: 546/B  
**比赛**: Codeforces Round 304 (Div. 2)  
**年份**: 2015  
**时间限制**: 3.0秒  
**内存限制**: 256MB  

## 题目描述

Colonel has n badges. He wants to give one badge to every of his n soldiers. Each badge has a coolness factor, which shows how much it's owner reached. Coolness factor can be increased by one for the cost of one coin.

For every pair of soldiers one of them should get a badge with strictly higher factor than the second one. Exact values of their factors aren't important, they just need to have distinct factors.

Colonel knows, which soldier is supposed to get which badge initially, but there is a problem. Some of badges may have the same factor of coolness. Help him and calculate how much money has to be paid for making all badges have different factors of coolness.

## 输入格式

First line of input consists of one integer n (1 ≤ n ≤ 3000).

Next line consists of n integers ai (1 ≤ ai ≤ n), which stand for coolness factor of each badge.

## 输出格式

Output single integer — minimum amount of coins the colonel has to pay.

## 样例

### 样例 1

**输入**:
```
4
1 3 1 4
```

**输出**:
```
1
```

### 样例 2

**输入**:
```
5
1 2 3 2 5
```

**输出**:
```
2
```

## 备注

In first sample test we can increase factor of first badge by 1.

In second sample test we can increase factors of the second and the third badge by 1.

## 题解

Let's count the number of badges with coolness factor 1, 2 and so on. Then, let's look at the number of badges with value equal to 1. If it's greater than 1, we have to increase a value of every of them except for one. Then, we look at number of badges with value 2, 3 and so on up to 2n - 2 (because maximum value of badge which we can achieve is 2n - 1). It is easy to see that this is the correct solution. We can implement it in O(n), but solutions that work in complexity O(n2) also passed.

