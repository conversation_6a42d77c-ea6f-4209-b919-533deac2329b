# Kyoya and Permutation

**题目ID**: 553/B  
**比赛**: Codeforces Round 309 (Div. 1)  
**年份**: 2015  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

Let's define the permutation of length n as an array p = [p1, p2, ..., pn] consisting of n distinct integers from range from 1 to n. We say that this permutation maps value 1 into the value p1, value 2 into the value p2 and so on.

<PERSON><PERSON><PERSON> has just learned about cyclic representation of a permutation. A cycle is a sequence of numbers such that each element of this sequence is being mapped into the next element of this sequence (and the last element of the cycle is being mapped into the first element of the cycle). The cyclic representation is a representation of p as a collection of cycles forming p. For example, permutation p = [4, 1, 6, 2, 5, 3] has a cyclic representation that looks like (142)(36)(5) because 1 is replaced by 4, 4 is replaced by 2, 2 is replaced by 1, 3 and 6 are swapped, and 5 remains in place.

Permutation may have several cyclic representations, so <PERSON><PERSON><PERSON> defines the standard cyclic representation of a permutation as follows. First, reorder the elements within each cycle so the largest element is first. Then, reorder all of the cycles so they are sorted by their first element. For our example above, the standard cyclic representation of [4, 1, 6, 2, 5, 3] is (421)(5)(63).

Now, <PERSON>yoya notices that if we drop the parenthesis in the standard cyclic representation, we get another permutation! For instance, [4, 1, 6, 2, 5, 3] will become [4, 2, 1, 5, 6, 3].

Kyoya notices that some permutations don't change after applying operation described above at all. He wrote all permutations of length n that do not change in a list in lexicographic order. Unfortunately, his friend Tamaki Suoh lost this list. Kyoya wishes to reproduce the list and he needs your help. Given the integers n and k, print the permutation that was k-th on Kyoya's list.

## 输入格式

The first line will contain two integers n, k (1 ≤ n ≤ 50, 1 ≤ k ≤ min{1018, l} where l is the length of the Kyoya's list).

## 输出格式

Print n space-separated integers, representing the permutation that is the answer for the question.

## 样例

### 样例 1

**输入**:
```
4 3
```

**输出**:
```
1 3 2 4
```

### 样例 2

**输入**:
```
10 1
```

**输出**:
```
1 2 3 4 5 6 7 8 9 10
```

## 备注

The standard cycle representation is (1)(32)(4), which after removing parenthesis gives us the original permutation. The first permutation on the list would be [1, 2, 3, 4], while the second permutation would be [1, 2, 4, 3].

## 题解

Solving this requires making the observation that only swaps between adjacent elements are allowed, and all of these swaps must be disjoint.
To prove this, consider the cycle that contains n. If this cycle is length 1, then we are ok. If the cycle is length 2, we need n to be involved in a cycle with n-1. Lastly, if the cycle is of length 3 or more, we will see we run into a problem. So, the only valid cases are the cycles of length 1 or 2. This means we can essentially ignore the elements in the cycle involved with n and continue with our reasoning recursively.
After making this observation, we can see the number of valid permutations of length n is fib(n+1).
So, to reconstruct the kth permutation in the list, we do this as follows: If k is less than fib(n), then return the kth permutation on {1,...,n-1}, add 1 to everything and add 1 in front. Otherwise, return the k-fib(n)th permutation on {1,...,n-2}, add 2 to everything, and add 2 1 in front.

