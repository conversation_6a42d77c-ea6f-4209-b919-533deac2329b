# Helping People

**题目ID**: 494/C  
**比赛**: Codeforces Round 282 (Div. 1)  
**年份**: 2014  
**时间限制**: 2.0秒  
**内存限制**: 512MB  

## 题目描述

<PERSON><PERSON> is a rich man. He also is very generous. That's why he decided to split his money between poor people. A charity institute knows n poor people numbered from 1 to n. The institute gave <PERSON><PERSON> q recommendations. A recommendation is a segment of people like [l, r] which means the institute recommended that <PERSON><PERSON> gives one dollar to every person whose number is in this segment.

However this charity has very odd rules about the recommendations. Because of those rules the recommendations are given in such a way that for every two recommendation [a, b] and [c, d] one of the following conditions holds:

- The two segments are completely disjoint. More formally either a ≤ b < c ≤ d or c ≤ d < a ≤ b
- One of the two segments are inside another. More formally either a ≤ c ≤ d ≤ b or c ≤ a ≤ b ≤ d.

The goodness of a charity is the value of maximum money a person has after <PERSON><PERSON> finishes giving his money. The institute knows for each recommendation what is the probability that <PERSON><PERSON> will accept it. They want to know the expected value of goodness of this charity. So they asked you for help.

You have been given the list of recommendations and for each recommendation the probability of it being accepted by <PERSON><PERSON>. You have also been given how much money each person initially has. You must find the expected value of goodness.

## 输入格式

In the first line two space-separated integers n, q (1 ≤ n ≤ 105, 1 ≤ q ≤ 5000) are given.

In the second line n space-separated integers a1, a2, ..., an (0 ≤ ai ≤ 109) are given meaning that person number i initially has ai dollars.

Each of the next q lines contains three space-separated numbers li, ri, pi (1 ≤ li ≤ ri ≤ n, 0 ≤ p ≤ 1) where li and ri are two integers describing the segment of recommendation and pi is a real number given with exactly three digits after decimal point which is equal to probability of Malek accepting this recommendation.

Note that a segment may appear several times in recommendations.

## 输出格式

Output the sought value. Your answer will be considered correct if its absolute or relative error is less than 10 - 6.

## 样例

### 样例 1

**输入**:
```
5 2
1 7 2 4 3
1 3 0.500
2 2 0.500
```

**输出**:
```
8.000000000
```

### 样例 2

**输入**:
```
5 2
281 280 279 278 282
1 4 1.000
1 4 0.000
```

**输出**:
```
282.000000000
```

### 样例 3

**输入**:
```
3 5
1 2 3
1 3 0.500
2 2 0.250
1 2 0.800
1 1 0.120
2 2 0.900
```

**输出**:
```
4.465000000
```

