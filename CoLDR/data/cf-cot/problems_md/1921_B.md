# Arranging Cats

**题目ID**: 1921/B  
**比赛**: Codeforces Round 920 (Div. 3)  
**年份**: 2024  
**时间限制**: 2.0秒  
**内存限制**: 512MB  

## 题目描述

In order to test the hypothesis about the cats, the scientists must arrange the cats in the boxes in a specific way. Of course, they would like to test the hypothesis and publish a sensational article as quickly as possible, because they are too engrossed in the next hypothesis about the phone's battery charge.

Scientists have $$$n$$$ boxes in which cats may or may not sit. Let the current state of the boxes be denoted by the sequence $$$b_1, \dots, b_n$$$: $$$b_i = 1$$$ if there is a cat in box number $$$i$$$, and $$$b_i = 0$$$ otherwise.

Fortunately, the unlimited production of cats has already been established, so in one day, the scientists can perform one of the following operations:

- Take a new cat and place it in a box (for some $$$i$$$ such that $$$b_i = 0$$$, assign $$$b_i = 1$$$).
- Remove a cat from a box and send it into retirement (for some $$$i$$$ such that $$$b_i = 1$$$, assign $$$b_i = 0$$$).
- Move a cat from one box to another (for some $$$i, j$$$ such that $$$b_i = 1, b_j = 0$$$, assign $$$b_i = 0, b_j = 1$$$).

It has also been found that some boxes were immediately filled with cats. Therefore, the scientists know the initial position of the cats in the boxes $$$s_1, \dots, s_n$$$ and the desired position $$$f_1, \dots, f_n$$$.

Due to the large amount of paperwork, the scientists do not have time to solve this problem. Help them for the sake of science and indicate the minimum number of days required to test the hypothesis.

## 输入格式

Each test consists of several test cases. The first line contains a single integer $$$t$$$ ($$$1 \le t \le 10^4$$$) — the number of test cases. This is followed by descriptions of the test cases.

Each test case consists of three lines.

The first line of each test case contains a single integer $$$n$$$ ($$$1 \le n \le 10^5$$$) — the number of boxes.

The second line of each test case contains a string $$$s$$$ of $$$n$$$ characters, where the $$$i$$$-th character is '1' if there is a cat in the $$$i$$$-th box and '0' otherwise.

The third line of each test case contains a string $$$f$$$ of $$$n$$$ characters, where the $$$i$$$-th character is '1' if there should be a cat in the $$$i$$$-th box and '0' otherwise.

It is guaranteed that in a test the sum of $$$n$$$ over all test cases does not exceed $$$10^5$$$.

## 输出格式

For each test case, output a single integer on a separate line — the minimum number of operations required to obtain the desired position from the initial position. It can be shown that a solution always exists.

## 样例

### 样例 1

**输入**:
```
6
5
10010
00001
1
1
1
3
000
111
4
0101
1010
3
100
101
8
10011001
11111110
```

**输出**:
```
2
0
3
2
1
4
```

## 备注

In the first test case, you can first move the cat from the first box to the fifth, and then remove the cat from the fourth box.

In the second test case, there is nothing to do — the only cat is already sitting in the correct box.

In the third test case of input data, it takes three days to place a cat in each box.

## 题解

Denote the amount of indices $$$i$$$ such that $$$s_i = 0$$$ and $$$f_i = 1$$$ as $$$add\_amnt$$$. Since it is impossible to change 0 to 1 at two different positions in one turn, the answer is not less than $$$add\_amnt$$$. Analogously, if $$$rmv\_amnt$$$ is amount of indices such that $$$s_i = 1$$$ and $$$f_i = 0$$$, the answer is not less than $$$rmv\_amnt$$$.
It turns out that the answer is actually equal to $$$\max (add\_amnt, rmv\_amnt)$$$. We can simply apply move operation from the index $$$i$$$ with $$$s_i = 1, f_i = 0$$$ to $$$j$$$ with $$$s_j = 0, f_j = 1$$$ while there are both of these types of indices (that will be $$$\min (rmv\_amnt, add\_amnt)$$$ operations) and then add or remove the rest of unsatisfied indices (that is exactly $$$|rmv\_amnt - add\_amnt|$$$ operations).

