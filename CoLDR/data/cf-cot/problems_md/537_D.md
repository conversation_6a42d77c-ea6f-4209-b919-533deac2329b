# Weird Chess

**题目ID**: 537/D  
**比赛**: VK Cup 2015 - Wild Card Round 2  
**年份**: 2015  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON> has been into chess for a long time and now he is sick of the game by the ordinary rules. He is going to think of new rules of the game and become world famous.

<PERSON>'s chessboard is a square of size n × n cells. <PERSON> decided that simple rules guarantee success, that's why his game will have only one type of pieces. Besides, all pieces in his game are of the same color. The possible moves of a piece are described by a set of shift vectors. The next passage contains a formal description of available moves.

Let the rows of the board be numbered from top to bottom and the columns be numbered from left to right from 1 to n. Let's assign to each square a pair of integers (x, y) — the number of the corresponding column and row. Each of the possible moves of the piece is defined by a pair of integers (dx, dy); using this move, the piece moves from the field (x, y) to the field (x + dx, y + dy). You can perform the move if the cell (x + dx, y + dy) is within the boundaries of the board and doesn't contain another piece. Pieces that stand on the cells other than (x, y) and (x + dx, y + dy) are not important when considering the possibility of making the given move (for example, like when a knight moves in usual chess).

<PERSON> offers you to find out what moves his chess piece can make. He placed several pieces on the board and for each unoccupied square he told you whether it is attacked by any present piece (i.e. whether some of the pieces on the field can move to that cell). Restore a possible set of shift vectors of the piece, or else determine that Igor has made a mistake and such situation is impossible for any set of shift vectors.

## 输入格式

The first line contains a single integer n (1 ≤ n ≤ 50).

The next n lines contain n characters each describing the position offered by Igor. The j-th character of the i-th string can have the following values:

- o — in this case the field (i, j) is occupied by a piece and the field may or may not be attacked by some other piece;
- x — in this case field (i, j) is attacked by some piece;
- . — in this case field (i, j) isn't attacked by any piece.

It is guaranteed that there is at least one piece on the board.

## 输出格式

If there is a valid set of moves, in the first line print a single word 'YES' (without the quotes). Next, print the description of the set of moves of a piece in the form of a (2n - 1) × (2n - 1) board, the center of the board has a piece and symbols 'x' mark cells that are attacked by it, in a format similar to the input. See examples of the output for a full understanding of the format. If there are several possible answers, print any of them.

If a valid set of moves does not exist, print a single word 'NO'.

## 样例

### 样例 1

**输入**:
```
5
oxxxx
x...x
x...x
x...x
xxxxo
```

**输出**:
```
YES
....x....
....x....
....x....
....x....
xxxxoxxxx
....x....
....x....
....x....
....x....
```

### 样例 2

**输入**:
```
6
.x.x..
x.x.x.
.xo..x
x..ox.
.x.x.x
..x.x.
```

**输出**:
```
YES
...........
...........
...........
....x.x....
...x...x...
.....o.....
...x...x...
....x.x....
...........
...........
...........
```

### 样例 3

**输入**:
```
3
o.x
oxx
o.x
```

**输出**:
```
NO
```

## 备注

In the first sample test the piece is a usual chess rook, and in the second sample test the piece is a usual chess knight.

