# Rock-paper-scissors

**题目ID**: 48/A  
**比赛**: School Personal Contest #3 (Winter Computer School 2010/11) - Codeforces Beta Round 45 (ACM-ICPC Rules)  
**年份**: 2010  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

Uncle <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> the Cat and <PERSON><PERSON><PERSON> the Dog live their simple but happy lives in Prostokvashino. Sometimes they receive parcels from Uncle <PERSON><PERSON><PERSON>’s parents and sometimes from anonymous benefactors, in which case it is hard to determine to which one of them the package has been sent. A photographic rifle is obviously for <PERSON><PERSON><PERSON> who loves hunting and fish is for <PERSON><PERSON><PERSON>, but for whom was a new video game console meant? Every one of the three friends claimed that the present is for him and nearly quarreled. Uncle <PERSON><PERSON><PERSON> had an idea how to solve the problem justly: they should suppose that the console was sent to all three of them and play it in turns. Everybody got relieved but then yet another burning problem popped up — who will play first? This time <PERSON><PERSON><PERSON> came up with a brilliant solution, suggesting the most fair way to find it out: play rock-paper-scissors together. The rules of the game are very simple. On the count of three every player shows a combination with his hand (or paw). The combination corresponds to one of three things: a rock, scissors or paper. Some of the gestures win over some other ones according to well-known rules: the rock breaks the scissors, the scissors cut the paper, and the paper gets wrapped over the stone. Usually there are two players. Yet there are three friends, that’s why they decided to choose the winner like that: If someone shows the gesture that wins over the other two players, then that player wins. Otherwise, another game round is required. Write a program that will determine the winner by the gestures they have shown.

## 输入格式

The first input line contains the name of the gesture that Uncle <PERSON><PERSON>dor showed, the second line shows which gesture Matroskin showed and the third line shows Sharic’s gesture.

## 输出格式

Print "F" (without quotes) if Uncle Fyodor wins. Print "M" if Matroskin wins and "S" if Sharic wins. If it is impossible to find the winner, print "?".

## 样例

### 样例 1

**输入**:
```
rock
rock
rock
```

**输出**:
```
?
```

### 样例 2

**输入**:
```
paper
rock
rock
```

**输出**:
```
F
```

### 样例 3

**输入**:
```
scissors
rock
rock
```

**输出**:
```
?
```

### 样例 4

**输入**:
```
scissors
paper
rock
```

**输出**:
```
?
```

