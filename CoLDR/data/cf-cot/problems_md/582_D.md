# Number of Binominal Coefficients

**题目ID**: 582/D  
**比赛**: Codeforces Round 323 (Div. 1)  
**年份**: 2015  
**时间限制**: 4.0秒  
**内存限制**: 256MB  

## 题目描述

For a given prime integer p and integers α, A calculate the number of pairs of integers (n, k), such that 0 ≤ k ≤ n ≤ A and $${\binom {n}{k}}$$ is divisible by pα.

As the answer can be rather large, print the remainder of the answer moduly 109 + 7.

Let us remind you that $${\binom {n}{k}}$$ is the number of ways k objects can be chosen from the set of n objects.

## 输入格式

The first line contains two integers, p and α (1 ≤ p, α ≤ 109, p is prime).

The second line contains the decimal record of integer A (0 ≤ A < 101000) without leading zeroes.

## 输出格式

In the single line print the answer to the problem.

## 样例

### 样例 1

**输入**:
```
2 2
7
```

**输出**:
```
3
```

### 样例 2

**输入**:
```
3 1
9
```

**输出**:
```
17
```

### 样例 3

**输入**:
```
3 3
9
```

**输出**:
```
0
```

### 样例 4

**输入**:
```
2 4
5000
```

**输出**:
```
8576851
```

## 备注

In the first sample three binominal coefficients divisible by 4 are $${ \binom { 4 } { 1 } } = 4$$, $${ \binom { 4 } { 3 } } = 4$$ and $${ \binom { 6 } { 3 } } = 20$$.

