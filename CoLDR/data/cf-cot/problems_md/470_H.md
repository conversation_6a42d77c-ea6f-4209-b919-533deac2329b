# Array Sorting

**题目ID**: 470/H  
**比赛**: Surprise Language Round 7  
**年份**: 2014  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

Sorting arrays is traditionally associated with high-level languages. How hard can it be in FALSE? Sort the given array in non-descending order.

## 输入格式

The input consists of a single line of space-separated integers. The first number is n (1 ≤ n ≤ 10) — the size of the array. The following n numbers are the elements of the array (1 ≤ ai ≤ 100).

## 输出格式

Output space-separated elements of the sorted array.

## 样例

### 样例 1

**输入**:
```
3 3 1 2
```

**输出**:
```
1 2 3
```

### 样例 2

**输入**:
```
7 12 2 3 44 5 60 2
```

**输出**:
```
2 2 3 5 12 44 60
```

