# Sloth

**题目ID**: 891/D  
**比赛**: Codeforces Round 446 (Div. 1)  
**年份**: 2017  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

Sloth is bad, mkay? So we decided to prepare a problem to punish lazy guys.

You are given a tree, you should count the number of ways to remove an edge from it and then add an edge to it such that the final graph is a tree and has a perfect matching. Two ways of this operation are considered different if their removed edges or their added edges aren't the same. The removed edge and the added edge can be equal.

A perfect matching is a subset of edges such that each vertex is an endpoint of exactly one of these edges.

## 输入格式

The first line contains n (2 ≤ n ≤ 5·105) — the number of vertices.

Each of the next n - 1 lines contains two integers a and b (1 ≤ a, b ≤ n) — the endpoints of one edge. It's guaranteed that the graph is a tree.

## 输出格式

Output a single integer — the answer to the problem.

## 样例

### 样例 1

**输入**:
```
4
1 2
2 3
3 4
```

**输出**:
```
8
```

### 样例 2

**输入**:
```
5
1 2
2 3
3 4
3 5
```

**输出**:
```
0
```

### 样例 3

**输入**:
```
8
1 2
2 3
3 4
1 5
5 6
6 7
1 8
```

**输出**:
```
22
```

## 备注

In first sample, there are 8 ways:

- edge between 2 and 3 turns to edge between 1 and 3,
- edge between 2 and 3 turns to edge between 1 and 4,
- edge between 2 and 3 turns to edge between 2 and 3,
- edge between 2 and 3 turns to edge between 2 and 4,
- edge between 1 and 2 turns to edge between 1 and 2,
- edge between 1 and 2 turns to edge between 1 and 4,
- edge between 3 and 4 turns to edge between 1 and 4,
- edge between 3 and 4 turns to edge between 3 and 4.

## 题解

If graph had odd number of vertices the answer is 0. Otherwise let's call edges that by removing them the remaining graph would have two even components good, and all the other edges are bad.
If you remove a good edge and put another edge somewhere such that the final graph is a tree, then it would have prefect matching if and only if the input tree had prefect matching.
If no two bad edges share a vertex, after removing a bad edge (lets call it X) we should chose the end points of the edge we want to add (lets call them v,u) such that the path between v and u in the input tree has alternately bad and good edges, the first and the last edges in the path are bad and X is in this path too. So for any path in the tree that has alternately bad and good edges and the first and final edges in it are bad we should add the $${ \frac { LengthOfThisPath + 1 } { 2 } }$$ to the answer. This can be done using dp.
If there are bad edges that share vertices, we know that each vertex has odd number of bad edges to its neighbors, and if this number is greater than 3 then the answer is 0. So each vertex has 1 or 3 odd edges to its neighbors. The path between end points of added edge should contain all the vertices with 3 bad edges and also two of their bad edges should be in the path. So if the vertices with 3 bad edges aren't in a path with this condition then the answer is 0 and otherwise we can calculate the answer by checking some conditions in their path and counting the number of paths with some condition at the end points of their path.

