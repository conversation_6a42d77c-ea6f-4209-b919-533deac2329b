# Cheap Travel

**题目ID**: 466/A  
**比赛**: Codeforces Round 266 (Div. 2)  
**年份**: 2014  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON> has recently started commuting by subway. We know that a one ride subway ticket costs a rubles. Besides, <PERSON> found out that she can buy a special ticket for m rides (she can buy it several times). It costs b rubles. <PERSON> did the math; she will need to use subway n times. Help <PERSON>, tell her what is the minimum sum of money she will have to spend to make n rides?

## 输入格式

The single line contains four space-separated integers n, m, a, b (1 ≤ n, m, a, b ≤ 1000) — the number of rides <PERSON> has planned, the number of rides covered by the m ride ticket, the price of a one ride ticket and the price of an m ride ticket.

## 输出格式

Print a single integer — the minimum sum in rubles that <PERSON> will need to spend.

## 样例

### 样例 1

**输入**:
```
6 2 1 2
```

**输出**:
```
6
```

### 样例 2

**输入**:
```
5 2 2 3
```

**输出**:
```
8
```

## 备注

In the first sample one of the optimal solutions is: each time buy a one ride ticket. There are other optimal solutions. For example, buy three m ride tickets.

