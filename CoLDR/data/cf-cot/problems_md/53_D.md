# Physical Education

**题目ID**: 53/D  
**比赛**: Codeforces Beta Round 49 (Div. 2)  
**年份**: 2011  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON><PERSON><PERSON> is a school PE teacher. Unlike other PE teachers, <PERSON><PERSON><PERSON> doesn't like it when the students stand in line according to their height. Instead, he demands that the children stand in the following order: a1, a2, ..., an, where ai is the height of the i-th student in the line and n is the number of students in the line. The children find it hard to keep in mind this strange arrangement, and today they formed the line in the following order: b1, b2, ..., bn, which upset <PERSON><PERSON><PERSON> immensely. Now <PERSON><PERSON><PERSON> wants to rearrange the children so that the resulting order is like this: a1, a2, ..., an. During each move <PERSON><PERSON><PERSON> can swap two people who stand next to each other in the line. Help <PERSON><PERSON><PERSON>, find the sequence of swaps leading to the arrangement <PERSON><PERSON><PERSON> needs. It is not required to minimize the number of moves.

## 输入格式

The first line contains an integer n (1 ≤ n ≤ 300) which is the number of students. The second line contains n space-separated integers ai (1 ≤ ai ≤ 109) which represent the height of the student occupying the i-th place must possess. The third line contains n space-separated integers bi (1 ≤ bi ≤ 109) which represent the height of the student occupying the i-th place in the initial arrangement. It is possible that some students possess similar heights. It is guaranteed that it is possible to arrange the children in the required order, i.e. a and b coincide as multisets.

## 输出格式

In the first line print an integer k (0 ≤ k ≤ 106) which is the number of moves. It is not required to minimize k but it must not exceed 106. Then print k lines each containing two space-separated integers. Line pi, pi + 1 (1 ≤ pi ≤ n - 1) means that Vasya should swap students occupying places pi and pi + 1.

## 样例

### 样例 1

**输入**:
```
4
1 2 3 2
3 2 1 2
```

**输出**:
```
4
2 3
1 2
3 4
2 3
```

### 样例 2

**输入**:
```
2
1 100500
1 100500
```

**输出**:
```
0
```

