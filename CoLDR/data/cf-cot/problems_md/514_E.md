# Darth Vader and Tree

**题目ID**: 514/E  
**比赛**: Codeforces Round 291 (Div. 2)  
**年份**: 2015  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

When <PERSON><PERSON> Vader gets bored, he sits down on the sofa, closes his eyes and thinks of an infinite rooted tree where each node has exactly n sons, at that for each node, the distance between it an its i-th left child equals to di. The Sith Lord loves counting the number of nodes in the tree that are at a distance at most x from the root. The distance is the sum of the lengths of edges on the path between nodes.

But he has got used to this activity and even grew bored of it. 'Why does he do that, then?' — you may ask. It's just that he feels superior knowing that only he can solve this problem.

Do you want to challenge <PERSON><PERSON> himself? Count the required number of nodes. As the answer can be rather large, find it modulo 109 + 7.

## 输入格式

The first line contains two space-separated integers n and x (1 ≤ n ≤ 105, 0 ≤ x ≤ 109) — the number of children of each node and the distance from the root within the range of which you need to count the nodes.

The next line contains n space-separated integers di (1 ≤ di ≤ 100) — the length of the edge that connects each node with its i-th child.

## 输出格式

Print a single number — the number of vertexes in the tree at distance from the root equal to at most x.

## 样例

### 样例 1

**输入**:
```
3 3
1 2 3
```

**输出**:
```
8
```

## 备注

Pictures to the sample (the yellow color marks the nodes the distance to which is at most three)

