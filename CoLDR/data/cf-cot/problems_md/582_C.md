# Superior Periodic Subarrays

**题目ID**: 582/C  
**比赛**: Codeforces Round 323 (Div. 1)  
**年份**: 2015  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

You are given an infinite periodic array a0, a1, ..., an - 1, ... with the period of length n. Formally, $$a_{i} = a_{i \bmod n}$$. A periodic subarray (l, s) (0 ≤ l < n, 1 ≤ s < n) of array a is an infinite periodic array with a period of length s that is a subsegment of array a, starting with position l.

A periodic subarray (l, s) is superior, if when attaching it to the array a, starting from index l, any element of the subarray is larger than or equal to the corresponding element of array a. An example of attaching is given on the figure (top — infinite array a, bottom — its periodic subarray (l, s)):

Find the number of distinct pairs (l, s), corresponding to the superior periodic arrays.

## 输入格式

The first line contains number n (1 ≤ n ≤ 2·105). The second line contains n numbers a0, a1, ..., an - 1 (1 ≤ ai ≤ 106), separated by a space.

## 输出格式

Print a single integer — the sought number of pairs.

## 样例

### 样例 1

**输入**:
```
4
7 1 2 3
```

**输出**:
```
2
```

### 样例 2

**输入**:
```
2
2 1
```

**输出**:
```
1
```

### 样例 3

**输入**:
```
3
1 1 1
```

**输出**:
```
6
```

## 备注

In the first sample the superior subarrays are (0, 1) and (3, 2).

Subarray (0, 1) is superior, as a0 ≥ a0, a0 ≥ a1, a0 ≥ a2, a0 ≥ a3, a0 ≥ a0, ...

Subarray (3, 2) is superior a3 ≥ a3, a0 ≥ a0, a3 ≥ a1, a0 ≥ a2, a3 ≥ a3, ...

In the third sample any pair of (l, s) corresponds to a superior subarray as all the elements of an array are distinct.

