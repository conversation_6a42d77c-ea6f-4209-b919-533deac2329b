# BerSU Ball

**题目ID**: 489/B  
**比赛**: Codeforces Round 277.5 (Div. 2)  
**年份**: 2014  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

The Berland State University is hosting a ballroom dance in celebration of its 100500-th anniversary! n boys and m girls are already busy rehearsing waltz, minuet, polonaise and quadrille moves.

We know that several boy&girl pairs are going to be invited to the ball. However, the partners' dancing skill in each pair must differ by at most one.

For each boy, we know his dancing skills. Similarly, for each girl we know her dancing skills. Write a code that can determine the largest possible number of pairs that can be formed from n boys and m girls.

## 输入格式

The first line contains an integer n (1 ≤ n ≤ 100) — the number of boys. The second line contains sequence a1, a2, ..., an (1 ≤ ai ≤ 100), where ai is the i-th boy's dancing skill.

Similarly, the third line contains an integer m (1 ≤ m ≤ 100) — the number of girls. The fourth line contains sequence b1, b2, ..., bm (1 ≤ bj ≤ 100), where bj is the j-th girl's dancing skill.

## 输出格式

Print a single number — the required maximum possible number of pairs.

## 样例

### 样例 1

**输入**:
```
4
1 4 6 2
5
5 1 5 7 9
```

**输出**:
```
3
```

### 样例 2

**输入**:
```
4
1 2 3 4
4
10 11 12 13
```

**输出**:
```
0
```

### 样例 3

**输入**:
```
5
1 1 1 1 1
3
1 2 3
```

**输出**:
```
2
```

