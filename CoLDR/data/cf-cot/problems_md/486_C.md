# Palindrome Transformation

**题目ID**: 486/C  
**比赛**: Codeforces Round 277 (Div. 2)  
**年份**: 2014  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON> is playing with a string on his computer. The string consists of n lowercase English letters. It is meaningless, so <PERSON> decided to make the string more beautiful, that is to make it be a palindrome by using 4 arrow keys: left, right, up, down.

There is a cursor pointing at some symbol of the string. Suppose that cursor is at position i (1 ≤ i ≤ n, the string uses 1-based indexing) now. Left and right arrow keys are used to move cursor around the string. The string is cyclic, that means that when <PERSON> presses left arrow key, the cursor will move to position i - 1 if i > 1 or to the end of the string (i. e. position n) otherwise. The same holds when he presses the right arrow key (if i = n, the cursor appears at the beginning of the string).

When <PERSON> presses up arrow key, the letter which the text cursor is pointing to will change to the next letter in English alphabet (assuming that alphabet is also cyclic, i. e. after 'z' follows 'a'). The same holds when he presses the down arrow key.

Initially, the text cursor is at position p.

Because <PERSON> has a lot homework to do, he wants to complete this as fast as possible. Can you help him by calculating the minimum number of arrow keys presses to make the string to be a palindrome?

## 输入格式

The first line contains two space-separated integers n (1 ≤ n ≤ 105) and p (1 ≤ p ≤ n), the length of Nam's string and the initial position of the text cursor.

The next line contains n lowercase characters of Nam's string.

## 输出格式

Print the minimum number of presses needed to change string into a palindrome.

## 样例

### 样例 1

**输入**:
```
8 3
aeabcaez
```

**输出**:
```
6
```

## 备注

A string is a palindrome if it reads the same forward or reversed.

In the sample test, initial Nam's string is: $$aeabcaez$$ (cursor position is shown bold).

In optimal solution, Nam may do 6 following steps:

$$\begin{array}{cccc}
\text{aeabcaez} & \xrightarrow{\text{right}} & \text{aeabcaez} & \xrightarrow{\text{up}} \\
& & & \text{aeaccaez} & \xrightarrow{\text{left}} \\
\text{aeaccaez} & \xrightarrow{\text{left}} & \text{aeaccaez} & \xrightarrow{\text{left}} \\
& & & \text{aeaccaez} & \xrightarrow{\text{down}} \\
& & & & \text{zeaccaez}
\end{array}$$

The result, $$\int e^{a} c \cdot c a e^{v} d v$$, is now a palindrome.

