# Combination Lock

**题目ID**: 540/A  
**比赛**: Codeforces Round 301 (Div. 2)  
**年份**: 2015  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON>roo<PERSON> <PERSON><PERSON><PERSON> keeps his most treasured savings in a home safe with a combination lock. Each time he wants to put there the treasures that he's earned fair and square, he has to open the lock.

The combination lock is represented by n rotating disks with digits from 0 to 9 written on them. <PERSON>roo<PERSON> M<PERSON><PERSON> has to turn some disks so that the combination of digits on the disks forms a secret combination. In one move, he can rotate one disk one digit forwards or backwards. In particular, in one move he can go from digit 0 to digit 9 and vice versa. What minimum number of actions does he need for that?

## 输入格式

The first line contains a single integer n (1 ≤ n ≤ 1000) — the number of disks on the combination lock.

The second line contains a string of n digits — the original state of the disks.

The third line contains a string of n digits — <PERSON><PERSON><PERSON>'s combination that opens the lock.

## 输出格式

Print a single integer — the minimum number of moves <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> needs to open the lock.

## 样例

### 样例 1

**输入**:
```
5
82195
64723
```

**输出**:
```
13
```

## 备注

In the sample he needs 13 moves:

- 1 disk: $$8 \rightarrow 7 \rightarrow 6$$
- 2 disk: $$2 \rightarrow 3 \rightarrow 4$$
- 3 disk: $$1 \rightarrow 0 \rightarrow 9 \rightarrow 8 \rightarrow 7$$
- 4 disk: $$9 \rightarrow 0 \rightarrow 1 \rightarrow 2$$
- 5 disk: $$5 \rightarrow 4 \rightarrow 3$$

