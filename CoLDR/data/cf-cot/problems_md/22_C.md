# System Administrator

**题目ID**: 22/C  
**比赛**: Codeforces Beta Round 22 (Div. 2 Only)  
**年份**: 2010  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON> got a job as a system administrator in X corporation. His first task was to connect n servers with the help of m two-way direct connection so that it becomes possible to transmit data from one server to any other server via these connections. Each direct connection has to link two different servers, each pair of servers should have at most one direct connection. Y corporation, a business rival of X corporation, made <PERSON> an offer that he couldn't refuse: <PERSON> was asked to connect the servers in such a way, that when server with index v fails, the transmission of data between some other two servers becomes impossible, i.e. the system stops being connected. Help Bob connect the servers.

## 输入格式

The first input line contains 3 space-separated integer numbers n, m, v (3 ≤ n ≤ 105, 0 ≤ m ≤ 105, 1 ≤ v ≤ n), n — amount of servers, m — amount of direct connections, v — index of the server that fails and leads to the failure of the whole system.

## 输出格式

If it is impossible to connect the servers in the required way, output -1. Otherwise output m lines with 2 numbers each — description of all the direct connections in the system. Each direct connection is described by two numbers — indexes of two servers, linked by this direct connection. The servers are numbered from 1. If the answer is not unique, output any.

## 样例

### 样例 1

**输入**:
```
5 6 3
```

**输出**:
```
1 2
2 3
3 4
4 5
1 3
3 5
```

### 样例 2

**输入**:
```
6 100 1
```

**输出**:
```
-1
```

