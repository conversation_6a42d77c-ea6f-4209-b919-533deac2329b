# Boxes And Balls

**题目ID**: 884/D  
**比赛**: Educational Codeforces Round 31  
**年份**: 2017  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON> has n different boxes. The first of them contains some balls of n different colors.

<PERSON> wants to play a strange game. He wants to distribute the balls into boxes in such a way that for every i (1 ≤ i ≤ n) i-th box will contain all balls with color i.

In order to do this, <PERSON> will make some turns. Each turn he does the following:

1. <PERSON> chooses any non-empty box and takes all balls from this box;
2. Then <PERSON> chooses any k empty boxes (the box from the first step becomes empty, and <PERSON> is allowed to choose it), separates the balls he took on the previous step into k non-empty groups and puts each group into one of the boxes. He should put each group into a separate box. He can choose either k = 2 or k = 3.

The penalty of the turn is the number of balls <PERSON> takes from the box during the first step of the turn. And penalty of the game is the total penalty of turns made by <PERSON> until he distributes all balls to corresponding boxes.

Help <PERSON> to determine the minimum possible penalty of the game!

## 输入格式

The first line contains one integer number n (1 ≤ n ≤ 200000) — the number of boxes and colors.

The second line contains n integer numbers a1, a2, ..., an (1 ≤ ai ≤ 109), where ai is the number of balls with color i.

## 输出格式

Print one number — the minimum possible penalty of the game.

## 样例

### 样例 1

**输入**:
```
3
1 2 3
```

**输出**:
```
6
```

### 样例 2

**输入**:
```
4
2 3 4 5
```

**输出**:
```
19
```

## 备注

In the first example you take all the balls from the first box, choose k = 3 and sort all colors to corresponding boxes. Penalty is 6.

In the second example you make two turns:

1. Take all the balls from the first box, choose k = 3, put balls of color 3 to the third box, of color 4 — to the fourth box and the rest put back into the first box. Penalty is 14;
2. Take all the balls from the first box, choose k = 2, put balls of color 1 to the first box, of color 2 — to the second box. Penalty is 5.

Total penalty is 19.

## 题解

Let's consider the process backwards: we will store the number of balls of each color in a multiset and then "merge" some of them.
If n is odd, then we can always pick three groups of balls with minimal sizes and replace them by one group (adding the size of this group to the penalty). Repeat until you have only one group.
If n is even, then we need to add an auxiliary group of size 0. Then n becomes odd, so we can use the above algorithm to solve this case.
Why does it work? This algorithm is exactly the same as the algorithm of building a Huffman code with the alphabet of size 3. And it can easily be seen that these problems are similar: by separating a group of balls into three groups, we add a new character to the codes of the colours present in that group, and our goal is to obtain a prefix code.

