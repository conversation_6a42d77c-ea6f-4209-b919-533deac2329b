# Vanya and Scales

**题目ID**: 552/C  
**比赛**: Codeforces Round 308 (Div. 2)  
**年份**: 2015  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON><PERSON> has a scales for weighing loads and weights of masses w0, w1, w2, ..., w100 grams where w is some integer not less than 2 (exactly one weight of each nominal value). <PERSON><PERSON> wonders whether he can weight an item with mass m using the given weights, if the weights can be put on both pans of the scales. Formally speaking, your task is to determine whether it is possible to place an item of mass m and some weights on the left pan of the scales, and some weights on the right pan of the scales so that the pans of the scales were in balance.

## 输入格式

The first line contains two integers w, m (2 ≤ w ≤ 109, 1 ≤ m ≤ 109) — the number defining the masses of the weights and the mass of the item.

## 输出格式

Print word 'YES' if the item can be weighted and 'NO' if it cannot.

## 样例

### 样例 1

**输入**:
```
3 7
```

**输出**:
```
YES
```

### 样例 2

**输入**:
```
100 99
```

**输出**:
```
YES
```

### 样例 3

**输入**:
```
100 50
```

**输出**:
```
NO
```

## 备注

Note to the first sample test. One pan can have an item of mass 7 and a weight of mass 3, and the second pan can have two weights of masses 9 and 1, correspondingly. Then 7 + 3 = 9 + 1.

Note to the second sample test. One pan of the scales can have an item of mass 99 and the weight of mass 1, and the second pan can have the weight of mass 100.

Note to the third sample test. It is impossible to measure the weight of the item in the manner described in the input.

