# Perfect Permutation

**题目ID**: 233/A  
**比赛**: Codeforces Round 144 (Div. 2)  
**年份**: 2012  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

A permutation is a sequence of integers p1, p2, ..., pn, consisting of n distinct positive integers, each of them doesn't exceed n. Let's denote the i-th element of permutation p as pi. We'll call number n the size of permutation p1, p2, ..., pn.

<PERSON><PERSON><PERSON> adores permutations. He likes some permutations more than the others. He calls such permutations perfect. A perfect permutation is such permutation p that for any i (1 ≤ i ≤ n) (n is the permutation size) the following equations hold ppi = i and pi ≠ i. <PERSON><PERSON><PERSON> asks you to print any perfect permutation of size n for the given n.

## 输入格式

A single line contains a single integer n (1 ≤ n ≤ 100) — the permutation size.

## 输出格式

If a perfect permutation of size n doesn't exist, print a single integer -1. Otherwise print n distinct integers from 1 to n, p1, p2, ..., pn — permutation p, that is perfect. Separate printed numbers by whitespaces.

## 样例

### 样例 1

**输入**:
```
1
```

**输出**:
```
-1
```

### 样例 2

**输入**:
```
2
```

**输出**:
```
2 1
```

### 样例 3

**输入**:
```
4
```

**输出**:
```
2 1 4 3
```

