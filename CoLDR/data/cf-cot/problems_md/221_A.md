# Little Elephant and Function

**题目ID**: 221/A  
**比赛**: Codeforces Round 136 (Div. 2)  
**年份**: 2012  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

The <PERSON> Elephant enjoys recursive functions.

This time he enjoys the sorting function. Let a is a permutation of an integers from 1 to n, inclusive, and ai denotes the i-th element of the permutation. The Little Elephant's recursive function f(x), that sorts the first x permutation's elements, works as follows:

- If x = 1, exit the function.
- Otherwise, call f(x - 1), and then make swap(ax - 1, ax) (swap the x-th and (x - 1)-th elements of a).

The <PERSON> Elephant's teacher believes that this function does not work correctly. But that-be do not get an F, the Little Elephant wants to show the performance of its function. Help him, find a permutation of numbers from 1 to n, such that after performing the Little Elephant's function (that is call f(n)), the permutation will be sorted in ascending order.

## 输入格式

A single line contains integer n (1 ≤ n ≤ 1000) — the size of permutation.

## 输出格式

In a single line print n distinct integers from 1 to n — the required permutation. Numbers in a line should be separated by spaces.

It is guaranteed that the answer exists.

## 样例

### 样例 1

**输入**:
```
1
```

**输出**:
```
1
```

### 样例 2

**输入**:
```
2
```

**输出**:
```
2 1
```

