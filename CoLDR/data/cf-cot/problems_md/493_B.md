# Vasya and Wrestling

**题目ID**: 493/B  
**比赛**: Codeforces Round 281 (Div. 2)  
**年份**: 2014  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON><PERSON><PERSON> has become interested in wrestling. In wrestling wrestlers use techniques for which they are awarded points by judges. The wrestler who gets the most points wins.

When the numbers of points of both wrestlers are equal, the wrestler whose sequence of points is lexicographically greater, wins.

If the sequences of the awarded points coincide, the wrestler who performed the last technique wins. Your task is to determine which wrestler won.

## 输入格式

The first line contains number n — the number of techniques that the wrestlers have used (1 ≤ n ≤ 2·105).

The following n lines contain integer numbers ai (|ai| ≤ 109, ai ≠ 0). If ai is positive, that means that the first wrestler performed the technique that was awarded with ai points. And if ai is negative, that means that the second wrestler performed the technique that was awarded with ( - ai) points.

The techniques are given in chronological order.

## 输出格式

If the first wrestler wins, print string "first", otherwise print "second"

## 样例

### 样例 1

**输入**:
```
5
1
2
-3
-4
3
```

**输出**:
```
second
```

### 样例 2

**输入**:
```
3
-1
-2
3
```

**输出**:
```
first
```

### 样例 3

**输入**:
```
2
4
-4
```

**输出**:
```
second
```

## 备注

Sequence x  =  x1x2... x|x| is lexicographically larger than sequence y  =  y1y2... y|y|, if either |x|  >  |y| and x1  =  y1,  x2  =  y2, ... ,  x|y|  =  y|y|, or there is such number r (r  <  |x|, r  <  |y|), that x1  =  y1,  x2  =  y2,  ... ,  xr  =  yr and xr  +  1  >  yr  +  1.

We use notation |a| to denote length of sequence a.

