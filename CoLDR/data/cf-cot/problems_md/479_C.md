# Exams

**题目ID**: 479/C  
**比赛**: Codeforces Round 274 (Div. 2)  
**年份**: 2014  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

Student <PERSON><PERSON> is an undergraduate student at the University. His end of term exams are approaching and he is to pass exactly n exams. <PERSON><PERSON> is a smart guy, so he will be able to pass any exam he takes on his first try. Besides, he can take several exams on one day, and in any order.

According to the schedule, a student can take the exam for the i-th subject on the day number ai. However, <PERSON><PERSON> has made an arrangement with each teacher and the teacher of the i-th subject allowed him to take an exam before the schedule time on day bi (bi < ai). Thus, <PERSON><PERSON> can take an exam for the i-th subject either on day ai, or on day bi. All the teachers put the record of the exam in the student's record book on the day of the actual exam and write down the date of the mark as number ai.

<PERSON><PERSON> believes that it would be rather strange if the entries in the record book did not go in the order of non-decreasing date. Therefore <PERSON><PERSON> asks you to help him. Find the minimum possible value of the day when <PERSON><PERSON> can take the final exam if he takes exams so that all the records in his record book go in the order of non-decreasing date.

## 输入格式

The first line contains a single positive integer n (1 ≤ n ≤ 5000) — the number of exams <PERSON><PERSON> will take.

Each of the next n lines contains two positive space-separated integers ai and bi (1 ≤ bi < ai ≤ 109) — the date of the exam in the schedule and the early date of passing the i-th exam, correspondingly.

## 输出格式

Print a single integer — the minimum possible number of the day when Valera can take the last exam if he takes all the exams so that all the records in his record book go in the order of non-decreasing date.

## 样例

### 样例 1

**输入**:
```
3
5 2
3 1
4 2
```

**输出**:
```
2
```

### 样例 2

**输入**:
```
3
6 1
5 2
4 3
```

**输出**:
```
6
```

## 备注

In the first sample Valera first takes an exam in the second subject on the first day (the teacher writes down the schedule date that is 3). On the next day he takes an exam in the third subject (the teacher writes down the schedule date, 4), then he takes an exam in the first subject (the teacher writes down the mark with date 5). Thus, Valera takes the last exam on the second day and the dates will go in the non-decreasing order: 3, 4, 5.

In the second sample Valera first takes an exam in the third subject on the fourth day. Then he takes an exam in the second subject on the fifth day. After that on the sixth day Valera takes an exam in the first subject.

