# Case of Fake Numbers

**题目ID**: 556/B  
**比赛**: Codeforces Round 310 (Div. 2)  
**年份**: 2015  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON><PERSON> the Android is a galaxy-famous detective. He is now investigating a case of frauds who make fake copies of the famous <PERSON><PERSON><PERSON>'s gears, puzzles that are as famous as the <PERSON><PERSON><PERSON>'s cube once was.

Its most important components are a button and a line of n similar gears. Each gear has n teeth containing all numbers from 0 to n - 1 in the counter-clockwise order. When you push a button, the first gear rotates clockwise, then the second gear rotates counter-clockwise, the the third gear rotates clockwise an so on.

Besides, each gear has exactly one active tooth. When a gear turns, a new active tooth is the one following after the current active tooth according to the direction of the rotation. For example, if n = 5, and the active tooth is the one containing number 0, then clockwise rotation makes the tooth with number 1 active, or the counter-clockwise rotating makes the tooth number 4 active.

<PERSON><PERSON> remembers that the real puzzle has the following property: you can push the button multiple times in such a way that in the end the numbers on the active teeth of the gears from first to last form sequence 0, 1, 2, ..., n - 1. Write a program that determines whether the given puzzle is real or fake.

## 输入格式

The first line contains integer n (1 ≤ n ≤ 1000) — the number of gears.

The second line contains n digits a1, a2, ..., an (0 ≤ ai ≤ n - 1) — the sequence of active teeth: the active tooth of the i-th gear contains number ai.

## 输出格式

In a single line print "Yes" (without the quotes), if the given Stolp's gears puzzle is real, and "No" (without the quotes) otherwise.

## 样例

### 样例 1

**输入**:
```
3
1 0 0
```

**输出**:
```
Yes
```

### 样例 2

**输入**:
```
5
4 2 1 4 3
```

**输出**:
```
Yes
```

### 样例 3

**输入**:
```
4
0 2 3 1
```

**输出**:
```
No
```

## 备注

In the first sample test when you push the button for the first time, the sequence of active teeth will be 2 2 1, when you push it for the second time, you get 0 1 2.

## 题解

If you press the button n times the gears will return to the initial state. So the simplest solution is just to simulate pressing the button for n times: if the gears are real then after several steps the active teeth sequence will be 0, 1, 2, ..., n - 1.

