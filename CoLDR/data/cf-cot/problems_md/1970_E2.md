# Trails (Medium)

**题目ID**: 1970/E2  
**比赛**: Helvetic Coding Contest 2024 online mirror (teams allowed, unrated)  
**年份**: 2024  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON> is hiking in the Alps surrounding Lake Geneva. In this area there are $$$m$$$ cabins, numbered 1 to $$$m$$$. Each cabin is connected, with one or more trails, to a central meeting point next to the lake. Each trail is either short or long. Cabin $$$i$$$ is connected with $$$s_i$$$ short trails and $$$l_i$$$ long trails to the lake.

Each day, <PERSON> walks a trail from the cabin where he currently is to Lake Geneva, and then from there he walks a trail to any of the $$$m$$$ cabins (including the one he started in). However, as he has to finish the hike in a day, at least one of the two trails has to be short.

How many possible combinations of trails can <PERSON> take if he starts in cabin 1 and walks for $$$n$$$ days?

Give the answer modulo $$$10^9 + 7$$$.

## 输入格式

The first line contains the integers $$$m$$$ and $$$n$$$.

The second line contains $$$m$$$ integers, $$$s_1, \dots, s_m$$$, where $$$s_i$$$ is the number of short trails between cabin $$$i$$$ and Lake Geneva.

The third and last line contains $$$m$$$ integers, $$$l_1, \dots, l_m$$$, where $$$l_i$$$ is the number of long trails between cabin $$$i$$$ and Lake Geneva.

We have the following constraints:

$$$0 \le s_i, l_i \le 10^3$$$.

$$$1 \le m \le 10^2$$$.

$$$1 \le n \le 10^9$$$.

## 输出格式

The number of possible combinations of trails, modulo $$$10^9 + 7$$$.

## 样例

### 样例 1

**输入**:
```
3 2
1 0 1
0 1 1
```

**输出**:
```
18
```

## 题解

Let $$$t_i := s_i + l_i$$$. The number of possible paths between cabin $$$i$$$ and cabin $$$j$$$ is $$$t_i t_j - l_i l_j$$$.
Let $$$\mathbf{v}_{k}$$$ be the vector whose $$$i$$$th entry is the number of paths that ends in cabin $$$i$$$ after walking for $$$k$$$ days. We then have $$$$$$ \mathbf{v}_0 = (1, 0, \dots, 0), $$$$$$ and $$$$$$ (\mathbf{v}_{k+1})_i = \sum_{j = 1}^{m} (t_i t_j - l_i l_j) (\mathbf{v}_{k})_j. $$$$$$ Let $$$A$$$ be the $$$(m \times m)$$$–matrix given by $$$$$$ A_{i,j} = t_i t_j - l_i l_j. $$$$$$ Then the formula for $$$\mathbf{v}_{k+1}$$$ can be rewritten as $$$$$$ \mathbf{v}_{k+1} = A \mathbf{v}_k. $$$$$$ Hence, $$$$$$ \mathbf{v}_n = A^n \mathbf{v}_0. $$$$$$ Now, we can compute $$$A^n$$$ in $$$\mathcal{O}(\log(n) m^3)$$$ time using the following recursive formula.
1. Compute $$$B := A^{\lfloor n/2 \rfloor}$$$
2. If $$$n$$$ is even: Return $$$B^2$$$
3. Else: Return $$$B^2 A$$$
The solution to the problem is now $$$$$$ (\mathbf{v}_n)_1 + \dots + (\mathbf{v}_n)_m. $$$$$$ The total running complexity is $$$\mathcal{O}(\log(n) m^3)$$$.

