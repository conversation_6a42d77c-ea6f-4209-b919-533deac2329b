# Mischievous Shooter

**题目ID**: 1921/G  
**比赛**: Codeforces Round 920 (Div. 3)  
**年份**: 2024  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

Once the mischievous and wayward shooter named <PERSON><PERSON> found himself on a rectangular field of size $$$n \times m$$$, divided into unit squares. Each cell either contains a target or not.

<PERSON><PERSON> only had a lucky shotgun with him, with which he can shoot in one of the four directions: right-down, left-down, left-up, or right-up. When fired, the shotgun hits all targets in the chosen direction, the Manhattan distance to which does not exceed a fixed constant $$$k$$$. The Manhattan distance between two points $$$(x_1, y_1)$$$ and $$$(x_2, y_2)$$$ is equal to $$$|x_1 - x_2| + |y_1 - y_2|$$$.

Possible hit areas for $$$k = 3$$$.

She<PERSON>'s goal is to hit as many targets as possible. Please help him find this value.

## 输入格式

Each test consists of several test cases. The first line contains a single integer $$$t$$$ ($$$1 \le t \le 1000$$$) — the number of test cases. Then follows the description of the test cases.

The first line of each test case contains field dimensions $$$n$$$, $$$m$$$, and the constant for the shotgun's power $$$k$$$ ($$$1 \le n, m, k \le 10^5, 1 \le n \cdot m \le 10^5$$$).

Each of the next $$$n$$$ lines contains $$$m$$$ characters — the description of the next field row, where the character '.' means the cell is empty, and the character '#' indicates the presence of a target.

It is guaranteed that the sum of $$$n \cdot m$$$ over all test cases does not exceed $$$10^5$$$.

## 输出格式

For each test case, output a single integer on a separate line, which is equal to the maximum possible number of hit targets with one shot.

## 样例

### 样例 1

**输入**:
```
4
3 3 1
.#.
###
.#.
2 5 3
###..
...##
4 4 2
..##
###.
#..#
####
2 1 3
#
#
```

**输出**:
```
3
4
5
2
```

## 备注

Possible optimal shots for the examples in the statement:

## 题解

First of all, notice that we can consider only the case where the triangle of affected cells is oriented left-up. To solve the remaining cases, we can solve the problem for four different board orientations and choose the maximum result from the obtained results.
We will store several arrays with prefix sums: for the sum of all numbers in the cells of the current column above the given one, as well as for the sum of all numbers in the cells up and to the right of the given one. Using such prefix sums, we can easily recalculate the answer in cell $$$(i, j)$$$ through the answer in cell $$$(i, j-1)$$$ in $$$O(1)$$$. To do this, we need to add the sum of the cells marked in green and subtract the sum of the cells marked in red.
The total time complexity of this solution is $$$O(nm)$$$.
Note that the problem could also be solved in time $$$O(nm\min(n, m))$$$ by computing prefix sums in the minimum of the directions and calculating the sums in the triangle in $$$O(\min(n, m))$$$. This solution also fits within the time limit.

