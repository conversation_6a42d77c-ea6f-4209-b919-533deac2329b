# Face Detection

**题目ID**: 549/A  
**比赛**: Looksery Cup 2015  
**年份**: 2015  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

The developers of Looksery have to write an efficient algorithm that detects faces on a picture. Unfortunately, they are currently busy preparing a contest for you, so you will have to do it for them.

In this problem an image is a rectangular table that consists of lowercase Latin letters. A face on the image is a 2 × 2 square, such that from the four letters of this square you can make word "face".

You need to write a program that determines the number of faces on the image. The squares that correspond to the faces can overlap.

## 输入格式

The first line contains two space-separated integers, n and m (1 ≤ n, m ≤ 50) — the height and the width of the image, respectively.

Next n lines define the image. Each line contains m lowercase Latin letters.

## 输出格式

In the single line print the number of faces on the image.

## 样例

### 样例 1

**输入**:
```
4 4
xxxx
xfax
xcex
xxxx
```

**输出**:
```
1
```

### 样例 2

**输入**:
```
4 2
xx
cf
ae
xx
```

**输出**:
```
1
```

### 样例 3

**输入**:
```
2 3
fac
cef
```

**输出**:
```
2
```

### 样例 4

**输入**:
```
1 4
face
```

**输出**:
```
0
```

## 备注

In the first sample the image contains a single face, located in a square with the upper left corner at the second line and the second column:

In the second sample the image also contains exactly one face, its upper left corner is at the second row and the first column.

In the third sample two faces are shown:

In the fourth sample the image has no faces on it.

## 题解

One should iterate through each 2x2 square and check if it is possible to rearrange letters in such way they they form the word "face". It could be done i.e. by sorting all letters from the square in alphabetic order and comparing the result with the word "acef"(sorted letters of word "face").

