# Ice Skating

**题目ID**: 217/A  
**比赛**: Codeforces Round 134 (Div. 1)  
**年份**: 2012  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON><PERSON><PERSON><PERSON> is learning to skate on ice. He's a beginner, so his only mode of transportation is pushing off from a snow drift to the north, east, south or west and sliding until he lands in another snow drift. He has noticed that in this way it's impossible to get from some snow drifts to some other by any sequence of moves. He now wants to heap up some additional snow drifts, so that he can get from any snow drift to any other one. He asked you to find the minimal number of snow drifts that need to be created.

We assume that <PERSON><PERSON><PERSON><PERSON> can only heap up snow drifts at integer coordinates.

## 输入格式

The first line of input contains a single integer n (1 ≤ n ≤ 100) — the number of snow drifts. Each of the following n lines contains two integers xi and yi (1 ≤ xi, yi ≤ 1000) — the coordinates of the i-th snow drift.

Note that the north direction coinсides with the direction of Oy axis, so the east direction coinсides with the direction of the Ox axis. All snow drift's locations are distinct.

## 输出格式

Output the minimal number of snow drifts that need to be created in order for <PERSON><PERSON><PERSON><PERSON> to be able to reach any snow drift from any other one.

## 样例

### 样例 1

**输入**:
```
2
2 1
1 2
```

**输出**:
```
1
```

### 样例 2

**输入**:
```
2
2 1
4 1
```

**输出**:
```
0
```

