# Flea travel

**题目ID**: 55/A  
**比赛**: Codeforces Beta Round 51  
**年份**: 2011  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

A flea is sitting at one of the n hassocks, arranged in a circle, at the moment. After minute number k the flea jumps through k - 1 hassoсks (clockwise). For example, after the first minute the flea jumps to the neighboring hassock. You should answer: will the flea visit all the hassocks or not. We assume that flea has infinitely much time for this jumping.

## 输入格式

The only line contains single integer: 1 ≤ n ≤ 1000 — number of hassocks.

## 输出格式

Output "YES" if all the hassocks will be visited and "NO" otherwise.

## 样例

### 样例 1

**输入**:
```
1
```

**输出**:
```
YES
```

### 样例 2

**输入**:
```
3
```

**输出**:
```
NO
```

