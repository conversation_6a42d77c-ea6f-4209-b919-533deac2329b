# Up the hill

**题目ID**: 491/A  
**比赛**: Testing Round 11  
**年份**: 2014  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

Hiking club "Up the hill" just returned from a walk. Now they are trying to remember which hills they've just walked through.

It is known that there were N stops, all on different integer heights between 1 and N kilometers (inclusive) above the sea level. On the first day they've traveled from the first stop to the second stop, on the second day they've traveled from the second to the third and so on, and on the last day they've traveled from the stop N - 1 to the stop N and successfully finished their expedition.

They are trying to find out which heights were their stops located at. They have an entry in a travel journal specifying how many days did they travel up the hill, and how many days did they walk down the hill.

Help them by suggesting some possible stop heights satisfying numbers from the travel journal.

## 输入格式

In the first line there is an integer non-negative number A denoting the number of days of climbing up the hill. Second line contains an integer non-negative number B — the number of days of walking down the hill (A + B + 1 = N, 1 ≤ N ≤ 100 000).

## 输出格式

Output N space-separated distinct integers from 1 to N inclusive, denoting possible heights of the stops in order of visiting.

## 样例

### 样例 1

**输入**:
```
0
1
```

**输出**:
```
2 1
```

### 样例 2

**输入**:
```
2
1
```

**输出**:
```
1 3 4 2
```

