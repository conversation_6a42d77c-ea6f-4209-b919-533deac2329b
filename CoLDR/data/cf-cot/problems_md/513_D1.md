# Constrained Tree

**题目ID**: 513/D1  
**比赛**: Rockethon 2015  
**年份**: 2015  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

You need to find a binary tree of size n that satisfies a given set of c constraints. Suppose that the nodes of the unknown binary tree are labeled using a pre-order traversal starting with 1. For the i-th constraint you are given two labels, ai and bi and a direction, left or right. In case of left direction, bi is an element of the subtree rooted at ai's left child. Similarly in the case of right direction bi is an element of the subtree rooted at ai's right child.

## 输入格式

The first line of input contains two integers n and c. The next c lines contain 2 integers ai, bi (1 ≤ ai, bi ≤ n) and either "LEFT" or "RIGHT" denoting whether b is in the subtree rooted at ai's left child or in the subtree rooted at ai's right child.

The problem consists of multiple subproblems. The subproblems have different constraints on the input. You will get some score for the correct submission of the subproblem. The description of the subproblems follows.

- In subproblem D1 (9 points), the constraints 1 ≤ n ≤ 100, 1 ≤ c ≤ 50 will hold.
- In subproblem D2 (8 points), the constraints 1 ≤ n ≤ 1000000, 1 ≤ c ≤ 100000 will hold.

## 输出格式

Output will be on a single line.

Any binary tree that satisfies the constraints will be accepted. The tree's nodes should be printed out as n space separated labels representing an in-order traversal, using the pre-order numbers as labels of vertices.

If there are no trees that satisfy the constraints, print "IMPOSSIBLE" (without quotes).

## 样例

### 样例 1

**输入**:
```
3 2
1 2 LEFT
1 3 RIGHT
```

**输出**:
```
2 1 3
```

### 样例 2

**输入**:
```
3 2
1 2 RIGHT
1 3 LEFT
```

**输出**:
```
IMPOSSIBLE
```

## 备注

Consider the first sample test. We need to find a tree with 3 nodes that satisfies the following two constraints. The node labeled 2 with pre-order traversal should be in the left subtree of the node labeled 1 with pre-order traversal; the node labeled 3 with pre-order traversal should be in the right subtree of the node labeled 1. There is only one tree with three nodes that satisfies these constraints and its in-order traversal is (2, 1, 3).

Pre-order is the "root – left subtree – right subtree" order. In-order is the "left subtree – root – right subtree" order.

For other information regarding in-order and pre-order, see http://en.wikipedia.org/wiki/Tree_traversal.

