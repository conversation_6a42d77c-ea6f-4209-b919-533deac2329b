# Team

**题目ID**: 231/A  
**比赛**: Codeforces Round 143 (Div. 2)  
**年份**: 2012  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

One day three best friends <PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON> decided to form a team and take part in programming contests. Participants are usually offered several problems during programming contests. Long before the start the friends decided that they will implement a problem if at least two of them are sure about the solution. Otherwise, the friends won't write the problem's solution.

This contest offers n problems to the participants. For each problem we know, which friend is sure about the solution. Help the friends find the number of problems for which they will write a solution.

## 输入格式

The first input line contains a single integer n (1 ≤ n ≤ 1000) — the number of problems in the contest. Then n lines contain three integers each, each integer is either 0 or 1. If the first number in the line equals 1, then <PERSON><PERSON> is sure about the problem's solution, otherwise he isn't sure. The second number shows <PERSON><PERSON><PERSON>'s view on the solution, the third number shows <PERSON><PERSON>'s view. The numbers on the lines are separated by spaces.

## 输出格式

Print a single integer — the number of problems the friends will implement on the contest.

## 样例

### 样例 1

**输入**:
```
3
1 1 0
1 1 1
1 0 0
```

**输出**:
```
2
```

### 样例 2

**输入**:
```
2
1 0 0
0 1 1
```

**输出**:
```
1
```

## 备注

In the first sample <PERSON>ya and Vasya are sure that they know how to solve the first problem and all three of them know how to solve the second problem. That means that they will write solutions for these problems. Only <PERSON>ya is sure about the solution for the third problem, but that isn't enough, so the friends won't take it.

In the second sample the friends will only implement the second problem, as Vasya and <PERSON>a are sure about the solution.

