# Campus

**题目ID**: 571/D  
**比赛**: Codeforces Round 317 [AimFund Thanks-Round] (Div. 1)  
**年份**: 2015  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

Oscolcovo city has a campus consisting of n student dormitories, n universities and n military offices. Initially, the i-th dormitory belongs to the i-th university and is assigned to the i-th military office.

Life goes on and the campus is continuously going through some changes. The changes can be of four types:

1. University aj merges with university bj. After that all the dormitories that belonged to university bj are assigned to to university aj, and university bj disappears.
2. Military office cj merges with military office dj. After that all the dormitories that were assigned to military office dj, are assigned to military office cj, and military office dj disappears.
3. Students of university xj move in dormitories. Lets kxj is the number of dormitories that belong to this university at the time when the students move in. Then the number of students in each dormitory of university xj increases by kxj (note that the more dormitories belong to the university, the more students move in each dormitory of the university).
4. Military office number yj conducts raids on all the dormitories assigned to it and takes all students from there.

Thus, at each moment of time each dormitory is assigned to exactly one university and one military office. Initially, all the dormitory are empty.

Your task is to process the changes that take place in the campus and answer the queries, how many people currently live in dormitory qj.

## 输入格式

The first line contains two integers, n and m (1 ≤ n, m ≤ 5·105) — the number of dormitories and the number of queries, respectively.

Next m lines contain the queries, each of them is given in one of the following formats:

- «U aj bj» — merging universities;
- «M cj dj» — merging military offices;
- «A xj» — students of university xj moving in the dormitories;
- «Z yj» — a raid in military office yj;
- «Q qj» — a query asking the number of people in dormitory qj.

## 输出格式

In the i-th line print the answer to the i-th query asking the number of people in the dormitory.

## 样例

### 样例 1

**输入**:
```
2 7
A 1
Q 1
U 1 2
A 1
Z 1
Q 1
Q 2
```

**输出**:
```
1
0
2
```

### 样例 2

**输入**:
```
5 12
U 1 2
M 4 5
A 1
Q 1
A 3
A 4
Q 3
Q 4
Z 4
Q 4
A 5
Q 5
```

**输出**:
```
2
1
1
0
1
```

## 备注

Consider the first sample test:

- In the first query university 1 owns only dormitory 1, so after the query dormitory 1 will have 1 student.
- After the third query university 1 owns dormitories 1 and 2.
- The fourth query increases by 2 the number of students living in dormitories 1 and 2 that belong to university number 1. After that 3 students live in the first dormitory and 2 students live in the second dormitory.
- At the fifth query the number of students living in dormitory 1, assigned to the military office 1, becomes zero.

## 题解

Let's suppose for each dormitory from Q query we already know the last raid moment.
When task will be much easier: we can throw away M and Z queries and to get right answer we should subtract two values: people count in dormitory right now and same count in a last raid moment.
On this basis, we have such plan:
1. For each Q query let's find the last raid moment using M and Z queries.
2. Find people count in two interesting moments using U and A queries.
3. Calculcates the final answer.
Let's try to solve the first part.
We want to make such queries on disjoint sets:
1. Merge two sets (M query).
2. Assign value time for all elements in particular set (Z query).
3. Get value for a particular element (Q query).
To solve this task we'll use a well-known technique: "merge smaller set to bigger".
We'll maintain such values:
• elements — set elements.
• set_id — for each element their set id.
• last_set_update_time — last moment when assign operation has occurred for each set.
• last_update_time — last moment when assign operation has occurred for each element.
• actual_time — moment of time when last_update_time was actually for the element.
Let's focus on actual_time value.
It's obvious that when we merge two sets each element can have a different last assign moment. But after first assignment all elements from any set will have the same value. So the answer for "Q query for element i from set s:
For each Z query you should just update last_set_update_time array.
It's easy to maintain this values when you merge two sets:
Let's suppose we want to merge set from to set to. For each element from set from we already know last assign time. So just update last_update_time with this value and actual_time is equal of last assign operation for set to.
The second part of the solution is the same as first one.
O(n * lg(n) + m) time and O(n + m) memory.

