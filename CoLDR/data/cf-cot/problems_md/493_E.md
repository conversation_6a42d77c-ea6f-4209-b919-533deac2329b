# V<PERSON><PERSON> and Polynomial

**题目ID**: 493/E  
**比赛**: Codeforces Round 281 (Div. 2)  
**年份**: 2014  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON><PERSON><PERSON> is studying in the last class of school and soon he will take exams. He decided to study polynomials. Polynomial is a function P(x) = a0 + a1x1 + ... + anxn. Numbers ai are called coefficients of a polynomial, non-negative integer n is called a degree of a polynomial.

<PERSON><PERSON><PERSON> has made a bet with his friends that he can solve any problem with polynomials. They suggested him the problem: "Determine how many polynomials P(x) exist with integer non-negative coefficients so that $$P(\mathbf{t}) = a$$, and $$P(P(t)) = b$$, where $$t,a$$ and b are given positive integers"?

<PERSON><PERSON><PERSON> does not like losing bets, but he has no idea how to solve this task, so please help him to solve the problem.

## 输入格式

The input contains three integer positive numbers $$t,a,b$$ no greater than 1018.

## 输出格式

If there is an infinite number of such polynomials, then print "inf" without quotes, otherwise print the reminder of an answer modulo 109 + 7.

## 样例

### 样例 1

**输入**:
```
2 2 2
```

**输出**:
```
2
```

### 样例 2

**输入**:
```
2 3 3
```

**输出**:
```
1
```

