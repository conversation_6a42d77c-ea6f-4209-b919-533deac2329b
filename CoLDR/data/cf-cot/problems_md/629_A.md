# Far Relative’s Birthday Cake

**题目ID**: 629/A  
**比赛**: Codeforces Round 343 (Div. 2)  
**年份**: 2016  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON>'s family is going celebrate <PERSON><PERSON><PERSON>'s birthday party. They love <PERSON><PERSON><PERSON> so they are planning to make his birthday cake weird!

The cake is a n × n square consisting of equal squares with side length 1. Each square is either empty or consists of a single chocolate. They bought the cake and randomly started to put the chocolates on the cake. The value of Famil Door's happiness will be equal to the number of pairs of cells with chocolates that are in the same row or in the same column of the cake. <PERSON><PERSON><PERSON><PERSON>'s family is wondering what is the amount of happiness of <PERSON><PERSON><PERSON> going to be?

Please, note that any pair can be counted no more than once, as two different cells can't share both the same row and the same column.

## 输入格式

In the first line of the input, you are given a single integer n (1 ≤ n ≤ 100) — the length of the side of the cake.

Then follow n lines, each containing n characters. Empty cells are denoted with '.', while cells that contain chocolates are denoted by 'C'.

## 输出格式

Print the value of <PERSON><PERSON>l Door's happiness, i.e. the number of pairs of chocolate pieces that share the same row or the same column.

## 样例

### 样例 1

**输入**:
```
3
.CC
C..
C.C
```

**输出**:
```
4
```

### 样例 2

**输入**:
```
4
CC..
C..C
.CC.
.CC.
```

**输出**:
```
9
```

## 备注

If we number rows from top to bottom and columns from left to right, then, pieces that share the same row in the first sample are:

1. (1, 2) and (1, 3)
2. (3, 1) and (3, 3)

1. (2, 1) and (3, 1)
2. (1, 3) and (3, 3)

