# Hamming Distance

**题目ID**: 470/G  
**比赛**: Surprise Language Round 7  
**年份**: 2014  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

Hamming distance between two strings of equal length is the number of positions at which the corresponding symbols are different. You are given two strings; calculate the distance between them.

## 输入格式

The input consists of two lines. Each line contains a string of characters 'A'-'Z' between 1 and 100 characters, inclusive. The strings have equal length.

## 输出格式

Output Hamming distance between the strings.

## 样例

### 样例 1

**输入**:
```
CODECHEF
TOPCODER
```

**输出**:
```
6
```

### 样例 2

**输入**:
```
HAMMING
DISTANC
```

**输出**:
```
6
```

