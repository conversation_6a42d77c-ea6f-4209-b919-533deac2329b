# Quasi Binary

**题目ID**: 537/B  
**比赛**: VK Cup 2015 - Wild Card Round 2  
**年份**: 2015  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

A number is called quasibinary if its decimal representation contains only digits 0 or 1. For example, numbers 0, 1, 101, 110011 — are quasibinary and numbers 2, 12, 900 are not.

You are given a positive integer n. Represent it as a sum of minimum number of quasibinary numbers.

## 输入格式

The first line contains a single integer n (1 ≤ n ≤ 106).

## 输出格式

In the first line print a single integer k — the minimum number of numbers in the representation of number n as a sum of quasibinary numbers.

In the second line print k numbers — the elements of the sum. All these numbers should be quasibinary according to the definition above, their sum should equal n. Do not have to print the leading zeroes in the numbers. The order of numbers doesn't matter. If there are multiple possible representations, you are allowed to print any of them.

## 样例

### 样例 1

**输入**:
```
9
```

**输出**:
```
9
1 1 1 1 1 1 1 1 1
```

### 样例 2

**输入**:
```
32
```

**输出**:
```
3
10 11 11
```

