# Kefa and Watch

**题目ID**: 580/E  
**比赛**: Codeforces Round 321 (Div. 2)  
**年份**: 2015  
**时间限制**: 1.5秒  
**内存限制**: 256MB  

## 题目描述

One day <PERSON><PERSON> the parrot was walking down the street as he was on the way home from the restaurant when he saw something glittering by the road. As he came nearer he understood that it was a watch. He decided to take it to the pawnbroker to earn some money.

The pawnbroker said that each watch contains a serial number represented by a string of digits from 0 to 9, and the more quality checks this number passes, the higher is the value of the watch. The check is defined by three positive integers l, r and d. The watches pass a check if a substring of the serial number from l to r has period d. Sometimes the pawnbroker gets distracted and <PERSON><PERSON> changes in some substring of the serial number all digits to c in order to increase profit from the watch.

The seller has a lot of things to do to begin with and with <PERSON><PERSON> messing about, he gave you a task: to write a program that determines the value of the watch.

Let us remind you that number x is called a period of string s (1 ≤ x ≤ |s|), if si  =  si + x for all i from 1 to |s|  -  x.

## 输入格式

The first line of the input contains three positive integers n, m and k (1 ≤ n ≤ 105, 1 ≤ m + k ≤ 105) — the length of the serial number, the number of change made by <PERSON><PERSON> and the number of quality checks.

The second line contains a serial number consisting of n digits.

Then m + k lines follow, containing either checks or changes.

The changes are given as 1 l r c (1 ≤ l ≤ r ≤ n, 0 ≤ c ≤ 9). That means that Kefa changed all the digits from the l-th to the r-th to be c.

The checks are given as 2 l r d (1 ≤ l ≤ r ≤ n, 1 ≤ d ≤ r - l + 1).

## 输出格式

For each check on a single line print "YES" if the watch passed it, otherwise print "NO".

## 样例

### 样例 1

**输入**:
```
3 1 2
112
2 2 3 1
1 1 3 8
2 1 2 1
```

**输出**:
```
NO
YES
```

### 样例 2

**输入**:
```
6 2 3
334934
2 2 5 2
1 4 4 3
2 1 6 3
1 2 3 8
2 3 6 1
```

**输出**:
```
NO
YES
NO
```

## 备注

In the first sample test two checks will be made. In the first one substring "12" is checked on whether or not it has period 1, so the answer is "NO". In the second one substring "88", is checked on whether or not it has period 1, and it has this period, so the answer is "YES".

In the second statement test three checks will be made. The first check processes substring "3493", which doesn't have period 2. Before the second check the string looks as "334334", so the answer to it is "YES". And finally, the third check processes substring "8334", which does not have period 1.

## 题解

Для начала посчитаем хэш для всех элементов строки в зависимости от их позиций. То есть хэш цифры k, стоящей на i-й позиции будет равен gi * k, где g — основание хэша. Для всех хэшей построим дерево отрезков сумм, поддерживающее групповую модификацию. Таким образом, запросы на модификацию мы можем выполнять за O(log n). Осталось разобраться с запросами второго типа. Допустим, нужно обработать запрос 2 l r d. Очевидно, что подстрока с l по r будет иметь период d, если подстрока с l + d по r равна подстроке с l по r - d (при условии, что r - l + 1 кратно d). С помощью дерева сумм мы можем узнать сумму хэшей на подотрезке, а значит можем сравнить две строки за O(log n).
Асимптотика — O(m log n).

