# Initial Bet

**题目ID**: 478/A  
**比赛**: Codeforces Round 273 (Div. 2)  
**年份**: 2014  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

There are five people playing a game called "Generosity". Each person gives some non-zero number of coins b as an initial bet. After all players make their bets of b coins, the following operation is repeated for several times: a coin is passed from one player to some other player.

Your task is to write a program that can, given the number of coins each player has at the end of the game, determine the size b of the initial bet or find out that such outcome of the game cannot be obtained for any positive number of coins b in the initial bet.

## 输入格式

The input consists of a single line containing five integers c1, c2, c3, c4 and c5 — the number of coins that the first, second, third, fourth and fifth players respectively have at the end of the game (0 ≤ c1, c2, c3, c4, c5 ≤ 100).

## 输出格式

Print the only line containing a single positive integer b — the number of coins in the initial bet of each player. If there is no such value of b, then print the only value "-1" (quotes for clarity).

## 样例

### 样例 1

**输入**:
```
2 5 4 0 4
```

**输出**:
```
3
```

### 样例 2

**输入**:
```
4 5 9 2 1
```

**输出**:
```
-1
```

## 备注

In the first sample the following sequence of operations is possible:

1. One coin is passed from the fourth player to the second player;
2. One coin is passed from the fourth player to the fifth player;
3. One coin is passed from the first player to the third player;
4. One coin is passed from the fourth player to the second player.

