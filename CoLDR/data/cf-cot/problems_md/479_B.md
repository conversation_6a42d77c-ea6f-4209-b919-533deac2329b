# Towers

**题目ID**: 479/B  
**比赛**: Codeforces Round 274 (Div. 2)  
**年份**: 2014  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

As you know, all the kids in Berland love playing with cubes. Little <PERSON><PERSON> has n towers consisting of cubes of the same size. Tower with number i consists of ai cubes stacked one on top of the other. <PERSON><PERSON> defines the instability of a set of towers as a value equal to the difference between the heights of the highest and the lowest of the towers. For example, if <PERSON><PERSON> built five cube towers with heights (8, 3, 2, 6, 3), the instability of this set is equal to 6 (the highest tower has height 8, the lowest one has height 2).

The boy wants the instability of his set of towers to be as low as possible. All he can do is to perform the following operation several times: take the top cube from some tower and put it on top of some other tower of his set. Please note that <PERSON><PERSON> would never put the cube on the same tower from which it was removed because he thinks it's a waste of time.

Before going to school, the boy will have time to perform no more than k such operations. <PERSON><PERSON> does not want to be late for class, so you have to help him accomplish this task.

## 输入格式

The first line contains two space-separated positive integers n and k (1 ≤ n ≤ 100, 1 ≤ k ≤ 1000) — the number of towers in the given set and the maximum number of operations <PERSON><PERSON> can perform. The second line contains n space-separated positive integers ai (1 ≤ ai ≤ 104) — the towers' initial heights.

## 输出格式

In the first line print two space-separated non-negative integers s and m (m ≤ k). The first number is the value of the minimum possible instability that can be obtained after performing at most k operations, the second number is the number of operations needed for that.

In the next m lines print the description of each operation as two positive integers i and j, each of them lies within limits from 1 to n. They represent that Petya took the top cube from the i-th tower and put in on the j-th one (i ≠ j). Note that in the process of performing operations the heights of some towers can become equal to zero.

If there are multiple correct sequences at which the minimum possible instability is achieved, you are allowed to print any of them.

## 样例

### 样例 1

**输入**:
```
3 2
5 8 5
```

**输出**:
```
0 2
2 1
2 3
```

### 样例 2

**输入**:
```
3 4
2 2 4
```

**输出**:
```
1 1
3 2
```

### 样例 3

**输入**:
```
5 3
8 3 2 6 3
```

**输出**:
```
3 3
1 3
1 2
1 3
```

## 备注

In the first sample you need to move the cubes two times, from the second tower to the third one and from the second one to the first one. Then the heights of the towers are all the same and equal to 6.

