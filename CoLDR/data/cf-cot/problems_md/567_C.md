# Geometric Progression

**题目ID**: 567/C  
**比赛**: Codeforces Round #Pi (Div. 2)  
**年份**: 2015  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON><PERSON><PERSON><PERSON> loves geometric progressions very much. Since he was only three years old, he loves only the progressions of length three. He also has a favorite integer k and a sequence a, consisting of n integers.

He wants to know how many subsequences of length three can be selected from a, so that they form a geometric progression with common ratio k.

A subsequence of length three is a combination of three such indexes i1, i2, i3, that 1 ≤ i1 < i2 < i3 ≤ n. That is, a subsequence of length three are such groups of three elements that are not necessarily consecutive in the sequence, but their indexes are strictly increasing.

A geometric progression with common ratio k is a sequence of numbers of the form b·k0, b·k1, ..., b·kr - 1.

<PERSON><PERSON><PERSON><PERSON> is only three years old, so he can not calculate this number himself. Help him to do it.

## 输入格式

The first line of the input contains two integers, n and k (1 ≤ n, k ≤ 2·105), showing how many numbers <PERSON><PERSON><PERSON><PERSON>'s sequence has and his favorite number.

The second line contains n integers a1, a2, ..., an ( - 109 ≤ ai ≤ 109) — elements of the sequence.

## 输出格式

Output a single number — the number of ways to choose a subsequence of length three, such that it forms a geometric progression with a common ratio k.

## 样例

### 样例 1

**输入**:
```
5 2
1 1 2 2 4
```

**输出**:
```
4
```

### 样例 2

**输入**:
```
3 1
1 1 1
```

**输出**:
```
1
```

### 样例 3

**输入**:
```
10 3
1 2 6 2 3 6 9 18 3 9
```

**输出**:
```
6
```

## 备注

In the first sample test the answer is four, as any of the two 1s can be chosen as the first element, the second element can be any of the 2s, and the third element of the subsequence must be equal to 4.

## 题解

Let's solve this problem for fixed middle element of progression. This means that if we fix element ai then the progression must consist of ai / k and ai·k elements. It could not be possible, for example, if ai is not divisible by k ($$a_{i} \mod k \neq 0$$).
For fixed middle element one could find the number of sequences by counting how many ai / k elements are placed left from fixed element and how many ai·k are placed right from it, and then multiplying this numbers. To do this, one could use two associative arrays Al and Ar, where for each key x will be stored count of occurences of x placed left (or right respectively) from current element. This could be done with map structure.
Sum of values calculated as described above will give the answer to the problem.

