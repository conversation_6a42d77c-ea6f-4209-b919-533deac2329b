# V<PERSON>ya the Hipster

**题目ID**: 581/A  
**比赛**: Codeforces Round 322 (Div. 2)  
**年份**: 2015  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

One day V<PERSON><PERSON> the Hipster decided to count how many socks he had. It turned out that he had a red socks and b blue socks.

According to the latest fashion, hipsters should wear the socks of different colors: a red one on the left foot, a blue one on the right foot.

Every day <PERSON><PERSON><PERSON> puts on new socks in the morning and throws them away before going to bed as he doesn't want to wash them.

<PERSON><PERSON><PERSON> wonders, what is the maximum number of days when he can dress fashionable and wear different socks, and after that, for how many days he can then wear the same socks until he either runs out of socks or cannot make a single pair from the socks he's got.

Can you help him?

## 输入格式

The single line of the input contains two positive integers a and b (1 ≤ a, b ≤ 100) — the number of red and blue socks that <PERSON><PERSON><PERSON>'s got.

## 输出格式

Print two space-separated integers — the maximum number of days when <PERSON><PERSON><PERSON> can wear different socks and the number of days when he can wear the same socks until he either runs out of socks or cannot make a single pair from the socks he's got.

Keep in mind that at the end of the day <PERSON><PERSON><PERSON> throws away the socks that he's been wearing on that day.

## 样例

### 样例 1

**输入**:
```
3 1
```

**输出**:
```
1 1
```

### 样例 2

**输入**:
```
2 3
```

**输出**:
```
2 0
```

### 样例 3

**输入**:
```
7 3
```

**输出**:
```
3 2
```

## 备注

In the first sample Vasya can first put on one pair of different socks, after that he has two red socks left to wear on the second day.

