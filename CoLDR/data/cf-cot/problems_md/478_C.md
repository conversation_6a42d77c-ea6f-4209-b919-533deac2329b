# Table Decorations

**题目ID**: 478/C  
**比赛**: Codeforces Round 273 (Div. 2)  
**年份**: 2014  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

You have r red, g green and b blue balloons. To decorate a single table for the banquet you need exactly three balloons. Three balloons attached to some table shouldn't have the same color. What maximum number t of tables can be decorated if we know number of balloons of each color?

Your task is to write a program that for given values r, g and b will find the maximum number t of tables, that can be decorated in the required manner.

## 输入格式

The single line contains three integers r, g and b (0 ≤ r, g, b ≤ 2·109) — the number of red, green and blue baloons respectively. The numbers are separated by exactly one space.

## 输出格式

Print a single integer t — the maximum number of tables that can be decorated in the required manner.

## 样例

### 样例 1

**输入**:
```
5 4 3
```

**输出**:
```
4
```

### 样例 2

**输入**:
```
1 1 1
```

**输出**:
```
1
```

### 样例 3

**输入**:
```
2 3 3
```

**输出**:
```
2
```

## 备注

In the first sample you can decorate the tables with the following balloon sets: "rgg", "gbb", "brr", "rrg", where "r", "g" and "b" represent the red, green and blue balls, respectively.

