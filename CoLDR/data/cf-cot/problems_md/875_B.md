# Sorting the Coins

**题目ID**: 875/B  
**比赛**: Codeforces Round 441 (Div. 1, by Moscow Team Olympiad)  
**年份**: 2017  
**时间限制**: 1.0秒  
**内存限制**: 512MB  

## 题目描述

Recently, <PERSON><PERSON> met with <PERSON> in a philatelic store, and since then they are collecting coins together. Their favorite occupation is to sort collections of coins. <PERSON> likes having things in order, that is why he wants his coins to be arranged in a row in such a way that firstly come coins out of circulation, and then come coins still in circulation.

For arranging coins <PERSON><PERSON> uses the following algorithm. One step of his algorithm looks like the following:

1. He looks through all the coins from left to right;
2. If he sees that the i-th coin is still in circulation, and (i + 1)-th coin is already out of circulation, he exchanges these two coins and continues watching coins from (i + 1)-th.

<PERSON><PERSON> repeats the procedure above until it happens that no two coins were exchanged during this procedure. <PERSON><PERSON> calls hardness of ordering the number of steps required for him according to the algorithm above to sort the sequence, e.g. the number of times he looks through the coins from the very beginning. For example, for the ordered sequence hardness of ordering equals one.

Today <PERSON> invited <PERSON><PERSON> and proposed him a game. First he puts n coins in a row, all of them are out of circulation. Then <PERSON> chooses one of the coins out of circulation and replaces it with a coin in circulation for n times. During this process <PERSON> constantly asks <PERSON><PERSON> what is the hardness of ordering of the sequence.

The task is more complicated because <PERSON>ma should not touch the coins and he should determine hardness of ordering in his mind. Help <PERSON>ma with this task.

## 输入格式

The first line contains single integer n (1 ≤ n ≤ 300 000) — number of coins that Sasha puts behind Dima.

Second line contains n distinct integers p1, p2, ..., pn (1 ≤ pi ≤ n) — positions that <PERSON> puts coins in circulation to. At first <PERSON> replaces coin located at position p1, then coin located at position p2 and so on. Coins are numbered from left to right.

## 输出格式

Print n + 1 numbers a0, a1, ..., an, where a0 is a hardness of ordering at the beginning, a1 is a hardness of ordering after the first replacement and so on.

## 样例

### 样例 1

**输入**:
```
4
1 3 4 2
```

**输出**:
```
1 2 3 2 1
```

### 样例 2

**输入**:
```
8
6 8 3 4 7 2 1 5
```

**输出**:
```
1 2 2 3 4 3 4 5 1
```

## 备注

Let's denote as O coin out of circulation, and as X — coin is circulation.

At the first sample, initially in row there are coins that are not in circulation, so Dima will look through them from left to right and won't make any exchanges.

After replacement of the first coin with a coin in circulation, Dima will exchange this coin with next three times and after that he will finally look through the coins and finish the process.

XOOO  →  OOOX

After replacement of the third coin, Dima's actions look this way:

XOXO  →  OXOX  →  OOXX

After replacement of the fourth coin, Dima's actions look this way:

XOXX  →  OXXX

Finally, after replacement of the second coin, row becomes consisting of coins that are in circulation and Dima will look through coins from left to right without any exchanges.

## 题解

We denote, for 0, a coin that has left circulation and for one coin in circulation.
We solve the problem for a fixed array. If it consists of only 1, then the answer is 0, since the array is already sorted. Otherwise, consider the most right zero. If there is not a single 1 to the left of this zero, then the array is already sorted and the answer is 1.
Let 1 appears k times to the left of the rightmost zero. For one iteration the nearest 1 on the left will move to the position of this zero, and zero will move one position to the left. After this iteration, k - 1 ones will remain to the left of the rightmost zero. Hence the answer is k + 1.
Let us return to the original problem. We will keep the pointer to the rightmost zero. Since as a result of queries the zeros only disappear, the pointer moves only to the left. If the rightmost zero has disappeared, move the pointer to the left by a cycle, until we find the next zero.
Consider pointer is at the position x (numeration from zero), and there are only p ones in the array. On the right of x all the symbols are ones so on the right there are only n–x–1 ones. So on the left are p–(n–x–1) ones.
This solution works in за O(n + q).


