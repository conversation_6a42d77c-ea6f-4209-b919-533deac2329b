# Vanya and Books

**题目ID**: 552/B  
**比赛**: Codeforces Round 308 (Div. 2)  
**年份**: 2015  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON><PERSON> got an important task — he should enumerate books in the library and label each book with its number. Each of the n books should be assigned with a number from 1 to n. Naturally, distinct books should be assigned distinct numbers.

<PERSON><PERSON> wants to know how many digits he will have to write down as he labels the books.

## 输入格式

The first line contains integer n (1 ≤ n ≤ 109) — the number of books in the library.

## 输出格式

Print the number of digits needed to number all the books.

## 样例

### 样例 1

**输入**:
```
13
```

**输出**:
```
17
```

### 样例 2

**输入**:
```
4
```

**输出**:
```
4
```

## 备注

Note to the first test. The books get numbers 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, which totals to 17 digits.

Note to the second sample. The books get numbers 1, 2, 3, 4, which totals to 4 digits.

