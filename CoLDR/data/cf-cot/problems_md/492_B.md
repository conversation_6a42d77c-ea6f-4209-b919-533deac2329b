# Vanya and Lanterns

**题目ID**: 492/B  
**比赛**: Codeforces Round 280 (Div. 2)  
**年份**: 2014  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON><PERSON> walks late at night along a straight street of length l, lit by n lanterns. Consider the coordinate system with the beginning of the street corresponding to the point 0, and its end corresponding to the point l. Then the i-th lantern is at the point ai. The lantern lights all points of the street that are at the distance of at most d from it, where d is some positive number, common for all lanterns.

<PERSON><PERSON> wonders: what is the minimum light radius d should the lanterns have to light the whole street?

## 输入格式

The first line contains two integers n, l (1 ≤ n ≤ 1000, 1 ≤ l ≤ 109) — the number of lanterns and the length of the street respectively.

The next line contains n integers ai (0 ≤ ai ≤ l). Multiple lanterns can be located at the same point. The lanterns may be located at the ends of the street.

## 输出格式

Print the minimum light radius d, needed to light the whole street. The answer will be considered correct if its absolute or relative error doesn't exceed 10 - 9.

## 样例

### 样例 1

**输入**:
```
7 15
15 5 3 7 9 14 0
```

**输出**:
```
2.5000000000
```

### 样例 2

**输入**:
```
2 5
2 5
```

**输出**:
```
2.0000000000
```

## 备注

Consider the second sample. At d = 2 the first lantern will light the segment [0, 4] of the street, and the second lantern will light segment [3, 5]. Thus, the whole street will be lit.

