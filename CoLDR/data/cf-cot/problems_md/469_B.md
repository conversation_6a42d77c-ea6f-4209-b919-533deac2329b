# Chat Online

**题目ID**: 469/B  
**比赛**: Codeforces Round 268 (Div. 2)  
**年份**: 2014  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON> and <PERSON> are good friends. They always chat online. But both of them have schedules.

<PERSON> Z has fixed schedule. He always online at any moment of time between a1 and b1, between a2 and b2, ..., between ap and bp (all borders inclusive). But the schedule of Little X is quite strange, it depends on the time when he gets up. If he gets up at time 0, he will be online at any moment of time between c1 and d1, between c2 and d2, ..., between cq and dq (all borders inclusive). But if he gets up at time t, these segments will be shifted by t. They become [ci + t, di + t] (for all i).

If at a moment of time, both Little X and Little Z are online simultaneosly, they can chat online happily. You know that Little X can get up at an integer moment of time between l and r (both borders inclusive). Also you know that Little X wants to get up at the moment of time, that is suitable for chatting with <PERSON> (they must have at least one common moment of time in schedules). How many integer moments of time from the segment [l, r] suit for that?

## 输入格式

The first line contains four space-separated integers p, q, l, r (1 ≤  p, q ≤ 50; 0 ≤ l ≤ r ≤ 1000).

Each of the next p lines contains two space-separated integers ai, bi (0 ≤ ai < bi ≤ 1000). Each of the next q lines contains two space-separated integers cj, dj (0 ≤ cj < dj ≤ 1000).

It's guaranteed that bi < ai + 1 and dj < cj + 1 for all valid i and j.

## 输出格式

Output a single integer — the number of moments of time from the segment [l, r] which suit for online conversation.

## 样例

### 样例 1

**输入**:
```
1 1 0 4
2 3
0 1
```

**输出**:
```
3
```

### 样例 2

**输入**:
```
2 3 0 20
15 17
23 26
1 4
7 11
15 17
```

**输出**:
```
20
```

