# <PERSON><PERSON> and WiFi

**题目ID**: 476/B  
**比赛**: Codeforces Round 272 (Div. 2)  
**年份**: 2014  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON><PERSON> is standing at the position 0 on a number line. <PERSON><PERSON><PERSON> is sending a list of commands through Wi-Fi to <PERSON><PERSON>'s smartphone and <PERSON><PERSON> follows them.

Each command is one of the following two types:

1. Go 1 unit towards the positive direction, denoted as '+'
2. Go 1 unit towards the negative direction, denoted as '-'

But the Wi-Fi condition is so poor that <PERSON><PERSON>'s smartphone reports some of the commands can't be recognized and <PERSON><PERSON> knows that some of them might even be wrong though successfully recognized. <PERSON><PERSON> decides to follow every recognized command and toss a fair coin to decide those unrecognized ones (that means, he moves to the 1 unit to the negative or positive direction with the same probability 0.5).

You are given an original list of commands sent by <PERSON><PERSON><PERSON> and list received by <PERSON><PERSON>. What is the probability that <PERSON><PERSON> ends in the position originally supposed to be final by <PERSON><PERSON><PERSON>'s commands?

## 输入格式

The first line contains a string s1 — the commands <PERSON><PERSON><PERSON> sends to <PERSON><PERSON>, this string consists of only the characters in the set {'+', '-'}.

The second line contains a string s2 — the commands <PERSON><PERSON>'s smartphone recognizes, this string consists of only the characters in the set {'+', '-', '?'}. '?' denotes an unrecognized command.

Lengths of two strings are equal and do not exceed 10.

## 输出格式

Output a single real number corresponding to the probability. The answer will be considered correct if its relative or absolute error doesn't exceed 10 - 9.

## 样例

### 样例 1

**输入**:
```
++-+-
+-+-+
```

**输出**:
```
1.000000000000
```

### 样例 2

**输入**:
```
+-+-
+-??
```

**输出**:
```
0.500000000000
```

### 样例 3

**输入**:
```
+++
??-
```

**输出**:
```
0.000000000000
```

## 备注

For the first sample, both s1 and s2 will lead Dreamoon to finish at the same position  + 1.

For the second sample, s1 will lead Dreamoon to finish at position 0, while there are four possibilites for s2: {"+-++", "+-+-", "+--+", "+---"} with ending position {+2, 0, 0, -2} respectively. So there are 2 correct cases out of 4, so the probability of finishing at the correct position is 0.5.

For the third sample, s2 could only lead us to finish at positions {+1, -1, -3}, so the probability to finish at the correct position  + 3 is 0.

