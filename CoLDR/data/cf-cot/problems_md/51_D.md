# Geometrical problem

**题目ID**: 51/D  
**比赛**: Codeforces Beta Round 48  
**年份**: 2010  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON><PERSON><PERSON><PERSON> loves geometric progressions — he collects them. However, as such progressions occur very rarely, he also loves the sequences of numbers where it is enough to delete a single element to get a geometric progression.

In this task we shall define geometric progressions as finite sequences of numbers a1, a2, ..., ak, where ai = c·bi - 1 for some real numbers c and b. For example, the sequences [2, -4, 8], [0, 0, 0, 0], [199] are geometric progressions and [0, 1, 2, 3] is not.

Recently <PERSON><PERSON><PERSON><PERSON> has found a sequence and he can't classify it. Help him to do it. Determine whether it is a geometric progression. If it is not, check if it can become a geometric progression if an element is deleted from it.

## 输入格式

The first line contains an integer n (1 ≤ n ≤ 105) — the number of elements in the given sequence. The second line contains the given sequence. The numbers are space-separated. All the elements of the given sequence are integers and their absolute value does not exceed 104.

## 输出格式

Print 0, if the given sequence is a geometric progression. Otherwise, check if it is possible to make the sequence a geometric progression by deleting a single element. If it is possible, print 1. If it is impossible, print 2.

## 样例

### 样例 1

**输入**:
```
4
3 6 12 24
```

**输出**:
```
0
```

### 样例 2

**输入**:
```
4
-8 -16 24 -32
```

**输出**:
```
1
```

### 样例 3

**输入**:
```
4
0 1 2 3
```

**输出**:
```
2
```

