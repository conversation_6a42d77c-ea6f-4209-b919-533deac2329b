# Bits

**题目ID**: 484/A  
**比赛**: Codeforces Round 276 (Div. 1)  
**年份**: 2014  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

Let's denote as $$\operatorname{popcount}(x)$$ the number of bits set ('1' bits) in the binary representation of the non-negative integer x.

You are given multiple queries consisting of pairs of integers l and r. For each query, find the x, such that l ≤ x ≤ r, and $$\operatorname{popcount}(x)$$ is maximum possible. If there are multiple such numbers find the smallest of them.

## 输入格式

The first line contains integer n — the number of queries (1 ≤ n ≤ 10000).

Each of the following n lines contain two integers li, ri — the arguments for the corresponding query (0 ≤ li ≤ ri ≤ 1018).

## 输出格式

For each query print the answer in a separate line.

## 样例

### 样例 1

**输入**:
```
3
1 2
2 4
1 10
```

**输出**:
```
1
3
7
```

## 备注

The binary representations of numbers from 1 to 10 are listed below:

110 = 12

210 = 102

310 = 112

410 = 1002

510 = 1012

610 = 1102

710 = 1112

810 = 10002

910 = 10012

1010 = 10102

