# Wet Shark and Odd and Even

**题目ID**: 621/A  
**比赛**: Codeforces Round 341 (Div. 2)  
**年份**: 2016  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

Today, <PERSON> Shark is given n integers. Using any of these integers no more than once, <PERSON> Shark wants to get maximum possible even (divisible by 2) sum. Please, calculate this value for <PERSON> Shark.

Note, that if <PERSON> Shark uses no integers from the n integers, the sum is an even integer 0.

## 输入格式

The first line of the input contains one integer, n (1 ≤ n ≤ 100 000). The next line contains n space separated integers given to <PERSON> Shark. Each of these integers is in range from 1 to 109, inclusive.

## 输出格式

Print the maximum possible even sum that can be obtained if we use some of the given integers.

## 样例

### 样例 1

**输入**:
```
3
1 2 3
```

**输出**:
```
6
```

### 样例 2

**输入**:
```
5
999999999 999999999 999999999 999999999 999999999
```

**输出**:
```
3999999996
```

## 备注

In the first sample, we can simply take all three integers for a total sum of 6.

In the second sample <PERSON> Shark should take any four out of five integers 999 999 999.

