# Polycarpus is Looking for Good Substrings

**题目ID**: 211/B  
**比赛**: VK Cup 2012 Finals  
**年份**: 2012  
**时间限制**: 4.0秒  
**内存限制**: 256MB  

## 题目描述

We'll call string s[a, b] = sasa + 1... sb (1 ≤ a ≤ b ≤ |s|) a substring of string s = s1s2... s|s|, where |s| is the length of string s.

The trace of a non-empty string t is a set of characters that the string consists of. For example, the trace of string "aab" equals {'a', 'b'}.

Let's consider an arbitrary string s and the set of its substrings with trace equal to C. We will denote the number of substrings from this set that are maximal by inclusion by r(C, s). Substring s[a, b] of length n = b - a + 1 belonging to some set is called maximal by inclusion, if there is no substring s[x, y] in this set with length greater than n, such that 1 ≤ x ≤ a ≤ b ≤ y ≤ |s|. Two substrings of string s are considered different even if they are equal but they are located at different positions of s.

<PERSON><PERSON><PERSON><PERSON> got a challenging practical task on a stringology exam. He must do the following: given string s and non-empty sets of characters C1, C2, ..., Cm, find r(Ci, s) for each set Ci. Help <PERSON><PERSON><PERSON><PERSON> to solve the problem as he really doesn't want to be expelled from the university and go to the army!

## 输入格式

The first line contains a non-empty string s (1 ≤ |s| ≤ 106).

The second line contains a single integer m (1 ≤ m ≤ 104). Next m lines contain descriptions of sets Ci. The i-th line contains string ci such that its trace equals Ci. It is guaranteed that all characters of each string ci are different.

Note that Ci are not necessarily different. All given strings consist of lowercase English letters.

## 输出格式

Print m integers — the i-th integer must equal r(Ci, s).

## 样例

### 样例 1

**输入**:
```
aaaaa
2
a
a
```

**输出**:
```
1
1
```

### 样例 2

**输入**:
```
abacaba
3
ac
ba
a
```

**输出**:
```
1
2
4
```

