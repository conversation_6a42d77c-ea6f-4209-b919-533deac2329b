# Inbox (100500)

**题目ID**: 465/B  
**比赛**: Codeforces Round 265 (Div. 2)  
**年份**: 2014  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

Over time, <PERSON><PERSON>'s mail box got littered with too many letters. Some of them are read, while others are unread.

<PERSON><PERSON>'s mail program can either show a list of all letters or show the content of a single letter. As soon as the program shows the content of an unread letter, it becomes read letter (if the program shows the content of a read letter nothing happens). In one click he can do any of the following operations:

- Move from the list of letters to the content of any single letter.
- Return to the list of letters from single letter viewing mode.
- In single letter viewing mode, move to the next or to the previous letter in the list. You cannot move from the first letter to the previous one or from the last letter to the next one.

The program cannot delete the letters from the list or rearrange them.

<PERSON><PERSON> wants to read all the unread letters and go watch football. Now he is viewing the list of all letters and for each letter he can see if it is read or unread. What minimum number of operations does <PERSON><PERSON> need to perform to read all unread letters?

## 输入格式

The first line contains a single integer n (1 ≤ n ≤ 1000) — the number of letters in the mailbox.

The second line contains n space-separated integers (zeros and ones) — the state of the letter list. The i-th number equals either 1, if the i-th number is unread, or 0, if the i-th letter is read.

## 输出格式

Print a single number — the minimum number of operations needed to make all the letters read.

## 样例

### 样例 1

**输入**:
```
5
0 1 0 1 0
```

**输出**:
```
3
```

### 样例 2

**输入**:
```
5
1 1 0 0 1
```

**输出**:
```
4
```

### 样例 3

**输入**:
```
2
0 0
```

**输出**:
```
0
```

## 备注

In the first sample Alexey needs three operations to cope with the task: open the second letter, move to the third one, move to the fourth one.

In the second sample the action plan: open the first letter, move to the second letter, return to the list, open the fifth letter.

In the third sample all letters are already read.

