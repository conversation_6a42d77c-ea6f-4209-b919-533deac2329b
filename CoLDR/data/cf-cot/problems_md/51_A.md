# Ch<PERSON><PERSON><PERSON>'s Problem

**题目ID**: 51/A  
**比赛**: Codeforces Beta Round 48  
**年份**: 2010  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON><PERSON><PERSON><PERSON> is a famous in all the Berland astrologist, magician and wizard, and he also is a liar and a cheater. One of his latest inventions is <PERSON><PERSON><PERSON><PERSON>' amulets! They bring luck and wealth, but are rather expensive. <PERSON><PERSON><PERSON><PERSON> makes them himself. The technology of their making is kept secret. But we know that throughout long nights Ch<PERSON><PERSON><PERSON> glues together domino pairs with super glue to get squares 2 × 2 which are the Ch<PERSON><PERSON>us' magic amulets!

That's what one of <PERSON><PERSON><PERSON><PERSON>'s amulets looks like

After a hard night <PERSON><PERSON><PERSON><PERSON> made n amulets. Everyone of them represents a square 2 × 2, every quarter contains 1 to 6 dots. Now he wants sort them into piles, every pile must contain similar amulets. Two amulets are called similar if they can be rotated by 90, 180 or 270 degrees so that the following condition is met: the numbers of dots in the corresponding quarters should be the same. It is forbidden to turn over the amulets.

Write a program that by the given amulets will find the number of piles on <PERSON><PERSON><PERSON><PERSON>' desk.

## 输入格式

The first line contains an integer n (1 ≤ n ≤ 1000), where n is the number of amulets. Then the amulet's descriptions are contained. Every description occupies two lines and contains two numbers (from 1 to 6) in each line. Between every pair of amulets the line "**" is located.

## 输出格式

Print the required number of piles.

## 样例

### 样例 1

**输入**:
```
4
31
23
**
31
23
**
13
32
**
32
13
```

**输出**:
```
1
```

### 样例 2

**输入**:
```
4
51
26
**
54
35
**
25
61
**
45
53
```

**输出**:
```
2
```

