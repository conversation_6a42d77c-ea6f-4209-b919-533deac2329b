# Frog Fights

**题目ID**: 625/E  
**比赛**: Codeforces Round 342 (Div. 2)  
**年份**: 2016  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON><PERSON><PERSON> recently visited frog farm and was inspired to create his own frog game.

Number of frogs are places on a cyclic gameboard, divided into m cells. Cells are numbered from 1 to m, but the board is cyclic, so cell number 1 goes right after the cell number m in the direction of movement. i-th frog during its turn can jump for ai cells.

Frogs move in turns, game starts with a move by frog 1. On its turn i-th frog moves ai cells forward, knocking out all the frogs on its way. If there is a frog in the last cell of the path of the i-th frog, that frog is also knocked out. After this the value ai is decreased by the number of frogs that were knocked out during this turn. If ai is zero or goes negative, then i-th frog doesn't make moves anymore.

After frog number 1 finishes its turn, frog number 2 starts to move, then frog number 3 and so on. After the frog number n makes its move, frog 1 starts to move again, then frog 2 and so on this process goes forever. If some frog was already knocked out from the board, we consider that it skips all its moves.

Help <PERSON><PERSON><PERSON> to identify, what frogs will stay on the board at the end of a game?

## 输入格式

First line of the input contains two integers n and m (1 ≤ n ≤ 100000, 1 ≤ m ≤ 109, n ≤ m) — number of frogs and gameboard size, respectively.

Following n lines contains frogs descriptions — two integers pi and ai (1 ≤ pi, ai ≤ m) — the number of cell occupied by i-th frog initially and initial jump length. All pi are guaranteed to be distinct.

## 输出格式

In the first line output number of frogs on the final gameboard. In the second line output their numbers in any order.

## 样例

### 样例 1

**输入**:
```
3 5
2 1
5 3
4 3
```

**输出**:
```
1
3
```

### 样例 2

**输入**:
```
5 6
1 2
3 4
2 5
5 1
6 1
```

**输出**:
```
2
1 4
```

## 备注

In the first sample first frog jumps 1 cell and finishes in cell number 3. Second frog jumps for 3 cells and finishes on cell number 3, knocking out frog number 1. Current jump length for frog number 2 is now 2. Third frog jumps to cell 2, then second frog jumps to cell 5. Third frog in turn finishes in cell 5 and removes frog 2 from the gameboard. Now, it's the only remaining frog in the game.

In the second sample first frog jumps 2 cells and knocks out frogs in cells 2 and 3. Its value ai is now 0. Then fourth frog jumps and knocks out fifth frog and its ai is now 0 too. These two frogs will remains on the gameboard forever.

