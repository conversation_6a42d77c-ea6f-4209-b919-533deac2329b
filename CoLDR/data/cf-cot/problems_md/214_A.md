# System of Equations

**题目ID**: 214/A  
**比赛**: Codeforces Round 131 (Div. 2)  
**年份**: 2012  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON><PERSON> loves math lessons very much, so he doesn't attend them, unlike <PERSON><PERSON><PERSON>. But now <PERSON><PERSON> wants to get a good mark for math. For that <PERSON><PERSON>, his math teacher, gave him a new task. <PERSON><PERSON> solved the task immediately. Can you?

You are given a system of equations:

You should count, how many there are pairs of integers (a, b) (0 ≤ a, b) which satisfy the system.

## 输入格式

A single line contains two integers n, m (1 ≤ n, m ≤ 1000) — the parameters of the system. The numbers on the line are separated by a space.

## 输出格式

On a single line print the answer to the problem.

## 样例

### 样例 1

**输入**:
```
9 3
```

**输出**:
```
1
```

### 样例 2

**输入**:
```
14 28
```

**输出**:
```
1
```

### 样例 3

**输入**:
```
4 20
```

**输出**:
```
0
```

## 备注

In the first sample the suitable pair is integers (3, 0). In the second sample the suitable pair is integers (3, 5). In the third sample there is no suitable pair.

