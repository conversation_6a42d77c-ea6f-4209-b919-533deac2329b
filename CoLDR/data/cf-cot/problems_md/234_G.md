# Practice

**题目ID**: 234/G  
**比赛**: Codeforces Round 145 (Div. 2, ACM-ICPC Rules)  
**年份**: 2012  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

Little time is left before Berland annual football championship. Therefore the coach of team "Losewille Rangers" decided to resume the practice, that were indefinitely interrupted for uncertain reasons. Overall there are n players in "Losewille Rangers". Each player on the team has a number — a unique integer from 1 to n. To prepare for the championship, the coach Mr. <PERSON> decided to spend some number of practices.

Mr. <PERSON><PERSON> spent some long nights of his holiday planning how to conduct the practices. He came to a very complex practice system. Each practice consists of one game, all n players of the team take part in the game. The players are sorted into two teams in some way. In this case, the teams may have different numbers of players, but each team must have at least one player.

The coach wants to be sure that after the series of the practice sessions each pair of players had at least one practice, when they played in different teams. As the players' energy is limited, the coach wants to achieve the goal in the least number of practices.

Help him to schedule the practices.

## 输入格式

A single input line contains integer n (2 ≤ n ≤ 1000).

## 输出格式

In the first line print m — the minimum number of practices the coach will have to schedule. Then print the descriptions of the practices in m lines.

In the i-th of those lines print fi — the number of players in the first team during the i-th practice (1 ≤ fi < n), and fi numbers from 1 to n — the numbers of players in the first team. The rest of the players will play in the second team during this practice. Separate numbers on a line with spaces. Print the numbers of the players in any order. If there are multiple optimal solutions, print any of them.

## 样例

### 样例 1

**输入**:
```
2
```

**输出**:
```
1
1 1
```

### 样例 2

**输入**:
```
3
```

**输出**:
```
2
2 1 2
1 1
```

