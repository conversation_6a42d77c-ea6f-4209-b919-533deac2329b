# Field of Wonders

**题目ID**: 883/E  
**比赛**: 2017-2018 ACM-ICPC, NEERC, Southern Subregional Contest (Online Mirror, ACM-ICPC Rules, Teams Preferred)  
**年份**: 2017  
**时间限制**: 3.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON><PERSON><PERSON><PERSON> takes part in the "Field of Wonders" TV show. The participants of the show have to guess a hidden word as fast as possible. Initially all the letters of the word are hidden.

The game consists of several turns. At each turn the participant tells a letter and the TV show host responds if there is such letter in the word or not. If there is such letter then the host reveals all such letters. For example, if the hidden word is "abacaba" and the player tells the letter "a", the host will reveal letters at all positions, occupied by "a": 1, 3, 5 and 7 (positions are numbered from left to right starting from 1).

<PERSON><PERSON><PERSON><PERSON> knows m words of exactly the same length as the hidden word. The hidden word is also known to him and appears as one of these m words.

At current moment a number of turns have already been made and some letters (possibly zero) of the hidden word are already revealed. Previously <PERSON><PERSON><PERSON><PERSON> has told exactly the letters which are currently revealed.

It is <PERSON><PERSON><PERSON><PERSON>' turn. He wants to tell a letter in such a way, that the TV show host will assuredly reveal at least one more letter. <PERSON><PERSON><PERSON><PERSON> cannot tell the letters, which are already revealed.

Your task is to help <PERSON><PERSON><PERSON><PERSON> and find out the number of letters he can tell so that the show host will assuredly reveal at least one of the remaining letters.

## 输入格式

The first line contains one integer n (1 ≤ n ≤ 50) — the length of the hidden word.

The following line describes already revealed letters. It contains the string of length n, which consists of lowercase Latin letters and symbols "*". If there is a letter at some position, then this letter was already revealed. If the position contains symbol "*", then the letter at this position has not been revealed yet. It is guaranteed, that at least one letter is still closed.

The third line contains an integer m (1 ≤ m ≤ 1000) — the number of words of length n, which Polycarpus knows. The following m lines contain the words themselves — n-letter strings of lowercase Latin letters. All words are distinct.

It is guaranteed that the hidden word appears as one of the given m words. Before the current move Polycarp has told exactly the letters which are currently revealed.

## 输出格式

Output the single integer — the number of letters Polycarpus can tell so that the TV show host definitely reveals at least one more letter. It is possible that this number is zero.

## 样例

### 样例 1

**输入**:
```
4
a**d
2
abcd
acbd
```

**输出**:
```
2
```

### 样例 2

**输入**:
```
5
lo*er
2
lover
loser
```

**输出**:
```
0
```

### 样例 3

**输入**:
```
3
a*a
2
aaa
aba
```

**输出**:
```
1
```

## 备注

In the first example Polycarpus can tell letters "b" and "c", which assuredly will be revealed.

The second example contains no letters which can be told as it is not clear, which of the letters "v" or "s" is located at the third position of the hidden word.

In the third example Polycarpus exactly knows that the hidden word is "aba", because in case it was "aaa", then the second letter "a" would have already been revealed in one of previous turns.

