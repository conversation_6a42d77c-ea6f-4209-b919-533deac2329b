# Bribes

**题目ID**: 575/B  
**比赛**: Bubble Cup 8 - Finals [Online Mirror]  
**年份**: 2015  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

Ruritania is a country with a very badly maintained road network, which is not exactly good news for lorry drivers that constantly have to do deliveries. In fact, when roads are maintained, they become one-way. It turns out that it is sometimes impossible to get from one town to another in a legal way – however, we know that all towns are reachable, though illegally!

Fortunately for us, the police tend to be very corrupt and they will allow a lorry driver to break the rules and drive in the wrong direction provided they receive ‘a small gift’. There is one patrol car for every road and they will request 1000 Ruritanian dinars when a driver drives in the wrong direction. However, being greedy, every time a patrol car notices the same driver breaking the rule, they will charge double the amount of money they requested the previous time on that particular road.

<PERSON><PERSON> is a lorry driver that managed to figure out this bribing pattern. As part of his job, he has to make K stops in some towns all over Ruritania and he has to make these stops in a certain order. There are N towns (enumerated from 1 to N) in Ruritania and <PERSON><PERSON>’s initial location is the capital city i.e. town 1. He happens to know which ones out of the N - 1 roads in Ruritania are currently unidirectional, but he is unable to compute the least amount of money he needs to prepare for bribing the police. Help <PERSON>a by providing him with an answer and you will be richly rewarded.

## 输入格式

The first line contains N, the number of towns in Ruritania. The following N - 1 lines contain information regarding individual roads between towns. A road is represented by a tuple of integers (a,b,x), which are separated with a single whitespace character. The numbers a and b represent the cities connected by this particular road, and x is either 0 or 1: 0 means that the road is bidirectional, 1 means that only the a → b direction is legal. The next line contains K, the number of stops Borna has to make. The final line of input contains K positive integers s1, …, sK: the towns Borna has to visit.

- 1 ≤ N ≤ 105
- 1 ≤ K ≤ 106
- 1 ≤ a, b ≤ N for all roads
- $$x \in \{0, 1\}$$ for all roads
- 1 ≤ si ≤ N for all 1 ≤ i ≤ K

## 输出格式

The output should contain a single number: the least amount of thousands of Ruritanian dinars Borna should allocate for bribes, modulo 109 + 7.

## 样例

### 样例 1

**输入**:
```
5
1 2 0
2 3 0
5 1 1
3 4 1
5
5 4 5 2 2
```

**输出**:
```
4
```

## 备注

Borna first takes the route 1 → 5 and has to pay 1000 dinars. After that, he takes the route 5 → 1 → 2 → 3 → 4 and pays nothing this time. However, when he has to return via 4 → 3 → 2 → 1 → 5, he needs to prepare 3000 (1000+2000) dinars. Afterwards, getting to 2 via 5 → 1 → 2 will cost him nothing. Finally, he doesn't even have to leave town 2 to get to 2, so there is no need to prepare any additional bribe money. Hence he has to prepare 4000 dinars in total.

