# Table

**题目ID**: 232/B  
**比赛**: Codeforces Round 144 (Div. 1)  
**年份**: 2012  
**时间限制**: 4.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON> has an n × m table. <PERSON> can paint points in some table cells, not more than one point in one table cell. <PERSON> wants to use such operations to make each square subtable of size n × n have exactly k points.

<PERSON> wondered, how many distinct ways to fill the table with points are there, provided that the condition must hold. As this number can be rather large, <PERSON> asks to find its remainder after dividing by 1000000007 (109 + 7).

You should assume that <PERSON> always paints a point exactly in the center of some cell. Two ways to fill a table are considered distinct, if there exists a table cell, that has a point in one way and doesn't have it in the other.

## 输入格式

A single line contains space-separated integers n, m, k (1 ≤ n ≤ 100; n ≤ m ≤ 1018; 0 ≤ k ≤ n2) — the number of rows of the table, the number of columns of the table and the number of points each square must contain.

Please, do not use the %lld specifier to read or write 64-bit integers in С++. It is preferred to use the cin, cout streams or the %I64d specifier.

## 输出格式

In a single line print a single integer — the remainder from dividing the described number of ways by 1000000007 (109 + 7).

## 样例

### 样例 1

**输入**:
```
5 6 1
```

**输出**:
```
45
```

## 备注

Let's consider the first test case:

