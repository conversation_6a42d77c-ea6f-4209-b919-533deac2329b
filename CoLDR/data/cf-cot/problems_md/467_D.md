# <PERSON><PERSON> and Essay

**题目ID**: 467/D  
**比赛**: Codeforces Round 267 (Div. 2)  
**年份**: 2014  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

After you had helped <PERSON><PERSON> to find friends in the «Call of Soldiers 3» game, he stopped studying completely. Today, the English teacher told him to prepare an essay. <PERSON><PERSON> didn't want to prepare the essay, so he asked <PERSON> for help. <PERSON> came to help and wrote the essay for <PERSON><PERSON>. But <PERSON><PERSON> didn't like the essay at all. Now <PERSON><PERSON> is going to change the essay using the synonym dictionary of the English language.

<PERSON><PERSON> does not want to change the meaning of the essay. So the only change he would do: change a word from essay to one of its synonyms, basing on a replacement rule from the dictionary. <PERSON>or may perform this operation any number of times.

As a result, <PERSON><PERSON> wants to get an essay which contains as little letters «R» (the case doesn't matter) as possible. If there are multiple essays with minimum number of «R»s he wants to get the one with minimum length (length of essay is the sum of the lengths of all the words in it). Help <PERSON><PERSON> get the required essay.

Please note that in this problem the case of letters doesn't matter. For example, if the synonym dictionary says that word cat can be replaced with word DOG, then it is allowed to replace the word Cat with the word doG.

## 输入格式

The first line contains a single integer m (1 ≤ m ≤ 105) — the number of words in the initial essay. The second line contains words of the essay. The words are separated by a single space. It is guaranteed that the total length of the words won't exceed 105 characters.

The next line contains a single integer n (0 ≤ n ≤ 105) — the number of pairs of words in synonym dictionary. The i-th of the next n lines contains two space-separated non-empty words xi and yi. They mean that word xi can be replaced with word yi (but not vise versa). It is guaranteed that the total length of all pairs of synonyms doesn't exceed 5·105 characters.

All the words at input can only consist of uppercase and lowercase letters of the English alphabet.

## 输出格式

Print two integers — the minimum number of letters «R» in an optimal essay and the minimum length of an optimal essay.

## 样例

### 样例 1

**输入**:
```
3
AbRb r Zz
4
xR abRb
aA xr
zz Z
xr y
```

**输出**:
```
2 6
```

### 样例 2

**输入**:
```
2
RuruRu fedya
1
ruruRU fedor
```

**输出**:
```
1 10
```

