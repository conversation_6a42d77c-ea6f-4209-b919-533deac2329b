# 123-sequence

**题目ID**: 52/A  
**比赛**: Codeforces Testing Round 1  
**年份**: 2011  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

There is a given sequence of integers a1, a2, ..., an, where every number is from 1 to 3 inclusively. You have to replace the minimum number of numbers in it so that all the numbers in the sequence are equal to each other.

## 输入格式

The first line contains an integer n (1 ≤ n ≤ 106). The second line contains a sequence of integers a1, a2, ..., an (1 ≤ ai ≤ 3).

## 输出格式

Print the minimum number of replacements needed to be performed to make all the numbers in the sequence equal.

## 样例

### 样例 1

**输入**:
```
9
1 3 2 2 2 1 1 2 3
```

**输出**:
```
5
```

## 备注

In the example all the numbers equal to 1 and 3 should be replaced by 2.

