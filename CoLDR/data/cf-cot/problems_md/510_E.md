# Fox And Dinner

**题目ID**: 510/E  
**比赛**: Codeforces Round 290 (Div. 2)  
**年份**: 2015  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON>iel is participating in a party in Prime Kingdom. There are n foxes there (include <PERSON>). The i-th fox is ai years old.

They will have dinner around some round tables. You want to distribute foxes such that:

1. Each fox is sitting at some table.
2. Each table has at least 3 foxes sitting around it.
3. The sum of ages of any two adjacent foxes around each table should be a prime number.

If k foxes f1, f2, ..., fk are sitting around table in clockwise order, then for 1 ≤ i ≤ k - 1: fi and fi + 1 are adjacent, and f1 and fk are also adjacent.

If it is possible to distribute the foxes in the desired manner, find out a way to do that.

## 输入格式

The first line contains single integer n (3 ≤ n ≤ 200): the number of foxes in this party.

The second line contains n integers ai (2 ≤ ai ≤ 104).

## 输出格式

If it is impossible to do this, output "Impossible".

Otherwise, in the first line output an integer m ($$1 \leq m \leq \frac{n}{3}$$): the number of tables.

Then output m lines, each line should start with an integer k -=– the number of foxes around that table, and then k numbers — indices of fox sitting around that table in clockwise order.

If there are several possible arrangements, output any of them.

## 样例

### 样例 1

**输入**:
```
4
3 4 8 9
```

**输出**:
```
1
4 1 2 4 3
```

### 样例 2

**输入**:
```
5
2 2 2 2 2
```

**输出**:
```
Impossible
```

### 样例 3

**输入**:
```
12
2 3 4 5 6 7 8 9 10 11 12 13
```

**输出**:
```
1
12 1 2 3 6 5 12 9 8 7 10 11 4
```

### 样例 4

**输入**:
```
24
2 3 4 5 6 7 8 9 10 11 12 13 14 15 16 17 18 19 20 21 22 23 24 25
```

**输出**:
```
3
6 1 2 3 6 5 4
10 7 8 9 12 15 14 13 16 11 10
8 17 18 23 22 19 20 21 24
```

## 备注

In example 1, they can sit around one table, their ages are: 3-8-9-4, adjacent sums are: 11, 17, 13 and 7, all those integers are primes.

In example 2, it is not possible: the sum of 2+2 = 4 is not a prime number.

