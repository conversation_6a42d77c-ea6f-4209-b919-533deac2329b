# Longest Increasing Subsequence

**题目ID**: 568/E  
**比赛**: Codeforces Round 315 (Div. 1)  
**年份**: 2015  
**时间限制**: 1.5秒  
**内存限制**: 128MB  

## 题目描述

Note that the memory limit in this problem is less than usual.

Let's consider an array consisting of positive integers, some positions of which contain gaps.

We have a collection of numbers that can be used to fill the gaps. Each number from the given collection can be used at most once.

Your task is to determine such way of filling gaps that the longest increasing subsequence in the formed array has a maximum size.

## 输入格式

The first line contains a single integer n — the length of the array (1 ≤ n ≤ 105).

The second line contains n space-separated integers — the elements of the sequence. A gap is marked as "-1". The elements that are not gaps are positive integers not exceeding 109. It is guaranteed that the sequence contains 0 ≤ k ≤ 1000 gaps.

The third line contains a single positive integer m — the number of elements to fill the gaps (k ≤ m ≤ 105).

The fourth line contains m positive integers — the numbers to fill gaps. Each number is a positive integer not exceeding 109. Some numbers may be equal.

## 输出格式

Print n space-separated numbers in a single line — the resulting sequence. If there are multiple possible answers, print any of them.

## 样例

### 样例 1

**输入**:
```
3
1 2 3
1
10
```

**输出**:
```
1 2 3
```

### 样例 2

**输入**:
```
3
1 -1 3
3
1 2 3
```

**输出**:
```
1 2 3
```

### 样例 3

**输入**:
```
2
-1 2
2
2 4
```

**输出**:
```
2 2
```

### 样例 4

**输入**:
```
3
-1 -1 -1
5
1 1 1 1 2
```

**输出**:
```
1 1 2
```

### 样例 5

**输入**:
```
4
-1 -1 -1 2
4
1 1 2 2
```

**输出**:
```
1 2 1 2
```

## 备注

In the first sample there are no gaps, so the correct answer is the initial sequence.

In the second sample there is only one way to get an increasing subsequence of length 3.

In the third sample answer "4 2" would also be correct. Note that only strictly increasing subsequences are considered.

In the fifth sample the answer "1 1 1 2" is not considered correct, as number 1 can be used in replacing only two times.

## 题解

Будем поддерживать массив c: c[len] — минимальное число, на которое может заканчиваться возрастающая подпоследовательность длины len (Одно из двух стандартных решений задачи о наибольшей возрастающей подпоследовательности). Элементы этого массива возрастают и добавление очередного элемента v к обработанной части последовательности сводится к нахождению такого i, что c[i] ≤ v и c[i + 1] ≥ v. При обработке пропуска нам нужно попробовать вставить все числа из множества b. Предварительно их отсортировав и двигаясь двумя указателями вдоль массивов b и c мы можем проделать нужные обновления за O(n + m).
Авторами подразумевалось использование O(n) памяти для восстановления ответа. Этого можно добиться следующим образом: 1. Параллельно с массивом c будем хранить массив cindex[len] - индекс элемента, на который заканчивается оптимальная НВП длины len. Если она заканчивается в пропуске - будем хранить, например,  - 1. 2. Также сохраним для каждого не пропуска - длину НВП(lenLIS[pos]), заканчивающейся в этой позиции (это несложно делается в процессе вычисления массива c) и позицию(prevIndex[pos]) предыдущего элемента в этой НВП (если это пропуск, опять же  - 1).
Теперь приступим к восстановлению ответа. Пока мы не наткнулись на пропуск - можно спокойно восстанавливать НВП с конца. Сложность обнаруживается, когда несколько следующих элементов последовательности попали в пропуски. Но можно несложно определить что это за пропуски и чем их заполнить. А именно: пусть сейчас мы стоим в позиции r. Нам нужно найти такую позицию l (не пропуск), что мы сможем заполнить ровно lenLIS[r] - lenLIS[l] пропусков между l и r возрастающими числами в интервале (a[l]..a[r]). Позицию l можно итеративно перебирать от r - 1 до 0, параллельно насчитывая число пройденных пропусков. Проверку условия описанного выше можно несложно сделать с помощью пары бинпоисков.
Немного подробнее и с деталями:
• Как узнать, что между позициями l и r можно заполнить пропуски так, чтобы не ухудшить генерируемый ответ?Пусть countSkip(l, r) - количество пропусков на интервале (l..r), а countBetween(x, y) - количество различных чисел из множества b, лежищих в интервале (x..y). Тогда позиции l и r хорошие тогда и только тогда, когда lenLIS[r] - lenLIS[l] = min(countSkip(l, r), countBetween(a[l], a[r])). countSkip можно насчитывать в процессе уменьшения границы l, countBetween(x, y) = max(0, lower_bound(b, y) - upper_bound(b, x)).
• Что делать, если НВП заканчивается или начинается в пропуске (тогда мы не знаем, откуда начать/где закончить)?Наиболее простым решением будет добавить  - ∞ и  + ∞ в начало и конец нашего массива соответственно. Это позволит избежать проблем с такими крайними случаями.
Сложность — $${ \mathcal { O } } ( n \log n + k ( n + m ) )$$.

