# Valuable Resources

**题目ID**: 485/B  
**比赛**: Codeforces Round 276 (Div. 2)  
**年份**: 2014  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

Many computer strategy games require building cities, recruiting army, conquering tribes, collecting resources. Sometimes it leads to interesting problems.

Let's suppose that your task is to build a square city. The world map uses the Cartesian coordinates. The sides of the city should be parallel to coordinate axes. The map contains mines with valuable resources, located at some points with integer coordinates. The sizes of mines are relatively small, i.e. they can be treated as points. The city should be built in such a way that all the mines are inside or on the border of the city square.

Building a city takes large amount of money depending on the size of the city, so you have to build the city with the minimum area. Given the positions of the mines find the minimum possible area of the city.

## 输入格式

The first line of the input contains number n — the number of mines on the map (2 ≤ n ≤ 1000). Each of the next n lines contains a pair of integers xi and yi — the coordinates of the corresponding mine ( - 109 ≤ xi, yi ≤ 109). All points are pairwise distinct.

## 输出格式

Print the minimum area of the city that can cover all the mines with valuable resources.

## 样例

### 样例 1

**输入**:
```
2
0 0
2 2
```

**输出**:
```
4
```

### 样例 2

**输入**:
```
2
0 0
0 3
```

**输出**:
```
9
```

