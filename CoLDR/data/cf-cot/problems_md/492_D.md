# <PERSON><PERSON> and Computer Game

**题目ID**: 492/D  
**比赛**: Codeforces Round 280 (Div. 2)  
**年份**: 2014  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON><PERSON> and his friend <PERSON><PERSON> play a computer game where they need to destroy n monsters to pass a level. <PERSON><PERSON>'s character performs attack with frequency x hits per second and <PERSON><PERSON>'s character performs attack with frequency y hits per second. Each character spends fixed time to raise a weapon and then he hits (the time to raise the weapon is 1 / x seconds for the first character and 1 / y seconds for the second one). The i-th monster dies after he receives ai hits.

<PERSON><PERSON> and <PERSON><PERSON> wonder who makes the last hit on each monster. If <PERSON><PERSON> and <PERSON><PERSON> make the last hit at the same time, we assume that both of them have made the last hit.

## 输入格式

The first line contains three integers n,x,y (1 ≤ n ≤ 105, 1 ≤ x, y ≤ 106) — the number of monsters, the frequency of <PERSON><PERSON>'s and <PERSON><PERSON>'s attack, correspondingly.

Next n lines contain integers ai (1 ≤ ai ≤ 109) — the number of hits needed do destroy the i-th monster.

## 输出格式

Print n lines. In the i-th line print word "<PERSON><PERSON>", if the last hit on the i-th monster was performed by <PERSON><PERSON>, "<PERSON><PERSON>", if <PERSON><PERSON> performed the last hit, or "Both", if both boys performed it at the same time.

## 样例

### 样例 1

**输入**:
```
4 3 2
1
2
3
4
```

**输出**:
```
<PERSON>ya
Vova
<PERSON>ya
Both
```

### 样例 2

**输入**:
```
2 1 1
1
2
```

**输出**:
```
Both
Both
```

## 备注

In the first sample <PERSON>ya makes the first hit at time 1 / 3, Vova makes the second hit at time 1 / 2, <PERSON>ya makes the third hit at time 2 / 3, and both boys make the fourth and fifth hit simultaneously at the time 1.

In the second sample Vanya and Vova make the first and second hit simultaneously at time 1.

