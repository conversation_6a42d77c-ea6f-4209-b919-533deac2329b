# Island Puzzle

**题目ID**: 627/F  
**比赛**: 8VC Venture Cup 2016 - Final Round  
**年份**: 2016  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

A remote island chain contains n islands, with some bidirectional bridges between them. The current bridge network forms a tree. In other words, a total of n - 1 bridges connect pairs of islands in a way that it's possible to reach any island from any other island using the bridge network. The center of each island contains an identical pedestal, and all but one of the islands has a fragile, uniquely colored statue currently held on the pedestal. The remaining island holds only an empty pedestal.

The islanders want to rearrange the statues in a new order. To do this, they repeat the following process: first, they choose an island directly adjacent to the island containing an empty pedestal. Then, they painstakingly carry the statue on this island across the adjoining bridge and place it on the empty pedestal.

It is often impossible to rearrange statues in the desired order using only the operation described above. The islanders would like to build one additional bridge in order to make this achievable in the fewest number of movements possible. Find the bridge to construct and the minimum number of statue movements necessary to arrange the statues in the desired position.

## 输入格式

The first line contains a single integer n (2 ≤ n ≤ 200 000) — the total number of islands.

The second line contains n space-separated integers ai (0 ≤ ai ≤ n - 1) — the statue currently located on the i-th island. If ai = 0, then the island has no statue. It is guaranteed that the ai are distinct.

The third line contains n space-separated integers bi (0 ≤ bi ≤ n - 1) — the desired statues of the i-th island. Once again, bi = 0 indicates the island desires no statue. It is guaranteed that the bi are distinct.

The next n - 1 lines each contain two distinct space-separated integers ui and vi (1 ≤ ui, vi ≤ n) — the endpoints of the i-th bridge. Bridges form a tree, and it is guaranteed that no bridge is listed twice in the input.

## 输出格式

Print a single line of integers:

If the rearrangement can be done in the existing network, output 0 t, where t is the number of moves necessary to perform the rearrangement.

Otherwise, print u, v, and t (1 ≤ u < v ≤ n) — the two endpoints of the new bridge, and the minimum number of statue movements needed to perform the rearrangement.

If the rearrangement cannot be done no matter how the new bridge is built, print a single line containing  - 1.

## 样例

### 样例 1

**输入**:
```
3
1 0 2
2 0 1
1 2
2 3
```

**输出**:
```
1 3 3
```

### 样例 2

**输入**:
```
2
1 0
0 1
1 2
```

**输出**:
```
0 1
```

### 样例 3

**输入**:
```
4
0 1 2 3
0 2 3 1
1 2
1 3
1 4
```

**输出**:
```
-1
```

## 备注

In the first sample, the islanders can build a bridge connecting islands 1 and 3 and then make the following sequence of moves: first move statue 1 from island 1 to island 2, then move statue 2 from island 3 to island 1, and finally move statue 1 from island 2 to island 3 for a total of 3 moves.

In the second sample, the islanders can simply move statue 1 from island 1 to island 2. No new bridges need to be built and only 1 move needs to be made.

In the third sample, no added bridge and subsequent movements result in the desired position.

