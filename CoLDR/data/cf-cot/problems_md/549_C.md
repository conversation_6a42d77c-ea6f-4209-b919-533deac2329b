# The Game Of Parity

**题目ID**: 549/C  
**比赛**: Looksery Cup 2015  
**年份**: 2015  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

There are n cities in Westeros. The i-th city is inhabited by ai people. <PERSON><PERSON><PERSON> and <PERSON><PERSON> play the following game: in one single move, a player chooses a certain town and burns it to the ground. Thus all its residents, sadly, die. <PERSON><PERSON> starts the game. The game ends when Westeros has exactly k cities left.

The prophecy says that if the total number of surviving residents is even, then <PERSON><PERSON><PERSON> wins: <PERSON><PERSON> gets beheaded, and Daener<PERSON> rises on the Iron Throne. If the total number of surviving residents is odd, <PERSON><PERSON> wins and everything goes in the completely opposite way.

Lord <PERSON><PERSON> wants to know which candidates to the throne he should support, and therefore he wonders, which one of them has a winning strategy. Answer to this question of Lord <PERSON> and maybe you will become the next Lord of Harrenholl.

## 输入格式

The first line contains two positive space-separated integers, n and k (1 ≤ k ≤ n ≤ 2·105) — the initial number of cities in Westeros and the number of cities at which the game ends.

The second line contains n space-separated positive integers ai (1 ≤ ai ≤ 106), which represent the population of each city in Westeros.

## 输出格式

Print string "<PERSON><PERSON><PERSON>" (without the quotes), if <PERSON><PERSON><PERSON> wins and "<PERSON><PERSON>" (without the quotes), if <PERSON><PERSON> wins.

## 样例

### 样例 1

**输入**:
```
3 1
1 2 1
```

**输出**:
```
Stannis
```

### 样例 2

**输入**:
```
3 1
2 2 1
```

**输出**:
```
Daenerys
```

### 样例 3

**输入**:
```
6 3
5 20 12 7 14 101
```

**输出**:
```
Stannis
```

## 备注

In the first sample Stannis will use his move to burn a city with two people and Daenerys will be forced to burn a city with one resident. The only survivor city will have one resident left, that is, the total sum is odd, and thus Stannis wins.

In the second sample, if Stannis burns a city with two people, Daenerys burns the city with one resident, or vice versa. In any case, the last remaining city will be inhabited by two people, that is, the total sum is even, and hence Daenerys wins.

## 题解

If n = k no moves may be done. The winner is determined with starting parity of citizens' number. Otherwise let's see that the player making the last move may guarantee his victory, if there are both odd and even cities when he makes the move. He just selects the city of which parity he should burn to obtain required parity. So, his enemy's target is to destroy all the cities of some one type. Sometimes the type does matter, sometimes doesn't. It depends on the player's name and the parity of k. So the problem's solution consists in checking if "non-last" player's moves number (n - k) / 2 is enough to destroy all the odd or even cities. If Stannis makes the last move and k is odd, Daenerys should burn all the odd or all the even cities. If k is even, Daenerys should burn all the odd cities. If Daenerys makes the last move and k is even, Stannis has no chance to win. If k is odd, Stannis should burn all the even cities.

