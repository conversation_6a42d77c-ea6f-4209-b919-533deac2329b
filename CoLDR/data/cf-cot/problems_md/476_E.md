# Dreamoon and Strings

**题目ID**: 476/E  
**比赛**: Codeforces Round 272 (Div. 2)  
**年份**: 2014  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON><PERSON> has a string s and a pattern string p. He first removes exactly x characters from s obtaining string s' as a result. Then he calculates $$\mathsf{occ}(s',p)$$ that is defined as the maximal number of non-overlapping substrings equal to p that can be found in s'. He wants to make this number as big as possible.

More formally, let's define $$\operatorname{ans}(x)$$ as maximum value of $$\mathsf{occ}(s',p)$$ over all s' that can be obtained by removing exactly x characters from s. <PERSON><PERSON> wants to know $$\operatorname{ans}(x)$$ for all x from 0 to |s| where |s| denotes the length of string s.

## 输入格式

The first line of the input contains the string s (1 ≤ |s| ≤ 2 000).

The second line of the input contains the string p (1 ≤ |p| ≤ 500).

Both strings will only consist of lower case English letters.

## 输出格式

Print |s| + 1 space-separated integers in a single line representing the $$\operatorname{ans}(x)$$ for all x from 0 to |s|.

## 样例

### 样例 1

**输入**:
```
aaaaa
aa
```

**输出**:
```
2 2 1 1 0 0
```

### 样例 2

**输入**:
```
axbaxxb
ab
```

**输出**:
```
0 1 1 2 1 1 0 0
```

## 备注

For the first sample, the corresponding optimal values of s' after removal 0 through |s| = 5 characters from s are {"aaaaa", "aaaa", "aaa", "aa", "a", ""}.

For the second sample, possible corresponding optimal values of s' are {"axbaxxb", "abaxxb", "axbab", "abab", "aba", "ab", "a", ""}.

