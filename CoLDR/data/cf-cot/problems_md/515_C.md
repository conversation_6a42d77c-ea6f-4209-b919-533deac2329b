# Drazil and Factorial

**题目ID**: 515/C  
**比赛**: Codeforces Round 292 (Div. 2)  
**年份**: 2015  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

Drazil is playing a math game with Varda.

Let's define $$\mathbf{F}(x)$$ for positive integer x as a product of factorials of its digits. For example, $$F(135) = 1! * 3! * 5! = 720$$.

First, they choose a decimal number a consisting of n digits that contains at least one digit larger than 1. This number may possibly start with leading zeroes. Then they should find maximum positive number x satisfying following two conditions:

1. x doesn't contain neither digit 0 nor digit 1.

2. $$\mathbf{F}(x)$$ = $$\mathbf{F}(a)$$.

Help friends find such number.

## 输入格式

The first line contains an integer n (1 ≤ n ≤ 15) — the number of digits in a.

The second line contains n digits of a. There is at least one digit in a that is larger than 1. Number a may possibly contain leading zeroes.

## 输出格式

Output a maximum possible integer satisfying the conditions above. There should be no zeroes and ones in this number decimal representation.

## 样例

### 样例 1

**输入**:
```
4
1234
```

**输出**:
```
33222
```

### 样例 2

**输入**:
```
3
555
```

**输出**:
```
555
```

## 备注

In the first case, $$F(1234) = 1! * 2! * 3! * 4! = 288 = F(3322)$$

