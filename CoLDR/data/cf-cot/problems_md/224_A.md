# Parallelepiped

**题目ID**: 224/A  
**比赛**: Codeforces Round 138 (Div. 2)  
**年份**: 2012  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

You've got a rectangular parallelepiped with integer edge lengths. You know the areas of its three faces that have a common vertex. Your task is to find the sum of lengths of all 12 edges of this parallelepiped.

## 输入格式

The first and the single line contains three space-separated integers — the areas of the parallelepiped's faces. The area's values are positive ( > 0) and do not exceed 104. It is guaranteed that there exists at least one parallelepiped that satisfies the problem statement.

## 输出格式

Print a single number — the sum of all edges of the parallelepiped.

## 样例

### 样例 1

**输入**:
```
1 1 1
```

**输出**:
```
12
```

### 样例 2

**输入**:
```
4 6 6
```

**输出**:
```
28
```

## 备注

In the first sample the parallelepiped has sizes 1 × 1 × 1, in the second one — 2 × 2 × 3.

