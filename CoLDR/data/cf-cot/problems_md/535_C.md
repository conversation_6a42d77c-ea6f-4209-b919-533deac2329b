# Tavas and Karafs

**题目ID**: 535/C  
**比赛**: Codeforces Round 299 (Div. 2)  
**年份**: 2015  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

Karafs is some kind of vegetable in shape of an 1 × h rectangle. Tavaspolis people love Karafs and they use Karafs in almost any kind of food. <PERSON><PERSON>, himself, is crazy about Karafs.

Each Karafs has a positive integer height. <PERSON><PERSON> has an infinite 1-based sequence of Karafses. The height of the i-th Karafs is si = A + (i - 1) × B.

For a given m, let's define an m-bite operation as decreasing the height of at most m distinct not eaten Karafses by 1. Karafs is considered as eaten when its height becomes zero.

Now <PERSON><PERSON><PERSON><PERSON> asks you n queries. In each query he gives you numbers l, t and m and you should find the largest number r such that l ≤ r and sequence sl, sl + 1, ..., sr can be eaten by performing m-bite no more than t times or print -1 if there is no such number r.

## 输入格式

The first line of input contains three integers A, B and n (1 ≤ A, B ≤ 106, 1 ≤ n ≤ 105).

Next n lines contain information about queries. i-th line contains integers l, t, m (1 ≤ l, t, m ≤ 106) for i-th query.

## 输出格式

For each query, print its answer in a single line.

## 样例

### 样例 1

**输入**:
```
2 1 4
1 5 3
3 3 10
7 10 2
6 4 8
```

**输出**:
```
4
-1
8
-1
```

### 样例 2

**输入**:
```
1 5 2
1 5 10
2 7 4
```

**输出**:
```
1
2
```

