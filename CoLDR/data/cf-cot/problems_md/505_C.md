# Mr. <PERSON><PERSON><PERSON>, the Treasure Hunter

**题目ID**: 505/C  
**比赛**: Codeforces Round 286 (Div. 2)  
**年份**: 2015  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

The Shuseki Islands are an archipelago of 30001 small islands in the Yutampo Sea. The islands are evenly spaced along a line, numbered from 0 to 30000 from the west to the east. These islands are known to contain many treasures. There are n gems in the Shuseki Islands in total, and the i-th gem is located on island pi.

Mr. <PERSON> has just arrived at island 0. With his great jumping ability, he will repeatedly perform jumps between islands to the east according to the following process:

- First, he will jump from island 0 to island d.
- After that, he will continue jumping according to the following rule. Let l be the length of the previous jump, that is, if his previous jump was from island prev to island cur, let l = cur - prev. He will perform a jump of length l - 1, l or l + 1 to the east. That is, he will jump to island (cur + l - 1), (cur + l) or (cur + l + 1) (if they exist). The length of a jump must be positive, that is, he cannot perform a jump of length 0 when l = 1. If there is no valid destination, he will stop jumping.

Mr. <PERSON> will collect the gems on the islands visited during the process. Find the maximum number of gems that he can collect.

## 输入格式

The first line of the input contains two space-separated integers n and d (1 ≤ n, d ≤ 30000), denoting the number of the gems in the Shuseki Islands and the length of the Mr. Kitayuta's first jump, respectively.

The next n lines describe the location of the gems. The i-th of them (1 ≤ i ≤ n) contains a integer pi (d ≤ p1 ≤ p2 ≤ ... ≤ pn ≤ 30000), denoting the number of the island that contains the i-th gem.

## 输出格式

Print the maximum number of gems that Mr. Kitayuta can collect.

## 样例

### 样例 1

**输入**:
```
4 10
10
21
27
27
```

**输出**:
```
3
```

### 样例 2

**输入**:
```
8 8
9
19
28
36
45
55
66
78
```

**输出**:
```
6
```

### 样例 3

**输入**:
```
13 7
8
8
9
16
17
17
18
21
23
24
24
26
30
```

**输出**:
```
4
```

## 备注

In the first sample, the optimal route is 0  →  10 (+1 gem)  →  19  →  27 (+2 gems)  → ...

In the second sample, the optimal route is 0  →  8  →  15  →  21 →  28 (+1 gem)  →  36 (+1 gem)  →  45 (+1 gem)  →  55 (+1 gem)  →  66 (+1 gem)  →  78 (+1 gem)  → ...

In the third sample, the optimal route is 0  →  7  →  13  →  18 (+1 gem)  →  24 (+2 gems)  →  30 (+1 gem)  → ...

