# Increase Sequence

**题目ID**: 466/D  
**比赛**: Codeforces Round 266 (Div. 2)  
**年份**: 2014  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON> has a sequence of integers a1, a2, ..., an. <PERSON> wants all numbers in the sequence to equal h. He can perform the operation of "adding one on the segment [l, r]": add one to all elements of the sequence with indices from l to r (inclusive). At that, <PERSON> never chooses any element as the beginning of the segment twice. Similarly, <PERSON> never chooses any element as the end of the segment twice. In other words, for any two segments [l1, r1] and [l2, r2], where <PERSON> added one, the following inequalities hold: l1 ≠ l2 and r1 ≠ r2.

How many distinct ways are there to make all numbers in the sequence equal h? Print this number of ways modulo 1000000007 (109 + 7). Two ways are considered distinct if one of them has a segment that isn't in the other way.

## 输入格式

The first line contains two integers n, h (1 ≤ n, h ≤ 2000). The next line contains n integers a1, a2, ..., an (0 ≤ ai ≤ 2000).

## 输出格式

Print a single integer — the answer to the problem modulo 1000000007 (109 + 7).

## 样例

### 样例 1

**输入**:
```
3 2
1 1 1
```

**输出**:
```
4
```

### 样例 2

**输入**:
```
5 1
1 1 1 1 1
```

**输出**:
```
1
```

### 样例 3

**输入**:
```
4 3
3 2 1 1
```

**输出**:
```
0
```

