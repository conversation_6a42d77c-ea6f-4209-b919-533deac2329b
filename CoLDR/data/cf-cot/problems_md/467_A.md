# George and Accommodation

**题目ID**: 467/A  
**比赛**: Codeforces Round 267 (Div. 2)  
**年份**: 2014  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON> has recently entered the BSUCP (Berland State University for Cool Programmers). <PERSON> has a friend <PERSON> who has also entered the university. Now they are moving into a dormitory.

<PERSON> and <PERSON> want to live in the same room. The dormitory has n rooms in total. At the moment the i-th room has pi people living in it and the room can accommodate qi people in total (pi ≤ qi). Your task is to count how many rooms has free place for both <PERSON> and <PERSON>.

## 输入格式

The first line contains a single integer n (1 ≤ n ≤ 100) — the number of rooms.

The i-th of the next n lines contains two integers pi and qi (0 ≤ pi ≤ qi ≤ 100) — the number of people who already live in the i-th room and the room's capacity.

## 输出格式

Print a single integer — the number of rooms where <PERSON> and <PERSON> can move in.

## 样例

### 样例 1

**输入**:
```
3
1 1
2 2
3 3
```

**输出**:
```
0
```

### 样例 2

**输入**:
```
3
1 10
0 10
10 10
```

**输出**:
```
2
```

