# Шестерни

**题目ID**: 497/D  
**比赛**: Codeforces Round 283 (Div. 1)  
**年份**: 2014  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

На плоскости расположены два многоугольника A и B. Многоугольник A вращается вокруг точки P, а многоугольник B — вокруг точки Q. Каждый многоугольник вращается с постоянной угловой скоростью по часовой стрелке вокруг своей точки, угловые скорости вращения многоугольников совпадают.

Требуется определить, произойдет ли когда-нибудь столкновение между многоугольниками. Под столкновением подразумевается ситуация, в которой многоугольники имеют хотя бы одну общую точку.

Гарантируется, что в момент времени 0 многоугольники A и B не пересекаются, и ни один из многоугольников не содержится целиком внутри другого.

Обратите внимание, что:

- многоугольники могут не являться выпуклыми;
- точки P и Q могут находиться на границе или вне своих многоугольников.

## 输入格式

В первой строке через пробел записаны координаты точки P.

Во второй строке записано одно целое число n (3 ≤ n ≤ 1000) — количество вершин многоугольника A.

В каждой их следующих n строк записано по два числа, разделенных пробелом — координаты очередной вершины многоугольника A.

Следующая строка оставлена пустой.

Далее следуют через пробел координаты точки Q.

В следующей строке находится одно целое число m (3 ≤ m ≤ 1000) — количество вершин многоугольника B. Далее в m строках в аналогичном формате описаны координаты координаты вершин многоугольника B.

Вершины обоих многоугольников перечислены в порядке обхода против часовой стрелки. Все координаты точек — целые числа, по модулю не превосходящие 104.

## 输出格式

Выведите «YES», если столкновение произойдет, и «NO» в противном случае.

## 样例

### 样例 1

**输入**:
```
1 0
4
0 0
1 0
1 5
0 5
9 0
4
9 0
9 -5
10 -5
10 0
```

**输出**:
```
YES
```

### 样例 2

**输入**:
```
0 0
3
1 0
2 -1
2 1
0 0
3
-1 0
-2 1
-2 -1
```

**输出**:
```
NO
```

## 备注

Многоугольником называется замкнутая ломаная без самопересечений и самокасаний.

Картинка к первому примеру:

