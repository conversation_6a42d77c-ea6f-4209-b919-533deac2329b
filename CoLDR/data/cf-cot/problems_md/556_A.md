# Case of the Zeros and Ones

**题目ID**: 556/A  
**比赛**: Codeforces Round 310 (Div. 2)  
**年份**: 2015  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON><PERSON> the Android is a galaxy-famous detective. In his free time he likes to think about strings containing zeros and ones.

Once he thought about a string of length n consisting of zeroes and ones. Consider the following operation: we choose any two adjacent positions in the string, and if one them contains 0, and the other contains 1, then we are allowed to remove these two digits from the string, obtaining a string of length n - 2 as a result.

Now <PERSON><PERSON> thinks about what is the minimum length of the string that can remain after applying the described operation several times (possibly, zero)? Help him to calculate this number.

## 输入格式

First line of the input contains a single integer n (1 ≤ n ≤ 2·105), the length of the string that <PERSON><PERSON> has.

The second line contains the string of length n consisting only from zeros and ones.

## 输出格式

Output the minimum length of the string that may remain after applying the described operations several times.

## 样例

### 样例 1

**输入**:
```
4
1100
```

**输出**:
```
0
```

### 样例 2

**输入**:
```
5
01010
```

**输出**:
```
1
```

### 样例 3

**输入**:
```
8
11101111
```

**输出**:
```
6
```

## 备注

In the first sample test it is possible to change the string like the following: $$1100 \rightarrow 10 \rightarrow (empty)$$.

In the second sample test it is possible to change the string like the following: $$01010 \rightarrow 010 \rightarrow 0$$.

In the third sample test it is possible to change the string like the following: $$11101111 \rightarrow 11111111$$.

