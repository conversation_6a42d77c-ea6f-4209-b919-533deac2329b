# A and <PERSON> and Chess

**题目ID**: 519/A  
**比赛**: Codeforces Round 294 (Div. 2)  
**年份**: 2015  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

A and B are preparing themselves for programming contests.

To train their logical thinking and solve problems better, <PERSON> and <PERSON> decided to play chess. During the game <PERSON> wondered whose position is now stronger.

For each chess piece we know its weight:

- the queen's weight is 9,
- the rook's weight is 5,
- the bishop's weight is 3,
- the knight's weight is 3,
- the pawn's weight is 1,
- the king's weight isn't considered in evaluating position.

The player's weight equals to the sum of weights of all his pieces on the board.

As <PERSON> doesn't like counting, he asked you to help him determine which player has the larger position weight.

## 输入格式

The input contains eight lines, eight characters each — the board's description.

The white pieces on the board are marked with uppercase letters, the black pieces are marked with lowercase letters.

The white pieces are denoted as follows: the queen is represented is 'Q', the rook — as 'R', the bishop — as'B', the knight — as 'N', the pawn — as '<PERSON>', the king — as 'K'.

The black pieces are denoted as 'q', 'r', 'b', 'n', 'p', 'k', respectively.

An empty square of the board is marked as '.' (a dot).

It is not guaranteed that the given chess position can be achieved in a real game. Specifically, there can be an arbitrary (possibly zero) number pieces of each type, the king may be under attack and so on.

## 输出格式

Print "White" (without quotes) if the weight of the position of the white pieces is more than the weight of the position of the black pieces, print "Black" if the weight of the black pieces is more than the weight of the white pieces and print "Draw" if the weights of the white and black pieces are equal.

## 样例

### 样例 1

**输入**:
```
...QK...
........
........
........
........
........
........
...rk...
```

**输出**:
```
White
```

### 样例 2

**输入**:
```
rnbqkbnr
pppppppp
........
........
........
........
PPPPPPPP
RNBQKBNR
```

**输出**:
```
Draw
```

### 样例 3

**输入**:
```
rppppppr
...k....
........
........
........
........
K...Q...
........
```

**输出**:
```
Black
```

## 备注

In the first test sample the weight of the position of the white pieces equals to 9, the weight of the position of the black pieces equals 5.

In the second test sample the weights of the positions of the black and the white pieces are equal to 39.

In the third test sample the weight of the position of the white pieces equals to 9, the weight of the position of the black pieces equals to 16.

