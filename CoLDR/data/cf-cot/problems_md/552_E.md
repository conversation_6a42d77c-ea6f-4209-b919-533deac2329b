# Vanya and Brackets

**题目ID**: 552/E  
**比赛**: Codeforces Round 308 (Div. 2)  
**年份**: 2015  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON><PERSON> is doing his maths homework. He has an expression of form $$x_{1} \diamond x_{2} \diamond x_{3} \diamond \ldots \diamond x_{n}$$, where x1, x2, ..., xn are digits from 1 to 9, and sign $$\text{ }$$ represents either a plus '+' or the multiplication sign '*'. <PERSON><PERSON> needs to add one pair of brackets in this expression so that to maximize the value of the resulting expression.

## 输入格式

The first line contains expression s (1 ≤ |s| ≤ 5001, |s| is odd), its odd positions only contain digits from 1 to 9, and even positions only contain signs  +  and  * .

The number of signs  *  doesn't exceed 15.

## 输出格式

In the first line print the maximum possible value of an expression.

## 样例

### 样例 1

**输入**:
```
3+5*7+8*4
```

**输出**:
```
303
```

### 样例 2

**输入**:
```
2+3*5
```

**输出**:
```
25
```

### 样例 3

**输入**:
```
3*4*5
```

**输出**:
```
60
```

## 备注

Note to the first sample test. 3 + 5 * (7 + 8) * 4 = 303.

Note to the second sample test. (2 + 3) * 5 = 25.

Note to the third sample test. (3 * 4) * 5 = 60 (also many other variants are valid, for instance, (3) * 4 * 5 = 60).

