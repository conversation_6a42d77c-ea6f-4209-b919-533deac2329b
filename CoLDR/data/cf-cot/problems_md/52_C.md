# Circular RMQ

**题目ID**: 52/C  
**比赛**: Codeforces Testing Round 1  
**年份**: 2011  
**时间限制**: 1.5秒  
**内存限制**: 256MB  

## 题目描述

You are given circular array a0, a1, ..., an - 1. There are two types of operations with it:

- inc(lf, rg, v) — this operation increases each element on the segment [lf, rg] (inclusively) by v;
- rmq(lf, rg) — this operation returns minimal value on the segment [lf, rg] (inclusively).

Assume segments to be circular, so if n = 5 and lf = 3, rg = 1, it means the index sequence: 3, 4, 0, 1.

Write program to process given sequence of operations.

## 输入格式

The first line contains integer n (1 ≤ n ≤ 200000). The next line contains initial state of the array: a0, a1, ..., an - 1 ( - 106 ≤ ai ≤ 106), ai are integer. The third line contains integer m (0 ≤ m ≤ 200000), m — the number of operartons. Next m lines contain one operation each. If line contains two integer lf, rg (0 ≤ lf, rg ≤ n - 1) it means rmq operation, it contains three integers lf, rg, v (0 ≤ lf, rg ≤ n - 1; - 106 ≤ v ≤ 106) — inc operation.

## 输出格式

For each rmq operation write result for it. Please, do not use %lld specificator to read or write 64-bit integers in C++. It is preffered to use cout (also you may use %I64d).

## 样例

### 样例 1

**输入**:
```
4
1 2 3 4
4
3 0
3 0 -1
0 1
2 1
```

**输出**:
```
1
0
0
```

