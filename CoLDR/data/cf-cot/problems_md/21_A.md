# Jabber ID

**题目ID**: 21/A  
**比赛**: Codeforces Alpha Round 21 (Codeforces format)  
**年份**: 2010  
**时间限制**: 0.5秒  
**内存限制**: 256MB  

## 题目描述

Jabber ID on the national Berland service «Babber» has a form <username>@<hostname>[/resource], where

- <username> — is a sequence of Latin letters (lowercase or uppercase), digits or underscores characters «_», the length of <username> is between 1 and 16, inclusive.
- <hostname> — is a sequence of word separated by periods (characters «.»), where each word should contain only characters allowed for <username>, the length of each word is between 1 and 16, inclusive. The length of <hostname> is between 1 and 32, inclusive.
- <resource> — is a sequence of Latin letters (lowercase or uppercase), digits or underscores characters «_», the length of <resource> is between 1 and 16, inclusive.

The content of square brackets is optional — it can be present or can be absent.

There are the samples of correct Jabber IDs: [email protected], [email protected]/contest.

Your task is to write program which checks if given string is a correct Jabber ID.

## 输入格式

The input contains of a single line. The line has the length between 1 and 100 characters, inclusive. Each characters has ASCII-code between 33 and 127, inclusive.

## 输出格式

Print YES or NO.

## 样例

### 样例 1

**输入**:
```
[email protected]
```

**输出**:
```
YES
```

### 样例 2

**输入**:
```
[email protected]
/contest.icpc/12
```

**输出**:
```
NO
```

