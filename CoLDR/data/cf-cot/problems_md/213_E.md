# Two Permutations

**题目ID**: 213/E  
**比赛**: Codeforces Round 131 (Div. 1)  
**年份**: 2012  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON><PERSON><PERSON> is very keen on number permutations.

A permutation a with length n is a sequence, consisting of n different numbers from 1 to n. Element number i (1 ≤ i ≤ n) of this permutation will be denoted as ai.

<PERSON><PERSON> decided to make a present to <PERSON><PERSON><PERSON> and came up with a new problem on permutations. <PERSON><PERSON> tells R<PERSON>k two number permutations: permutation a with length n and permutation b with length m. R<PERSON><PERSON> must give an answer to the problem: how many distinct integers d exist, such that sequence c (c1 = a1 + d, c2 = a2 + d, ..., cn = an + d) of length n is a subsequence of b.

Sequence a is a subsequence of sequence b, if there are such indices i1, i2, ..., in (1 ≤ i1 < i2 < ... < in ≤ m), that a1 = bi1, a2 = bi2, ..., an = bin, where n is the length of sequence a, and m is the length of sequence b.

You are given permutations a and b, help <PERSON><PERSON><PERSON> solve the given problem.

## 输入格式

The first line contains two integers n and m (1 ≤ n ≤ m ≤ 200000) — the sizes of the given permutations. The second line contains n distinct integers — permutation a, the third line contains m distinct integers — permutation b. Numbers on the lines are separated by spaces.

## 输出格式

On a single line print the answer to the problem.

## 样例

### 样例 1

**输入**:
```
1 1
1
1
```

**输出**:
```
1
```

### 样例 2

**输入**:
```
1 2
1
2 1
```

**输出**:
```
2
```

### 样例 3

**输入**:
```
3 3
2 3 1
1 2 3
```

**输出**:
```
0
```

