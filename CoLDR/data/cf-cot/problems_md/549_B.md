# Looksery Party

**题目ID**: 549/B  
**比赛**: Looksery Cup 2015  
**年份**: 2015  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

The Looksery company, consisting of n staff members, is planning another big party. Every employee has his phone number and the phone numbers of his friends in the phone book. Everyone who comes to the party, sends messages to his contacts about how cool it is. At the same time everyone is trying to spend as much time on the fun as possible, so they send messages to everyone without special thinking, moreover, each person even sends a message to himself or herself.

<PERSON> and <PERSON>, Looksery developers, started a dispute on how many messages each person gets. <PERSON> indicates n numbers, the i-th of which indicates how many messages, in his view, the i-th employee is going to take. If <PERSON> guesses correctly at least one of these numbers, he wins, otherwise <PERSON> wins.

You support <PERSON> in this debate, so you need, given the contact lists of the employees, to determine whether there is a situation where <PERSON> loses. Specifically, you need to determine which employees should come to the party, and which should not, so after all the visitors send messages to their contacts, each employee received a number of messages that is different from what <PERSON> stated.

## 输入格式

The first line contains a single integer n (1 ≤ n ≤ 100) — the number of employees of company Looksery.

Next n lines contain the description of the contact lists of the employees. The i-th of these lines contains a string of length n, consisting of digits zero and one, specifying the contact list of the i-th employee. If the j-th character of the i-th string equals 1, then the j-th employee is in the i-th employee's contact list, otherwise he isn't. It is guaranteed that the i-th character of the i-th line is always equal to 1.

The last line contains n space-separated integers: a1, a2, ..., an (0 ≤ ai ≤ n), where ai represents the number of messages that the i-th employee should get according to Igor.

## 输出格式

In the first line print a single integer m — the number of employees who should come to the party so that Igor loses the dispute.

In the second line print m space-separated integers — the numbers of these employees in an arbitrary order.

If Igor wins the dispute in any case, print -1.

If there are multiple possible solutions, print any of them.

## 样例

### 样例 1

**输入**:
```
3
101
010
001
0 1 2
```

**输出**:
```
1
1
```

### 样例 2

**输入**:
```
1
1
1
```

**输出**:
```
0
```

### 样例 3

**输入**:
```
4
1111
0101
1110
0001
1 0 1 0
```

**输出**:
```
4
1 2 3 4
```

## 备注

In the first sample Igor supposes that the first employee will receive 0 messages. Since he isn't contained in any other contact list he must come to the party in order to receive one message from himself. If he is the only who come to the party then he will receive 1 message, the second employee will receive 0 messages and the third will also receive 1 message. Thereby Igor won't guess any number.

In the second sample if the single employee comes to the party he receives 1 message and Igor wins, so he shouldn't do it.

In the third sample the first employee will receive 2 messages, the second — 3, the third — 2, the fourth — 3.

## 题解

In any cases there is such set of people that if they come on party and send messages to their contacts then each employee receives the number of messages that is different from what Igor pointed. Let's show how to build such set. There are 2 cases.
1. There are no zeros among Igor's numbers. So if nobody comes on party then each employee receives 0 messages and, therefore, the desired set is empty.
2. There is at least one zero. Suppose Igor thinks that i-th employee will receive 0 messages. Then we should add i-th employee in the desired set. He will send messages to his contacts and will receive 1 message from himself. If we add other employees in the desired set then the number of messages that i-th employee will receive will not decrease so we can remove him from considering. Igor pointed some numbers for people from contact list of i-th employee and because they have already received one message we need to decrease these numbers by one. After that we can consider the same problem but with number of employees equals to n - 1. If the remaining number of employees is equal to 0 then the desired set is built.

