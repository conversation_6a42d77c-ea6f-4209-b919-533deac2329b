# The Road to Berland is Paved With Good Intentions

**题目ID**: 228/E  
**比赛**: Codeforces Round 141 (Div. 2)  
**年份**: 2012  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

Berland has n cities, some of them are connected by bidirectional roads. For each road we know whether it is asphalted or not.

The King of Berland Valera II wants to asphalt all roads of Berland, for that he gathered a group of workers. Every day <PERSON><PERSON> chooses exactly one city and orders the crew to asphalt all roads that come from the city. The valiant crew fulfilled the King's order in a day, then workers went home.

Unfortunately, not everything is as great as Valera II would like. The main part of the group were gastarbeiters — illegal immigrants who are enthusiastic but not exactly good at understanding orders in Berlandian. Therefore, having received orders to asphalt the roads coming from some of the city, the group asphalted all non-asphalted roads coming from the city, and vice versa, took the asphalt from the roads that had it.

Upon learning of this progress, Valera II was very upset, but since it was too late to change anything, he asked you to make a program that determines whether you can in some way asphalt Berlandian roads in at most n days. Help the king.

## 输入格式

The first line contains two space-separated integers n, m $$( 1 \leq n \leq 100 ; \; 1 \leq m \leq \frac { n \cdot ( n - 1 ) } { 2 } )$$ — the number of cities and roads in Berland, correspondingly. Next m lines contain the descriptions of roads in Berland: the i-th line contains three space-separated integers ai, bi, ci (1 ≤ ai, bi ≤ n; ai ≠ bi; 0 ≤ ci ≤ 1). The first two integers (ai, bi) are indexes of the cities that are connected by the i-th road, the third integer (ci) equals 1, if the road was initially asphalted, and 0 otherwise.

Consider the cities in Berland indexed from 1 to n, and the roads indexed from 1 to m. It is guaranteed that between two Berlandian cities there is not more than one road.

## 输出格式

In the first line print a single integer x (0 ≤ x ≤ n) — the number of days needed to asphalt all roads. In the second line print x space-separated integers — the indexes of the cities to send the workers to. Print the cities in the order, in which Valera send the workers to asphalt roads. If there are multiple solutions, print any of them.

If there's no way to asphalt all roads, print "Impossible" (without the quotes).

## 样例

### 样例 1

**输入**:
```
4 4
1 2 1
2 4 0
4 3 1
3 2 0
```

**输出**:
```
4
3 2 1 3
```

### 样例 2

**输入**:
```
3 3
1 2 0
2 3 0
3 1 0
```

**输出**:
```
Impossible
```

