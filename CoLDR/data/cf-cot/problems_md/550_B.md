# Preparing Olympiad

**题目ID**: 550/B  
**比赛**: Codeforces Round 306 (Div. 2)  
**年份**: 2015  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

You have n problems. You have estimated the difficulty of the i-th one as integer ci. Now you want to prepare a problemset for a contest, using some of the problems you've made.

A problemset for the contest must consist of at least two problems. You think that the total difficulty of the problems of the contest must be at least l and at most r. Also, you think that the difference between difficulties of the easiest and the hardest of the chosen problems must be at least x.

Find the number of ways to choose a problemset for the contest.

## 输入格式

The first line contains four integers n, l, r, x (1 ≤ n ≤ 15, 1 ≤ l ≤ r ≤ 109, 1 ≤ x ≤ 106) — the number of problems you have, the minimum and maximum value of total difficulty of the problemset and the minimum difference in difficulty between the hardest problem in the pack and the easiest one, respectively.

The second line contains n integers c1, c2, ..., cn (1 ≤ ci ≤ 106) — the difficulty of each problem.

## 输出格式

Print the number of ways to choose a suitable problemset for the contest.

## 样例

### 样例 1

**输入**:
```
3 5 6 1
1 2 3
```

**输出**:
```
2
```

### 样例 2

**输入**:
```
4 40 50 10
10 20 30 25
```

**输出**:
```
2
```

### 样例 3

**输入**:
```
5 25 35 10
10 10 20 10 20
```

**输出**:
```
6
```

## 备注

In the first example two sets are suitable, one consisting of the second and third problem, another one consisting of all three problems.

In the second example, two sets of problems are suitable — the set of problems with difficulties 10 and 30 as well as the set of problems with difficulties 20 and 30.

In the third example any set consisting of one problem of difficulty 10 and one problem of difficulty 20 is suitable.

## 题解

Because of the low constraints, this problem can be solved by complete search over all problem sets (there are 2n of them).
For every potential problem set (which can be conviniently expressed as bit mask) we need to check if it satisfies all needed criteria. We can simply find the sum of problem complexities and also the difference between the most difficult and the easiest problems in linear time, iterating over the problems that we included in our current set/bitmask. If this problem set can be used, we increase the answer by one.
Complexity of this solution is O(2n·n).

