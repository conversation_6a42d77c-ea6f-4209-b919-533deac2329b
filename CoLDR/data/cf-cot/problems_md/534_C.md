# Polycarpus' Dice

**题目ID**: 534/C  
**比赛**: Codeforces Round 298 (Div. 2)  
**年份**: 2015  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON><PERSON><PERSON><PERSON> has n dice d1, d2, ..., dn. The i-th dice shows numbers from 1 to di. <PERSON><PERSON><PERSON><PERSON> rolled all the dice and the sum of numbers they showed is A<PERSON> <PERSON><PERSON><PERSON><PERSON> didn't see which dice showed what number, she knows only the sum A and the values d1, d2, ..., dn. However, she finds it enough to make a series of statements of the following type: dice i couldn't show number r. For example, if <PERSON><PERSON><PERSON><PERSON> had two six-faced dice and the total sum is A = 11, then <PERSON><PERSON><PERSON><PERSON> can state that each of the two dice couldn't show a value less than five (otherwise, the remaining dice must have a value of at least seven, which is impossible).

For each dice find the number of values for which it can be guaranteed that the dice couldn't show these values if the sum of the shown values is A.

## 输入格式

The first line contains two integers n, A (1 ≤ n ≤ 2·105, n ≤ A ≤ s) — the number of dice and the sum of shown values where s = d1 + d2 + ... + dn.

The second line contains n integers d1, d2, ..., dn (1 ≤ di ≤ 106), where di is the maximum value that the i-th dice can show.

## 输出格式

Print n integers b1, b2, ..., bn, where bi is the number of values for which it is guaranteed that the i-th dice couldn't show them.

## 样例

### 样例 1

**输入**:
```
2 8
4 4
```

**输出**:
```
3 3
```

### 样例 2

**输入**:
```
1 3
5
```

**输出**:
```
4
```

### 样例 3

**输入**:
```
2 3
2 3
```

**输出**:
```
0 1
```

## 备注

In the first sample from the statement A equal to 8 could be obtained in the only case when both the first and the second dice show 4. Correspondingly, both dice couldn't show values 1, 2 or 3.

In the second sample from the statement A equal to 3 could be obtained when the single dice shows 3. Correspondingly, it couldn't show 1, 2, 4 or 5.

In the third sample from the statement A equal to 3 could be obtained when one dice shows 1 and the other dice shows 2. That's why the first dice doesn't have any values it couldn't show and the second dice couldn't show 3.

