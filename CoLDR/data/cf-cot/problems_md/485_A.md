# Factory

**题目ID**: 485/A  
**比赛**: Codeforces Round 276 (Div. 2)  
**年份**: 2014  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

One industrial factory is reforming working plan. The director suggested to set a mythical detail production norm. If at the beginning of the day there were x details in the factory storage, then by the end of the day the factory has to produce $$x \mod m$$ (remainder after dividing x by m) more details. Unfortunately, no customer has ever bought any mythical detail, so all the details produced stay on the factory.

The board of directors are worried that the production by the given plan may eventually stop (that means that there will be а moment when the current number of details on the factory is divisible by m).

Given the number of details a on the first day and number m check if the production stops at some moment.

## 输入格式

The first line contains two integers a and m (1 ≤ a, m ≤ 105).

## 输出格式

Print "Yes" (without quotes) if the production will eventually stop, otherwise print "No".

## 样例

### 样例 1

**输入**:
```
1 5
```

**输出**:
```
No
```

### 样例 2

**输入**:
```
3 6
```

**输出**:
```
Yes
```

