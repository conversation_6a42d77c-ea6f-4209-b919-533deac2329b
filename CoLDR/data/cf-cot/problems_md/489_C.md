# Given Length and Sum of Digits...

**题目ID**: 489/C  
**比赛**: Codeforces Round 277.5 (Div. 2)  
**年份**: 2014  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

You have a positive integer m and a non-negative integer s. Your task is to find the smallest and the largest of the numbers that have length m and sum of digits s. The required numbers should be non-negative integers written in the decimal base without leading zeroes.

## 输入格式

The single line of the input contains a pair of integers m, s (1 ≤ m ≤ 100, 0 ≤ s ≤ 900) — the length and the sum of the digits of the required numbers.

## 输出格式

In the output print the pair of the required non-negative integer numbers — first the minimum possible number, then — the maximum possible number. If no numbers satisfying conditions required exist, print the pair of numbers "-1 -1" (without the quotes).

## 样例

### 样例 1

**输入**:
```
2 15
```

**输出**:
```
69 96
```

### 样例 2

**输入**:
```
3 0
```

**输出**:
```
-1 -1
```

