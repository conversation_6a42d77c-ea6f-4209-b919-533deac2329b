# Om Nom and <PERSON> Park

**题目ID**: 526/B  
**比赛**: ZeptoLab Code Rush 2015  
**年份**: 2015  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON><PERSON> is the main character of a game "Cut the Rope". He is a bright little monster who likes visiting friends living at the other side of the park. However the dark old parks can scare even somebody as fearless as <PERSON><PERSON>, so he asks you to help him.

The park consists of 2n + 1 - 1 squares connected by roads so that the scheme of the park is a full binary tree of depth n. More formally, the entrance to the park is located at the square 1. The exits out of the park are located at squares 2n, 2n + 1, ..., 2n + 1 - 1 and these exits lead straight to the Om Nom friends' houses. From each square i (2 ≤ i < 2n + 1) there is a road to the square $$\frac{i}{2}$$. Thus, it is possible to go from the park entrance to each of the exits by walking along exactly n roads.

Om <PERSON>m loves counting lights on the way to his friend. <PERSON><PERSON>m is afraid of spiders who live in the park, so he doesn't like to walk along roads that are not enough lit. What he wants is that the way to any of his friends should have in total the same number of lights. That will make him feel safe.

He asked you to help him install additional lights. Determine what minimum number of lights it is needed to additionally place on the park roads so that a path from the entrance to any exit of the park contains the same number of street lights. You may add an arbitrary number of street lights to each of the roads.

## 输入格式

The first line contains integer n (1 ≤ n ≤ 10) — the number of roads on the path from the entrance to any exit.

The next line contains 2n + 1 - 2 numbers a2, a3, ... a2n + 1 - 1 — the initial numbers of street lights on each road of the park. Here ai is the number of street lights on the road between squares i and $$\frac{i}{2}$$. All numbers ai are positive integers, not exceeding 100.

## 输出格式

Print the minimum number of street lights that we should add to the roads of the park to make Om Nom feel safe.

## 样例

### 样例 1

**输入**:
```
2
1 2 3 4 5 6
```

**输出**:
```
5
```

## 备注

Picture for the sample test. Green color denotes the additional street lights.

