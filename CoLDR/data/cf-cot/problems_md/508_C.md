# <PERSON> and Ghosts

**题目ID**: 508/C  
**比赛**: Codeforces Round 288 (Div. 2)  
**年份**: 2015  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON> loves to watch horror movies. In the best traditions of horror, she will be visited by m ghosts tonight. <PERSON> has lots of candles prepared for the visits, each candle can produce light for exactly t seconds. It takes the girl one second to light one candle. More formally, <PERSON> can spend one second to light one candle, then this candle burns for exactly t seconds and then goes out and can no longer be used.

For each of the m ghosts Anya knows the time at which it comes: the i-th visit will happen wi seconds after midnight, all wi's are distinct. Each visit lasts exactly one second.

What is the minimum number of candles Anya should use so that during each visit, at least r candles are burning? Anya can start to light a candle at any time that is integer number of seconds from midnight, possibly, at the time before midnight. That means, she can start to light a candle integer number of seconds before midnight or integer number of seconds after a midnight, or in other words in any integer moment of time.

## 输入格式

The first line contains three integers m, t, r (1 ≤ m, t, r ≤ 300), representing the number of ghosts to visit <PERSON>, the duration of a candle's burning and the minimum number of candles that should burn during each visit.

The next line contains m space-separated numbers wi (1 ≤ i ≤ m, 1 ≤ wi ≤ 300), the i-th of them repesents at what second after the midnight the i-th ghost will come. All wi's are distinct, they follow in the strictly increasing order.

## 输出格式

If it is possible to make at least r candles burn during each visit, then print the minimum number of candles that Anya needs to light for that.

If that is impossible, print  - 1.

## 样例

### 样例 1

**输入**:
```
1 8 3
10
```

**输出**:
```
3
```

### 样例 2

**输入**:
```
2 10 1
5 8
```

**输出**:
```
1
```

### 样例 3

**输入**:
```
1 1 3
10
```

**输出**:
```
-1
```

## 备注

Anya can start lighting a candle in the same second with ghost visit. But this candle isn't counted as burning at this visit.

It takes exactly one second to light up a candle and only after that second this candle is considered burning; it means that if Anya starts lighting candle at moment x, candle is buring from second x + 1 to second x + t inclusively.

In the first sample test three candles are enough. For example, Anya can start lighting them at the 3-rd, 5-th and 7-th seconds after the midnight.

In the second sample test one candle is enough. For example, Anya can start lighting it one second before the midnight.

In the third sample test the answer is  - 1, since during each second at most one candle can burn but Anya needs three candles to light up the room at the moment when the ghost comes.

