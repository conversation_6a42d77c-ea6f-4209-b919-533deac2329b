# Diophantine equation

**题目ID**: 530/C  
**比赛**: VK Cup 2015 - Wild Card Round 1  
**年份**: 2015  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

You are given an equation A * X + B * Y = C, A, B, C are positive integer coefficients, X and Y are variables which can have positive integer values only. Output the number of solutions of this equation and the solutions themselves.

## 输入格式

The only line of input contains integers A, B and C (1 ≤ A, B, C ≤ 1000), separated with spaces.

## 输出格式

In the first line of the output print the number of the solutions N. In the next N lines print the solutions, formatted as "XY", sorted in ascending order of X, one solution per line.

## 样例

### 样例 1

**输入**:
```
3 5 35
```

**输出**:
```
2
5 4
10 1
```

### 样例 2

**输入**:
```
3 35 5
```

**输出**:
```
0
```

