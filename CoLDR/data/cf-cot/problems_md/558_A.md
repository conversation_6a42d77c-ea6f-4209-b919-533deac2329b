# Lala Land and Apple Trees

**题目ID**: 558/A  
**比赛**: Codeforces Round 312 (Div. 2)  
**年份**: 2015  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON><PERSON> lives in Lala Land. Lala Land is a very beautiful country that is located on a coordinate line. Lala Land is famous with its apple trees growing everywhere.

Lala Land has exactly n apple trees. Tree number i is located in a position xi and has ai apples growing on it. <PERSON><PERSON> wants to collect apples from the apple trees. <PERSON><PERSON> currently stands in x = 0 position. At the beginning, he can choose whether to go right or left. He'll continue in his direction until he meets an apple tree he didn't visit before. He'll take all of its apples and then reverse his direction, continue walking in this direction until he meets another apple tree he didn't visit before and so on. In the other words, <PERSON><PERSON> reverses his direction when visiting each new apple tree. <PERSON><PERSON> will stop collecting apples when there are no more trees he didn't visit in the direction he is facing.

What is the maximum number of apples he can collect?

## 输入格式

The first line contains one number n (1 ≤ n ≤ 100), the number of apple trees in Lala Land.

The following n lines contains two integers each xi, ai ( - 105 ≤ xi ≤ 105, xi ≠ 0, 1 ≤ ai ≤ 105), representing the position of the i-th tree and number of apples on it.

It's guaranteed that there is at most one apple tree at each coordinate. It's guaranteed that no tree grows in point 0.

## 输出格式

Output the maximum number of apples Amr can collect.

## 样例

### 样例 1

**输入**:
```
2
-1 5
1 5
```

**输出**:
```
10
```

### 样例 2

**输入**:
```
3
-2 2
1 4
-1 3
```

**输出**:
```
9
```

### 样例 3

**输入**:
```
3
1 9
3 5
7 10
```

**输出**:
```
9
```

## 备注

In the first sample test it doesn't matter if Amr chose at first to go left or right. In both cases he'll get all the apples.

In the second sample test the optimal solution is to go left to x =  - 1, collect apples from there, then the direction will be reversed, Amr has to go to x = 1, collect apples from there, then the direction will be reversed and Amr goes to the final tree x =  - 2.

In the third sample test the optimal solution is to go right to x = 1, collect apples from there, then the direction will be reversed and Amr will not be able to collect anymore apples because there are no apple trees to his left.

