# Points in triangle

**题目ID**: 530/H  
**比赛**: VK Cup 2015 - Wild Card Round 1  
**年份**: 2015  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

You are given a set of points on a plane with positive integer coordinates. Find a triangle of minimum area with vertices in points (0, 0), (A, 0) and (0, B) (A and B are unknown positive integers) that contains all the given points inside it (points on the edges count towards being inside).

## 输入格式

The first line of the input contains an integer N (1 ≤ N ≤ 100) — the number of points. The following N lines contain pairs of numbers X and Y (1 ≤ X, Y ≤ 100) - the coordinates of the points. All points are distinct.

## 输出格式

Output one floating-point number — the minimal area of the triangle. The answer is considered to be correct if its absolute or relative error does not exceed 10 - 4.

## 样例

### 样例 1

**输入**:
```
2
1 1
1 3
```

**输出**:
```
6.0
```

### 样例 2

**输入**:
```
2
2 1
1 2
```

**输出**:
```
4.5
```

