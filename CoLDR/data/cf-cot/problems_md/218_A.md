# Mountain Scenery

**题目ID**: 218/A  
**比赛**: Codeforces Round 134 (Div. 2)  
**年份**: 2012  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

Little <PERSON><PERSON><PERSON> has found a picture with n mountain peaks painted on it. The n painted peaks are represented by a non-closed polyline, consisting of 2n segments. The segments go through 2n + 1 points with coordinates (1, y1), (2, y2), ..., (2n + 1, y2n + 1), with the i-th segment connecting the point (i, yi) and the point (i + 1, yi + 1). For any even i (2 ≤ i ≤ 2n) the following condition holds: yi - 1 < yi and yi > yi + 1.

We shall call a vertex of a polyline with an even x coordinate a mountain peak.

The figure to the left shows the initial picture, the figure to the right shows what the picture looks like after <PERSON><PERSON><PERSON>'s actions. The affected peaks are marked red, k = 2.

<PERSON><PERSON><PERSON> fancied a little mischief. He chose exactly k mountain peaks, rubbed out the segments that went through those peaks and increased each peak's height by one (that is, he increased the y coordinate of the corresponding points). Then he painted the missing segments to get a new picture of mountain peaks. Let us denote the points through which the new polyline passes on <PERSON><PERSON><PERSON>'s new picture as (1, r1), (2, r2), ..., (2n + 1, r2n + 1).

Given Bolek's final picture, restore the initial one.

## 输入格式

The first line contains two space-separated integers n and k (1 ≤ k ≤ n ≤ 100). The next line contains 2n + 1 space-separated integers r1, r2, ..., r2n + 1 (0 ≤ ri ≤ 100) — the y coordinates of the polyline vertices on Bolek's picture.

It is guaranteed that we can obtain the given picture after performing the described actions on some picture of mountain peaks.

## 输出格式

Print 2n + 1 integers y1, y2, ..., y2n + 1 — the y coordinates of the vertices of the polyline on the initial picture. If there are multiple answers, output any one of them.

## 样例

### 样例 1

**输入**:
```
3 2
0 5 3 5 1 5 2
```

**输出**:
```
0 5 3 4 1 4 2
```

### 样例 2

**输入**:
```
1 1
0 2 0
```

**输出**:
```
0 1 0
```

