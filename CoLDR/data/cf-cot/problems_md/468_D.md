# Tree

**题目ID**: 468/D  
**比赛**: Codeforces Round 268 (Div. 1)  
**年份**: 2014  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

Little X has a tree consisting of n nodes (they are numbered from 1 to n). Each edge of the tree has a positive length. Let's define the distance between two nodes v and u (we'll denote it d(v, u)) as the sum of the lengths of edges in the shortest path between v and u.

A permutation p is a sequence of n distinct integers p1, p2, ..., pn (1 ≤ pi ≤ n). Little <PERSON> wants to find a permutation p such that sum $$\sum_{i=1}^{n} d(i,p_i)$$ is maximal possible. If there are multiple optimal permutations, he wants to find the lexicographically smallest one. Help him with the task!

## 输入格式

The first line contains an integer n (1 ≤ n ≤ 105).

Each of the next n - 1 lines contains three space separated integers ui,  vi, wi (1 ≤  ui,  vi ≤  n; 1 ≤  wi ≤  105), denoting an edge between nodes ui and vi with length equal to wi.

It is guaranteed that these edges form a tree.

## 输出格式

In the first line print the maximum possible value of the described sum. In the second line print n integers, representing the lexicographically smallest permutation.

## 样例

### 样例 1

**输入**:
```
2
1 2 3
```

**输出**:
```
6
2 1
```

### 样例 2

**输入**:
```
5
1 2 2
1 3 3
2 4 4
2 5 5
```

**输出**:
```
32
2 1 4 5 3
```

