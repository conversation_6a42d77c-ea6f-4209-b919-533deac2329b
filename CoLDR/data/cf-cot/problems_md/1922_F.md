# Replace on Segment

**题目ID**: 1922/F  
**比赛**: Educational Codeforces Round 161 (Rated for Div. 2)  
**年份**: 2024  
**时间限制**: 3.0秒  
**内存限制**: 256MB  

## 题目描述

You are given an array $$$a_1, a_2, \dots, a_n$$$, where each element is an integer from $$$1$$$ to $$$x$$$.

You can perform the following operation with it any number of times:

- choose three integers $$$l$$$, $$$r$$$ and $$$k$$$ such that $$$1 \le l \le r \le n$$$, $$$1 \le k \le x$$$ and each element $$$a_i$$$ such that $$$l \le i \le r$$$ is different from $$$k$$$. Then, for each $$$i \in [l, r]$$$, replace $$$a_i$$$ with $$$k$$$.

In other words, you choose a subsegment of the array and an integer from $$$1$$$ to $$$x$$$ which does not appear in that subsegment, and replace every element in the subsegment with that chosen integer.

Your goal is to make all elements in the array equal. What is the minimum number of operations that you have to perform?

## 输入格式

The first line contains one integer $$$t$$$ ($$$1 \le t \le 100$$$) — the number of test cases.

Each test case consists of two lines:

- the first line contains two integers $$$n$$$ and $$$x$$$ ($$$1 \le x \le n \le 100$$$);
- the second line contains $$$n$$$ integers $$$a_1, a_2, \dots, a_n$$$ ($$$1 \le a_i \le x$$$).

Additional constraint on the input: the sum of $$$n$$$ over all test cases does not exceed $$$500$$$.

## 输出格式

For each test case, print one integer — the minimum number of operations you have to perform.

## 样例

### 样例 1

**输入**:
```
3
3 2
1 2 1
6 3
1 2 3 1 2 3
12 3
3 1 3 1 2 1 1 2 3 1 1 3
```

**输出**:
```
1
2
2
```

## 题解

First of all, we claim the following: if you apply an operation on a segment, you may treat the resulting segment as one element (i. e. we can "merge" the elements affected by an operation into one). This is quite intuitive, but the formal proof is kinda long, so if you're not interested in it, feel free to skip the next paragraphs written in italic.
Formal proof: suppose we merged several adjacent equal elements into one. Let's show that it doesn't change the answer for the array. Let the array before merging a segment of adjacent equal elements be $$$a$$$, and the array after merging be $$$a'$$$. We will show that $$$f(a) = f(a')$$$, where $$$f(x)$$$ is the minimum number of operations to solve the problem on the array $$$x$$$.
• $$$f(a) \ge f(a')$$$: suppose we built a sequence of operations that turns all elements of $$$a$$$ equal. Consider the segment of adjacent equal elements we merged to get $$$a'$$$. Let's discard all elements of that segment, except for the first one, from all operations in the sequence, and remove all operations which now affect zero elements. We will get a valid sequence of operations that turns all elements of $$$a'$$$ equal. So, any valid sequence of operations for $$$a$$$ can be transformed into a valid sequence of operations for $$$a'$$$ (with possibly discarding some operations), and that's why $$$f(a) \ge f(a')$$$;
• $$$f(a) \le f(a')$$$: suppose we built a sequence of operations that turns all elements of $$$a'$$$ equal. It can be transformed into a valid sequence of operations for $$$a'$$$, if we "expand" the element we got from merging the segment in all operations. So, $$$f(a) \le f(a')$$$;
• since $$$f(a) \ge f(a')$$$ and $$$f(a) \le f(a')$$$, then $$$f(a) = f(a')$$$.
This means that after you've done an operation on a segment, the next operations will either affect that whole segment, or not affect any element from the segment at all.
This allows us to use the following dynamic programming idea: let $$$dp[l][r][k]$$$ be the minimum number of operations required to turn all elements on the segment $$$[l, r]$$$ into $$$k$$$. If we want to transform all elements into $$$k$$$, then there are two options:
• either the last operation will turn the whole segment into $$$k$$$, so we need to calculate the number of operations required to get rid of all elements equal to $$$k$$$ from the segment;
• or the segment $$$[l, r]$$$ can be split into several segments which we will turn into $$$k$$$ separately.
The second option can be modeled quite easily: we iterate on the splitting point between two parts $$$i$$$, and update $$$dp[l][r][k]$$$ with $$$dp[l][i][k] + dp[i+1][r][k]$$$. However, the first option is a bit more complicated.
Let's introduce a second dynamic programming to our solution: let $$$dp2[l][r][k]$$$ be the minimum number of operations to remove all occurrences of $$$k$$$ from the segment $$$[l, r]$$$. Then, the first option for computing $$$dp[l][r][k]$$$ can be implemented by simply updating $$$dp[l][r][k]$$$ with $$$dp2[l][r][k] + 1$$$.
Now, let's show how to calculate $$$dp2[l][r][k]$$$. It's quite similar to the first dynamic programming:
• either the last operation on the segment will turn the whole segment into some other element $$$m$$$, so we can iterate on it and update $$$dp2[l][r][k]$$$ with $$$dp[l][r][m]$$$;
• or the segment $$$[l, r]$$$ can be split into two parts, and we will get rid of elements equal to $$$k$$$ from these parts separately (so we update $$$dp2[l][r][k]$$$ with $$$dp2[l][i][k] + dp2[i+1][r][k]$$$).
Okay, it looks like we got a solution working in $$$O(n^4)$$$. There's just one problem, though. There are cyclic dependencies in our dynamic programming:
• $$$dp[l][r][k]$$$ depends on $$$dp2[l][r][k]$$$;
• $$$dp2[l][r][k]$$$ depends on $$$dp[l][r][m]$$$, where $$$m \ne k$$$;
• $$$dp[l][r][m]$$$ depends on $$$dp2[l][r][m]$$$;
• $$$dp2[l][r][m]$$$ depends on $$$dp[l][r][k]$$$.
We have to either somehow deal with them, or get rid of them. The model solution eliminates these cyclic dependencies as follows: when we need to calculate $$$dp[l][r][k]$$$, let's discard all elements equal to $$$k$$$ from the ends of the segment (i. e. move $$$l$$$ to $$$l'$$$ and $$$r$$$ to $$$r'$$$, where $$$l'$$$ and $$$r'$$$ are the first and last occurrences of elements not equal to $$$k$$$). Similarly, when we need to calculate $$$dp2[l][r][k]$$$, let's discard all elements not equal to $$$k$$$ from the ends of the segment. It's quite easy to show that these operations won't make the answer worse (if you remove an element from an array, the minimum number of operations to "fix" the array doesn't increase). It's also not that hard to show that this method gets rid of all cyclic dependencies: if we consider the cyclic dependency we described earlier, we can see that the element $$$a_l$$$ will be discarded from the segment either when computing $$$dp[l][r][k]$$$ (if $$$a_l = k$$$) or when computing $$$dp2[l][r][k]$$$ (if $$$a_l \ne k$$$).
That way, we get a dynamic programming solution working in $$$O(n^4)$$$.

