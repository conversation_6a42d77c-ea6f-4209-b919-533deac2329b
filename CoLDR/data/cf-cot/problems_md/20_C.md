# Dijkstra?

**题目ID**: 20/C  
**比赛**: Codeforces Alpha Round 20 (Codeforces format)  
**年份**: 2010  
**时间限制**: 1.0秒  
**内存限制**: 64MB  

## 题目描述

You are given a weighted undirected graph. The vertices are enumerated from 1 to n. Your task is to find the shortest path between the vertex 1 and the vertex n.

## 输入格式

The first line contains two integers n and m (2 ≤ n ≤ 105, 0 ≤ m ≤ 105), where n is the number of vertices and m is the number of edges. Following m lines contain one edge each in form ai, bi and wi (1 ≤ ai, bi ≤ n, 1 ≤ wi ≤ 106), where ai, bi are edge endpoints and wi is the length of the edge.

It is possible that the graph has loops and multiple edges between pair of vertices.

## 输出格式

Write the only integer -1 in case of no path. Write the shortest path in opposite case. If there are many solutions, print any of them.

## 样例

### 样例 1

**输入**:
```
5 6
1 2 2
2 5 5
2 3 4
1 4 1
4 3 3
3 5 1
```

**输出**:
```
1 4 3 5
```

### 样例 2

**输入**:
```
5 6
1 2 2
2 5 5
2 3 4
1 4 1
4 3 3
3 5 1
```

**输出**:
```
1 4 3 5
```

