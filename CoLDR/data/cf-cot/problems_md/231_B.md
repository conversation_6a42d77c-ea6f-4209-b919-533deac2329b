# Magic, Wizardry and Wonders

**题目ID**: 231/B  
**比赛**: Codeforces Round 143 (Div. 2)  
**年份**: 2012  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON><PERSON><PERSON> the Great Magician and Conjurer loves all kinds of miracles and wizardry. In one wave of a magic wand he can turn an object into something else. But, as you all know, there is no better magic in the Universe than the magic of numbers. That's why <PERSON><PERSON><PERSON> adores math and spends a lot of time turning some numbers into some other ones.

This morning he has n cards with integers lined up in front of him. Each integer is not less than 1, but not greater than l. When <PERSON><PERSON><PERSON> waves his magic wand, two rightmost cards vanish from the line and a new card magically appears in their place. It contains the difference between the left and the right numbers on the two vanished cards. <PERSON><PERSON><PERSON> was very interested to know what would happen next, and so he waved with his magic wand on and on, until the table had a single card left.

Suppose that <PERSON><PERSON><PERSON> originally had the following cards: 4, 1, 1, 3 (listed from left to right). Then after the first wave the line would be: 4, 1, -2, and after the second one: 4, 3, and after the third one the table would have a single card with number 1.

Please note that in spite of the fact that initially all the numbers on the cards were not less than 1 and not greater than l, the numbers on the appearing cards can be anything, no restrictions are imposed on them.

It is now evening. <PERSON>asya is very tired and wants to return everything back, but does not remember which cards he had in the morning. He only remembers that there were n cards, they contained integers from 1 to l, and after all magical actions he was left with a single card containing number d.

Help Vasya to recover the initial set of cards with numbers.

## 输入格式

The single line contains three space-separated integers: n (2 ≤ n ≤ 100) — the initial number of cards on the table, d (|d| ≤ 104) — the number on the card that was left on the table after all the magical actions, and l (1 ≤ l ≤ 100) — the limits for the initial integers.

## 输出格式

If Vasya is mistaken, that is, if there doesn't exist a set that meets the requirements given in the statement, then print a single number -1, otherwise print the sought set containing n integers from 1 to l. Separate the integers by spaces. Print the integers in the order, in which they were written on the cards from left to right. If there are several suitable sets of numbers, you can print any of them.

## 样例

### 样例 1

**输入**:
```
3 3 2
```

**输出**:
```
2 1 2
```

### 样例 2

**输入**:
```
5 -4 3
```

**输出**:
```
-1
```

### 样例 3

**输入**:
```
5 -4 4
```

**输出**:
```
2 4 1 4 1
```

