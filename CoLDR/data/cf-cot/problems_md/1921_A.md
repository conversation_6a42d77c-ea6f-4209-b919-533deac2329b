# Square

**题目ID**: 1921/A  
**比赛**: Codeforces Round 920 (Div. 3)  
**年份**: 2024  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

A square of positive (strictly greater than $$$0$$$) area is located on the coordinate plane, with sides parallel to the coordinate axes. You are given the coordinates of its corners, in random order. Your task is to find the area of the square.

## 输入格式

Each test consists of several testcases. The first line contains one integer $$$t$$$ ($$$1 \le t \le 100$$$) — the number of testcases. The following is a description of the testcases.

Each testcase contains four lines, each line contains two integers $$$x_i, y_i$$$ ($$$-1000\le x_i, y_i\le 1000$$$), coordinates of the corners of the square.

It is guaranteed that there is a square with sides parallel to the coordinate axes, with positive (strictly greater than $$$0$$$) area, with corners in given points.

## 输出格式

For each test case, print a single integer, the area of the square.

## 样例

### 样例 1

**输入**:
```
3
1 2
4 5
1 5
4 2
-1 1
1 -1
1 1
-1 -1
45 11
45 39
17 11
17 39
```

**输出**:
```
9
4
784
```

## 题解

There are many ways to solve this problem, the simplest way is as follows. Let's find the minimum and maximum coordinate $$$x$$$ among all the corners of the square. The difference of these coordinates will give us the length of the square side $$$d = x_{max} - x_{min}$$$. After that, we can calculate the area of the square as $$$s = d^2$$$.

