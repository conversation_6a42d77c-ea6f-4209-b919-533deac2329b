# Contest

**题目ID**: 501/A  
**比赛**: Codeforces Round 285 (Div. 2)  
**年份**: 2015  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON><PERSON> and <PERSON><PERSON><PERSON> participated in a Codeforces contest. Unfortunately, each of them solved only one problem, though successfully submitted it at the first attempt. <PERSON><PERSON> solved the problem that costs a points and <PERSON><PERSON><PERSON> solved the problem that costs b points. Besides, <PERSON><PERSON> submitted the problem c minutes after the contest started and <PERSON><PERSON><PERSON> submitted the problem d minutes after the contest started. As you know, on Codeforces the cost of a problem reduces as a round continues. That is, if you submit a problem that costs p points t minutes after the contest started, you get $$\max \left( \frac{3p}{10}, p - \frac{p}{250} \times t \right)$$ points.

<PERSON><PERSON> and <PERSON><PERSON><PERSON> are having an argument trying to find out who got more points. Help them to find out the truth.

## 输入格式

The first line contains four integers a, b, c, d (250 ≤ a, b ≤ 3500, 0 ≤ c, d ≤ 180).

It is guaranteed that numbers a and b are divisible by 250 (just like on any real Codeforces round).

## 输出格式

Output on a single line:

"<PERSON><PERSON>" (without the quotes), if <PERSON><PERSON> got more points than <PERSON><PERSON><PERSON>.

"<PERSON><PERSON>ya" (without the quotes), if <PERSON><PERSON><PERSON> got more points than <PERSON><PERSON>.

"Tie" (without the quotes), if both of them got the same number of points.

## 样例

### 样例 1

**输入**:
```
500 1000 20 30
```

**输出**:
```
Vasya
```

### 样例 2

**输入**:
```
1000 1000 1 1
```

**输出**:
```
Tie
```

### 样例 3

**输入**:
```
1500 1000 176 177
```

**输出**:
```
Misha
```

