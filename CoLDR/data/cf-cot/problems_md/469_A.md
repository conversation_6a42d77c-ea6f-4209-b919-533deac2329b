# I Wanna Be the Guy

**题目ID**: 469/A  
**比赛**: Codeforces Round 268 (Div. 2)  
**年份**: 2014  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

There is a game called "I Wanna Be the Guy", consisting of n levels. <PERSON> and his friend <PERSON> are addicted to the game. Each of them wants to pass the whole game.

Little X can pass only p levels of the game. And Little Y can pass only q levels of the game. You are given the indices of levels Little X can pass and the indices of levels Little Y can pass. Will Little X and <PERSON> Y pass the whole game, if they cooperate each other?

## 输入格式

The first line contains a single integer n (1 ≤  n ≤ 100).

The next line contains an integer p (0 ≤ p ≤ n) at first, then follows p distinct integers a1, a2, ..., ap (1 ≤ ai ≤ n). These integers denote the indices of levels Little X can pass. The next line contains the levels Little Y can pass in the same format. It's assumed that levels are numbered from 1 to n.

## 输出格式

If they can pass all the levels, print "I become the guy.". If it's impossible, print "Oh, my keyboard!" (without the quotes).

## 样例

### 样例 1

**输入**:
```
4
3 1 2 3
2 2 4
```

**输出**:
```
I become the guy.
```

### 样例 2

**输入**:
```
4
3 1 2 3
2 2 3
```

**输出**:
```
Oh, my keyboard!
```

## 备注

In the first sample, Little X can pass levels [1 2 3], and Little Y can pass level [2 4], so they can pass all the levels both.

In the second sample, no one can pass level 4.

