# Vitaly and Strings

**题目ID**: 518/A  
**比赛**: Codeforces Round 293 (Div. 2)  
**年份**: 2015  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON><PERSON> is a diligent student who never missed a lesson in his five years of studying in the university. He always does his homework on time and passes his exams in time.

During the last lesson the teacher has provided two strings s and t to <PERSON><PERSON>. The strings have the same length, they consist of lowercase English letters, string s is lexicographically smaller than string t. <PERSON><PERSON> wondered if there is such string that is lexicographically larger than string s and at the same is lexicographically smaller than string t. This string should also consist of lowercase English letters and have the length equal to the lengths of strings s and t.

Let's help Vitaly solve this easy problem!

## 输入格式

The first line contains string s (1 ≤ |s| ≤ 100), consisting of lowercase English letters. Here, |s| denotes the length of the string.

The second line contains string t (|t| = |s|), consisting of lowercase English letters.

It is guaranteed that the lengths of strings s and t are the same and string s is lexicographically less than string t.

## 输出格式

If the string that meets the given requirements doesn't exist, print a single string "No such string" (without the quotes).

If such string exists, print it. If there are multiple valid strings, you may print any of them.

## 样例

### 样例 1

**输入**:
```
a
c
```

**输出**:
```
b
```

### 样例 2

**输入**:
```
aaa
zzz
```

**输出**:
```
kkk
```

### 样例 3

**输入**:
```
abcdefg
abcdefh
```

**输出**:
```
No such string
```

## 备注

String s = s1s2... sn is said to be lexicographically smaller than t = t1t2... tn, if there exists such i, that s1 = t1, s2 = t2, ... si - 1 = ti - 1, si < ti.

