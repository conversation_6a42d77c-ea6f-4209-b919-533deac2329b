# Infinite Inversions

**题目ID**: 540/E  
**比赛**: Codeforces Round 301 (Div. 2)  
**年份**: 2015  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

There is an infinite sequence consisting of all positive integers in the increasing order: p = {1, 2, 3, ...}. We performed n swap operations with this sequence. A swap(a, b) is an operation of swapping the elements of the sequence on positions a and b. Your task is to find the number of inversions in the resulting sequence, i.e. the number of such index pairs (i, j), that i < j and pi > pj.

## 输入格式

The first line contains a single integer n (1 ≤ n ≤ 105) — the number of swap operations applied to the sequence.

Each of the next n lines contains two integers ai and bi (1 ≤ ai, bi ≤ 109, ai ≠ bi) — the arguments of the swap operation.

## 输出格式

Print a single integer — the number of inversions in the resulting sequence.

## 样例

### 样例 1

**输入**:
```
2
4 2
1 4
```

**输出**:
```
4
```

### 样例 2

**输入**:
```
3
1 6
3 4
2 5
```

**输出**:
```
15
```

## 备注

In the first sample the sequence is being modified as follows: $$\{1,2,3,4,5,\ldots\} \rightarrow \{1,4,3,2,5,\ldots\} \rightarrow \{2,4,3,1,5.\ldots\}$$. It has 4 inversions formed by index pairs (1, 4), (2, 3), (2, 4) and (3, 4).

