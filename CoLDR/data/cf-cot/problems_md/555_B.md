# Case of Fugitive

**题目ID**: 555/B  
**比赛**: Codeforces Round 310 (Div. 1)  
**年份**: 2015  
**时间限制**: 3.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON><PERSON> the Android is a galaxy-famous detective. He is now chasing a criminal hiding on the planet Oxa-5, the planet almost fully covered with water.

The only dry land there is an archipelago of n narrow islands located in a row. For more comfort let's represent them as non-intersecting segments on a straight line: island i has coordinates [li, ri], besides, ri < li + 1 for 1 ≤ i ≤ n - 1.

To reach the goal, <PERSON><PERSON> needs to place a bridge between each pair of adjacent islands. A bridge of length a can be placed between the i-th and the (i + 1)-th islads, if there are such coordinates of x and y, that li ≤ x ≤ ri, li + 1 ≤ y ≤ ri + 1 and y - x = a.

The detective was supplied with m bridges, each bridge can be used at most once. Help him determine whether the bridges he got are enough to connect each pair of adjacent islands.

## 输入格式

The first line contains integers n (2 ≤ n ≤ 2·105) and m (1 ≤ m ≤ 2·105) — the number of islands and bridges.

Next n lines each contain two integers li and ri (1 ≤ li ≤ ri ≤ 1018) — the coordinates of the island endpoints.

The last line contains m integer numbers a1, a2, ..., am (1 ≤ ai ≤ 1018) — the lengths of the bridges that <PERSON>id got.

## 输出格式

If it is impossible to place a bridge between each pair of adjacent islands in the required manner, print on a single line "No" (without the quotes), otherwise print in the first line "Yes" (without the quotes), and in the second line print n - 1 numbers b1, b2, ..., bn - 1, which mean that between islands i and i + 1 there must be used a bridge number bi.

If there are multiple correct answers, print any of them. Note that in this problem it is necessary to print "Yes" and "No" in correct case.

## 样例

### 样例 1

**输入**:
```
4 4
1 4
7 8
9 10
12 14
4 5 3 8
```

**输出**:
```
Yes
2 3 1
```

### 样例 2

**输入**:
```
2 2
11 14
17 18
2 9
```

**输出**:
```
No
```

### 样例 3

**输入**:
```
2 1
1 1
1000000000000000000 1000000000000000000
999999999999999999
```

**输出**:
```
Yes
1
```

## 备注

In the first sample test you can, for example, place the second bridge between points 3 and 8, place the third bridge between points 7 and 10 and place the first bridge between points 10 and 14.

In the second sample test the first bridge is too short and the second bridge is too long, so the solution doesn't exist.

## 题解

You can put a bridge between islands i and i + 1 iff its length lies in the segment [li + 1 - ri;ri + 1 - li]. Now we have a well-known problem: there is a set of segments and a set of points on a line and for every segment you need to choose a point that lies in this segment. Every point can be choosed only once. This problem can be solved with an easy greedy algorithm.

