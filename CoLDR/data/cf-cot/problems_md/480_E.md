# Parking Lot

**题目ID**: 480/E  
**比赛**: Codeforces Round 274 (Div. 1)  
**年份**: 2014  
**时间限制**: 3.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON><PERSON>'s been bored at work and he is killing the time by watching the parking lot at the office. The parking lot looks from above like an n × m table (a cell of the table corresponds to a single parking spot). Some spots in the parking lot are taken, others are empty.

<PERSON><PERSON> watches cars riding into the parking lot one by one. After a car settles down at the parking spot, <PERSON><PERSON> amuzes himself by counting what maximum square of empty spots (i.e. a square subtable) can be seen on the parking lot if we look at it from above. Also, he takes notes of the square's size (side length) in his notebook.

You task is: given the state of the parking lot at the initial moment of time and the information about where the arriving cars park, restore what <PERSON><PERSON> wrote in his notebook. It is midday, so nobody leaves the lot.

## 输入格式

The first line contains three integers n, m and k — the sizes of the parking lot and the number of arriving cars after <PERSON><PERSON> started his watch (1 ≤ n, m, k ≤ 2000). Each of the following n lines contains m characters 'X' and '.', where 'X' means a taken spot and '.' means an empty spot. Each of the next k lines contains a pair of integers xi, yi — the number of row and column of the spot the corresponding car takes (1 ≤ xi ≤ n, 1 ≤ yi ≤ m). It is guaranteed that this place was empty. You can assume that a car enters a parking lot only after the previous car successfully finds a spot.

## 输出格式

Print k integers — the length of the side of the maximum square of empty spots after the corresponding car has entered the parking lot.

## 样例

### 样例 1

**输入**:
```
7 8 4
........
X.....X.
........
........
.X......
........
........
1 5
6 4
3 5
4 6
```

**输出**:
```
5
4
4
3
```

