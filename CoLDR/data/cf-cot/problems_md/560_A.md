# Currency System in Geraldion

**题目ID**: 560/A  
**比赛**: Codeforces Round 313 (Div. 2)  
**年份**: 2015  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

A magic island Geraldion, where <PERSON> lives, has its own currency system. It uses banknotes of several values. But the problem is, the system is not perfect and sometimes it happens that Geraldionians cannot express a certain sum of money with any set of banknotes. Of course, they can use any number of banknotes of each value. Such sum is called unfortunate. <PERSON> wondered: what is the minimum unfortunate sum?

## 输入格式

The first line contains number n (1 ≤ n ≤ 1000) — the number of values of the banknotes that used in Geraldion.

The second line contains n distinct space-separated numbers a1, a2, ..., an (1 ≤ ai ≤ 106) — the values of the banknotes.

## 输出格式

Print a single line — the minimum unfortunate sum. If there are no unfortunate sums, print  - 1.

## 样例

### 样例 1

**输入**:
```
5
1 2 3 4 5
```

**输出**:
```
-1
```

