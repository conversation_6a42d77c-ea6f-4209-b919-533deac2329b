# Sign Posts

**题目ID**: 568/D  
**比赛**: Codeforces Round 315 (Div. 1)  
**年份**: 2015  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

One Khanate had a lot of roads and very little wood. Riding along the roads was inconvenient, because the roads did not have road signs indicating the direction to important cities.

The Han decided that it's time to fix the issue, and ordered to put signs on every road. The Minister of Transport has to do that, but he has only k signs. Help the minister to solve his problem, otherwise the poor guy can lose not only his position, but also his head.

More formally, every road in the Khanate is a line on the Oxy plane, given by an equation of the form Ax + By + C = 0 (A and B are not equal to 0 at the same time). You are required to determine whether you can put signs in at most k points so that each road had at least one sign installed.

## 输入格式

The input starts with two positive integers n, k (1 ≤ n ≤ 105, 1 ≤ k ≤ 5)

Next n lines contain three integers each, Ai, Bi, Ci, the coefficients of the equation that determines the road (|Ai|, |Bi|, |Ci| ≤ 105, Ai2 + Bi2 ≠ 0).

It is guaranteed that no two roads coincide.

## 输出格式

If there is no solution, print "NO" in the single line (without the quotes).

Otherwise, print in the first line "YES" (without the quotes).

In the second line print a single number m (m ≤ k) — the number of used signs. In the next m lines print the descriptions of their locations.

Description of a location of one sign is two integers v, u. If u and v are two distinct integers between 1 and n, we assume that sign is at the point of intersection of roads number v and u. If u =  - 1, and v is an integer between 1 and n, then the sign is on the road number v in the point not lying on any other road. In any other case the description of a sign will be assumed invalid and your answer will be considered incorrect. In case if v = u, or if v and u are the numbers of two non-intersecting roads, your answer will also be considered incorrect.

The roads are numbered starting from 1 in the order in which they follow in the input.

## 样例

### 样例 1

**输入**:
```
3 1
1 0 0
0 -1 0
7 -93 0
```

**输出**:
```
YES
1
1 2
```

### 样例 2

**输入**:
```
3 1
1 0 0
0 1 0
1 1 3
```

**输出**:
```
NO
```

### 样例 3

**输入**:
```
2 3
3 4 5
5 6 7
```

**输出**:
```
YES
2
1 -1
2 -1
```

## 备注

Note that you do not have to minimize m, but it shouldn't be more than k.

In the first test all three roads intersect at point (0,0).

In the second test all three roads form a triangle and there is no way to place one sign so that it would stand on all three roads at once.

## 题解

Предположим, что решение есть. Если n ≤ k, то мы можем на каждую дорогу поставить по столбу. Иначе рассмотрим любые k + 1 дорогу. По принципу Дирихле среди них найдутся две, для которых будет общий указатель. Переберём эту пару дорог, поставим столб на их пересечение, уберем дороги, которые тоже проходят через эту точку. Мы свели задачу к меньшему числу столбов. Таким рекурсивным перебором мы решим задачу (если решение существует).
Решение это работает за $$O(n \cdot \frac{k!(k+1)!}{2^k})$$. Если написать аккуратно, это может зайти.
Но это решение можно ускорить. Заметим, что если у нас есть точка, через которую проходит хотя бы k + 1 дорога, то мы обязаны поставить столб в эту точку. При достаточно больших n (у меня в решении отсечка n > 30k2) такая точка точно есть (если решение существует), причём её можно искать вероятностным алгоритмом. При условии, что решение существует, вероятность, что две произвольные дороги пересекаются в такой точке не меньше $${ \frac { 1 } { k ^ { * } } }$$, поэтому если попробовать 100 раз, то с вероятностью $$1 - 10^{-8}$$ такая точка найдется, и мы сможем уменьшить k.
Все проверки можно делать в целых числах.
Сложность — $${\cal O}(nk^{2}+k^{2}\cdot{\frac{k!(k+1)!}{2^{k}}})$$.

