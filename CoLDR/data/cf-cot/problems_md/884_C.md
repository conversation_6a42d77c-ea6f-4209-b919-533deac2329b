# Bertown Subway

**题目ID**: 884/C  
**比赛**: Educational Codeforces Round 31  
**年份**: 2017  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

The construction of subway in Bertown is almost finished! The President of Berland will visit this city soon to look at the new subway himself.

There are n stations in the subway. It was built according to the Bertown Transport Law:

1. For each station i there exists exactly one train that goes from this station. Its destination station is pi, possibly pi = i;
2. For each station i there exists exactly one station j such that pj = i.

The President will consider the convenience of subway after visiting it. The convenience is the number of ordered pairs (x, y) such that person can start at station x and, after taking some subway trains (possibly zero), arrive at station y (1 ≤ x, y ≤ n).

The mayor of Bertown thinks that if the subway is not convenient enough, then the President might consider installing a new mayor (and, of course, the current mayor doesn't want it to happen). Before President visits the city mayor has enough time to rebuild some paths of subway, thus changing the values of pi for not more than two subway stations. Of course, breaking the Bertown Transport Law is really bad, so the subway must be built according to the Law even after changes.

The mayor wants to do these changes in such a way that the convenience of the subway is maximized. Help him to calculate the maximum possible convenience he can get!

## 输入格式

The first line contains one integer number n (1 ≤ n ≤ 100000) — the number of stations.

The second line contains n integer numbers p1, p2, ..., pn (1 ≤ pi ≤ n) — the current structure of the subway. All these numbers are distinct.

## 输出格式

Print one number — the maximum possible value of convenience.

## 样例

### 样例 1

**输入**:
```
3
2 1 3
```

**输出**:
```
9
```

### 样例 2

**输入**:
```
5
1 5 4 3 2
```

**输出**:
```
17
```

## 备注

In the first example the mayor can change p2 to 3 and p3 to 1, so there will be 9 pairs: (1, 1), (1, 2), (1, 3), (2, 1), (2, 2), (2, 3), (3, 1), (3, 2), (3, 3).

In the second example the mayor can change p2 to 4 and p3 to 5.

## 题解

Let's notice that one swap can affect at most two cycles of this permutation. Moreover you can join two cycles into one with the length equal to the sums of lengths of initial ones.
The function we are going to maximize is f(a, b) = (a + b)2 - a2 - b2, where a and b are the lengths of the cycles we are joining together. f(a, b) = (a2 + 2ab + b2) - a2 - b2 = 2ab. Now its easily seen that the maximum is achived when joining two cycles with the greatest product of lengths. Finally they are the two longest cycles in permutation.
Overall complexity: O(n).

