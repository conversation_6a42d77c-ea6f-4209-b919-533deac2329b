# Data Center Drama

**题目ID**: 527/E  
**比赛**: Codeforces Round 296 (Div. 2)  
**年份**: 2015  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

The project of a data center of a Big Software Company consists of n computers connected by m cables. Simply speaking, each computer can be considered as a box with multiple cables going out of the box. Very Important Information is transmitted along each cable in one of the two directions. As the data center plan is not yet approved, it wasn't determined yet in which direction information will go along each cable. The cables are put so that each computer is connected with each one, perhaps through some other computers.

The person in charge of the cleaning the data center will be <PERSON>, the janitor. She loves to tie cables into bundles using cable ties. For some reasons, she groups the cables sticking out of a computer into groups of two, and if it isn't possible, then she gets furious and attacks the computer with the water from the bucket.

It should also be noted that due to the specific physical characteristics of the Very Important Information, it is strictly forbidden to connect in one bundle two cables where information flows in different directions.

The management of the data center wants to determine how to send information along each cable so that <PERSON> is able to group all the cables coming out of each computer into groups of two, observing the condition above. Since it may not be possible with the existing connections plan, you are allowed to add the minimum possible number of cables to the scheme, and then you need to determine the direction of the information flow for each cable (yes, sometimes data centers are designed based on the janitors' convenience...)

## 输入格式

The first line contains two numbers, n and m (1 ≤ n ≤ 100 000, 1 ≤ m ≤ 200 000) — the number of computers and the number of the already present cables, respectively.

Each of the next lines contains two numbers ai, bi (1 ≤ ai, bi ≤ n) — the indices of the computers connected by the i-th cable. The data centers often have a very complex structure, so a pair of computers may have more than one pair of cables between them and some cables may connect a computer with itself.

## 输出格式

In the first line print a single number p (p ≥ m) — the minimum number of cables in the final scheme.

In each of the next p lines print a pair of numbers ci, di (1 ≤ ci, di ≤ n), describing another cable. Such entry means that information will go along a certain cable in direction from ci to di.

Among the cables you printed there should be all the cables presented in the original plan in some of two possible directions. It is guaranteed that there is a solution where p doesn't exceed 500 000.

If there are several posible solutions with minimum possible value of p, print any of them.

## 样例

### 样例 1

**输入**:
```
4 6
1 2
2 3
3 4
4 1
1 3
1 3
```

**输出**:
```
6
1 2
3 4
1 4
3 2
1 3
1 3
```

### 样例 2

**输入**:
```
3 4
1 2
2 3
1 1
3 3
```

**输出**:
```
6
2 1
2 3
1 1
3 3
3 1
1 1
```

## 备注

Picture for the first sample test. The tied pairs of cables are shown going out from the same point.

Picture for the second test from the statement. The added cables are drawin in bold.

Alternative answer for the second sample test:

