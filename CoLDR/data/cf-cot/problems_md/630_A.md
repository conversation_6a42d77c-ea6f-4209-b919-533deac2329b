# Again Twenty Five!

**题目ID**: 630/A  
**比赛**: Experimental Educational Round: VolBIT Formulas Blitz  
**年份**: 2016  
**时间限制**: 0.5秒  
**内存限制**: 64MB  

## 题目描述

The HR manager was disappointed again. The last applicant failed the interview the same way as 24 previous ones. "Do I give such a hard task?" — the HR manager thought. "Just raise number 5 to the power of n and get last two digits of the number. Yes, of course, n can be rather big, and one cannot find the power using a calculator, but we need people who are able to think, not just follow the instructions."

Could you pass the interview in the machine vision company in IT City?

## 输入格式

The only line of the input contains a single integer n (2 ≤ n ≤ 2·1018) — the power in which you need to raise number 5.

## 输出格式

Output the last two digits of 5n without spaces between them.

## 样例

### 样例 1

**输入**:
```
2
```

**输出**:
```
25
```

## 题解

The problem of getting the last two digits is equivalent to the problem of getting the number modulo 100. So we need to calculate $${ 5 } ^ { n } \mod 1 0 0$$. According to the rules of modular arithmetic
$$(a \cdot b) \mod c = ((a \mod c) \cdot (b \mod c)) \mod c$$
So
$${ { 5 } ^ { n } \mod 100 = ( ( 5 ^ { n - 1 } \mod 100 ) \cdot 5 ) \mod 100 }$$
Let's note that 52 = 25. Then
$$5^{3} \mod 100 = ((5^{2} \mod 100) \cdot 5) \mod 100 = (25 \cdot 5) \mod 100 = 25$$
$${ { 5 ^ { 4 } \mod 100 = ( ( 5 ^ { 3 } \mod 100 ) \cdot 5 ) \mod 100 = ( 2 5 \cdot 5 ) \mod 100 = 2 5 } }$$
And so on. All $${ 5 } ^ { n } \mod 1 0 0$$ are equal to 25 for all n ≥ 2.
So to solve the problem one need to just output 25. There is no need to read n.

