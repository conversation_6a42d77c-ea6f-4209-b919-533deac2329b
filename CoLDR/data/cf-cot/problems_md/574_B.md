# Bear and Three Musketeers

**题目ID**: 574/B  
**比赛**: Codeforces Round 318 [RussianCodeCup Thanks-Round] (Div. 2)  
**年份**: 2015  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

Do you know a story about the three musketeers? Anyway, you will learn about its origins now.

<PERSON><PERSON><PERSON><PERSON><PERSON> is a cardinal in the city of Bearis. He is tired of dealing with crime by himself. He needs three brave warriors to help him to fight against bad guys.

There are n warriors. <PERSON><PERSON><PERSON><PERSON><PERSON> wants to choose three of them to become musketeers but it's not that easy. The most important condition is that musketeers must know each other to cooperate efficiently. And they shouldn't be too well known because they could be betrayed by old friends. For each musketeer his recognition is the number of warriors he knows, excluding other two musketeers.

Help <PERSON><PERSON><PERSON><PERSON><PERSON>! Find if it is possible to choose three musketeers knowing each other, and what is minimum possible sum of their recognitions.

## 输入格式

The first line contains two space-separated integers, n and m (3 ≤ n ≤ 4000, 0 ≤ m ≤ 4000) — respectively number of warriors and number of pairs of warriors knowing each other.

i-th of the following m lines contains two space-separated integers ai and bi (1 ≤ ai, bi ≤ n, ai ≠ bi). Warriors ai and bi know each other. Each pair of warriors will be listed at most once.

## 输出格式

If <PERSON><PERSON><PERSON>kieu can choose three musketeers, print the minimum possible sum of their recognitions. Otherwise, print "-1" (without the quotes).

## 样例

### 样例 1

**输入**:
```
5 6
1 2
1 3
2 3
2 4
3 4
4 5
```

**输出**:
```
2
```

### 样例 2

**输入**:
```
7 4
2 1
3 6
5 1
1 7
```

**输出**:
```
-1
```

## 备注

In the first sample Richelimakieu should choose a triple 1, 2, 3. The first musketeer doesn't know anyone except other two musketeers so his recognition is 0. The second musketeer has recognition 1 because he knows warrior number 4. The third musketeer also has recognition 1 because he knows warrior 4. Sum of recognitions is 0 + 1 + 1 = 2.

The other possible triple is 2, 3, 4 but it has greater sum of recognitions, equal to 1 + 1 + 1 = 3.

In the second sample there is no triple of warriors knowing each other.

## 题解

Warriors are vertices and "knowing each other" is an edge. We want to find connected triple of vertices with the lowest sum of degrees (and print sum - 6 because we don't want to count edges from one chosen vertex to another).
Brute force is O(n3). We iterate over all triples a, b, c and consider them as musketeers. They must be connected by edges (they must know each other). If they are, then we consider sum of their degrees.
We must notice that there is low limit for number of edges. So instead of iterating over triples of vertices we can iterate over edges and then iterate over third vertex. It gives us O(n·m) and it's intended solution. To check if third vertex is connected with other two, you should additionally store edges in 2D adjacency matrix.
It's also possible to write it by changing brute force solution with "if" in right place. Check it out in second implementation - ...

