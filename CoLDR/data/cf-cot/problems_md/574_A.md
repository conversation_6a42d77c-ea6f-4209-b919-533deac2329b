# Bear and Elections

**题目ID**: 574/A  
**比赛**: Codeforces Round 318 [RussianCodeCup Thanks-Round] (Div. 2)  
**年份**: 2015  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON><PERSON> is a grizzly bear who desires power and adoration. He wants to win in upcoming elections and rule over the Bearland.

There are n candidates, including <PERSON><PERSON>. We know how many citizens are going to vote for each candidate. Now i-th candidate would get ai votes. <PERSON><PERSON> is candidate number 1. To win in elections, he must get strictly more votes than any other candidate.

Victory is more important than everything else so <PERSON><PERSON> decided to cheat. He will steal votes from his opponents by bribing some citizens. To bribe a citizen, <PERSON><PERSON> must give him or her one candy - citizens are bears and bears like candies. <PERSON><PERSON> doesn't have many candies and wonders - how many citizens does he have to bribe?

## 输入格式

The first line contains single integer n (2 ≤ n ≤ 100) - number of candidates.

The second line contains n space-separated integers a1, a2, ..., an (1 ≤ ai ≤ 1000) - number of votes for each candidate. <PERSON><PERSON> is candidate number 1.

Note that after bribing number of votes for some candidate might be zero or might be greater than 1000.

## 输出格式

Print the minimum number of citizens <PERSON><PERSON> must bribe to have strictly more votes than any other candidate.

## 样例

### 样例 1

**输入**:
```
5
5 1 11 2 8
```

**输出**:
```
4
```

### 样例 2

**输入**:
```
4
1 8 8 8
```

**输出**:
```
6
```

### 样例 3

**输入**:
```
2
7 6
```

**输出**:
```
0
```

## 备注

In the first sample <PERSON>k has 5 votes. One of the ways to achieve victory is to bribe 4 citizens who want to vote for the third candidate. Then numbers of votes would be 9, 1, 7, 2, 8 (Limak would have 9 votes). Alternatively, Limak could steal only 3 votes from the third candidate and 1 vote from the second candidate to get situation 9, 0, 8, 2, 8.

In the second sample Limak will steal 2 votes from each candidate. Situation will be 7, 6, 6, 6.

In the third sample Limak is a winner without bribing any citizen.

