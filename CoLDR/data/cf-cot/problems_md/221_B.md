# Little Elephant and Numbers

**题目ID**: 221/B  
**比赛**: Codeforces Round 136 (Div. 2)  
**年份**: 2012  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

The Little Elephant loves numbers.

He has a positive integer x. The Little Elephant wants to find the number of positive integers d, such that d is the divisor of x, and x and d have at least one common (the same) digit in their decimal representations.

Help the Little Elephant to find the described number.

## 输入格式

A single line contains a single integer x (1 ≤ x ≤ 109).

## 输出格式

In a single line print an integer — the answer to the problem.

## 样例

### 样例 1

**输入**:
```
1
```

**输出**:
```
1
```

### 样例 2

**输入**:
```
10
```

**输出**:
```
2
```

