# String inside out

**题目ID**: 530/B  
**比赛**: VK Cup 2015 - Wild Card Round 1  
**年份**: 2015  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

You are given a string S of even length s1..s2n . Perform the following manipulations:

- divide it into two halves s1..sn and sn + 1..s2n
- reverse each of them sn..s1 and s2n..sn + 1
- concatenate the resulting strings into sn..s1s2n..sn + 1

Output the result of these manipulations.

## 输入格式

The only line of the input contains a string of lowercase Latin letters. The length of the string is between 2 and 20, inclusive, and it is even.

## 输出格式

Output the string which is the result of the described manipulations.

## 样例

### 样例 1

**输入**:
```
codeforces
```

**输出**:
```
fedocsecro
```

### 样例 2

**输入**:
```
qwertyasdfgh
```

**输出**:
```
ytrewqhgfdsa
```

