# Tiling with Hexagons

**题目ID**: 216/A  
**比赛**: Codeforces Round 133 (Div. 2)  
**年份**: 2012  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

Several ages ago Berland was a kingdom. The King of Berland adored math. That's why, when he first visited one of his many palaces, he first of all paid attention to the floor in one hall. The floor was tiled with hexagonal tiles.

The hall also turned out hexagonal in its shape. The King walked along the perimeter of the hall and concluded that each of the six sides has a, b, c, a, b and c adjacent tiles, correspondingly.

To better visualize the situation, look at the picture showing a similar hexagon for a = 2, b = 3 and c = 4.

According to the legend, as the King of Berland obtained the values a, b and c, he almost immediately calculated the total number of tiles on the hall floor. Can you do the same?

## 输入格式

The first line contains three integers: a, b and c (2 ≤ a, b, c ≤ 1000).

## 输出格式

Print a single number — the total number of tiles on the hall floor.

## 样例

### 样例 1

**输入**:
```
2 3 4
```

**输出**:
```
18
```

