# Information Graph

**题目ID**: 466/E  
**比赛**: Codeforces Round 266 (Div. 2)  
**年份**: 2014  
**时间限制**: 1.0秒  
**内存限制**: 512MB  

## 题目描述

There are n employees working in company "X" (let's number them from 1 to n for convenience). Initially the employees didn't have any relationships among each other. On each of m next days one of the following events took place:

- either employee y became the boss of employee x (at that, employee x didn't have a boss before);
- or employee x gets a packet of documents and signs them; then he gives the packet to his boss. The boss signs the documents and gives them to his boss and so on (the last person to sign the documents sends them to the archive);
- or comes a request of type "determine whether employee x signs certain documents".

Your task is to write a program that will, given the events, answer the queries of the described type. At that, it is guaranteed that throughout the whole working time the company didn't have cyclic dependencies.

## 输入格式

The first line contains two integers n and m (1 ≤ n, m ≤ 105) — the number of employees and the number of events.

Each of the next m lines contains the description of one event (the events are given in the chronological order). The first number of the line determines the type of event t (1 ≤ t ≤ 3).

- If t = 1, then next follow two integers x and y (1 ≤ x, y ≤ n) — numbers of the company employees. It is guaranteed that employee x doesn't have the boss currently.
- If t = 2, then next follow integer x (1 ≤ x ≤ n) — the number of the employee who got a document packet.
- If t = 3, then next follow two integers x and i (1 ≤ x ≤ n; 1 ≤ i ≤ [number of packets that have already been given]) — the employee and the number of the document packet for which you need to find out information. The document packets are numbered started from 1 in the chronological order.

It is guaranteed that the input has at least one query of the third type.

## 输出格式

For each query of the third type print "YES" if the employee signed the document package and "NO" otherwise. Print all the words without the quotes.

## 样例

### 样例 1

**输入**:
```
4 9
1 4 3
2 4
3 3 1
1 2 3
2 2
3 1 2
1 3 1
2 2
3 1 3
```

**输出**:
```
YES
NO
YES
```

