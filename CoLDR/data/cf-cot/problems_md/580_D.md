# <PERSON><PERSON> and Dishes

**题目ID**: 580/D  
**比赛**: Codeforces Round 321 (Div. 2)  
**年份**: 2015  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

When <PERSON><PERSON> came to the restaurant and sat at a table, the waiter immediately brought him the menu. There were n dishes. <PERSON><PERSON> knows that he needs exactly m dishes. But at that, he doesn't want to order the same dish twice to taste as many dishes as possible.

<PERSON><PERSON> knows that the i-th dish gives him ai units of satisfaction. But some dishes do not go well together and some dishes go very well together. <PERSON><PERSON> set to himself k rules of eating food of the following type — if he eats dish x exactly before dish y (there should be no other dishes between x and y), then his satisfaction level raises by c.

Of course, our parrot wants to get some maximal possible satisfaction from going to the restaurant. Help him in this hard task!

## 输入格式

The first line of the input contains three space-separated numbers, n, m and k (1 ≤ m ≤ n ≤ 18, 0 ≤ k ≤ n * (n - 1)) — the number of dishes on the menu, the number of portions <PERSON><PERSON> needs to eat to get full and the number of eating rules.

The second line contains n space-separated numbers ai, (0 ≤ ai ≤ 109) — the satisfaction he gets from the i-th dish.

Next k lines contain the rules. The i-th rule is described by the three numbers xi, yi and ci (1 ≤ xi, yi ≤ n, 0 ≤ ci ≤ 109). That means that if you eat dish xi right before dish yi, then the Kefa's satisfaction increases by ci. It is guaranteed that there are no such pairs of indexes i and j (1 ≤ i < j ≤ k), that xi = xj and yi = yj.

## 输出格式

In the single line of the output print the maximum satisfaction that Kefa can get from going to the restaurant.

## 样例

### 样例 1

**输入**:
```
2 2 1
1 1
2 1 1
```

**输出**:
```
3
```

### 样例 2

**输入**:
```
4 3 2
1 2 3 4
2 1 5
3 4 2
```

**输出**:
```
12
```

## 备注

In the first sample it is best to first eat the second dish, then the first one. Then we get one unit of satisfaction for each dish and plus one more for the rule.

In the second test the fitting sequences of choice are 4 2 1 or 2 1 4. In both cases we get satisfaction 7 for dishes and also, if we fulfill rule 1, we get an additional satisfaction 5.

## 题解

Для решения задачи будем использовать двумерное ДП. Первым измерением будет маска уже взятых блюд, а вторым — номер последнего взятого блюда. Для переходов будем перебирать все нулевые биты текущей маски. Будем пытаться поставить в них единицу, а затем обновлять ответ для новой маски. Ответ будет состоять из ответа для старой маски, ценности блюда, которому соответствует добавленный бит и правила, которое может быть использовано. Окончательным ответом будет максимум из всех значений ДП, где маска содержит ровно m единиц.
Асимптотика — O(2n * n2).

