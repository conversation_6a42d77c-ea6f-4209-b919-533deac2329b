# Anton and currency you all know

**题目ID**: 508/B  
**比赛**: Codeforces Round 288 (Div. 2)  
**年份**: 2015  
**时间限制**: 0.5秒  
**内存限制**: 256MB  

## 题目描述

Berland, 2016. The exchange rate of currency you all know against the burle has increased so much that to simplify the calculations, its fractional part was neglected and the exchange rate is now assumed to be an integer.

Reliable sources have informed the financier <PERSON> of some information about the exchange rate of currency you all know against the burle for tomorrow. Now <PERSON> knows that tomorrow the exchange rate will be an even number, which can be obtained from the present rate by swapping exactly two distinct digits in it. Of all the possible values that meet these conditions, the exchange rate for tomorrow will be the maximum possible. It is guaranteed that today the exchange rate is an odd positive integer n. Help <PERSON> to determine the exchange rate of currency you all know for tomorrow!

## 输入格式

The first line contains an odd positive integer n — the exchange rate of currency you all know for today. The length of number n's representation is within range from 2 to 105, inclusive. The representation of n doesn't contain any leading zeroes.

## 输出格式

If the information about tomorrow's exchange rate is inconsistent, that is, there is no integer that meets the condition, print  - 1.

Otherwise, print the exchange rate of currency you all know against the burle for tomorrow. This should be the maximum possible number of those that are even and that are obtained from today's exchange rate by swapping exactly two digits. Exchange rate representation should not contain leading zeroes.

## 样例

### 样例 1

**输入**:
```
527
```

**输出**:
```
572
```

### 样例 2

**输入**:
```
4573
```

**输出**:
```
3574
```

### 样例 3

**输入**:
```
1357997531
```

**输出**:
```
-1
```

