# <PERSON> and Walls

**题目ID**: 525/D  
**比赛**: Codeforces Round 297 (Div. 2)  
**年份**: 2015  
**时间限制**: 2.0秒  
**内存限制**: 512MB  

## 题目描述

Finally it is a day when <PERSON> has enough money for buying an apartment. He found a great option close to the center of the city with a nice price.

Plan of the apartment found by <PERSON> looks like a rectangle n × m consisting of squares of size 1 × 1. Each of those squares contains either a wall (such square is denoted by a symbol "*" on the plan) or a free space (such square is denoted on the plan by a symbol ".").

Room in an apartment is a maximal connected area consisting of free squares. Squares are considered adjacent if they share a common side.

The old <PERSON> dream is to live in an apartment where all rooms are rectangles. He asks you to calculate minimum number of walls you need to remove in order to achieve this goal. After removing a wall from a square it becomes a free square. While removing the walls it is possible that some rooms unite into a single one.

## 输入格式

The first line of the input contains two integers n, m (1 ≤ n, m ≤ 2000) denoting the size of the Arthur apartments.

Following n lines each contain m symbols — the plan of the apartment.

If the cell is denoted by a symbol "*" then it contains a wall.

If the cell is denoted by a symbol "." then it this cell is free from walls and also this cell is contained in some of the rooms.

## 输出格式

Output n rows each consisting of m symbols that show how the <PERSON> apartment plan should look like after deleting the minimum number of walls in order to make each room (maximum connected area free from walls) be a rectangle.

If there are several possible answers, output any of them.

## 样例

### 样例 1

**输入**:
```
5 5
.*.*.
*****
.*.*.
*****
.*.*.
```

**输出**:
```
.*.*.
*****
.*.*.
*****
.*.*.
```

### 样例 2

**输入**:
```
6 7
***.*.*
..*.*.*
*.*.*.*
*.*.*.*
..*...*
*******
```

**输出**:
```
***...*
..*...*
..*...*
..*...*
..*...*
*******
```

### 样例 3

**输入**:
```
4 5
.....
.....
..***
..*..
```

**输出**:
```
.....
.....
.....
.....
```

