# Tennis Game

**题目ID**: 496/D  
**比赛**: Codeforces Round 283 (Div. 2)  
**年份**: 2014  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON><PERSON> and <PERSON><PERSON> love playing table tennis. A single match is played according to the following rules: a match consists of multiple sets, each set consists of multiple serves. Each serve is won by one of the players, this player scores one point. As soon as one of the players scores t points, he wins the set; then the next set starts and scores of both players are being set to 0. As soon as one of the players wins the total of s sets, he wins the match and the match is over. Here s and t are some positive integer numbers.

To spice it up, <PERSON><PERSON> and <PERSON><PERSON> choose new numbers s and t before every match. Besides, for the sake of history they keep a record of each match: that is, for each serve they write down the winner. Serve winners are recorded in the chronological order. In a record the set is over as soon as one of the players scores t points and the match is over as soon as one of the players wins s sets.

<PERSON><PERSON> and <PERSON><PERSON> have found a record of an old match. Unfortunately, the sequence of serves in the record isn't divided into sets and numbers s and t for the given match are also lost. The players now wonder what values of s and t might be. Can you determine all the possible options?

## 输入格式

The first line contains a single integer n — the length of the sequence of games (1 ≤ n ≤ 105).

The second line contains n space-separated integers ai. If ai = 1, then the i-th serve was won by Petya, if ai = 2, then the i-th serve was won by Gena.

It is not guaranteed that at least one option for numbers s and t corresponds to the given record.

## 输出格式

In the first line print a single number k — the number of options for numbers s and t.

In each of the following k lines print two integers si and ti — the option for numbers s and t. Print the options in the order of increasing si, and for equal si — in the order of increasing ti.

## 样例

### 样例 1

**输入**:
```
5
1 2 1 2 1
```

**输出**:
```
2
1 3
3 1
```

### 样例 2

**输入**:
```
4
1 1 1 1
```

**输出**:
```
3
1 4
2 2
4 1
```

### 样例 3

**输入**:
```
4
1 2 1 2
```

**输出**:
```
0
```

### 样例 4

**输入**:
```
8
2 1 2 1 1 1 1 1
```

**输出**:
```
3
1 6
2 3
6 1
```

