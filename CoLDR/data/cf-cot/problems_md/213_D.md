# Stars

**题目ID**: 213/D  
**比赛**: Codeforces Round 131 (Div. 1)  
**年份**: 2012  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON><PERSON> loves painting stars. A star is a shape that results if we take a regular pentagon and paint all diagonals in it.

Recently he decided to teach <PERSON><PERSON><PERSON> to paint stars. After many years of training <PERSON><PERSON><PERSON> could paint stars easily. But now <PERSON><PERSON> decided to test <PERSON><PERSON><PERSON> and complicated the task. <PERSON><PERSON><PERSON> must paint n stars, observing the following rules:

- all stars must be painted in a single move (i.e. it is forbidden to take the pen away from the paper);
- it is forbidden to paint the same segment of non-zero length more than once;
- the stars can intersect only in their vertexes;
- the length of a side of the regular pentagon, in which <PERSON><PERSON><PERSON> paints each star, must equal 10.

Help <PERSON><PERSON><PERSON> to cope with this hard task.

## 输入格式

A single line contains an integer (1 ≤ n ≤ 100) — the number of stars to paint.

## 输出格式

On the first line print an integer m (1 ≤ m ≤ 5·n). On the next m lines print coordinates of m distinct points with accuracy of at least 9 and at most 100 digits after decimal point. All coordinates should not exceed 5000 in their absolute value. On each of the next n lines print 5 integers — the indexes of the points that form the given star in the clockwise or counterclockwise order. On the next line print 5·n + 1 integers — the numbers of points in the order, in which Rubik paints stars. That is, if number with index i is ai, and number with index i + 1 is ai + 1, then points with indexes ai and ai + 1 will have a segment painted between them.

You can consider all m printed points indexed from 1 to m in the order, in which they occur in the output. Separate the numbers on the lines with whitespaces.

Note that the answer has an imprecise validation. Try to obtain as accurate a solution as possible. The validator performs all calculations considering that the absolute error of a participant's answer is not more than 10 - 8.

## 样例

### 样例 1

**输入**:
```
1
```

**输出**:
```
5
3.830127018922193 3.366025403784439
-3.601321235851749 10.057331467373021
0.466045194906253 19.192786043799030
10.411264148588986 18.147501411122495
12.490381056766580 8.366025403784439
1 2 3 4 5
1 3 5 2 4 1
```

## 备注

The initial position of points in the sample is:

The order in which Rubik can paint segments is:

