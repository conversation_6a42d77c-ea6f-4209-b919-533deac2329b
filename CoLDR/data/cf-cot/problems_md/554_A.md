# Kyoya and Photobooks

**题目ID**: 554/A  
**比赛**: Codeforces Round 309 (Div. 2)  
**年份**: 2015  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON><PERSON><PERSON> is selling photobooks of the Ouran High School Host Club. He has 26 photos, labeled "a" to "z", and he has compiled them into a photo booklet with some photos in some order (possibly with some photos being duplicated). A photo booklet can be described as a string of lowercase letters, consisting of the photos in the booklet in order. He now wants to sell some "special edition" photobooks, each with one extra photo inserted anywhere in the book. He wants to make as many distinct photobooks as possible, so he can make more money. He asks <PERSON><PERSON><PERSON>, how many distinct photobooks can he make by inserting one extra photo into the photobook he already has?

Please help <PERSON><PERSON><PERSON> solve this problem.

## 输入格式

The first line of input will be a single string s (1 ≤ |s| ≤ 20). String s consists only of lowercase English letters.

## 输出格式

Output a single integer equal to the number of distinct photobooks <PERSON><PERSON><PERSON> can make.

## 样例

### 样例 1

**输入**:
```
a
```

**输出**:
```
51
```

### 样例 2

**输入**:
```
hi
```

**输出**:
```
76
```

## 备注

In the first case, we can make 'ab','ac',...,'az','ba','ca',...,'za', and 'aa', producing a total of 51 distinct photo booklets.

## 题解

It's enough to just simulate adding every character at every position at the string, and removing any duplicates. For instance, we can use a HashSet of Strings in Java to do this.
Bonus: Prove that the number of ways is always (length of string + 1) * 25 + 1.

