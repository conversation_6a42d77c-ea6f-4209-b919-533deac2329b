# Fox And Snake

**题目ID**: 510/A  
**比赛**: Codeforces Round 290 (Div. 2)  
**年份**: 2015  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON>iel starts to learn programming. The first task is drawing a fox! However, that turns out to be too hard for a beginner, so she decides to draw a snake instead.

A snake is a pattern on a n by m table. Denote c-th cell of r-th row as (r, c). The tail of the snake is located at (1, 1), then it's body extends to (1, m), then goes down 2 rows to (3, m), then goes left to (3, 1) and so on.

Your task is to draw this snake for Fox Ciel: the empty cells should be represented as dot characters ('.') and the snake cells should be filled with number signs ('#').

Consider sample tests in order to understand the snake pattern.

## 输入格式

The only line contains two integers: n and m (3 ≤ n, m ≤ 50).

n is an odd number.

## 输出格式

Output n lines. Each line should contain a string consisting of m characters. Do not output spaces.

## 样例

### 样例 1

**输入**:
```
3 3
```

**输出**:
```
###
..#
###
```

### 样例 2

**输入**:
```
3 4
```

**输出**:
```
####
...#
####
```

### 样例 3

**输入**:
```
5 3
```

**输出**:
```
###
..#
###
#..
###
```

### 样例 4

**输入**:
```
9 9
```

**输出**:
```
#########
........#
#########
#........
#########
........#
#########
#........
#########
```

