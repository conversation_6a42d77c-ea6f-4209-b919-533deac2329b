# Worms

**题目ID**: 474/B  
**比赛**: Codeforces Round 271 (Div. 2)  
**年份**: 2014  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

It is lunch time for <PERSON><PERSON>. His friend, <PERSON><PERSON>, prepared him a nice game for lunch.

<PERSON><PERSON> brought <PERSON><PERSON> n ordered piles of worms such that i-th pile contains ai worms. He labeled all these worms with consecutive integers: worms in first pile are labeled with numbers 1 to a1, worms in second pile are labeled with numbers a1 + 1 to a1 + a2 and so on. See the example for a better understanding.

<PERSON><PERSON> can't eat all the worms (<PERSON><PERSON> brought a lot) and, as we all know, <PERSON><PERSON> is blind, so <PERSON><PERSON> tells him the labels of the best juicy worms. <PERSON><PERSON> will only give <PERSON><PERSON> a worm if <PERSON><PERSON> says correctly in which pile this worm is contained.

Poor <PERSON><PERSON> asks for your help. For all juicy worms said by <PERSON><PERSON>, tell <PERSON><PERSON> the correct answers.

## 输入格式

The first line contains a single integer n (1 ≤ n ≤ 105), the number of piles.

The second line contains n integers a1, a2, ..., an (1 ≤ ai ≤ 103, a1 + a2 + ... + an ≤ 106), where ai is the number of worms in the i-th pile.

The third line contains single integer m (1 ≤ m ≤ 105), the number of juicy worms said by <PERSON><PERSON>.

The fourth line contains m integers q1, q2, ..., qm (1 ≤ qi ≤ a1 + a2 + ... + an), the labels of the juicy worms.

## 输出格式

Print m lines to the standard output. The i-th line should contain an integer, representing the number of the pile where the worm labeled with the number qi is.

## 样例

### 样例 1

**输入**:
```
5
2 7 3 4 9
3
1 25 11
```

**输出**:
```
1
5
3
```

## 备注

For the sample input:

- The worms with labels from [1, 2] are in the first pile.
- The worms with labels from [3, 9] are in the second pile.
- The worms with labels from [10, 12] are in the third pile.
- The worms with labels from [13, 16] are in the fourth pile.
- The worms with labels from [17, 25] are in the fifth pile.

