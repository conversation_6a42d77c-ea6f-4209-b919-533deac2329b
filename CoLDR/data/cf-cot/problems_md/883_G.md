# Orientation of Edges

**题目ID**: 883/G  
**比赛**: 2017-2018 ACM-ICPC, NEERC, Southern Subregional Contest (Online Mirror, ACM-ICPC Rules, Teams Preferred)  
**年份**: 2017  
**时间限制**: 3.0秒  
**内存限制**: 256MB  

## 题目描述

V<PERSON>ya has a graph containing both directed (oriented) and undirected (non-oriented) edges. There can be multiple edges between a pair of vertices.

<PERSON><PERSON><PERSON> has picked a vertex s from the graph. Now <PERSON><PERSON><PERSON> wants to create two separate plans:

1. to orient each undirected edge in one of two possible directions to maximize number of vertices reachable from vertex s;
2. to orient each undirected edge in one of two possible directions to minimize number of vertices reachable from vertex s.

In each of two plans each undirected edge must become directed. For an edge chosen directions can differ in two plans.

Help <PERSON><PERSON><PERSON> find the plans.

## 输入格式

The first line contains three integers n, m and s (2 ≤ n ≤ 3·105, 1 ≤ m ≤ 3·105, 1 ≤ s ≤ n) — number of vertices and edges in the graph, and the vertex <PERSON><PERSON><PERSON> has picked.

The following m lines contain information about the graph edges. Each line contains three integers ti, ui and vi (1 ≤ ti ≤ 2, 1 ≤ ui, vi ≤ n, ui ≠ vi) — edge type and vertices connected by the edge. If ti = 1 then the edge is directed and goes from the vertex ui to the vertex vi. If ti = 2 then the edge is undirected and it connects the vertices ui and vi.

It is guaranteed that there is at least one undirected edge in the graph.

## 输出格式

The first two lines should describe the plan which maximizes the number of reachable vertices. The lines three and four should describe the plan which minimizes the number of reachable vertices.

A description of each plan should start with a line containing the number of reachable vertices. The second line of a plan should consist of f symbols '+' and '-', where f is the number of undirected edges in the initial graph. Print '+' as the j-th symbol of the string if the j-th undirected edge (u, v) from the input should be oriented from u to v. Print '-' to signify the opposite direction (from v to u). Consider undirected edges to be numbered in the same order they are given in the input.

If there are multiple solutions, print any of them.

## 样例

### 样例 1

**输入**:
```
2 2 1
1 1 2
2 2 1
```

**输出**:
```
2
-
2
+
```

### 样例 2

**输入**:
```
6 6 3
2 2 6
1 4 5
2 3 4
1 4 1
1 3 1
2 2 3
```

**输出**:
```
6
++-
2
+-+
```

