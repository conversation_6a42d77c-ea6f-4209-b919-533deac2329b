# Two Buttons

**题目ID**: 520/B  
**比赛**: Codeforces Round 295 (Div. 2)  
**年份**: 2015  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON><PERSON><PERSON> has found a strange device. On the front panel of a device there are: a red button, a blue button and a display showing some positive integer. After clicking the red button, device multiplies the displayed number by two. After clicking the blue button, device subtracts one from the number on the display. If at some point the number stops being positive, the device breaks down. The display can show arbitrarily large numbers. Initially, the display shows number n.

<PERSON> wants to get number m on the display. What minimum number of clicks he has to make in order to achieve this result?

## 输入格式

The first and the only line of the input contains two distinct integers n and m (1 ≤ n, m ≤ 104), separated by a space .

## 输出格式

Print a single number — the minimum number of times one needs to push the button required to get the number m out of number n.

## 样例

### 样例 1

**输入**:
```
4 6
```

**输出**:
```
2
```

### 样例 2

**输入**:
```
10 1
```

**输出**:
```
9
```

## 备注

In the first example you need to push the blue button once, and then push the red button once.

In the second example, doubling the number is unnecessary, so we need to push the blue button nine times.

