# Expression

**题目ID**: 479/A  
**比赛**: Codeforces Round 274 (Div. 2)  
**年份**: 2014  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON><PERSON> studies in a school and he adores Maths. His class has been studying arithmetic expressions. On the last class the teacher wrote three positive integers a, b, c on the blackboard. The task was to insert signs of operations '+' and '*', and probably brackets between the numbers so that the value of the resulting expression is as large as possible. Let's consider an example: assume that the teacher wrote numbers 1, 2 and 3 on the blackboard. Here are some ways of placing signs and brackets:

- 1+2*3=7
- 1*(2+3)=5
- 1*2*3=6
- (1+2)*3=9

Note that you can insert operation signs only between a and b, and between b and c, that is, you cannot swap integers. For instance, in the given sample you cannot get expression (1+3)*2.

It's easy to see that the maximum value that you can obtain is 9.

Your task is: given a, b and c print the maximum value that you can get.

## 输入格式

The input contains three integers a, b and c, each on a single line (1 ≤ a, b, c ≤ 10).

## 输出格式

Print the maximum value of the expression that you can obtain.

## 样例

### 样例 1

**输入**:
```
1
2
3
```

**输出**:
```
9
```

### 样例 2

**输入**:
```
2
10
3
```

**输出**:
```
60
```

