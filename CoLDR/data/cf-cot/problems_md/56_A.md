# Bar

**题目ID**: 56/A  
**比赛**: Codeforces Beta Round 52 (Div. 2)  
**年份**: 2011  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

According to Berland laws it is only allowed to sell alcohol to people not younger than 18 years. <PERSON><PERSON><PERSON>'s job is to monitor the law's enforcement. Tonight he entered a bar and saw n people sitting there. For every one of them <PERSON><PERSON><PERSON> happened to determine either the age or the drink the person is having. <PERSON><PERSON><PERSON> can check any person, i.e. learn his age and the drink he is having at the same time. What minimal number of people should <PERSON><PERSON><PERSON> check additionally to make sure that there are no clients under 18 having alcohol drinks?

The list of all alcohol drinks in Berland is: ABSINTH, BEER, BRANDY, CHAMPAGNE, GIN, RUM, SAKE, TEQUILA, VODKA, WHISKEY, WINE

## 输入格式

The first line contains an integer n (1 ≤ n ≤ 100) which is the number of the bar's clients. Then follow n lines, each describing one visitor. A line either contains his age (an integer from 0 to 1000) or his drink (a string of capital Latin letters from 1 to 100 in length). It is guaranteed that the input data does not contain spaces and other unnecessary separators.

Only the drinks from the list given above should be considered alcohol.

## 输出格式

Print a single number which is the number of people <PERSON><PERSON><PERSON> should check to guarantee the law enforcement.

## 样例

### 样例 1

**输入**:
```
5
18
VODKA
COKE
19
17
```

**输出**:
```
2
```

## 备注

In the sample test the second and fifth clients should be checked.

