# Chewbaсca and Number

**题目ID**: 514/A  
**比赛**: Codeforces Round 291 (Div. 2)  
**年份**: 2015  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON>walker gave <PERSON><PERSON><PERSON><PERSON> an integer number x. <PERSON><PERSON><PERSON><PERSON> isn't good at numbers but he loves inverting digits in them. Inverting digit t means replacing it with digit 9 - t.

Help <PERSON><PERSON><PERSON><PERSON> to transform the initial number x to the minimum possible positive number by inverting some (possibly, zero) digits. The decimal representation of the final number shouldn't start with a zero.

## 输入格式

The first line contains a single integer x (1 ≤ x ≤ 1018) — the number that <PERSON>walker gave to Chewbacca.

## 输出格式

Print the minimum possible positive number that <PERSON><PERSON><PERSON><PERSON> can obtain after inverting some digits. The number shouldn't contain leading zeroes.

## 样例

### 样例 1

**输入**:
```
27
```

**输出**:
```
22
```

### 样例 2

**输入**:
```
4545
```

**输出**:
```
4444
```

