# Berland Miners

**题目ID**: 532/A  
**比赛**: VK Cup 2015 - Round 2  
**年份**: 2015  
**时间限制**: 3.0秒  
**内存限制**: 256MB  

## 题目描述

The biggest gold mine in Berland consists of n caves, connected by n - 1 transitions. The entrance to the mine leads to the cave number 1, it is possible to go from it to any remaining cave of the mine by moving along the transitions.

The mine is being developed by the InMine Inc., k miners work for it. Each day the corporation sorts miners into caves so that each cave has at most one miner working there.

For each cave we know the height of its ceiling hi in meters, and for each miner we know his height sj, also in meters. If a miner's height doesn't exceed the height of the cave ceiling where he is, then he can stand there comfortably, otherwise, he has to stoop and that makes him unhappy.

Unfortunately, miners typically go on strike in Berland, so InMine makes all the possible effort to make miners happy about their work conditions. To ensure that no miner goes on strike, you need make sure that no miner has to stoop at any moment on his way from the entrance to the mine to his cave (in particular, he must be able to stand comfortably in the cave where he works).

To reach this goal, you can choose exactly one cave and increase the height of its ceiling by several meters. However enlarging a cave is an expensive and complex procedure. That's why InMine Inc. asks you either to determine the minimum number of meters you should raise the ceiling of some cave so that it is be possible to sort the miners into the caves and keep all miners happy with their working conditions or to determine that it is impossible to achieve by raising ceiling in exactly one cave.

## 输入格式

The first line contains integer n (1 ≤ n ≤ 5·105) — the number of caves in the mine.

Then follows a line consisting of n positive integers h1, h2, ..., hn (1 ≤ hi ≤ 109), where hi is the height of the ceiling in the i-th cave.

Next n - 1 lines contain the descriptions of transitions between the caves. Each line has the form ai, bi (1 ≤ ai, bi ≤ n, ai ≠ bi), where ai and bi are the numbers of the caves connected by a path.

The next line contains integer k (1 ≤ k ≤ n).

The last line contains k integers s1, s2, ..., sk (1 ≤ sj ≤ 109), where sj is the j-th miner's height.

## 输出格式

In the single line print the minimum number of meters that you need to raise the ceiling by in some cave so that all miners could be sorted into caves and be happy about the work conditions. If it is impossible to do, print  - 1. If it is initially possible and there's no need to raise any ceiling, print 0.

## 样例

### 样例 1

**输入**:
```
6
5 8 4 6 3 12
1 2
1 3
4 2
2 5
6 3
6
7 4 2 5 3 11
```

**输出**:
```
6
```

### 样例 2

**输入**:
```
7
10 14 7 12 4 50 1
1 2
2 3
2 4
5 1
6 5
1 7
6
7 3 4 8 8 10
```

**输出**:
```
0
```

### 样例 3

**输入**:
```
3
4 2 8
1 2
1 3
2
17 15
```

**输出**:
```
-1
```

## 备注

In the first sample test we should increase ceiling height in the first cave from 5 to 11. After that we can distribute miners as following (first goes index of a miner, then index of a cave): $$1 \rightarrow 2, 2 \rightarrow 3, 3 \rightarrow 5, 4 \rightarrow 4, 5 \rightarrow 6, 6 \rightarrow 1$$.

In the second sample test there is no need to do anything since it is already possible to distribute miners as following: $$1 \rightarrow 3, 2 \rightarrow 5, 3 \rightarrow 6, 4 \rightarrow 1, 5 \rightarrow 2, 6 \rightarrow 4$$.

In the third sample test it is impossible.

## 题解

Для удобства рассуждений будем считать, что n = k. К этом случаю можно свести добавив шахтеров нулевого роста.
Без возможности увеличить одну из шахт задача решается большим количеством различных жадностей. Рассмотрим одну из них. Для каждой шахты найдем минимальный рост шахтера, который может в нее попасть. Назовем эту величину настоящей высотой шахты. Отсортируем шахты по этой величине, а шахтеров по росту. Пройдем по списку шахт, и в каждую шахту поставим самого высокого невзятого шахтера, которого можно туда поставить. Назовем шахты и шахтеров, которые остались без пары свободными.
При изменении высоты одной из шахт, у некоторого множества шахт увеличивается настоящая высота. Можно доказать, что после увеличения можно будет разместить всех шахтеров тогда, и только тогда, когда всех свободных шахтеров можно разметить по шахтам, настоящая высота которых увеличилась, а также увеличилась настоящая высота одной из шахт, настоящая высота которых была меньше чем самой маленькой свободной.
Заметим, что из второго условия следует, что все эти шахты были заполнены. Это моментально дает нам достаточность условия - шахтеров, которые были в этих шахтах, можно рассадить по свободным, которые все больше. Необходимость второго условия также тривиальна - если оно не выполнено, то в эту шахту все еще некого посадить.
Отсортируем шахты так, чтобы те, у которых минимум высоты на пути от корня достигается на одной и той же вершине шли рядом, и были отсортированны по второму минимуму на пути внутри группы. Сделаем бинарный поиск по ответу. Каждая группа уже отсортирована по настоящей высоте после прибавления, а значит проверить, что всех свободных шахтеров можно распределить можно за линейное от размера группы время.

