# Vacuum Сleaner

**题目ID**: 54/E  
**比赛**: Codeforces Beta Round 50  
**年份**: 2011  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

One winter evening the Hedgehog was relaxing at home in his cozy armchair and clicking through the TV channels. Stumbled on an issue of «TopShop», the Hedgehog was about to change the channel when all of a sudden he was stopped by an advertisement of a new wondrous invention.

Actually, a vacuum cleaner was advertised there. It was called Marvellous Vacuum and it doesn't even need a human to operate it while it cleans! The vacuum cleaner can move around the flat on its own: it moves in some direction and if it hits an obstacle there, it automatically chooses a new direction. Sooner or later this vacuum cleaner will travel through all the room and clean it all. Having remembered how much time the Hedgehog spends every time on cleaning (surely, no less than a half of the day), he got eager to buy this wonder.

However, the Hedgehog quickly understood that the cleaner has at least one weak point: it won't clean well in the room's corners because it often won't able to reach the corner due to its shape. To estimate how serious is this drawback in practice, the Hedgehog asked you to write for him the corresponding program.

You will be given the cleaner's shape in the top view. We will consider only the cases when the vacuum cleaner is represented as a convex polygon. The room is some infinitely large rectangle. We consider one corner of this room and want to find such a rotation of the vacuum cleaner so that it, being pushed into this corner, will leave the minimum possible area in the corner uncovered.

## 输入格式

The first line contains an integer N which represents the number of vertices of the vacuum cleaner's polygon (3 ≤ N ≤ 4·104). Then follow N lines each containing two numbers — the coordinates of a vertex of the polygon. All the coordinates are integer and their absolute values do not exceed 106.

It is guaranteed that the given polygon is nondegenerate and convex (no three points lie on the same line). The polygon vertices are given in a clockwise or counter-clockwise direction.

## 输出格式

Print the minimum possible uncovered area. The answer will be accepted if it is within 10 - 6 of absolute or relative error from the correct answer.

## 样例

### 样例 1

**输入**:
```
4
0 0
1 0
1 1
0 1
```

**输出**:
```
0.00000000000000000000
```

### 样例 2

**输入**:
```
8
1 2
2 1
2 -1
1 -2
-1 -2
-2 -1
-2 1
-1 2
```

**输出**:
```
0.50000000000000000000
```

