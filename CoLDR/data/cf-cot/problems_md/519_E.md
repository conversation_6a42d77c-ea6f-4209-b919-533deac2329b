# A and B and Lecture Rooms

**题目ID**: 519/E  
**比赛**: Codeforces Round 294 (Div. 2)  
**年份**: 2015  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

A and B are preparing themselves for programming contests.

The University where A and B study is a set of rooms connected by corridors. Overall, the University has n rooms connected by n - 1 corridors so that you can get from any room to any other one by moving along the corridors. The rooms are numbered from 1 to n.

Every day А and B write contests in some rooms of their university, and after each contest they gather together in the same room and discuss problems. A and B want the distance from the rooms where problems are discussed to the rooms where contests are written to be equal. The distance between two rooms is the number of edges on the shortest path between them.

As they write contests in new rooms every day, they asked you to help them find the number of possible rooms to discuss problems for each of the following m days.

## 输入格式

The first line contains integer n (1 ≤ n ≤ 105) — the number of rooms in the University.

The next n - 1 lines describe the corridors. The i-th of these lines (1 ≤ i ≤ n - 1) contains two integers ai and bi (1 ≤ ai, bi ≤ n), showing that the i-th corridor connects rooms ai and bi.

The next line contains integer m (1 ≤ m ≤ 105) — the number of queries.

Next m lines describe the queries. The j-th of these lines (1 ≤ j ≤ m) contains two integers xj and yj (1 ≤ xj, yj ≤ n) that means that on the j-th day A will write the contest in the room xj, B will write in the room yj.

## 输出格式

In the i-th (1 ≤ i ≤ m) line print the number of rooms that are equidistant from the rooms where A and B write contest on the i-th day.

## 样例

### 样例 1

**输入**:
```
4
1 2
1 3
2 4
1
2 3
```

**输出**:
```
1
```

### 样例 2

**输入**:
```
4
1 2
2 3
2 4
2
1 2
1 3
```

**输出**:
```
0
2
```

## 备注

in the first sample there is only one room at the same distance from rooms number 2 and 3 — room number 1.

