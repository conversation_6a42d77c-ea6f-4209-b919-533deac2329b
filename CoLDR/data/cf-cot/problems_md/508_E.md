# Arthur and Brackets

**题目ID**: 508/E  
**比赛**: Codeforces Round 288 (Div. 2)  
**年份**: 2015  
**时间限制**: 2.0秒  
**内存限制**: 128MB  

## 题目描述

Notice that the memory limit is non-standard.

Recently <PERSON> and <PERSON> have studied correct bracket sequences. <PERSON> understood this topic perfectly and become so amazed about correct bracket sequences, so he even got himself a favorite correct bracket sequence of length 2n. Unlike <PERSON>, <PERSON> understood the topic very badly, and broke <PERSON>'s favorite correct bracket sequence just to spite him.

All <PERSON> remembers about his favorite sequence is for each opening parenthesis ('(') the approximate distance to the corresponding closing one (')'). For the i-th opening bracket he remembers the segment [li, ri], containing the distance to the corresponding closing bracket.

Formally speaking, for the i-th opening bracket (in order from left to right) we know that the difference of its position and the position of the corresponding closing bracket belongs to the segment [li, ri].

Help <PERSON> restore his favorite correct bracket sequence!

## 输入格式

The first line contains integer n (1 ≤ n ≤ 600), the number of opening brackets in <PERSON>'s favorite correct bracket sequence.

Next n lines contain numbers li and ri (1 ≤ li ≤ ri < 2n), representing the segment where lies the distance from the i-th opening bracket and the corresponding closing one.

The descriptions of the segments are given in the order in which the opening brackets occur in <PERSON>'s favorite sequence if we list them from left to right.

## 输出格式

If it is possible to restore the correct bracket sequence by the given data, print any possible choice.

If <PERSON> got something wrong, and there are no sequences corresponding to the given information, print a single line "IMPOSSIBLE" (without the quotes).

## 样例

### 样例 1

**输入**:
```
4
1 1
1 1
1 1
1 1
```

**输出**:
```
()()()()
```

### 样例 2

**输入**:
```
3
5 5
3 3
1 1
```

**输出**:
```
((()))
```

### 样例 3

**输入**:
```
3
5 5
3 3
2 2
```

**输出**:
```
IMPOSSIBLE
```

### 样例 4

**输入**:
```
3
2 3
1 4
1 4
```

**输出**:
```
(())()
```

