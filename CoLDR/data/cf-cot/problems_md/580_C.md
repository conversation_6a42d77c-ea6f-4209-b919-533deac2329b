# Ke<PERSON> and Park

**题目ID**: 580/C  
**比赛**: Codeforces Round 321 (Div. 2)  
**年份**: 2015  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON><PERSON> decided to celebrate his first big salary by going to the restaurant.

He lives by an unusual park. The park is a rooted tree consisting of n vertices with the root at vertex 1. Vertex 1 also contains <PERSON><PERSON>'s house. Unfortunaely for our hero, the park also contains cats. <PERSON><PERSON> has already found out what are the vertices with cats in them.

The leaf vertices of the park contain restaurants. <PERSON><PERSON> wants to choose a restaurant where he will go, but unfortunately he is very afraid of cats, so there is no way he will go to the restaurant if the path from the restaurant to his house contains more than m consecutive vertices with cats.

Your task is to help <PERSON><PERSON> count the number of restaurants where he can go.

## 输入格式

The first line contains two integers, n and m (2 ≤ n ≤ 105, 1 ≤ m ≤ n) — the number of vertices of the tree and the maximum number of consecutive vertices with cats that is still ok for <PERSON><PERSON>.

The second line contains n integers a1, a2, ..., an, where each ai either equals to 0 (then vertex i has no cat), or equals to 1 (then vertex i has a cat).

Next n - 1 lines contains the edges of the tree in the format "xi yi" (without the quotes) (1 ≤ xi, yi ≤ n, xi ≠ yi), where xi and yi are the vertices of the tree, connected by an edge.

It is guaranteed that the given set of edges specifies a tree.

## 输出格式

A single integer — the number of distinct leaves of a tree the path to which from Kefa's home contains at most m consecutive vertices with cats.

## 样例

### 样例 1

**输入**:
```
4 1
1 1 0 0
1 2
1 3
1 4
```

**输出**:
```
2
```

### 样例 2

**输入**:
```
7 1
1 0 1 1 0 0 0
1 2
1 3
2 4
2 5
3 6
3 7
```

**输出**:
```
2
```

## 备注

Let us remind you that a tree is a connected graph on n vertices and n - 1 edge. A rooted tree is a tree with a special vertex called root. In a rooted tree among any two vertices connected by an edge, one vertex is a parent (the one closer to the root), and the other one is a child. A vertex is called a leaf, if it has no children.

Note to the first sample test:  The vertices containing cats are marked red. The restaurants are at vertices 2, 3, 4. Kefa can't go only to the restaurant located at vertex 2.

Note to the second sample test:  The restaurants are located at vertices 4, 5, 6, 7. Kefa can't go to restaurants 6, 7.

## 题解

Будем спускаться по дереву от корня, поддерживая дополнительный параметр k — количество встреченных подряд вершин с котами. Если k превысило m, то выходим. Тогда ответ — это количество листьев, до которых мы смогли дойти.
Асимптотика — O(n).

