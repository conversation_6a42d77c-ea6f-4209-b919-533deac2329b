# Randomizer

**题目ID**: 559/D  
**比赛**: Codeforces Round 313 (Div. 1)  
**年份**: 2015  
**时间限制**: 2.5秒  
**内存限制**: 256MB  

## 题目描述

<PERSON> got tired of playing board games with the usual six-sided die, and he bought a toy called Randomizer. It functions as follows.

A Randomizer has its own coordinate plane on which a strictly convex polygon is painted, the polygon is called a basic polygon. If you shake a Randomizer, it draws some nondegenerate (i.e. having a non-zero area) convex polygon with vertices at some vertices of the basic polygon. The result of the roll (more precisely, the result of the shaking) is considered to be the number of points with integer coordinates, which were strictly inside (the points on the border are not considered) the selected polygon. Now <PERSON> is wondering: what is the expected result of shaking the Randomizer?

During the shaking the Randomizer considers all the possible non-degenerate convex polygons with vertices at the vertices of the basic polygon. Let's assume that there are k versions of the polygons. Then the Randomizer chooses each of them with probability $$\frac{1}{k}$$.

## 输入格式

The first line of the input contains a single integer n (3 ≤ n ≤ 100 000) — the number of vertices of the basic polygon.

Next n lines contain the coordinates of the vertices of the basic polygon. The i-th of these lines contain two integers xi and yi ( - 109 ≤ xi, yi ≤ 109) — the coordinates of the i-th vertex of the polygon. The vertices are given in the counter-clockwise order.

## 输出格式

Print the sought expected value with absolute or relative error at most 10 - 9.

## 样例

### 样例 1

**输入**:
```
4
0 0
2 0
2 2
0 2
```

**输出**:
```
0.2
```

### 样例 2

**输入**:
```
5
0 0
2 0
2 2
1 3
0 2
```

**输出**:
```
0.8125
```

## 备注

A polygon is called strictly convex if it is convex and no its vertices lie on the same line.

Let's assume that a random variable takes values x1, ..., xn with probabilities p1, ..., pn, correspondingly. Then the expected value of this variable equals to $$\sum_{i=1}^{n} x_{i} p_{i}$$.

