# Robots protection

**题目ID**: 575/I  
**比赛**: Bubble Cup 8 - Finals [Online Mirror]  
**年份**: 2015  
**时间限制**: 1.5秒  
**内存限制**: 512MB  

## 题目描述

Company "Robots industries" produces robots for territory protection. Robots protect triangle territories — right isosceles triangles with catheti parallel to North-South and East-West directions.

Owner of some land buys and sets robots on his territory to protect it. From time to time, businessmen want to build offices on that land and want to know how many robots will guard it. You are to handle these queries.

## 输入格式

The first line contains integer N — width and height of the land, and integer Q — number of queries to handle.

Next Q lines contain queries you need to process.

Two types of queries:

1. 1 dir x y len — add a robot to protect a triangle. Depending on the value of dir, the values of x, y and len represent a different triangle:   dir = 1: Triangle is defined by the points (x, y), (x + len, y), (x, y + len)  dir = 2: Triangle is defined by the points (x, y), (x + len, y), (x, y - len)  dir = 3: Triangle is defined by the points (x, y), (x - len, y), (x, y + len)  dir = 4: Triangle is defined by the points (x, y), (x - len, y), (x, y - len)
2. 2 x y — output how many robots guard this point (robot guards a point if the point is inside or on the border of its triangle)

- 1 ≤ N ≤ 5000
- 1 ≤ Q ≤ 105
- 1 ≤ dir ≤ 4
- All points of triangles are within range [1, N]
- All numbers are positive integers

## 输出格式

For each second type query output how many robots guard this point. Each answer should be in a separate line.

## 样例

### 样例 1

**输入**:
```
17 10
1 1 3 2 4
1 3 10 3 7
1 2 6 8 2
1 3 9 4 2
2 4 4
1 4 15 10 6
2 7 7
2 9 4
2 12 2
2 13 8
```

**输出**:
```
2
2
2
0
1
```

