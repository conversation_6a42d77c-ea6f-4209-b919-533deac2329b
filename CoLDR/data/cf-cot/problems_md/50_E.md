# Square Equation Roots

**题目ID**: 50/E  
**比赛**: Codeforces Beta Round 47  
**年份**: 2010  
**时间限制**: 5.0秒  
**内存限制**: 256MB  

## 题目描述

A schoolboy <PERSON><PERSON> studies square equations. The equations that are included in the school curriculum, usually look simple:

x2 + 2bx + c = 0

<PERSON><PERSON> noticed that some equations have two real roots, some of them have only one root and some equations don't have real roots at all. Moreover it turned out that several different square equations can have a common root.

<PERSON><PERSON> is interested in how many different real roots have all the equations of the type described above for all the possible pairs of numbers b and c such that 1 ≤ b ≤ n, 1 ≤ c ≤ m. Help <PERSON><PERSON> find that number.

## 输入格式

The single line contains two integers n and m. (1 ≤ n, m ≤ 5000000).

## 输出格式

Print a single number which is the number of real roots of the described set of equations.

## 样例

### 样例 1

**输入**:
```
3 3
```

**输出**:
```
12
```

### 样例 2

**输入**:
```
1 2
```

**输出**:
```
1
```

## 备注

In the second test from the statement the following equations are analysed:

b = 1, c = 1: x2 + 2x + 1 = 0; The root is x =  - 1

b = 1, c = 2: x2 + 2x + 2 = 0; No roots

Overall there's one root

In the second test the following equations are analysed:

b = 1, c = 1: x2 + 2x + 1 = 0; The root is x =  - 1

b = 1, c = 2: x2 + 2x + 2 = 0; No roots

b = 1, c = 3: x2 + 2x + 3 = 0; No roots

b = 2, c = 1: x2 + 4x + 1 = 0; The roots are $$x_{1}=-2-\sqrt{3}, x_{2}=-2+\sqrt{3}$$

b = 2, c = 2: x2 + 4x + 2 = 0; The roots are $$x_{1}=-2-\sqrt{2}, x_{2}=-2+\sqrt{2}$$

b = 2, c = 3: x2 + 4x + 3 = 0; The roots are x1 =  - 3, x2 =  - 1

b = 3, c = 1: x2 + 6x + 1 = 0; The roots are $$x_{1}=-3-2\sqrt{2}, x_{2}=-3+2\sqrt{2}$$

b = 3, c = 2: x2 + 6x + 2 = 0; The roots are $$x_{1}=-3-\sqrt{7}, x_{2}=-3+\sqrt{7}$$

b = 3, c = 3: x2 + 6x + 3 = 0; The roots are $$x_{1}=-3-\sqrt{6}, x_{2}=-3+\sqrt{6}$$ Overall there are 13 roots and as the root  - 1 is repeated twice, that means there are 12 different roots.

