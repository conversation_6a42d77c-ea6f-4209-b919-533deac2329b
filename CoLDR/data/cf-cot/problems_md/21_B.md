# Intersection

**题目ID**: 21/B  
**比赛**: Codeforces Alpha Round 21 (Codeforces format)  
**年份**: 2010  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

You are given two set of points. The first set is determined by the equation A1x + B1y + C1 = 0, and the second one is determined by the equation A2x + B2y + C2 = 0.

Write the program which finds the number of points in the intersection of two given sets.

## 输入格式

The first line of the input contains three integer numbers A1, B1, C1 separated by space. The second line contains three integer numbers A2, B2, C2 separated by space. All the numbers are between -100 and 100, inclusive.

## 输出格式

Print the number of points in the intersection or -1 if there are infinite number of points.

## 样例

### 样例 1

**输入**:
```
1 1 0
2 2 0
```

**输出**:
```
-1
```

### 样例 2

**输入**:
```
1 1 0
2 -2 0
```

**输出**:
```
1
```

