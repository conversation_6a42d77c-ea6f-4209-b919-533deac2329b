# <PERSON><PERSON> and XOR

**题目ID**: 504/D  
**比赛**: Codeforces Round 285 (Div. 1)  
**年份**: 2015  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

After <PERSON><PERSON>'s birthday he had many large numbers left, scattered across the room. Now it's time to clean up and <PERSON><PERSON> needs to put them in a basket. He ordered this task to his pet robot that agreed to complete the task at certain conditions. Before the robot puts a number x to the basket, <PERSON><PERSON> should answer the question: is it possible to choose one or multiple numbers that already are in the basket, such that their XOR sum equals x?

If the answer is positive, you also need to give the indexes of these numbers. If there are multiple options of choosing numbers, you are allowed to choose any correct option. After <PERSON><PERSON>'s answer the robot puts the number to the basket.

Initially the basket is empty. Each integer you put in the basket takes some number. The first integer you put into the basket take number 0, the second integer takes number 1 and so on.

<PERSON><PERSON> needs to clean up the place as soon as possible but unfortunately, he isn't that good at mathematics. He asks you to help him.

## 输入格式

The first line contains number m (1 ≤ m ≤ 2000), showing how many numbers are scattered around the room.

The next m lines contain the numbers in the order in which the robot puts them in the basket. Each number is a positive integer strictly less than 10600 that doesn't contain leading zeroes.

## 输出格式

For each number either print a 0 on the corresponding line, if the number cannot be represented as a XOR sum of numbers that are in the basket, or print integer k showing how many numbers are in the representation and the indexes of these numbers. Separate the numbers by spaces. Each number can occur in the representation at most once.

## 样例

### 样例 1

**输入**:
```
7
7
6
5
4
3
2
1
```

**输出**:
```
0
0
0
3 0 1 2
2 1 2
2 0 2
2 0 1
```

### 样例 2

**输入**:
```
2
5
5
```

**输出**:
```
0
1 0
```

## 备注

The XOR sum of numbers is the result of bitwise sum of numbers modulo 2.

