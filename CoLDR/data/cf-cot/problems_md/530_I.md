# Different variables

**题目ID**: 530/I  
**比赛**: VK Cup 2015 - Wild Card Round 1  
**年份**: 2015  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

N variables X1, ..., XN can have positive integer values. You are given K constraints for these value that look like "the values of variables Xi1, Xi2, ..., XiM are different". Among all possible lists of values of these variables that satisfy these constraints select the ones which have minimum possible max(Xi). Output lexicographically least of these lists.

## 输入格式

The first line of input contains two integers n and k (2 ≤ N ≤ 10, 1 ≤ K ≤ 100) — the number of variables and the number of constraints.

The following K lines contain the constraints, formatted as follows: the first number in the line M (2 ≤ M ≤ N) gives the number of variables in the constraint. After it follow M space-separated integers i1, ..., iM — the indices of the variables used in the constraint (1 ≤ ij ≤ N). All ij in one constraint are different.

## 输出格式

Output the values of X1, X2, ..., XN in a single line, separated with single spaces.

## 样例

### 样例 1

**输入**:
```
2 1
2 1 2
```

**输出**:
```
1 2
```

### 样例 2

**输入**:
```
3 2
2 1 2
2 2 3
```

**输出**:
```
1 2 1
```

