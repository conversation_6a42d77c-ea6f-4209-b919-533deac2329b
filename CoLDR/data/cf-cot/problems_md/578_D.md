# LCS Again

**题目ID**: 578/D  
**比赛**: Codeforces Round 320 (Div. 1) [<PERSON><PERSON> Thanks-Round]  
**年份**: 2015  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

You are given a string S of length n with each character being one of the first m lowercase English letters.

Calculate how many different strings T of length n composed from the first m lowercase English letters exist such that the length of LCS (longest common subsequence) between S and T is n - 1.

Recall that LCS of two strings S and T is the longest string C such that C both in S and T as a subsequence.

## 输入格式

The first line contains two numbers n and m denoting the length of string S and number of first English lowercase characters forming the character set for strings (1 ≤ n ≤ 100 000, 2 ≤ m ≤ 26).

The second line contains string S.

## 输出格式

Print the only line containing the answer.

## 样例

### 样例 1

**输入**:
```
3 3
aaa
```

**输出**:
```
6
```

### 样例 2

**输入**:
```
3 3
aab
```

**输出**:
```
11
```

### 样例 3

**输入**:
```
1 2
a
```

**输出**:
```
1
```

### 样例 4

**输入**:
```
10 9
abacadefgh
```

**输出**:
```
789
```

## 备注

For the first sample, the 6 possible strings T are: aab, aac, aba, aca, baa, caa.

For the second sample, the 11 possible strings T are: aaa, aac, aba, abb, abc, aca, acb, baa, bab, caa, cab.

For the third sample, the only possible string T is b.

