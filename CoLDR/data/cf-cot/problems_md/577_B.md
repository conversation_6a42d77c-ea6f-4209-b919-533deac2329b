# Mo<PERSON>lo Sum

**题目ID**: 577/B  
**比赛**: Codeforces Round 319 (Div. 2)  
**年份**: 2015  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

You are given a sequence of numbers a1, a2, ..., an, and a number m.

Check if it is possible to choose a non-empty subsequence aij such that the sum of numbers in this subsequence is divisible by m.

## 输入格式

The first line contains two numbers, n and m (1 ≤ n ≤ 106, 2 ≤ m ≤ 103) — the size of the original sequence and the number such that sum should be divisible by it.

The second line contains n integers a1, a2, ..., an (0 ≤ ai ≤ 109).

## 输出格式

In the single line print either "YES" (without the quotes) if there exists the sought subsequence, or "NO" (without the quotes), if such subsequence doesn't exist.

## 样例

### 样例 1

**输入**:
```
3 5
1 2 3
```

**输出**:
```
YES
```

### 样例 2

**输入**:
```
1 6
5
```

**输出**:
```
NO
```

### 样例 3

**输入**:
```
4 6
3 1 1 3
```

**输出**:
```
YES
```

### 样例 4

**输入**:
```
6 6
5 5 5 5 5 5
```

**输出**:
```
YES
```

## 备注

In the first sample test you can choose numbers 2 and 3, the sum of which is divisible by 5.

In the second sample test the single non-empty subsequence of numbers is a single number 5. Number 5 is not divisible by 6, that is, the sought subsequence doesn't exist.

In the third sample test you need to choose two numbers 3 on the ends.

In the fourth sample test you can take the whole subsequence.

