# Creating Words

**题目ID**: 1985/A  
**比赛**: Codeforces Round 952 (Div. 4)  
**年份**: 2024  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON> is given two strings $$$a$$$ and $$$b$$$, both of length $$$3$$$. He thinks it's particularly funny to create two new words by swapping the first character of $$$a$$$ with the first character of $$$b$$$. He wants you to output $$$a$$$ and $$$b$$$ after the swap.

Note that the new words may not necessarily be different.

## 输入格式

The first line contains $$$t$$$ ($$$1 \leq t \leq 100$$$)  — the number of test cases.

The first and only line of each test case contains two space-separated strings, $$$a$$$ and $$$b$$$, both of length $$$3$$$. The strings only contain lowercase Latin letters.

## 输出格式

For each test case, after the swap, output $$$a$$$ and $$$b$$$, separated by a space.

## 样例

### 样例 1

**输入**:
```
6
bit set
cat dog
hot dog
uwu owo
cat cat
zzz zzz
```

**输出**:
```
sit bet
dat cog
dot hog
owu uwo
cat cat
zzz zzz
```

