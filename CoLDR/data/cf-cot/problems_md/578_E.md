# Walking!

**题目ID**: 578/E  
**比赛**: Codeforces Round 320 (Div. 1) [<PERSON><PERSON> Thanks-Round]  
**年份**: 2015  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

There is a sand trail in front of <PERSON>'s home.

In daytime, people walk over it and leave a footprint on the trail for their every single step. Alice cannot distinguish the order of the footprints, but she can tell whether each footprint is made by left foot or right foot. Also she's certain that all people are walking by alternating left foot and right foot.

For example, suppose that one person walked through the trail and left some footprints. The footprints are RRLRL in order along the trail ('R' means right foot and 'L' means left foot). You might think the outcome of the footprints is strange. But in fact, some steps are resulting from walking backwards!

There are some possible order of steps that produce these footprints such as 1 → 3 → 2 → 5 → 4 or 2 → 3 → 4 → 5 → 1 (we suppose that the distance between two consecutive steps can be arbitrarily long). The number of backward steps from above two examples are 2 and 1 separately.

Alice is interested in these footprints. Whenever there is a person walking trough the trail, she takes a picture of all these footprints along the trail and erase all of them so that next person will leave a new set of footprints. We know that people walk by alternating right foot and left foot, but we don't know if the first step is made by left foot or right foot.

Alice wants to know the minimum possible number of backward steps made by a person. But it's a little hard. Please help Alice to calculate it. You also need to construct one possible history of these footprints.

## 输入格式

Only one line containing the string S (1 ≤ |S| ≤ 100 000) containing all footprints in order along the trail from entrance to exit.

It is guaranteed that there is at least one possible footprint history.

## 输出格式

You should output 2 lines.

The first line should contain a number denoting the minimum number of backward steps.

The second line should contain a permutation of integers from 1 to |S|. This permutation should denote the order of footprints that may possible be used by person walked there.

If there are several possible answers, you may output any of them.

## 样例

### 样例 1

**输入**:
```
RRLRL
```

**输出**:
```
1
2 5 1 3 4
```

### 样例 2

**输入**:
```
RLRLRLRLR
```

**输出**:
```
0
1 2 3 4 5 6 7 8 9
```

### 样例 3

**输入**:
```
RRRRRLLLL
```

**输出**:
```
4
4 9 3 8 2 7 1 6 5
```

## 备注

For the first sample, one possible order is 2 → 5 → 1 → 3 → 4, among them only the step 5 → 1 is backward step so the answer is 1.

For the second example one possible order is just to follow the order of input, thus there are no backward steps.

For the third sample, there will be 4 backward steps because every step from L to R will be a backward step.

