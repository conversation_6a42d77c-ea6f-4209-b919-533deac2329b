# Thorns and Coins

**题目ID**: 1932/A  
**比赛**: Codeforces Round 927 (Div. 3)  
**年份**: 2024  
**时间限制**: 1.0秒  
**内存限制**: 512MB  

## 题目描述

During your journey through computer universes, you stumbled upon a very interesting world. It is a path with $$$n$$$ consecutive cells, each of which can either be empty, contain thorns, or a coin. In one move, you can move one or two cells along the path, provided that the destination cell does not contain thorns (and belongs to the path). If you move to the cell with a coin, you pick it up.

Here, green arrows correspond to legal moves, and the red arrow corresponds to an illegal move.

You want to collect as many coins as possible. Find the maximum number of coins you can collect in the discovered world if you start in the leftmost cell of the path.

## 输入格式

The first line of input contains a single integer $$$t$$$ ($$$1 \le t \le 1000$$$) — the number of test cases. Then the descriptions of the test cases follow.

The first line of each test case contains a single integer $$$n$$$ ($$$1 \le n \le 50$$$) — the length of the path.

The second line of each test case contains a string of $$$n$$$ characters, the description of the path. The character '.' denotes an empty cell, '@' denotes a cell with a coin, and '*' denotes a cell with thorns. It is guaranteed that the first cell is empty.

## 输出格式

For each test case, output a single integer, the maximum number of coins you can collect.

## 样例

### 样例 1

**输入**:
```
3
10
.@@*@.**@@
5
.@@@@
15
.@@..@***..@@@*
```

**输出**:
```
3
4
3
```

## 备注

The picture for the first example is in the problem statement.

Here is the picture for the second example:

And here is the picture for the third example:

## 题解

Let's move forward by $$$1$$$ if the next cell does not have spikes, and by $$$2$$$ otherwise. By doing so, we will visit all spike-free cells that we can reach, and thus collect all the coins in those cells. Note that if we are in cell $$$i$$$, and cells $$$i+1$$$ and $$$i+2$$$ have spikes, then we can only jump into the spikes and thus end the game.
Therefore, we need to count how many coins appear in the string before the substring "**".

