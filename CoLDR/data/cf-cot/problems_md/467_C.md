# <PERSON> and <PERSON>

**题目ID**: 467/C  
**比赛**: Codeforces Round 267 (Div. 2)  
**年份**: 2014  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

The new ITone 6 has been released recently and <PERSON> got really keen to buy it. Unfortunately, he didn't have enough money, so <PERSON> was going to work as a programmer. Now he faced the following problem at the work.

Given a sequence of n integers p1, p2, ..., pn. You are to choose k pairs of integers:

[l1, r1], [l2, r2], ..., [lk, rk] (1 ≤ l1 ≤ r1 < l2 ≤ r2 < ... < lk ≤ rk ≤ n; ri - li + 1 = m),

in such a way that the value of sum $$\sum_{i=1}^{k}\sum_{j=l_i}^{r_i}p_j$$ is maximal possible. Help <PERSON> to cope with the task.

## 输入格式

The first line contains three integers n, m and k (1 ≤ (m × k) ≤ n ≤ 5000). The second line contains n integers p1, p2, ..., pn (0 ≤ pi ≤ 109).

## 输出格式

Print an integer in a single line — the maximum possible value of sum.

## 样例

### 样例 1

**输入**:
```
5 2 1
1 2 3 4 5
```

**输出**:
```
9
```

### 样例 2

**输入**:
```
7 1 3
2 10 7 18 5 33 0
```

**输出**:
```
61
```

