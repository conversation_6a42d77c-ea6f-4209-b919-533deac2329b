# Amr and Chemistry

**题目ID**: 558/C  
**比赛**: Codeforces Round 312 (Div. 2)  
**年份**: 2015  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON><PERSON> loves Chemistry, and specially doing experiments. He is preparing for a new interesting experiment.

<PERSON><PERSON> has n different types of chemicals. Each chemical i has an initial volume of ai liters. For this experiment, <PERSON><PERSON> has to mix all the chemicals together, but all the chemicals volumes must be equal first. So his task is to make all the chemicals volumes equal.

To do this, <PERSON><PERSON> can do two different kind of operations.

- Choose some chemical i and double its current volume so the new volume will be 2ai
- Choose some chemical i and divide its volume by two (integer division) so the new volume will be $$\left[\frac{a_{i}}{2}\right]$$

Suppose that each chemical is contained in a vessel of infinite volume. Now <PERSON><PERSON> wonders what is the minimum number of operations required to make all the chemicals volumes equal?

## 输入格式

The first line contains one number n (1 ≤ n ≤ 105), the number of chemicals.

The second line contains n space separated integers ai (1 ≤ ai ≤ 105), representing the initial volume of the i-th chemical in liters.

## 输出格式

Output one integer the minimum number of operations required to make all the chemicals volumes equal.

## 样例

### 样例 1

**输入**:
```
3
4 8 2
```

**输出**:
```
2
```

### 样例 2

**输入**:
```
3
3 5 6
```

**输出**:
```
5
```

## 备注

In the first sample test, the optimal solution is to divide the second chemical volume by two, and multiply the third chemical volume by two to make all the volumes equal 4.

In the second sample test, the optimal solution is to divide the first chemical volume by two, and divide the second and the third chemical volumes by two twice to make all the volumes equal 1.

