# Partial Sums

**题目ID**: 223/C  
**比赛**: Codeforces Round 138 (Div. 1)  
**年份**: 2012  
**时间限制**: 4.0秒  
**内存限制**: 256MB  

## 题目描述

You've got an array a, consisting of n integers. The array elements are indexed from 1 to n. Let's determine a two step operation like that:

1. First we build by the array a an array s of partial sums, consisting of n elements. Element number i (1 ≤ i ≤ n) of array s equals $$s_i = \left( \sum_{j=1}^{i} a_j \right) \mod (10^9 + 7)$$. The operation x mod y means that we take the remainder of the division of number x by number y.
2. Then we write the contents of the array s to the array a. Element number i (1 ≤ i ≤ n) of the array s becomes the i-th element of the array a (ai = si).

You task is to find array a after exactly k described operations are applied.

## 输入格式

The first line contains two space-separated integers n and k (1 ≤ n ≤ 2000, 0 ≤ k ≤ 109). The next line contains n space-separated integers a1, a2, ..., an — elements of the array a (0 ≤ ai ≤ 109).

## 输出格式

Print n integers  — elements of the array a after the operations are applied to it. Print the elements in the order of increasing of their indexes in the array a. Separate the printed numbers by spaces.

## 样例

### 样例 1

**输入**:
```
3 1
1 2 3
```

**输出**:
```
1 3 6
```

### 样例 2

**输入**:
```
5 0
3 14 15 92 6
```

**输出**:
```
3 14 15 92 6
```

