# Toy Cars

**题目ID**: 545/A  
**比赛**: Codeforces Round 303 (Div. 2)  
**年份**: 2015  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON> <PERSON>, thanks to her older brother, likes to play with cars. Today she decided to set up a tournament between them. The process of a tournament is described in the next paragraph.

There are n toy cars. Each pair collides. The result of a collision can be one of the following: no car turned over, one car turned over, both cars turned over. A car is good if it turned over in no collision. The results of the collisions are determined by an n × n matrix А: there is a number on the intersection of the і-th row and j-th column that describes the result of the collision of the і-th and the j-th car:

- - 1: if this pair of cars never collided.  - 1 occurs only on the main diagonal of the matrix.
- 0: if no car turned over during the collision.
- 1: if only the i-th car turned over during the collision.
- 2: if only the j-th car turned over during the collision.
- 3: if both cars turned over during the collision.

<PERSON> wants to find all the good cars. She quickly determined which cars are good. Can you cope with the task?

## 输入格式

The first line contains integer n (1 ≤ n ≤ 100) — the number of cars.

Each of the next n lines contains n space-separated integers that determine matrix A.

It is guaranteed that on the main diagonal there are  - 1, and  - 1 doesn't appear anywhere else in the matrix.

It is guaranteed that the input is correct, that is, if <PERSON>j = 1, then Aji = 2, if Aij = 3, then Aji = 3, and if Aij = 0, then Aji = 0.

## 输出格式

Print the number of good cars and in the next line print their space-separated indices in the increasing order.

## 样例

### 样例 1

**输入**:
```
3
-1 0 0
0 -1 1
0 2 -1
```

**输出**:
```
2
1 3
```

### 样例 2

**输入**:
```
4
-1 3 3 3
3 -1 3 3
3 3 -1 3
3 3 3 -1
```

**输出**:
```
0
```

