# GCD Table

**题目ID**: 582/A  
**比赛**: Codeforces Round 323 (Div. 1)  
**年份**: 2015  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

The GCD table G of size n × n for an array of positive integers a of length n is defined by formula

$${ g _ { i j } = \operatorname* { g c d } ( a _ { i }, a _ { j } ). }$$

Let us remind you that the greatest common divisor (GCD) of two positive integers x and y is the greatest integer that is divisor of both x and y, it is denoted as $$\gcd(x,y)$$. For example, for array a = {4, 3, 6, 2} of length 4 the GCD table will look as follows:

Given all the numbers of the GCD table G, restore array a.

## 输入格式

The first line contains number n (1 ≤ n ≤ 500) — the length of array a. The second line contains n2 space-separated numbers — the elements of the GCD table of G for array a.

All the numbers in the table are positive integers, not exceeding 109. Note that the elements are given in an arbitrary order. It is guaranteed that the set of the input data corresponds to some array a.

## 输出格式

In the single line print n positive integers — the elements of array a. If there are multiple possible solutions, you are allowed to print any of them.

## 样例

### 样例 1

**输入**:
```
4
2 1 2 3 4 3 2 6 1 1 2 2 1 2 3 2
```

**输出**:
```
4 3 6 2
```

### 样例 2

**输入**:
```
1
42
```

**输出**:
```
42
```

### 样例 3

**输入**:
```
2
1 1 1 1
```

**输出**:
```
1 1
```

