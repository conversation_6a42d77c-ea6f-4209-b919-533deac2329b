# Planar Graph

**题目ID**: 223/E  
**比赛**: Codeforces Round 138 (Div. 1)  
**年份**: 2012  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

A graph is called planar, if it can be drawn in such a way that its edges intersect only at their vertexes.

An articulation point is such a vertex of an undirected graph, that when removed increases the number of connected components of the graph.

A bridge is such an edge of an undirected graph, that when removed increases the number of connected components of the graph.

You've got a connected undirected planar graph consisting of n vertexes, numbered from 1 to n, drawn on the plane. The graph has no bridges, articulation points, loops and multiple edges. You are also given q queries. Each query is a cycle in the graph. The query response is the number of graph vertexes, which (if you draw a graph and the cycle on the plane) are located either inside the cycle, or on it. Write a program that, given the graph and the queries, will answer each query.

## 输入格式

The first line contains two space-separated integers n and m (3 ≤ n, m ≤ 105) — the number of vertexes and edges of the graph. Next m lines contain the edges of the graph: the i-th line contains two space-separated integers ui and vi (1 ≤ ui, vi ≤ n) — the numbers of vertexes, connecting the i-th edge. The next n lines contain the positions of the planar graph vertexes on the plane: the i-th line contains a pair of space-separated integers xi and yi (|xi|, |yi| ≤ 109) — the coordinates of the i-th vertex of the graph on the plane.

The next line contains integer q (1 ≤ q ≤ 105) — the number of queries. Then follow q lines that describe the queries: the i-th line contains the sequence of space-separated integers ki, a1, a2, ..., aki (1 ≤ aj ≤ n; ki > 2), where ki is the cycle length in the i-th query, aj are numbers of the vertexes that form a cycle. The numbers of vertexes in the cycle are given in the clockwise or counterclockwise order. The given cycles are simple, that is they cannot go through a graph vertex more than once. The total length of all cycles in all queries does not exceed 105.

It is guaranteed that the given graph contains no bridges, articulation points, loops and multiple edges. It is guaranteed that the edge segments can have common points only at the graph's vertexes.

## 输出格式

For each query print a single integer — the number of vertexes inside the cycle or on it. Print the answers in the order, in which the queries follow in the input. Separate the numbers by spaces.

## 样例

### 样例 1

**输入**:
```
3 3
1 2
2 3
3 1
0 0
1 0
0 1
1
3 1 2 3
```

**输出**:
```
3
```

### 样例 2

**输入**:
```
5 8
1 2
2 3
3 4
4 1
1 5
2 5
3 5
4 5
0 0
2 0
2 2
0 2
1 1
1
4 1 2 3 4
```

**输出**:
```
5
```

### 样例 3

**输入**:
```
4 5
1 2
2 3
3 4
4 1
2 4
0 0
1 0
1 1
0 1
3
3 1 2 4
3 4 2 3
4 1 2 3 4
```

**输出**:
```
3
3
4
```

