# Deciphering

**题目ID**: 491/C  
**比赛**: Testing Round 11  
**年份**: 2014  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

One day <PERSON> found a <PERSON>'s piece of paper with a message dedicated to <PERSON><PERSON>. <PERSON> wants to know what is there in a message, but unfortunately the message is ciphered. <PERSON> knows that her students usually cipher their messages by replacing each letter of an original message by some another letter. Replacement works in such way that same letters are always replaced with some fixed letter, and different letters are always replaced by different letters.

<PERSON> supposed that the message contains answers to the final exam (since its length is equal to the number of final exam questions). On the other hand she knows that <PERSON>'s answer are not necessary correct. There are K possible answers for each questions. Of course, <PERSON> knows correct answers.

<PERSON> decided to decipher message in such way that the number of <PERSON>'s correct answers is maximum possible. She is very busy now, so your task is to help her.

## 输入格式

First line contains length of both strings N (1 ≤ N ≤ 2 000 000) and an integer K — number of possible answers for each of the questions (1 ≤ K ≤ 52). Answers to the questions are denoted as Latin letters abcde...xyzABCDE...XYZ in the order. For example for K = 6, possible answers are abcdef and for K = 30 possible answers are abcde...xyzABCD.

Second line contains a ciphered message string consisting of Latin letters.

Third line contains a correct answers string consisting of Latin letters.

## 输出格式

In the first line output maximum possible number of correct Sasha's answers.

In the second line output cipher rule as the string of length K where for each letter from the students' cipher (starting from 'a' as mentioned above) there is specified which answer does it correspond to.

If there are several ways to produce maximum answer, output any of them.

## 样例

### 样例 1

**输入**:
```
10 2
aaabbbaaab
bbbbabbbbb
```

**输出**:
```
7
ba
```

### 样例 2

**输入**:
```
10 2
aaaaaaabbb
bbbbaaabbb
```

**输出**:
```
6
ab
```

### 样例 3

**输入**:
```
9 4
dacbdacbd
acbdacbda
```

**输出**:
```
9
cdba
```

