# Eat the Chip

**题目ID**: 1921/E  
**比赛**: Codeforces Round 920 (Div. 3)  
**年份**: 2024  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON> and <PERSON> are playing a game on a checkered board. The board has $$$h$$$ rows, numbered from top to bottom, and $$$w$$$ columns, numbered from left to right. Both players have a chip each. Initially, <PERSON>'s chip is located at the cell with coordinates $$$(x_a, y_a)$$$ (row $$$x_a$$$, column $$$y_a$$$), and <PERSON>'s chip is located at $$$(x_b, y_b)$$$. It is guaranteed that the initial positions of the chips do not coincide. Players take turns making moves, with <PERSON> starting.

On her turn, <PERSON> can move her chip one cell down or one cell down-right or down-left (diagonally). <PERSON>, on the other hand, moves his chip one cell up, up-right, or up-left. It is not allowed to make moves that go beyond the board boundaries.

More formally, if at the beginning of <PERSON>'s turn she is in the cell with coordinates $$$(x_a, y_a)$$$, then she can move her chip to one of the cells $$$(x_a + 1, y_a)$$$, $$$(x_a + 1, y_a - 1)$$$, or $$$(x_a + 1, y_a + 1)$$$. Bob, on his turn, from the cell $$$(x_b, y_b)$$$ can move to $$$(x_b - 1, y_b)$$$, $$$(x_b - 1, y_b - 1)$$$, or $$$(x_b - 1, y_b + 1)$$$. The new chip coordinates $$$(x', y')$$$ must satisfy the conditions $$$1 \le x' \le h$$$ and $$$1 \le y' \le w$$$.

Example game state. Alice plays with the white chip, Bob with the black one. Arrows indicate possible moves.

A player immediately wins if they place their chip in a cell occupied by the other player's chip. If either player cannot make a move (Alice—if she is in the last row, i.e. $$$x_a = h$$$, Bob—if he is in the first row, i.e. $$$x_b = 1$$$), the game immediately ends in a draw.

What will be the outcome of the game if both opponents play optimally?

## 输入格式

Each test consists of multiple test cases. The first line contains a single integer $$$t$$$ ($$$1 \le t \le 10^4$$$) — the number of test cases. This is followed by the description of the test cases.

Each test case consists of a single line containing six integers $$$h$$$, $$$w$$$, $$$x_a$$$, $$$y_a$$$, $$$x_b$$$, $$$y_b$$$ ($$$1 \le x_a, x_b \le h \le 10^6$$$, $$$1 \le y_a, y_b \le w \le 10^9$$$) — the dimensions of the board and the initial positions of Alice's and Bob's chips. It is guaranteed that either $$$x_a \ne x_b$$$ or $$$y_a \ne y_b$$$.

It is guaranteed that the sum of $$$h$$$ over all test cases does not exceed $$$10^6$$$.

## 输出格式

For each test case, output "Alice" if Alice wins, "Bob" if Bob wins, and "Draw" if neither player can secure a victory. You can output each letter in any case (lowercase or uppercase). For example, the strings "bOb", "bob", "Bob", and "BOB" will be accepted as Bob's victory.

## 样例

### 样例 1

**输入**:
```
12
6 5 2 2 5 3
4 1 2 1 4 1
1 4 1 3 1 1
5 5 1 4 5 2
4 4 1 1 4 4
10 10 1 6 10 8
10 10 2 6 10 7
10 10 9 1 8 1
10 10 8 1 10 2
10 10 1 1 2 1
10 10 1 3 4 1
10 10 3 1 1 1
```

**输出**:
```
Alice
Bob
Draw
Draw
Draw
Alice
Draw
Draw
Bob
Alice
Alice
Draw
```

## 题解

First, let's note that the difference $$$x_b - x_a$$$ decreases exactly by one each (both Alice's and Bob's) turn. In the end, if one of the players was able to win, $$$x_b - x_a = 0$$$. In particular, that means that if $$$x_a - x_b$$$ is initially odd then only Alice has a chance of winning the match and vice versa.
Suppose that $$$x_a - x_b$$$ is initially even (the outcome of the match could be either Bob's win or draw). If $$$x_a \ge x_b$$$ the answer is immediately draw. Otherwise, the players will make $$$t = (x_b - x_a) / 2$$$ moves each before $$$x_a = x_b$$$.
If at some point during these $$$2t$$$ moves Bob can achieve $$$y_a = y_b$$$, he is winning as he can continue with symmetrical responses to Alice's turns.
If $$$y_a > y_b$$$ and Bob cannot reach right border ($$$w > y_b + t$$$), Alice can always choose the rightmost option for her and after each of $$$2t$$$ moves $$$y_a$$$ will be greater than $$$y_b$$$ which means Bob cannot win. Otherwise, if Bob always chooses the rightmost option for him, he will eventually achieve $$$y_a = y_b$$$.
The case when $$$y_a$$$ is initially less than $$$y_b$$$ as well as the case when Alice has a chance to win ($$$x_b - x_a$$$ is odd) can be covered in a similar way.

