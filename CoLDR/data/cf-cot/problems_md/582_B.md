# Once Again...

**题目ID**: 582/B  
**比赛**: Codeforces Round 323 (Div. 1)  
**年份**: 2015  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

You are given an array of positive integers a1, a2, ..., an × T of length n × T. We know that for any i > n it is true that ai = ai - n. Find the length of the longest non-decreasing sequence of the given array.

## 输入格式

The first line contains two space-separated integers: n, T (1 ≤ n ≤ 100, 1 ≤ T ≤ 107). The second line contains n space-separated integers a1, a2, ..., an (1 ≤ ai ≤ 300).

## 输出格式

Print a single number — the length of a sought sequence.

## 样例

### 样例 1

**输入**:
```
4 3
3 1 4 2
```

**输出**:
```
5
```

## 备注

The array given in the sample looks like that: 3, 1, 4, 2, 3, 1, 4, 2, 3, 1, 4, 2. The elements in bold form the largest non-decreasing subsequence.

