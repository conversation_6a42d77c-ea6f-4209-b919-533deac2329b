# Multiplication Table

**题目ID**: 577/A  
**比赛**: Codeforces Round 319 (Div. 2)  
**年份**: 2015  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

Let's consider a table consisting of n rows and n columns. The cell located at the intersection of i-th row and j-th column contains number i × j. The rows and columns are numbered starting from 1.

You are given a positive integer x. Your task is to count the number of cells in a table that contain number x.

## 输入格式

The single line contains numbers n and x (1 ≤ n ≤ 105, 1 ≤ x ≤ 109) — the size of the table and the number that we are looking for in the table.

## 输出格式

Print a single number: the number of times x occurs in the table.

## 样例

### 样例 1

**输入**:
```
10 5
```

**输出**:
```
2
```

### 样例 2

**输入**:
```
6 12
```

**输出**:
```
4
```

### 样例 3

**输入**:
```
5 13
```

**输出**:
```
0
```

## 备注

A table for the second sample test is given below. The occurrences of number 12 are marked bold.

