# Pillars

**题目ID**: 474/E  
**比赛**: Codeforces Round 271 (Div. 2)  
**年份**: 2014  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON><PERSON> found a row with n pillars. The i-th pillar has the height of hi meters. Starting from one pillar i1, <PERSON><PERSON> wants to jump on the pillars i2, ..., ik. (1 ≤ i1 < i2 < ... < ik ≤ n). From a pillar i <PERSON><PERSON> can jump on a pillar j only if i < j and |hi - hj| ≥ d, where |x| is the absolute value of the number x.

Now <PERSON><PERSON> is asking you find out a jump sequence with maximal length and print it.

## 输入格式

The first line contains two integers n and d (1 ≤ n ≤ 105, 0 ≤ d ≤ 109).

The second line contains n numbers h1, h2, ..., hn (1 ≤ hi ≤ 1015).

## 输出格式

The first line should contain one integer k, the maximal length of a jump sequence.

The second line should contain k integers i1, i2, ..., ik (1 ≤ i1 < i2 < ... < ik ≤ n), representing the pillars' indices from the maximal length jump sequence.

If there is more than one maximal length jump sequence, print any.

## 样例

### 样例 1

**输入**:
```
5 2
1 3 6 7 4
```

**输出**:
```
4
1 2 3 5
```

### 样例 2

**输入**:
```
10 3
2 1 3 6 9 11 7 3 20 18
```

**输出**:
```
6
1 4 6 7 8 9
```

## 备注

In the first example Marmot chooses the pillars 1, 2, 3, 5 with the heights 1, 3, 6, 4. Another jump sequence of length 4 is 1, 2, 4, 5.

