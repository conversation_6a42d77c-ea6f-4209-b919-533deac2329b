# Anniversary

**题目ID**: 226/C  
**比赛**: Codeforces Round 140 (Div. 1)  
**年份**: 2012  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

There are less than 60 years left till the 900-th birthday anniversary of a famous Italian mathematician <PERSON>. Of course, such important anniversary needs much preparations.

<PERSON><PERSON> is sure that it'll be great to learn to solve the following problem by the Big Day: You're given a set A, consisting of numbers l, l + 1, l + 2, ..., r; let's consider all its k-element subsets; for each such subset let's find the largest common divisor of Fibonacci numbers with indexes, determined by the subset elements. Among all found common divisors, <PERSON><PERSON> is interested in the largest one.

<PERSON><PERSON> asked to remind you that Fibonacci numbers are elements of a numeric sequence, where F1 = 1, F2 = 1, Fn = Fn - 1 + Fn - 2 for n ≥ 3.

<PERSON><PERSON> has more than half a century ahead to solve the given task, but you only have two hours. Count the residue from dividing the sought largest common divisor by m.

## 输入格式

The first line contains four space-separated integers m, l, r and k (1 ≤ m ≤ 109; 1 ≤ l < r ≤ 1012; 2 ≤ k ≤ r - l + 1).

Please, do not use the %lld specifier to read or write 64-bit integers in С++. It is preferred to use cin, cout streams or the %I64d specifier.

## 输出格式

Print a single integer — the residue from dividing the sought greatest common divisor by m.

## 样例

### 样例 1

**输入**:
```
10 1 8 2
```

**输出**:
```
3
```

### 样例 2

**输入**:
```
10 1 8 3
```

**输出**:
```
1
```

