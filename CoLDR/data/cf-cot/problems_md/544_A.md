# Set of Strings

**题目ID**: 544/A  
**比赛**: Codeforces Round 302 (Div. 2)  
**年份**: 2015  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

You are given a string q. A sequence of k strings s1, s2, ..., sk is called beautiful, if the concatenation of these strings is string q (formally, s1 + s2 + ... + sk = q) and the first characters of these strings are distinct.

Find any beautiful sequence of strings or determine that the beautiful sequence doesn't exist.

## 输入格式

The first line contains a positive integer k (1 ≤ k ≤ 26) — the number of strings that should be in a beautiful sequence.

The second line contains string q, consisting of lowercase Latin letters. The length of the string is within range from 1 to 100, inclusive.

## 输出格式

If such sequence doesn't exist, then print in a single line "NO" (without the quotes). Otherwise, print in the first line "YES" (without the quotes) and in the next k lines print the beautiful sequence of strings s1, s2, ..., sk.

If there are multiple possible answers, print any of them.

## 样例

### 样例 1

**输入**:
```
1
abca
```

**输出**:
```
YES
abca
```

### 样例 2

**输入**:
```
2
aaacas
```

**输出**:
```
YES
aaa
cas
```

### 样例 3

**输入**:
```
4
abc
```

**输出**:
```
NO
```

## 备注

In the second sample there are two possible answers: {"aaaca", "s"} and {"aaa", "cas"}.

