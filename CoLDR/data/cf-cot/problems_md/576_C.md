# Points on Plane

**题目ID**: 576/C  
**比赛**: Codeforces Round 319 (Div. 1)  
**年份**: 2015  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

On a plane are n points (xi, yi) with integer coordinates between 0 and 106. The distance between the two points with numbers a and b is said to be the following value: $$\operatorname{dist}(a,b)=|x_{a}-x_{b}|+|y_{a}-y_{b}|$$ (the distance calculated by such formula is called Manhattan distance).

We call a hamiltonian path to be some permutation pi of numbers from 1 to n. We say that the length of this path is value $$\sum_{i=1}^{n-1} \text{dist}(p_i, p_{i+1})$$.

Find some hamiltonian path with a length of no more than 25 × 108. Note that you do not have to minimize the path length.

## 输入格式

The first line contains integer n (1 ≤ n ≤ 106).

The i + 1-th line contains the coordinates of the i-th point: xi and yi (0 ≤ xi, yi ≤ 106).

It is guaranteed that no two points coincide.

## 输出格式

Print the permutation of numbers pi from 1 to n — the sought Hamiltonian path. The permutation must meet the inequality $${ \sum _ { i = 1 } ^ { n - 1 } \operatorname { d i s t } ( p _ { i }, p _ { i + 1 } ) } \leq 2 5 \times 1 0 ^ { 8 }$$.

If there are multiple possible answers, print any of them.

It is guaranteed that the answer exists.

## 样例

### 样例 1

**输入**:
```
5
0 7
8 10
3 4
5 0
9 12
```

**输出**:
```
4 3 1 2 5
```

## 备注

In the sample test the total distance is:

$$\text{dist}(4,3)+\text{dist}(3,1)+\text{dist}(1,2)+\text{dist}(2,5)=$$

(|5 - 3| + |0 - 4|) + (|3 - 0| + |4 - 7|) + (|0 - 8| + |7 - 10|) + (|8 - 9| + |10 - 12|) = 2 + 4 + 3 + 3 + 8 + 3 + 1 + 2 = 26

