# Chess

**题目ID**: 57/E  
**比赛**: Codeforces Beta Round 53  
**年份**: 2011  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

Brian the <PERSON> adores chess. Not long ago he argued with <PERSON><PERSON><PERSON> the Rabbit that a knight is better than a king. To prove his point he tries to show that the knight is very fast but <PERSON><PERSON><PERSON> doesn't accept statements without evidence. He constructed an infinite chessboard for <PERSON>, where he deleted several squares to add some more interest to the game. <PERSON> only needs to count how many different board squares a knight standing on a square with coordinates of (0, 0) can reach in no more than k moves. Naturally, it is forbidden to move to the deleted squares.

<PERSON> doesn't very much like exact sciences himself and is not acquainted with programming, that's why he will hardly be able to get ahead of <PERSON><PERSON><PERSON> who has already started solving the problem. Help <PERSON> to solve the problem faster than <PERSON><PERSON><PERSON>.

## 输入格式

The first line contains two integers k and n (0 ≤ k ≤ 1018, 0 ≤ n ≤ 440) which are correspondingly the maximal number of moves a knight can make and the number of deleted cells. Then follow n lines, each giving the coordinates of a deleted square in the form (xi, yi) (|xi| ≤ 10, |yi| ≤ 10). All the numbers are integer, the deleted squares are different and it is guaranteed that the square (0, 0) is not deleted.

Please, do not use %lld specificator to read or write 64-bit integers in C++. It is preffered to use cin (also you may use %I64d).

## 输出格式

You must print the answer on a single line. As it can be rather long, you should print it modulo 1000000007.

## 样例

### 样例 1

**输入**:
```
1 0
```

**输出**:
```
9
```

### 样例 2

**输入**:
```
2 7
-1 2
1 2
2 1
2 -1
1 -2
-1 -2
-2 -1
```

**输出**:
```
9
```

