# Covered Path

**题目ID**: 534/B  
**比赛**: Codeforces Round 298 (Div. 2)  
**年份**: 2015  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

The on-board computer on <PERSON>y<PERSON><PERSON>'s car measured that the car speed at the beginning of some section of the path equals v1 meters per second, and in the end it is v2 meters per second. We know that this section of the route took exactly t seconds to pass.

Assuming that at each of the seconds the speed is constant, and between seconds the speed can change at most by d meters per second in absolute value (i.e., the difference in the speed of any two adjacent seconds does not exceed d in absolute value), find the maximum possible length of the path section in meters.

## 输入格式

The first line contains two integers v1 and v2 (1 ≤ v1, v2 ≤ 100) — the speeds in meters per second at the beginning of the segment and at the end of the segment, respectively.

The second line contains two integers t (2 ≤ t ≤ 100) — the time when the car moves along the segment in seconds, d (0 ≤ d ≤ 10) — the maximum value of the speed change between adjacent seconds.

It is guaranteed that there is a way to complete the segment so that:

- the speed in the first second equals v1,
- the speed in the last second equals v2,
- the absolute value of difference of speeds between any two adjacent seconds doesn't exceed d.

## 输出格式

Print the maximum possible length of the path segment in meters.

## 样例

### 样例 1

**输入**:
```
5 6
4 2
```

**输出**:
```
26
```

### 样例 2

**输入**:
```
10 10
10 0
```

**输出**:
```
100
```

## 备注

In the first sample the sequence of speeds of Polycarpus' car can look as follows: 5, 7, 8, 6. Thus, the total path is 5 + 7 + 8 + 6 = 26 meters.

In the second sample, as d = 0, the car covers the whole segment at constant speed v = 10. In t = 10 seconds it covers the distance of 100 meters.

