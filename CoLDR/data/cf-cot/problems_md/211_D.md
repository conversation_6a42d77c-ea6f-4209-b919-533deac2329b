# Cutting a Fence

**题目ID**: 211/D  
**比赛**: VK Cup 2012 Finals  
**年份**: 2012  
**时间限制**: 5.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON><PERSON><PERSON> the carpenter has an estate that is separated from the wood by a fence. The fence consists of n planks put in a line. The fence is not closed in a circle. The planks are numbered from left to right from 1 to n, the i-th plank is of height ai. All planks have the same width, the lower edge of each plank is located at the ground level.

Recently a local newspaper "Malevich and Life" wrote that the most fashionable way to decorate a fence in the summer is to draw a fuchsia-colored rectangle on it, the lower side of the rectangle must be located at the lower edge of the fence.

<PERSON><PERSON><PERSON> is delighted with this idea! He immediately bought some fuchsia-colored paint and began to decide what kind of the rectangle he should paint. <PERSON><PERSON><PERSON> is sure that the rectangle should cover k consecutive planks. In other words, he will paint planks number x, x + 1, ..., x + k - 1 for some x (1 ≤ x ≤ n - k + 1). He wants to paint the rectangle of maximal area, so the rectangle height equals min ai for x ≤ i ≤ x + k - 1, x is the number of the first colored plank.

<PERSON><PERSON><PERSON> has already made up his mind that the rectangle width can be equal to one of numbers of the sequence k1, k2, ..., km. For each ki he wants to know the expected height of the painted rectangle, provided that he selects x for such fence uniformly among all n - ki + 1 possible values. Help him to find the expected heights.

## 输入格式

The first line contains a single integer n (1 ≤ n ≤ 106) — the number of planks in the fence. The second line contains a sequence of integers a1, a2, ..., an (1 ≤ ai ≤ 109) where ai is the height of the i-th plank of the fence.

The third line contains an integer m (1 ≤ m ≤ 106) and the next line contains m space-separated integers k1, k2, ..., km (1 ≤ ki ≤ n) where ki is the width of the desired fuchsia-colored rectangle in planks.

## 输出格式

Print m whitespace-separated real numbers, the i-th number equals the expected value of the rectangle height, if its width in planks equals ki. The value will be considered correct if its absolute or relative error doesn't exceed 10 - 9.

## 样例

### 样例 1

**输入**:
```
3
3 2 1
3
1 2 3
```

**输出**:
```
2.000000000000000
1.500000000000000
1.000000000000000
```

### 样例 2

**输入**:
```
2
1 1
3
1 2 1
```

**输出**:
```
1.000000000000000
1.000000000000000
1.000000000000000
```

## 备注

Let's consider the first sample test.

- There are three possible positions of the fence for k1 = 1. For the first position (x = 1) the height is 3, for the second one (x = 2) the height is 2, for the third one (x = 3) the height is 1. As the fence position is chosen uniformly, the expected height of the fence equals $$\frac{3+2+1}{3}=2$$;
- There are two possible positions of the fence for k2 = 2. For the first position (x = 1) the height is 2, for the second one (x = 2) the height is 1. The expected height of the fence equals $${ \frac { 2 + 1 } { 2 } } = 1. 5$$;
- There is the only possible position of the fence for k3 = 3. The expected height of the fence equals 1.

