# <PERSON> and Pixels

**题目ID**: 508/A  
**比赛**: Codeforces Round 288 (Div. 2)  
**年份**: 2015  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON> loves his phone and also putting his hair up... But the hair is now irrelevant.

<PERSON> has installed a new game to his phone. The goal of the game is following. There is a rectangular field consisting of n row with m pixels in each row. Initially, all the pixels are colored white. In one move, <PERSON> can choose any pixel and color it black. In particular, he can choose the pixel that is already black, then after the boy's move the pixel does not change, that is, it remains black. <PERSON> loses the game when a 2 × 2 square consisting of black pixels is formed.

<PERSON> has made a plan of k moves, according to which he will paint pixels. Each turn in his plan is represented as a pair of numbers i and j, denoting respectively the row and the column of the pixel to be colored on the current move.

Determine whether <PERSON> loses if he acts in accordance with his plan, and if he does, on what move the 2 × 2 square consisting of black pixels is formed.

## 输入格式

The first line of the input contains three integers n, m, k (1 ≤ n, m ≤ 1000, 1 ≤ k ≤ 105) — the number of rows, the number of columns and the number of moves that <PERSON> is going to perform.

The next k lines contain <PERSON>'s moves in the order he makes them. Each line contains two integers i and j (1 ≤ i ≤ n, 1 ≤ j ≤ m), representing the row number and column number of the pixel that was painted during a move.

## 输出格式

If <PERSON> loses, print the number of the move when the 2 × 2 square consisting of black pixels is formed.

If <PERSON> doesn't lose, that is, no 2 × 2 square consisting of black pixels is formed during the given k moves, print 0.

## 样例

### 样例 1

**输入**:
```
2 2 4
1 1
1 2
2 1
2 2
```

**输出**:
```
4
```

### 样例 2

**输入**:
```
2 3 6
2 3
2 2
1 3
2 2
1 2
1 1
```

**输出**:
```
5
```

### 样例 3

**输入**:
```
5 3 7
2 3
1 2
1 1
4 1
3 1
5 3
3 2
```

**输出**:
```
0
```

