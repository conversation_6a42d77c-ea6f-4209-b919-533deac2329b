# <PERSON><PERSON> and Sums

**题目ID**: 476/C  
**比赛**: Codeforces Round 272 (Div. 2)  
**年份**: 2014  
**时间限制**: 1.5秒  
**内存限制**: 256MB  

## 题目描述

<PERSON><PERSON> loves summing up something for no reason. One day he obtains two integers a and b occasionally. He wants to calculate the sum of all nice integers. Positive integer x is called nice if $${ \bmod ( x, b ) } \neq 0$$ and $$\frac{\operatorname{div}(x,b)}{\operatorname{mod}(x,b)} = k$$, where k is some integer number in range [1, a].

By $$\operatorname{div}(x,y)$$ we denote the quotient of integer division of x and y. By $$\operatorname{mod}(x,y)$$ we denote the remainder of integer division of x and y. You can read more about these operations here: http://goo.gl/AcsXhT.

The answer may be large, so please print its remainder modulo 1 000 000 007 (109 + 7). Can you compute it faster than <PERSON><PERSON>?

## 输入格式

The single line of the input contains two integers a, b (1 ≤ a, b ≤ 107).

## 输出格式

Print a single integer representing the answer modulo 1 000 000 007 (109 + 7).

## 样例

### 样例 1

**输入**:
```
1 1
```

**输出**:
```
0
```

### 样例 2

**输入**:
```
2 2
```

**输出**:
```
8
```

## 备注

For the first sample, there are no nice integers because $$\operatorname{mod}(x,1)$$ is always zero.

For the second sample, the set of nice integers is {3, 5}.

