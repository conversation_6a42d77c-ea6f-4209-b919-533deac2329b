# Am<PERSON> and Pins

**题目ID**: 507/B  
**比赛**: Codeforces Round 287 (Div. 2)  
**年份**: 2015  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON><PERSON> loves Geometry. One day he came up with a very interesting problem.

<PERSON><PERSON> has a circle of radius r and center in point (x, y). He wants the circle center to be in new position (x', y').

In one step <PERSON><PERSON> can put a pin to the border of the circle in a certain point, then rotate the circle around that pin by any angle and finally remove the pin.

Help <PERSON><PERSON> to achieve his goal in minimum number of steps.

## 输入格式

Input consists of 5 space-separated integers r, x, y, x' y' (1 ≤ r ≤ 105,  - 105 ≤ x, y, x', y' ≤ 105), circle radius, coordinates of original center of the circle and coordinates of destination center of the circle respectively.

## 输出格式

Output a single integer — minimum number of steps required to move the center of the circle to the destination point.

## 样例

### 样例 1

**输入**:
```
2 0 0 0 4
```

**输出**:
```
1
```

### 样例 2

**输入**:
```
1 1 1 4 4
```

**输出**:
```
3
```

### 样例 3

**输入**:
```
4 5 6 5 6
```

**输出**:
```
0
```

## 备注

In the first sample test the optimal way is to put a pin at point (0, 2) and rotate the circle by 180 degrees counter-clockwise (or clockwise, no matter).

