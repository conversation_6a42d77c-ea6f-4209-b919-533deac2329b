# Alex and broken contest

**题目ID**: 877/A  
**比赛**: Codeforces Round 442 (Div. 2)  
**年份**: 2017  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

One day <PERSON> was creating a contest about his friends, but accidentally deleted it. Fortunately, all the problems were saved, but now he needs to find them among other problems.

But there are too many problems, to do it manually. <PERSON> asks you to write a program, which will determine if a problem is from this contest by its name.

It is known, that problem is from this contest if and only if its name contains one of <PERSON>'s friends' name exactly once. His friends' names are "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>" and "<PERSON><PERSON>".

Names are case sensitive.

## 输入格式

The only line contains string from lowercase and uppercase letters and "_" symbols of length, not more than 100 — the name of the problem.

## 输出格式

Print "YES", if problem is from this contest, and "NO" otherwise.

## 样例

### 样例 1

**输入**:
```
Alex_and_broken_contest
```

**输出**:
```
NO
```

### 样例 2

**输入**:
```
NikitaAndString
```

**输出**:
```
YES
```

### 样例 3

**输入**:
```
<PERSON><PERSON>_and_<PERSON><PERSON>
```

**输出**:
```
NO
```

## 题解

You need just implement what is written in the statements. Count the total number of entries of the names and check if it's equal to 1.

