# <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>

**题目ID**: 535/B  
**比赛**: Codeforces Round 299 (Div. 2)  
**年份**: 2015  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

Once again <PERSON><PERSON> started eating coffee mix without water! <PERSON><PERSON> told him that it smells awful, but he didn't stop doing that. That's why <PERSON><PERSON> told his smart friend, <PERSON><PERSON><PERSON><PERSON> to punish him! <PERSON><PERSON><PERSON><PERSON> took <PERSON><PERSON>' headphones and told him: "If you solve the following problem, I'll return it to you."

The problem is:

You are given a lucky number n. Lucky numbers are the positive integers whose decimal representations contain only the lucky digits 4 and 7. For example, numbers 47, 744, 4 are lucky and 5, 17, 467 are not.

If we sort all lucky numbers in increasing order, what's the 1-based index of n?

<PERSON><PERSON> is not as smart as <PERSON><PERSON><PERSON><PERSON>, so he asked you to do him a favor and solve this problem so he can have his headphones back.

## 输入格式

The first and only line of input contains a lucky number n (1 ≤ n ≤ 109).

## 输出格式

Print the index of n among all lucky numbers.

## 样例

### 样例 1

**输入**:
```
4
```

**输出**:
```
1
```

### 样例 2

**输入**:
```
7
```

**输出**:
```
2
```

### 样例 3

**输入**:
```
77
```

**输出**:
```
6
```

