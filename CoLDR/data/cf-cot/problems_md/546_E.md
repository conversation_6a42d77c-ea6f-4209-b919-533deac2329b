# Soldier and Traveling

**题目ID**: 546/E  
**比赛**: Codeforces Round 304 (Div. 2)  
**年份**: 2015  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

In the country there are n cities and m bidirectional roads between them. Each city has an army. Army of the i-th city consists of ai soldiers. Now soldiers roam. After roaming each soldier has to either stay in his city or to go to the one of neighboring cities by at moving along at most one road.

Check if is it possible that after roaming there will be exactly bi soldiers in the i-th city.

## 输入格式

First line of input consists of two integers n and m (1 ≤ n ≤ 100, 0 ≤ m ≤ 200).

Next line contains n integers a1, a2, ..., an (0 ≤ ai ≤ 100).

Next line contains n integers b1, b2, ..., bn (0 ≤ bi ≤ 100).

Then m lines follow, each of them consists of two integers p and q (1 ≤ p, q ≤ n, p ≠ q) denoting that there is an undirected road between cities p and q.

It is guaranteed that there is at most one road between each pair of cities.

## 输出格式

If the conditions can not be met output single word "NO".

Otherwise output word "YES" and then n lines, each of them consisting of n integers. Number in the i-th line in the j-th column should denote how many soldiers should road from city i to city j (if i ≠ j) or how many soldiers should stay in city i (if i = j).

If there are several possible answers you may output any of them.

## 样例

### 样例 1

**输入**:
```
4 4
1 2 6 3
3 5 3 1
1 2
2 3
3 4
4 2
```

**输出**:
```
YES
1 0 0 0
2 0 0 0
0 5 1 0
0 0 2 1
```

### 样例 2

**输入**:
```
2 0
1 2
2 1
```

**输出**:
```
NO
```

## 题解

There are few ways to solve this task, but I'll describe the simplest (in my opinion) one.
Let's build a flow network in following way:
Make a source.
Make a first group of vertices consisting of n vertices, each of them for one city.
Connect a source with ith vertex in first group with edge that has capacity ai.
Make a sink and second group of vertices in the same way, but use bi except for ai.
If there is a road between cities i and j or i = j. Make two edges, first should be connecting ith vertex from first group, and jth vertex from second group, and has infinity capacity. Second should be similar, but connect jth from first group and ith from second group.
Then find a maxflow, in any complexity.
If maxflow is equal to sum of ai and is equal to sum of bi, then there exists an answer. How can we get it? We just have to check how many units are we pushing through edge connecting two vertices from different groups.
I told about many solutions, because every solution, which doesn't use greedy strategy, can undo it's previous pushes, and does it in reasonable complexity should pass.

