# Maximum Value

**题目ID**: 484/B  
**比赛**: Codeforces Round 276 (Div. 1)  
**年份**: 2014  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

You are given a sequence a consisting of n integers. Find the maximum possible value of $$a_i \bmod a_j$$ (integer remainder of ai divided by aj), where 1 ≤ i, j ≤ n and ai ≥ aj.

## 输入格式

The first line contains integer n — the length of the sequence (1 ≤ n ≤ 2·105).

The second line contains n space-separated integers ai (1 ≤ ai ≤ 106).

## 输出格式

Print the answer to the problem.

## 样例

### 样例 1

**输入**:
```
3
3 4 5
```

**输出**:
```
2
```

