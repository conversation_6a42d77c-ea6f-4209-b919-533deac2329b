# Increasing Subsequences

**题目ID**: 1922/E  
**比赛**: Educational Codeforces Round 161 (Rated for Div. 2)  
**年份**: 2024  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

Let's recall that an increasing subsequence of the array $$$a$$$ is a sequence that can be obtained from it by removing some elements without changing the order of the remaining elements, and the remaining elements are strictly increasing (i. e $$$a_{b_1} < a_{b_2} < \dots < a_{b_k}$$$ and $$$b_1 < b_2 < \dots < b_k$$$). Note that an empty subsequence is also increasing.

You are given a positive integer $$$X$$$. Your task is to find an array of integers of length at most $$$200$$$, such that it has exactly $$$X$$$ increasing subsequences, or report that there is no such array. If there are several answers, you can print any of them.

If two subsequences consist of the same elements, but correspond to different positions in the array, they are considered different (for example, the array $$$[2, 2]$$$ has two different subsequences equal to $$$[2]$$$).

## 输入格式

The first line contains a single integer $$$t$$$ ($$$1 \le t \le 1000$$$) — the number of test cases.

The only line of each test case contains a single integer $$$X$$$ ($$$2 \le X \le 10^{18}$$$).

## 输出格式

For each query, print the answer to it. If it is impossible to find the required array, print -1 on the first line. Otherwise, print a positive integer $$$n$$$ on the first line — the length of the array. On the second line, print $$$n$$$ integers — the required array itself. If there are several answers, you can print any of them. All elements of the array should be in the range $$$[-10^9; 10^9]$$$.

## 样例

### 样例 1

**输入**:
```
4
2
5
13
37
```

**输出**:
```
1
0
3
0 1 0
5
2 2 3 4 2
7
-1 -1 0 0 2 3 -1
```

## 题解

Let's consider one of the solutions for constructing the required array.
Let array $$$a$$$ have $$$x$$$ increasing subsequences. If we add a new minimum to the end of the array, the number of increasing subsequences in the new array equals $$$x+1$$$ (since the new element does not form increasing subsequences with other elements, only subsequences consisting of this element will be added). If we add a new maximum to the end of the array, the number of increasing subsequences in the new array equals $$$2x$$$ (since the new element forms increasing subsequences with any other elements).
Using the above facts, let's define a recursive function $$$f(x)$$$, which returns an array with exactly $$$x$$$ increasing subsequences. For an odd value of $$$x$$$, $$$f(x) = f(x-1) + min$$$ (here + denotes adding an element to the end of the array); for an even value of $$$x$$$, $$$f(x) = f(\frac{x}{2}) + max$$$. Now we need to estimate the number of elements in the array obtained by this algorithm. Note that there cannot be two consecutive operations of the first type ($$$x \rightarrow x-1$$$), so every two operations, the value of $$$x$$$ decreases by at least two times. Thus, the size of the array satisfies the limit of $$$200$$$.

