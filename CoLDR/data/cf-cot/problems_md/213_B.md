# Numbers

**题目ID**: 213/B  
**比赛**: Codeforces Round 131 (Div. 1)  
**年份**: 2012  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON><PERSON> loves writing all sorts of problems, especially such that he can't solve himself. You've got one of his problems, the one <PERSON><PERSON> gave to <PERSON><PERSON><PERSON>. And <PERSON><PERSON><PERSON> asks you to solve it.

There is integer n and array a, consisting of ten integers, indexed by numbers from 0 to 9. Your task is to count the number of positive integers with the following properties:

- the number's length does not exceed n;
- the number doesn't have leading zeroes;
- digit i (0 ≤ i ≤ 9) occurs in the number at least a[i] times.

## 输入格式

The first line contains integer n (1 ≤ n ≤ 100). The next line contains 10 integers a[0], a[1], ..., a[9] (0 ≤ a[i] ≤ 100) — elements of array a. The numbers are separated by spaces.

## 输出格式

On a single line print the remainder of dividing the answer to the problem by 1000000007 (109 + 7).

## 样例

### 样例 1

**输入**:
```
1
0 0 0 0 0 0 0 0 0 1
```

**输出**:
```
1
```

### 样例 2

**输入**:
```
2
1 1 0 0 0 0 0 0 0 0
```

**输出**:
```
1
```

### 样例 3

**输入**:
```
3
1 1 0 0 0 0 0 0 0 0
```

**输出**:
```
36
```

## 备注

In the first sample number 9 meets the requirements.

In the second sample number 10 meets the requirements.

In the third sample numbers 10, 110, 210, 120, 103 meet the requirements. There are other suitable numbers, 36 in total.

