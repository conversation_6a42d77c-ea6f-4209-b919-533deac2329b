# Vanya and Cubes

**题目ID**: 492/A  
**比赛**: Codeforces Round 280 (Div. 2)  
**年份**: 2014  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON><PERSON> got n cubes. He decided to build a pyramid from them. <PERSON><PERSON> wants to build the pyramid as follows: the top level of the pyramid must consist of 1 cube, the second level must consist of 1 + 2 = 3 cubes, the third level must have 1 + 2 + 3 = 6 cubes, and so on. Thus, the i-th level of the pyramid must have 1 + 2 + ... + (i - 1) + i cubes.

<PERSON><PERSON> wants to know what is the maximum height of the pyramid that he can make using the given cubes.

## 输入格式

The first line contains integer n (1 ≤ n ≤ 104) — the number of cubes given to <PERSON><PERSON>.

## 输出格式

Print the maximum possible height of the pyramid in the single line.

## 样例

### 样例 1

**输入**:
```
1
```

**输出**:
```
1
```

### 样例 2

**输入**:
```
25
```

**输出**:
```
4
```

## 备注

Illustration to the second sample:

