# Fight the Monster

**题目ID**: 487/A  
**比赛**: Codeforces Round 278 (Div. 1)  
**年份**: 2014  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

A monster is attacking the Cyberland!

<PERSON>, a braver, is going to beat the monster. <PERSON> and the monster each have 3 attributes: hitpoints (HP), offensive power (ATK) and defensive power (DEF).

During the battle, every second the monster's HP decrease by max(0, ATKY - DEFM), while <PERSON>'s HP decreases by max(0, ATKM - DEFY), where index Y denotes <PERSON> Yang and index M denotes monster. Both decreases happen simultaneously Once monster's HP ≤ 0 and the same time <PERSON>'s HP > 0, <PERSON> wins.

<PERSON> can buy attributes from the magic shop of Cyberland: h bitcoins per HP, a bitcoins per ATK, and d bitcoins per DEF.

Now Master <PERSON> wants to know the minimum number of bitcoins he can spend in order to win.

## 输入格式

The first line contains three integers HPY, ATKY, DEFY, separated by a space, denoting the initial HP, ATK and DEF of <PERSON>.

The second line contains three integers HPM, ATKM, DEFM, separated by a space, denoting the HP, ATK and DEF of the monster.

The third line contains three integers h, a, d, separated by a space, denoting the price of 1 HP, 1 ATK and 1 DEF.

All numbers in input are integer and lie between 1 and 100 inclusively.

## 输出格式

The only output line should contain an integer, denoting the minimum bitcoins Master Yang should spend in order to win.

## 样例

### 样例 1

**输入**:
```
1 2 1
1 100 1
1 100 100
```

**输出**:
```
99
```

### 样例 2

**输入**:
```
100 100 100
1 1 1
1 1 1
```

**输出**:
```
0
```

## 备注

For the first sample, prices for ATK and DEF are extremely high. Master Yang can buy 99 HP, then he can beat the monster with 1 HP left.

For the second sample, Master Yang is strong enough to beat the monster, so he doesn't need to buy anything.

