# Brackets in Implications

**题目ID**: 550/E  
**比赛**: Codeforces Round 306 (Div. 2)  
**年份**: 2015  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

Implication is a function of two logical arguments, its value is false if and only if the value of the first argument is true and the value of the second argument is false.

Implication is written by using character '$$\rightarrow$$', and the arguments and the result of the implication are written as '0' (false) and '1' (true). According to the definition of the implication:

$$0 \rightarrow 0 = 1$$ $$0 \rightarrow 1 = 1$$$$1 \rightarrow 0 = 0$$ $$\overrightarrow { 1 } \rightarrow 1 = 1$$

When a logical expression contains multiple implications, then when there are no brackets, it will be calculated from left to fight. For example,

$$0\to 0\to 0=(0\to 0)\to 0=1\to 0=0$$.

When there are brackets, we first calculate the expression in brackets. For example,

$$0\to(0\to0)=0\to1=1$$.

For the given logical expression $$a_{1}\rightarrow a_{2}\rightarrow a_{3}\rightarrow\cdots\rightarrow a_{n}$$ determine if it is possible to place there brackets so that the value of a logical expression is false. If it is possible, your task is to find such an arrangement of brackets.

## 输入格式

The first line contains integer n (1 ≤ n ≤ 100 000) — the number of arguments in a logical expression.

The second line contains n numbers a1, a2, ..., an ($$a_i \in \{0, 1\}$$), which means the values of arguments in the expression in the order they occur.

## 输出格式

Print "NO" (without the quotes), if it is impossible to place brackets in the expression so that its value was equal to 0.

Otherwise, print "YES" in the first line and the logical expression with the required arrangement of brackets in the second line.

The expression should only contain characters '0', '1', '-' (character with ASCII code 45), '>' (character with ASCII code 62), '(' and ')'. Characters '-' and '>' can occur in an expression only paired like that: ("->") and represent implication. The total number of logical arguments (i.e. digits '0' and '1') in the expression must be equal to n. The order in which the digits follow in the expression from left to right must coincide with a1, a2, ..., an.

The expression should be correct. More formally, a correct expression is determined as follows:

- Expressions "0", "1" (without the quotes) are correct.
- If v1, v2 are correct, then v1->v2 is a correct expression.
- If v is a correct expression, then (v) is a correct expression.

The total number of characters in the resulting expression mustn't exceed 106.

If there are multiple possible answers, you are allowed to print any of them.

## 样例

### 样例 1

**输入**:
```
4
0 1 1 0
```

**输出**:
```
YES
(((0)->1)->(1->0))
```

### 样例 2

**输入**:
```
2
1 1
```

**输出**:
```
NO
```

### 样例 3

**输入**:
```
1
0
```

**输出**:
```
YES
0
```

## 题解

Let input consists of $$a_{1}\rightarrow a_{2}\rightarrow\cdots\rightarrow a_{n}$$, ai is 0 or 1 for all i.
Let's show that there is no solution in only two cases:
1) an = 1.
$$x \rightarrow 1 = 1$$, for all x, and no parentheses can change last 1 to 0.
2) Input has the form $$\overrightarrow { 1 } \rightarrow \overrightarrow { 1 } \rightarrow \overrightarrow { 1 } \rightarrow \cdots \rightarrow \overrightarrow { 1 } \rightarrow \overrightarrow { 0 } \rightarrow \overrightarrow { 0 }$$ or its suffix with at least two arguments.
This can be proven by induction. For input $$0 \rightarrow 0$$ there is no solution, for longer inputs any attempt to put parentheses will decrease the number of 1s in the beginning by one, or will introduce 1 in the last position (which will lead to case one).
Let's construct solution for all other cases.
1) For input 0 we don't need to do anything.
2) For input of the form $$\cdots \rightarrow 1 \rightarrow 0$$ we don't need any parentheses, the value of this expression is always
3) Expression in the form $$\cdots \to 0 \to \cdots \to 0 \to 0$$ (where second missed part consists of ones only). Then $$\cdots \to (0 \to (1 \to 1 \to \cdots \to 1 \to 0)) \to 0 = 0$$.
Complexity of the solution is O(n).

