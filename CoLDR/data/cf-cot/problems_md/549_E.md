# Sasha Circle

**题目ID**: 549/E  
**比赛**: Looksery Cup 2015  
**年份**: 2015  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

Berlanders like to eat cones after a hard day. Misha Square and Sasha Circle are local authorities of Berland. Each of them controls its points of cone trade. <PERSON><PERSON> has n points, <PERSON> — m. Since their subordinates constantly had conflicts with each other, they decided to build a fence in the form of a circle, so that the points of trade of one businessman are strictly inside a circle, and points of the other one are strictly outside. It doesn't matter which of the two gentlemen will have his trade points inside the circle.

Determine whether they can build a fence or not.

## 输入格式

The first line contains two integers n and m (1 ≤ n, m ≤ 10000), numbers of <PERSON><PERSON>'s and <PERSON>'s trade points respectively.

The next n lines contains pairs of space-separated integers Mx, My ( - 104 ≤ Mx, My ≤ 104), coordinates of <PERSON><PERSON>'s trade points.

The next m lines contains pairs of space-separated integers Sx, Sy ( - 104 ≤ Sx, Sy ≤ 104), coordinates of <PERSON>'s trade points.

It is guaranteed that all n + m points are distinct.

## 输出格式

The only output line should contain either word "YES" without quotes in case it is possible to build a such fence or word "NO" in the other case.

## 样例

### 样例 1

**输入**:
```
2 2
-1 0
1 0
0 -1
0 1
```

**输出**:
```
NO
```

### 样例 2

**输入**:
```
4 4
1 0
0 1
-1 0
0 -1
1 1
-1 1
-1 -1
1 -1
```

**输出**:
```
YES
```

## 备注

In the first sample there is no possibility to separate points, because any circle that contains both points ( - 1, 0), (1, 0) also contains at least one point from the set (0,  - 1), (0, 1), and vice-versa: any circle that contains both points (0,  - 1), (0, 1) also contains at least one point from the set ( - 1, 0), (1, 0)

In the second sample one of the possible solution is shown below. Misha's points are marked with red colour and Sasha's are marked with blue.

