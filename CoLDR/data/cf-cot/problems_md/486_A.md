# Calculating Function

**题目ID**: 486/A  
**比赛**: Codeforces Round 277 (Div. 2)  
**年份**: 2014  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

For a positive integer n let's define a function f:

f(n) =  - 1 + 2 - 3 + .. + ( - 1)nn

Your task is to calculate f(n) for a given integer n.

## 输入格式

The single line contains the positive integer n (1 ≤ n ≤ 1015).

## 输出格式

Print f(n) in a single line.

## 样例

### 样例 1

**输入**:
```
4
```

**输出**:
```
2
```

### 样例 2

**输入**:
```
5
```

**输出**:
```
-3
```

## 备注

f(4) =  - 1 + 2 - 3 + 4 = 2

f(5) =  - 1 + 2 - 3 + 4 - 5 =  - 3

