# Equidistant String

**题目ID**: 545/B  
**比赛**: Codeforces Round 303 (Div. 2)  
**年份**: 2015  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

Little <PERSON> loves strings. Today she calculates distances between them. As <PERSON> is a small girl after all, her strings contain only digits zero and one. She uses the definition of Hamming distance:

We will define the distance between two strings s and t of the same length consisting of digits zero and one as the number of positions i, such that si isn't equal to ti.

As besides everything else <PERSON> loves symmetry, she wants to find for two strings s and t of length n such string p of length n, that the distance from p to s was equal to the distance from p to t.

It's time for <PERSON> to go to bed, help her find such string p or state that it is impossible.

## 输入格式

The first line contains string s of length n.

The second line contains string t of length n.

The length of string n is within range from 1 to 105. It is guaranteed that both strings contain only digits zero and one.

## 输出格式

Print a string of length n, consisting of digits zero and one, that meets the problem statement. If no such string exist, print on a single line "impossible" (without the quotes).

If there are multiple possible answers, print any of them.

## 样例

### 样例 1

**输入**:
```
0001
1011
```

**输出**:
```
0011
```

### 样例 2

**输入**:
```
000
111
```

**输出**:
```
impossible
```

## 备注

In the first sample different answers are possible, namely — 0010, 0011, 0110, 0111, 1000, 1001, 1100, 1101.

