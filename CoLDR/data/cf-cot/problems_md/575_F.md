# Bulbo

**题目ID**: 575/F  
**比赛**: Bubble Cup 8 - Finals [Online Mirror]  
**年份**: 2015  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

Bananistan is a beautiful banana republic. Beautiful women in beautiful dresses. Beautiful statues of beautiful warlords. Beautiful stars in beautiful nights.

In Bananistan people play this crazy game – Bulbo. There’s an array of bulbs and player at the position, which represents one of the bulbs. The distance between two neighboring bulbs is 1. Before each turn player can change his position with cost |posnew - posold|. After that, a contiguous set of bulbs lights-up and player pays the cost that’s equal to the distance to the closest shining bulb. Then, all bulbs go dark again. The goal is to minimize your summed cost. I tell you, Bananistanians are spending their nights playing with bulbs.

Banana day is approaching, and you are hired to play the most beautiful Bulbo game ever. A huge array of bulbs is installed, and you know your initial position and all the light-ups in advance. You need to play the ideal game and impress Bananistanians, and their families.

## 输入格式

The first line contains number of turns n and initial position x. Next n lines contain two numbers lstart and lend, which represent that all bulbs from interval [lstart, lend] are shining this turn.

- 1 ≤ n ≤ 5000
- 1 ≤ x ≤ 109
- 1 ≤ lstart ≤ lend ≤ 109

## 输出格式

Output should contain a single number which represents the best result (minimum cost) that could be obtained by playing this Bulbo game.

## 样例

### 样例 1

**输入**:
```
5 4
2 7
9 16
8 10
9 17
1 6
```

**输出**:
```
8
```

## 备注

Before 1. turn move to position 5

Before 2. turn move to position 9

Before 5. turn move to position 8

