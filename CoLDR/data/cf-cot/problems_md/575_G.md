# Run for beer

**题目ID**: 575/G  
**比赛**: Bubble Cup 8 - Finals [Online Mirror]  
**年份**: 2015  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

People in BubbleLand like to drink beer. Little do you know, beer here is so good and strong that every time you drink it your speed goes 10 times slower than before you drank it.

<PERSON><PERSON><PERSON> lives in city Beergrade, but wants to go to city Beerburg. You are given a road map of BubbleLand and you need to find the fastest way for him. When he starts his journey in Beergrade his speed is 1. When he comes to a new city he always tries a glass of local beer, which divides his speed by 10.

The question here is what the minimal time for him to reach Beerburg is. If there are several paths with the same minimal time, pick the one that has least roads on it. If there is still more than one path, pick any.

It is guaranteed that there will be at least one path from Beergrade to Beerburg.

## 输入格式

The first line of input contains integer N — the number of cities in Bubbleland and integer M — the number of roads in this country. Cities are enumerated from 0 to N - 1, with city 0 being Beergrade, and city N - 1 being Beerburg. Each of the following M lines contains three integers a, b (a ≠ b) and len. These numbers indicate that there is a bidirectional road between cities a and b with length len.

- 2 ≤ N ≤ 105
- 1 ≤ M ≤ 105
- 0 ≤ len ≤ 9
- There is at most one road between two cities

## 输出格式

The first line of output should contain minimal time needed to go from Beergrade to Beerburg.

The second line of the output should contain the number of cities on the path from Beergrade to Beerburg that takes minimal time.

The third line of output should contain the numbers of cities on this path in the order they are visited, separated by spaces.

## 样例

### 样例 1

**输入**:
```
8 10
0 1 1
1 2 5
2 7 6
0 3 2
3 7 3
0 4 0
4 5 0
5 7 2
0 6 0
6 7 7
```

**输出**:
```
32
3
0 3 7
```

