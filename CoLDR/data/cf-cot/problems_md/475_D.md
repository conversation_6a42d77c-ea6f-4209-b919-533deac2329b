# CGCDSSQ

**题目ID**: 475/D  
**比赛**: Bayan 2015 Contest Warm Up  
**年份**: 2014  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

Given a sequence of integers a1, ..., an and q queries x1, ..., xq on it. For each query xi you have to count the number of pairs (l, r) such that 1 ≤ l ≤ r ≤ n and gcd(al, al + 1, ..., ar) = xi.

$$\gcd(v_{1},v_{2},\ldots,v_{n})$$ is a greatest common divisor of v1, v2, ..., vn, that is equal to a largest positive integer that divides all vi.

## 输入格式

The first line of the input contains integer n, (1 ≤ n ≤ 105), denoting the length of the sequence. The next line contains n space separated integers a1, ..., an, (1 ≤ ai ≤ 109).

The third line of the input contains integer q, (1 ≤ q ≤ 3 × 105), denoting the number of queries. Then follows q lines, each contain an integer xi, (1 ≤ xi ≤ 109).

## 输出格式

For each query print the result in a separate line.

## 样例

### 样例 1

**输入**:
```
3
2 6 3
5
1
2
3
4
6
```

**输出**:
```
1
2
2
0
1
```

### 样例 2

**输入**:
```
7
10 20 3 15 1000 60 16
10
1
2
3
4
5
6
10
20
60
1000
```

**输出**:
```
14
0
2
2
2
0
2
2
1
1
```

