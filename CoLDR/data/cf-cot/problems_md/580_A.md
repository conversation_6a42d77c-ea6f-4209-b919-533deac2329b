# Ke<PERSON> and First Steps

**题目ID**: 580/A  
**比赛**: Codeforces Round 321 (Div. 2)  
**年份**: 2015  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON><PERSON> decided to make some money doing business on the Internet for exactly n days. He knows that on the i-th day (1 ≤ i ≤ n) he makes ai money. <PERSON><PERSON> loves progress, that's why he wants to know the length of the maximum non-decreasing subsegment in sequence ai. Let us remind you that the subsegment of the sequence is its continuous fragment. A subsegment of numbers is called non-decreasing if all numbers in it follow in the non-decreasing order.

Help <PERSON><PERSON> cope with this task!

## 输入格式

The first line contains integer n (1 ≤ n ≤ 105).

The second line contains n integers a1,  a2,  ...,  an (1 ≤ ai ≤ 109).

## 输出格式

Print a single integer — the length of the maximum non-decreasing subsegment of sequence a.

## 样例

### 样例 1

**输入**:
```
6
2 2 1 3 4 1
```

**输出**:
```
3
```

### 样例 2

**输入**:
```
3
2 2 9
```

**输出**:
```
3
```

## 备注

In the first test the maximum non-decreasing subsegment is the numbers from the third to the fifth one.

In the second test the maximum non-decreasing subsegment is the numbers from the first to the third one.

## 题解

Заметим, что если в массиве есть две пересекающиеся непрерывные неубывающие подпоследовательности, то их можно объединить в одну. Поэтому можно просто проходиться по массиву слева направо. Если текущую подпоследовательность можно продолжить с помощью i-го элемента, то делаем это, иначе начинаем новую. Ответом будет максимальная из всех найденных подпоследовательностей.
Асимптотика — O(n).

