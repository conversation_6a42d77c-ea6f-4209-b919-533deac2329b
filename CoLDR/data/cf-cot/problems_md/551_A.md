# GukiZ and Contest

**题目ID**: 551/A  
**比赛**: Codeforces Round 307 (Div. 2)  
**年份**: 2015  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

Professor <PERSON><PERSON><PERSON> likes programming contests. He especially likes to rate his students on the contests he prepares. Now, he has decided to prepare a new contest.

In total, n students will attend, and before the start, every one of them has some positive integer rating. Students are indexed from 1 to n. Let's denote the rating of i-th student as ai. After the contest ends, every student will end up with some positive integer position. <PERSON><PERSON><PERSON> expects that his students will take places according to their ratings.

He thinks that each student will take place equal to $$1 + (\text{number of students with strictly higher rating than his or her})$$. In particular, if student A has rating strictly lower then student B, A will get the strictly better position than B, and if two students have equal ratings, they will share the same position.

<PERSON><PERSON><PERSON> would like you to reconstruct the results by following his expectations. Help him and determine the position after the end of the contest for each of his students if everything goes as expected.

## 输入格式

The first line contains integer n (1 ≤ n ≤ 2000), number of <PERSON><PERSON><PERSON>'s students.

The second line contains n numbers a1, a2, ... an (1 ≤ ai ≤ 2000) where ai is the rating of i-th student (1 ≤ i ≤ n).

## 输出格式

In a single line, print the position after the end of the contest for each of n students in the same order as they appear in the input.

## 样例

### 样例 1

**输入**:
```
3
1 3 3
```

**输出**:
```
3 1 1
```

### 样例 2

**输入**:
```
1
1
```

**输出**:
```
1
```

### 样例 3

**输入**:
```
5
3 5 3 4 5
```

**输出**:
```
4 1 4 3 1
```

## 备注

In the first sample, students 2 and 3 are positioned first (there is no other student with higher rating), and student 1 is positioned third since there are two students with higher rating.

In the second sample, first student is the only one on the contest.

In the third sample, students 2 and 5 share the first position with highest rating, student 4 is next with third position, and students 1 and 3 are the last sharing fourth position.

## 题解

Problem is very simple. Just implement what is written in the problem statement: for every array element, count the number of array elements greater than it and add one. Time complexity O(n2).

