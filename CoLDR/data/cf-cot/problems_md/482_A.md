# Diverse Permutation

**题目ID**: 482/A  
**比赛**: Codeforces Round 275 (Div. 1)  
**年份**: 2014  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

Permutation p is an ordered set of integers p1,   p2,   ...,   pn, consisting of n distinct positive integers not larger than n. We'll denote as n the length of permutation p1,   p2,   ...,   pn.

Your task is to find such permutation p of length n, that the group of numbers |p1 - p2|, |p2 - p3|, ..., |pn - 1 - pn| has exactly k distinct elements.

## 输入格式

The single line of the input contains two space-separated positive integers n, k (1 ≤ k < n ≤ 105).

## 输出格式

Print n integers forming the permutation. If there are multiple answers, print any of them.

## 样例

### 样例 1

**输入**:
```
3 2
```

**输出**:
```
1 3 2
```

### 样例 2

**输入**:
```
3 1
```

**输出**:
```
1 2 3
```

### 样例 3

**输入**:
```
5 2
```

**输出**:
```
1 3 2 4 5
```

## 备注

By |x| we denote the absolute value of number x.

