# <PERSON><PERSON> and New Game

**题目ID**: 467/B  
**比赛**: Codeforces Round 267 (Div. 2)  
**年份**: 2014  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

After you had helped <PERSON> and <PERSON> to move in the dorm, they went to help their friend <PERSON><PERSON> play a new computer game «Call of Soldiers 3».

The game has (m + 1) players and n types of soldiers in total. Players «Call of Soldiers 3» are numbered form 1 to (m + 1). Types of soldiers are numbered from 0 to n - 1. Each player has an army. Army of the i-th player can be described by non-negative integer xi. Consider binary representation of xi: if the j-th bit of number xi equal to one, then the army of the i-th player has soldiers of the j-th type.

<PERSON><PERSON> is the (m + 1)-th player of the game. He assume that two players can become friends if their armies differ in at most k types of soldiers (in other words, binary representations of the corresponding numbers differ in at most k bits). Help <PERSON><PERSON> and count how many players can become his friends.

## 输入格式

The first line contains three integers n, m, k (1 ≤ k ≤ n ≤ 20; 1 ≤ m ≤ 1000).

The i-th of the next (m + 1) lines contains a single integer xi (1 ≤ xi ≤ 2n - 1), that describes the i-th player's army. We remind you that <PERSON><PERSON> is the (m + 1)-th player.

## 输出格式

Print a single integer — the number of <PERSON>or's potential friends.

## 样例

### 样例 1

**输入**:
```
7 3 1
8
5
111
17
```

**输出**:
```
0
```

### 样例 2

**输入**:
```
3 3 3
1
2
3
4
```

**输出**:
```
3
```

