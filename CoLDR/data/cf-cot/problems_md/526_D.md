# Om Nom and Necklace

**题目ID**: 526/D  
**比赛**: ZeptoLab Code Rush 2015  
**年份**: 2015  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

One day O<PERSON> Nom found a thread with n beads of different colors. He decided to cut the first several beads from this thread to make a bead necklace and present it to his girlfriend <PERSON><PERSON>.

O<PERSON>m knows that his girlfriend loves beautiful patterns. That's why he wants the beads on the necklace to form a regular pattern. A sequence of beads S is regular if it can be represented as S = A + B + A + B + A + ... + A + B + A, where A and B are some bead sequences, " + " is the concatenation of sequences, there are exactly 2k + 1 summands in this sum, among which there are k + 1 "A" summands and k "B" summands that follow in alternating order. <PERSON><PERSON> knows that her friend is an eager mathematician, so she doesn't mind if A or B is an empty sequence.

Help <PERSON>m <PERSON>m determine in which ways he can cut off the first several beads from the found thread (at least one; probably, all) so that they form a regular pattern. When <PERSON><PERSON>m cuts off the beads, he doesn't change their order.

## 输入格式

The first line contains two integers n, k (1 ≤ n, k ≤ 1 000 000) — the number of beads on the thread that <PERSON><PERSON> found and number k from the definition of the regular sequence above.

The second line contains the sequence of n lowercase Latin letters that represent the colors of the beads. Each color corresponds to a single letter.

## 输出格式

Print a string consisting of n zeroes and ones. Position i (1 ≤ i ≤ n) must contain either number one if the first i beads on the thread form a regular sequence, or a zero otherwise.

## 样例

### 样例 1

**输入**:
```
7 2
bcabcab
```

**输出**:
```
0000011
```

### 样例 2

**输入**:
```
21 2
ababaababaababaababaa
```

**输出**:
```
000110000111111000011
```

## 备注

In the first sample test a regular sequence is both a sequence of the first 6 beads (we can take A = "", B = "bca"), and a sequence of the first 7 beads (we can take A = "b", B = "ca").

In the second sample test, for example, a sequence of the first 13 beads is regular, if we take A = "aba", B = "ba".

