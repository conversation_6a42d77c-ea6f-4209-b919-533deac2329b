# Different String

**题目ID**: 1971/B  
**比赛**: Codeforces Round 944 (Div. 4)  
**年份**: 2024  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

You are given a string $$$s$$$ consisting of lowercase English letters.

Rearrange the characters of $$$s$$$ to form a new string $$$r$$$ that is not equal to $$$s$$$, or report that it's impossible.

## 输入格式

The first line contains a single integer $$$t$$$ ($$$1 \leq t \leq 1000$$$) — the number of test cases.

The only line of each test case contains a string $$$s$$$ of length at most $$$10$$$ consisting of lowercase English letters.

## 输出格式

For each test case, if no such string $$$r$$$ exists as described in the statement, output "NO" (without quotes).

Otherwise, output "YES" (without quotes). Then, output one line — the string $$$r$$$, consisting of letters of string $$$s$$$.

You can output "YES" and "NO" in any case (for example, strings "yEs", "yes", and "Yes" will be recognized as a positive response).

If multiple answers are possible, you can output any of them.

## 样例

### 样例 1

**输入**:
```
8
codeforces
aaaaa
xxxxy
co
d
nutdealer
mwistht
hhhhhhhhhh
```

**输出**:
```
YES
forcodesec
NO
YES
xxyxx
YES
oc
NO
YES
undertale
YES
thtsiwm
NO
```

## 备注

In the first test case, another possible answer is $$$\texttt{forcescode}$$$.

In the second test case, all rearrangements of $$$\texttt{aaaaa}$$$ are equal to $$$\texttt{aaaaa}$$$.

## 题解

Since the string is length at most $$$10$$$, we can try all swaps of two characters of the string. This is $$$\mathcal{O}(|s|^2)$$$ per test case, which is fast enough. If none of them create a different string, then all characters in the original string are the same, so the answer is NO.
Bonus: Actually, it's enough to try all swaps with the first character, solving the problem in $$$\mathcal{O}(|s|)$$$. Why?

