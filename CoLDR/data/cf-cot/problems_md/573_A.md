# Bear and Poker

**题目ID**: 573/A  
**比赛**: Codeforces Round 318 [RussianCodeCup Thanks-Round] (Div. 1)  
**年份**: 2015  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON><PERSON> is an old brown bear. He often plays poker with his friends. Today they went to a casino. There are n players (including <PERSON><PERSON> himself) and right now all of them have bids on the table. i-th of them has bid with size ai dollars.

Each player can double his bid any number of times and triple his bid any number of times. The casino has a great jackpot for making all bids equal. Is it possible that <PERSON><PERSON> and his friends will win a jackpot?

## 输入格式

First line of input contains an integer n (2 ≤ n ≤ 105), the number of players.

The second line contains n integer numbers a1, a2, ..., an (1 ≤ ai ≤ 109) — the bids of players.

## 输出格式

Print "Yes" (without the quotes) if players can make their bids become equal, or "No" otherwise.

## 样例

### 样例 1

**输入**:
```
4
75 150 75 50
```

**输出**:
```
Yes
```

### 样例 2

**输入**:
```
3
100 150 250
```

**输出**:
```
No
```

## 备注

In the first sample test first and third players should double their bids twice, second player should double his bid once and fourth player should both double and triple his bid.

It can be shown that in the second sample test there is no way to make all bids equal.

## 题解

Any positive integer number can be factorized and written as 2a·3b·5c·7e·....
We can multiply given numbers by 2 and 3 so we can increase a and b for them. So we can make all a and b equal by increasing them to the same big value (e.g. 100). But we can't change powers of other prime numbers so they must be equal from the beginning. We can check it by diving all numbers from input by two and by three as many times as possible. Then all of them must be equal.
Alternative solution is to calculate GCD of given numbers. Answer is "YES" iff we can get each number by multiplying GCD by 2 and 3. Otherwise, some number had different power of prime number other than 2 and 3.

