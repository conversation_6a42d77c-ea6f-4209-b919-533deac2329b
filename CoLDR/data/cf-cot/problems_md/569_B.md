# Inventory

**题目ID**: 569/B  
**比赛**: Codeforces Round 315 (Div. 2)  
**年份**: 2015  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

Companies always have a lot of equipment, furniture and other things. All of them should be tracked. To do this, there is an inventory number assigned with each item. It is much easier to create a database by using those numbers and keep the track of everything.

During an audit, you were surprised to find out that the items are not numbered sequentially, and some items even share the same inventory number! There is an urgent need to fix it. You have chosen to make the numbers of the items sequential, starting with 1. Changing a number is quite a time-consuming process, and you would like to make maximum use of the current numbering.

You have been given information on current inventory numbers for n items in the company. Renumber items so that their inventory numbers form a permutation of numbers from 1 to n by changing the number of as few items as possible. Let us remind you that a set of n numbers forms a permutation if all the numbers are in the range from 1 to n, and no two numbers are equal.

## 输入格式

The first line contains a single integer n — the number of items (1 ≤ n ≤ 105).

The second line contains n numbers a1, a2, ..., an (1 ≤ ai ≤ 105) — the initial inventory numbers of the items.

## 输出格式

Print n numbers — the final inventory numbers of the items in the order they occur in the input. If there are multiple possible answers, you may print any of them.

## 样例

### 样例 1

**输入**:
```
3
1 3 2
```

**输出**:
```
1 3 2
```

### 样例 2

**输入**:
```
4
2 2 3 3
```

**输出**:
```
2 1 3 4
```

### 样例 3

**输入**:
```
1
2
```

**输出**:
```
1
```

## 备注

In the first test the numeration is already a permutation, so there is no need to change anything.

In the second test there are two pairs of equal numbers, in each pair you need to replace one number.

In the third test you need to replace 2 by 1, as the numbering should start from one.

## 题解

Подойдём к задаче с другой стороны: а сколько максимально можно оставить номеров, чтобы получилась перестановка? Очевидно, что эти номера должны быть от 1 до n, причем среди них не должно быть повторяющихся. И понятно, что это условие необходимое и достаточное.
Такую задачу можно решать жадным алгоритмом. Если мы встретили число, которого ещё не было, при этом оно от 1 до n, то мы его оставим. Для реализации можно завести массив used, в котором отмечать уже использованные числа.
Затем нужно ещё раз пройти по массиву и распределить неиспользованные числа.
Сложность — O(n).

