# Three Logos

**题目ID**: 581/D  
**比赛**: Codeforces Round 322 (Div. 2)  
**年份**: 2015  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

Three companies decided to order a billboard with pictures of their logos. A billboard is a big square board. A logo of each company is a rectangle of a non-zero area.

Advertisers will put up the ad only if it is possible to place all three logos on the billboard so that they do not overlap and the billboard has no empty space left. When you put a logo on the billboard, you should rotate it so that the sides were parallel to the sides of the billboard.

Your task is to determine if it is possible to put the logos of all the three companies on some square billboard without breaking any of the described rules.

## 输入格式

The first line of the input contains six positive integers x1, y1, x2, y2, x3, y3 (1 ≤ x1, y1, x2, y2, x3, y3 ≤ 100), where xi and yi determine the length and width of the logo of the i-th company respectively.

## 输出格式

If it is impossible to place all the three logos on a square shield, print a single integer "-1" (without the quotes).

If it is possible, print in the first line the length of a side of square n, where you can place all the three logos. Each of the next n lines should contain n uppercase English letters "A", "B" or "C". The sets of the same letters should form solid rectangles, provided that:

- the sizes of the rectangle composed from letters "A" should be equal to the sizes of the logo of the first company,
- the sizes of the rectangle composed from letters "B" should be equal to the sizes of the logo of the second company,
- the sizes of the rectangle composed from letters "C" should be equal to the sizes of the logo of the third company,

Note that the logos of the companies can be rotated for printing on the billboard. The billboard mustn't have any empty space. If a square billboard can be filled with the logos in multiple ways, you are allowed to print any of them.

See the samples to better understand the statement.

## 样例

### 样例 1

**输入**:
```
5 1 2 5 5 2
```

**输出**:
```
5
AAAAA
BBBBB
BBBBB
CCCCC
CCCCC
```

### 样例 2

**输入**:
```
4 4 2 6 4 2
```

**输出**:
```
6
BBBBBB
BBBBBB
AAAACC
AAAACC
AAAACC
AAAACC
```

