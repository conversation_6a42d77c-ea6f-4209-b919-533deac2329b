# Love Triangles

**题目ID**: 553/C  
**比赛**: Codeforces Round 309 (Div. 1)  
**年份**: 2015  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

There are many anime that are about "love triangles": <PERSON> loves <PERSON>, and <PERSON> loves <PERSON> as well, but <PERSON> hates <PERSON>. You are thinking about an anime which has n characters. The characters are labeled from 1 to n. Every pair of two characters can either mutually love each other or mutually hate each other (there is no neutral state).

You hate love triangles (A-B are in love and B-C are in love, but A-C hate each other), and you also hate it when nobody is in love. So, considering any three characters, you will be happy if exactly one pair is in love (<PERSON> and <PERSON> love each other, and <PERSON> hates both A and B), or if all three pairs are in love (<PERSON> loves B, <PERSON> loves C, <PERSON> loves A).

You are given a list of m known relationships in the anime. You know for sure that certain pairs love each other, and certain pairs hate each other. You're wondering how many ways you can fill in the remaining relationships so you are happy with every triangle. Two ways are considered different if two characters are in love in one way but hate each other in the other. Print this count modulo 1 000 000 007.

## 输入格式

The first line of input will contain two integers n, m (3 ≤ n ≤ 100 000, 0 ≤ m ≤ 100 000).

The next m lines will contain the description of the known relationships. The i-th line will contain three integers ai, bi, ci. If ci is 1, then ai and bi are in love, otherwise, they hate each other (1 ≤ ai, bi ≤ n, ai ≠ bi, $$c_i \in \{0, 1\}$$).

Each pair of people will be described no more than once.

## 输出格式

Print a single integer equal to the number of ways to fill in the remaining pairs so that you are happy with every triangle modulo 1 000 000 007.

## 样例

### 样例 1

**输入**:
```
3 0
```

**输出**:
```
4
```

### 样例 2

**输入**:
```
4 4
1 2 1
2 3 1
3 4 0
4 1 0
```

**输出**:
```
1
```

### 样例 3

**输入**:
```
4 4
1 2 1
2 3 1
3 4 0
4 1 1
```

**输出**:
```
0
```

## 备注

In the first sample, the four ways are to:

- Make everyone love each other
- Make 1 and 2 love each other, and 3 hate 1 and 2 (symmetrically, we get 3 ways from this).

In the second sample, the only possible solution is to make 1 and 3 love each other and 2 and 4 hate each other.

## 题解

Let's look at the graph of characters who love each other. Each love-connected component can be collapsed into a single node, since we know that all characters in the same connected component must love each other.
Now, we claim that the resulting collapsed graph with the hate edges has a solution if and only if the resulting graph is bipartite.
To show this, suppose the graph is not bipartite. Then, there is an odd cycle. If the cycle is of length 1, it is a self edge, which clearly isn't allowed (since a node must love itself). For any odd cycle of length more than 1, let's label the nodes in the cycle a1, a2, a3, ..., ak. Then, in general, we must have ai loves a(i + 2) ± od{k}, since ai, a(i + 1) ± od{k} hate each other and a(i + 1) ± od{k}, a(i + 2) ± od{k} hate each other. However, we can use the fact that the cycle is odd and eventually get that ai and ai + 1 love each other. However, this is a contradiction, since we said they must originally hate each other.
For the other direction, suppose the graph is bipartite. Let X, Y be an arbitrary bipartition of the graph. If we let all nodes in X love each other and all nodes in Y love each other, and every edge between X and Y hate each other, then we get a solution.
Thus, we can see that we have a solution if and only if the graph is bipartite. So, if the graph is not bipartite, the answer is zero. Otherwise, the second part of the proof gives us a way to count. We just need to count the number of different bipartitions of the graph. It's not too hard to see that this is just simply 2^(number of connected components - 1).
This entire algorithm takes O(N + M) time.

