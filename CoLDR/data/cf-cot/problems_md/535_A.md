# <PERSON><PERSON> and <PERSON><PERSON>s

**题目ID**: 535/A  
**比赛**: Codeforces Round 299 (Div. 2)  
**年份**: 2015  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

Today <PERSON><PERSON> got his test result as an integer score and he wants to share it with his girlfriend, <PERSON><PERSON><PERSON>.

His phone operating system is Tavdroid, and its keyboard doesn't have any digits! He wants to share his score with <PERSON><PERSON><PERSON> via text, so he has no choice but to send this number using words.

He ate coffee mix without water again, so right now he's really messed up and can't think.

Your task is to help him by telling him what to type.

## 输入格式

The first and only line of input contains an integer s (0 ≤ s ≤ 99), <PERSON><PERSON>'s score.

## 输出格式

In the first and only line of output, print a single string consisting only from English lowercase letters and hyphens ('-'). Do not use spaces.

## 样例

### 样例 1

**输入**:
```
6
```

**输出**:
```
six
```

### 样例 2

**输入**:
```
99
```

**输出**:
```
ninety-nine
```

### 样例 3

**输入**:
```
20
```

**输出**:
```
twenty
```

## 备注

You can find all you need to know about English numerals in http://en.wikipedia.org/wiki/English_numerals .

