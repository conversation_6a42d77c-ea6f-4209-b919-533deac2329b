# Soldier and Bananas

**题目ID**: 546/A  
**比赛**: Codeforces Round 304 (Div. 2)  
**年份**: 2015  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

A soldier wants to buy w bananas in the shop. He has to pay k dollars for the first banana, 2k dollars for the second one and so on (in other words, he has to pay i·k dollars for the i-th banana).

He has n dollars. How many dollars does he have to borrow from his friend soldier to buy w bananas?

## 输入格式

The first line contains three positive integers k, n, w (1  ≤  k, w  ≤  1000, 0 ≤ n ≤ 109), the cost of the first banana, initial number of dollars the soldier has and number of bananas he wants.

## 输出格式

Output one integer — the amount of dollars that the soldier must borrow from his friend. If he doesn't have to borrow money, output 0.

## 样例

### 样例 1

**输入**:
```
3 17 4
```

**输出**:
```
13
```

## 题解

We can easily calculate the sum of money that we need to buy all the bananas that we want, let's name it x.
If n >  = x the answer is 0, because we don't need to borrow anything.
Otherwise the answer is x - n.

