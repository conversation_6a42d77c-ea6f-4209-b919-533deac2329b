# Minimization

**题目ID**: 571/B  
**比赛**: Codeforces Round 317 [AimFund Thanks-Round] (Div. 1)  
**年份**: 2015  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

You've got array A, consisting of n integers and a positive integer k. Array A is indexed by integers from 1 to n.

You need to permute the array elements so that value

$$\sum_{i=1}^{n-k}|A[i]-A[i+k]|$$

## 输入格式

The first line contains two integers n, k (2 ≤ n ≤ 3·105, 1 ≤ k ≤ min(5000, n - 1)).

The second line contains n integers A[1], A[2], ..., A[n] ( - 109 ≤ A[i] ≤ 109), separate by spaces — elements of the array A.

## 输出格式

Print the minimum possible value of the sum described in the statement.

## 样例

### 样例 1

**输入**:
```
3 2
1 2 4
```

**输出**:
```
1
```

### 样例 2

**输入**:
```
5 2
3 -5 3 -5 3
```

**输出**:
```
0
```

### 样例 3

**输入**:
```
6 3
4 3 4 3 2 5
```

**输出**:
```
3
```

## 备注

In the first test one of the optimal permutations is 1 4 2.

In the second test the initial order is optimal.

In the third test one of the optimal permutations is 2 3 4 4 3 5.

## 题解

We can divide all indices [1;n] into groups by their remainder modulo k. While counting $${ \sum _ { i = 1 } ^ { n - k } | a _ { i } - a _ { i + k } | }$$, we can consider each group separately and sum the distances between neighbouring numbers in each group.
Consider one group, corresponding to some remainder i modulo k, i.e. containing aj for $$j \equiv i \mod (k)$$. Let's write down its numbers from left to right: b1, b2, ..., bm. Then this group adds to the overall sum the value
Now consider two groups b1, ..., bm and c1, c2, ..., cl, both sorted in non-decreasing order. We claim that either b1 ≥ cl or bm ≤ c1, i.e. segments [b1, bm] and [c1, cl] can have common points only in their endpoints.
Why is this true? These groups add |bm - b1| + |cl - c1| to the overall sum. We consider the case c1 ≥ b1, the other is symmetric. If c1 < bm, then swapping c1 and bm will not increase the values these groups add to the answer, since the right border of b group moves to the left, and the left border of c group moves to the right. So, c1 ≥ bm in that case, and the assertion is proved.
Now we know that the values in each group should from a continuous segment of the sorted original array. In fact, we have $$k - ( n \mod k )$$ groups of size $$\pi_{k}$$ (so called small groups) and $$( n \mod k )$$ groups of size $$(\frac{n}{k}+1)$$ (so called large groups). Consider the following dynamic programming: dp[L][S] — the minimal sum of values added to the answer by L large groups and S small groups, if we choose the elements for them from the first $$L \cdot \left( \frac{n}{k} + 1 \right) + S \cdot \frac{n}{k}$$ elements of the sorted array A. There are no more than O(k2) states, and each transition can be made in O(1): we choose large or small group to add and obtain the number it adds to the sum by subtracting two elements of the sorted array. The answer for the problem will be in $$dp\left[(n \mod k)\right]\left[k-(n \mod k)\right]$$.
The overall complexity of the solution is $${\mathcal {O}}(n\log n+k^{2})$$. We can note that in pretests $$( n \mod k )$$ was quite small, and some slower solutions could pass, but they failed on final tests.

