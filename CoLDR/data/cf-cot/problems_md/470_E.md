# Chessboard

**题目ID**: 470/E  
**比赛**: Surprise Language Round 7  
**年份**: 2014  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

Chessboard is a board of n × n squares arranged in two alternating colors (black and white). Top left square is white. You are given board size n. Output an image of a chessboard, with black and white squares marked with '#' and '.' characters, respectively.

## 输入格式

The only line of input contains an integer n (1 ≤ n ≤ 9).

## 输出格式

Output an image of n × n chessboard.

## 样例

### 样例 1

**输入**:
```
4
```

**输出**:
```
.#.#
#.#.
.#.#
#.#.
```

