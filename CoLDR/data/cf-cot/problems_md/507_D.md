# The Maths Lecture

**题目ID**: 507/D  
**比赛**: Codeforces Round 287 (Div. 2)  
**年份**: 2015  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON><PERSON> doesn't like Maths as he finds it really boring, so he usually sleeps in Maths lectures. But one day the teacher suspected that <PERSON><PERSON> is sleeping and asked him a question to make sure he wasn't.

First he gave <PERSON><PERSON> two positive integers n and k. Then he asked <PERSON><PERSON>, how many integer numbers x > 0 exist such that:

- Decimal representation of x (without leading zeroes) consists of exactly n digits;
- There exists some integer y > 0 such that:   $$y \bmod k = 0$$;  decimal representation of y is a suffix of decimal representation of x.

As the answer to this question may be pretty huge the teacher asked <PERSON><PERSON> to output only its remainder modulo a number m.

Can you help <PERSON><PERSON> escape this embarrassing situation?

## 输入格式

Input consists of three integers n, k, m (1 ≤ n ≤ 1000, 1 ≤ k ≤ 100, 1 ≤ m ≤ 109).

## 输出格式

Print the required number modulo m.

## 样例

### 样例 1

**输入**:
```
1 2 1000
```

**输出**:
```
4
```

### 样例 2

**输入**:
```
2 2 1000
```

**输出**:
```
45
```

### 样例 3

**输入**:
```
5 3 1103
```

**输出**:
```
590
```

## 备注

A suffix of a string S is a non-empty string that can be obtained by removing some number (possibly, zero) of first characters from S.

