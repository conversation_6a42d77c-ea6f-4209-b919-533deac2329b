# Hacking Cypher

**题目ID**: 490/C  
**比赛**: Codeforces Round 279 (Div. 2)  
**年份**: 2014  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

Polycarpus participates in a competition for hacking into a new secure messenger. He's almost won.

Having carefully studied the interaction protocol, <PERSON><PERSON><PERSON><PERSON> came to the conclusion that the secret key can be obtained if he properly cuts the public key of the application into two parts. The public key is a long integer which may consist of even a million digits!

<PERSON><PERSON><PERSON><PERSON> needs to find such a way to cut the public key into two nonempty parts, that the first (left) part is divisible by a as a separate number, and the second (right) part is divisible by b as a separate number. Both parts should be positive integers that have no leading zeros. <PERSON>ycarpus knows values a and b.

Help Polycarpus and find any suitable method to cut the public key.

## 输入格式

The first line of the input contains the public key of the messenger — an integer without leading zeroes, its length is in range from 1 to 106 digits. The second line contains a pair of space-separated positive integers a, b (1 ≤ a, b ≤ 108).

## 输出格式

In the first line print "YES" (without the quotes), if the method satisfying conditions above exists. In this case, next print two lines — the left and right parts after the cut. These two parts, being concatenated, must be exactly identical to the public key. The left part must be divisible by a, and the right part must be divisible by b. The two parts must be positive integers having no leading zeros. If there are several answers, print any of them.

If there is no answer, print in a single line "NO" (without the quotes).

## 样例

### 样例 1

**输入**:
```
116401024
97 1024
```

**输出**:
```
YES
11640
1024
```

### 样例 2

**输入**:
```
284254589153928171911281811000
1009 1000
```

**输出**:
```
YES
2842545891539
28171911281811000
```

### 样例 3

**输入**:
```
120
12 1
```

**输出**:
```
NO
```

