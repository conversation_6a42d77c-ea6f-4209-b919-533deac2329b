# Zigzag

**题目ID**: 228/D  
**比赛**: Codeforces Round 141 (Div. 2)  
**年份**: 2012  
**时间限制**: 3.0秒  
**内存限制**: 256MB  

## 题目描述

The court wizard <PERSON><PERSON><PERSON><PERSON> wants to become a famous mathematician. For that, he needs his own theorem, like the <PERSON><PERSON><PERSON> theorem, or his sum, like the Minkowski sum. But most of all he wants to have his sequence, like the <PERSON><PERSON><PERSON><PERSON> sequence, and his function, like the Euler's totient function.

The Zigag's sequence with the zigzag factor z is an infinite sequence Siz (i ≥ 1; z ≥ 2), that is determined as follows:

- Siz = 2, when $$( i \mod 2 ( z - 1 ) ) = 0$$;
- $$S_{i}^{z} = ( i \mod 2(z-1) )$$, when $$0 < ( i \mod 2(z-1) ) \leq z$$;
- $$S_{i}^{z}=2z-(i \mod 2(z-1))$$, when $$(i \mod 2(z-1)) > z$$.

Operation $$x \mod y$$ means taking the remainder from dividing number x by number y. For example, the beginning of sequence Si3 (zigzag factor 3) looks as follows: 1, 2, 3, 2, 1, 2, 3, 2, 1.

Let's assume that we are given an array a, consisting of n integers. Let's define element number i (1 ≤ i ≤ n) of the array as ai. The Zigzag function is function $$Z(l,r,z) = \sum_{i=l}^{r} a_i \cdot S_{i-l+1}^z$$, where l, r, z satisfy the inequalities 1 ≤ l ≤ r ≤ n, z ≥ 2.

To become better acquainted with the Zigzag sequence and the Zigzag function, the wizard offers you to implement the following operations on the given array a.

1. The assignment operation. The operation parameters are (p, v). The operation denotes assigning value v to the p-th array element. After the operation is applied, the value of the array element ap equals v.
2. The Zigzag operation. The operation parameters are (l, r, z). The operation denotes calculating the Zigzag function Z(l, r, z).

Explore the magical powers of zigzags, implement the described operations.

## 输入格式

The first line contains integer n (1 ≤ n ≤ 105) — The number of elements in array a. The second line contains n space-separated integers: a1, a2, ..., an (1 ≤ ai ≤ 109) — the elements of the array.

The third line contains integer m (1 ≤ m ≤ 105) — the number of operations. Next m lines contain the operations' descriptions. An operation's description starts with integer ti (1 ≤ ti ≤ 2) — the operation type.

- If ti = 1 (assignment operation), then on the line follow two space-separated integers: pi, vi (1 ≤ pi ≤ n; 1 ≤ vi ≤ 109) — the parameters of the assigning operation.
- If ti = 2 (Zigzag operation), then on the line follow three space-separated integers: li, ri, zi (1 ≤ li ≤ ri ≤ n; 2 ≤ zi ≤ 6) — the parameters of the Zigzag operation.

You should execute the operations in the order, in which they are given in the input.

## 输出格式

For each Zigzag operation print the calculated value of the Zigzag function on a single line. Print the values for Zigzag functions in the order, in which they are given in the input.

Please, do not use the %lld specifier to read or write 64-bit integers in С++. It is preferred to use cin, cout streams or the %I64d specifier.

## 样例

### 样例 1

**输入**:
```
5
2 3 1 5 5
4
2 2 3 2
2 1 5 3
1 3 5
2 1 5 3
```

**输出**:
```
5
26
38
```

## 备注

Explanation of the sample test:

- Result of the first operation is Z(2, 3, 2) = 3·1 + 1·2 = 5.
- Result of the second operation is Z(1, 5, 3) = 2·1 + 3·2 + 1·3 + 5·2 + 5·1 = 26.
- After the third operation array a is equal to 2, 3, 5, 5, 5.
- Result of the forth operation is Z(1, 5, 3) = 2·1 + 3·2 + 5·3 + 5·2 + 5·1 = 38.

