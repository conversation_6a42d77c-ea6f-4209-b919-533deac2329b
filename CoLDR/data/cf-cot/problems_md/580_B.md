# Kefa and Company

**题目ID**: 580/B  
**比赛**: Codeforces Round 321 (Div. 2)  
**年份**: 2015  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON><PERSON> wants to celebrate his first big salary by going to restaurant. However, he needs company.

<PERSON><PERSON> has n friends, each friend will agree to go to the restaurant if <PERSON><PERSON> asks. Each friend is characterized by the amount of money he has and the friendship factor in respect to <PERSON><PERSON>. The parrot doesn't want any friend to feel poor compared to somebody else in the company (<PERSON><PERSON> doesn't count). A friend feels poor if in the company there is someone who has at least d units of money more than he does. Also, <PERSON><PERSON> wants the total friendship factor of the members of the company to be maximum. Help him invite an optimal company!

## 输入格式

The first line of the input contains two space-separated integers, n and d (1 ≤ n ≤ 105, $$1 \leq d \leq 10^9$$) — the number of <PERSON><PERSON>'s friends and the minimum difference between the amount of money in order to feel poor, respectively.

Next n lines contain the descriptions of <PERSON><PERSON>'s friends, the (i + 1)-th line contains the description of the i-th friend of type mi, si (0 ≤ mi, si ≤ 109) — the amount of money and the friendship factor, respectively.

## 输出格式

Print the maximum total friendship factir that can be reached.

## 样例

### 样例 1

**输入**:
```
4 5
75 5
0 100
150 20
75 1
```

**输出**:
```
100
```

### 样例 2

**输入**:
```
5 100
0 7
11 32
99 10
46 8
87 54
```

**输出**:
```
111
```

## 备注

In the first sample test the most profitable strategy is to form a company from only the second friend. At all other variants the total degree of friendship will be worse.

In the second sample test we can take all the friends.

## 题解

Сначала отсортируем всех друзей по возрастанию денег. Теперь ответом будет какой-то подотрезок массива. Далее будем использовать метод двух указателей для нахождения требуемого подотрезка.
Асимптотика — O(n log n).

