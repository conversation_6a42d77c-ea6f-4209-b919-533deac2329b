# First Digit Law

**题目ID**: 54/C  
**比赛**: Codeforces Beta Round 50  
**年份**: 2011  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

In the probability theory the following paradox called <PERSON><PERSON>'s law is known: "In many lists of random numbers taken from real sources, numbers starting with digit 1 occur much more often than numbers starting with any other digit" (that's the simplest form of the law).

Having read about it on Codeforces, the Hedgehog got intrigued by the statement and wishes to thoroughly explore it. He finds the following similar problem interesting in particular: there are N random variables, the i-th of which can take any integer value from some segment [Li;Ri] (all numbers from this segment are equiprobable). It means that the value of the i-th quantity can be equal to any integer number from a given interval [Li;Ri] with probability 1 / (Ri - Li + 1).

The Hedgehog wants to know the probability of the event that the first digits of at least K% of those values will be equal to one. In other words, let us consider some set of fixed values of these random variables and leave only the first digit (the MSD — most significant digit) of each value. Then let's count how many times the digit 1 is encountered and if it is encountered in at least K per cent of those N values, than such set of values will be called a good one. You have to find the probability that a set of values of the given random variables will be a good one.

## 输入格式

The first line contains number N which is the number of random variables (1 ≤ N ≤ 1000). Then follow N lines containing pairs of numbers Li, Ri, each of whom is a description of a random variable. It is guaranteed that 1 ≤ Li ≤ Ri ≤ 1018.

The last line contains an integer K (0 ≤ K ≤ 100).

All the numbers in the input file are integers.

Please, do not use %lld specificator to read or write 64-bit integers in C++. It is preffered to use cin (also you may use %I64d).

## 输出格式

Print the required probability. Print the fractional number with such a precision that the relative or absolute error of the result won't exceed 10 - 9.

## 样例

### 样例 1

**输入**:
```
1
1 2
50
```

**输出**:
```
0.500000000000000
```

### 样例 2

**输入**:
```
2
1 2
9 11
50
```

**输出**:
```
0.833333333333333
```

