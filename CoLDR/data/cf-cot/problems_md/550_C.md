# Divisibility by Eight

**题目ID**: 550/C  
**比赛**: Codeforces Round 306 (Div. 2)  
**年份**: 2015  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

You are given a non-negative integer n, its decimal representation consists of at most 100 digits and doesn't contain leading zeroes.

Your task is to determine if it is possible in this case to remove some of the digits (possibly not remove any digit at all) so that the result contains at least one digit, forms a non-negative integer, doesn't have leading zeroes and is divisible by 8. After the removing, it is forbidden to rearrange the digits.

If a solution exists, you should print it.

## 输入格式

The single line of the input contains a non-negative integer n. The representation of number n doesn't contain any leading zeroes and its length doesn't exceed 100 digits.

## 输出格式

Print "NO" (without quotes), if there is no such way to remove some digits from number n.

Otherwise, print "YES" in the first line and the resulting number after removing digits from number n in the second line. The printed number must be divisible by 8.

If there are multiple possible answers, you may print any of them.

## 样例

### 样例 1

**输入**:
```
3454
```

**输出**:
```
YES
344
```

### 样例 2

**输入**:
```
10
```

**输出**:
```
YES
0
```

### 样例 3

**输入**:
```
111111
```

**输出**:
```
NO
```

## 题解

This problem can be solved with at least two different approaches.
The first one is based on the "school" property of the divisibility by eight — number can be divided by eight if and only if its last three digits form a number that can be divided by eight. Thus, it is enough to test only numbers that can be obtained from the original one by crossing out and that contain at most three digits (so we check only all one-digit, two-digit and three-digit numbers). This can be done in O(len3) with three nested loops (here len is the length of the original number).
Second approach uses dynamic programming. Let's calculate dp[i][j], 1 ≤ i ≤ n, 0 ≤ j < 8. The value of dp is true if we can cross out some ditits from the prefix of length i such that the remaining number gives j modulo eight, and false otherwise.
This dp can be calculated in the following way: let ai be ith digit of the given number. Then dp[i][ai mod 8] = 1 (just this number). For all 0 ≤ j < 8 dp[i][(j * 10 + ai) mod 8] = max(dp[i][(j * 10 + ai) mod 8], dp[i - 1][j]) (we add current digit to some previous result), dp[i][(j * 10) mod 8] = max(dp[i][(j * 10) mod 8], dp[i - 1][j]) (we cross out current digit).
Answer is "YES" if dp[i][0] = 1 for some position i. For restoring the answer we need to keep additional array prev[i][j], which will say from where current value was calculated. Complexity of such solution is O(8·len) (again len is the length of the original number).

