# Music

**题目ID**: 569/A  
**比赛**: Codeforces Round 315 (Div. 2)  
**年份**: 2015  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

Little <PERSON><PERSON> loves listening to music via his smartphone. But the smartphone doesn't have much memory, so <PERSON><PERSON> listens to his favorite songs in a well-known social network InTalk.

Unfortunately, internet is not that fast in the city of Ekaterinozavodsk and the song takes a lot of time to download. But <PERSON><PERSON> is quite impatient. The song's duration is T seconds. <PERSON><PERSON> downloads the first S seconds of the song and plays it. When the playback reaches the point that has not yet been downloaded, <PERSON><PERSON> immediately plays the song from the start (the loaded part of the song stays in his phone, and the download is continued from the same place), and it happens until the song is downloaded completely and <PERSON><PERSON> listens to it to the end. For q seconds of real time the Internet allows you to download q - 1 seconds of the track.

Tell <PERSON><PERSON>, for how many times he will start the song, including the very first start.

## 输入格式

The single line contains three integers T, S, q (2 ≤ q ≤ 104, 1 ≤ S < T ≤ 105).

## 输出格式

Print a single integer — the number of times the song will be restarted.

## 样例

### 样例 1

**输入**:
```
5 2 2
```

**输出**:
```
2
```

### 样例 2

**输入**:
```
5 4 7
```

**输出**:
```
1
```

### 样例 3

**输入**:
```
6 2 3
```

**输出**:
```
1
```

## 备注

In the first test, the song is played twice faster than it is downloaded, which means that during four first seconds Lesha reaches the moment that has not been downloaded, and starts the song again. After another two seconds, the song is downloaded completely, and thus, Lesha starts the song twice.

In the second test, the song is almost downloaded, and Lesha will start it only once.

In the third sample test the download finishes and Lesha finishes listening at the same moment. Note that song isn't restarted in this case.

## 题解

Пусть перед очередным запуском прогружено S секунд песни, найдем, сколько будет прогружено перед следующим запуском. $$\frac{x}{q} = \frac{x-S}{q-1}$$. Отсюда x = qS.
Тогда решение: будем умножать S на q пока S < T. Количество таких умножений — это ответ.
Сложность — $${\mathcal O}\left(\log T\right)$$

