# Two Substrings

**题目ID**: 550/A  
**比赛**: Codeforces Round 306 (Div. 2)  
**年份**: 2015  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

You are given string s. Your task is to determine if the given string s contains two non-overlapping substrings "AB" and "BA" (the substrings can go in any order).

## 输入格式

The only line of input contains a string s of length between 1 and 105 consisting of uppercase Latin letters.

## 输出格式

Print "YES" (without the quotes), if string s contains two non-overlapping substrings "AB" and "BA", and "NO" otherwise.

## 样例

### 样例 1

**输入**:
```
ABA
```

**输出**:
```
NO
```

### 样例 2

**输入**:
```
BACFAB
```

**输出**:
```
YES
```

### 样例 3

**输入**:
```
AXBYBXA
```

**输出**:
```
NO
```

## 备注

In the first sample test, despite the fact that there are substrings "AB" and "BA", their occurrences overlap, so the answer is "NO".

In the second sample test there are the following occurrences of the substrings: BACFAB.

In the third sample test there is no substring "AB" nor substring "BA".

## 题解

There are many ways to solve this problem. Author solution does the following: check if substring "AB" goes before "BA", and then vice versa, if "BA" goes before "AB".
You can do it in the following way: find the first occurence of "AB" then check all substrings of length two to the right of it to check if substring "BA" also exists. Then do it vice versa.
Complexity of the solution is O(n), where n is the length of the given string.

