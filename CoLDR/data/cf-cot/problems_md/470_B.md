# Hexakosioihexekontahexaphobia

**题目ID**: 470/B  
**比赛**: Surprise Language Round 7  
**年份**: 2014  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

This (unpronounceable) word means simply fear of number 666.

You are given a string of digits. Check whether it is scary for a person suffering from this phobia, i.e., whether it contains number 666 as a substring.

## 输入格式

The input will consist of a single string p. The string contains between 1 and 100 digits ('0'-'9'), inclusive. The string doesn't contain any other characters except digits.

## 输出格式

Output "YES" if given string contains number 666, and "NO" otherwise (quotes for clarity only).

## 样例

### 样例 1

**输入**:
```
123098
```

**输出**:
```
NO
```

### 样例 2

**输入**:
```
16660
```

**输出**:
```
YES
```

### 样例 3

**输入**:
```
1606061
```

**输出**:
```
NO
```

## 备注

Note that 666 must be a contiguous substring of p, not a subsequence (see sample 3).

