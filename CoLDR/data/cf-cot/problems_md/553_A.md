# Kyoya and Colored Balls

**题目ID**: 553/A  
**比赛**: Codeforces Round 309 (Div. 1)  
**年份**: 2015  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON><PERSON><PERSON> has a bag with n colored balls that are colored with k different colors. The colors are labeled from 1 to k. Balls of the same color are indistinguishable. He draws balls from the bag one by one until the bag is empty. He noticed that he drew the last ball of color i before drawing the last ball of color i + 1 for all i from 1 to k - 1. Now he wonders how many different ways this can happen.

## 输入格式

The first line of input will have one integer k (1 ≤ k ≤ 1000) the number of colors.

Then, k lines will follow. The i-th line will contain ci, the number of balls of the i-th color (1 ≤ ci ≤ 1000).

The total number of balls doesn't exceed 1000.

## 输出格式

A single integer, the number of ways that <PERSON><PERSON><PERSON> can draw the balls from the bag as described in the statement, modulo 1 000 000 007.

## 样例

### 样例 1

**输入**:
```
3
2
2
1
```

**输出**:
```
3
```

### 样例 2

**输入**:
```
4
1
2
3
4
```

**输出**:
```
1680
```

## 备注

In the first sample, we have 2 balls of color 1, 2 balls of color 2, and 1 ball of color 3. The three ways for Kyoya are:

## 题解

Let fi be the number of ways to solve the problem using only the first i colors. We want to compute fn.
Initially, we have f1 = 1, since we only have a single color. Now, to go from fi to fi + 1, we note that we need to put a ball of color i + 1 at the very end, but the other balls of color i + 1 can go anywhere in the sequence. Thus, we can just multiply by the correct binomial coefficient. Thus, $$f_{i+1} = f_i \cdot \binom{c_1 + \ldots + c_{i+1} - 1}{c_{i+1} - 1}$$. So, we just need to precompute binomial coefficients, and then evaluate the product.

