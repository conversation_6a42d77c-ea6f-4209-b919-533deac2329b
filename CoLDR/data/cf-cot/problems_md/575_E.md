# Spectator Riots

**题目ID**: 575/E  
**比赛**: Bubble Cup 8 - Finals [Online Mirror]  
**年份**: 2015  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

It’s riot time on football stadium Ramacana! Raging fans have entered the field and the police find themselves in a difficult situation. The field can be represented as a square in the coordinate system defined by two diagonal vertices in (0,0) and (105, 105). The sides of that square are also considered to be inside the field, everything else is outside.

In the beginning, there are N fans on the field. For each fan we are given his speed, an integer vi as well as his integer coordinates (xi, yi). A fan with those coordinates might move and after one second he might be at any point (xi + p, yi + q) where 0 ≤ |p| + |q| ≤ vi. p, q are both integers.

Points that go outside of the square that represents the field are excluded and all others have equal probability of being the location of that specific fan after one second.

<PERSON><PERSON>, a young and promising police officer, has sent a flying drone to take a photo of the riot from above. The drone’s camera works like this:

1. It selects three points with integer coordinates such that there is a chance of a fan appearing there after one second. They must not be collinear or the camera won’t work. It is guaranteed that not all of the initial positions of fans will be on the same line.
2. Camera focuses those points and creates a circle that passes through those three points. A photo is taken after one second (one second after the initial state).
3. Everything that is on the circle or inside it at the moment of taking the photo (one second after focusing the points) will be on the photo.

Your goal is to select those three points so that the expected number of fans seen on the photo is maximized. If there are more such selections, select those three points that give the circle with largest radius among them. If there are still more suitable selections, any one of them will be accepted. If your answer follows conditions above and radius of circle you return is smaller then the optimal one by 0.01, your output will be considered as correct.

No test will have optimal radius bigger than 1010.

## 输入格式

The first line contains the number of fans on the field, N. The next N lines contain three integers: xi ,yi, vi. They are the x-coordinate, y-coordinate and speed of fan i at the beginning of the one second interval considered in the task.

- 3 ≤ N ≤ 105
- 0 ≤ xi, yi ≤ 105
- 0 ≤ vi ≤ 1000
- All numbers are integers

## 输出格式

You need to output the three points that camera needs to select. Print them in three lines, with every line containing the x-coordinate, then y-coordinate, separated by a single space. The order of points does not matter.

## 样例

### 样例 1

**输入**:
```
3
1 1 1
1 1 1
1 2 1
```

**输出**:
```
2 2
2 1
1 0
```

