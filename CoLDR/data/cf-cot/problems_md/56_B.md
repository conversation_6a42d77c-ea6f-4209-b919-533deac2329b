# Spoilt Permutation

**题目ID**: 56/B  
**比赛**: Codeforces Beta Round 52 (Div. 2)  
**年份**: 2011  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON><PERSON><PERSON> collects coins: he has exactly one coin for every year from 1 to n. Naturally, <PERSON><PERSON><PERSON> keeps all the coins in his collection in the order in which they were released. Once <PERSON><PERSON><PERSON>'s younger brother made a change — he took all the coins whose release year dated from l to r inclusively and put them in the reverse order. That is, he took a certain segment [l, r] and reversed it. At that the segment's endpoints did not coincide. For example, if n = 8, then initially <PERSON><PERSON><PERSON>'s coins were kept in the order 1 2 3 4 5 6 7 8. If <PERSON><PERSON><PERSON>'s younger brother chose the segment [2, 6], then after the reversal the coin order will change to 1 6 5 4 3 2 7 8. <PERSON><PERSON><PERSON> suspects that someone else could have spoilt the permutation after his brother. Help him to find that out. Check if the given permutation can be obtained from the permutation 1 2 ... n using exactly one segment reversal. If it is possible, find the segment itself.

## 输入格式

The first line contains an integer n (1 ≤ n ≤ 1000) which is the number of coins in <PERSON><PERSON><PERSON>'s collection. The second line contains space-separated n integers which are the spoilt sequence of coins. It is guaranteed that the given sequence is a permutation, i.e. it contains only integers from 1 to n, and every number is used exactly 1 time.

## 输出格式

If it is impossible to obtain the given permutation from the original one in exactly one action, print 0 0. Otherwise, print two numbers l r (1 ≤ l < r ≤ n) which are the endpoints of the segment that needs to be reversed to obtain from permutation 1 2 ... n the given one.

## 样例

### 样例 1

**输入**:
```
8
1 6 5 4 3 2 7 8
```

**输出**:
```
2 6
```

### 样例 2

**输入**:
```
4
2 3 4 1
```

**输出**:
```
0 0
```

### 样例 3

**输入**:
```
4
1 2 3 4
```

**输出**:
```
0 0
```

