# Bear and Blocks

**题目ID**: 573/B  
**比赛**: Codeforces Round 318 [RussianCodeCup Thanks-Round] (Div. 1)  
**年份**: 2015  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON><PERSON> is a little bear who loves to play. Today he is playing by destroying block towers. He built n towers in a row. The i-th tower is made of hi identical blocks. For clarification see picture for the first sample.

<PERSON><PERSON> will repeat the following operation till everything is destroyed.

Block is called internal if it has all four neighbors, i.e. it has each side (top, left, down and right) adjacent to other block or to the floor. Otherwise, block is boundary. In one operation <PERSON><PERSON> destroys all boundary blocks. His paws are very fast and he destroys all those blocks at the same time.

<PERSON><PERSON> is ready to start. You task is to count how many operations will it take him to destroy all towers.

## 输入格式

The first line contains single integer n (1 ≤ n ≤ 105).

The second line contains n space-separated integers h1, h2, ..., hn (1 ≤ hi ≤ 109) — sizes of towers.

## 输出格式

Print the number of operations needed to destroy all towers.

## 样例

### 样例 1

**输入**:
```
6
2 1 4 6 2 2
```

**输出**:
```
3
```

### 样例 2

**输入**:
```
7
3 3 3 1 3 3 3
```

**输出**:
```
2
```

## 备注

The picture below shows all three operations for the first sample test. Each time boundary blocks are marked with red color.

## 题解

In one operation the highest block in each tower disappears. So do all blocks above heights of neighbour towers. And all other blocks remain. It means that in one operation all heights change according to formula hi = min(hi - 1, hi - 1, hi + 1) where h0 = hn + 1 = 0. By using this formula two times we get height after two operations: hi = max(0, min(hi - 2, hi - 1 - 1, hi - 2, hi + 1 - 1, hi + 2)) and so on. From now I will omit max(0, ...) part to make it easier to read.
After k operations we get hi = min(Left, Right) where Left = min(hi - j - (k - j)) = min(hi - j + j - k) for $$j \in \{0, 1, \ldots, k\}$$ and Right is defined similarly. hi becomes zero when Left or Right becomes zero. And Left becomes zero when k = min(hi - j + j) - we will find this value for all i. If you are now lost in this editorial, try to draw some test and analyze my formulas with it.
For each i we are looking for min(hi - j + j). We can iterate over i from left to right keeping some variable best:
1) best = max(best, hi) to consider hi - 0
2) best is our answer for i
3) best +  +
We should to the same for Right and take min(Left, Right) for each i. Then final answer is maximum over answers for i.

