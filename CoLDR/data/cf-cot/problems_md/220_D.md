# Little Elephant and Triangle

**题目ID**: 220/D  
**比赛**: Codeforces Round 136 (Div. 1)  
**年份**: 2012  
**时间限制**: 3.0秒  
**内存限制**: 256MB  

## 题目描述

The <PERSON> Elephant is playing with the Cartesian coordinates' system. Most of all he likes playing with integer points. The Little Elephant defines an integer point as a pair of integers (x; y), such that 0 ≤ x ≤ w and 0 ≤ y ≤ h. Thus, the Little Elephant knows only (w + 1)·(h + 1) distinct integer points.

The <PERSON> Elephant wants to paint a triangle with vertexes at integer points, the triangle's area must be a positive integer. For that, he needs to find the number of groups of three points that form such triangle. At that, the order of points in a group matters, that is, the group of three points (0;0), (0;2), (2;2) isn't equal to the group (0;2), (0;0), (2;2).

Help the <PERSON> Elephant to find the number of groups of three integer points that form a nondegenerate triangle with integer area.

## 输入格式

A single line contains two integers w and h (1 ≤ w, h ≤ 4000).

## 输出格式

In a single output line print an integer — the remainder of dividing the answer to the problem by 1000000007 (109 + 7).

## 样例

### 样例 1

**输入**:
```
2 1
```

**输出**:
```
36
```

### 样例 2

**输入**:
```
2 2
```

**输出**:
```
240
```

