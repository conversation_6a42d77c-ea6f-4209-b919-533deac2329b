# <PERSON><PERSON> and <PERSON><PERSON>

**题目ID**: 535/D  
**比赛**: Codeforces Round 299 (Div. 2)  
**年份**: 2015  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON><PERSON> is a strange creature. Usually "zzz" comes out of people's mouth while sleeping, but string s of length n comes out from <PERSON><PERSON>' mouth instead.

Today <PERSON><PERSON> fell asleep in <PERSON><PERSON>' place. While he was sleeping, <PERSON><PERSON> did a little process on s. <PERSON><PERSON> has a favorite string p. He determined all positions x1 < x2 < ... < xk where p matches s. More formally, for each xi (1 ≤ i ≤ k) he condition sxisxi + 1... sxi + |p| - 1 = p is fullfilled.

Then <PERSON><PERSON> wrote down one of subsequences of x1, x2, ... xk (possibly, he didn't write anything) on a piece of paper. Here a sequence b is a subsequence of sequence a if and only if we can turn a into b by removing some of its elements (maybe no one of them or all).

After <PERSON><PERSON> woke up, <PERSON><PERSON> told him everything. He couldn't remember string s, but he knew that both p and s only contains lowercase English letters and also he had the subsequence he had written on that piece of paper.

<PERSON><PERSON> wonders, what is the number of possible values of s? He asked <PERSON><PERSON><PERSON><PERSON>, but he wasn't smart enough to solve this. So, <PERSON><PERSON> asked you to calculate this number for him.

Answer can be very large, so <PERSON><PERSON> wants you to print the answer modulo 109 + 7.

## 输入格式

The first line contains two integers n and m, the length of s and the length of the subsequence Malekas wrote down (1 ≤ n ≤ 106 and 0 ≤ m ≤ n - |p| + 1).

The second line contains string p (1 ≤ |p| ≤ n).

The next line contains m space separated integers y1, y2, ..., ym, Malekas' subsequence (1 ≤ y1 < y2 < ... < ym ≤ n - |p| + 1).

## 输出格式

In a single line print the answer modulo 1000 000 007.

## 样例

### 样例 1

**输入**:
```
6 2
ioi
1 3
```

**输出**:
```
26
```

### 样例 2

**输入**:
```
5 2
ioi
1 2
```

**输出**:
```
0
```

## 备注

In the first sample test all strings of form "ioioi?" where the question mark replaces arbitrary English letter satisfy.

Here |x| denotes the length of string x.

Please note that it's possible that there is no such string (answer is 0).

