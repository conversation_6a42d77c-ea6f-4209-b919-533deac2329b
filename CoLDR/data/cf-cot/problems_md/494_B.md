# Obsessive String

**题目ID**: 494/B  
**比赛**: Codeforces Round 282 (Div. 1)  
**年份**: 2014  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON><PERSON> has recently found a string t and suddenly became quite fond of it. He spent several days trying to find all occurrences of t in other strings he had. Finally he became tired and started thinking about the following problem. Given a string s how many ways are there to extract k ≥ 1 non-overlapping substrings from it such that each of them contains string t as a substring? More formally, you need to calculate the number of ways to choose two sequences a1, a2, ..., ak and b1, b2, ..., bk satisfying the following requirements:

- k ≥ 1
- $$$$
- $$\forall i \left( 1 \leq i \leq k \right) b_i \geq a_i$$
- $$\forall i \left( 2 \leq i \leq k \right) a_i > b_{i-1}$$
- $$\forall i \left( 1 \leq i \leq k \right)$$  t is a substring of string saisai + 1... sbi (string s is considered as 1-indexed).

As the number of ways can be rather large print it modulo 109 + 7.

## 输入格式

Input consists of two lines containing strings s and t (1 ≤ |s|, |t| ≤ 105). Each string consists of lowercase Latin letters.

## 输出格式

Print the answer in a single line.

## 样例

### 样例 1

**输入**:
```
ababa
aba
```

**输出**:
```
5
```

### 样例 2

**输入**:
```
welcometoroundtwohundredandeightytwo
d
```

**输出**:
```
274201
```

### 样例 3

**输入**:
```
ddd
d
```

**输出**:
```
12
```

