# Hack it!

**题目ID**: 468/C  
**比赛**: Codeforces Round 268 (Div. 1)  
**年份**: 2014  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

Little X has met the following problem recently.

Let's define f(x) as the sum of digits in decimal representation of number x (for example, f(1234) = 1 + 2 + 3 + 4). You are to calculate $$\sum_{i=l}^{r} f(i) \bmod a.$$

Of course Little <PERSON> has solved this problem quickly, has locked it, and then has tried to hack others. He has seen the following C++ code:

## 输入格式

The first line contains a single integer a (1 ≤ a ≤ 1018).

## 输出格式

Print two integers: l, r (1 ≤ l ≤ r < 10200) — the required test data. Leading zeros aren't allowed. It's guaranteed that the solution exists.

## 样例

### 样例 1

**输入**:
```
46
```

**输出**:
```
1 10
```

### 样例 2

**输入**:
```
126444381000032
```

**输出**:
```
2333333 2333333333333
```

