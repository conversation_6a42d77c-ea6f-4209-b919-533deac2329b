# <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>'s Painting

**题目ID**: 475/C  
**比赛**: Bayan 2015 Contest Warm Up  
**年份**: 2014  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

Rumors say that one of <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>'s paintings has been altered. A rectangular brush has been moved right and down on the painting.

Consider the painting as a n × m rectangular grid. At the beginning an x × y rectangular brush is placed somewhere in the frame, with edges parallel to the frame, (1 ≤ x ≤ n, 1 ≤ y ≤ m). Then the brush is moved several times. Each time the brush is moved one unit right or down. The brush has been strictly inside the frame during the painting. The brush alters every cell it has covered at some moment.

You have found one of the old <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ol<PERSON>'s paintings. You want to know if it's possible that it has been altered in described manner. If yes, you also want to know minimum possible area of the brush.

## 输入格式

The first line of input contains two integers n and m, (1 ≤ n, m ≤ 1000), denoting the height and width of the painting.

The next n lines contain the painting. Each line has m characters. Character 'X' denotes an altered cell, otherwise it's showed by '.'. There will be at least one altered cell in the painting.

## 输出格式

Print the minimum area of the brush in a line, if the painting is possibly altered, otherwise print  - 1.

## 样例

### 样例 1

**输入**:
```
4 4
XX..
XX..
XXXX
XXXX
```

**输出**:
```
4
```

### 样例 2

**输入**:
```
4 4
....
.XXX
.XXX
....
```

**输出**:
```
2
```

### 样例 3

**输入**:
```
4 5
XXXX.
XXXX.
.XX..
.XX..
```

**输出**:
```
-1
```

