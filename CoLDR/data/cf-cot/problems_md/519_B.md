# A and B and Compilation Errors

**题目ID**: 519/B  
**比赛**: Codeforces Round 294 (Div. 2)  
**年份**: 2015  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

A and <PERSON> are preparing themselves for programming contests.

<PERSON> loves to debug his code. But before he runs the solution and starts debugging, he has to first compile the code.

Initially, the compiler displayed n compilation errors, each of them is represented as a positive integer. After some effort, <PERSON> managed to fix some mistake and then another one mistake.

However, despite the fact that <PERSON> is sure that he corrected the two errors, he can not understand exactly what compilation errors disappeared — the compiler of the language which <PERSON> uses shows errors in the new order every time! <PERSON> is sure that unlike many other programming languages, compilation errors for his programming language do not depend on each other, that is, if you correct one error, the set of other error does not change.

Can you help <PERSON> find out exactly what two errors he corrected?

## 输入格式

The first line of the input contains integer n (3 ≤ n ≤ 105) — the initial number of compilation errors.

The second line contains n space-separated integers a1, a2, ..., an (1 ≤ ai ≤ 109) — the errors the compiler displayed for the first time.

The third line contains n - 1 space-separated integers b1, b2, ..., bn - 1 — the errors displayed at the second compilation. It is guaranteed that the sequence in the third line contains all numbers of the second string except for exactly one.

The fourth line contains n - 2 space-separated integers с1, с2, ..., сn - 2 — the errors displayed at the third compilation. It is guaranteed that the sequence in the fourth line contains all numbers of the third line except for exactly one.

## 输出格式

Print two numbers on a single line: the numbers of the compilation errors that disappeared after B made the first and the second correction, respectively.

## 样例

### 样例 1

**输入**:
```
5
1 5 8 123 7
123 7 5 1
5 1 7
```

**输出**:
```
8
123
```

### 样例 2

**输入**:
```
6
1 4 3 3 5 7
3 7 5 4 3
4 3 7 5
```

**输出**:
```
1
3
```

## 备注

In the first test sample B first corrects the error number 8, then the error number 123.

In the second test sample B first corrects the error number 1, then the error number 3. Note that if there are multiple errors with the same number, B can correct only one of them in one step.

