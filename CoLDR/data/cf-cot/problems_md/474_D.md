# Flowers

**题目ID**: 474/D  
**比赛**: Codeforces Round 271 (Div. 2)  
**年份**: 2014  
**时间限制**: 1.5秒  
**内存限制**: 256MB  

## 题目描述

We saw the little game <PERSON><PERSON> made for <PERSON><PERSON>'s lunch. Now it's <PERSON><PERSON>'s dinner time and, as we all know, <PERSON><PERSON> eats flowers. At every dinner he eats some red and white flowers. Therefore a dinner can be represented as a sequence of several flowers, some of them white and some of them red.

But, for a dinner to be tasty, there is a rule: <PERSON><PERSON> wants to eat white flowers only in groups of size k.

Now <PERSON><PERSON> wonders in how many ways he can eat between a and b flowers. As the number of ways could be very large, print it modulo 1000000007 (109 + 7).

## 输入格式

Input contains several test cases.

The first line contains two integers t and k (1 ≤ t, k ≤ 105), where t represents the number of test cases.

The next t lines contain two integers ai and bi (1 ≤ ai ≤ bi ≤ 105), describing the i-th test.

## 输出格式

Print t lines to the standard output. The i-th line should contain the number of ways in which <PERSON><PERSON> can eat between ai and bi flowers at dinner modulo 1000000007 (109 + 7).

## 样例

### 样例 1

**输入**:
```
3 2
1 3
2 3
4 4
```

**输出**:
```
6
5
5
```

## 备注

- For K = 2 and length 1 Marmot can eat (R).
- For K = 2 and length 2 Marmot can eat (RR) and (WW).
- For K = 2 and length 3 Marmot can eat (RRR), (RWW) and (WWR).
- For K = 2 and length 4 Marmot can eat, for example, (WWWW) or (RWWR), but for example he can't eat (WWWR).

