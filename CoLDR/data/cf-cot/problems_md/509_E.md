# Pretty Song

**题目ID**: 509/E  
**比赛**: Codeforces Round 289 (Div. 2, ACM ICPC Rules)  
**年份**: 2015  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

When <PERSON> was studying in the seventh grade, he started listening to music a lot. In order to evaluate which songs he likes more, he introduced the notion of the song's prettiness. The title of the song is a word consisting of uppercase Latin letters. The prettiness of the song is the prettiness of its title.

Let's define the simple prettiness of a word as the ratio of the number of vowels in the word to the number of all letters in the word.

Let's define the prettiness of a word as the sum of simple prettiness of all the substrings of the word.

More formally, let's define the function vowel(c) which is equal to 1, if c is a vowel, and to 0 otherwise. Let si be the i-th character of string s, and si..j be the substring of word s, staring at the i-th character and ending at the j-th character (sisi + 1... sj, i ≤ j).

Then the simple prettiness of s is defined by the formula:

$$simple(s) = \frac{\sum_{i=1}^{|s|} vowel(s_i)}{|s|}$$

The prettiness of s equals

$$\sum_{1 \leq i \leq j \leq |s|} simple(s_{i..j})$$

Find the prettiness of the given song title.

We assume that the vowels are I, E, A, O, U, Y.

## 输入格式

The input contains a single string s (1 ≤ |s| ≤ 5·105) — the title of the song.

## 输出格式

Print the prettiness of the song with the absolute or relative error of at most 10 - 6.

## 样例

### 样例 1

**输入**:
```
IEAIAIO
```

**输出**:
```
28.0000000
```

### 样例 2

**输入**:
```
BYOB
```

**输出**:
```
5.8333333
```

### 样例 3

**输入**:
```
YISVOWEL
```

**输出**:
```
17.0500000
```

## 备注

In the first sample all letters are vowels. The simple prettiness of each substring is 1. The word of length 7 has 28 substrings. So, the prettiness of the song equals to 28.

