# Reading

**题目ID**: 234/B  
**比赛**: Codeforces Round 145 (Div. 2, ACM-ICPC Rules)  
**年份**: 2012  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON><PERSON><PERSON> is going to the Olympics in the city Ntown by train. The boy wants to read the textbook to prepare for the Olympics. He counted that he needed k hours for this. He also found that the light in the train changes every hour. The light is measured on a scale from 0 to 100, where 0 is very dark, and 100 is very light.

<PERSON><PERSON><PERSON> has a train lighting schedule for all n hours of the trip — n numbers from 0 to 100 each (the light level in the first hour, the second hour and so on). During each of those hours he will either read the whole time, or not read at all. He wants to choose k hours to read a book, not necessarily consecutive, so that the minimum level of light among the selected hours were maximum. <PERSON><PERSON><PERSON> is very excited before the upcoming contest, help him choose reading hours.

## 输入格式

The first input line contains two integers n and k (1 ≤ n ≤ 1000, 1 ≤ k ≤ n) — the number of hours on the train and the number of hours to read, correspondingly. The second line contains n space-separated integers ai (0 ≤ ai ≤ 100), ai is the light level at the i-th hour.

## 输出格式

In the first output line print the minimum light level <PERSON><PERSON><PERSON> will read at. In the second line print k distinct space-separated integers b1, b2, ..., bk, — the indexes of hours Vasya will read at (1 ≤ bi ≤ n). The hours are indexed starting from 1. If there are multiple optimal solutions, print any of them. Print the numbers bi in an arbitrary order.

## 样例

### 样例 1

**输入**:
```
5 3
20 10 30 40 10
```

**输出**:
```
20
1 3 4
```

### 样例 2

**输入**:
```
6 5
90 20 35 40 60 100
```

**输出**:
```
35
1 3 4 5 6
```

## 备注

In the first sample Vasya should read at the first hour (light 20), third hour (light 30) and at the fourth hour (light 40). The minimum light Vasya will have to read at is 20.

