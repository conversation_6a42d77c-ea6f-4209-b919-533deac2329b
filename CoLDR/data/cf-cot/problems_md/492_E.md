# Vanya and Field

**题目ID**: 492/E  
**比赛**: Codeforces Round 280 (Div. 2)  
**年份**: 2014  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON><PERSON> decided to walk in the field of size n × n cells. The field contains m apple trees, the i-th apple tree is at the cell with coordinates (xi, yi). <PERSON><PERSON> moves towards vector (dx, dy). That means that if <PERSON><PERSON> is now at the cell (x, y), then in a second he will be at cell $$( ( x + d x ) \bmod n, ( y + d y ) \bmod n )$$. The following condition is satisfied for the vector: $$\gcd(n,dx)=\gcd(n,dy)=1$$, where $$\gcd(a,b)$$ is the largest integer that divides both a and b. <PERSON><PERSON> ends his path when he reaches the square he has already visited.

<PERSON><PERSON> wonders, from what square of the field he should start his path to see as many apple trees as possible.

## 输入格式

The first line contains integers n, m, dx, dy(1 ≤ n ≤ 106, 1 ≤ m ≤ 105, 1 ≤ dx, dy ≤ n) — the size of the field, the number of apple trees and the vector of <PERSON><PERSON>'s movement. Next m lines contain integers xi, yi (0 ≤ xi, yi ≤ n - 1) — the coordinates of apples. One cell may contain multiple apple trees.

## 输出格式

Print two space-separated numbers — the coordinates of the cell from which you should start your path. If there are several answers you are allowed to print any of them.

## 样例

### 样例 1

**输入**:
```
5 5 2 3
0 0
1 2
1 3
2 4
3 1
```

**输出**:
```
1 3
```

### 样例 2

**输入**:
```
2 3 1 1
0 0
0 1
1 1
```

**输出**:
```
0 0
```

## 备注

In the first sample Vanya's path will look like: (1, 3) - (3, 1) - (0, 4) - (2, 2) - (4, 0) - (1, 3)

In the second sample: (0, 0) - (1, 1) - (0, 0)

