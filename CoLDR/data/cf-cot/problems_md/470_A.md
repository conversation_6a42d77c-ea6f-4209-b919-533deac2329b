# Crystal Ball Sequence

**题目ID**: 470/A  
**比赛**: Surprise Language Round 7  
**年份**: 2014  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

Crystal ball sequence on hexagonal lattice is defined as follows: n-th element is the number of lattice points inside a hexagon with (n + 1) points on each side. The formula is Hn = 3·n·(n + 1) + 1. You are given n; calculate n-th element of the sequence.

## 输入格式

The only line of input contains an integer n (0 ≤ n ≤ 9).

## 输出格式

Output the n-th element of crystal ball sequence.

## 样例

### 样例 1

**输入**:
```
1
```

**输出**:
```
7
```

### 样例 2

**输入**:
```
3
```

**输出**:
```
37
```

