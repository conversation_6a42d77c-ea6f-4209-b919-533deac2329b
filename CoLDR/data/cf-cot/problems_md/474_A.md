# Keyboard

**题目ID**: 474/A  
**比赛**: Codeforces Round 271 (Div. 2)  
**年份**: 2014  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

Our good friend <PERSON><PERSON> is trying to code a big message. He is typing on an unusual keyboard with characters arranged in following way:

Unfortunately <PERSON><PERSON> is blind, so sometimes it is problem for him to put his hands accurately. He accidentally moved both his hands with one position to the left or to the right. That means that now he presses not a button he wants, but one neighboring button (left or right, as specified in input).

We have a sequence of characters he has typed and we want to find the original message.

## 输入格式

First line of the input contains one letter describing direction of shifting ('L' or 'R' respectively for left or right).

Second line contains a sequence of characters written by <PERSON><PERSON>. The size of this sequence will be no more than 100. Sequence contains only symbols that appear on <PERSON><PERSON>'s keyboard. It doesn't contain spaces as there is no space on <PERSON><PERSON>'s keyboard.

It is guaranteed that even though <PERSON><PERSON> hands are moved, he is still pressing buttons on keyboard and not hitting outside it.

## 输出格式

Print a line that contains the original message.

## 样例

### 样例 1

**输入**:
```
R
s;;upimrrfod;pbr
```

**输出**:
```
ally<PERSON><PERSON><PERSON>love
```

