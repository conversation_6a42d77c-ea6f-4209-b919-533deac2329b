# Coins

**题目ID**: 47/B  
**比赛**: Codeforces Beta Round 44 (Div. 2)  
**年份**: 2010  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

One day <PERSON><PERSON><PERSON> came across three Berland coins. They didn't have any numbers that's why <PERSON><PERSON><PERSON> didn't understand how their denominations differ. He supposed that if one coin is heavier than the other one, then it should be worth more. <PERSON><PERSON><PERSON> weighed all the three pairs of coins on pan balance scales and told you the results. Find out how the deminations of the coins differ or if <PERSON><PERSON><PERSON> has a mistake in the weighting results. No two coins are equal.

## 输入格式

The input data contains the results of all the weighting, one result on each line. It is guaranteed that every coin pair was weighted exactly once. <PERSON><PERSON><PERSON> labelled the coins with letters «A», «B» and «C». Each result is a line that appears as (letter)(> or < sign)(letter). For example, if coin "A" proved lighter than coin "B", the result of the weighting is A<B.

## 输出格式

It the results are contradictory, print Impossible. Otherwise, print without spaces the rearrangement of letters «A», «B» and «C» which represent the coins in the increasing order of their weights.

## 样例

### 样例 1

**输入**:
```
A>B
C<B
A>C
```

**输出**:
```
CBA
```

### 样例 2

**输入**:
```
A<B
B>C
C>A
```

**输出**:
```
ACB
```

