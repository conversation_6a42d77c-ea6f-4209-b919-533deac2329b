# Number Challenge

**题目ID**: 235/E  
**比赛**: Codeforces Round 146 (Div. 1)  
**年份**: 2012  
**时间限制**: 3.0秒  
**内存限制**: 512MB  

## 题目描述

Let's denote d(n) as the number of divisors of a positive integer n. You are given three integers a, b and c. Your task is to calculate the following sum:

$$\sum_{i=1}^{a}\sum_{j=1}^{b}\sum_{k=1}^{c}d(i \cdot j \cdot k)$$

Find the sum modulo 1073741824 (230).

## 输入格式

The first line contains three space-separated integers a, b and c (1 ≤ a, b, c ≤ 2000).

## 输出格式

Print a single integer — the required sum modulo 1073741824 (230).

## 样例

### 样例 1

**输入**:
```
2 2 2
```

**输出**:
```
20
```

### 样例 2

**输入**:
```
4 4 4
```

**输出**:
```
328
```

### 样例 3

**输入**:
```
10 10 10
```

**输出**:
```
11536
```

## 备注

For the first example.

- d(1·1·1) = d(1) = 1;
- d(1·1·2) = d(2) = 2;
- d(1·2·1) = d(2) = 2;
- d(1·2·2) = d(4) = 3;
- d(2·1·1) = d(2) = 2;
- d(2·1·2) = d(4) = 3;
- d(2·2·1) = d(4) = 3;
- d(2·2·2) = d(8) = 4.

So the result is 1 + 2 + 2 + 3 + 2 + 3 + 3 + 4 = 20.

