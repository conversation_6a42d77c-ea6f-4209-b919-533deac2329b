# <PERSON> and <PERSON>

**题目ID**: 562/E  
**比赛**: VK Cup 2015 - Finals  
**年份**: 2015  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

Two kittens, <PERSON> and <PERSON>, play with a pair of non-negative integers x and y. As you can guess from their names, kitten Max loves to maximize and kitten <PERSON> loves to minimize. As part of this game <PERSON> wants to make sure that both numbers, x and y became negative at the same time, and kitten <PERSON> tries to prevent him from doing so.

Each kitten has a set of pairs of integers available to it. Kitten Max has n pairs of non-negative integers (ai, bi) (1 ≤ i ≤ n), and kitten Min has m pairs of non-negative integers (cj, dj) (1 ≤ j ≤ m). As kitten Max makes a move, it can take any available pair (ai, bi) and add ai to x and bi to y, and kitten Min can take any available pair (cj, dj) and subtract cj from x and dj from y. Each kitten can use each pair multiple times during distinct moves.

<PERSON> moves first. Kitten Min is winning if at some moment both numbers a, b are negative simultaneously. Otherwise, the winner of the game is kitten <PERSON>. Determine which kitten wins if both of them play optimally.

## 输入格式

The first line contains two integers, n and m (1 ≤ n, m ≤ 100 000) — the number of pairs of numbers available to <PERSON> and <PERSON>, correspondingly.

The second line contains two integers x, y (1 ≤ x, y ≤ 109) — the initial values of numbers with which the kittens are playing.

Next n lines contain the pairs of numbers ai, bi (1 ≤ ai, bi ≤ 109) — the pairs available to Max.

The last m lines contain pairs of numbers cj, dj (1 ≤ cj, dj ≤ 109) — the pairs available to Min.

## 输出格式

Print «Max» (without the quotes), if kitten Max wins, or "Min" (without the quotes), if kitten Min wins.

## 样例

### 样例 1

**输入**:
```
2 2
42 43
2 3
3 2
3 10
10 3
```

**输出**:
```
Min
```

### 样例 2

**输入**:
```
1 1
1 1
3 4
1 1
```

**输出**:
```
Max
```

## 备注

In the first test from the statement Min can respond to move (2, 3) by move (3, 10), and to move (3, 2) by move (10, 3). Thus, for each pair of Max and Min's moves the values of both numbers x and y will strictly decrease, ergo, Min will win sooner or later.

In the second sample test after each pair of Max and Min's moves both numbers x and y only increase, thus none of them will become negative.

