# <PERSON> and Feet

**题目ID**: 547/B  
**比赛**: Codeforces Round 305 (Div. 1)  
**年份**: 2015  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON> is the president of country What-The-Fatherland. There are n bears living in this country besides <PERSON>. All of them are standing in a line and they are numbered from 1 to n from left to right. i-th bear is exactly ai feet high.

A group of bears is a non-empty contiguous segment of the line. The size of a group is the number of bears in that group. The strength of a group is the minimum height of the bear in that group.

<PERSON> is a curious to know for each x such that 1 ≤ x ≤ n the maximum strength among all groups of size x.

## 输入格式

The first line of input contains integer n (1 ≤ n ≤ 2 × 105), the number of bears.

The second line contains n integers separated by space, a1, a2, ..., an (1 ≤ ai ≤ 109), heights of bears.

## 输出格式

Print n integers in one line. For each x from 1 to n, print the maximum strength among all groups of size x.

## 样例

### 样例 1

**输入**:
```
10
1 2 3 4 5 4 3 2 1 6
```

**输出**:
```
6 4 4 3 3 2 2 1 1 1
```

