# Little Elephant and Array

**题目ID**: 220/B  
**比赛**: Codeforces Round 136 (Div. 1)  
**年份**: 2012  
**时间限制**: 4.0秒  
**内存限制**: 256MB  

## 题目描述

The <PERSON> Elephant loves playing with arrays. He has array a, consisting of n positive integers, indexed from 1 to n. Let's denote the number with index i as ai.

Additionally the Little Elephant has m queries to the array, each query is characterised by a pair of integers lj and rj (1 ≤ lj ≤ rj ≤ n). For each query lj, rj the Little Elephant has to count, how many numbers x exist, such that number x occurs exactly x times among numbers alj, alj + 1, ..., arj.

Help the <PERSON> Elephant to count the answers to all queries.

## 输入格式

The first line contains two space-separated integers n and m (1 ≤ n, m ≤ 105) — the size of array a and the number of queries to it. The next line contains n space-separated positive integers a1, a2, ..., an (1 ≤ ai ≤ 109). Next m lines contain descriptions of queries, one per line. The j-th of these lines contains the description of the j-th query as two space-separated integers lj and rj (1 ≤ lj ≤ rj ≤ n).

## 输出格式

In m lines print m integers — the answers to the queries. The j-th line should contain the answer to the j-th query.

## 样例

### 样例 1

**输入**:
```
7 2
3 1 2 2 3 3 7
1 7
3 4
```

**输出**:
```
3
1
```

