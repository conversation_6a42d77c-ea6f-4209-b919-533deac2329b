# Sum and product

**题目ID**: 530/E  
**比赛**: VK Cup 2015 - Wild Card Round 1  
**年份**: 2015  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

You are given integers N and D. Find N positive integers x1...xN such that the difference of their product and their sum equals D.

## 输入格式

The only line of input contains integers N (2 ≤ N ≤ 1000) and D (0 ≤ D ≤ 1000).

## 输出格式

Output N integers that satisfy the given condition in non-decreasing order (in a single line, separated with spaces). Note that some numbers can be equal. Numbers printed by you must not exceed 106.

## 样例

### 样例 1

**输入**:
```
2 1
```

**输出**:
```
2 3
```

### 样例 2

**输入**:
```
3 5
```

**输出**:
```
1 2 8
```

