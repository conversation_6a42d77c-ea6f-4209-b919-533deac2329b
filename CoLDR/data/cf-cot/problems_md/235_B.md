# Let's Play Osu!

**题目ID**: 235/B  
**比赛**: Codeforces Round 146 (Div. 1)  
**年份**: 2012  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

You're playing a game called Osu! Here's a simplified version of it. There are n clicks in a game. For each click there are two outcomes: correct or bad. Let us denote correct as "O", bad as "X", then the whole play can be encoded as a sequence of n characters "O" and "X".

Using the play sequence you can calculate the score for the play as follows: for every maximal consecutive "O"s block, add the square of its length (the number of characters "O") to the score. For example, if your play can be encoded as "OOXOOOXXOO", then there's three maximal consecutive "O"s block "OO", "OOO", "OO", so your score will be 22 + 32 + 22 = 17. If there are no correct clicks in a play then the score for the play equals to 0.

You know that the probability to click the i-th (1 ≤ i ≤ n) click correctly is pi. In other words, the i-th character in the play sequence has pi probability to be "O", 1 - pi to be "X". You task is to calculate the expected score for your play.

## 输入格式

The first line contains an integer n (1 ≤ n ≤ 105) — the number of clicks. The second line contains n space-separated real numbers p1, p2, ..., pn (0 ≤ pi ≤ 1).

There will be at most six digits after the decimal point in the given pi.

## 输出格式

Print a single real number — the expected score for your play. Your answer will be considered correct if its absolute or relative error does not exceed 10 - 6.

## 样例

### 样例 1

**输入**:
```
3
0.5 0.5 0.5
```

**输出**:
```
2.750000000000000
```

### 样例 2

**输入**:
```
4
0.7 0.2 0.1 0.9
```

**输出**:
```
2.489200000000000
```

### 样例 3

**输入**:
```
5
1 1 1 1 1
```

**输出**:
```
25.000000000000000
```

## 备注

For the first example. There are 8 possible outcomes. Each has a probability of 0.125.

- "OOO"  →  32 = 9;
- "OOX"  →  22 = 4;
- "OXO"  →  12 + 12 = 2;
- "OXX"  →  12 = 1;
- "XOO"  →  22 = 4;
- "XOX"  →  12 = 1;
- "XXO"  →  12 = 1;
- "XXX"  →  0.

So the expected score is $${ \frac { 9 + 4 + 2 + 1 + 4 + 1 + 1 } { 8 } } = 2. 7 5$$

