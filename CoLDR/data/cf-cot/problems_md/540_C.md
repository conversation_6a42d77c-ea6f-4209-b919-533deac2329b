# Ice Cave

**题目ID**: 540/C  
**比赛**: Codeforces Round 301 (Div. 2)  
**年份**: 2015  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

You play a computer game. Your character stands on some level of a multilevel ice cave. In order to move on forward, you need to descend one level lower and the only way to do this is to fall through the ice.

The level of the cave where you are is a rectangular square grid of n rows and m columns. Each cell consists either from intact or from cracked ice. From each cell you can move to cells that are side-adjacent with yours (due to some limitations of the game engine you cannot make jumps on the same place, i.e. jump from a cell to itself). If you move to the cell with cracked ice, then your character falls down through it and if you move to the cell with intact ice, then the ice on this cell becomes cracked.

Let's number the rows with integers from 1 to n from top to bottom and the columns with integers from 1 to m from left to right. Let's denote a cell on the intersection of the r-th row and the c-th column as (r, c).

You are staying in the cell (r1, c1) and this cell is cracked because you've just fallen here from a higher level. You need to fall down through the cell (r2, c2) since the exit to the next level is there. Can you do this?

## 输入格式

The first line contains two integers, n and m (1 ≤ n, m ≤ 500) — the number of rows and columns in the cave description.

Each of the next n lines describes the initial state of the level of the cave, each line consists of m characters "." (that is, intact ice) and "X" (cracked ice).

The next line contains two integers, r1 and c1 (1 ≤ r1 ≤ n, 1 ≤ c1 ≤ m) — your initial coordinates. It is guaranteed that the description of the cave contains character 'X' in cell (r1, c1), that is, the ice on the starting cell is initially cracked.

The next line contains two integers r2 and c2 (1 ≤ r2 ≤ n, 1 ≤ c2 ≤ m) — the coordinates of the cell through which you need to fall. The final cell may coincide with the starting one.

## 输出格式

If you can reach the destination, print 'YES', otherwise print 'NO'.

## 样例

### 样例 1

**输入**:
```
4 6
X...XX
...XX.
.X..X.
......
1 6
2 2
```

**输出**:
```
YES
```

### 样例 2

**输入**:
```
5 4
.X..
...X
X.X.
....
.XX.
5 3
1 1
```

**输出**:
```
NO
```

### 样例 3

**输入**:
```
4 7
..X.XX.
.XX..X.
X...X..
X......
2 2
1 6
```

**输出**:
```
YES
```

## 备注

In the first sample test one possible path is:

$$(1,6)\rightarrow(2,6)\rightarrow(3,6)\rightarrow(4,5)\rightarrow(4,4)\rightarrow(4,3)\rightarrow(4,2)\rightarrow(4,1)\rightarrow(3,2)\rightarrow(2,3)\rightarrow(2,2)$$

After the first visit of cell (2, 2) the ice on it cracks and when you step there for the second time, your character falls through the ice as intended.

