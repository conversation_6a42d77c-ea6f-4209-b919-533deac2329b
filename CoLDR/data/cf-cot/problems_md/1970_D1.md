# Arithmancy (Easy)

**题目ID**: 1970/D1  
**比赛**: Helvetic Coding Contest 2024 online mirror (teams allowed, unrated)  
**年份**: 2024  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

Professor <PERSON> is preparing to teach her Ari<PERSON><PERSON>cy class. She needs to prepare $$$n$$$ distinct magic words for the class. Each magic word is a string consisting of characters X and O. A spell is a string created by concatenating two magic words together. The power of a spell is equal to the number of its different non-empty substrings. For example, the power of the spell XOXO is equal to 7, because it has 7 different substrings: X, O, XO, OX, XOX, OXO and XOXO.

Each student will create their own spell by concatenating two magic words. Since the students are not very good at magic yet, they will choose each of the two words independently and uniformly at random from the $$$n$$$ words provided by <PERSON>. It is therefore also possible that the two words a student chooses are the same. Each student will then compute the power of their spell, and tell it to <PERSON>. In order to check their work, and of course to impress the students, Professor <PERSON> needs to find out which two magic words and in which order were concatenated by each student.

Your program needs to perform the role of Professor <PERSON>: first, create $$$n$$$ distinct magic words, and then handle multiple requests where it is given the spell power and needs to determine the indices of the two magic words, in the correct order, that were used to create the corresponding spell.

## 输入格式

None

## 输出格式

None

## 样例

### 样例 1

**输入**:
```
2


2
15

11
```

**输出**:
```
XOXO
X


1 1

2 1
```

## 题解

Given that we need to output 3 words only, we can manually (trying a few options on paper until we find a solution) construct 3 words such that all 9 spell powers are different.

