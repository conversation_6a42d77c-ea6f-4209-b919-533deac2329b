# <PERSON> the Fool VS Go<PERSON>ych the Dragon

**题目ID**: 48/E  
**比赛**: School Personal Contest #3 (Winter Computer School 2010/11) - Codeforces Beta Round 45 (ACM-ICPC Rules)  
**年份**: 2010  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

Once upon a time in a kingdom far, far away… Okay, let’s start at the point where <PERSON> the Fool met <PERSON><PERSON><PERSON> the Dragon. <PERSON> took out his magic sword and the battle began. First <PERSON><PERSON><PERSON> had h heads and t tails. With each strike of the sword <PERSON> can either cut off several heads (from 1 to n, but not more than <PERSON><PERSON><PERSON> has at the moment), or several tails (from 1 to m, but not more than <PERSON><PERSON><PERSON> has at the moment). At the same time, horrible though it seems, <PERSON><PERSON><PERSON> the Dragon can also grow new heads and tails. And the number of growing heads and tails is determined uniquely by the number of heads or tails cut by the current strike. When the total number of heads and tails exceeds R, <PERSON><PERSON><PERSON> the Dragon strikes its final blow and destroys <PERSON> the Fool. That’s why <PERSON> aims to cut off all the dragon’s heads and tails as quickly as possible and win. The events can also develop in a third way: neither of the opponents can win over the other one and they will continue fighting forever.

The tale goes like this; easy to say, hard to do. Your task is to write a program that will determine the battle’s outcome. Consider that <PERSON> strikes consecutively. After each blow <PERSON><PERSON><PERSON> grows a number of new heads and tails depending on the number of cut ones. Gorynych the <PERSON> is defeated if after the blow he loses all his heads and tails and can’t grow new ones. <PERSON> fights in the optimal way (fools are lucky), i.e.

- if <PERSON> can win, he wins having struck the least number of blows;
- if it is impossible to defeat Gorynych, but is possible to resist him for an infinitely long period of time, then that’s the strategy <PERSON> chooses;
- if Gorynych wins in any case, <PERSON> aims to resist him for as long as possible.

## 输入格式

The first line contains three integers h, t and R (0 ≤ h, t, R ≤ 200, 0 < h + t ≤ R) which represent the initial numbers of Gorynych’s heads and tails and the largest total number of heads and tails with which Gorynych the Dragon does not yet attack. The next line contains integer n (1 ≤ n ≤ 200). The next n contain pairs of non-negative numbers "hi ti" which represent the number of heads and the number of tails correspondingly, that will grow if Gorynych has i heads (1 ≤ i ≤ n) cut. The next line contains an integer m (1 ≤ m ≤ 200) and then — the description of Gorynych’s behavior when his tails are cut off in the format identical to the one described above. All the numbers in the input file do not exceed 200.

## 输出格式

Print "Ivan" (without quotes) in the first line if Ivan wins, or "Zmey" (that means a dragon in Russian) if Gorynych the Dragon wins. In the second line print a single integer which represents the number of blows Ivan makes. If the battle will continue forever, print in the first line "Draw".

## 样例

### 样例 1

**输入**:
```
2 2 4
2
1 0
0 1
3
0 1
0 1
0 0
```

**输出**:
```
Ivan
2
```

### 样例 2

**输入**:
```
2 2 4
1
0 1
1
1 0
```

**输出**:
```
Draw
```

### 样例 3

**输入**:
```
2 2 5
1
1 1
1
3 0
```

**输出**:
```
Zmey
2
```

