# Guess Your Way Out!

**题目ID**: 507/C  
**比赛**: Codeforces Round 287 (Div. 2)  
**年份**: 2015  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON><PERSON> bought a new video game "Guess Your Way Out!". The goal of the game is to find an exit from the maze that looks like a perfect binary tree of height h. The player is initially standing at the root of the tree and the exit from the tree is located at some leaf node.

Let's index all the leaf nodes from the left to the right from 1 to 2h. The exit is located at some node n where 1 ≤ n ≤ 2h, the player doesn't know where the exit is so he has to guess his way out!

<PERSON><PERSON> follows simple algorithm to choose the path. Let's consider infinite command string "LRLRLRLRL..." (consisting of alternating characters 'L' and 'R'). <PERSON><PERSON> sequentially executes the characters of the string using following rules:

- Character 'L' means "go to the left child of the current node";
- Character 'R' means "go to the right child of the current node";
- If the destination node is already visited, <PERSON><PERSON> skips current command, otherwise he moves to the destination node;
- If <PERSON><PERSON> skipped two consecutive commands, he goes back to the parent of the current node before executing next command;
- If he reached a leaf node that is not the exit, he returns to the parent of the current node;
- If he reaches an exit, the game is finished.

Now <PERSON><PERSON> wonders, if he follows this algorithm, how many nodes he is going to visit before reaching the exit?

## 输入格式

Input consists of two integers h, n (1 ≤ h ≤ 50, 1 ≤ n ≤ 2h).

## 输出格式

Output a single integer representing the number of nodes (excluding the exit node) Amr is going to visit before reaching the exit by following this algorithm.

## 样例

### 样例 1

**输入**:
```
1 2
```

**输出**:
```
2
```

### 样例 2

**输入**:
```
2 3
```

**输出**:
```
5
```

### 样例 3

**输入**:
```
3 6
```

**输出**:
```
10
```

### 样例 4

**输入**:
```
10 1024
```

**输出**:
```
2046
```

## 备注

A perfect binary tree of height h is a binary tree consisting of h + 1 levels. Level 0 consists of a single node called root, level h consists of 2h nodes called leaves. Each node that is not a leaf has exactly two children, left and right one.

Following picture illustrates the sample test number 3. Nodes are labeled according to the order of visit.

