# MUH and Lots and Lots of Segments

**题目ID**: 471/E  
**比赛**: Codeforces Round 269 (Div. 2)  
**年份**: 2014  
**时间限制**: 2.0秒  
**内存限制**: 512MB  

## 题目描述

Polar bears <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> from the zoo of St. Petersburg and elephant <PERSON> from the zoo of Kiev decided to do some painting. As they were trying to create their first masterpiece, they made a draft on a piece of paper. The draft consists of n segments. Each segment was either horizontal or vertical. Now the friends want to simplify the draft by deleting some segments or parts of segments so that the final masterpiece meets three conditions:

1. <PERSON> wants to be able to paint the whole picture in one stroke: by putting the brush on the paper and never taking it off until the picture is ready. The brush can paint the same place multiple times. That's why all the remaining segments must form a single connected shape.
2. <PERSON><PERSON><PERSON><PERSON> wants the resulting shape to be simple. He defines a simple shape as a shape that doesn't contain any cycles.
3. Initially all the segment on the draft have integer startpoint and endpoint coordinates. <PERSON><PERSON><PERSON> doesn't like real coordinates and she wants this condition to be fulfilled after all the changes.

As in other parts the draft is already beautiful, the friends decided to delete such parts of the draft that the sum of lengths of the remaining segments is as large as possible. Your task is to count this maximum sum of the lengths that remain after all the extra segments are removed.

## 输入格式

The first line of the input contains integer n (1 ≤ n ≤ 2·105) — the number of segments on the draft. The next n lines contain four integers each: x1, y1, x2, y2 ( - 109 ≤ x1 ≤ x2 ≤ 109;  - 109 ≤ y1 ≤ y2 ≤ 109) — the two startpoint and the two endpoint coordinates of a segment. All segments are non-degenerative and either are strictly horizontal or strictly vertical.

No two horizontal segments share common points. No two vertical segments share common points.

## 输出格式

Print a single integer — the maximum sum of lengths for the remaining segments.

## 样例

### 样例 1

**输入**:
```
2
0 0 0 1
1 0 1 1
```

**输出**:
```
1
```

### 样例 2

**输入**:
```
4
0 0 1 0
0 0 0 1
1 -1 1 2
0 1 1 1
```

**输出**:
```
5
```

## 备注

The shapes that you can get in the two given samples are:

In the first sample you need to delete any segment as the two segments together do not form a single connected shape.

In the second sample the initial segments form a cycle, there are four ways to break the cycle: delete the first, second or fourth segment altogether or delete the middle of the third segment. The last way is shown on the picture.

