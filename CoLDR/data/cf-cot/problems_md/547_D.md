# <PERSON> and <PERSON>

**题目ID**: 547/D  
**比赛**: Codeforces Round 305 (Div. 1)  
**年份**: 2015  
**时间限制**: 3.0秒  
**内存限制**: 256MB  

## 题目描述

As everyone knows, bears love fish. But <PERSON> is a strange bear; He hates fish! The even more strange thing about him is he has an infinite number of blue and red fish.

He has marked n distinct points in the plane. i-th point is point (xi, yi). He wants to put exactly one fish in each of these points such that the difference between the number of red fish and the blue fish on each horizontal or vertical line is at most 1.

He can't find a way to perform that! Please help him.

## 输入格式

The first line of input contains integer n (1 ≤ n ≤ 2 × 105).

The next n lines contain the information about the points, i-th line contains two integers xi and yi (1 ≤ xi, yi ≤ 2 × 105), the i-th point coordinates.

It is guaranteed that there is at least one valid answer.

## 输出格式

Print the answer as a sequence of n characters 'r' (for red) or 'b' (for blue) where i-th character denotes the color of the fish in the i-th point.

## 样例

### 样例 1

**输入**:
```
4
1 1
1 2
2 1
2 2
```

**输出**:
```
brrb
```

### 样例 2

**输入**:
```
3
1 1
1 2
2 1
```

**输出**:
```
brr
```

