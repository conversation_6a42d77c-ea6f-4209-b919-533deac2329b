# Array

**题目ID**: 57/C  
**比赛**: Codeforces Beta Round 53  
**年份**: 2011  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON> the <PERSON> has been interested in arrays ever since he was a child. At the moment he is researching arrays with the length of n, containing only integers from 1 to n. He is not good at math, that's why some simple things drive him crazy. For example, yesterday he grew keen on counting how many different beautiful arrays there are. <PERSON> thinks that an array is beautiful if it meets one of the two conditions:

- each elements, starting from the second one, is no more than the preceding one
- each element, starting from the second one, is no less than the preceding one

Having got absolutely mad at himself and at math, <PERSON> came to <PERSON><PERSON><PERSON> and <PERSON> to ask them for help. However, they only laughed at him and said that the answer is too simple and not interesting. Help <PERSON> the <PERSON> to find the answer at last.

## 输入格式

The single line contains an integer n which is the size of the array (1 ≤ n ≤ 105).

## 输出格式

You must print the answer on a single line. As it can be rather long, you should print it modulo 1000000007.

## 样例

### 样例 1

**输入**:
```
2
```

**输出**:
```
4
```

### 样例 2

**输入**:
```
3
```

**输出**:
```
17
```

