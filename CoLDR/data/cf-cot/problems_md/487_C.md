# Prefix Product Sequence

**题目ID**: 487/C  
**比赛**: Codeforces Round 278 (Div. 1)  
**年份**: 2014  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

Consider a sequence [a1, a2, ... , an]. Define its prefix product sequence $${ \big [ } a _ { 1 } { \bmod { n } }, ( a _ { 1 } a _ { 2 } ) { \bmod { n } }, \cdots, ( a _ { 1 } a _ { 2 } \cdots a _ { n } ) { \bmod { n } } { \big ] }$$.

Now given n, find a permutation of [1, 2, ..., n], such that its prefix product sequence is a permutation of [0, 1, ..., n - 1].

## 输入格式

The only input line contains an integer n (1 ≤ n ≤ 105).

## 输出格式

In the first output line, print "YES" if such sequence exists, or print "NO" if no such sequence exists.

If any solution exists, you should output n more lines. i-th line contains only an integer ai. The elements of the sequence should be different positive integers no larger than n.

If there are multiple solutions, you are allowed to print any of them.

## 样例

### 样例 1

**输入**:
```
7
```

**输出**:
```
YES
1
4
3
6
5
2
7
```

### 样例 2

**输入**:
```
6
```

**输出**:
```
NO
```

## 备注

For the second sample, there are no valid sequences.

