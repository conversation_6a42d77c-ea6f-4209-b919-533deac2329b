# Ilya and Escalator

**题目ID**: 518/D  
**比赛**: Codeforces Round 293 (Div. 2)  
**年份**: 2015  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON><PERSON> got tired of sports programming, left university and got a job in the subway. He was given the task to determine the escalator load factor.

Let's assume that n people stand in the queue for the escalator. At each second one of the two following possibilities takes place: either the first person in the queue enters the escalator with probability p, or the first person in the queue doesn't move with probability (1 - p), paralyzed by his fear of escalators and making the whole queue wait behind him.

Formally speaking, the i-th person in the queue cannot enter the escalator until people with indices from 1 to i - 1 inclusive enter it. In one second only one person can enter the escalator. The escalator is infinite, so if a person enters it, he never leaves it, that is he will be standing on the escalator at any following second. <PERSON><PERSON> needs to count the expected value of the number of people standing on the escalator after t seconds.

Your task is to help him solve this complicated task.

## 输入格式

The first line of the input contains three numbers n, p, t (1 ≤ n, t ≤ 2000, 0 ≤ p ≤ 1). Numbers n and t are integers, number p is real, given with exactly two digits after the decimal point.

## 输出格式

Print a single real number — the expected number of people who will be standing on the escalator after t seconds. The absolute or relative error mustn't exceed 10 - 6.

## 样例

### 样例 1

**输入**:
```
1 0.50 1
```

**输出**:
```
0.5
```

### 样例 2

**输入**:
```
1 0.50 4
```

**输出**:
```
0.9375
```

### 样例 3

**输入**:
```
4 0.20 2
```

**输出**:
```
0.4
```

