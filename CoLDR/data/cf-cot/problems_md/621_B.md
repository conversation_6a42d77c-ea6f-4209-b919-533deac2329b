# Wet Shark and Bishops

**题目ID**: 621/B  
**比赛**: Codeforces Round 341 (Div. 2)  
**年份**: 2016  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

Today, <PERSON> Shark is given n bishops on a 1000 by 1000 grid. Both rows and columns of the grid are numbered from 1 to 1000. Rows are numbered from top to bottom, while columns are numbered from left to right.

<PERSON> Shark thinks that two bishops attack each other if they share the same diagonal. Note, that this is the only criteria, so two bishops may attack each other (according to <PERSON>) even if there is another bishop located between them. Now <PERSON> Shark wants to count the number of pairs of bishops that attack each other.

## 输入格式

The first line of the input contains n (1 ≤ n ≤ 200 000) — the number of bishops.

Each of next n lines contains two space separated integers xi and yi (1 ≤ xi, yi ≤ 1000) — the number of row and the number of column where i-th bishop is positioned. It's guaranteed that no two bishops share the same position.

## 输出格式

Output one integer — the number of pairs of bishops which attack each other.

## 样例

### 样例 1

**输入**:
```
5
1 1
1 5
3 3
5 1
5 5
```

**输出**:
```
6
```

### 样例 2

**输入**:
```
3
1 1
2 3
3 5
```

**输出**:
```
0
```

## 备注

In the first sample following pairs of bishops attack each other: (1, 3), (1, 5), (2, 3), (2, 4), (3, 4) and (3, 5). Pairs (1, 2), (1, 4), (2, 5) and (4, 5) do not attack each other because they do not share the same diagonal.

