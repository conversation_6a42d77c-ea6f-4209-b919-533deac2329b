# Progress Monitoring

**题目ID**: 509/F  
**比赛**: Codeforces Round 289 (Div. 2, ACM ICPC Rules)  
**年份**: 2015  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

Programming teacher <PERSON> is going to propose the following task for one of his tests for students:

You are given a tree T with n vertices, specified by its adjacency matrix a[1... n, 1... n]. What is the output of the following pseudocode?

In order to simplify the test results checking procedure, <PERSON> decided to create a tree T such that the result is his favorite sequence b. On the other hand, <PERSON> doesn't want to provide students with same trees as input, otherwise they might cheat. That's why <PERSON> is trying to find out the number of different trees T such that the result of running the above pseudocode with <PERSON> as input is exactly the sequence b. Can you help him?

Two trees with n vertices are called different if their adjacency matrices a1 and a2 are different, i. e. there exists a pair (i, j), such that 1 ≤ i, j ≤ n and a1[i][j] ≠ a2[i][j].

## 输入格式

The first line contains the positive integer n (1 ≤ n ≤ 500) — the length of sequence b.

The second line contains n positive integers b1, b2, ..., bn (1 ≤ bi ≤ n). It is guaranteed that b is a permutation, or in other words, each of the numbers 1, 2, ..., n appears exactly once in the sequence b. Also it is guaranteed that b1 = 1.

## 输出格式

Output the number of trees satisfying the conditions above modulo 109 + 7.

## 样例

### 样例 1

**输入**:
```
3
1 2 3
```

**输出**:
```
2
```

### 样例 2

**输入**:
```
3
1 3 2
```

**输出**:
```
1
```

