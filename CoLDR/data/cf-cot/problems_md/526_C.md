# Om Nom and Candies

**题目ID**: 526/C  
**比赛**: ZeptoLab Code Rush 2015  
**年份**: 2015  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

A sweet little monster Om Nom loves candies very much. One day he found himself in a rather tricky situation that required him to think a bit in order to enjoy candies the most. Would you succeed with the same task if you were on his place?

One day, when he came to his friend <PERSON>, <PERSON><PERSON> Nom didn't find him at home but he found two bags with candies. The first was full of blue candies and the second bag was full of red candies. Om Nom knows that each red candy weighs Wr grams and each blue candy weighs Wb grams. Eating a single red candy gives Om Nom Hr joy units and eating a single blue candy gives Om Nom Hb joy units.

Candies are the most important thing in the world, but on the other hand overeating is not good. Om Nom knows if he eats more than C grams of candies, he will get sick. Om Nom thinks that it isn't proper to leave candy leftovers, so he can only eat a whole candy. <PERSON><PERSON><PERSON> is a great mathematician and he quickly determined how many candies of what type he should eat in order to get the maximum number of joy units. Can you repeat his achievement? You can assume that each bag contains more candies that <PERSON><PERSON> Nom can eat.

## 输入格式

The single line contains five integers C, Hr, Hb, Wr, Wb (1 ≤ C, Hr, Hb, Wr, Wb ≤ 109).

## 输出格式

Print a single integer — the maximum number of joy units that Om Nom can get.

## 样例

### 样例 1

**输入**:
```
10 3 5 2 3
```

**输出**:
```
16
```

## 备注

In the sample test Om Nom can eat two candies of each type and thus get 16 joy units.

