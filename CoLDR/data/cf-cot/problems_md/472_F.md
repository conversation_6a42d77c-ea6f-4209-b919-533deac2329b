# Design Tutorial: Change the Goal

**题目ID**: 472/F  
**比赛**: Codeforces Round 270  
**年份**: 2014  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

There are some tasks which have the following structure: you are given a model, and you can do some operations, you should use these operations to achive the goal. One way to create a new task is to use the same model and same operations, but change the goal.

Let's have a try. I have created the following task for Topcoder SRM 557 Div1-Hard: you are given n integers x1, x2, ..., xn. You are allowed to perform the assignments (as many as you want) of the following form xi ^= xj (in the original task i and j must be different, but in this task we allow i to equal j). The goal is to maximize the sum of all xi.

Now we just change the goal. You are also given n integers y1, y2, ..., yn. You should make x1, x2, ..., xn exactly equal to y1, y2, ..., yn. In other words, for each i number xi should be equal to yi.

## 输入格式

The first line contains an integer n (1 ≤ n ≤ 10000). The second line contains n integers: x1 to xn (0 ≤ xi ≤ 109). The third line contains n integers: y1 to yn (0 ≤ yi ≤ 109).

## 输出格式

If there is no solution, output -1.

If there is a solution, then in the first line output an integer m (0 ≤ m ≤ 1000000) – the number of assignments you need to perform. Then print m lines, each line should contain two integers i and j (1 ≤ i, j ≤ n), which denote assignment xi ^= xj.

If there are multiple solutions you can print any of them. We can prove that under these constraints if there exists a solution then there always exists a solution with no more than 106 operations.

## 样例

### 样例 1

**输入**:
```
2
3 5
6 0
```

**输出**:
```
2
1 2
2 2
```

### 样例 2

**输入**:
```
5
0 0 0 0 0
1 2 3 4 5
```

**输出**:
```
-1
```

### 样例 3

**输入**:
```
3
4 5 6
1 2 3
```

**输出**:
```
5
3 1
1 2
2 2
2 3
3 1
```

### 样例 4

**输入**:
```
3
1 2 3
4 5 6
```

**输出**:
```
-1
```

## 备注

Assignment a ^= b denotes assignment a = a ^ b, where operation "^" is bitwise XOR of two integers.

