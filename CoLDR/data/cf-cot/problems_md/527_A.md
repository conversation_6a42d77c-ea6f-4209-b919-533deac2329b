# Playing with Paper

**题目ID**: 527/A  
**比赛**: Codeforces Round 296 (Div. 2)  
**年份**: 2015  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

One day <PERSON><PERSON><PERSON> was sitting on a not so interesting Maths lesson and making an origami from a rectangular a mm  ×  b mm sheet of paper (a > b). Usually the first step in making an origami is making a square piece of paper from the rectangular sheet by folding the sheet along the bisector of the right angle, and cutting the excess part.

After making a paper ship from the square piece, <PERSON><PERSON><PERSON> looked on the remaining (a - b) mm  ×  b mm strip of paper. He got the idea to use this strip of paper in the same way to make an origami, and then use the remainder (if it exists) and so on. At the moment when he is left with a square piece of paper, he will make the last ship from it and stop.

Can you determine how many ships <PERSON><PERSON><PERSON> will make during the lesson?

## 输入格式

The first line of the input contains two integers a, b (1 ≤ b < a ≤ 1012) — the sizes of the original sheet of paper.

## 输出格式

Print a single integer — the number of ships that <PERSON><PERSON><PERSON> will make.

## 样例

### 样例 1

**输入**:
```
2 1
```

**输出**:
```
2
```

### 样例 2

**输入**:
```
10 7
```

**输出**:
```
6
```

### 样例 3

**输入**:
```
1000000000000 1
```

**输出**:
```
1000000000000
```

## 备注

Pictures to the first and second sample test.

