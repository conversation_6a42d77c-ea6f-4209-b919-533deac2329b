# Land Lot

**题目ID**: 48/B  
**比赛**: School Personal Contest #3 (Winter Computer School 2010/11) - Codeforces Beta Round 45 (ACM-ICPC Rules)  
**年份**: 2010  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON><PERSON><PERSON> has a beautiful garden where wonderful fruit trees grow and yield fantastic harvest every year. But lately thieves started to sneak into the garden at nights and steal the fruit too often. <PERSON><PERSON><PERSON> can’t spend the nights in the garden and guard the fruit because there’s no house in the garden! <PERSON><PERSON><PERSON> had been saving in for some time and finally he decided to build the house. The rest is simple: he should choose in which part of the garden to build the house. In the evening he sat at his table and drew the garden’s plan. On the plan the garden is represented as a rectangular checkered field n × m in size divided into squares whose side length is 1. In some squares <PERSON><PERSON><PERSON> marked the trees growing there (one shouldn’t plant the trees too close to each other that’s why one square contains no more than one tree). <PERSON><PERSON><PERSON> wants to find a rectangular land lot a × b squares in size to build a house on, at that the land lot border should go along the lines of the grid that separates the squares. All the trees that grow on the building lot will have to be chopped off. <PERSON><PERSON><PERSON> loves his garden very much, so help him choose the building land lot location so that the number of chopped trees would be as little as possible.

## 输入格式

The first line contains two integers n and m (1 ≤ n, m ≤ 50) which represent the garden location. The next n lines contain m numbers 0 or 1, which describe the garden on the scheme. The zero means that a tree doesn’t grow on this square and the 1 means that there is a growing tree. The last line contains two integers a and b (1 ≤ a, b ≤ 50). Note that Vasya can choose for building an a × b rectangle as well a b × a one, i.e. the side of the lot with the length of a can be located as parallel to the garden side with the length of n, as well as parallel to the garden side with the length of m.

## 输出格式

Print the minimum number of trees that needs to be chopped off to select a land lot a × b in size to build a house on. It is guaranteed that at least one lot location can always be found, i. e. either a ≤ n and b ≤ m, or a ≤ m и b ≤ n.

## 样例

### 样例 1

**输入**:
```
2 2
1 0
1 1
1 1
```

**输出**:
```
0
```

### 样例 2

**输入**:
```
4 5
0 0 1 0 1
0 1 1 1 0
1 0 1 0 1
1 1 1 1 1
2 3
```

**输出**:
```
2
```

## 备注

In the second example the upper left square is (1,1) and the lower right is (3,2).

