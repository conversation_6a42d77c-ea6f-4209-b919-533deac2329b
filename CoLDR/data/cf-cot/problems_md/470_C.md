# Eval

**题目ID**: 470/C  
**比赛**: Surprise Language Round 7  
**年份**: 2014  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

You are given a simple arithmetic expression of the form a?b, where a and b are integer constants, and ? can be one of the following operations: '+' (addition), '-' (subtraction), '*' (multiplication), '/' (integer division) or '%' (modulo operation).

Output the result of evaluation of this expression.

## 输入格式

The input is a single line containing an expression a?b. Here a and b are integers between 1 and 999, inclusive; ? is an operation character: '+', '-' (ASCII code 45), '*', '/' or '%'.

## 输出格式

Output a single integer — the result of evaluation of this expression.

## 样例

### 样例 1

**输入**:
```
123+456
```

**输出**:
```
579
```

### 样例 2

**输入**:
```
192/5
```

**输出**:
```
38
```

### 样例 3

**输入**:
```
945%19
```

**输出**:
```
14
```

