# Sum

**题目ID**: 49/B  
**比赛**: Codeforces Beta Round 46 (Div. 2)  
**年份**: 2010  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON><PERSON><PERSON> studies positional numeral systems. Unfortunately, he often forgets to write the base of notation in which the expression is written. Once he saw a note in his notebook saying a + b = ?, and that the base of the positional notation wasn’t written anywhere. Now <PERSON><PERSON><PERSON> has to choose a base p and regard the expression as written in the base p positional notation. <PERSON><PERSON><PERSON> understood that he can get different results with different bases, and some bases are even invalid. For example, expression 78 + 87 in the base 16 positional notation is equal to FF16, in the base 15 positional notation it is equal to 11015, in the base 10 one — to 16510, in the base 9 one — to 1769, and in the base 8 or lesser-based positional notations the expression is invalid as all the numbers should be strictly less than the positional notation base. <PERSON><PERSON><PERSON> got interested in what is the length of the longest possible expression value. Help him to find this length.

The length of a number should be understood as the number of numeric characters in it. For example, the length of the longest answer for 78 + 87 = ? is 3. It is calculated like that in the base 15 (11015), base 10 (16510), base 9 (1769) positional notations, for example, and in some other ones.

## 输入格式

The first letter contains two space-separated numbers a and b (1 ≤ a, b ≤ 1000) which represent the given summands.

## 输出格式

Print a single number — the length of the longest answer.

## 样例

### 样例 1

**输入**:
```
78 87
```

**输出**:
```
3
```

### 样例 2

**输入**:
```
1 1
```

**输出**:
```
2
```

