# Random Function and Tree

**题目ID**: 482/D  
**比赛**: Codeforces Round 275 (Div. 1)  
**年份**: 2014  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

You have a rooted tree consisting of n vertices. Let's number them with integers from 1 to n inclusive. The root of the tree is the vertex 1. For each i > 1 direct parent of the vertex i is pi. We say that vertex i is child for its direct parent pi.

You have initially painted all the vertices with red color. You like to repaint some vertices of the tree. To perform painting you use the function paint that you call with the root of the tree as an argument. Here is the pseudocode of this function:

As a result of this function, some vertices may change their colors to white or black and some of them may remain red.

Your task is to determine the number of distinct possible colorings of the vertices of the tree. We will assume that the coloring is possible if there is a nonzero probability to get this coloring with a single call of paint(1). We assume that the colorings are different if there is a pair of vertices that are painted with different colors in these colorings. Since the required number may be very large, find its remainder of division by 1000000007 (109 + 7).

## 输入格式

The first line contains a single integer n (2 ≤ n ≤ 105) — the number of vertexes in the tree.

The second line contains n - 1 integers p2, p3, ..., pn (1 ≤ pi < i). Number pi is the parent of vertex i.

## 输出格式

Print a single integer — the answer to the problem modulo 1000000007 (109 + 7)

## 样例

### 样例 1

**输入**:
```
4
1 2 1
```

**输出**:
```
8
```

### 样例 2

**输入**:
```
3
1 1
```

**输出**:
```
5
```

## 备注

All possible coloring patterns of the first sample are given below.

