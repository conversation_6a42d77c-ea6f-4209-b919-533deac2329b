# Primes or Palindromes?

**题目ID**: 568/A  
**比赛**: Codeforces Round 315 (Div. 1)  
**年份**: 2015  
**时间限制**: 3.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON><PERSON><PERSON><PERSON> believes that the current definition of prime numbers is obsolete as they are too complex and unpredictable. A palindromic number is another matter. It is aesthetically pleasing, and it has a number of remarkable properties. Help <PERSON><PERSON><PERSON><PERSON> to convince the scientific community in this!

Let us remind you that a number is called prime if it is integer larger than one, and is not divisible by any positive integer other than itself and one.

<PERSON><PERSON><PERSON><PERSON> calls a number a palindromic if it is integer, positive, and its decimal representation without leading zeros is a palindrome, i.e. reads the same from left to right and right to left.

One problem with prime numbers is that there are too many of them. Let's introduce the following notation: π(n) — the number of primes no larger than n, rub(n) — the number of palindromic numbers no larger than n. <PERSON><PERSON><PERSON><PERSON> wants to prove that there are a lot more primes than palindromic ones.

He asked you to solve the following problem: for a given value of the coefficient A find the maximum n, such that π(n) ≤ A·rub(n).

## 输入格式

The input consists of two positive integers p, q, the numerator and denominator of the fraction that is the value of A ($$A = \frac{p}{q}$$, $$p,q \leq 10^4, \frac{1}{42} \leq \frac{p}{q} \leq 42$$).

## 输出格式

If such maximum number exists, then print it. Otherwise, print "Palindromic tree is better than splay tree" (without the quotes).

## 样例

### 样例 1

**输入**:
```
1 1
```

**输出**:
```
40
```

### 样例 2

**输入**:
```
1 42
```

**输出**:
```
1
```

### 样例 3

**输入**:
```
6 4
```

**输出**:
```
172
```

## 题解

Известно, что количество простых чисел, не превосходящих n, — число порядка $$\frac{n}{\log n}$$.
Если мы зафиксируем длину числа k, то количество палиндромных чисел такой длины будет порядка $$10^{\left\lfloor \frac{k+1}{2} \right\rfloor}$$. Это $$O(\sqrt{n})$$.
Таким образом, количество простых чисел асимптотически больше количества палиндромных чисел, а значит для любой константы A найдётся ответ. И для такого ответа $$A \sim \frac{\sqrt{n}}{\log n}$$. В нашем случае n будут не больше 107.
Поэтому мы можем для всех чисел до 107 проверить, являются ли они простыми (с помощью решета Эратосфена), а также являются ли они палиндромными (с помощью наивного алгоритма, или же можно динамикой считать зеркальное отображение числа). Затем мы посчитаем префиксные суммы, а потом линейным поиском найдём ответ.
При A ≤ 42 ответ не превышает 2·106.
Сложность — $${ \mathcal { O } } ( a n s \cdot \log \log a n s )$$.

