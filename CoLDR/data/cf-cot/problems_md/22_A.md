# Second Order Statistics

**题目ID**: 22/A  
**比赛**: Codeforces Beta Round 22 (Div. 2 Only)  
**年份**: 2010  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

Once <PERSON> needed to find the second order statistics of a sequence of integer numbers. Lets choose each number from the sequence exactly once and sort them. The value on the second position is the second order statistics of the given sequence. In other words it is the smallest element strictly greater than the minimum. Help <PERSON> solve this problem.

## 输入格式

The first input line contains integer n (1 ≤ n ≤ 100) — amount of numbers in the sequence. The second line contains n space-separated integer numbers — elements of the sequence. These numbers don't exceed 100 in absolute value.

## 输出格式

If the given sequence has the second order statistics, output this order statistics, otherwise output NO.

## 样例

### 样例 1

**输入**:
```
4
1 2 2 -4
```

**输出**:
```
1
```

### 样例 2

**输入**:
```
5
1 2 3 1 1
```

**输出**:
```
2
```

