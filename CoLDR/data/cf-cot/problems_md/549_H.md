# Degenerate Matrix

**题目ID**: 549/H  
**比赛**: Looksery Cup 2015  
**年份**: 2015  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

The determinant of a matrix 2 × 2 is defined as follows:

$$\operatorname*{det}\left(\begin{array}{cc}
a & b \\
c & d
\end{array}\right) = ad - bc$$

A matrix is called degenerate if its determinant is equal to zero.

The norm ||A|| of a matrix A is defined as a maximum of absolute values of its elements.

You are given a matrix $${\boldsymbol{A}}={\begin{pmatrix}a&b\\c&d\end{pmatrix}}$$. Consider any degenerate matrix B such that norm ||A - B|| is minimum possible. Determine ||A - B||.

## 输入格式

The first line contains two integers a and b (|a|, |b| ≤ 109), the elements of the first row of matrix A.

The second line contains two integers c and d (|c|, |d| ≤ 109) the elements of the second row of matrix A.

## 输出格式

Output a single real number, the minimum possible value of ||A - B||. Your answer is considered to be correct if its absolute or relative error does not exceed 10 - 9.

## 样例

### 样例 1

**输入**:
```
1 2
3 4
```

**输出**:
```
0.2000000000
```

### 样例 2

**输入**:
```
1 0
0 1
```

**输出**:
```
0.5000000000
```

## 备注

In the first sample matrix B is $${ \left( \begin{array} { l l } { 1. 2 } & { 1. 8 } \\ { 2. 8 } & { 4. 2 } \end{array} \right) }$$

In the second sample matrix B is $${ \left( \begin{array} { l l } { 0. 5 } & { 0. 5 } \\ { 0. 5 } & { 0. 5 } \end{array} \right) }$$

## 题解

The rows of degenerate matrix are linear dependent so the matrix B can be written in the following way: $${ \left( \begin{array} { l } { x } \\ { k \cdot x } \end{array} \right) }$$. Suppose $$A = \begin{pmatrix} x_0 & y_0 \\ x_1 & y_1 \end{pmatrix}$$. Let's assume that elements of the first row of matrix A are coordinates of point a0 on two-dimensional plane and the elements of the second row are coordinates of point a1. Assume that the rows of matrix B are also coordinates of points b0 and b1. Let's note that in this case the line that is passing through points b0 and b1 is also passing through point (0, 0).
Let's find the answer — the number d — by using binary search. Suppose we are considering some number d0. We need to check if there is such matrix B that $${ \left| \begin{array} { l l } { x_{ 0 } - x } & { y _ { 0 } - y } \\ { x _ { 1 } - k \cdot x } & { y _ { 1 } - k \cdot y } \end{array} \right| } \leq d _ { 0 }$$. In geometric interpretation it means that point b0 are inside the square which center is point a0 and length of side is 2·d0. In the same way the point b1 are inside the square which center is point a1 and length of side is 2·d0. So we need to check if there is a line that is passing through point (0, 0) and is crossing these squares. In order to do this we should consider every vertex of the first square, build the line that is passing through the chosen vertex and the center of coordinate plane and check if it cross any side of the other square. Afterwards we should swap the squares and check again. Finally if there is a desired line we need to reduce the search area and to expand otherwise.

