# Bargaining Table

**题目ID**: 22/B  
**比赛**: Codeforces Beta Round 22 (Div. 2 Only)  
**年份**: 2010  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON> wants to put a new bargaining table in his office. To do so he measured the office room thoroughly and drew its plan: <PERSON>'s office room is a rectangular room n × m meters. Each square meter of the room is either occupied by some furniture, or free. A bargaining table is rectangular, and should be placed so, that its sides are parallel to the office walls. <PERSON> doesn't want to change or rearrange anything, that's why all the squares that will be occupied by the table should be initially free. <PERSON> wants the new table to sit as many people as possible, thus its perimeter should be maximal. Help <PERSON> find out the maximum possible perimeter of a bargaining table for his office.

## 输入格式

The first line contains 2 space-separated numbers n and m (1 ≤ n, m ≤ 25) — the office room dimensions. Then there follow n lines with m characters 0 or 1 each. 0 stands for a free square meter of the office room. 1 stands for an occupied square meter. It's guaranteed that at least one square meter in the room is free.

## 输出格式

Output one number — the maximum possible perimeter of a bargaining table for <PERSON>'s office room.

## 样例

### 样例 1

**输入**:
```
3 3
000
010
000
```

**输出**:
```
8
```

### 样例 2

**输入**:
```
5 4
1100
0000
0000
0000
0000
```

**输出**:
```
16
```

