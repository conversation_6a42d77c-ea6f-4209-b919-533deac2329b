# New Year Book Reading

**题目ID**: 500/C  
**比赛**: Good Bye 2014  
**年份**: 2014  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

New Year is coming, and <PERSON><PERSON><PERSON><PERSON> decided to read many books during 2015, unlike this year. He has n books numbered by integers from 1 to n. The weight of the i-th (1 ≤ i ≤ n) book is wi.

As <PERSON><PERSON><PERSON><PERSON>'s house is not large enough to have a bookshelf, he keeps the n books by stacking them vertically. When he wants to read a certain book x, he follows the steps described below.

1. He lifts all the books above book x.
2. He pushes book x out of the stack.
3. He puts down the lifted books without changing their order.
4. After reading book x, he puts book x on the top of the stack.

He decided to read books for m days. In the j-th (1 ≤ j ≤ m) day, he will read the book that is numbered with integer bj (1 ≤ bj ≤ n). To read the book, he has to use the process described in the paragraph above. It is possible that he decides to re-read the same book several times.

After making this plan, he realized that the total weight of books he should lift during m days would be too heavy. So, he decided to change the order of the stacked books before the New Year comes, and minimize the total weight. You may assume that books can be stacked in any possible order. Note that book that he is going to read on certain step isn't considered as lifted on that step. Can you help him?

## 输入格式

The first line contains two space-separated integers n (2 ≤ n ≤ 500) and m (1 ≤ m ≤ 1000) — the number of books, and the number of days for which Jaehyun would read books.

The second line contains n space-separated integers w1, w2, ..., wn (1 ≤ wi ≤ 100) — the weight of each book.

The third line contains m space separated integers b1, b2, ..., bm (1 ≤ bj ≤ n) — the order of books that he would read. Note that he can read the same book more than once.

## 输出格式

Print the minimum total weight of books he should lift, which can be achieved by rearranging the order of stacked books.

## 样例

### 样例 1

**输入**:
```
3 5
1 2 3
1 3 2 3 1
```

**输出**:
```
12
```

## 备注

Here's a picture depicting the example. Each vertical column presents the stacked books.

