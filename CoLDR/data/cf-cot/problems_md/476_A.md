# <PERSON><PERSON> and Stairs

**题目ID**: 476/A  
**比赛**: Codeforces Round 272 (Div. 2)  
**年份**: 2014  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON><PERSON> wants to climb up a stair of n steps. He can climb 1 or 2 steps at each move. <PERSON><PERSON> wants the number of moves to be a multiple of an integer m.

What is the minimal number of moves making him climb to the top of the stairs that satisfies his condition?

## 输入格式

The single line contains two space separated integers n, m (0 < n ≤ 10000, 1 < m ≤ 10).

## 输出格式

Print a single integer — the minimal number of moves being a multiple of m. If there is no way he can climb satisfying condition print  - 1 instead.

## 样例

### 样例 1

**输入**:
```
10 2
```

**输出**:
```
6
```

### 样例 2

**输入**:
```
3 5
```

**输出**:
```
-1
```

## 备注

For the first sample, <PERSON><PERSON> could climb in 6 moves with following sequence of steps: {2, 2, 2, 2, 1, 1}.

For the second sample, there are only three valid sequence of steps {2, 1}, {1, 2}, {1, 1, 1} with 2, 2, and 3 steps respectively. All these numbers are not multiples of 5.

