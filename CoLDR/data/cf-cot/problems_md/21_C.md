# Stripe 2

**题目ID**: 21/C  
**比赛**: Codeforces Alpha Round 21 (Codeforces format)  
**年份**: 2010  
**时间限制**: 1.0秒  
**内存限制**: 64MB  

## 题目描述

Once <PERSON> took a paper stripe of n squares (the height of the stripe is 1 square). In each square he wrote an integer number, possibly negative. He became interested in how many ways exist to cut this stripe into three pieces so that the sum of numbers from each piece is equal to the sum of numbers from any other piece, and each piece contains positive integer amount of squares. Would you help <PERSON> solve this problem?

## 输入格式

The first input line contains integer n (1 ≤ n ≤ 105) — amount of squares in the stripe. The second line contains n space-separated numbers — they are the numbers written in the squares of the stripe. These numbers are integer and do not exceed 10000 in absolute value.

## 输出格式

Output the amount of ways to cut the stripe into three non-empty pieces so that the sum of numbers from each piece is equal to the sum of numbers from any other piece. Don't forget that it's allowed to cut the stripe along the squares' borders only.

## 样例

### 样例 1

**输入**:
```
4
1 2 3 3
```

**输出**:
```
1
```

### 样例 2

**输入**:
```
5
1 2 3 4 5
```

**输出**:
```
0
```

