# Bear and Drawing

**题目ID**: 573/C  
**比赛**: Codeforces Round 318 [RussianCodeCup Thanks-Round] (Div. 1)  
**年份**: 2015  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON><PERSON> is a little bear who learns to draw. People usually start with houses, fences and flowers but why would bears do it? <PERSON><PERSON> lives in the forest and he decides to draw a tree.

Recall that tree is a connected graph consisting of n vertices and n - 1 edges.

<PERSON><PERSON> chose a tree with n vertices. He has infinite strip of paper with two parallel rows of dots. Little bear wants to assign vertices of a tree to some n distinct dots on a paper so that edges would intersect only at their endpoints — drawn tree must be planar. Below you can see one of correct drawings for the first sample test.

Is it possible for <PERSON><PERSON> to draw chosen tree?

## 输入格式

The first line contains single integer n (1 ≤ n ≤ 105).

Next n - 1 lines contain description of a tree. i-th of them contains two space-separated integers ai and bi (1 ≤ ai, bi ≤ n, ai ≠ bi) denoting an edge between vertices ai and bi. It's guaranteed that given description forms a tree.

## 输出格式

Print "Yes" (without the quotes) if <PERSON><PERSON> can draw chosen tree. Otherwise, print "No" (without the quotes).

## 样例

### 样例 1

**输入**:
```
8
1 2
1 3
1 6
6 4
6 7
6 5
7 8
```

**输出**:
```
Yes
```

### 样例 2

**输入**:
```
13
1 2
1 3
1 4
2 5
2 6
2 7
3 8
3 9
3 10
4 11
4 12
4 13
```

**输出**:
```
No
```

## 题解

Let's consider a tree already drawn on a strip of paper. Let's take first vertex on the left and last vertex on the right (in case of two vertices with the same x, we choose any of them). There is a path between them. Let's forget about vertices not on this path. A path divides a strip into 1D regions.
What can be added to a path? Only simple paths with one edge to a main path. So it can be one of the following structures (Line or Y-letter):
Note that Y-letter can have long legs but its main part can have only one edge.
How check it? TODO

