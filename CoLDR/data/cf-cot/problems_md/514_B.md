# Han <PERSON> and Lazer Gun

**题目ID**: 514/B  
**比赛**: Codeforces Round 291 (Div. 2)  
**年份**: 2015  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

There are n Imperial stormtroopers on the field. The battle field is a plane with Cartesian coordinate system. Each stormtrooper is associated with his coordinates (x, y) on this plane.

<PERSON> has the newest duplex lazer gun to fight these stormtroopers. It is situated at the point (x0, y0). In one shot it can can destroy all the stormtroopers, situated on some line that crosses point (x0, y0).

Your task is to determine what minimum number of shots Han Solo needs to defeat all the stormtroopers.

The gun is the newest invention, it shoots very quickly and even after a very large number of shots the stormtroopers don't have enough time to realize what's happening and change their location.

## 输入格式

The first line contains three integers n, x0 и y0 (1 ≤ n ≤ 1000,  - 104 ≤ x0, y0 ≤ 104) — the number of stormtroopers on the battle field and the coordinates of your gun.

Next n lines contain two integers each xi, yi ( - 104 ≤ xi, yi ≤ 104) — the coordinates of the stormtroopers on the battlefield. It is guaranteed that no stormtrooper stands at the same point with the gun. Multiple stormtroopers can stand at the same point.

## 输出格式

Print a single integer — the minimum number of shots Han <PERSON> needs to destroy all the stormtroopers.

## 样例

### 样例 1

**输入**:
```
4 0 0
1 1
2 2
2 0
-1 -1
```

**输出**:
```
2
```

### 样例 2

**输入**:
```
2 1 2
1 1
1 0
```

**输出**:
```
1
```

## 备注

Explanation to the first and second samples from the statement, respectively:

