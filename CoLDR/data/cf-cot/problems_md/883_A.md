# Automatic Door

**题目ID**: 883/A  
**比赛**: 2017-2018 ACM-ICPC, NEERC, Southern Subregional Contest (Online Mirror, ACM-ICPC Rules, Teams Preferred)  
**年份**: 2017  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

There is an automatic door at the entrance of a factory. The door works in the following way:

- when one or several people come to the door and it is closed, the door immediately opens automatically and all people immediately come inside,
- when one or several people come to the door and it is open, all people immediately come inside,
- opened door immediately closes in d seconds after its opening,
- if the door is closing and one or several people are coming to the door at the same moment, then all of them will have enough time to enter and only after that the door will close.

For example, if d = 3 and four people are coming at four different moments of time t1 = 4, t2 = 7, t3 = 9 and t4 = 13 then the door will open three times: at moments 4, 9 and 13. It will close at moments 7 and 12.

It is known that n employees will enter at moments a, 2·a, 3·a, ..., n·a (the value a is positive integer). Also m clients will enter at moments t1, t2, ..., tm.

Write program to find the number of times the automatic door will open. Assume that the door is initially closed.

## 输入格式

The first line contains four integers n, m, a and d (1 ≤ n, a ≤ 109, 1 ≤ m ≤ 105, 1 ≤ d ≤ 1018) — the number of the employees, the number of the clients, the moment of time when the first employee will come and the period of time in which the door closes.

The second line contains integer sequence t1, t2, ..., tm (1 ≤ ti ≤ 1018) — moments of time when clients will come. The values ti are given in non-decreasing order.

## 输出格式

Print the number of times the door will open.

## 样例

### 样例 1

**输入**:
```
1 1 3 4
7
```

**输出**:
```
1
```

### 样例 2

**输入**:
```
4 3 4 2
7 9 11
```

**输出**:
```
4
```

## 备注

In the first example the only employee will come at moment 3. At this moment the door will open and will stay open until the moment 7. At the same moment of time the client will come, so at first he will enter and only after it the door will close. Thus the door will open one time.

