# Is your horseshoe on the other hoof?

**题目ID**: 228/A  
**比赛**: Codeforces Round 141 (Div. 2)  
**年份**: 2012  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON><PERSON> the Horse is going to the party with friends. He has been following the fashion trends for a while, and he knows that it is very popular to wear all horseshoes of different color. <PERSON><PERSON> has got four horseshoes left from the last year, but maybe some of them have the same color. In this case he needs to go to the store and buy some few more horseshoes, not to lose face in front of his stylish comrades.

Fortunately, the store sells horseshoes of all colors under the sun and <PERSON><PERSON> has enough money to buy any four of them. However, in order to save the money, he would like to spend as little money as possible, so you need to help <PERSON><PERSON> and determine what is the minimum number of horseshoes he needs to buy to wear four horseshoes of different colors to a party.

## 输入格式

The first line contains four space-separated integers s1, s2, s3, s4 (1 ≤ s1, s2, s3, s4 ≤ 109) — the colors of horseshoes Valera has.

Consider all possible colors indexed with integers.

## 输出格式

Print a single integer — the minimum number of horseshoes <PERSON>ra needs to buy.

## 样例

### 样例 1

**输入**:
```
1 7 3 3
```

**输出**:
```
1
```

### 样例 2

**输入**:
```
7 7 7 7
```

**输出**:
```
3
```

