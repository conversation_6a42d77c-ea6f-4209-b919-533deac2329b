# Design Tutorial: Make It Nondeterministic

**题目ID**: 472/C  
**比赛**: Codeforces Round 270  
**年份**: 2014  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

A way to make a new task is to make it nondeterministic or probabilistic. For example, the hard task of Topcoder SRM 595, Constellation, is the probabilistic version of a convex hull.

Let's try to make a new task. Firstly we will use the following task. There are n people, sort them by their name. It is just an ordinary sorting problem, but we can make it more interesting by adding nondeterministic element. There are n people, each person will use either his/her first name or last name as a handle. Can the lexicographical order of the handles be exactly equal to the given permutation p?

More formally, if we denote the handle of the i-th person as hi, then the following condition must hold: $$$$.

## 输入格式

The first line contains an integer n (1 ≤ n ≤ 105) — the number of people.

The next n lines each contains two strings. The i-th line contains strings fi and si (1 ≤ |fi|, |si| ≤ 50) — the first name and last name of the i-th person. Each string consists only of lowercase English letters. All of the given 2n strings will be distinct.

The next line contains n distinct integers: p1, p2, ..., pn (1 ≤ pi ≤ n).

## 输出格式

If it is possible, output "YES", otherwise output "NO".

## 样例

### 样例 1

**输入**:
```
3
gennady korotkevich
petr mitrichev
gaoyuan chen
1 2 3
```

**输出**:
```
NO
```

### 样例 2

**输入**:
```
3
gennady korotkevich
petr mitrichev
gaoyuan chen
3 1 2
```

**输出**:
```
YES
```

### 样例 3

**输入**:
```
2
galileo galilei
nicolaus copernicus
2 1
```

**输出**:
```
YES
```

### 样例 4

**输入**:
```
10
rean schwarzer
fei claussell
alisa reinford
eliot craig
laura arseid
jusis albarea
machias regnitz
sara valestin
emma millstein
gaius worzel
1 2 3 4 5 6 7 8 9 10
```

**输出**:
```
NO
```

### 样例 5

**输入**:
```
10
rean schwarzer
fei claussell
alisa reinford
eliot craig
laura arseid
jusis albarea
machias regnitz
sara valestin
emma millstein
gaius worzel
2 4 9 6 5 7 1 3 8 10
```

**输出**:
```
YES
```

## 备注

In example 1 and 2, we have 3 people: tourist, Petr and me (cgy4ever). You can see that whatever handle is chosen, I must be the first, then tourist and Petr must be the last.

In example 3, if Copernicus uses "copernicus" as his handle, everything will be alright.

