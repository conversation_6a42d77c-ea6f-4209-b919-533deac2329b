# Ilya and Sticks

**题目ID**: 525/C  
**比赛**: Codeforces Round 297 (Div. 2)  
**年份**: 2015  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

In the evening, after the contest <PERSON><PERSON> was bored, and he really felt like maximizing. He remembered that he had a set of n sticks and an instrument. Each stick is characterized by its length li.

<PERSON><PERSON> decided to make a rectangle from the sticks. And due to his whim, he decided to make rectangles in such a way that maximizes their total area. Each stick is used in making at most one rectangle, it is possible that some of sticks remain unused. Bending sticks is not allowed.

Sticks with lengths a1, a2, a3 and a4 can make a rectangle if the following properties are observed:

- a1 ≤ a2 ≤ a3 ≤ a4
- a1 = a2
- a3 = a4

A rectangle can be made of sticks with lengths of, for example, 3 3 3 3 or 2 2 4 4. A rectangle cannot be made of, for example, sticks 5 5 5 7.

<PERSON><PERSON> also has an instrument which can reduce the length of the sticks. The sticks are made of a special material, so the length of each stick can be reduced by at most one. For example, a stick with length 5 can either stay at this length or be transformed into a stick of length 4.

You have to answer the question — what maximum total area of the rectangles can <PERSON><PERSON> get with a file if makes rectangles from the available sticks?

## 输入格式

The first line of the input contains a positive integer n (1 ≤ n ≤ 105) — the number of the available sticks.

The second line of the input contains n positive integers li (2 ≤ li ≤ 106) — the lengths of the sticks.

## 输出格式

The first line of the output must contain a single non-negative integer — the maximum total area of the rectangles that Ilya can make from the available sticks.

## 样例

### 样例 1

**输入**:
```
4
2 4 4 2
```

**输出**:
```
8
```

### 样例 2

**输入**:
```
4
2 2 3 5
```

**输出**:
```
0
```

### 样例 3

**输入**:
```
4
100003 100004 100005 100006
```

**输出**:
```
10000800015
```

