# <PERSON>oon and Binary

**题目ID**: 477/D  
**比赛**: Codeforces Round 272 (Div. 1)  
**年份**: 2014  
**时间限制**: 2.0秒  
**内存限制**: 512MB  

## 题目描述

<PERSON><PERSON> saw a large integer x written on the ground and wants to print its binary form out. <PERSON><PERSON> has accomplished the part of turning x into its binary format. Now he is going to print it in the following manner.

He has an integer n = 0 and can only perform the following two operations in any order for unlimited times each:

1. Print n in binary form without leading zeros, each print will append to the right of previous prints.
2. Increase n by 1.

Let's define an ideal sequence as a sequence of operations that can successfully print binary representation of x without leading zeros and ends with a print operation (i.e. operation 1). <PERSON><PERSON> wants to know how many different ideal sequences are there and the length (in operations) of the shortest ideal sequence.

The answers might be large so please print them modulo 1000000007 (109 + 7).

Let's define the string representation of an ideal sequence as a string of '1' and '2' where the i-th character in the string matches the i-th operation performed. Two ideal sequences are called different if their string representations are different.

## 输入格式

The single line of the input contains a binary integer representing x (1 ≤ x < 25000) without leading zeros.

## 输出格式

The first line of the output should contain an integer representing the number of different ideal sequences modulo 1000000007 (109 + 7).

The second line of the output contains an integer representing the minimal length of an ideal sequence modulo 1000000007 (109 + 7).

## 样例

### 样例 1

**输入**:
```
101
```

**输出**:
```
1
6
```

### 样例 2

**输入**:
```
11010
```

**输出**:
```
3
5
```

## 备注

For the first sample, the shortest and the only ideal sequence is «222221» of length 6.

For the second sample, there are three ideal sequences «21211», «212222222221», «222222222222222222222222221». Among them the shortest one has length 5.

