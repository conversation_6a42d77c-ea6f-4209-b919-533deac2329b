# Watermelon

**题目ID**: 4/A  
**比赛**: Codeforces Beta Round 4 (Div. 2 Only)  
**年份**: 2010  
**时间限制**: 1.0秒  
**内存限制**: 64MB  

## 题目描述

One hot summer day <PERSON> and his friend <PERSON> decided to buy a watermelon. They chose the biggest and the ripest one, in their opinion. After that the watermelon was weighed, and the scales showed w kilos. They rushed home, dying of thirst, and decided to divide the berry, however they faced a hard problem.

<PERSON> and <PERSON> are great fans of even numbers, that's why they want to divide the watermelon in such a way that each of the two parts weighs even number of kilos, at the same time it is not obligatory that the parts are equal. The boys are extremely tired and want to start their meal as soon as possible, that's why you should help them and find out, if they can divide the watermelon in the way they want. For sure, each of them should get a part of positive weight.

## 输入格式

The first (and the only) input line contains integer number w (1 ≤ w ≤ 100) — the weight of the watermelon bought by the boys.

## 输出格式

Print YES, if the boys can divide the watermelon into two parts, each of them weighing even number of kilos; and NO in the opposite case.

## 样例

### 样例 1

**输入**:
```
8
```

**输出**:
```
YES
```

## 备注

For example, the boys can divide the watermelon into two parts of 2 and 6 kilos respectively (another variant — two parts of 4 and 4 kilos).

