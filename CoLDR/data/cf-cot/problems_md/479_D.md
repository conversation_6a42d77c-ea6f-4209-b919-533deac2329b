# Long Jumps

**题目ID**: 479/D  
**比赛**: Codeforces Round 274 (Div. 2)  
**年份**: 2014  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON><PERSON> is a PE teacher at a school in Berland. Soon the students are going to take a test in long jumps, and <PERSON><PERSON> has lost his favorite ruler!

However, there is no reason for disappointment, as <PERSON><PERSON> has found another ruler, its length is l centimeters. The ruler already has n marks, with which he can make measurements. We assume that the marks are numbered from 1 to n in the order they appear from the beginning of the ruler to its end. The first point coincides with the beginning of the ruler and represents the origin. The last mark coincides with the end of the ruler, at distance l from the origin. This ruler can be repesented by an increasing sequence a1, a2, ..., an, where ai denotes the distance of the i-th mark from the origin (a1 = 0, an = l).

<PERSON><PERSON> believes that with a ruler he can measure the distance of d centimeters, if there is a pair of integers i and j (1 ≤ i ≤ j ≤ n), such that the distance between the i-th and the j-th mark is exactly equal to d (in other words, aj - ai = d).

Under the rules, the girls should be able to jump at least x centimeters, and the boys should be able to jump at least y (x < y) centimeters. To test the children's abilities, <PERSON><PERSON> needs a ruler to measure each of the distances x and y.

Your task is to determine what is the minimum number of additional marks you need to add on the ruler so that they can be used to measure the distances x and y. Valery can add the marks at any integer non-negative distance from the origin not exceeding the length of the ruler.

## 输入格式

The first line contains four positive space-separated integers n, l, x, y (2 ≤ n ≤ 105, 2 ≤ l ≤ 109, 1 ≤ x < y ≤ l) — the number of marks, the length of the ruler and the jump norms for girls and boys, correspondingly.

The second line contains a sequence of n integers a1, a2, ..., an (0 = a1 < a2 < ... < an = l), where ai shows the distance from the i-th mark to the origin.

## 输出格式

In the first line print a single non-negative integer v — the minimum number of marks that you need to add on the ruler.

In the second line print v space-separated integers p1, p2, ..., pv (0 ≤ pi ≤ l). Number pi means that the i-th mark should be at the distance of pi centimeters from the origin. Print the marks in any order. If there are multiple solutions, print any of them.

## 样例

### 样例 1

**输入**:
```
3 250 185 230
0 185 250
```

**输出**:
```
1
230
```

### 样例 2

**输入**:
```
4 250 185 230
0 20 185 250
```

**输出**:
```
0
```

### 样例 3

**输入**:
```
2 300 185 230
0 300
```

**输出**:
```
2
185 230
```

## 备注

In the first sample it is impossible to initially measure the distance of 230 centimeters. For that it is enough to add a 20 centimeter mark or a 230 centimeter mark.

In the second sample you already can use the ruler to measure the distances of 185 and 230 centimeters, so you don't have to add new marks.

In the third sample the ruler only contains the initial and the final marks. We will need to add two marks to be able to test the children's skills.

