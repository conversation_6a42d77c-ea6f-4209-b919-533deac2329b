# Triangular numbers

**题目ID**: 47/A  
**比赛**: Codeforces Beta Round 44 (Div. 2)  
**年份**: 2010  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

A triangular number is the number of dots in an equilateral triangle uniformly filled with dots. For example, three dots can be arranged in a triangle; thus three is a triangular number. The n-th triangular number is the number of dots in a triangle with n dots on a side. $$T_{n}=\frac{n(n+1)}{2}$$. You can learn more about these numbers from Wikipedia (http://en.wikipedia.org/wiki/Triangular_number).

Your task is to find out if a given integer is a triangular number.

## 输入格式

The first line contains the single number n (1 ≤ n ≤ 500) — the given integer.

## 输出格式

If the given integer is a triangular number output YES, otherwise output NO.

## 样例

### 样例 1

**输入**:
```
1
```

**输出**:
```
YES
```

### 样例 2

**输入**:
```
2
```

**输出**:
```
NO
```

### 样例 3

**输入**:
```
3
```

**输出**:
```
YES
```

