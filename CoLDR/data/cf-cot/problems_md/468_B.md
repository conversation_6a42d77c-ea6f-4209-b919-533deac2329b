# Two Sets

**题目ID**: 468/B  
**比赛**: Codeforces Round 268 (Div. 1)  
**年份**: 2014  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

Little X has n distinct integers: p1, p2, ..., pn. He wants to divide all of them into two sets A and B. The following two conditions must be satisfied:

- If number x belongs to set A, then number a - x must also belong to set A.
- If number x belongs to set B, then number b - x must also belong to set B.

Help Little X divide the numbers into two sets or determine that it's impossible.

## 输入格式

The first line contains three space-separated integers n, a, b (1 ≤ n ≤ 105; 1 ≤ a, b ≤ 109). The next line contains n space-separated distinct integers p1, p2, ..., pn (1 ≤ pi ≤ 109).

## 输出格式

If there is a way to divide the numbers into two sets, then print "YES" in the first line. Then print n integers: b1, b2, ..., bn (bi equals either 0, or 1), describing the division. If bi equals to 0, then pi belongs to set A, otherwise it belongs to set B.

If it's impossible, print "NO" (without the quotes).

## 样例

### 样例 1

**输入**:
```
4 5 9
2 3 4 5
```

**输出**:
```
YES
0 0 1 1
```

### 样例 2

**输入**:
```
3 3 4
1 2 4
```

**输出**:
```
NO
```

## 备注

It's OK if all the numbers are in the same set, and the other one is empty.

