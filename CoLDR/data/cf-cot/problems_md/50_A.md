# Domino piling

**题目ID**: 50/A  
**比赛**: Codeforces Beta Round 47  
**年份**: 2010  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

You are given a rectangular board of M × N squares. Also you are given an unlimited number of standard domino pieces of 2 × 1 squares. You are allowed to rotate the pieces. You are asked to place as many dominoes as possible on the board so as to meet the following conditions:

1. Each domino completely covers two squares.

2. No two dominoes overlap.

3. Each domino lies entirely inside the board. It is allowed to touch the edges of the board.

Find the maximum number of dominoes, which can be placed under these restrictions.

## 输入格式

In a single line you are given two integers M and N — board sizes in squares (1 ≤ M ≤ N ≤ 16).

## 输出格式

Output one number — the maximal number of dominoes, which can be placed.

## 样例

### 样例 1

**输入**:
```
2 4
```

**输出**:
```
4
```

### 样例 2

**输入**:
```
3 3
```

**输出**:
```
4
```

