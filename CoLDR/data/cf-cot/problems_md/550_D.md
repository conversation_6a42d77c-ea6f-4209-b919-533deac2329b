# Regular Bridge

**题目ID**: 550/D  
**比赛**: Codeforces Round 306 (Div. 2)  
**年份**: 2015  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

An undirected graph is called k-regular, if the degrees of all its vertices are equal k. An edge of a connected graph is called a bridge, if after removing it the graph is being split into two connected components.

Build a connected undirected k-regular graph containing at least one bridge, or else state that such graph doesn't exist.

## 输入格式

The single line of the input contains integer k (1 ≤ k ≤ 100) — the required degree of the vertices of the regular graph.

## 输出格式

Print "NO" (without quotes), if such graph doesn't exist.

Otherwise, print "YES" in the first line and the description of any suitable graph in the next lines.

The description of the made graph must start with numbers n and m — the number of vertices and edges respectively.

Each of the next m lines must contain two integers, a and b (1 ≤ a, b ≤ n, a ≠ b), that mean that there is an edge connecting the vertices a and b. A graph shouldn't contain multiple edges and edges that lead from a vertex to itself. A graph must be connected, the degrees of all vertices of the graph must be equal k. At least one edge of the graph must be a bridge. You can print the edges of the graph in any order. You can print the ends of each edge in any order.

The constructed graph must contain at most 106 vertices and 106 edges (it is guaranteed that if at least one graph that meets the requirements exists, then there also exists the graph with at most 106 vertices and at most 106 edges).

## 样例

### 样例 1

**输入**:
```
1
```

**输出**:
```
YES
2 1
1 2
```

## 备注

In the sample from the statement there is a suitable graph consisting of two vertices, connected by a single edge.

## 题解

Let's prove that there is no solution for even k.
Suppose our graph contains some bridges, k = 2s (even), all degrees are k. Then there always exists strongly connected component that is connected to other part of the graph with exactly one bridge.
Consider this component. Let's remove bridge that connects it to the remaining graph. Then it has one vertex with degree k - 1 = 2s - 1 and some vertices with degrees k = 2s. But then the graph consisting of this component will contain only one vertex with odd degree, which is impossible by Handshaking Lemma.
Let's construct the answer for odd k. Let k = 2s - 1.
For k = 1 graph consisting of two nodes connected by edge works.
For k ≥ 3 let's construct graph with 2k + 4 nodes. Let it consist of two strongly connected components connected by bridge. Enumerate nodes of first component from 1 to k + 2, second component will be the same as the first one.
Let vertex 1 be connected to the second component by bridge. Also connect it with k - 1 edges to vertices 2, 3, ..., k. Connect vertices 2, 3, ..., k to each other (add all possible edges between them), and then remove edges between every neighbouring pair, for example edges 2 - 3, 4 - 5, ..., (k - 1) - k.
Then we connect vertices 2, 3, ..., k with vertices k + 1 and k + 2. And finally add an edge between nodes k + 1 and k + 2.
Build the second component in the similar manner, and add a bridge between components. Constructed graph has one bridge, all degrees of k and consists of O(k) nodes and O(k2) edges.
Complexity of the solution — O(k2).

