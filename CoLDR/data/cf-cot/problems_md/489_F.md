# Special Matrices

**题目ID**: 489/F  
**比赛**: Codeforces Round 277.5 (Div. 2)  
**年份**: 2014  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

An n × n square matrix is special, if:

- it is binary, that is, each cell contains either a 0, or a 1;
- the number of ones in each row and column equals 2.

You are given n and the first m rows of the matrix. Print the number of special n × n matrices, such that the first m rows coincide with the given ones.

As the required value can be rather large, print the remainder after dividing the value by the given number mod.

## 输入格式

The first line of the input contains three integers n, m, mod (2 ≤ n ≤ 500, 0 ≤ m ≤ n, 2 ≤ mod ≤ 109). Then m lines follow, each of them contains n characters — the first rows of the required special matrices. Each of these lines contains exactly two characters '1', the rest characters are '0'. Each column of the given m × n table contains at most two numbers one.

## 输出格式

Print the remainder after dividing the required value by number mod.

## 样例

### 样例 1

**输入**:
```
3 1 1000
011
```

**输出**:
```
2
```

### 样例 2

**输入**:
```
4 4 100500
0110
1010
0101
1001
```

**输出**:
```
1
```

## 备注

For the first test the required matrices are:

In the second test the required matrix is already fully given, so the answer is 1.

