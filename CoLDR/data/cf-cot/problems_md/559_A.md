# <PERSON>'s Hexagon

**题目ID**: 559/A  
**比赛**: Codeforces Round 313 (Div. 1)  
**年份**: 2015  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON> got a very curious hexagon for his birthday. The boy found out that all the angles of the hexagon are equal to $$120^\circ$$. Then he measured the length of its sides, and found that each of them is equal to an integer number of centimeters. There the properties of the hexagon ended and <PERSON> decided to draw on it.

He painted a few lines, parallel to the sides of the hexagon. The lines split the hexagon into regular triangles with sides of 1 centimeter. Now <PERSON> wonders how many triangles he has got. But there were so many of them that <PERSON> lost the track of his counting. Help the boy count the triangles.

## 输入格式

The first and the single line of the input contains 6 space-separated integers a1, a2, a3, a4, a5 and a6 (1 ≤ ai ≤ 1000) — the lengths of the sides of the hexagons in centimeters in the clockwise order. It is guaranteed that the hexagon with the indicated properties and the exactly such sides exists.

## 输出格式

Print a single integer — the number of triangles with the sides of one 1 centimeter, into which the hexagon is split.

## 样例

### 样例 1

**输入**:
```
1 1 1 1 1 1
```

**输出**:
```
6
```

### 样例 2

**输入**:
```
1 2 1 2 1 2
```

**输出**:
```
13
```

## 备注

This is what Gerald's hexagon looks like in the first sample:

And that's what it looks like in the second sample:

