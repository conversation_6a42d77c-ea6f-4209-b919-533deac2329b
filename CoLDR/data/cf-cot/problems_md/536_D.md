# Tavas in Kansas

**题目ID**: 536/D  
**比赛**: Codeforces Round 299 (Div. 1)  
**年份**: 2015  
**时间限制**: 2.0秒  
**内存限制**: 512MB  

## 题目描述

<PERSON><PERSON> lives in Kansas. Kansas has n cities numbered from 1 to n connected with m bidirectional roads. We can travel from any city to any other city via these roads. Kansas is as strange as Tavas. So there may be a road between a city and itself or more than one road between two cities.

<PERSON><PERSON> invented a game and called it "Dashti". He wants to play <PERSON><PERSON> with his girlfriends, <PERSON><PERSON><PERSON>.

In this game, they assign an arbitrary integer value to each city of Kansas. The value of i-th city equals to pi.

During the game, <PERSON><PERSON> is in city s and <PERSON><PERSON><PERSON> is in city t. They play in turn and <PERSON><PERSON> goes first. A player in his/her turn, must choose a non-negative integer x and his/her score increases by the sum of values of all cities with (shortest) distance no more than x from his/her city. Each city may be used once, or in the other words, after first time a player gets score from a city, city score becomes zero.

There is an additional rule: the player must choose x such that he/she gets the point of at least one city that was not used before. Note that city may initially have value 0, such city isn't considered as been used at the beginning of the game, i. e. each player may use it to fullfill this rule.

The game ends when nobody can make a move.

A player's score is the sum of the points he/she earned during the game. The winner is the player with greater score, or there is a draw if players score the same value. Both players start game with zero points.

If <PERSON>vas wins, he'll break his girlfriend's heart, and if <PERSON>fas wins, <PERSON>vas will cry. But if their scores are equal, they'll be happy and <PERSON>vas will give <PERSON>fas flowers.

They're not too emotional after all, so they'll play optimally. Your task is to tell <PERSON>vas what's going to happen after the game ends.

## 输入格式

The first line of input contains two integers n and m (2 ≤ n ≤ 2000, n - 1 ≤ m ≤ 105).

The second line of input contains two integers s and t (1 ≤ s, t ≤ n, s ≠ t).

The next line contains n integers p1, p2, ..., pn separated by spaces (|pi| ≤ 109).

The next m lines contain the roads. Each line contains three integers v, u, w and it means that there's an road with length w between cities v and u (1 ≤ u, v ≤ n and 0 ≤ w ≤ 109). The road may lead from the city to itself, there may be several roads between each pair of cities.

## 输出格式

If Tavas wins, print "Break a heart". If Nafas wins print "Cry" and if nobody wins (i. e. the game ended with draw) print "Flowers".

## 样例

### 样例 1

**输入**:
```
4 4
1 2
3 2 5 -11
1 4 2
3 4 2
3 1 5
3 2 1
```

**输出**:
```
Cry
```

### 样例 2

**输入**:
```
5 4
1 2
2 2 -5 -4 6
1 2 4
2 3 5
2 4 2
4 5 2
```

**输出**:
```
Break a heart
```

### 样例 3

**输入**:
```
2 1
1 2
-5 -5
1 2 10
```

**输出**:
```
Flowers
```

