# Sums of Digits

**题目ID**: 509/C  
**比赛**: Codeforces Round 289 (Div. 2, ACM ICPC Rules)  
**年份**: 2015  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON><PERSON><PERSON> had a strictly increasing sequence of positive integers a1, ..., an. <PERSON><PERSON><PERSON> used it to build a new sequence b1, ..., bn, where bi is the sum of digits of ai's decimal representation. Then sequence ai got lost and all that remained is sequence bi.

<PERSON><PERSON><PERSON> wonders what the numbers ai could be like. Of all the possible options he likes the one sequence with the minimum possible last number an. Help <PERSON><PERSON><PERSON> restore the initial sequence.

It is guaranteed that such a sequence always exists.

## 输入格式

The first line contains a single integer number n (1 ≤ n ≤ 300).

Next n lines contain integer numbers b1, ..., bn  — the required sums of digits. All bi belong to the range 1 ≤ bi ≤ 300.

## 输出格式

Print n integer numbers, one per line — the correct option for numbers ai, in order of following in sequence. The sequence should be strictly increasing. The sum of digits of the i-th number should be equal to bi.

If there are multiple sequences with least possible number an, print any of them. Print the numbers without leading zeroes.

## 样例

### 样例 1

**输入**:
```
3
1
2
3
```

**输出**:
```
1
2
3
```

### 样例 2

**输入**:
```
3
3
2
1
```

**输出**:
```
3
11
100
```

## 题解

The algorithm is greedy: first, take the minimal number with sum of digits a1 — call it b1. Then, on the i-th step take bi as the minimal number with sum of digits ai, which is more than bi - 1.
It can be easily proven that this algorithm gives an optimal answer. But how to solve the subproblem: given x and y, find the minimal number with sum of digits x, which is more than y?
We use a standard approach: iterate through the digits of y from right to left, trying to increase the current digit and somehow change the digits to the right in order to reach the sum of digits equal to x. Note that if we are considering the (k + 1)-th digit from the right and increase it, we can make the sum of k least significant digits to be any number between 0 and 9k. When we find such position, that increasing a digit in it and changing the least significant digits gives us a number with sum of digits x, we stop the process and obtain the answer. Note that if k least significant digits should have sum m (where 0 ≤ m ≤ 9k), we should obtain the answer greedily, going from the right to the left and putting to the position the largest digit we can.
Let us bound the maximal length of the answer, i.e. of bn. If some bi has at least 40 digits, than we take the minimal k such that 10k ≥ bi. Than between 10k and 10k + 1 there exist numbers with any sum of digits between 1 and 9k. If k ≥ 40, than 9k ≥ 300, which is the upper bound of all bi. So, in the constraints of the problem, bi + 1 will be less than 10k + 1. Than, similarly, bi + 2 < 10k + 2 and so on. So, the length of the answer increases by no more than one after reaching the length of 40. Consequently, the maximal length of the answer can't be more than 340.
The complexity of solution is O(n·maxLen). Since n ≤ 300, maxLen ≤ 340, the solution runs much faster the time limit.

