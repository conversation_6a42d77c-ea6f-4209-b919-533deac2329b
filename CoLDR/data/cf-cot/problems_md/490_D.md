# Chocolate

**题目ID**: 490/D  
**比赛**: Codeforces Round 279 (Div. 2)  
**年份**: 2014  
**时间限制**: 1.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON><PERSON><PERSON><PERSON> likes giving presents to <PERSON><PERSON><PERSON>. He has bought two chocolate bars, each of them has the shape of a segmented rectangle. The first bar is a1 × b1 segments large and the second one is a2 × b2 segments large.

<PERSON><PERSON><PERSON><PERSON> wants to give <PERSON><PERSON><PERSON> one of the bars at the lunch break and eat the other one himself. Besides, he wants to show that <PERSON><PERSON><PERSON><PERSON>'s mind and <PERSON><PERSON><PERSON>'s beauty are equally matched, so the two bars must have the same number of squares.

To make the bars have the same number of squares, <PERSON><PERSON><PERSON><PERSON> eats a little piece of chocolate each minute. Each minute he does the following:

- he either breaks one bar exactly in half (vertically or horizontally) and eats exactly a half of the bar,
- or he chips of exactly one third of a bar (vertically or horizontally) and eats exactly a third of the bar.

In the first case he is left with a half, of the bar and in the second case he is left with two thirds of the bar.

Both variants aren't always possible, and sometimes <PERSON><PERSON><PERSON><PERSON> cannot chip off a half nor a third. For example, if the bar is 16 × 23, then <PERSON>ycar<PERSON> can chip off a half, but not a third. If the bar is 20 × 18, then <PERSON>ycar<PERSON> can chip off both a half and a third. If the bar is 5 × 7, then <PERSON>y<PERSON>pus cannot chip off a half nor a third.

What is the minimum number of minutes Polycarpus needs to make two bars consist of the same number of squares? Find not only the required minimum number of minutes, but also the possible sizes of the bars after the process.

## 输入格式

The first line of the input contains integers a1, b1 (1 ≤ a1, b1 ≤ 109) — the initial sizes of the first chocolate bar. The second line of the input contains integers a2, b2 (1 ≤ a2, b2 ≤ 109) — the initial sizes of the second bar.

You can use the data of type int64 (in Pascal), long long (in С++), long (in Java) to process large integers (exceeding 231 - 1).

## 输出格式

In the first line print m — the sought minimum number of minutes. In the second and third line print the possible sizes of the bars after they are leveled in m minutes. Print the sizes using the format identical to the input format. Print the sizes (the numbers in the printed pairs) in any order. The second line must correspond to the first bar and the third line must correspond to the second bar. If there are multiple solutions, print any of them.

If there is no solution, print a single line with integer -1.

## 样例

### 样例 1

**输入**:
```
2 6
2 3
```

**输出**:
```
1
1 6
2 3
```

### 样例 2

**输入**:
```
36 5
10 16
```

**输出**:
```
3
16 5
5 16
```

### 样例 3

**输入**:
```
3 5
2 1
```

**输出**:
```
-1
```

