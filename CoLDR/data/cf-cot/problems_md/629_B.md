# Far Relative’s Problem

**题目ID**: 629/B  
**比赛**: Codeforces Round 343 (Div. 2)  
**年份**: 2016  
**时间限制**: 2.0秒  
**内存限制**: 256MB  

## 题目描述

<PERSON><PERSON><PERSON> wants to celebrate his birthday with his friends from Far Far Away. He has n friends and each of them can come to the party in a specific range of days of the year from ai to bi. Of course, <PERSON><PERSON><PERSON> Door wants to have as many friends celebrating together with him as possible.

Far cars are as weird as Far Far Away citizens, so they can only carry two people of opposite gender, that is exactly one male and one female. However, <PERSON> is so far from here that no other transportation may be used to get to the party.

Famil Door should select some day of the year and invite some of his friends, such that they all are available at this moment and the number of male friends invited is equal to the number of female friends invited. Find the maximum number of friends that may present at the party.

## 输入格式

The first line of the input contains a single integer n (1 ≤ n ≤ 5000) — then number of <PERSON><PERSON>l Door's friends.

Then follow n lines, that describe the friends. Each line starts with a capital letter 'F' for female friends and with a capital letter 'M' for male friends. Then follow two integers ai and bi (1 ≤ ai ≤ bi ≤ 366), providing that the i-th friend can come to the party from day ai to day bi inclusive.

## 输出格式

Print the maximum number of people that may come to Famil Door's party.

## 样例

### 样例 1

**输入**:
```
4
M 151 307
F 343 352
F 117 145
M 24 128
```

**输出**:
```
2
```

### 样例 2

**输入**:
```
6
M 128 130
F 128 131
F 131 140
F 131 141
M 131 200
M 140 200
```

**输出**:
```
4
```

## 备注

In the first sample, friends 3 and 4 can come on any day in range [117, 128].

In the second sample, friends with indices 3, 4, 5 and 6 can come on day 140.

