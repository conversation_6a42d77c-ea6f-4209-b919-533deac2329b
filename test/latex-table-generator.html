<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LaTeX 表格动态生成器</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        .top-section {
            display: flex;
            gap: 20px;
        }
        .editor {
            flex: 2;
            border: 1px solid #ccc;
            padding: 15px;
            border-radius: 5px;
        }
        .preview {
            flex: 1;
            border: 1px solid #ccc;
            padding: 15px;
            border-radius: 5px;
            background-color: #f9f9f9;
        }
        .table-preview {
            width: 100%;
            border: 1px solid #ccc;
            padding: 15px;
            border-radius: 5px;
            background-color: #f9f9f9;
            margin-top: 20px;
        }
        input, select, button {
            margin: 2px;
            padding: 5px;
        }
        .visual-table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        .visual-table th, .visual-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: center;
            min-width: 100px;
        }
        .visual-table th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .visual-table input, .visual-table select {
            width: 90%;
            border: none;
            background: transparent;
            text-align: center;
        }
        .visual-table input:focus, .visual-table select:focus {
            background-color: #fff;
            border: 1px solid #007bff;
        }
        .category-cell {
            background-color: #e9ecef;
            font-weight: bold;
            vertical-align: middle;
        }
        .controls-row {
            margin: 10px 0;
        }
        .btn-small {
            padding: 3px 6px;
            font-size: 12px;
            margin: 1px;
        }
        textarea {
            width: 100%;
            height: 400px;
            font-family: monospace;
            font-size: 12px;
        }
        .btn {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 3px;
            cursor: pointer;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .btn-danger {
            background-color: #dc3545;
        }
        .btn-danger:hover {
            background-color: #c82333;
        }
        .btn-success {
            background-color: #28a745;
        }
        .btn-success:hover {
            background-color: #218838;
        }
    </style>
</head>
<body>
    <h1>LaTeX 表格动态生成器</h1>
    
    <div class="container">
        <div class="top-section">
            <div class="editor">
                <h3>表格编辑器</h3>

                <div class="controls-row">
                    <label>表格标题: </label>
                    <input type="text" id="tableCaption" value="功能对比表" placeholder="输入表格标题">
                </div>

                <div class="controls-row">
                    <button class="btn" onclick="addColumn()">添加列</button>
                    <button class="btn" onclick="addRowGroup()">添加类别</button>
                    <button class="btn btn-success" onclick="generateLatex()">生成 LaTeX</button>
                    <button class="btn btn-danger" onclick="clearTable()">清空表格</button>
                </div>

                <div id="tableEditor">
                    <!-- 动态生成的可视化表格编辑器 -->
                </div>
            </div>

            <div class="preview">
                <h3>LaTeX 代码预览</h3>
                <textarea id="latexOutput" readonly></textarea>
                <button class="btn" onclick="copyToClipboard()">复制到剪贴板</button>
            </div>
        </div>

        <div class="table-preview">
            <h3>表格预览</h3>
            <div id="tablePreview">
                <!-- 表格预览区域 -->
            </div>
        </div>
    </div>

    <script>
        let tableData = {
            caption: "功能对比表",
            headers: ["类别", "功能项", "工具1", "工具2", "工具3"],
            rows: [
                {
                    category: "代码补全",
                    items: [
                        ["多行补全", "✓", "✓", "✗"],
                        ["Tab 补全", "✓", "✓", "✓"]
                    ]
                },
                {
                    category: "智能对话", 
                    items: [
                        ["代码解释", "✓", "✓", "✓"],
                        ["语音图像输入", "✓", "✗", "✗"]
                    ]
                }
            ]
        };

        function renderEditor() {
            const editor = document.getElementById('tableEditor');
            let html = '';

            // 创建可视化表格
            html += '<table class="visual-table">';

            // 表头行
            html += '<thead><tr>';
            tableData.headers.forEach((header, index) => {
                html += '<th>';
                html += `<input type="text" value="${header}" onchange="updateHeader(${index}, this.value)" placeholder="列标题">`;
                if (index > 1) {
                    html += `<br><button class="btn btn-danger btn-small" onclick="removeColumn(${index})">删除列</button>`;
                }
                html += '</th>';
            });
            html += '</tr></thead>';

            // 数据行
            html += '<tbody>';
            tableData.rows.forEach((row, rowIndex) => {
                const itemCount = row.items.length;

                row.items.forEach((item, itemIndex) => {
                    html += '<tr>';

                    // 类别列（使用rowspan）
                    if (itemIndex === 0) {
                        html += `<td class="category-cell" rowspan="${itemCount}">`;
                        html += `<input type="text" value="${row.category}" onchange="updateCategory(${rowIndex}, this.value)" placeholder="类别">`;
                        html += `<br><button class="btn btn-danger btn-small" onclick="removeRowGroup(${rowIndex})">删除类别</button>`;
                        html += '</td>';
                    }

                    // 功能项和其他列
                    item.forEach((cell, cellIndex) => {
                        html += '<td>';
                        if (cellIndex === 0) {
                            html += `<input type="text" value="${cell}" onchange="updateCell(${rowIndex}, ${itemIndex}, ${cellIndex}, this.value)" placeholder="功能项">`;
                        } else {
                            html += `<select onchange="updateCell(${rowIndex}, ${itemIndex}, ${cellIndex}, this.value)">`;
                            html += `<option value="✓" ${cell === '✓' ? 'selected' : ''}>✓ 支持</option>`;
                            html += `<option value="✗" ${cell === '✗' ? 'selected' : ''}>✗ 不支持</option>`;
                            html += `<option value="" ${cell === '' ? 'selected' : ''}>空白</option>`;
                            html += `</select>`;
                        }
                        html += '</td>';
                    });

                    // 操作列
                    html += '<td>';
                    html += `<button class="btn btn-danger btn-small" onclick="removeItem(${rowIndex}, ${itemIndex})">删除项</button>`;
                    html += '</td>';

                    html += '</tr>';
                });

                // 添加功能项按钮行
                html += '<tr>';
                html += `<td colspan="${tableData.headers.length + 1}" style="text-align: center; background-color: #f8f9fa;">`;
                html += `<button class="btn btn-small" onclick="addItem(${rowIndex})">+ 添加功能项到 "${row.category}"</button>`;
                html += '</td>';
                html += '</tr>';
            });
            html += '</tbody>';
            html += '</table>';

            // 添加类别按钮
            html += '<div style="text-align: center; margin: 15px 0;">';
            html += '<button class="btn" onclick="addRowGroup()">+ 添加新类别</button>';
            html += '</div>';

            editor.innerHTML = html;
        }

        function renderPreview() {
            const preview = document.getElementById('tablePreview');
            let html = '';

            html += '<table class="visual-table">';

            // 表头
            html += '<thead><tr>';
            tableData.headers.forEach(header => {
                html += `<th>${header}</th>`;
            });
            html += '</tr></thead>';

            // 数据行
            html += '<tbody>';
            tableData.rows.forEach(row => {
                const itemCount = row.items.length;

                row.items.forEach((item, itemIndex) => {
                    html += '<tr>';

                    // 类别列
                    if (itemIndex === 0) {
                        html += `<td class="category-cell" rowspan="${itemCount}">${row.category}</td>`;
                    }

                    // 其他列
                    item.forEach(cell => {
                        let displayCell = cell;
                        if (cell === '✓') displayCell = '✓';
                        if (cell === '✗') displayCell = '✗';
                        html += `<td>${displayCell}</td>`;
                    });

                    html += '</tr>';
                });
            });
            html += '</tbody>';
            html += '</table>';

            preview.innerHTML = html;
        }

        function updateHeader(index, value) {
            tableData.headers[index] = value;
            renderPreview();
        }

        function updateCategory(rowIndex, value) {
            tableData.rows[rowIndex].category = value;
            renderPreview();
        }

        function updateCell(rowIndex, itemIndex, cellIndex, value) {
            tableData.rows[rowIndex].items[itemIndex][cellIndex] = value;
            renderPreview();
        }

        function addColumn() {
            const newColName = prompt("请输入新列名称:", "新工具");
            if (newColName) {
                tableData.headers.push(newColName);
                tableData.rows.forEach(row => {
                    row.items.forEach(item => {
                        item.push("✓");
                    });
                });
                renderEditor();
                renderPreview();
            }
        }

        function removeColumn(index) {
            if (index > 1 && confirm("确定要删除这一列吗？")) {
                tableData.headers.splice(index, 1);
                tableData.rows.forEach(row => {
                    row.items.forEach(item => {
                        item.splice(index - 1, 1);
                    });
                });
                renderEditor();
                renderPreview();
            }
        }

        function addRowGroup() {
            const categoryName = prompt("请输入类别名称:", "新类别");
            if (categoryName) {
                const newRow = {
                    category: categoryName,
                    items: [["新功能", ...Array(tableData.headers.length - 2).fill("✓")]]
                };
                tableData.rows.push(newRow);
                renderEditor();
                renderPreview();
            }
        }

        function removeRowGroup(rowIndex) {
            if (confirm("确定要删除这个类别及其所有功能项吗？")) {
                tableData.rows.splice(rowIndex, 1);
                renderEditor();
                renderPreview();
            }
        }

        function addItem(rowIndex) {
            const itemName = prompt("请输入功能项名称:", "新功能");
            if (itemName) {
                const newItem = [itemName, ...Array(tableData.headers.length - 2).fill("✓")];
                tableData.rows[rowIndex].items.push(newItem);
                renderEditor();
                renderPreview();
            }
        }

        function removeItem(rowIndex, itemIndex) {
            if (confirm("确定要删除这个功能项吗？")) {
                tableData.rows[rowIndex].items.splice(itemIndex, 1);
                if (tableData.rows[rowIndex].items.length === 0) {
                    tableData.rows.splice(rowIndex, 1);
                }
                renderEditor();
                renderPreview();
            }
        }

        function addRow() {
            addRowGroup();
        }

        function clearTable() {
            if (confirm("确定要清空整个表格吗？")) {
                tableData = {
                    caption: "功能对比表",
                    headers: ["类别", "功能项", "工具1"],
                    rows: []
                };
                renderEditor();
                renderPreview();
            }
        }

        function generateLatex() {
            const caption = document.getElementById('tableCaption').value || tableData.caption;
            
            let latex = `% \\documentclass{article}
\\documentclass{article}
\\usepackage{ctex}
\\usepackage{graphicx}      % For \\resizebox
\\usepackage{amssymb}       % For \\checkmark
\\usepackage{pifont}        % For \\ding{55}
\\usepackage{booktabs}      % For \\toprule, \\midrule, \\bottomrule
\\usepackage{geometry}
\\usepackage{multirow}
\\geometry{a4paper, margin=1in}

\\begin{document}

\\begin{table}[htbp]
\\centering
\\caption{${caption}}
\\resizebox{\\textwidth}{!}{%
\\begin{tabular}{l|l|${'c|'.repeat(tableData.headers.length - 2)}c}
\\toprule
`;

            // 生成表头
            const headerRow = tableData.headers.map(h => `\\textbf{${h}}`).join(' & ');
            latex += headerRow + ' \\\\\n\\midrule\n';

            // 生成数据行
            tableData.rows.forEach((row, rowIndex) => {
                const itemCount = row.items.length;
                
                row.items.forEach((item, itemIndex) => {
                    let rowStr = '';
                    
                    if (itemIndex === 0) {
                        // 第一行包含类别
                        rowStr += `\\multirow{${itemCount}}{*}{${row.category}}\n& `;
                    } else {
                        rowStr += '& ';
                    }
                    
                    // 添加功能项和其他列
                    const cells = item.map(cell => {
                        if (cell === '✓') return '\\checkmark';
                        if (cell === '✗') return '\\ding{55}';
                        return cell;
                    });
                    
                    rowStr += cells.join(' & ') + ' \\\\';
                    latex += rowStr + '\n';
                });
                
                if (rowIndex < tableData.rows.length - 1) {
                    latex += '\\midrule\n';
                }
            });

            latex += `\\bottomrule
\\end{tabular}%
}
\\end{table}

\\end{document}`;

            document.getElementById('latexOutput').value = latex;
        }

        function copyToClipboard() {
            const textarea = document.getElementById('latexOutput');
            textarea.select();
            document.execCommand('copy');
            alert('LaTeX 代码已复制到剪贴板！');
        }

        // 初始化
        document.getElementById('tableCaption').addEventListener('input', function() {
            tableData.caption = this.value;
        });

        renderEditor();
        renderPreview();
        generateLatex();
    </script>
</body>
</html>