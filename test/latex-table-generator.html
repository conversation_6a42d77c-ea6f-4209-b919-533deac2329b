<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LaTeX 表格动态生成器</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1400px;
            margin: 0 auto;
            padding: 15px;
            font-size: 14px;
        }
        .container {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        .editor {
            border: 1px solid #ccc;
            padding: 10px;
            border-radius: 5px;
        }
        .bottom-controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            padding: 15px;
            border: 1px solid #ccc;
            border-radius: 5px;
            background-color: #f9f9f9;
        }
        .status-info {
            text-align: center;
            font-size: 12px;
            color: #666;
            margin-top: 10px;
        }
        input, select, button {
            margin: 2px;
            padding: 5px;
        }
        .table-container {
            overflow-x: auto;
            max-width: 100%;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .visual-table {
            width: max-content;
            min-width: 100%;
            border-collapse: collapse;
            margin: 0;
        }
        .visual-table th, .visual-table td {
            border: 1px solid #ddd;
            padding: 2px;
            text-align: center;
            min-width: 45px;
            max-width: 80px;
            position: relative;
            font-size: 10px;
        }
        .visual-table th {
            background-color: #f2f2f2;
            font-weight: bold;
            position: sticky;
            top: 0;
            z-index: 10;
            padding: 6px 4px;
        }
        .visual-table input, .visual-table select {
            width: 100%;
            border: none;
            background: transparent;
            text-align: center;
            font-size: 10px;
            padding: 1px;
            box-sizing: border-box;
        }
        .visual-table input:focus, .visual-table select:focus {
            background-color: #fff;
            border: 1px solid #007bff;
        }
        .category-cell {
            background-color: #e9ecef;
            font-weight: bold;
            vertical-align: middle;
            min-width: 40px;
            max-width: 70px;
            cursor: move;
        }
        .category-dragging {
            opacity: 0.5;
            background-color: #ffeaa7 !important;
        }
        .category-drag-over {
            background-color: #74b9ff !important;
        }
        .controls-row {
            margin: 10px 0;
        }
        .btn-icon {
            background: none;
            border: 1px solid #ccc;
            width: 16px;
            height: 16px;
            border-radius: 2px;
            cursor: pointer;
            font-size: 10px;
            line-height: 1;
            margin: 1px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            color: #666;
        }
        .btn-icon:hover {
            background-color: #f0f0f0;
        }
        .btn-icon.add {
            color: #28a745;
            border-color: #28a745;
        }
        .btn-icon.add:hover {
            background-color: #d4edda;
        }
        .btn-icon.remove {
            color: #dc3545;
            border-color: #dc3545;
        }
        .btn-icon.remove:hover {
            background-color: #f8d7da;
        }
        .drag-handle {
            cursor: move;
            color: #999;
            font-size: 14px;
            padding: 2px;
        }
        .drag-handle:hover {
            color: #666;
        }
        .dragging {
            opacity: 0.5;
        }
        .drag-over {
            background-color: #e3f2fd !important;
        }
        .column-header {
            position: relative;
        }
        .column-drag-handle {
            position: absolute;
            top: 2px;
            left: 2px;
            cursor: move;
            color: #999;
            font-size: 12px;
        }
        .column-drag-handle:hover {
            color: #666;
        }
        textarea {
            width: 100%;
            height: 400px;
            font-family: monospace;
            font-size: 12px;
        }
        .btn {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 3px;
            cursor: pointer;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .btn-danger {
            background-color: #dc3545;
        }
        .btn-danger:hover {
            background-color: #c82333;
        }
        .btn-success {
            background-color: #28a745;
        }
        .btn-success:hover {
            background-color: #218838;
        }
    </style>
</head>
<body>
    <h1>LaTeX 表格动态生成器</h1>
    
    <div class="container">
        <div class="editor">
            <h3>LaTeX 表格编辑器</h3>

            <div class="controls-row">
                <label>表格标题: </label>
                <input type="text" id="tableCaption" value="功能对比表" placeholder="输入表格标题">
            </div>

            <div class="table-container">
                <div id="tableEditor">
                    <!-- 动态生成的可视化表格编辑器 -->
                </div>
            </div>
        </div>

        <div class="bottom-controls">
            <button class="btn btn-success" onclick="copyLatexToClipboard()">📋 复制 LaTeX</button>
            <button class="btn" onclick="exportData()">💾 导出数据</button>
            <button class="btn btn-danger" onclick="clearTable()">🗑️ 清空表格</button>
        </div>

        <div class="status-info">
            <div id="saveStatus">数据自动保存到 data.json</div>
        </div>
    </div>

    <script>
        let tableData = {
            caption: "功能对比表",
            headers: ["类别", "功能项", "工具1", "工具2", "工具3"],
            rows: [
                {
                    category: "代码补全",
                    items: [
                        ["多行补全", "✓", "✓", "✗"],
                        ["Tab 补全", "✓", "✓", "✓"]
                    ]
                },
                {
                    category: "智能对话", 
                    items: [
                        ["代码解释", "✓", "✓", "✓"],
                        ["语音图像输入", "✓", "✗", "✗"]
                    ]
                }
            ]
        };

        function renderEditor() {
            const editor = document.getElementById('tableEditor');
            let html = '';

            // 创建可视化表格
            html += '<table class="visual-table">';

            // 表头行
            html += '<thead><tr>';
            // 拖拽列
            html += '<th style="width: 20px; min-width: 20px; font-size: 10px;">⋮⋮</th>';
            tableData.headers.forEach((header, index) => {
                html += `<th class="column-header" draggable="true" ondragstart="dragColumnStart(event, ${index})" ondragover="dragColumnOver(event)" ondrop="dropColumn(event, ${index})">`;
                if (index > 1) {
                    html += `<span class="column-drag-handle" style="font-size: 8px;">⋮⋮</span>`;
                }
                html += `<input type="text" value="${header}" onchange="updateHeader(${index}, this.value)" placeholder="列标题">`;
                if (index > 1) {
                    html += `<br><button class="btn-icon remove" onclick="removeColumn(${index})" title="删除列">×</button>`;
                }
                html += '</th>';
            });
            // 添加列按钮
            html += '<th style="width: 30px; min-width: 30px; background-color: #f8f9fa;">';
            html += '<button class="btn-icon add" onclick="addColumn()" title="添加列">+</button>';
            html += '</th>';
            html += '</tr></thead>';

            // 数据行
            html += '<tbody>';
            tableData.rows.forEach((row, rowIndex) => {
                const itemCount = row.items.length;

                row.items.forEach((item, itemIndex) => {
                    html += `<tr draggable="true" ondragstart="dragRowStart(event, ${rowIndex}, ${itemIndex})" ondragover="dragRowOver(event)" ondrop="dropRow(event, ${rowIndex}, ${itemIndex})">`;

                    // 拖拽手柄列
                    html += '<td class="drag-handle" style="font-size: 8px;">⋮⋮</td>';

                    // 类别列（使用rowspan，支持拖拽）
                    if (itemIndex === 0) {
                        html += `<td class="category-cell" rowspan="${itemCount}" draggable="true" ondragstart="dragCategoryStart(event, ${rowIndex})" ondragover="dragCategoryOver(event)" ondrop="dropCategory(event, ${rowIndex})">`;
                        html += `<input type="text" value="${row.category}" onchange="updateCategory(${rowIndex}, this.value)" placeholder="类别">`;
                        html += `<br><button class="btn-icon remove" onclick="removeRowGroup(${rowIndex})" title="删除类别">×</button>`;
                        html += '</td>';
                    }

                    // 功能项和其他列
                    item.forEach((cell, cellIndex) => {
                        html += '<td>';
                        if (cellIndex === 0) {
                            html += `<input type="text" value="${cell}" onchange="updateCell(${rowIndex}, ${itemIndex}, ${cellIndex}, this.value)" placeholder="功能项">`;
                        } else {
                            html += `<select onchange="updateCell(${rowIndex}, ${itemIndex}, ${cellIndex}, this.value)">`;
                            html += `<option value="✓" ${cell === '✓' ? 'selected' : ''}>✓</option>`;
                            html += `<option value="✗" ${cell === '✗' ? 'selected' : ''}>✗</option>`;
                            html += `<option value="" ${cell === '' ? 'selected' : ''}>-</option>`;
                            html += `</select>`;
                        }
                        html += '</td>';
                    });

                    // 操作列
                    html += '<td>';
                    html += `<button class="btn-icon remove" onclick="removeItem(${rowIndex}, ${itemIndex})" title="删除项">×</button>`;
                    html += '</td>';

                    html += '</tr>';
                });

                // 添加功能项按钮行
                html += '<tr>';
                html += `<td></td><td colspan="${tableData.headers.length + 1}" style="text-align: center; background-color: #f8f9fa; padding: 3px;">`;
                html += `<button class="btn-icon add" onclick="addItem(${rowIndex})" title="添加功能项">+</button>`;
                html += '</td>';
                html += '</tr>';
            });
            html += '</tbody>';
            html += '</table>';

            // 添加类别按钮（小按钮形式）
            html += '<div style="text-align: center; margin: 10px 0;">';
            html += '<button class="btn-icon add" onclick="addRowGroup()" title="添加新类别">+</button>';
            html += '</div>';

            editor.innerHTML = html;
        }

        // 拖拽相关变量
        let draggedColumn = null;
        let draggedRow = null;
        let draggedItem = null;
        let draggedCategory = null;

        // 列拖拽功能
        function dragColumnStart(event, columnIndex) {
            draggedColumn = columnIndex;
            event.dataTransfer.effectAllowed = 'move';
            event.target.classList.add('dragging');
        }

        function dragColumnOver(event) {
            event.preventDefault();
            event.dataTransfer.dropEffect = 'move';
            event.target.closest('th').classList.add('drag-over');
        }

        function dropColumn(event, targetIndex) {
            event.preventDefault();
            event.target.closest('th').classList.remove('drag-over');

            if (draggedColumn !== null && draggedColumn !== targetIndex && draggedColumn > 1 && targetIndex > 1) {
                // 移动表头
                const header = tableData.headers.splice(draggedColumn, 1)[0];
                tableData.headers.splice(targetIndex, 0, header);

                // 移动数据列
                tableData.rows.forEach(row => {
                    row.items.forEach(item => {
                        const cell = item.splice(draggedColumn - 1, 1)[0];
                        item.splice(targetIndex - 1, 0, cell);
                    });
                });

                renderEditor();
                saveToJsonFile();
            }

            draggedColumn = null;
            document.querySelectorAll('.dragging').forEach(el => el.classList.remove('dragging'));
        }

        // 行拖拽功能
        function dragRowStart(event, rowIndex, itemIndex) {
            draggedRow = rowIndex;
            draggedItem = itemIndex;
            event.dataTransfer.effectAllowed = 'move';
            event.target.classList.add('dragging');
        }

        function dragRowOver(event) {
            event.preventDefault();
            event.dataTransfer.dropEffect = 'move';
            event.target.closest('tr').classList.add('drag-over');
        }

        function dropRow(event, targetRowIndex, targetItemIndex) {
            event.preventDefault();
            event.target.closest('tr').classList.remove('drag-over');

            if (draggedRow !== null && draggedItem !== null) {
                // 移动同一类别内的项目
                if (draggedRow === targetRowIndex && draggedItem !== targetItemIndex) {
                    const item = tableData.rows[draggedRow].items.splice(draggedItem, 1)[0];
                    tableData.rows[draggedRow].items.splice(targetItemIndex, 0, item);

                    renderEditor();
                    saveToJsonFile();
                }
                // 移动到不同类别（暂时不支持，避免复杂性）
            }

            draggedRow = null;
            draggedItem = null;
            document.querySelectorAll('.dragging').forEach(el => el.classList.remove('dragging'));
        }

        // 类别拖拽功能
        function dragCategoryStart(event, categoryIndex) {
            draggedCategory = categoryIndex;
            event.dataTransfer.effectAllowed = 'move';
            event.target.classList.add('category-dragging');
        }

        function dragCategoryOver(event) {
            event.preventDefault();
            event.dataTransfer.dropEffect = 'move';
            event.target.closest('.category-cell').classList.add('category-drag-over');
        }

        function dropCategory(event, targetIndex) {
            event.preventDefault();
            event.target.closest('.category-cell').classList.remove('category-drag-over');

            if (draggedCategory !== null && draggedCategory !== targetIndex) {
                // 移动类别
                const category = tableData.rows.splice(draggedCategory, 1)[0];
                tableData.rows.splice(targetIndex, 0, category);

                renderEditor();
                saveToJsonFile();
            }

            draggedCategory = null;
            document.querySelectorAll('.category-dragging').forEach(el => el.classList.remove('category-dragging'));
        }

        // 清理拖拽状态
        document.addEventListener('dragend', function() {
            document.querySelectorAll('.dragging, .drag-over, .category-dragging, .category-drag-over').forEach(el => {
                el.classList.remove('dragging', 'drag-over', 'category-dragging', 'category-drag-over');
            });
        });

        // File persistence system using a fixed file path
        const DATA_FILE_PATH = './data.json';

        async function loadDataFromFile() {
            try {
                const response = await fetch(DATA_FILE_PATH + '?t=' + Date.now()); // 防止缓存
                if (response.ok) {
                    const data = await response.json();
                    tableData = data;
                    document.getElementById('tableCaption').value = tableData.caption || '功能对比表';
                    updateSaveStatus('数据已从 data.json 加载');
                    return true;
                } else if (response.status === 404) {
                    updateSaveStatus('data.json 不存在，使用默认数据');
                    return false;
                }
            } catch (error) {
                console.error('加载数据失败:', error);
                updateSaveStatus('加载失败，使用默认数据', true);
                return false;
            }
        }

        async function saveToJsonFile() {
            try {
                const dataStr = JSON.stringify(tableData, null, 2);

                // 尝试使用 File System Access API 直接保存到 data.json
                if ('showSaveFilePicker' in window && window.fileHandle) {
                    const writable = await window.fileHandle.createWritable();
                    await writable.write(dataStr);
                    await writable.close();
                    updateSaveStatus('已保存到 data.json');
                } else {
                    // 备用方案：下载文件
                    const blob = new Blob([dataStr], { type: 'application/json' });
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = 'data.json';
                    a.style.display = 'none';
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    URL.revokeObjectURL(url);
                    updateSaveStatus('已下载 data.json');
                }
            } catch (error) {
                console.error('保存失败:', error);
                updateSaveStatus('保存失败', true);
            }
        }

        function updateSaveStatus(message, isError = false) {
            const status = document.getElementById('saveStatus');
            status.textContent = message;
            status.style.color = isError ? '#dc3545' : '#28a745';
            setTimeout(() => {
                status.textContent = '数据自动保存到 data.json';
                status.style.color = '#666';
            }, 2000);
        }

        function updateHeader(index, value) {
            tableData.headers[index] = value;
            saveToJsonFile();
        }

        function updateCategory(rowIndex, value) {
            tableData.rows[rowIndex].category = value;
            saveToJsonFile();
        }

        function updateCell(rowIndex, itemIndex, cellIndex, value) {
            tableData.rows[rowIndex].items[itemIndex][cellIndex] = value;
            saveToJsonFile();
        }

        function addColumn() {
            const newColName = prompt("请输入新列名称:", "新工具");
            if (newColName) {
                tableData.headers.push(newColName);
                tableData.rows.forEach(row => {
                    row.items.forEach(item => {
                        item.push("✓");
                    });
                });
                renderEditor();
                saveToJsonFile();
            }
        }

        function removeColumn(index) {
            if (index > 1 && confirm("确定要删除这一列吗？")) {
                tableData.headers.splice(index, 1);
                tableData.rows.forEach(row => {
                    row.items.forEach(item => {
                        item.splice(index - 1, 1);
                    });
                });
                renderEditor();
                saveToJsonFile();
            }
        }

        function addRowGroup() {
            const categoryName = prompt("请输入类别名称:", "新类别");
            if (categoryName) {
                const newRow = {
                    category: categoryName,
                    items: [["新功能", ...Array(tableData.headers.length - 2).fill("✓")]]
                };
                tableData.rows.push(newRow);
                renderEditor();
                saveToJsonFile();
            }
        }

        function removeRowGroup(rowIndex) {
            if (confirm("确定要删除这个类别及其所有功能项吗？")) {
                tableData.rows.splice(rowIndex, 1);
                renderEditor();
                saveToJsonFile();
            }
        }

        function addItem(rowIndex) {
            const itemName = prompt("请输入功能项名称:", "新功能");
            if (itemName) {
                const newItem = [itemName, ...Array(tableData.headers.length - 2).fill("✓")];
                tableData.rows[rowIndex].items.push(newItem);
                renderEditor();
                saveToJsonFile();
            }
        }

        function removeItem(rowIndex, itemIndex) {
            if (confirm("确定要删除这个功能项吗？")) {
                tableData.rows[rowIndex].items.splice(itemIndex, 1);
                if (tableData.rows[rowIndex].items.length === 0) {
                    tableData.rows.splice(rowIndex, 1);
                }
                renderEditor();
                saveToJsonFile();
            }
        }

        function addRow() {
            addRowGroup();
        }

        function clearTable() {
            if (confirm("确定要清空整个表格吗？")) {
                tableData = {
                    caption: "功能对比表",
                    headers: ["类别", "功能项", "工具1"],
                    rows: []
                };
                document.getElementById('tableCaption').value = tableData.caption;
                renderEditor();
                saveToJsonFile();
            }
        }

        function copyLatexToClipboard() {
            generateLatex();
            const latex = document.getElementById('latexOutput').value;
            navigator.clipboard.writeText(latex).then(() => {
                alert('LaTeX 代码已复制到剪贴板！');
            }).catch(() => {
                // 备用方法
                const textarea = document.getElementById('latexOutput');
                textarea.select();
                document.execCommand('copy');
                alert('LaTeX 代码已复制到剪贴板！');
            });
        }

        function exportData() {
            window.manualSave = true;
            saveToJsonFile();
        }

        function generateLatex() {
            const caption = document.getElementById('tableCaption').value || tableData.caption;
            
            let latex = `% \\documentclass{article}
\\documentclass{article}
\\usepackage{ctex}
\\usepackage{graphicx}      % For \\resizebox
\\usepackage{amssymb}       % For \\checkmark
\\usepackage{pifont}        % For \\ding{55}
\\usepackage{booktabs}      % For \\toprule, \\midrule, \\bottomrule
\\usepackage{geometry}
\\usepackage{multirow}
\\geometry{a4paper, margin=1in}

\\begin{document}

\\begin{table}[htbp]
\\centering
\\caption{${caption}}
\\resizebox{\\textwidth}{!}{%
\\begin{tabular}{l|l|${'c|'.repeat(tableData.headers.length - 2)}c}
\\toprule
`;

            // 生成表头
            const headerRow = tableData.headers.map(h => `\\textbf{${h}}`).join(' & ');
            latex += headerRow + ' \\\\\n\\midrule\n';

            // 生成数据行
            tableData.rows.forEach((row, rowIndex) => {
                const itemCount = row.items.length;
                
                row.items.forEach((item, itemIndex) => {
                    let rowStr = '';
                    
                    if (itemIndex === 0) {
                        // 第一行包含类别
                        rowStr += `\\multirow{${itemCount}}{*}{${row.category}}\n& `;
                    } else {
                        rowStr += '& ';
                    }
                    
                    // 添加功能项和其他列
                    const cells = item.map(cell => {
                        if (cell === '✓') return '\\checkmark';
                        if (cell === '✗') return '\\ding{55}';
                        return cell;
                    });
                    
                    rowStr += cells.join(' & ') + ' \\\\';
                    latex += rowStr + '\n';
                });
                
                if (rowIndex < tableData.rows.length - 1) {
                    latex += '\\midrule\n';
                }
            });

            latex += `\\bottomrule
\\end{tabular}%
}
\\end{table}

\\end{document}`;

            document.getElementById('latexOutput').value = latex;
        }

        function copyToClipboard() {
            const textarea = document.getElementById('latexOutput');
            textarea.select();
            document.execCommand('copy');
            alert('LaTeX 代码已复制到剪贴板！');
        }

        // 初始化
        document.getElementById('tableCaption').addEventListener('input', function() {
            tableData.caption = this.value;
            saveToJsonFile();
        });

        // 添加隐藏的textarea用于复制功能
        const hiddenTextarea = document.createElement('textarea');
        hiddenTextarea.id = 'latexOutput';
        hiddenTextarea.style.display = 'none';
        document.body.appendChild(hiddenTextarea);

        // Load data from file on startup, then render
        async function initializeApp() {
            // First try to load from localStorage (immediate fallback)
            const localData = localStorage.getItem('tableData');
            if (localData) {
                try {
                    tableData = JSON.parse(localData);
                    document.getElementById('tableCaption').value = tableData.caption || '功能对比表';
                } catch (e) {
                    console.error('Failed to parse local data:', e);
                }
            }

            // Try to load from file (this will work if running on a server)
            await loadDataFromFile();

            // Render the interface
            renderEditor();
            generateLatex();
        }

        // Start the application
        initializeApp();
    </script>
</body>
</html>